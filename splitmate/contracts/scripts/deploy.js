const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying SplitMate Rewards contract...");

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("📝 Deploying with account:", deployer.address);

  // Get account balance
  const balance = await ethers.provider.getBalance(deployer.address);
  console.log("💰 Account balance:", ethers.formatEther(balance), "ETH");

  // Deploy the contract
  const SplitMateRewards = await ethers.getContractFactory("SplitMateRewards");
  const splitMateRewards = await SplitMateRewards.deploy(deployer.address);

  await splitMateRewards.waitForDeployment();

  const contractAddress = await splitMateRewards.getAddress();
  console.log("✅ SplitMateRewards deployed to:", contractAddress);

  // Verify deployment
  console.log("🔍 Verifying deployment...");
  const totalSupply = await splitMateRewards.totalSupply();
  console.log("📊 Initial total supply:", totalSupply.toString());

  // Save deployment info
  const deploymentInfo = {
    network: hre.network.name,
    contractAddress: contractAddress,
    deployer: deployer.address,
    blockNumber: await ethers.provider.getBlockNumber(),
    timestamp: new Date().toISOString(),
    transactionHash: splitMateRewards.deploymentTransaction()?.hash
  };

  console.log("\n📋 Deployment Summary:");
  console.log("Network:", deploymentInfo.network);
  console.log("Contract Address:", deploymentInfo.contractAddress);
  console.log("Deployer:", deploymentInfo.deployer);
  console.log("Block Number:", deploymentInfo.blockNumber);
  console.log("Transaction Hash:", deploymentInfo.transactionHash);

  // Test minting a reward (optional)
  if (hre.network.name === "hardhat" || hre.network.name === "localhost") {
    console.log("\n🧪 Testing reward minting...");
    
    try {
      const tx = await splitMateRewards.mintReward(deployer.address, "early_adopter");
      await tx.wait();
      console.log("✅ Test reward minted successfully");
      
      const newTotalSupply = await splitMateRewards.totalSupply();
      console.log("📊 New total supply:", newTotalSupply.toString());
      
      const hasAchievement = await splitMateRewards.hasAchievement(deployer.address, "early_adopter");
      console.log("🏆 Has early_adopter achievement:", hasAchievement);
    } catch (error) {
      console.log("❌ Test minting failed:", error.message);
    }
  }

  console.log("\n🎉 Deployment completed successfully!");
  
  if (hre.network.name !== "hardhat") {
    console.log("\n📝 To verify the contract on Basescan, run:");
    console.log(`npx hardhat verify --network ${hre.network.name} ${contractAddress} "${deployer.address}"`);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
