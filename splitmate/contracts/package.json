{"name": "splitmate-contracts", "version": "1.0.0", "description": "Smart contracts for SplitMate NFT rewards", "main": "index.js", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "deploy": "hardhat run scripts/deploy.js", "deploy:base": "hardhat run scripts/deploy.js --network base"}, "keywords": ["solidity", "hardhat", "nft", "base", "rewards"], "author": "SplitMate Team", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^4.0.0", "hardhat": "^2.19.0"}, "dependencies": {"@openzeppelin/contracts": "^5.0.0"}}