// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Counters.sol";

/**
 * @title SplitMateRewards
 * @dev NFT contract for SplitMate achievement rewards
 * Mints POAP-style badges for various achievements like:
 * - First payment
 * - Fastest payer
 * - Most active splitter
 * - Perfect payment record
 */
contract SplitMateRewards is ERC721, ERC721URIStorage, Ownable {
    using Counters for Counters.Counter;

    Counters.Counter private _tokenIdCounter;

    // Mapping from achievement type to metadata URI
    mapping(string => string) public achievementMetadata;
    
    // Mapping from user to achievement type to whether they have it
    mapping(address => mapping(string => bool)) public userAchievements;
    
    // Mapping from token ID to achievement type
    mapping(uint256 => string) public tokenAchievements;

    // Events
    event AchievementUnlocked(address indexed user, string achievementType, uint256 tokenId);
    event AchievementMetadataUpdated(string achievementType, string metadataURI);

    constructor(address initialOwner) 
        ERC721("SplitMate Rewards", "SMREWARD") 
        Ownable(initialOwner) 
    {
        // Initialize default achievement metadata
        _initializeAchievements();
    }

    /**
     * @dev Initialize default achievement types and metadata
     */
    function _initializeAchievements() private {
        achievementMetadata["first_payment"] = "ipfs://QmFirstPaymentMetadata";
        achievementMetadata["fastest_payer"] = "ipfs://QmFastestPayerMetadata";
        achievementMetadata["perfect_record"] = "ipfs://QmPerfectRecordMetadata";
        achievementMetadata["group_organizer"] = "ipfs://QmGroupOrganizerMetadata";
        achievementMetadata["heavy_splitter"] = "ipfs://QmHeavySplitterMetadata";
        achievementMetadata["early_adopter"] = "ipfs://QmEarlyAdopterMetadata";
    }

    /**
     * @dev Mint a reward NFT for a specific achievement
     * @param to Address to mint the NFT to
     * @param achievementType Type of achievement (e.g., "first_payment")
     */
    function mintReward(address to, string memory achievementType) public onlyOwner {
        require(bytes(achievementMetadata[achievementType]).length > 0, "Achievement type not supported");
        require(!userAchievements[to][achievementType], "User already has this achievement");

        uint256 tokenId = _tokenIdCounter.current();
        _tokenIdCounter.increment();

        _safeMint(to, tokenId);
        _setTokenURI(tokenId, achievementMetadata[achievementType]);
        
        userAchievements[to][achievementType] = true;
        tokenAchievements[tokenId] = achievementType;

        emit AchievementUnlocked(to, achievementType, tokenId);
    }

    /**
     * @dev Batch mint multiple rewards to different users
     * @param recipients Array of addresses to mint to
     * @param achievementTypes Array of achievement types corresponding to each recipient
     */
    function batchMintRewards(
        address[] memory recipients, 
        string[] memory achievementTypes
    ) public onlyOwner {
        require(recipients.length == achievementTypes.length, "Arrays length mismatch");
        
        for (uint256 i = 0; i < recipients.length; i++) {
            if (!userAchievements[recipients[i]][achievementTypes[i]]) {
                mintReward(recipients[i], achievementTypes[i]);
            }
        }
    }

    /**
     * @dev Update metadata URI for an achievement type
     * @param achievementType The achievement type to update
     * @param metadataURI New metadata URI
     */
    function updateAchievementMetadata(
        string memory achievementType, 
        string memory metadataURI
    ) public onlyOwner {
        achievementMetadata[achievementType] = metadataURI;
        emit AchievementMetadataUpdated(achievementType, metadataURI);
    }

    /**
     * @dev Check if a user has a specific achievement
     * @param user Address to check
     * @param achievementType Achievement type to check
     * @return bool Whether the user has the achievement
     */
    function hasAchievement(address user, string memory achievementType) public view returns (bool) {
        return userAchievements[user][achievementType];
    }

    /**
     * @dev Get all tokens owned by a user
     * @param user Address to get tokens for
     * @return uint256[] Array of token IDs
     */
    function getUserTokens(address user) public view returns (uint256[] memory) {
        uint256 balance = balanceOf(user);
        uint256[] memory tokens = new uint256[](balance);
        uint256 currentIndex = 0;
        
        for (uint256 i = 0; i < _tokenIdCounter.current(); i++) {
            if (_ownerOf(i) == user) {
                tokens[currentIndex] = i;
                currentIndex++;
            }
        }
        
        return tokens;
    }

    /**
     * @dev Get achievement type for a token
     * @param tokenId Token ID to get achievement for
     * @return string Achievement type
     */
    function getTokenAchievement(uint256 tokenId) public view returns (string memory) {
        require(_ownerOf(tokenId) != address(0), "Token does not exist");
        return tokenAchievements[tokenId];
    }

    /**
     * @dev Get total number of minted tokens
     * @return uint256 Total supply
     */
    function totalSupply() public view returns (uint256) {
        return _tokenIdCounter.current();
    }

    // Override required functions
    function tokenURI(uint256 tokenId) public view override(ERC721, ERC721URIStorage) returns (string memory) {
        return super.tokenURI(tokenId);
    }

    function supportsInterface(bytes4 interfaceId) public view override(ERC721, ERC721URIStorage) returns (bool) {
        return super.supportsInterface(interfaceId);
    }

    /**
     * @dev Disable token transfers to make them soulbound (optional)
     * Uncomment the following function to make NFTs non-transferable
     */
    /*
    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 tokenId,
        uint256 batchSize
    ) internal override {
        require(from == address(0) || to == address(0), "SoulBound: Transfer not allowed");
        super._beforeTokenTransfer(from, to, tokenId, batchSize);
    }
    */
}
