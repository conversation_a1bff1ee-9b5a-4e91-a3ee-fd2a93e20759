{"version": 3, "sources": ["../../qrcode/lib/can-promise.js", "../../qrcode/lib/core/utils.js", "../../qrcode/lib/core/error-correction-level.js", "../../qrcode/lib/core/bit-buffer.js", "../../qrcode/lib/core/bit-matrix.js", "../../qrcode/lib/core/alignment-pattern.js", "../../qrcode/lib/core/finder-pattern.js", "../../qrcode/lib/core/mask-pattern.js", "../../qrcode/lib/core/error-correction-code.js", "../../qrcode/lib/core/galois-field.js", "../../qrcode/lib/core/polynomial.js", "../../qrcode/lib/core/reed-solomon-encoder.js", "../../qrcode/lib/core/version-check.js", "../../qrcode/lib/core/regex.js", "../../qrcode/lib/core/mode.js", "../../qrcode/lib/core/version.js", "../../qrcode/lib/core/format-info.js", "../../qrcode/lib/core/numeric-data.js", "../../qrcode/lib/core/alphanumeric-data.js", "../../encode-utf8/index.js", "../../qrcode/lib/core/byte-data.js", "../../qrcode/lib/core/kanji-data.js", "../../dijkstrajs/dijkstra.js", "../../qrcode/lib/core/segments.js", "../../qrcode/lib/core/qrcode.js", "../../qrcode/lib/renderer/utils.js", "../../qrcode/lib/renderer/canvas.js", "../../qrcode/lib/renderer/svg-tag.js", "../../qrcode/lib/browser.js", "../../@reown/appkit-ui/src/composites/wui-wallet-image/styles.ts", "../../@reown/appkit-ui/src/composites/wui-wallet-image/index.ts", "../../@reown/appkit-ui/src/composites/wui-all-wallets-image/styles.ts", "../../@reown/appkit-ui/src/composites/wui-all-wallets-image/index.ts", "../../@reown/appkit-ui/src/composites/wui-list-wallet/styles.ts", "../../@reown/appkit-ui/src/composites/wui-list-wallet/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-all-wallets-widget/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connect-announced-widget/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connect-custom-widget/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connect-external-widget/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connect-featured-widget/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connect-injected-widget/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connect-multi-chain-widget/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connect-recent-widget/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connect-recommended-widget/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connect-walletconnect-widget/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connector-list/styles.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connector-list/index.ts", "../../@reown/appkit-ui/src/composites/wui-tabs/styles.ts", "../../@reown/appkit-ui/src/composites/wui-tabs/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connecting-header/index.ts", "../../@reown/appkit-ui/src/composites/wui-button/styles.ts", "../../@reown/appkit-ui/src/composites/wui-button/index.ts", "../../@reown/appkit-ui/src/composites/wui-link/styles.ts", "../../@reown/appkit-ui/src/composites/wui-link/index.ts", "../../@reown/appkit-ui/src/components/wui-loading-thumbnail/styles.ts", "../../@reown/appkit-ui/src/components/wui-loading-thumbnail/index.ts", "../../@reown/appkit-ui/src/composites/wui-chip-button/styles.ts", "../../@reown/appkit-ui/src/composites/wui-chip-button/index.ts", "../../@reown/appkit-ui/src/composites/wui-cta-button/styles.ts", "../../@reown/appkit-ui/src/composites/wui-cta-button/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-mobile-download-links/styles.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-mobile-download-links/index.ts", "../../@reown/appkit-scaffold-ui/src/utils/w3m-connecting-widget/styles.ts", "../../@reown/appkit-scaffold-ui/src/utils/w3m-connecting-widget/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connecting-wc-browser/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connecting-wc-desktop/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connecting-wc-mobile/index.ts", "../../@reown/appkit-ui/src/utils/QrCode.ts", "../../@reown/appkit-ui/src/composites/wui-qr-code/styles.ts", "../../@reown/appkit-ui/src/composites/wui-qr-code/index.ts", "../../@reown/appkit-ui/src/components/wui-shimmer/styles.ts", "../../@reown/appkit-ui/src/components/wui-shimmer/index.ts", "../../@reown/appkit-ui/src/utils/ConstantsUtil.ts", "../../@reown/appkit-ui/src/composites/wui-ux-by-reown/styles.ts", "../../@reown/appkit-ui/src/composites/wui-ux-by-reown/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connecting-wc-qrcode/styles.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connecting-wc-qrcode/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connecting-wc-unsupported/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-connecting-wc-web/index.ts", "../../@reown/appkit-scaffold-ui/src/views/w3m-connecting-wc-view/index.ts", "../../@reown/appkit-scaffold-ui/src/views/w3m-connecting-wc-basic-view/index.ts", "../../lit-html/src/directives/ref.ts", "../../@reown/appkit-ui/src/composites/wui-switch/styles.ts", "../../@reown/appkit-ui/src/composites/wui-switch/index.ts", "../../@reown/appkit-ui/src/composites/wui-certified-switch/styles.ts", "../../@reown/appkit-ui/src/composites/wui-certified-switch/index.ts", "../../@reown/appkit-ui/src/composites/wui-input-element/styles.ts", "../../@reown/appkit-ui/src/composites/wui-input-element/index.ts", "../../@reown/appkit-ui/src/composites/wui-input-text/styles.ts", "../../@reown/appkit-ui/src/composites/wui-input-text/index.ts", "../../@reown/appkit-ui/src/composites/wui-search-bar/styles.ts", "../../@reown/appkit-ui/src/composites/wui-search-bar/index.ts", "../../@reown/appkit-ui/src/assets/svg/networkMd.ts", "../../@reown/appkit-ui/src/composites/wui-card-select-loader/styles.ts", "../../@reown/appkit-ui/src/composites/wui-card-select-loader/index.ts", "../../@reown/appkit-ui/src/layout/wui-grid/styles.ts", "../../@reown/appkit-ui/src/layout/wui-grid/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-all-wallets-list-item/styles.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-all-wallets-list-item/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-all-wallets-list/styles.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-all-wallets-list/index.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-all-wallets-search/styles.ts", "../../@reown/appkit-scaffold-ui/src/partials/w3m-all-wallets-search/index.ts", "../../@reown/appkit-scaffold-ui/src/views/w3m-all-wallets-view/index.ts", "../../@reown/appkit-ui/src/composites/wui-list-item/styles.ts", "../../@reown/appkit-ui/src/composites/wui-list-item/index.ts", "../../@reown/appkit-scaffold-ui/src/views/w3m-downloads-view/index.ts"], "sourcesContent": ["// can-promise has a crash in some versions of react native that dont have\n// standard global objects\n// https://github.com/soldair/node-qrcode/issues/157\n\nmodule.exports = function () {\n  return typeof Promise === 'function' && Promise.prototype && Promise.prototype.then\n}\n", "let toSJISFunction\nconst CODEWORDS_COUNT = [\n  0, // Not used\n  26, 44, 70, 100, 134, 172, 196, 242, 292, 346,\n  404, 466, 532, 581, 655, 733, 815, 901, 991, 1085,\n  1156, 1258, 1364, 1474, 1588, 1706, 1828, 1921, 2051, 2185,\n  2323, 2465, 2611, 2761, 2876, 3034, 3196, 3362, 3532, 3706\n]\n\n/**\n * Returns the QR Code size for the specified version\n *\n * @param  {Number} version QR Code version\n * @return {Number}         size of QR code\n */\nexports.getSymbolSize = function getSymbolSize (version) {\n  if (!version) throw new Error('\"version\" cannot be null or undefined')\n  if (version < 1 || version > 40) throw new Error('\"version\" should be in range from 1 to 40')\n  return version * 4 + 17\n}\n\n/**\n * Returns the total number of codewords used to store data and EC information.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Data length in bits\n */\nexports.getSymbolTotalCodewords = function getSymbolTotalCodewords (version) {\n  return CODEWORDS_COUNT[version]\n}\n\n/**\n * Encode data with Bose-Chaudhuri-Hocquenghem\n *\n * @param  {Number} data Value to encode\n * @return {Number}      Encoded value\n */\nexports.getBCHDigit = function (data) {\n  let digit = 0\n\n  while (data !== 0) {\n    digit++\n    data >>>= 1\n  }\n\n  return digit\n}\n\nexports.setToSJISFunction = function setToSJISFunction (f) {\n  if (typeof f !== 'function') {\n    throw new Error('\"toSJISFunc\" is not a valid function.')\n  }\n\n  toSJISFunction = f\n}\n\nexports.isKanjiModeEnabled = function () {\n  return typeof toSJISFunction !== 'undefined'\n}\n\nexports.toSJIS = function toSJIS (kanji) {\n  return toSJISFunction(kanji)\n}\n", "exports.L = { bit: 1 }\nexports.M = { bit: 0 }\nexports.Q = { bit: 3 }\nexports.H = { bit: 2 }\n\nfunction fromString (string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string')\n  }\n\n  const lcStr = string.toLowerCase()\n\n  switch (lcStr) {\n    case 'l':\n    case 'low':\n      return exports.L\n\n    case 'm':\n    case 'medium':\n      return exports.M\n\n    case 'q':\n    case 'quartile':\n      return exports.Q\n\n    case 'h':\n    case 'high':\n      return exports.H\n\n    default:\n      throw new Error('Unknown EC Level: ' + string)\n  }\n}\n\nexports.isValid = function isValid (level) {\n  return level && typeof level.bit !== 'undefined' &&\n    level.bit >= 0 && level.bit < 4\n}\n\nexports.from = function from (value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value\n  }\n\n  try {\n    return fromString(value)\n  } catch (e) {\n    return defaultValue\n  }\n}\n", "function BitBuffer () {\n  this.buffer = []\n  this.length = 0\n}\n\nBitBuffer.prototype = {\n\n  get: function (index) {\n    const bufIndex = Math.floor(index / 8)\n    return ((this.buffer[bufIndex] >>> (7 - index % 8)) & 1) === 1\n  },\n\n  put: function (num, length) {\n    for (let i = 0; i < length; i++) {\n      this.putBit(((num >>> (length - i - 1)) & 1) === 1)\n    }\n  },\n\n  getLengthInBits: function () {\n    return this.length\n  },\n\n  putBit: function (bit) {\n    const bufIndex = Math.floor(this.length / 8)\n    if (this.buffer.length <= bufIndex) {\n      this.buffer.push(0)\n    }\n\n    if (bit) {\n      this.buffer[bufIndex] |= (0x80 >>> (this.length % 8))\n    }\n\n    this.length++\n  }\n}\n\nmodule.exports = BitBuffer\n", "/**\n * Helper class to handle QR Code symbol modules\n *\n * @param {Number} size Symbol size\n */\nfunction BitMatrix (size) {\n  if (!size || size < 1) {\n    throw new Error('BitMatrix size must be defined and greater than 0')\n  }\n\n  this.size = size\n  this.data = new Uint8Array(size * size)\n  this.reservedBit = new Uint8Array(size * size)\n}\n\n/**\n * Set bit value at specified location\n * If reserved flag is set, this bit will be ignored during masking process\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n * @param {Boolean} reserved\n */\nBitMatrix.prototype.set = function (row, col, value, reserved) {\n  const index = row * this.size + col\n  this.data[index] = value\n  if (reserved) this.reservedBit[index] = true\n}\n\n/**\n * Returns bit value at specified location\n *\n * @param  {Number}  row\n * @param  {Number}  col\n * @return {Boolean}\n */\nBitMatrix.prototype.get = function (row, col) {\n  return this.data[row * this.size + col]\n}\n\n/**\n * Applies xor operator at specified location\n * (used during masking process)\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n */\nBitMatrix.prototype.xor = function (row, col, value) {\n  this.data[row * this.size + col] ^= value\n}\n\n/**\n * Check if bit at specified location is reserved\n *\n * @param {Number}   row\n * @param {Number}   col\n * @return {Boolean}\n */\nBitMatrix.prototype.isReserved = function (row, col) {\n  return this.reservedBit[row * this.size + col]\n}\n\nmodule.exports = BitMatrix\n", "/**\n * Alignment pattern are fixed reference pattern in defined positions\n * in a matrix symbology, which enables the decode software to re-synchronise\n * the coordinate mapping of the image modules in the event of moderate amounts\n * of distortion of the image.\n *\n * Alignment patterns are present only in QR Code symbols of version 2 or larger\n * and their number depends on the symbol version.\n */\n\nconst getSymbolSize = require('./utils').getSymbolSize\n\n/**\n * Calculate the row/column coordinates of the center module of each alignment pattern\n * for the specified QR Code version.\n *\n * The alignment patterns are positioned symmetrically on either side of the diagonal\n * running from the top left corner of the symbol to the bottom right corner.\n *\n * Since positions are simmetrical only half of the coordinates are returned.\n * Each item of the array will represent in turn the x and y coordinate.\n * @see {@link getPositions}\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinate\n */\nexports.getRowColCoords = function getRowColCoords (version) {\n  if (version === 1) return []\n\n  const posCount = Math.floor(version / 7) + 2\n  const size = getSymbolSize(version)\n  const intervals = size === 145 ? 26 : Math.ceil((size - 13) / (2 * posCount - 2)) * 2\n  const positions = [size - 7] // Last coord is always (size - 7)\n\n  for (let i = 1; i < posCount - 1; i++) {\n    positions[i] = positions[i - 1] - intervals\n  }\n\n  positions.push(6) // First coord is always 6\n\n  return positions.reverse()\n}\n\n/**\n * Returns an array containing the positions of each alignment pattern.\n * Each array's element represent the center point of the pattern as (x, y) coordinates\n *\n * Coordinates are calculated expanding the row/column coordinates returned by {@link getRowColCoords}\n * and filtering out the items that overlaps with finder pattern\n *\n * @example\n * For a Version 7 symbol {@link getRowColCoords} returns values 6, 22 and 38.\n * The alignment patterns, therefore, are to be centered on (row, column)\n * positions (6,22), (22,6), (22,22), (22,38), (38,22), (38,38).\n * Note that the coordinates (6,6), (6,38), (38,6) are occupied by finder patterns\n * and are not therefore used for alignment patterns.\n *\n * let pos = getPositions(7)\n * // [[6,22], [22,6], [22,22], [22,38], [38,22], [38,38]]\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions (version) {\n  const coords = []\n  const pos = exports.getRowColCoords(version)\n  const posLength = pos.length\n\n  for (let i = 0; i < posLength; i++) {\n    for (let j = 0; j < posLength; j++) {\n      // Skip if position is occupied by finder patterns\n      if ((i === 0 && j === 0) || // top-left\n          (i === 0 && j === posLength - 1) || // bottom-left\n          (i === posLength - 1 && j === 0)) { // top-right\n        continue\n      }\n\n      coords.push([pos[i], pos[j]])\n    }\n  }\n\n  return coords\n}\n", "const getSymbolSize = require('./utils').getSymbolSize\nconst FINDER_PATTERN_SIZE = 7\n\n/**\n * Returns an array containing the positions of each finder pattern.\n * Each array's element represent the top-left point of the pattern as (x, y) coordinates\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions (version) {\n  const size = getSymbolSize(version)\n\n  return [\n    // top-left\n    [0, 0],\n    // top-right\n    [size - FINDER_PATTERN_SIZE, 0],\n    // bottom-left\n    [0, size - FINDER_PATTERN_SIZE]\n  ]\n}\n", "/**\n * Data mask pattern reference\n * @type {Object}\n */\nexports.Patterns = {\n  PATTERN000: 0,\n  PATTERN001: 1,\n  PATTERN010: 2,\n  PATTERN011: 3,\n  PATTERN100: 4,\n  PATTERN101: 5,\n  PATTERN110: 6,\n  PATTERN111: 7\n}\n\n/**\n * Weighted penalty scores for the undesirable features\n * @type {Object}\n */\nconst PenaltyScores = {\n  N1: 3,\n  N2: 3,\n  N3: 40,\n  N4: 10\n}\n\n/**\n * Check if mask pattern value is valid\n *\n * @param  {Number}  mask    Mask pattern\n * @return {Boolean}         true if valid, false otherwise\n */\nexports.isValid = function isValid (mask) {\n  return mask != null && mask !== '' && !isNaN(mask) && mask >= 0 && mask <= 7\n}\n\n/**\n * Returns mask pattern from a value.\n * If value is not valid, returns undefined\n *\n * @param  {Number|String} value        Mask pattern value\n * @return {Number}                     Valid mask pattern or undefined\n */\nexports.from = function from (value) {\n  return exports.isValid(value) ? parseInt(value, 10) : undefined\n}\n\n/**\n* Find adjacent modules in row/column with the same color\n* and assign a penalty value.\n*\n* Points: N1 + i\n* i is the amount by which the number of adjacent modules of the same color exceeds 5\n*/\nexports.getPenaltyN1 = function getPenaltyN1 (data) {\n  const size = data.size\n  let points = 0\n  let sameCountCol = 0\n  let sameCountRow = 0\n  let lastCol = null\n  let lastRow = null\n\n  for (let row = 0; row < size; row++) {\n    sameCountCol = sameCountRow = 0\n    lastCol = lastRow = null\n\n    for (let col = 0; col < size; col++) {\n      let module = data.get(row, col)\n      if (module === lastCol) {\n        sameCountCol++\n      } else {\n        if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5)\n        lastCol = module\n        sameCountCol = 1\n      }\n\n      module = data.get(col, row)\n      if (module === lastRow) {\n        sameCountRow++\n      } else {\n        if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5)\n        lastRow = module\n        sameCountRow = 1\n      }\n    }\n\n    if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5)\n    if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5)\n  }\n\n  return points\n}\n\n/**\n * Find 2x2 blocks with the same color and assign a penalty value\n *\n * Points: N2 * (m - 1) * (n - 1)\n */\nexports.getPenaltyN2 = function getPenaltyN2 (data) {\n  const size = data.size\n  let points = 0\n\n  for (let row = 0; row < size - 1; row++) {\n    for (let col = 0; col < size - 1; col++) {\n      const last = data.get(row, col) +\n        data.get(row, col + 1) +\n        data.get(row + 1, col) +\n        data.get(row + 1, col + 1)\n\n      if (last === 4 || last === 0) points++\n    }\n  }\n\n  return points * PenaltyScores.N2\n}\n\n/**\n * Find 1:1:3:1:1 ratio (dark:light:dark:light:dark) pattern in row/column,\n * preceded or followed by light area 4 modules wide\n *\n * Points: N3 * number of pattern found\n */\nexports.getPenaltyN3 = function getPenaltyN3 (data) {\n  const size = data.size\n  let points = 0\n  let bitsCol = 0\n  let bitsRow = 0\n\n  for (let row = 0; row < size; row++) {\n    bitsCol = bitsRow = 0\n    for (let col = 0; col < size; col++) {\n      bitsCol = ((bitsCol << 1) & 0x7FF) | data.get(row, col)\n      if (col >= 10 && (bitsCol === 0x5D0 || bitsCol === 0x05D)) points++\n\n      bitsRow = ((bitsRow << 1) & 0x7FF) | data.get(col, row)\n      if (col >= 10 && (bitsRow === 0x5D0 || bitsRow === 0x05D)) points++\n    }\n  }\n\n  return points * PenaltyScores.N3\n}\n\n/**\n * Calculate proportion of dark modules in entire symbol\n *\n * Points: N4 * k\n *\n * k is the rating of the deviation of the proportion of dark modules\n * in the symbol from 50% in steps of 5%\n */\nexports.getPenaltyN4 = function getPenaltyN4 (data) {\n  let darkCount = 0\n  const modulesCount = data.data.length\n\n  for (let i = 0; i < modulesCount; i++) darkCount += data.data[i]\n\n  const k = Math.abs(Math.ceil((darkCount * 100 / modulesCount) / 5) - 10)\n\n  return k * PenaltyScores.N4\n}\n\n/**\n * Return mask value at given position\n *\n * @param  {Number} maskPattern Pattern reference value\n * @param  {Number} i           Row\n * @param  {Number} j           Column\n * @return {Boolean}            Mask value\n */\nfunction getMaskAt (maskPattern, i, j) {\n  switch (maskPattern) {\n    case exports.Patterns.PATTERN000: return (i + j) % 2 === 0\n    case exports.Patterns.PATTERN001: return i % 2 === 0\n    case exports.Patterns.PATTERN010: return j % 3 === 0\n    case exports.Patterns.PATTERN011: return (i + j) % 3 === 0\n    case exports.Patterns.PATTERN100: return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 === 0\n    case exports.Patterns.PATTERN101: return (i * j) % 2 + (i * j) % 3 === 0\n    case exports.Patterns.PATTERN110: return ((i * j) % 2 + (i * j) % 3) % 2 === 0\n    case exports.Patterns.PATTERN111: return ((i * j) % 3 + (i + j) % 2) % 2 === 0\n\n    default: throw new Error('bad maskPattern:' + maskPattern)\n  }\n}\n\n/**\n * Apply a mask pattern to a BitMatrix\n *\n * @param  {Number}    pattern Pattern reference number\n * @param  {BitMatrix} data    BitMatrix data\n */\nexports.applyMask = function applyMask (pattern, data) {\n  const size = data.size\n\n  for (let col = 0; col < size; col++) {\n    for (let row = 0; row < size; row++) {\n      if (data.isReserved(row, col)) continue\n      data.xor(row, col, getMaskAt(pattern, row, col))\n    }\n  }\n}\n\n/**\n * Returns the best mask pattern for data\n *\n * @param  {BitMatrix} data\n * @return {Number} Mask pattern reference number\n */\nexports.getBestMask = function getBestMask (data, setupFormatFunc) {\n  const numPatterns = Object.keys(exports.Patterns).length\n  let bestPattern = 0\n  let lowerPenalty = Infinity\n\n  for (let p = 0; p < numPatterns; p++) {\n    setupFormatFunc(p)\n    exports.applyMask(p, data)\n\n    // Calculate penalty\n    const penalty =\n      exports.getPenaltyN1(data) +\n      exports.getPenaltyN2(data) +\n      exports.getPenaltyN3(data) +\n      exports.getPenaltyN4(data)\n\n    // Undo previously applied mask\n    exports.applyMask(p, data)\n\n    if (penalty < lowerPenalty) {\n      lowerPenalty = penalty\n      bestPattern = p\n    }\n  }\n\n  return bestPattern\n}\n", "const ECLevel = require('./error-correction-level')\r\n\r\nconst EC_BLOCKS_TABLE = [\r\n// L  M  Q  H\r\n  1, 1, 1, 1,\r\n  1, 1, 1, 1,\r\n  1, 1, 2, 2,\r\n  1, 2, 2, 4,\r\n  1, 2, 4, 4,\r\n  2, 4, 4, 4,\r\n  2, 4, 6, 5,\r\n  2, 4, 6, 6,\r\n  2, 5, 8, 8,\r\n  4, 5, 8, 8,\r\n  4, 5, 8, 11,\r\n  4, 8, 10, 11,\r\n  4, 9, 12, 16,\r\n  4, 9, 16, 16,\r\n  6, 10, 12, 18,\r\n  6, 10, 17, 16,\r\n  6, 11, 16, 19,\r\n  6, 13, 18, 21,\r\n  7, 14, 21, 25,\r\n  8, 16, 20, 25,\r\n  8, 17, 23, 25,\r\n  9, 17, 23, 34,\r\n  9, 18, 25, 30,\r\n  10, 20, 27, 32,\r\n  12, 21, 29, 35,\r\n  12, 23, 34, 37,\r\n  12, 25, 34, 40,\r\n  13, 26, 35, 42,\r\n  14, 28, 38, 45,\r\n  15, 29, 40, 48,\r\n  16, 31, 43, 51,\r\n  17, 33, 45, 54,\r\n  18, 35, 48, 57,\r\n  19, 37, 51, 60,\r\n  19, 38, 53, 63,\r\n  20, 40, 56, 66,\r\n  21, 43, 59, 70,\r\n  22, 45, 62, 74,\r\n  24, 47, 65, 77,\r\n  25, 49, 68, 81\r\n]\r\n\r\nconst EC_CODEWORDS_TABLE = [\r\n// L  M  Q  H\r\n  7, 10, 13, 17,\r\n  10, 16, 22, 28,\r\n  15, 26, 36, 44,\r\n  20, 36, 52, 64,\r\n  26, 48, 72, 88,\r\n  36, 64, 96, 112,\r\n  40, 72, 108, 130,\r\n  48, 88, 132, 156,\r\n  60, 110, 160, 192,\r\n  72, 130, 192, 224,\r\n  80, 150, 224, 264,\r\n  96, 176, 260, 308,\r\n  104, 198, 288, 352,\r\n  120, 216, 320, 384,\r\n  132, 240, 360, 432,\r\n  144, 280, 408, 480,\r\n  168, 308, 448, 532,\r\n  180, 338, 504, 588,\r\n  196, 364, 546, 650,\r\n  224, 416, 600, 700,\r\n  224, 442, 644, 750,\r\n  252, 476, 690, 816,\r\n  270, 504, 750, 900,\r\n  300, 560, 810, 960,\r\n  312, 588, 870, 1050,\r\n  336, 644, 952, 1110,\r\n  360, 700, 1020, 1200,\r\n  390, 728, 1050, 1260,\r\n  420, 784, 1140, 1350,\r\n  450, 812, 1200, 1440,\r\n  480, 868, 1290, 1530,\r\n  510, 924, 1350, 1620,\r\n  540, 980, 1440, 1710,\r\n  570, 1036, 1530, 1800,\r\n  570, 1064, 1590, 1890,\r\n  600, 1120, 1680, 1980,\r\n  630, 1204, 1770, 2100,\r\n  660, 1260, 1860, 2220,\r\n  720, 1316, 1950, 2310,\r\n  750, 1372, 2040, 2430\r\n]\r\n\r\n/**\r\n * Returns the number of error correction block that the QR Code should contain\r\n * for the specified version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction blocks\r\n */\r\nexports.getBlocksCount = function getBlocksCount (version, errorCorrectionLevel) {\r\n  switch (errorCorrectionLevel) {\r\n    case ECLevel.L:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 0]\r\n    case ECLevel.M:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 1]\r\n    case ECLevel.Q:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 2]\r\n    case ECLevel.H:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 3]\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n\r\n/**\r\n * Returns the number of error correction codewords to use for the specified\r\n * version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction codewords\r\n */\r\nexports.getTotalCodewordsCount = function getTotalCodewordsCount (version, errorCorrectionLevel) {\r\n  switch (errorCorrectionLevel) {\r\n    case ECLevel.L:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 0]\r\n    case ECLevel.M:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 1]\r\n    case ECLevel.Q:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 2]\r\n    case ECLevel.H:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 3]\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n", "const EXP_TABLE = new Uint8Array(512)\nconst LOG_TABLE = new Uint8Array(256)\n/**\n * Precompute the log and anti-log tables for faster computation later\n *\n * For each possible value in the galois field 2^8, we will pre-compute\n * the logarithm and anti-logarithm (exponential) of this value\n *\n * ref {@link https://en.wikiversity.org/wiki/Reed%E2%80%93Solomon_codes_for_coders#Introduction_to_mathematical_fields}\n */\n;(function initTables () {\n  let x = 1\n  for (let i = 0; i < 255; i++) {\n    EXP_TABLE[i] = x\n    LOG_TABLE[x] = i\n\n    x <<= 1 // multiply by 2\n\n    // The QR code specification says to use byte-wise modulo 100011101 arithmetic.\n    // This means that when a number is 256 or larger, it should be XORed with 0x11D.\n    if (x & 0x100) { // similar to x >= 256, but a lot faster (because 0x100 == 256)\n      x ^= 0x11D\n    }\n  }\n\n  // Optimization: double the size of the anti-log table so that we don't need to mod 255 to\n  // stay inside the bounds (because we will mainly use this table for the multiplication of\n  // two GF numbers, no more).\n  // @see {@link mul}\n  for (let i = 255; i < 512; i++) {\n    EXP_TABLE[i] = EXP_TABLE[i - 255]\n  }\n}())\n\n/**\n * Returns log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.log = function log (n) {\n  if (n < 1) throw new Error('log(' + n + ')')\n  return LOG_TABLE[n]\n}\n\n/**\n * Returns anti-log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.exp = function exp (n) {\n  return EXP_TABLE[n]\n}\n\n/**\n * Multiplies two number inside Galois Field\n *\n * @param  {Number} x\n * @param  {Number} y\n * @return {Number}\n */\nexports.mul = function mul (x, y) {\n  if (x === 0 || y === 0) return 0\n\n  // should be EXP_TABLE[(LOG_TABLE[x] + LOG_TABLE[y]) % 255] if EXP_TABLE wasn't oversized\n  // @see {@link initTables}\n  return EXP_TABLE[LOG_TABLE[x] + LOG_TABLE[y]]\n}\n", "const GF = require('./galois-field')\n\n/**\n * Multiplies two polynomials inside Galois Field\n *\n * @param  {Uint8Array} p1 Polynomial\n * @param  {Uint8Array} p2 Polynomial\n * @return {Uint8Array}    Product of p1 and p2\n */\nexports.mul = function mul (p1, p2) {\n  const coeff = new Uint8Array(p1.length + p2.length - 1)\n\n  for (let i = 0; i < p1.length; i++) {\n    for (let j = 0; j < p2.length; j++) {\n      coeff[i + j] ^= GF.mul(p1[i], p2[j])\n    }\n  }\n\n  return coeff\n}\n\n/**\n * Calculate the remainder of polynomials division\n *\n * @param  {Uint8Array} divident Polynomial\n * @param  {Uint8Array} divisor  Polynomial\n * @return {Uint8Array}          Remainder\n */\nexports.mod = function mod (divident, divisor) {\n  let result = new Uint8Array(divident)\n\n  while ((result.length - divisor.length) >= 0) {\n    const coeff = result[0]\n\n    for (let i = 0; i < divisor.length; i++) {\n      result[i] ^= GF.mul(divisor[i], coeff)\n    }\n\n    // remove all zeros from buffer head\n    let offset = 0\n    while (offset < result.length && result[offset] === 0) offset++\n    result = result.slice(offset)\n  }\n\n  return result\n}\n\n/**\n * Generate an irreducible generator polynomial of specified degree\n * (used by Reed-Solomon encoder)\n *\n * @param  {Number} degree Degree of the generator polynomial\n * @return {Uint8Array}    Buffer containing polynomial coefficients\n */\nexports.generateECPolynomial = function generateECPolynomial (degree) {\n  let poly = new Uint8Array([1])\n  for (let i = 0; i < degree; i++) {\n    poly = exports.mul(poly, new Uint8Array([1, GF.exp(i)]))\n  }\n\n  return poly\n}\n", "const Polynomial = require('./polynomial')\n\nfunction ReedSolomonEncoder (degree) {\n  this.genPoly = undefined\n  this.degree = degree\n\n  if (this.degree) this.initialize(this.degree)\n}\n\n/**\n * Initialize the encoder.\n * The input param should correspond to the number of error correction codewords.\n *\n * @param  {Number} degree\n */\nReedSolomonEncoder.prototype.initialize = function initialize (degree) {\n  // create an irreducible generator polynomial\n  this.degree = degree\n  this.genPoly = Polynomial.generateECPolynomial(this.degree)\n}\n\n/**\n * Encodes a chunk of data\n *\n * @param  {Uint8Array} data Buffer containing input data\n * @return {Uint8Array}      Buffer containing encoded data\n */\nReedSolomonEncoder.prototype.encode = function encode (data) {\n  if (!this.genPoly) {\n    throw new Error('Encoder not initialized')\n  }\n\n  // Calculate EC for this data block\n  // extends data size to data+genPoly size\n  const paddedData = new Uint8Array(data.length + this.degree)\n  paddedData.set(data)\n\n  // The error correction codewords are the remainder after dividing the data codewords\n  // by a generator polynomial\n  const remainder = Polynomial.mod(paddedData, this.genPoly)\n\n  // return EC data blocks (last n byte, where n is the degree of genPoly)\n  // If coefficients number in remainder are less than genPoly degree,\n  // pad with 0s to the left to reach the needed number of coefficients\n  const start = this.degree - remainder.length\n  if (start > 0) {\n    const buff = new Uint8Array(this.degree)\n    buff.set(remainder, start)\n\n    return buff\n  }\n\n  return remainder\n}\n\nmodule.exports = ReedSolomonEncoder\n", "/**\n * Check if QR Code version is valid\n *\n * @param  {Number}  version QR Code version\n * @return {Boolean}         true if valid version, false otherwise\n */\nexports.isValid = function isValid (version) {\n  return !isNaN(version) && version >= 1 && version <= 40\n}\n", "const numeric = '[0-9]+'\nconst alphanumeric = '[A-Z $%*+\\\\-./:]+'\nlet kanji = '(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|' +\n  '[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|' +\n  '[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|' +\n  '[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+'\nkanji = kanji.replace(/u/g, '\\\\u')\n\nconst byte = '(?:(?![A-Z0-9 $%*+\\\\-./:]|' + kanji + ')(?:.|[\\r\\n]))+'\n\nexports.KANJI = new RegExp(kanji, 'g')\nexports.BYTE_KANJI = new RegExp('[^A-Z0-9 $%*+\\\\-./:]+', 'g')\nexports.BYTE = new RegExp(byte, 'g')\nexports.NUMERIC = new RegExp(numeric, 'g')\nexports.ALPHANUMERIC = new RegExp(alphanumeric, 'g')\n\nconst TEST_KANJI = new RegExp('^' + kanji + '$')\nconst TEST_NUMERIC = new RegExp('^' + numeric + '$')\nconst TEST_ALPHANUMERIC = new RegExp('^[A-Z0-9 $%*+\\\\-./:]+$')\n\nexports.testKanji = function testKanji (str) {\n  return TEST_KANJI.test(str)\n}\n\nexports.testNumeric = function testNumeric (str) {\n  return TEST_NUMERIC.test(str)\n}\n\nexports.testAlphanumeric = function testAlphanumeric (str) {\n  return TEST_ALPHANUMERIC.test(str)\n}\n", "const VersionCheck = require('./version-check')\nconst Regex = require('./regex')\n\n/**\n * Numeric mode encodes data from the decimal digit set (0 - 9)\n * (byte values 30HEX to 39HEX).\n * Normally, 3 data characters are represented by 10 bits.\n *\n * @type {Object}\n */\nexports.NUMERIC = {\n  id: 'Numeric',\n  bit: 1 << 0,\n  ccBits: [10, 12, 14]\n}\n\n/**\n * Alphanumeric mode encodes data from a set of 45 characters,\n * i.e. 10 numeric digits (0 - 9),\n *      26 alphabetic characters (A - Z),\n *   and 9 symbols (SP, $, %, *, +, -, ., /, :).\n * Normally, two input characters are represented by 11 bits.\n *\n * @type {Object}\n */\nexports.ALPHANUMERIC = {\n  id: 'Alphanumeric',\n  bit: 1 << 1,\n  ccBits: [9, 11, 13]\n}\n\n/**\n * In byte mode, data is encoded at 8 bits per character.\n *\n * @type {Object}\n */\nexports.BYTE = {\n  id: 'Byte',\n  bit: 1 << 2,\n  ccBits: [8, 16, 16]\n}\n\n/**\n * The Kanji mode efficiently encodes Kanji characters in accordance with\n * the Shift JIS system based on JIS X 0208.\n * The Shift JIS values are shifted from the JIS X 0208 values.\n * JIS X 0208 gives details of the shift coded representation.\n * Each two-byte character value is compacted to a 13-bit binary codeword.\n *\n * @type {Object}\n */\nexports.KANJI = {\n  id: 'Kanji',\n  bit: 1 << 3,\n  ccBits: [8, 10, 12]\n}\n\n/**\n * Mixed mode will contain a sequences of data in a combination of any of\n * the modes described above\n *\n * @type {Object}\n */\nexports.MIXED = {\n  bit: -1\n}\n\n/**\n * Returns the number of bits needed to store the data length\n * according to QR Code specifications.\n *\n * @param  {Mode}   mode    Data mode\n * @param  {Number} version QR Code version\n * @return {Number}         Number of bits\n */\nexports.getCharCountIndicator = function getCharCountIndicator (mode, version) {\n  if (!mode.ccBits) throw new Error('Invalid mode: ' + mode)\n\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid version: ' + version)\n  }\n\n  if (version >= 1 && version < 10) return mode.ccBits[0]\n  else if (version < 27) return mode.ccBits[1]\n  return mode.ccBits[2]\n}\n\n/**\n * Returns the most efficient mode to store the specified data\n *\n * @param  {String} dataStr Input data string\n * @return {Mode}           Best mode\n */\nexports.getBestModeForData = function getBestModeForData (dataStr) {\n  if (Regex.testNumeric(dataStr)) return exports.NUMERIC\n  else if (Regex.testAlphanumeric(dataStr)) return exports.ALPHANUMERIC\n  else if (Regex.testKanji(dataStr)) return exports.KANJI\n  else return exports.BYTE\n}\n\n/**\n * Return mode name as string\n *\n * @param {Mode} mode Mode object\n * @returns {String}  Mode name\n */\nexports.toString = function toString (mode) {\n  if (mode && mode.id) return mode.id\n  throw new Error('Invalid mode')\n}\n\n/**\n * Check if input param is a valid mode object\n *\n * @param   {Mode}    mode Mode object\n * @returns {Boolean} True if valid mode, false otherwise\n */\nexports.isValid = function isValid (mode) {\n  return mode && mode.bit && mode.ccBits\n}\n\n/**\n * Get mode object from its name\n *\n * @param   {String} string Mode name\n * @returns {Mode}          Mode object\n */\nfunction fromString (string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string')\n  }\n\n  const lcStr = string.toLowerCase()\n\n  switch (lcStr) {\n    case 'numeric':\n      return exports.NUMERIC\n    case 'alphanumeric':\n      return exports.ALPHANUMERIC\n    case 'kanji':\n      return exports.KANJI\n    case 'byte':\n      return exports.BYTE\n    default:\n      throw new Error('Unknown mode: ' + string)\n  }\n}\n\n/**\n * Returns mode from a value.\n * If value is not a valid mode, returns defaultValue\n *\n * @param  {Mode|String} value        Encoding mode\n * @param  {Mode}        defaultValue Fallback value\n * @return {Mode}                     Encoding mode\n */\nexports.from = function from (value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value\n  }\n\n  try {\n    return fromString(value)\n  } catch (e) {\n    return defaultValue\n  }\n}\n", "const Utils = require('./utils')\nconst ECCode = require('./error-correction-code')\nconst ECLevel = require('./error-correction-level')\nconst Mode = require('./mode')\nconst VersionCheck = require('./version-check')\n\n// Generator polynomial used to encode version information\nconst G18 = (1 << 12) | (1 << 11) | (1 << 10) | (1 << 9) | (1 << 8) | (1 << 5) | (1 << 2) | (1 << 0)\nconst G18_BCH = Utils.getBCHDigit(G18)\n\nfunction getBestVersionForDataLength (mode, length, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, mode)) {\n      return currentVersion\n    }\n  }\n\n  return undefined\n}\n\nfunction getReservedBitsCount (mode, version) {\n  // Character count indicator + mode indicator bits\n  return Mode.getCharCountIndicator(mode, version) + 4\n}\n\nfunction getTotalBitsFromDataArray (segments, version) {\n  let totalBits = 0\n\n  segments.forEach(function (data) {\n    const reservedBits = getReservedBitsCount(data.mode, version)\n    totalBits += reservedBits + data.getBitsLength()\n  })\n\n  return totalBits\n}\n\nfunction getBestVersionForMixedData (segments, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    const length = getTotalBitsFromDataArray(segments, currentVersion)\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, Mode.MIXED)) {\n      return currentVersion\n    }\n  }\n\n  return undefined\n}\n\n/**\n * Returns version number from a value.\n * If value is not a valid version, returns defaultValue\n *\n * @param  {Number|String} value        QR Code version\n * @param  {Number}        defaultValue Fallback value\n * @return {Number}                     QR Code version number\n */\nexports.from = function from (value, defaultValue) {\n  if (VersionCheck.isValid(value)) {\n    return parseInt(value, 10)\n  }\n\n  return defaultValue\n}\n\n/**\n * Returns how much data can be stored with the specified QR code version\n * and error correction level\n *\n * @param  {Number} version              QR Code version (1-40)\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Mode}   mode                 Data mode\n * @return {Number}                      Quantity of storable data\n */\nexports.getCapacity = function getCapacity (version, errorCorrectionLevel, mode) {\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid QR Code version')\n  }\n\n  // Use Byte mode as default\n  if (typeof mode === 'undefined') mode = Mode.BYTE\n\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n\n  // Total number of data codewords\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8\n\n  if (mode === Mode.MIXED) return dataTotalCodewordsBits\n\n  const usableBits = dataTotalCodewordsBits - getReservedBitsCount(mode, version)\n\n  // Return max number of storable codewords\n  switch (mode) {\n    case Mode.NUMERIC:\n      return Math.floor((usableBits / 10) * 3)\n\n    case Mode.ALPHANUMERIC:\n      return Math.floor((usableBits / 11) * 2)\n\n    case Mode.KANJI:\n      return Math.floor(usableBits / 13)\n\n    case Mode.BYTE:\n    default:\n      return Math.floor(usableBits / 8)\n  }\n}\n\n/**\n * Returns the minimum version needed to contain the amount of data\n *\n * @param  {Segment} data                    Segment of data\n * @param  {Number} [errorCorrectionLevel=H] Error correction level\n * @param  {Mode} mode                       Data mode\n * @return {Number}                          QR Code version\n */\nexports.getBestVersionForData = function getBestVersionForData (data, errorCorrectionLevel) {\n  let seg\n\n  const ecl = ECLevel.from(errorCorrectionLevel, ECLevel.M)\n\n  if (Array.isArray(data)) {\n    if (data.length > 1) {\n      return getBestVersionForMixedData(data, ecl)\n    }\n\n    if (data.length === 0) {\n      return 1\n    }\n\n    seg = data[0]\n  } else {\n    seg = data\n  }\n\n  return getBestVersionForDataLength(seg.mode, seg.getLength(), ecl)\n}\n\n/**\n * Returns version information with relative error correction bits\n *\n * The version information is included in QR Code symbols of version 7 or larger.\n * It consists of an 18-bit sequence containing 6 data bits,\n * with 12 error correction bits calculated using the (18, 6) Golay code.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Encoded version info bits\n */\nexports.getEncodedBits = function getEncodedBits (version) {\n  if (!VersionCheck.isValid(version) || version < 7) {\n    throw new Error('Invalid QR Code version')\n  }\n\n  let d = version << 12\n\n  while (Utils.getBCHDigit(d) - G18_BCH >= 0) {\n    d ^= (G18 << (Utils.getBCHDigit(d) - G18_BCH))\n  }\n\n  return (version << 12) | d\n}\n", "const Utils = require('./utils')\n\nconst G15 = (1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0)\nconst G15_MASK = (1 << 14) | (1 << 12) | (1 << 10) | (1 << 4) | (1 << 1)\nconst G15_BCH = Utils.getBCHDigit(G15)\n\n/**\n * Returns format information with relative error correction bits\n *\n * The format information is a 15-bit sequence containing 5 data bits,\n * with 10 error correction bits calculated using the (15, 5) BCH code.\n *\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Number} mask                 Mask pattern\n * @return {Number}                      Encoded format information bits\n */\nexports.getEncodedBits = function getEncodedBits (errorCorrectionLevel, mask) {\n  const data = ((errorCorrectionLevel.bit << 3) | mask)\n  let d = data << 10\n\n  while (Utils.getBCHDigit(d) - G15_BCH >= 0) {\n    d ^= (G15 << (Utils.getBCHDigit(d) - G15_BCH))\n  }\n\n  // xor final data with mask pattern in order to ensure that\n  // no combination of Error Correction Level and data mask pattern\n  // will result in an all-zero data string\n  return ((data << 10) | d) ^ G15_MASK\n}\n", "const Mode = require('./mode')\n\nfunction NumericData (data) {\n  this.mode = Mode.NUMERIC\n  this.data = data.toString()\n}\n\nNumericData.getBitsLength = function getBitsLength (length) {\n  return 10 * Math.floor(length / 3) + ((length % 3) ? ((length % 3) * 3 + 1) : 0)\n}\n\nNumericData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nNumericData.prototype.getBitsLength = function getBitsLength () {\n  return NumericData.getBitsLength(this.data.length)\n}\n\nNumericData.prototype.write = function write (bitBuffer) {\n  let i, group, value\n\n  // The input data string is divided into groups of three digits,\n  // and each group is converted to its 10-bit binary equivalent.\n  for (i = 0; i + 3 <= this.data.length; i += 3) {\n    group = this.data.substr(i, 3)\n    value = parseInt(group, 10)\n\n    bitBuffer.put(value, 10)\n  }\n\n  // If the number of input digits is not an exact multiple of three,\n  // the final one or two digits are converted to 4 or 7 bits respectively.\n  const remainingNum = this.data.length - i\n  if (remainingNum > 0) {\n    group = this.data.substr(i)\n    value = parseInt(group, 10)\n\n    bitBuffer.put(value, remainingNum * 3 + 1)\n  }\n}\n\nmodule.exports = NumericData\n", "const Mode = require('./mode')\n\n/**\n * Array of characters available in alphanumeric mode\n *\n * As per QR Code specification, to each character\n * is assigned a value from 0 to 44 which in this case coincides\n * with the array index\n *\n * @type {Array}\n */\nconst ALPHA_NUM_CHARS = [\n  '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',\n  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',\n  'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',\n  ' ', '$', '%', '*', '+', '-', '.', '/', ':'\n]\n\nfunction AlphanumericData (data) {\n  this.mode = Mode.ALPHANUMERIC\n  this.data = data\n}\n\nAlphanumericData.getBitsLength = function getBitsLength (length) {\n  return 11 * Math.floor(length / 2) + 6 * (length % 2)\n}\n\nAlphanumericData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nAlphanumericData.prototype.getBitsLength = function getBitsLength () {\n  return AlphanumericData.getBitsLength(this.data.length)\n}\n\nAlphanumericData.prototype.write = function write (bitBuffer) {\n  let i\n\n  // Input data characters are divided into groups of two characters\n  // and encoded as 11-bit binary codes.\n  for (i = 0; i + 2 <= this.data.length; i += 2) {\n    // The character value of the first character is multiplied by 45\n    let value = ALPHA_NUM_CHARS.indexOf(this.data[i]) * 45\n\n    // The character value of the second digit is added to the product\n    value += ALPHA_NUM_CHARS.indexOf(this.data[i + 1])\n\n    // The sum is then stored as 11-bit binary number\n    bitBuffer.put(value, 11)\n  }\n\n  // If the number of input data characters is not a multiple of two,\n  // the character value of the final character is encoded as a 6-bit binary number.\n  if (this.data.length % 2) {\n    bitBuffer.put(ALPHA_NUM_CHARS.indexOf(this.data[i]), 6)\n  }\n}\n\nmodule.exports = AlphanumericData\n", "'use strict'\n\nmodule.exports = function encodeUtf8 (input) {\n  var result = []\n  var size = input.length\n\n  for (var index = 0; index < size; index++) {\n    var point = input.charCodeAt(index)\n\n    if (point >= 0xD800 && point <= 0xDBFF && size > index + 1) {\n      var second = input.charCodeAt(index + 1)\n\n      if (second >= 0xDC00 && second <= 0xDFFF) {\n        // https://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n        point = (point - 0xD800) * 0x400 + second - 0xDC00 + 0x10000\n        index += 1\n      }\n    }\n\n    // US-ASCII\n    if (point < 0x80) {\n      result.push(point)\n      continue\n    }\n\n    // 2-byte UTF-8\n    if (point < 0x800) {\n      result.push((point >> 6) | 192)\n      result.push((point & 63) | 128)\n      continue\n    }\n\n    // 3-byte UTF-8\n    if (point < 0xD800 || (point >= 0xE000 && point < 0x10000)) {\n      result.push((point >> 12) | 224)\n      result.push(((point >> 6) & 63) | 128)\n      result.push((point & 63) | 128)\n      continue\n    }\n\n    // 4-byte UTF-8\n    if (point >= 0x10000 && point <= 0x10FFFF) {\n      result.push((point >> 18) | 240)\n      result.push(((point >> 12) & 63) | 128)\n      result.push(((point >> 6) & 63) | 128)\n      result.push((point & 63) | 128)\n      continue\n    }\n\n    // Invalid character\n    result.push(0xEF, 0xBF, 0xBD)\n  }\n\n  return new Uint8Array(result).buffer\n}\n", "const encodeUtf8 = require('encode-utf8')\nconst Mode = require('./mode')\n\nfunction ByteData (data) {\n  this.mode = Mode.BYTE\n  if (typeof (data) === 'string') {\n    data = encodeUtf8(data)\n  }\n  this.data = new Uint8Array(data)\n}\n\nByteData.getBitsLength = function getBitsLength (length) {\n  return length * 8\n}\n\nByteData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nByteData.prototype.getBitsLength = function getBitsLength () {\n  return ByteData.getBitsLength(this.data.length)\n}\n\nByteData.prototype.write = function (bitBuffer) {\n  for (let i = 0, l = this.data.length; i < l; i++) {\n    bitBuffer.put(this.data[i], 8)\n  }\n}\n\nmodule.exports = ByteData\n", "const Mode = require('./mode')\nconst Utils = require('./utils')\n\nfunction KanjiData (data) {\n  this.mode = Mode.KANJI\n  this.data = data\n}\n\nKanjiData.getBitsLength = function getBitsLength (length) {\n  return length * 13\n}\n\nKanjiData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nKanjiData.prototype.getBitsLength = function getBitsLength () {\n  return KanjiData.getBitsLength(this.data.length)\n}\n\nKanjiData.prototype.write = function (bitBuffer) {\n  let i\n\n  // In the Shift JIS system, Kanji characters are represented by a two byte combination.\n  // These byte values are shifted from the JIS X 0208 values.\n  // JIS X 0208 gives details of the shift coded representation.\n  for (i = 0; i < this.data.length; i++) {\n    let value = Utils.toSJIS(this.data[i])\n\n    // For characters with Shift JIS values from 0x8140 to 0x9FFC:\n    if (value >= 0x8140 && value <= 0x9FFC) {\n      // Subtract 0x8140 from Shift JIS value\n      value -= 0x8140\n\n    // For characters with Shift JIS values from 0xE040 to 0xEBBF\n    } else if (value >= 0xE040 && value <= 0xEBBF) {\n      // Subtract 0xC140 from Shift JIS value\n      value -= 0xC140\n    } else {\n      throw new Error(\n        'Invalid SJIS character: ' + this.data[i] + '\\n' +\n        'Make sure your charset is UTF-8')\n    }\n\n    // Multiply most significant byte of result by 0xC0\n    // and add least significant byte to product\n    value = (((value >>> 8) & 0xff) * 0xC0) + (value & 0xff)\n\n    // Convert result to a 13-bit binary string\n    bitBuffer.put(value, 13)\n  }\n}\n\nmodule.exports = KanjiData\n", "'use strict';\n\n/******************************************************************************\n * Created 2008-08-19.\n *\n * Dijkstra path-finding functions. Adapted from the Dijkstar Python project.\n *\n * Copyright (C) 2008\n *   <PERSON> <<EMAIL>>\n *   All rights reserved\n *\n * Licensed under the MIT license.\n *\n *   http://www.opensource.org/licenses/mit-license.php\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n *****************************************************************************/\nvar dijkstra = {\n  single_source_shortest_paths: function(graph, s, d) {\n    // Predecessor map for each node that has been encountered.\n    // node ID => predecessor node ID\n    var predecessors = {};\n\n    // Costs of shortest paths from s to all nodes encountered.\n    // node ID => cost\n    var costs = {};\n    costs[s] = 0;\n\n    // Costs of shortest paths from s to all nodes encountered; differs from\n    // `costs` in that it provides easy access to the node that currently has\n    // the known shortest path from s.\n    // XXX: Do we actually need both `costs` and `open`?\n    var open = dijkstra.PriorityQueue.make();\n    open.push(s, 0);\n\n    var closest,\n        u, v,\n        cost_of_s_to_u,\n        adjacent_nodes,\n        cost_of_e,\n        cost_of_s_to_u_plus_cost_of_e,\n        cost_of_s_to_v,\n        first_visit;\n    while (!open.empty()) {\n      // In the nodes remaining in graph that have a known cost from s,\n      // find the node, u, that currently has the shortest path from s.\n      closest = open.pop();\n      u = closest.value;\n      cost_of_s_to_u = closest.cost;\n\n      // Get nodes adjacent to u...\n      adjacent_nodes = graph[u] || {};\n\n      // ...and explore the edges that connect u to those nodes, updating\n      // the cost of the shortest paths to any or all of those nodes as\n      // necessary. v is the node across the current edge from u.\n      for (v in adjacent_nodes) {\n        if (adjacent_nodes.hasOwnProperty(v)) {\n          // Get the cost of the edge running from u to v.\n          cost_of_e = adjacent_nodes[v];\n\n          // Cost of s to u plus the cost of u to v across e--this is *a*\n          // cost from s to v that may or may not be less than the current\n          // known cost to v.\n          cost_of_s_to_u_plus_cost_of_e = cost_of_s_to_u + cost_of_e;\n\n          // If we haven't visited v yet OR if the current known cost from s to\n          // v is greater than the new cost we just found (cost of s to u plus\n          // cost of u to v across e), update v's cost in the cost list and\n          // update v's predecessor in the predecessor list (it's now u).\n          cost_of_s_to_v = costs[v];\n          first_visit = (typeof costs[v] === 'undefined');\n          if (first_visit || cost_of_s_to_v > cost_of_s_to_u_plus_cost_of_e) {\n            costs[v] = cost_of_s_to_u_plus_cost_of_e;\n            open.push(v, cost_of_s_to_u_plus_cost_of_e);\n            predecessors[v] = u;\n          }\n        }\n      }\n    }\n\n    if (typeof d !== 'undefined' && typeof costs[d] === 'undefined') {\n      var msg = ['Could not find a path from ', s, ' to ', d, '.'].join('');\n      throw new Error(msg);\n    }\n\n    return predecessors;\n  },\n\n  extract_shortest_path_from_predecessor_list: function(predecessors, d) {\n    var nodes = [];\n    var u = d;\n    var predecessor;\n    while (u) {\n      nodes.push(u);\n      predecessor = predecessors[u];\n      u = predecessors[u];\n    }\n    nodes.reverse();\n    return nodes;\n  },\n\n  find_path: function(graph, s, d) {\n    var predecessors = dijkstra.single_source_shortest_paths(graph, s, d);\n    return dijkstra.extract_shortest_path_from_predecessor_list(\n      predecessors, d);\n  },\n\n  /**\n   * A very naive priority queue implementation.\n   */\n  PriorityQueue: {\n    make: function (opts) {\n      var T = dijkstra.PriorityQueue,\n          t = {},\n          key;\n      opts = opts || {};\n      for (key in T) {\n        if (T.hasOwnProperty(key)) {\n          t[key] = T[key];\n        }\n      }\n      t.queue = [];\n      t.sorter = opts.sorter || T.default_sorter;\n      return t;\n    },\n\n    default_sorter: function (a, b) {\n      return a.cost - b.cost;\n    },\n\n    /**\n     * Add a new item to the queue and ensure the highest priority element\n     * is at the front of the queue.\n     */\n    push: function (value, cost) {\n      var item = {value: value, cost: cost};\n      this.queue.push(item);\n      this.queue.sort(this.sorter);\n    },\n\n    /**\n     * Return the highest priority element in the queue.\n     */\n    pop: function () {\n      return this.queue.shift();\n    },\n\n    empty: function () {\n      return this.queue.length === 0;\n    }\n  }\n};\n\n\n// node.js module exports\nif (typeof module !== 'undefined') {\n  module.exports = dijkstra;\n}\n", "const Mode = require('./mode')\nconst NumericData = require('./numeric-data')\nconst AlphanumericData = require('./alphanumeric-data')\nconst ByteData = require('./byte-data')\nconst KanjiData = require('./kanji-data')\nconst Regex = require('./regex')\nconst Utils = require('./utils')\nconst dijkstra = require('dijkstrajs')\n\n/**\n * Returns UTF8 byte length\n *\n * @param  {String} str Input string\n * @return {Number}     Number of byte\n */\nfunction getStringByteLength (str) {\n  return unescape(encodeURIComponent(str)).length\n}\n\n/**\n * Get a list of segments of the specified mode\n * from a string\n *\n * @param  {Mode}   mode Segment mode\n * @param  {String} str  String to process\n * @return {Array}       Array of object with segments data\n */\nfunction getSegments (regex, mode, str) {\n  const segments = []\n  let result\n\n  while ((result = regex.exec(str)) !== null) {\n    segments.push({\n      data: result[0],\n      index: result.index,\n      mode: mode,\n      length: result[0].length\n    })\n  }\n\n  return segments\n}\n\n/**\n * Extracts a series of segments with the appropriate\n * modes from a string\n *\n * @param  {String} dataStr Input string\n * @return {Array}          Array of object with segments data\n */\nfunction getSegmentsFromString (dataStr) {\n  const numSegs = getSegments(Regex.NUMERIC, Mode.NUMERIC, dataStr)\n  const alphaNumSegs = getSegments(Regex.ALPHANUMERIC, Mode.ALPHANUMERIC, dataStr)\n  let byteSegs\n  let kanjiSegs\n\n  if (Utils.isKanjiModeEnabled()) {\n    byteSegs = getSegments(Regex.BYTE, Mode.BYTE, dataStr)\n    kanjiSegs = getSegments(Regex.KANJI, Mode.KANJI, dataStr)\n  } else {\n    byteSegs = getSegments(Regex.BYTE_KANJI, Mode.BYTE, dataStr)\n    kanjiSegs = []\n  }\n\n  const segs = numSegs.concat(alphaNumSegs, byteSegs, kanjiSegs)\n\n  return segs\n    .sort(function (s1, s2) {\n      return s1.index - s2.index\n    })\n    .map(function (obj) {\n      return {\n        data: obj.data,\n        mode: obj.mode,\n        length: obj.length\n      }\n    })\n}\n\n/**\n * Returns how many bits are needed to encode a string of\n * specified length with the specified mode\n *\n * @param  {Number} length String length\n * @param  {Mode} mode     Segment mode\n * @return {Number}        Bit length\n */\nfunction getSegmentBitsLength (length, mode) {\n  switch (mode) {\n    case Mode.NUMERIC:\n      return NumericData.getBitsLength(length)\n    case Mode.ALPHANUMERIC:\n      return AlphanumericData.getBitsLength(length)\n    case Mode.KANJI:\n      return KanjiData.getBitsLength(length)\n    case Mode.BYTE:\n      return ByteData.getBitsLength(length)\n  }\n}\n\n/**\n * Merges adjacent segments which have the same mode\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction mergeSegments (segs) {\n  return segs.reduce(function (acc, curr) {\n    const prevSeg = acc.length - 1 >= 0 ? acc[acc.length - 1] : null\n    if (prevSeg && prevSeg.mode === curr.mode) {\n      acc[acc.length - 1].data += curr.data\n      return acc\n    }\n\n    acc.push(curr)\n    return acc\n  }, [])\n}\n\n/**\n * Generates a list of all possible nodes combination which\n * will be used to build a segments graph.\n *\n * Nodes are divided by groups. Each group will contain a list of all the modes\n * in which is possible to encode the given text.\n *\n * For example the text '12345' can be encoded as Numeric, Alphanumeric or Byte.\n * The group for '12345' will contain then 3 objects, one for each\n * possible encoding mode.\n *\n * Each node represents a possible segment.\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction buildNodes (segs) {\n  const nodes = []\n  for (let i = 0; i < segs.length; i++) {\n    const seg = segs[i]\n\n    switch (seg.mode) {\n      case Mode.NUMERIC:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.ALPHANUMERIC, length: seg.length },\n          { data: seg.data, mode: Mode.BYTE, length: seg.length }\n        ])\n        break\n      case Mode.ALPHANUMERIC:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.BYTE, length: seg.length }\n        ])\n        break\n      case Mode.KANJI:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }\n        ])\n        break\n      case Mode.BYTE:\n        nodes.push([\n          { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }\n        ])\n    }\n  }\n\n  return nodes\n}\n\n/**\n * Builds a graph from a list of nodes.\n * All segments in each node group will be connected with all the segments of\n * the next group and so on.\n *\n * At each connection will be assigned a weight depending on the\n * segment's byte length.\n *\n * @param  {Array} nodes    Array of object with segments data\n * @param  {Number} version QR Code version\n * @return {Object}         Graph of all possible segments\n */\nfunction buildGraph (nodes, version) {\n  const table = {}\n  const graph = { start: {} }\n  let prevNodeIds = ['start']\n\n  for (let i = 0; i < nodes.length; i++) {\n    const nodeGroup = nodes[i]\n    const currentNodeIds = []\n\n    for (let j = 0; j < nodeGroup.length; j++) {\n      const node = nodeGroup[j]\n      const key = '' + i + j\n\n      currentNodeIds.push(key)\n      table[key] = { node: node, lastCount: 0 }\n      graph[key] = {}\n\n      for (let n = 0; n < prevNodeIds.length; n++) {\n        const prevNodeId = prevNodeIds[n]\n\n        if (table[prevNodeId] && table[prevNodeId].node.mode === node.mode) {\n          graph[prevNodeId][key] =\n            getSegmentBitsLength(table[prevNodeId].lastCount + node.length, node.mode) -\n            getSegmentBitsLength(table[prevNodeId].lastCount, node.mode)\n\n          table[prevNodeId].lastCount += node.length\n        } else {\n          if (table[prevNodeId]) table[prevNodeId].lastCount = node.length\n\n          graph[prevNodeId][key] = getSegmentBitsLength(node.length, node.mode) +\n            4 + Mode.getCharCountIndicator(node.mode, version) // switch cost\n        }\n      }\n    }\n\n    prevNodeIds = currentNodeIds\n  }\n\n  for (let n = 0; n < prevNodeIds.length; n++) {\n    graph[prevNodeIds[n]].end = 0\n  }\n\n  return { map: graph, table: table }\n}\n\n/**\n * Builds a segment from a specified data and mode.\n * If a mode is not specified, the more suitable will be used.\n *\n * @param  {String} data             Input data\n * @param  {Mode | String} modesHint Data mode\n * @return {Segment}                 Segment\n */\nfunction buildSingleSegment (data, modesHint) {\n  let mode\n  const bestMode = Mode.getBestModeForData(data)\n\n  mode = Mode.from(modesHint, bestMode)\n\n  // Make sure data can be encoded\n  if (mode !== Mode.BYTE && mode.bit < bestMode.bit) {\n    throw new Error('\"' + data + '\"' +\n      ' cannot be encoded with mode ' + Mode.toString(mode) +\n      '.\\n Suggested mode is: ' + Mode.toString(bestMode))\n  }\n\n  // Use Mode.BYTE if Kanji support is disabled\n  if (mode === Mode.KANJI && !Utils.isKanjiModeEnabled()) {\n    mode = Mode.BYTE\n  }\n\n  switch (mode) {\n    case Mode.NUMERIC:\n      return new NumericData(data)\n\n    case Mode.ALPHANUMERIC:\n      return new AlphanumericData(data)\n\n    case Mode.KANJI:\n      return new KanjiData(data)\n\n    case Mode.BYTE:\n      return new ByteData(data)\n  }\n}\n\n/**\n * Builds a list of segments from an array.\n * Array can contain Strings or Objects with segment's info.\n *\n * For each item which is a string, will be generated a segment with the given\n * string and the more appropriate encoding mode.\n *\n * For each item which is an object, will be generated a segment with the given\n * data and mode.\n * Objects must contain at least the property \"data\".\n * If property \"mode\" is not present, the more suitable mode will be used.\n *\n * @param  {Array} array Array of objects with segments data\n * @return {Array}       Array of Segments\n */\nexports.fromArray = function fromArray (array) {\n  return array.reduce(function (acc, seg) {\n    if (typeof seg === 'string') {\n      acc.push(buildSingleSegment(seg, null))\n    } else if (seg.data) {\n      acc.push(buildSingleSegment(seg.data, seg.mode))\n    }\n\n    return acc\n  }, [])\n}\n\n/**\n * Builds an optimized sequence of segments from a string,\n * which will produce the shortest possible bitstream.\n *\n * @param  {String} data    Input string\n * @param  {Number} version QR Code version\n * @return {Array}          Array of segments\n */\nexports.fromString = function fromString (data, version) {\n  const segs = getSegmentsFromString(data, Utils.isKanjiModeEnabled())\n\n  const nodes = buildNodes(segs)\n  const graph = buildGraph(nodes, version)\n  const path = dijkstra.find_path(graph.map, 'start', 'end')\n\n  const optimizedSegs = []\n  for (let i = 1; i < path.length - 1; i++) {\n    optimizedSegs.push(graph.table[path[i]].node)\n  }\n\n  return exports.fromArray(mergeSegments(optimizedSegs))\n}\n\n/**\n * Splits a string in various segments with the modes which\n * best represent their content.\n * The produced segments are far from being optimized.\n * The output of this function is only used to estimate a QR Code version\n * which may contain the data.\n *\n * @param  {string} data Input string\n * @return {Array}       Array of segments\n */\nexports.rawSplit = function rawSplit (data) {\n  return exports.fromArray(\n    getSegmentsFromString(data, Utils.isKanjiModeEnabled())\n  )\n}\n", "const Utils = require('./utils')\nconst ECLevel = require('./error-correction-level')\nconst BitBuffer = require('./bit-buffer')\nconst BitMatrix = require('./bit-matrix')\nconst AlignmentPattern = require('./alignment-pattern')\nconst FinderPattern = require('./finder-pattern')\nconst MaskPattern = require('./mask-pattern')\nconst ECCode = require('./error-correction-code')\nconst ReedSolomonEncoder = require('./reed-solomon-encoder')\nconst Version = require('./version')\nconst FormatInfo = require('./format-info')\nconst Mode = require('./mode')\nconst Segments = require('./segments')\n\n/**\n * QRCode for JavaScript\n *\n * modified by <PERSON> for nodejs support\n * Copyright (c) 2011 Ryan Day\n *\n * Licensed under the MIT license:\n *   http://www.opensource.org/licenses/mit-license.php\n *\n//---------------------------------------------------------------------\n// QRCode for JavaScript\n//\n// Copyright (c) 2009 <PERSON><PERSON><PERSON>\n//\n// URL: http://www.d-project.com/\n//\n// Licensed under the MIT license:\n//   http://www.opensource.org/licenses/mit-license.php\n//\n// The word \"QR Code\" is registered trademark of\n// DENSO WAVE INCORPORATED\n//   http://www.denso-wave.com/qrcode/faqpatent-e.html\n//\n//---------------------------------------------------------------------\n*/\n\n/**\n * Add finder patterns bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupFinderPattern (matrix, version) {\n  const size = matrix.size\n  const pos = FinderPattern.getPositions(version)\n\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0]\n    const col = pos[i][1]\n\n    for (let r = -1; r <= 7; r++) {\n      if (row + r <= -1 || size <= row + r) continue\n\n      for (let c = -1; c <= 7; c++) {\n        if (col + c <= -1 || size <= col + c) continue\n\n        if ((r >= 0 && r <= 6 && (c === 0 || c === 6)) ||\n          (c >= 0 && c <= 6 && (r === 0 || r === 6)) ||\n          (r >= 2 && r <= 4 && c >= 2 && c <= 4)) {\n          matrix.set(row + r, col + c, true, true)\n        } else {\n          matrix.set(row + r, col + c, false, true)\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add timing pattern bits to matrix\n *\n * Note: this function must be called before {@link setupAlignmentPattern}\n *\n * @param  {BitMatrix} matrix Modules matrix\n */\nfunction setupTimingPattern (matrix) {\n  const size = matrix.size\n\n  for (let r = 8; r < size - 8; r++) {\n    const value = r % 2 === 0\n    matrix.set(r, 6, value, true)\n    matrix.set(6, r, value, true)\n  }\n}\n\n/**\n * Add alignment patterns bits to matrix\n *\n * Note: this function must be called after {@link setupTimingPattern}\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupAlignmentPattern (matrix, version) {\n  const pos = AlignmentPattern.getPositions(version)\n\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0]\n    const col = pos[i][1]\n\n    for (let r = -2; r <= 2; r++) {\n      for (let c = -2; c <= 2; c++) {\n        if (r === -2 || r === 2 || c === -2 || c === 2 ||\n          (r === 0 && c === 0)) {\n          matrix.set(row + r, col + c, true, true)\n        } else {\n          matrix.set(row + r, col + c, false, true)\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add version info bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupVersionInfo (matrix, version) {\n  const size = matrix.size\n  const bits = Version.getEncodedBits(version)\n  let row, col, mod\n\n  for (let i = 0; i < 18; i++) {\n    row = Math.floor(i / 3)\n    col = i % 3 + size - 8 - 3\n    mod = ((bits >> i) & 1) === 1\n\n    matrix.set(row, col, mod, true)\n    matrix.set(col, row, mod, true)\n  }\n}\n\n/**\n * Add format info bits to matrix\n *\n * @param  {BitMatrix} matrix               Modules matrix\n * @param  {ErrorCorrectionLevel}    errorCorrectionLevel Error correction level\n * @param  {Number}    maskPattern          Mask pattern reference value\n */\nfunction setupFormatInfo (matrix, errorCorrectionLevel, maskPattern) {\n  const size = matrix.size\n  const bits = FormatInfo.getEncodedBits(errorCorrectionLevel, maskPattern)\n  let i, mod\n\n  for (i = 0; i < 15; i++) {\n    mod = ((bits >> i) & 1) === 1\n\n    // vertical\n    if (i < 6) {\n      matrix.set(i, 8, mod, true)\n    } else if (i < 8) {\n      matrix.set(i + 1, 8, mod, true)\n    } else {\n      matrix.set(size - 15 + i, 8, mod, true)\n    }\n\n    // horizontal\n    if (i < 8) {\n      matrix.set(8, size - i - 1, mod, true)\n    } else if (i < 9) {\n      matrix.set(8, 15 - i - 1 + 1, mod, true)\n    } else {\n      matrix.set(8, 15 - i - 1, mod, true)\n    }\n  }\n\n  // fixed module\n  matrix.set(size - 8, 8, 1, true)\n}\n\n/**\n * Add encoded data bits to matrix\n *\n * @param  {BitMatrix}  matrix Modules matrix\n * @param  {Uint8Array} data   Data codewords\n */\nfunction setupData (matrix, data) {\n  const size = matrix.size\n  let inc = -1\n  let row = size - 1\n  let bitIndex = 7\n  let byteIndex = 0\n\n  for (let col = size - 1; col > 0; col -= 2) {\n    if (col === 6) col--\n\n    while (true) {\n      for (let c = 0; c < 2; c++) {\n        if (!matrix.isReserved(row, col - c)) {\n          let dark = false\n\n          if (byteIndex < data.length) {\n            dark = (((data[byteIndex] >>> bitIndex) & 1) === 1)\n          }\n\n          matrix.set(row, col - c, dark)\n          bitIndex--\n\n          if (bitIndex === -1) {\n            byteIndex++\n            bitIndex = 7\n          }\n        }\n      }\n\n      row += inc\n\n      if (row < 0 || size <= row) {\n        row -= inc\n        inc = -inc\n        break\n      }\n    }\n  }\n}\n\n/**\n * Create encoded codewords from data input\n *\n * @param  {Number}   version              QR Code version\n * @param  {ErrorCorrectionLevel}   errorCorrectionLevel Error correction level\n * @param  {ByteData} data                 Data input\n * @return {Uint8Array}                    Buffer containing encoded codewords\n */\nfunction createData (version, errorCorrectionLevel, segments) {\n  // Prepare data buffer\n  const buffer = new BitBuffer()\n\n  segments.forEach(function (data) {\n    // prefix data with mode indicator (4 bits)\n    buffer.put(data.mode.bit, 4)\n\n    // Prefix data with character count indicator.\n    // The character count indicator is a string of bits that represents the\n    // number of characters that are being encoded.\n    // The character count indicator must be placed after the mode indicator\n    // and must be a certain number of bits long, depending on the QR version\n    // and data mode\n    // @see {@link Mode.getCharCountIndicator}.\n    buffer.put(data.getLength(), Mode.getCharCountIndicator(data.mode, version))\n\n    // add binary data sequence to buffer\n    data.write(buffer)\n  })\n\n  // Calculate required number of bits\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8\n\n  // Add a terminator.\n  // If the bit string is shorter than the total number of required bits,\n  // a terminator of up to four 0s must be added to the right side of the string.\n  // If the bit string is more than four bits shorter than the required number of bits,\n  // add four 0s to the end.\n  if (buffer.getLengthInBits() + 4 <= dataTotalCodewordsBits) {\n    buffer.put(0, 4)\n  }\n\n  // If the bit string is fewer than four bits shorter, add only the number of 0s that\n  // are needed to reach the required number of bits.\n\n  // After adding the terminator, if the number of bits in the string is not a multiple of 8,\n  // pad the string on the right with 0s to make the string's length a multiple of 8.\n  while (buffer.getLengthInBits() % 8 !== 0) {\n    buffer.putBit(0)\n  }\n\n  // Add pad bytes if the string is still shorter than the total number of required bits.\n  // Extend the buffer to fill the data capacity of the symbol corresponding to\n  // the Version and Error Correction Level by adding the Pad Codewords 11101100 (0xEC)\n  // and 00010001 (0x11) alternately.\n  const remainingByte = (dataTotalCodewordsBits - buffer.getLengthInBits()) / 8\n  for (let i = 0; i < remainingByte; i++) {\n    buffer.put(i % 2 ? 0x11 : 0xEC, 8)\n  }\n\n  return createCodewords(buffer, version, errorCorrectionLevel)\n}\n\n/**\n * Encode input data with Reed-Solomon and return codewords with\n * relative error correction bits\n *\n * @param  {BitBuffer} bitBuffer            Data to encode\n * @param  {Number}    version              QR Code version\n * @param  {ErrorCorrectionLevel} errorCorrectionLevel Error correction level\n * @return {Uint8Array}                     Buffer containing encoded codewords\n */\nfunction createCodewords (bitBuffer, version, errorCorrectionLevel) {\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n\n  // Total number of data codewords\n  const dataTotalCodewords = totalCodewords - ecTotalCodewords\n\n  // Total number of blocks\n  const ecTotalBlocks = ECCode.getBlocksCount(version, errorCorrectionLevel)\n\n  // Calculate how many blocks each group should contain\n  const blocksInGroup2 = totalCodewords % ecTotalBlocks\n  const blocksInGroup1 = ecTotalBlocks - blocksInGroup2\n\n  const totalCodewordsInGroup1 = Math.floor(totalCodewords / ecTotalBlocks)\n\n  const dataCodewordsInGroup1 = Math.floor(dataTotalCodewords / ecTotalBlocks)\n  const dataCodewordsInGroup2 = dataCodewordsInGroup1 + 1\n\n  // Number of EC codewords is the same for both groups\n  const ecCount = totalCodewordsInGroup1 - dataCodewordsInGroup1\n\n  // Initialize a Reed-Solomon encoder with a generator polynomial of degree ecCount\n  const rs = new ReedSolomonEncoder(ecCount)\n\n  let offset = 0\n  const dcData = new Array(ecTotalBlocks)\n  const ecData = new Array(ecTotalBlocks)\n  let maxDataSize = 0\n  const buffer = new Uint8Array(bitBuffer.buffer)\n\n  // Divide the buffer into the required number of blocks\n  for (let b = 0; b < ecTotalBlocks; b++) {\n    const dataSize = b < blocksInGroup1 ? dataCodewordsInGroup1 : dataCodewordsInGroup2\n\n    // extract a block of data from buffer\n    dcData[b] = buffer.slice(offset, offset + dataSize)\n\n    // Calculate EC codewords for this data block\n    ecData[b] = rs.encode(dcData[b])\n\n    offset += dataSize\n    maxDataSize = Math.max(maxDataSize, dataSize)\n  }\n\n  // Create final data\n  // Interleave the data and error correction codewords from each block\n  const data = new Uint8Array(totalCodewords)\n  let index = 0\n  let i, r\n\n  // Add data codewords\n  for (i = 0; i < maxDataSize; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      if (i < dcData[r].length) {\n        data[index++] = dcData[r][i]\n      }\n    }\n  }\n\n  // Apped EC codewords\n  for (i = 0; i < ecCount; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      data[index++] = ecData[r][i]\n    }\n  }\n\n  return data\n}\n\n/**\n * Build QR Code symbol\n *\n * @param  {String} data                 Input string\n * @param  {Number} version              QR Code version\n * @param  {ErrorCorretionLevel} errorCorrectionLevel Error level\n * @param  {MaskPattern} maskPattern     Mask pattern\n * @return {Object}                      Object containing symbol data\n */\nfunction createSymbol (data, version, errorCorrectionLevel, maskPattern) {\n  let segments\n\n  if (Array.isArray(data)) {\n    segments = Segments.fromArray(data)\n  } else if (typeof data === 'string') {\n    let estimatedVersion = version\n\n    if (!estimatedVersion) {\n      const rawSegments = Segments.rawSplit(data)\n\n      // Estimate best version that can contain raw splitted segments\n      estimatedVersion = Version.getBestVersionForData(rawSegments, errorCorrectionLevel)\n    }\n\n    // Build optimized segments\n    // If estimated version is undefined, try with the highest version\n    segments = Segments.fromString(data, estimatedVersion || 40)\n  } else {\n    throw new Error('Invalid data')\n  }\n\n  // Get the min version that can contain data\n  const bestVersion = Version.getBestVersionForData(segments, errorCorrectionLevel)\n\n  // If no version is found, data cannot be stored\n  if (!bestVersion) {\n    throw new Error('The amount of data is too big to be stored in a QR Code')\n  }\n\n  // If not specified, use min version as default\n  if (!version) {\n    version = bestVersion\n\n  // Check if the specified version can contain the data\n  } else if (version < bestVersion) {\n    throw new Error('\\n' +\n      'The chosen QR Code version cannot contain this amount of data.\\n' +\n      'Minimum version required to store current data is: ' + bestVersion + '.\\n'\n    )\n  }\n\n  const dataBits = createData(version, errorCorrectionLevel, segments)\n\n  // Allocate matrix buffer\n  const moduleCount = Utils.getSymbolSize(version)\n  const modules = new BitMatrix(moduleCount)\n\n  // Add function modules\n  setupFinderPattern(modules, version)\n  setupTimingPattern(modules)\n  setupAlignmentPattern(modules, version)\n\n  // Add temporary dummy bits for format info just to set them as reserved.\n  // This is needed to prevent these bits from being masked by {@link MaskPattern.applyMask}\n  // since the masking operation must be performed only on the encoding region.\n  // These blocks will be replaced with correct values later in code.\n  setupFormatInfo(modules, errorCorrectionLevel, 0)\n\n  if (version >= 7) {\n    setupVersionInfo(modules, version)\n  }\n\n  // Add data codewords\n  setupData(modules, dataBits)\n\n  if (isNaN(maskPattern)) {\n    // Find best mask pattern\n    maskPattern = MaskPattern.getBestMask(modules,\n      setupFormatInfo.bind(null, modules, errorCorrectionLevel))\n  }\n\n  // Apply mask pattern\n  MaskPattern.applyMask(maskPattern, modules)\n\n  // Replace format info bits with correct values\n  setupFormatInfo(modules, errorCorrectionLevel, maskPattern)\n\n  return {\n    modules: modules,\n    version: version,\n    errorCorrectionLevel: errorCorrectionLevel,\n    maskPattern: maskPattern,\n    segments: segments\n  }\n}\n\n/**\n * QR Code\n *\n * @param {String | Array} data                 Input data\n * @param {Object} options                      Optional configurations\n * @param {Number} options.version              QR Code version\n * @param {String} options.errorCorrectionLevel Error correction level\n * @param {Function} options.toSJISFunc         Helper func to convert utf8 to sjis\n */\nexports.create = function create (data, options) {\n  if (typeof data === 'undefined' || data === '') {\n    throw new Error('No input text')\n  }\n\n  let errorCorrectionLevel = ECLevel.M\n  let version\n  let mask\n\n  if (typeof options !== 'undefined') {\n    // Use higher error correction level as default\n    errorCorrectionLevel = ECLevel.from(options.errorCorrectionLevel, ECLevel.M)\n    version = Version.from(options.version)\n    mask = MaskPattern.from(options.maskPattern)\n\n    if (options.toSJISFunc) {\n      Utils.setToSJISFunction(options.toSJISFunc)\n    }\n  }\n\n  return createSymbol(data, version, errorCorrectionLevel, mask)\n}\n", "function hex2rgba (hex) {\n  if (typeof hex === 'number') {\n    hex = hex.toString()\n  }\n\n  if (typeof hex !== 'string') {\n    throw new Error('Color should be defined as hex string')\n  }\n\n  let hexCode = hex.slice().replace('#', '').split('')\n  if (hexCode.length < 3 || hexCode.length === 5 || hexCode.length > 8) {\n    throw new Error('Invalid hex color: ' + hex)\n  }\n\n  // Convert from short to long form (fff -> ffffff)\n  if (hexCode.length === 3 || hexCode.length === 4) {\n    hexCode = Array.prototype.concat.apply([], hexCode.map(function (c) {\n      return [c, c]\n    }))\n  }\n\n  // Add default alpha value\n  if (hexCode.length === 6) hexCode.push('F', 'F')\n\n  const hexValue = parseInt(hexCode.join(''), 16)\n\n  return {\n    r: (hexValue >> 24) & 255,\n    g: (hexValue >> 16) & 255,\n    b: (hexValue >> 8) & 255,\n    a: hexValue & 255,\n    hex: '#' + hexCode.slice(0, 6).join('')\n  }\n}\n\nexports.getOptions = function getOptions (options) {\n  if (!options) options = {}\n  if (!options.color) options.color = {}\n\n  const margin = typeof options.margin === 'undefined' ||\n    options.margin === null ||\n    options.margin < 0\n    ? 4\n    : options.margin\n\n  const width = options.width && options.width >= 21 ? options.width : undefined\n  const scale = options.scale || 4\n\n  return {\n    width: width,\n    scale: width ? 4 : scale,\n    margin: margin,\n    color: {\n      dark: hex2rgba(options.color.dark || '#000000ff'),\n      light: hex2rgba(options.color.light || '#ffffffff')\n    },\n    type: options.type,\n    rendererOpts: options.rendererOpts || {}\n  }\n}\n\nexports.getScale = function getScale (qrSize, opts) {\n  return opts.width && opts.width >= qrSize + opts.margin * 2\n    ? opts.width / (qrSize + opts.margin * 2)\n    : opts.scale\n}\n\nexports.getImageWidth = function getImageWidth (qrSize, opts) {\n  const scale = exports.getScale(qrSize, opts)\n  return Math.floor((qrSize + opts.margin * 2) * scale)\n}\n\nexports.qrToImageData = function qrToImageData (imgData, qr, opts) {\n  const size = qr.modules.size\n  const data = qr.modules.data\n  const scale = exports.getScale(size, opts)\n  const symbolSize = Math.floor((size + opts.margin * 2) * scale)\n  const scaledMargin = opts.margin * scale\n  const palette = [opts.color.light, opts.color.dark]\n\n  for (let i = 0; i < symbolSize; i++) {\n    for (let j = 0; j < symbolSize; j++) {\n      let posDst = (i * symbolSize + j) * 4\n      let pxColor = opts.color.light\n\n      if (i >= scaledMargin && j >= scaledMargin &&\n        i < symbolSize - scaledMargin && j < symbolSize - scaledMargin) {\n        const iSrc = Math.floor((i - scaledMargin) / scale)\n        const jSrc = Math.floor((j - scaledMargin) / scale)\n        pxColor = palette[data[iSrc * size + jSrc] ? 1 : 0]\n      }\n\n      imgData[posDst++] = pxColor.r\n      imgData[posDst++] = pxColor.g\n      imgData[posDst++] = pxColor.b\n      imgData[posDst] = pxColor.a\n    }\n  }\n}\n", "const Utils = require('./utils')\n\nfunction clearCanvas (ctx, canvas, size) {\n  ctx.clearRect(0, 0, canvas.width, canvas.height)\n\n  if (!canvas.style) canvas.style = {}\n  canvas.height = size\n  canvas.width = size\n  canvas.style.height = size + 'px'\n  canvas.style.width = size + 'px'\n}\n\nfunction getCanvasElement () {\n  try {\n    return document.createElement('canvas')\n  } catch (e) {\n    throw new Error('You need to specify a canvas element')\n  }\n}\n\nexports.render = function render (qrData, canvas, options) {\n  let opts = options\n  let canvasEl = canvas\n\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas\n    canvas = undefined\n  }\n\n  if (!canvas) {\n    canvasEl = getCanvasElement()\n  }\n\n  opts = Utils.getOptions(opts)\n  const size = Utils.getImageWidth(qrData.modules.size, opts)\n\n  const ctx = canvasEl.getContext('2d')\n  const image = ctx.createImageData(size, size)\n  Utils.qrToImageData(image.data, qrData, opts)\n\n  clearCanvas(ctx, canvasEl, size)\n  ctx.putImageData(image, 0, 0)\n\n  return canvasEl\n}\n\nexports.renderToDataURL = function renderToDataURL (qrData, canvas, options) {\n  let opts = options\n\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas\n    canvas = undefined\n  }\n\n  if (!opts) opts = {}\n\n  const canvasEl = exports.render(qrData, canvas, opts)\n\n  const type = opts.type || 'image/png'\n  const rendererOpts = opts.rendererOpts || {}\n\n  return canvasEl.toDataURL(type, rendererOpts.quality)\n}\n", "const Utils = require('./utils')\n\nfunction getColorAttrib (color, attrib) {\n  const alpha = color.a / 255\n  const str = attrib + '=\"' + color.hex + '\"'\n\n  return alpha < 1\n    ? str + ' ' + attrib + '-opacity=\"' + alpha.toFixed(2).slice(1) + '\"'\n    : str\n}\n\nfunction svgCmd (cmd, x, y) {\n  let str = cmd + x\n  if (typeof y !== 'undefined') str += ' ' + y\n\n  return str\n}\n\nfunction qrToPath (data, size, margin) {\n  let path = ''\n  let moveBy = 0\n  let newRow = false\n  let lineLength = 0\n\n  for (let i = 0; i < data.length; i++) {\n    const col = Math.floor(i % size)\n    const row = Math.floor(i / size)\n\n    if (!col && !newRow) newRow = true\n\n    if (data[i]) {\n      lineLength++\n\n      if (!(i > 0 && col > 0 && data[i - 1])) {\n        path += newRow\n          ? svgCmd('M', col + margin, 0.5 + row + margin)\n          : svgCmd('m', moveBy, 0)\n\n        moveBy = 0\n        newRow = false\n      }\n\n      if (!(col + 1 < size && data[i + 1])) {\n        path += svgCmd('h', lineLength)\n        lineLength = 0\n      }\n    } else {\n      moveBy++\n    }\n  }\n\n  return path\n}\n\nexports.render = function render (qrData, options, cb) {\n  const opts = Utils.getOptions(options)\n  const size = qrData.modules.size\n  const data = qrData.modules.data\n  const qrcodesize = size + opts.margin * 2\n\n  const bg = !opts.color.light.a\n    ? ''\n    : '<path ' + getColorAttrib(opts.color.light, 'fill') +\n      ' d=\"M0 0h' + qrcodesize + 'v' + qrcodesize + 'H0z\"/>'\n\n  const path =\n    '<path ' + getColorAttrib(opts.color.dark, 'stroke') +\n    ' d=\"' + qrToPath(data, size, opts.margin) + '\"/>'\n\n  const viewBox = 'viewBox=\"' + '0 0 ' + qrcodesize + ' ' + qrcodesize + '\"'\n\n  const width = !opts.width ? '' : 'width=\"' + opts.width + '\" height=\"' + opts.width + '\" '\n\n  const svgTag = '<svg xmlns=\"http://www.w3.org/2000/svg\" ' + width + viewBox + ' shape-rendering=\"crispEdges\">' + bg + path + '</svg>\\n'\n\n  if (typeof cb === 'function') {\n    cb(null, svgTag)\n  }\n\n  return svgTag\n}\n", "\nconst canPromise = require('./can-promise')\n\nconst QRCode = require('./core/qrcode')\nconst CanvasRenderer = require('./renderer/canvas')\nconst SvgRenderer = require('./renderer/svg-tag.js')\n\nfunction renderCanvas (renderFunc, canvas, text, opts, cb) {\n  const args = [].slice.call(arguments, 1)\n  const argsNum = args.length\n  const isLastArgCb = typeof args[argsNum - 1] === 'function'\n\n  if (!isLastArgCb && !canPromise()) {\n    throw new Error('Callback required as last argument')\n  }\n\n  if (isLastArgCb) {\n    if (argsNum < 2) {\n      throw new Error('Too few arguments provided')\n    }\n\n    if (argsNum === 2) {\n      cb = text\n      text = canvas\n      canvas = opts = undefined\n    } else if (argsNum === 3) {\n      if (canvas.getContext && typeof cb === 'undefined') {\n        cb = opts\n        opts = undefined\n      } else {\n        cb = opts\n        opts = text\n        text = canvas\n        canvas = undefined\n      }\n    }\n  } else {\n    if (argsNum < 1) {\n      throw new Error('Too few arguments provided')\n    }\n\n    if (argsNum === 1) {\n      text = canvas\n      canvas = opts = undefined\n    } else if (argsNum === 2 && !canvas.getContext) {\n      opts = text\n      text = canvas\n      canvas = undefined\n    }\n\n    return new Promise(function (resolve, reject) {\n      try {\n        const data = QRCode.create(text, opts)\n        resolve(renderFunc(data, canvas, opts))\n      } catch (e) {\n        reject(e)\n      }\n    })\n  }\n\n  try {\n    const data = QRCode.create(text, opts)\n    cb(null, renderFunc(data, canvas, opts))\n  } catch (e) {\n    cb(e)\n  }\n}\n\nexports.create = QRCode.create\nexports.toCanvas = renderCanvas.bind(null, CanvasRenderer.render)\nexports.toDataURL = renderCanvas.bind(null, CanvasRenderer.renderToDataURL)\n\n// only svg for now.\nexports.toString = renderCanvas.bind(null, function (data, _, opts) {\n  return SvgRenderer.render(data, opts)\n})\n", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "/**\n * @license\n * Copyright 2020 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nimport {nothing, ElementPart} from '../lit-html.js';\nimport {directive, AsyncDirective} from '../async-directive.js';\n\n/**\n * Creates a new Ref object, which is container for a reference to an element.\n */\nexport const createRef = <T = Element>() => new Ref<T>();\n\n/**\n * An object that holds a ref value.\n */\nclass Ref<T = Element> {\n  /**\n   * The current Element value of the ref, or else `undefined` if the ref is no\n   * longer rendered.\n   */\n  readonly value?: T;\n}\n\nexport type {Ref};\n\ninterface RefInternal {\n  value: Element | undefined;\n}\n\n// When callbacks are used for refs, this map tracks the last value the callback\n// was called with, for ensuring a directive doesn't clear the ref if the ref\n// has already been rendered to a new spot. It is double-keyed on both the\n// context (`options.host`) and the callback, since we auto-bind class methods\n// to `options.host`.\nconst lastElementForContextAndCallback = new WeakMap<\n  object,\n  WeakMap<Function, Element | undefined>\n>();\n\nexport type RefOrCallback<T = Element> = Ref<T> | ((el: T | undefined) => void);\n\nclass RefDirective extends AsyncDirective {\n  private _element?: Element;\n  private _ref?: RefOrCallback;\n  private _context?: object;\n\n  render(_ref?: RefOrCallback) {\n    return nothing;\n  }\n\n  override update(part: ElementPart, [ref]: Parameters<this['render']>) {\n    const refChanged = ref !== this._ref;\n    if (refChanged && this._ref !== undefined) {\n      // The ref passed to the directive has changed;\n      // unset the previous ref's value\n      this._updateRefValue(undefined);\n    }\n    if (refChanged || this._lastElementForRef !== this._element) {\n      // We either got a new ref or this is the first render;\n      // store the ref/element & update the ref value\n      this._ref = ref;\n      this._context = part.options?.host;\n      this._updateRefValue((this._element = part.element));\n    }\n    return nothing;\n  }\n\n  private _updateRefValue(element: Element | undefined) {\n    if (!this.isConnected) {\n      element = undefined;\n    }\n    if (typeof this._ref === 'function') {\n      // If the current ref was called with a previous value, call with\n      // `undefined`; We do this to ensure callbacks are called in a consistent\n      // way regardless of whether a ref might be moving up in the tree (in\n      // which case it would otherwise be called with the new value before the\n      // previous one unsets it) and down in the tree (where it would be unset\n      // before being set). Note that element lookup is keyed by\n      // both the context and the callback, since we allow passing unbound\n      // functions that are called on options.host, and we want to treat\n      // these as unique \"instances\" of a function.\n      const context = this._context ?? globalThis;\n      let lastElementForCallback =\n        lastElementForContextAndCallback.get(context);\n      if (lastElementForCallback === undefined) {\n        lastElementForCallback = new WeakMap();\n        lastElementForContextAndCallback.set(context, lastElementForCallback);\n      }\n      if (lastElementForCallback.get(this._ref) !== undefined) {\n        this._ref.call(this._context, undefined);\n      }\n      lastElementForCallback.set(this._ref, element);\n      // Call the ref with the new element value\n      if (element !== undefined) {\n        this._ref.call(this._context, element);\n      }\n    } else {\n      (this._ref as RefInternal)!.value = element;\n    }\n  }\n\n  private get _lastElementForRef() {\n    return typeof this._ref === 'function'\n      ? lastElementForContextAndCallback\n          .get(this._context ?? globalThis)\n          ?.get(this._ref)\n      : this._ref?.value;\n  }\n\n  override disconnected() {\n    // Only clear the box if our element is still the one in it (i.e. another\n    // directive instance hasn't rendered its element to it before us); that\n    // only happens in the event of the directive being cleared (not via manual\n    // disconnection)\n    if (this._lastElementForRef === this._element) {\n      this._updateRefValue(undefined);\n    }\n  }\n\n  override reconnected() {\n    // If we were manually disconnected, we can safely put our element back in\n    // the box, since no rendering could have occurred to change its state\n    this._updateRefValue(this._element);\n  }\n}\n\n/**\n * Sets the value of a Ref object or calls a ref callback with the element it's\n * bound to.\n *\n * A Ref object acts as a container for a reference to an element. A ref\n * callback is a function that takes an element as its only argument.\n *\n * The ref directive sets the value of the Ref object or calls the ref callback\n * during rendering, if the referenced element changed.\n *\n * Note: If a ref callback is rendered to a different element position or is\n * removed in a subsequent render, it will first be called with `undefined`,\n * followed by another call with the new element it was rendered to (if any).\n *\n * ```js\n * // Using Ref object\n * const inputRef = createRef();\n * render(html`<input ${ref(inputRef)}>`, container);\n * inputRef.value.focus();\n *\n * // Using callback\n * const callback = (inputElement) => inputElement.focus();\n * render(html`<input ${ref(callback)}>`, container);\n * ```\n */\nexport const ref = directive(RefDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {RefDirective};\n", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAIA,WAAO,UAAU,WAAY;AAC3B,aAAO,OAAO,YAAY,cAAc,QAAQ,aAAa,QAAQ,UAAU;AAAA,IACjF;AAAA;AAAA;;;ACNA;AAAA;AAAA,QAAI;AACJ,QAAM,kBAAkB;AAAA,MACtB;AAAA;AAAA,MACA;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC1C;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC7C;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MACtD;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,IACxD;AAQA,YAAQ,gBAAgB,SAAS,cAAe,SAAS;AACvD,UAAI,CAAC,QAAS,OAAM,IAAI,MAAM,uCAAuC;AACrE,UAAI,UAAU,KAAK,UAAU,GAAI,OAAM,IAAI,MAAM,2CAA2C;AAC5F,aAAO,UAAU,IAAI;AAAA,IACvB;AAQA,YAAQ,0BAA0B,SAAS,wBAAyB,SAAS;AAC3E,aAAO,gBAAgB,OAAO;AAAA,IAChC;AAQA,YAAQ,cAAc,SAAU,MAAM;AACpC,UAAI,QAAQ;AAEZ,aAAO,SAAS,GAAG;AACjB;AACA,kBAAU;AAAA,MACZ;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,oBAAoB,SAAS,kBAAmB,GAAG;AACzD,UAAI,OAAO,MAAM,YAAY;AAC3B,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AAEA,uBAAiB;AAAA,IACnB;AAEA,YAAQ,qBAAqB,WAAY;AACvC,aAAO,OAAO,mBAAmB;AAAA,IACnC;AAEA,YAAQ,SAAS,SAAS,OAAQ,OAAO;AACvC,aAAO,eAAe,KAAK;AAAA,IAC7B;AAAA;AAAA;;;AC9DA;AAAA;AAAA,YAAQ,IAAI,EAAE,KAAK,EAAE;AACrB,YAAQ,IAAI,EAAE,KAAK,EAAE;AACrB,YAAQ,IAAI,EAAE,KAAK,EAAE;AACrB,YAAQ,IAAI,EAAE,KAAK,EAAE;AAErB,aAAS,WAAY,QAAQ;AAC3B,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACzC;AAEA,YAAM,QAAQ,OAAO,YAAY;AAEjC,cAAQ,OAAO;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QAEjB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QAEjB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QAEjB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QAEjB;AACE,gBAAM,IAAI,MAAM,uBAAuB,MAAM;AAAA,MACjD;AAAA,IACF;AAEA,YAAQ,UAAU,SAAS,QAAS,OAAO;AACzC,aAAO,SAAS,OAAO,MAAM,QAAQ,eACnC,MAAM,OAAO,KAAK,MAAM,MAAM;AAAA,IAClC;AAEA,YAAQ,OAAO,SAAS,KAAM,OAAO,cAAc;AACjD,UAAI,QAAQ,QAAQ,KAAK,GAAG;AAC1B,eAAO;AAAA,MACT;AAEA,UAAI;AACF,eAAO,WAAW,KAAK;AAAA,MACzB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;ACjDA;AAAA;AAAA,aAAS,YAAa;AACpB,WAAK,SAAS,CAAC;AACf,WAAK,SAAS;AAAA,IAChB;AAEA,cAAU,YAAY;AAAA,MAEpB,KAAK,SAAU,OAAO;AACpB,cAAM,WAAW,KAAK,MAAM,QAAQ,CAAC;AACrC,gBAAS,KAAK,OAAO,QAAQ,MAAO,IAAI,QAAQ,IAAM,OAAO;AAAA,MAC/D;AAAA,MAEA,KAAK,SAAU,KAAK,QAAQ;AAC1B,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,eAAK,QAAS,QAAS,SAAS,IAAI,IAAM,OAAO,CAAC;AAAA,QACpD;AAAA,MACF;AAAA,MAEA,iBAAiB,WAAY;AAC3B,eAAO,KAAK;AAAA,MACd;AAAA,MAEA,QAAQ,SAAU,KAAK;AACrB,cAAM,WAAW,KAAK,MAAM,KAAK,SAAS,CAAC;AAC3C,YAAI,KAAK,OAAO,UAAU,UAAU;AAClC,eAAK,OAAO,KAAK,CAAC;AAAA,QACpB;AAEA,YAAI,KAAK;AACP,eAAK,OAAO,QAAQ,KAAM,QAAU,KAAK,SAAS;AAAA,QACpD;AAEA,aAAK;AAAA,MACP;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpCjB;AAAA;AAKA,aAAS,UAAW,MAAM;AACxB,UAAI,CAAC,QAAQ,OAAO,GAAG;AACrB,cAAM,IAAI,MAAM,mDAAmD;AAAA,MACrE;AAEA,WAAK,OAAO;AACZ,WAAK,OAAO,IAAI,WAAW,OAAO,IAAI;AACtC,WAAK,cAAc,IAAI,WAAW,OAAO,IAAI;AAAA,IAC/C;AAWA,cAAU,UAAU,MAAM,SAAU,KAAK,KAAK,OAAO,UAAU;AAC7D,YAAM,QAAQ,MAAM,KAAK,OAAO;AAChC,WAAK,KAAK,KAAK,IAAI;AACnB,UAAI,SAAU,MAAK,YAAY,KAAK,IAAI;AAAA,IAC1C;AASA,cAAU,UAAU,MAAM,SAAU,KAAK,KAAK;AAC5C,aAAO,KAAK,KAAK,MAAM,KAAK,OAAO,GAAG;AAAA,IACxC;AAUA,cAAU,UAAU,MAAM,SAAU,KAAK,KAAK,OAAO;AACnD,WAAK,KAAK,MAAM,KAAK,OAAO,GAAG,KAAK;AAAA,IACtC;AASA,cAAU,UAAU,aAAa,SAAU,KAAK,KAAK;AACnD,aAAO,KAAK,YAAY,MAAM,KAAK,OAAO,GAAG;AAAA,IAC/C;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChEjB;AAAA;AAUA,QAAM,gBAAgB,gBAAmB;AAgBzC,YAAQ,kBAAkB,SAAS,gBAAiB,SAAS;AAC3D,UAAI,YAAY,EAAG,QAAO,CAAC;AAE3B,YAAM,WAAW,KAAK,MAAM,UAAU,CAAC,IAAI;AAC3C,YAAM,OAAO,cAAc,OAAO;AAClC,YAAM,YAAY,SAAS,MAAM,KAAK,KAAK,MAAM,OAAO,OAAO,IAAI,WAAW,EAAE,IAAI;AACpF,YAAM,YAAY,CAAC,OAAO,CAAC;AAE3B,eAAS,IAAI,GAAG,IAAI,WAAW,GAAG,KAAK;AACrC,kBAAU,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI;AAAA,MACpC;AAEA,gBAAU,KAAK,CAAC;AAEhB,aAAO,UAAU,QAAQ;AAAA,IAC3B;AAsBA,YAAQ,eAAe,SAAS,aAAc,SAAS;AACrD,YAAM,SAAS,CAAC;AAChB,YAAM,MAAM,QAAQ,gBAAgB,OAAO;AAC3C,YAAM,YAAY,IAAI;AAEtB,eAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAElC,cAAK,MAAM,KAAK,MAAM;AAAA,UACjB,MAAM,KAAK,MAAM,YAAY;AAAA,UAC7B,MAAM,YAAY,KAAK,MAAM,GAAI;AACpC;AAAA,UACF;AAEA,iBAAO,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAAA,QAC9B;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AClFA;AAAA;AAAA,QAAM,gBAAgB,gBAAmB;AACzC,QAAM,sBAAsB;AAS5B,YAAQ,eAAe,SAAS,aAAc,SAAS;AACrD,YAAM,OAAO,cAAc,OAAO;AAElC,aAAO;AAAA;AAAA,QAEL,CAAC,GAAG,CAAC;AAAA;AAAA,QAEL,CAAC,OAAO,qBAAqB,CAAC;AAAA;AAAA,QAE9B,CAAC,GAAG,OAAO,mBAAmB;AAAA,MAChC;AAAA,IACF;AAAA;AAAA;;;ACrBA;AAAA;AAIA,YAAQ,WAAW;AAAA,MACjB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,IACd;AAMA,QAAM,gBAAgB;AAAA,MACpB,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN;AAQA,YAAQ,UAAU,SAAS,QAAS,MAAM;AACxC,aAAO,QAAQ,QAAQ,SAAS,MAAM,CAAC,MAAM,IAAI,KAAK,QAAQ,KAAK,QAAQ;AAAA,IAC7E;AASA,YAAQ,OAAO,SAAS,KAAM,OAAO;AACnC,aAAO,QAAQ,QAAQ,KAAK,IAAI,SAAS,OAAO,EAAE,IAAI;AAAA,IACxD;AASA,YAAQ,eAAe,SAAS,aAAc,MAAM;AAClD,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS;AACb,UAAI,eAAe;AACnB,UAAI,eAAe;AACnB,UAAI,UAAU;AACd,UAAI,UAAU;AAEd,eAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,uBAAe,eAAe;AAC9B,kBAAU,UAAU;AAEpB,iBAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,cAAIA,UAAS,KAAK,IAAI,KAAK,GAAG;AAC9B,cAAIA,YAAW,SAAS;AACtB;AAAA,UACF,OAAO;AACL,gBAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AACpE,sBAAUA;AACV,2BAAe;AAAA,UACjB;AAEA,UAAAA,UAAS,KAAK,IAAI,KAAK,GAAG;AAC1B,cAAIA,YAAW,SAAS;AACtB;AAAA,UACF,OAAO;AACL,gBAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AACpE,sBAAUA;AACV,2BAAe;AAAA,UACjB;AAAA,QACF;AAEA,YAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AACpE,YAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AAAA,MACtE;AAEA,aAAO;AAAA,IACT;AAOA,YAAQ,eAAe,SAAS,aAAc,MAAM;AAClD,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS;AAEb,eAAS,MAAM,GAAG,MAAM,OAAO,GAAG,OAAO;AACvC,iBAAS,MAAM,GAAG,MAAM,OAAO,GAAG,OAAO;AACvC,gBAAM,OAAO,KAAK,IAAI,KAAK,GAAG,IAC5B,KAAK,IAAI,KAAK,MAAM,CAAC,IACrB,KAAK,IAAI,MAAM,GAAG,GAAG,IACrB,KAAK,IAAI,MAAM,GAAG,MAAM,CAAC;AAE3B,cAAI,SAAS,KAAK,SAAS,EAAG;AAAA,QAChC;AAAA,MACF;AAEA,aAAO,SAAS,cAAc;AAAA,IAChC;AAQA,YAAQ,eAAe,SAAS,aAAc,MAAM;AAClD,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS;AACb,UAAI,UAAU;AACd,UAAI,UAAU;AAEd,eAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,kBAAU,UAAU;AACpB,iBAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,oBAAY,WAAW,IAAK,OAAS,KAAK,IAAI,KAAK,GAAG;AACtD,cAAI,OAAO,OAAO,YAAY,QAAS,YAAY,IAAQ;AAE3D,oBAAY,WAAW,IAAK,OAAS,KAAK,IAAI,KAAK,GAAG;AACtD,cAAI,OAAO,OAAO,YAAY,QAAS,YAAY,IAAQ;AAAA,QAC7D;AAAA,MACF;AAEA,aAAO,SAAS,cAAc;AAAA,IAChC;AAUA,YAAQ,eAAe,SAAS,aAAc,MAAM;AAClD,UAAI,YAAY;AAChB,YAAM,eAAe,KAAK,KAAK;AAE/B,eAAS,IAAI,GAAG,IAAI,cAAc,IAAK,cAAa,KAAK,KAAK,CAAC;AAE/D,YAAM,IAAI,KAAK,IAAI,KAAK,KAAM,YAAY,MAAM,eAAgB,CAAC,IAAI,EAAE;AAEvE,aAAO,IAAI,cAAc;AAAA,IAC3B;AAUA,aAAS,UAAW,aAAa,GAAG,GAAG;AACrC,cAAQ,aAAa;AAAA,QACnB,KAAK,QAAQ,SAAS;AAAY,kBAAQ,IAAI,KAAK,MAAM;AAAA,QACzD,KAAK,QAAQ,SAAS;AAAY,iBAAO,IAAI,MAAM;AAAA,QACnD,KAAK,QAAQ,SAAS;AAAY,iBAAO,IAAI,MAAM;AAAA,QACnD,KAAK,QAAQ,SAAS;AAAY,kBAAQ,IAAI,KAAK,MAAM;AAAA,QACzD,KAAK,QAAQ,SAAS;AAAY,kBAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM;AAAA,QACzF,KAAK,QAAQ,SAAS;AAAY,iBAAQ,IAAI,IAAK,IAAK,IAAI,IAAK,MAAM;AAAA,QACvE,KAAK,QAAQ,SAAS;AAAY,kBAAS,IAAI,IAAK,IAAK,IAAI,IAAK,KAAK,MAAM;AAAA,QAC7E,KAAK,QAAQ,SAAS;AAAY,kBAAS,IAAI,IAAK,KAAK,IAAI,KAAK,KAAK,MAAM;AAAA,QAE7E;AAAS,gBAAM,IAAI,MAAM,qBAAqB,WAAW;AAAA,MAC3D;AAAA,IACF;AAQA,YAAQ,YAAY,SAAS,UAAW,SAAS,MAAM;AACrD,YAAM,OAAO,KAAK;AAElB,eAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,iBAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,cAAI,KAAK,WAAW,KAAK,GAAG,EAAG;AAC/B,eAAK,IAAI,KAAK,KAAK,UAAU,SAAS,KAAK,GAAG,CAAC;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAQA,YAAQ,cAAc,SAAS,YAAa,MAAM,iBAAiB;AACjE,YAAM,cAAc,OAAO,KAAK,QAAQ,QAAQ,EAAE;AAClD,UAAI,cAAc;AAClB,UAAI,eAAe;AAEnB,eAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,wBAAgB,CAAC;AACjB,gBAAQ,UAAU,GAAG,IAAI;AAGzB,cAAM,UACJ,QAAQ,aAAa,IAAI,IACzB,QAAQ,aAAa,IAAI,IACzB,QAAQ,aAAa,IAAI,IACzB,QAAQ,aAAa,IAAI;AAG3B,gBAAQ,UAAU,GAAG,IAAI;AAEzB,YAAI,UAAU,cAAc;AAC1B,yBAAe;AACf,wBAAc;AAAA,QAChB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACzOA;AAAA;AAAA,QAAM,UAAU;AAEhB,QAAM,kBAAkB;AAAA;AAAA,MAEtB;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,MACV;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,MACV;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,MACV;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,IACd;AAEA,QAAM,qBAAqB;AAAA;AAAA,MAEzB;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAK;AAAA,MACb;AAAA,MAAI;AAAA,MAAI;AAAA,MAAK;AAAA,MACb;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MACd;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MACd;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MACd;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MACd;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,IACnB;AAUA,YAAQ,iBAAiB,SAAS,eAAgB,SAAS,sBAAsB;AAC/E,cAAQ,sBAAsB;AAAA,QAC5B,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAUA,YAAQ,yBAAyB,SAAS,uBAAwB,SAAS,sBAAsB;AAC/F,cAAQ,sBAAsB;AAAA,QAC5B,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAAA;AAAA;;;ACtIA;AAAA;AAAA,QAAM,YAAY,IAAI,WAAW,GAAG;AACpC,QAAM,YAAY,IAAI,WAAW,GAAG;AASnC,KAAC,SAAS,aAAc;AACvB,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,kBAAU,CAAC,IAAI;AACf,kBAAU,CAAC,IAAI;AAEf,cAAM;AAIN,YAAI,IAAI,KAAO;AACb,eAAK;AAAA,QACP;AAAA,MACF;AAMA,eAAS,IAAI,KAAK,IAAI,KAAK,KAAK;AAC9B,kBAAU,CAAC,IAAI,UAAU,IAAI,GAAG;AAAA,MAClC;AAAA,IACF,GAAE;AAQF,YAAQ,MAAM,SAAS,IAAK,GAAG;AAC7B,UAAI,IAAI,EAAG,OAAM,IAAI,MAAM,SAAS,IAAI,GAAG;AAC3C,aAAO,UAAU,CAAC;AAAA,IACpB;AAQA,YAAQ,MAAM,SAAS,IAAK,GAAG;AAC7B,aAAO,UAAU,CAAC;AAAA,IACpB;AASA,YAAQ,MAAM,SAAS,IAAK,GAAG,GAAG;AAChC,UAAI,MAAM,KAAK,MAAM,EAAG,QAAO;AAI/B,aAAO,UAAU,UAAU,CAAC,IAAI,UAAU,CAAC,CAAC;AAAA,IAC9C;AAAA;AAAA;;;ACpEA;AAAA;AAAA,QAAM,KAAK;AASX,YAAQ,MAAM,SAAS,IAAK,IAAI,IAAI;AAClC,YAAM,QAAQ,IAAI,WAAW,GAAG,SAAS,GAAG,SAAS,CAAC;AAEtD,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,iBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,gBAAM,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,QACrC;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AASA,YAAQ,MAAM,SAAS,IAAK,UAAU,SAAS;AAC7C,UAAI,SAAS,IAAI,WAAW,QAAQ;AAEpC,aAAQ,OAAO,SAAS,QAAQ,UAAW,GAAG;AAC5C,cAAM,QAAQ,OAAO,CAAC;AAEtB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,iBAAO,CAAC,KAAK,GAAG,IAAI,QAAQ,CAAC,GAAG,KAAK;AAAA,QACvC;AAGA,YAAI,SAAS;AACb,eAAO,SAAS,OAAO,UAAU,OAAO,MAAM,MAAM,EAAG;AACvD,iBAAS,OAAO,MAAM,MAAM;AAAA,MAC9B;AAEA,aAAO;AAAA,IACT;AASA,YAAQ,uBAAuB,SAAS,qBAAsB,QAAQ;AACpE,UAAI,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC;AAC7B,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,eAAO,QAAQ,IAAI,MAAM,IAAI,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAAA,MACzD;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC7DA;AAAA;AAAA,QAAM,aAAa;AAEnB,aAAS,mBAAoB,QAAQ;AACnC,WAAK,UAAU;AACf,WAAK,SAAS;AAEd,UAAI,KAAK,OAAQ,MAAK,WAAW,KAAK,MAAM;AAAA,IAC9C;AAQA,uBAAmB,UAAU,aAAa,SAAS,WAAY,QAAQ;AAErE,WAAK,SAAS;AACd,WAAK,UAAU,WAAW,qBAAqB,KAAK,MAAM;AAAA,IAC5D;AAQA,uBAAmB,UAAU,SAAS,SAAS,OAAQ,MAAM;AAC3D,UAAI,CAAC,KAAK,SAAS;AACjB,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AAIA,YAAM,aAAa,IAAI,WAAW,KAAK,SAAS,KAAK,MAAM;AAC3D,iBAAW,IAAI,IAAI;AAInB,YAAM,YAAY,WAAW,IAAI,YAAY,KAAK,OAAO;AAKzD,YAAM,QAAQ,KAAK,SAAS,UAAU;AACtC,UAAI,QAAQ,GAAG;AACb,cAAM,OAAO,IAAI,WAAW,KAAK,MAAM;AACvC,aAAK,IAAI,WAAW,KAAK;AAEzB,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvDjB;AAAA;AAMA,YAAQ,UAAU,SAAS,QAAS,SAAS;AAC3C,aAAO,CAAC,MAAM,OAAO,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD;AAAA;AAAA;;;ACRA;AAAA;AAAA,QAAM,UAAU;AAChB,QAAM,eAAe;AACrB,QAAI,QAAQ;AAIZ,YAAQ,MAAM,QAAQ,MAAM,KAAK;AAEjC,QAAM,OAAO,+BAA+B,QAAQ;AAEpD,YAAQ,QAAQ,IAAI,OAAO,OAAO,GAAG;AACrC,YAAQ,aAAa,IAAI,OAAO,yBAAyB,GAAG;AAC5D,YAAQ,OAAO,IAAI,OAAO,MAAM,GAAG;AACnC,YAAQ,UAAU,IAAI,OAAO,SAAS,GAAG;AACzC,YAAQ,eAAe,IAAI,OAAO,cAAc,GAAG;AAEnD,QAAM,aAAa,IAAI,OAAO,MAAM,QAAQ,GAAG;AAC/C,QAAM,eAAe,IAAI,OAAO,MAAM,UAAU,GAAG;AACnD,QAAM,oBAAoB,IAAI,OAAO,wBAAwB;AAE7D,YAAQ,YAAY,SAAS,UAAW,KAAK;AAC3C,aAAO,WAAW,KAAK,GAAG;AAAA,IAC5B;AAEA,YAAQ,cAAc,SAAS,YAAa,KAAK;AAC/C,aAAO,aAAa,KAAK,GAAG;AAAA,IAC9B;AAEA,YAAQ,mBAAmB,SAAS,iBAAkB,KAAK;AACzD,aAAO,kBAAkB,KAAK,GAAG;AAAA,IACnC;AAAA;AAAA;;;AC9BA;AAAA;AAAA,QAAM,eAAe;AACrB,QAAM,QAAQ;AASd,YAAQ,UAAU;AAAA,MAChB,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,IAAI,IAAI,EAAE;AAAA,IACrB;AAWA,YAAQ,eAAe;AAAA,MACrB,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,GAAG,IAAI,EAAE;AAAA,IACpB;AAOA,YAAQ,OAAO;AAAA,MACb,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,GAAG,IAAI,EAAE;AAAA,IACpB;AAWA,YAAQ,QAAQ;AAAA,MACd,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,GAAG,IAAI,EAAE;AAAA,IACpB;AAQA,YAAQ,QAAQ;AAAA,MACd,KAAK;AAAA,IACP;AAUA,YAAQ,wBAAwB,SAAS,sBAAuB,MAAM,SAAS;AAC7E,UAAI,CAAC,KAAK,OAAQ,OAAM,IAAI,MAAM,mBAAmB,IAAI;AAEzD,UAAI,CAAC,aAAa,QAAQ,OAAO,GAAG;AAClC,cAAM,IAAI,MAAM,sBAAsB,OAAO;AAAA,MAC/C;AAEA,UAAI,WAAW,KAAK,UAAU,GAAI,QAAO,KAAK,OAAO,CAAC;AAAA,eAC7C,UAAU,GAAI,QAAO,KAAK,OAAO,CAAC;AAC3C,aAAO,KAAK,OAAO,CAAC;AAAA,IACtB;AAQA,YAAQ,qBAAqB,SAAS,mBAAoB,SAAS;AACjE,UAAI,MAAM,YAAY,OAAO,EAAG,QAAO,QAAQ;AAAA,eACtC,MAAM,iBAAiB,OAAO,EAAG,QAAO,QAAQ;AAAA,eAChD,MAAM,UAAU,OAAO,EAAG,QAAO,QAAQ;AAAA,UAC7C,QAAO,QAAQ;AAAA,IACtB;AAQA,YAAQ,WAAW,SAAS,SAAU,MAAM;AAC1C,UAAI,QAAQ,KAAK,GAAI,QAAO,KAAK;AACjC,YAAM,IAAI,MAAM,cAAc;AAAA,IAChC;AAQA,YAAQ,UAAU,SAAS,QAAS,MAAM;AACxC,aAAO,QAAQ,KAAK,OAAO,KAAK;AAAA,IAClC;AAQA,aAAS,WAAY,QAAQ;AAC3B,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACzC;AAEA,YAAM,QAAQ,OAAO,YAAY;AAEjC,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB;AACE,gBAAM,IAAI,MAAM,mBAAmB,MAAM;AAAA,MAC7C;AAAA,IACF;AAUA,YAAQ,OAAO,SAAS,KAAM,OAAO,cAAc;AACjD,UAAI,QAAQ,QAAQ,KAAK,GAAG;AAC1B,eAAO;AAAA,MACT;AAEA,UAAI;AACF,eAAO,WAAW,KAAK;AAAA,MACzB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;ACtKA;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,SAAS;AACf,QAAM,UAAU;AAChB,QAAM,OAAO;AACb,QAAM,eAAe;AAGrB,QAAM,MAAO,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;AAClG,QAAM,UAAU,MAAM,YAAY,GAAG;AAErC,aAAS,4BAA6B,MAAM,QAAQ,sBAAsB;AACxE,eAAS,iBAAiB,GAAG,kBAAkB,IAAI,kBAAkB;AACnE,YAAI,UAAU,QAAQ,YAAY,gBAAgB,sBAAsB,IAAI,GAAG;AAC7E,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,qBAAsB,MAAM,SAAS;AAE5C,aAAO,KAAK,sBAAsB,MAAM,OAAO,IAAI;AAAA,IACrD;AAEA,aAAS,0BAA2B,UAAU,SAAS;AACrD,UAAI,YAAY;AAEhB,eAAS,QAAQ,SAAU,MAAM;AAC/B,cAAM,eAAe,qBAAqB,KAAK,MAAM,OAAO;AAC5D,qBAAa,eAAe,KAAK,cAAc;AAAA,MACjD,CAAC;AAED,aAAO;AAAA,IACT;AAEA,aAAS,2BAA4B,UAAU,sBAAsB;AACnE,eAAS,iBAAiB,GAAG,kBAAkB,IAAI,kBAAkB;AACnE,cAAM,SAAS,0BAA0B,UAAU,cAAc;AACjE,YAAI,UAAU,QAAQ,YAAY,gBAAgB,sBAAsB,KAAK,KAAK,GAAG;AACnF,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAUA,YAAQ,OAAO,SAAS,KAAM,OAAO,cAAc;AACjD,UAAI,aAAa,QAAQ,KAAK,GAAG;AAC/B,eAAO,SAAS,OAAO,EAAE;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AAWA,YAAQ,cAAc,SAAS,YAAa,SAAS,sBAAsB,MAAM;AAC/E,UAAI,CAAC,aAAa,QAAQ,OAAO,GAAG;AAClC,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AAGA,UAAI,OAAO,SAAS,YAAa,QAAO,KAAK;AAG7C,YAAM,iBAAiB,MAAM,wBAAwB,OAAO;AAG5D,YAAM,mBAAmB,OAAO,uBAAuB,SAAS,oBAAoB;AAGpF,YAAM,0BAA0B,iBAAiB,oBAAoB;AAErE,UAAI,SAAS,KAAK,MAAO,QAAO;AAEhC,YAAM,aAAa,yBAAyB,qBAAqB,MAAM,OAAO;AAG9E,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,iBAAO,KAAK,MAAO,aAAa,KAAM,CAAC;AAAA,QAEzC,KAAK,KAAK;AACR,iBAAO,KAAK,MAAO,aAAa,KAAM,CAAC;AAAA,QAEzC,KAAK,KAAK;AACR,iBAAO,KAAK,MAAM,aAAa,EAAE;AAAA,QAEnC,KAAK,KAAK;AAAA,QACV;AACE,iBAAO,KAAK,MAAM,aAAa,CAAC;AAAA,MACpC;AAAA,IACF;AAUA,YAAQ,wBAAwB,SAAS,sBAAuB,MAAM,sBAAsB;AAC1F,UAAI;AAEJ,YAAM,MAAM,QAAQ,KAAK,sBAAsB,QAAQ,CAAC;AAExD,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,YAAI,KAAK,SAAS,GAAG;AACnB,iBAAO,2BAA2B,MAAM,GAAG;AAAA,QAC7C;AAEA,YAAI,KAAK,WAAW,GAAG;AACrB,iBAAO;AAAA,QACT;AAEA,cAAM,KAAK,CAAC;AAAA,MACd,OAAO;AACL,cAAM;AAAA,MACR;AAEA,aAAO,4BAA4B,IAAI,MAAM,IAAI,UAAU,GAAG,GAAG;AAAA,IACnE;AAYA,YAAQ,iBAAiB,SAAS,eAAgB,SAAS;AACzD,UAAI,CAAC,aAAa,QAAQ,OAAO,KAAK,UAAU,GAAG;AACjD,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AAEA,UAAI,IAAI,WAAW;AAEnB,aAAO,MAAM,YAAY,CAAC,IAAI,WAAW,GAAG;AAC1C,aAAM,OAAQ,MAAM,YAAY,CAAC,IAAI;AAAA,MACvC;AAEA,aAAQ,WAAW,KAAM;AAAA,IAC3B;AAAA;AAAA;;;AClKA;AAAA;AAAA,QAAM,QAAQ;AAEd,QAAM,MAAO,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;AACrF,QAAM,WAAY,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK;AACtE,QAAM,UAAU,MAAM,YAAY,GAAG;AAYrC,YAAQ,iBAAiB,SAAS,eAAgB,sBAAsB,MAAM;AAC5E,YAAM,OAAS,qBAAqB,OAAO,IAAK;AAChD,UAAI,IAAI,QAAQ;AAEhB,aAAO,MAAM,YAAY,CAAC,IAAI,WAAW,GAAG;AAC1C,aAAM,OAAQ,MAAM,YAAY,CAAC,IAAI;AAAA,MACvC;AAKA,cAAS,QAAQ,KAAM,KAAK;AAAA,IAC9B;AAAA;AAAA;;;AC5BA;AAAA;AAAA,QAAM,OAAO;AAEb,aAAS,YAAa,MAAM;AAC1B,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO,KAAK,SAAS;AAAA,IAC5B;AAEA,gBAAY,gBAAgB,SAAS,cAAe,QAAQ;AAC1D,aAAO,KAAK,KAAK,MAAM,SAAS,CAAC,KAAM,SAAS,IAAO,SAAS,IAAK,IAAI,IAAK;AAAA,IAChF;AAEA,gBAAY,UAAU,YAAY,SAAS,YAAa;AACtD,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,gBAAY,UAAU,gBAAgB,SAAS,gBAAiB;AAC9D,aAAO,YAAY,cAAc,KAAK,KAAK,MAAM;AAAA,IACnD;AAEA,gBAAY,UAAU,QAAQ,SAAS,MAAO,WAAW;AACvD,UAAI,GAAG,OAAO;AAId,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK,KAAK,QAAQ,KAAK,GAAG;AAC7C,gBAAQ,KAAK,KAAK,OAAO,GAAG,CAAC;AAC7B,gBAAQ,SAAS,OAAO,EAAE;AAE1B,kBAAU,IAAI,OAAO,EAAE;AAAA,MACzB;AAIA,YAAM,eAAe,KAAK,KAAK,SAAS;AACxC,UAAI,eAAe,GAAG;AACpB,gBAAQ,KAAK,KAAK,OAAO,CAAC;AAC1B,gBAAQ,SAAS,OAAO,EAAE;AAE1B,kBAAU,IAAI,OAAO,eAAe,IAAI,CAAC;AAAA,MAC3C;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC1CjB;AAAA;AAAA,QAAM,OAAO;AAWb,QAAM,kBAAkB;AAAA,MACtB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC7C;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC5D;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC5D;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,IAC1C;AAEA,aAAS,iBAAkB,MAAM;AAC/B,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO;AAAA,IACd;AAEA,qBAAiB,gBAAgB,SAAS,cAAe,QAAQ;AAC/D,aAAO,KAAK,KAAK,MAAM,SAAS,CAAC,IAAI,KAAK,SAAS;AAAA,IACrD;AAEA,qBAAiB,UAAU,YAAY,SAAS,YAAa;AAC3D,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,qBAAiB,UAAU,gBAAgB,SAAS,gBAAiB;AACnE,aAAO,iBAAiB,cAAc,KAAK,KAAK,MAAM;AAAA,IACxD;AAEA,qBAAiB,UAAU,QAAQ,SAAS,MAAO,WAAW;AAC5D,UAAI;AAIJ,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK,KAAK,QAAQ,KAAK,GAAG;AAE7C,YAAI,QAAQ,gBAAgB,QAAQ,KAAK,KAAK,CAAC,CAAC,IAAI;AAGpD,iBAAS,gBAAgB,QAAQ,KAAK,KAAK,IAAI,CAAC,CAAC;AAGjD,kBAAU,IAAI,OAAO,EAAE;AAAA,MACzB;AAIA,UAAI,KAAK,KAAK,SAAS,GAAG;AACxB,kBAAU,IAAI,gBAAgB,QAAQ,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC;AAAA,MACxD;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC1DjB;AAAA;AAAA;AAEA,WAAO,UAAU,SAAS,WAAY,OAAO;AAC3C,UAAI,SAAS,CAAC;AACd,UAAI,OAAO,MAAM;AAEjB,eAAS,QAAQ,GAAG,QAAQ,MAAM,SAAS;AACzC,YAAI,QAAQ,MAAM,WAAW,KAAK;AAElC,YAAI,SAAS,SAAU,SAAS,SAAU,OAAO,QAAQ,GAAG;AAC1D,cAAI,SAAS,MAAM,WAAW,QAAQ,CAAC;AAEvC,cAAI,UAAU,SAAU,UAAU,OAAQ;AAExC,qBAAS,QAAQ,SAAU,OAAQ,SAAS,QAAS;AACrD,qBAAS;AAAA,UACX;AAAA,QACF;AAGA,YAAI,QAAQ,KAAM;AAChB,iBAAO,KAAK,KAAK;AACjB;AAAA,QACF;AAGA,YAAI,QAAQ,MAAO;AACjB,iBAAO,KAAM,SAAS,IAAK,GAAG;AAC9B,iBAAO,KAAM,QAAQ,KAAM,GAAG;AAC9B;AAAA,QACF;AAGA,YAAI,QAAQ,SAAW,SAAS,SAAU,QAAQ,OAAU;AAC1D,iBAAO,KAAM,SAAS,KAAM,GAAG;AAC/B,iBAAO,KAAO,SAAS,IAAK,KAAM,GAAG;AACrC,iBAAO,KAAM,QAAQ,KAAM,GAAG;AAC9B;AAAA,QACF;AAGA,YAAI,SAAS,SAAW,SAAS,SAAU;AACzC,iBAAO,KAAM,SAAS,KAAM,GAAG;AAC/B,iBAAO,KAAO,SAAS,KAAM,KAAM,GAAG;AACtC,iBAAO,KAAO,SAAS,IAAK,KAAM,GAAG;AACrC,iBAAO,KAAM,QAAQ,KAAM,GAAG;AAC9B;AAAA,QACF;AAGA,eAAO,KAAK,KAAM,KAAM,GAAI;AAAA,MAC9B;AAEA,aAAO,IAAI,WAAW,MAAM,EAAE;AAAA,IAChC;AAAA;AAAA;;;ACtDA;AAAA;AAAA,QAAM,aAAa;AACnB,QAAM,OAAO;AAEb,aAAS,SAAU,MAAM;AACvB,WAAK,OAAO,KAAK;AACjB,UAAI,OAAQ,SAAU,UAAU;AAC9B,eAAO,WAAW,IAAI;AAAA,MACxB;AACA,WAAK,OAAO,IAAI,WAAW,IAAI;AAAA,IACjC;AAEA,aAAS,gBAAgB,SAAS,cAAe,QAAQ;AACvD,aAAO,SAAS;AAAA,IAClB;AAEA,aAAS,UAAU,YAAY,SAAS,YAAa;AACnD,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,aAAS,UAAU,gBAAgB,SAAS,gBAAiB;AAC3D,aAAO,SAAS,cAAc,KAAK,KAAK,MAAM;AAAA,IAChD;AAEA,aAAS,UAAU,QAAQ,SAAU,WAAW;AAC9C,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,IAAI,GAAG,KAAK;AAChD,kBAAU,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC;AAAA,MAC/B;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7BjB;AAAA;AAAA,QAAM,OAAO;AACb,QAAM,QAAQ;AAEd,aAAS,UAAW,MAAM;AACxB,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO;AAAA,IACd;AAEA,cAAU,gBAAgB,SAAS,cAAe,QAAQ;AACxD,aAAO,SAAS;AAAA,IAClB;AAEA,cAAU,UAAU,YAAY,SAAS,YAAa;AACpD,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,cAAU,UAAU,gBAAgB,SAAS,gBAAiB;AAC5D,aAAO,UAAU,cAAc,KAAK,KAAK,MAAM;AAAA,IACjD;AAEA,cAAU,UAAU,QAAQ,SAAU,WAAW;AAC/C,UAAI;AAKJ,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACrC,YAAI,QAAQ,MAAM,OAAO,KAAK,KAAK,CAAC,CAAC;AAGrC,YAAI,SAAS,SAAU,SAAS,OAAQ;AAEtC,mBAAS;AAAA,QAGX,WAAW,SAAS,SAAU,SAAS,OAAQ;AAE7C,mBAAS;AAAA,QACX,OAAO;AACL,gBAAM,IAAI;AAAA,YACR,6BAA6B,KAAK,KAAK,CAAC,IAAI;AAAA,UACX;AAAA,QACrC;AAIA,iBAAW,UAAU,IAAK,OAAQ,OAAS,QAAQ;AAGnD,kBAAU,IAAI,OAAO,EAAE;AAAA,MACzB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrDjB;AAAA;AAAA;AAuBA,QAAI,WAAW;AAAA,MACb,8BAA8B,SAAS,OAAO,GAAG,GAAG;AAGlD,YAAI,eAAe,CAAC;AAIpB,YAAI,QAAQ,CAAC;AACb,cAAM,CAAC,IAAI;AAMX,YAAI,OAAO,SAAS,cAAc,KAAK;AACvC,aAAK,KAAK,GAAG,CAAC;AAEd,YAAI,SACA,GAAG,GACH,gBACA,gBACA,WACA,+BACA,gBACA;AACJ,eAAO,CAAC,KAAK,MAAM,GAAG;AAGpB,oBAAU,KAAK,IAAI;AACnB,cAAI,QAAQ;AACZ,2BAAiB,QAAQ;AAGzB,2BAAiB,MAAM,CAAC,KAAK,CAAC;AAK9B,eAAK,KAAK,gBAAgB;AACxB,gBAAI,eAAe,eAAe,CAAC,GAAG;AAEpC,0BAAY,eAAe,CAAC;AAK5B,8CAAgC,iBAAiB;AAMjD,+BAAiB,MAAM,CAAC;AACxB,4BAAe,OAAO,MAAM,CAAC,MAAM;AACnC,kBAAI,eAAe,iBAAiB,+BAA+B;AACjE,sBAAM,CAAC,IAAI;AACX,qBAAK,KAAK,GAAG,6BAA6B;AAC1C,6BAAa,CAAC,IAAI;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,OAAO,MAAM,eAAe,OAAO,MAAM,CAAC,MAAM,aAAa;AAC/D,cAAI,MAAM,CAAC,+BAA+B,GAAG,QAAQ,GAAG,GAAG,EAAE,KAAK,EAAE;AACpE,gBAAM,IAAI,MAAM,GAAG;AAAA,QACrB;AAEA,eAAO;AAAA,MACT;AAAA,MAEA,6CAA6C,SAAS,cAAc,GAAG;AACrE,YAAI,QAAQ,CAAC;AACb,YAAI,IAAI;AACR,YAAI;AACJ,eAAO,GAAG;AACR,gBAAM,KAAK,CAAC;AACZ,wBAAc,aAAa,CAAC;AAC5B,cAAI,aAAa,CAAC;AAAA,QACpB;AACA,cAAM,QAAQ;AACd,eAAO;AAAA,MACT;AAAA,MAEA,WAAW,SAAS,OAAO,GAAG,GAAG;AAC/B,YAAI,eAAe,SAAS,6BAA6B,OAAO,GAAG,CAAC;AACpE,eAAO,SAAS;AAAA,UACd;AAAA,UAAc;AAAA,QAAC;AAAA,MACnB;AAAA;AAAA;AAAA;AAAA,MAKA,eAAe;AAAA,QACb,MAAM,SAAU,MAAM;AACpB,cAAI,IAAI,SAAS,eACb,IAAI,CAAC,GACL;AACJ,iBAAO,QAAQ,CAAC;AAChB,eAAK,OAAO,GAAG;AACb,gBAAI,EAAE,eAAe,GAAG,GAAG;AACzB,gBAAE,GAAG,IAAI,EAAE,GAAG;AAAA,YAChB;AAAA,UACF;AACA,YAAE,QAAQ,CAAC;AACX,YAAE,SAAS,KAAK,UAAU,EAAE;AAC5B,iBAAO;AAAA,QACT;AAAA,QAEA,gBAAgB,SAAU,GAAG,GAAG;AAC9B,iBAAO,EAAE,OAAO,EAAE;AAAA,QACpB;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,MAAM,SAAU,OAAO,MAAM;AAC3B,cAAI,OAAO,EAAC,OAAc,KAAU;AACpC,eAAK,MAAM,KAAK,IAAI;AACpB,eAAK,MAAM,KAAK,KAAK,MAAM;AAAA,QAC7B;AAAA;AAAA;AAAA;AAAA,QAKA,KAAK,WAAY;AACf,iBAAO,KAAK,MAAM,MAAM;AAAA,QAC1B;AAAA,QAEA,OAAO,WAAY;AACjB,iBAAO,KAAK,MAAM,WAAW;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAIA,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACpKA;AAAA;AAAA,QAAM,OAAO;AACb,QAAM,cAAc;AACpB,QAAM,mBAAmB;AACzB,QAAM,WAAW;AACjB,QAAM,YAAY;AAClB,QAAM,QAAQ;AACd,QAAM,QAAQ;AACd,QAAM,WAAW;AAQjB,aAAS,oBAAqB,KAAK;AACjC,aAAO,SAAS,mBAAmB,GAAG,CAAC,EAAE;AAAA,IAC3C;AAUA,aAAS,YAAa,OAAO,MAAM,KAAK;AACtC,YAAM,WAAW,CAAC;AAClB,UAAI;AAEJ,cAAQ,SAAS,MAAM,KAAK,GAAG,OAAO,MAAM;AAC1C,iBAAS,KAAK;AAAA,UACZ,MAAM,OAAO,CAAC;AAAA,UACd,OAAO,OAAO;AAAA,UACd;AAAA,UACA,QAAQ,OAAO,CAAC,EAAE;AAAA,QACpB,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IACT;AASA,aAAS,sBAAuB,SAAS;AACvC,YAAM,UAAU,YAAY,MAAM,SAAS,KAAK,SAAS,OAAO;AAChE,YAAM,eAAe,YAAY,MAAM,cAAc,KAAK,cAAc,OAAO;AAC/E,UAAI;AACJ,UAAI;AAEJ,UAAI,MAAM,mBAAmB,GAAG;AAC9B,mBAAW,YAAY,MAAM,MAAM,KAAK,MAAM,OAAO;AACrD,oBAAY,YAAY,MAAM,OAAO,KAAK,OAAO,OAAO;AAAA,MAC1D,OAAO;AACL,mBAAW,YAAY,MAAM,YAAY,KAAK,MAAM,OAAO;AAC3D,oBAAY,CAAC;AAAA,MACf;AAEA,YAAM,OAAO,QAAQ,OAAO,cAAc,UAAU,SAAS;AAE7D,aAAO,KACJ,KAAK,SAAU,IAAI,IAAI;AACtB,eAAO,GAAG,QAAQ,GAAG;AAAA,MACvB,CAAC,EACA,IAAI,SAAU,KAAK;AAClB,eAAO;AAAA,UACL,MAAM,IAAI;AAAA,UACV,MAAM,IAAI;AAAA,UACV,QAAQ,IAAI;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACL;AAUA,aAAS,qBAAsB,QAAQ,MAAM;AAC3C,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,iBAAO,YAAY,cAAc,MAAM;AAAA,QACzC,KAAK,KAAK;AACR,iBAAO,iBAAiB,cAAc,MAAM;AAAA,QAC9C,KAAK,KAAK;AACR,iBAAO,UAAU,cAAc,MAAM;AAAA,QACvC,KAAK,KAAK;AACR,iBAAO,SAAS,cAAc,MAAM;AAAA,MACxC;AAAA,IACF;AAQA,aAAS,cAAe,MAAM;AAC5B,aAAO,KAAK,OAAO,SAAU,KAAK,MAAM;AACtC,cAAM,UAAU,IAAI,SAAS,KAAK,IAAI,IAAI,IAAI,SAAS,CAAC,IAAI;AAC5D,YAAI,WAAW,QAAQ,SAAS,KAAK,MAAM;AACzC,cAAI,IAAI,SAAS,CAAC,EAAE,QAAQ,KAAK;AACjC,iBAAO;AAAA,QACT;AAEA,YAAI,KAAK,IAAI;AACb,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP;AAkBA,aAAS,WAAY,MAAM;AACzB,YAAM,QAAQ,CAAC;AACf,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,MAAM,KAAK,CAAC;AAElB,gBAAQ,IAAI,MAAM;AAAA,UAChB,KAAK,KAAK;AACR,kBAAM,KAAK;AAAA,cAAC;AAAA,cACV,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,cAAc,QAAQ,IAAI,OAAO;AAAA,cAC9D,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM,QAAQ,IAAI,OAAO;AAAA,YACxD,CAAC;AACD;AAAA,UACF,KAAK,KAAK;AACR,kBAAM,KAAK;AAAA,cAAC;AAAA,cACV,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM,QAAQ,IAAI,OAAO;AAAA,YACxD,CAAC;AACD;AAAA,UACF,KAAK,KAAK;AACR,kBAAM,KAAK;AAAA,cAAC;AAAA,cACV,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM,QAAQ,oBAAoB,IAAI,IAAI,EAAE;AAAA,YAC3E,CAAC;AACD;AAAA,UACF,KAAK,KAAK;AACR,kBAAM,KAAK;AAAA,cACT,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM,QAAQ,oBAAoB,IAAI,IAAI,EAAE;AAAA,YAC3E,CAAC;AAAA,QACL;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAcA,aAAS,WAAY,OAAO,SAAS;AACnC,YAAM,QAAQ,CAAC;AACf,YAAM,QAAQ,EAAE,OAAO,CAAC,EAAE;AAC1B,UAAI,cAAc,CAAC,OAAO;AAE1B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,YAAY,MAAM,CAAC;AACzB,cAAM,iBAAiB,CAAC;AAExB,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAM,OAAO,UAAU,CAAC;AACxB,gBAAM,MAAM,KAAK,IAAI;AAErB,yBAAe,KAAK,GAAG;AACvB,gBAAM,GAAG,IAAI,EAAE,MAAY,WAAW,EAAE;AACxC,gBAAM,GAAG,IAAI,CAAC;AAEd,mBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,kBAAM,aAAa,YAAY,CAAC;AAEhC,gBAAI,MAAM,UAAU,KAAK,MAAM,UAAU,EAAE,KAAK,SAAS,KAAK,MAAM;AAClE,oBAAM,UAAU,EAAE,GAAG,IACnB,qBAAqB,MAAM,UAAU,EAAE,YAAY,KAAK,QAAQ,KAAK,IAAI,IACzE,qBAAqB,MAAM,UAAU,EAAE,WAAW,KAAK,IAAI;AAE7D,oBAAM,UAAU,EAAE,aAAa,KAAK;AAAA,YACtC,OAAO;AACL,kBAAI,MAAM,UAAU,EAAG,OAAM,UAAU,EAAE,YAAY,KAAK;AAE1D,oBAAM,UAAU,EAAE,GAAG,IAAI,qBAAqB,KAAK,QAAQ,KAAK,IAAI,IAClE,IAAI,KAAK,sBAAsB,KAAK,MAAM,OAAO;AAAA,YACrD;AAAA,UACF;AAAA,QACF;AAEA,sBAAc;AAAA,MAChB;AAEA,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,cAAM,YAAY,CAAC,CAAC,EAAE,MAAM;AAAA,MAC9B;AAEA,aAAO,EAAE,KAAK,OAAO,MAAa;AAAA,IACpC;AAUA,aAAS,mBAAoB,MAAM,WAAW;AAC5C,UAAI;AACJ,YAAM,WAAW,KAAK,mBAAmB,IAAI;AAE7C,aAAO,KAAK,KAAK,WAAW,QAAQ;AAGpC,UAAI,SAAS,KAAK,QAAQ,KAAK,MAAM,SAAS,KAAK;AACjD,cAAM,IAAI,MAAM,MAAM,OAAO,mCACO,KAAK,SAAS,IAAI,IACpD,4BAA4B,KAAK,SAAS,QAAQ,CAAC;AAAA,MACvD;AAGA,UAAI,SAAS,KAAK,SAAS,CAAC,MAAM,mBAAmB,GAAG;AACtD,eAAO,KAAK;AAAA,MACd;AAEA,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,iBAAO,IAAI,YAAY,IAAI;AAAA,QAE7B,KAAK,KAAK;AACR,iBAAO,IAAI,iBAAiB,IAAI;AAAA,QAElC,KAAK,KAAK;AACR,iBAAO,IAAI,UAAU,IAAI;AAAA,QAE3B,KAAK,KAAK;AACR,iBAAO,IAAI,SAAS,IAAI;AAAA,MAC5B;AAAA,IACF;AAiBA,YAAQ,YAAY,SAAS,UAAW,OAAO;AAC7C,aAAO,MAAM,OAAO,SAAU,KAAK,KAAK;AACtC,YAAI,OAAO,QAAQ,UAAU;AAC3B,cAAI,KAAK,mBAAmB,KAAK,IAAI,CAAC;AAAA,QACxC,WAAW,IAAI,MAAM;AACnB,cAAI,KAAK,mBAAmB,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,QACjD;AAEA,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP;AAUA,YAAQ,aAAa,SAAS,WAAY,MAAM,SAAS;AACvD,YAAM,OAAO,sBAAsB,MAAM,MAAM,mBAAmB,CAAC;AAEnE,YAAM,QAAQ,WAAW,IAAI;AAC7B,YAAM,QAAQ,WAAW,OAAO,OAAO;AACvC,YAAM,OAAO,SAAS,UAAU,MAAM,KAAK,SAAS,KAAK;AAEzD,YAAM,gBAAgB,CAAC;AACvB,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACxC,sBAAc,KAAK,MAAM,MAAM,KAAK,CAAC,CAAC,EAAE,IAAI;AAAA,MAC9C;AAEA,aAAO,QAAQ,UAAU,cAAc,aAAa,CAAC;AAAA,IACvD;AAYA,YAAQ,WAAW,SAAS,SAAU,MAAM;AAC1C,aAAO,QAAQ;AAAA,QACb,sBAAsB,MAAM,MAAM,mBAAmB,CAAC;AAAA,MACxD;AAAA,IACF;AAAA;AAAA;;;ACzUA;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,UAAU;AAChB,QAAM,YAAY;AAClB,QAAM,YAAY;AAClB,QAAM,mBAAmB;AACzB,QAAM,gBAAgB;AACtB,QAAM,cAAc;AACpB,QAAM,SAAS;AACf,QAAM,qBAAqB;AAC3B,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,OAAO;AACb,QAAM,WAAW;AAkCjB,aAAS,mBAAoB,QAAQ,SAAS;AAC5C,YAAM,OAAO,OAAO;AACpB,YAAM,MAAM,cAAc,aAAa,OAAO;AAE9C,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAM,MAAM,IAAI,CAAC,EAAE,CAAC;AACpB,cAAM,MAAM,IAAI,CAAC,EAAE,CAAC;AAEpB,iBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC5B,cAAI,MAAM,KAAK,MAAM,QAAQ,MAAM,EAAG;AAEtC,mBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC5B,gBAAI,MAAM,KAAK,MAAM,QAAQ,MAAM,EAAG;AAEtC,gBAAK,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,MACxC,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,MACtC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAI;AACxC,qBAAO,IAAI,MAAM,GAAG,MAAM,GAAG,MAAM,IAAI;AAAA,YACzC,OAAO;AACL,qBAAO,IAAI,MAAM,GAAG,MAAM,GAAG,OAAO,IAAI;AAAA,YAC1C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AASA,aAAS,mBAAoB,QAAQ;AACnC,YAAM,OAAO,OAAO;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK;AACjC,cAAM,QAAQ,IAAI,MAAM;AACxB,eAAO,IAAI,GAAG,GAAG,OAAO,IAAI;AAC5B,eAAO,IAAI,GAAG,GAAG,OAAO,IAAI;AAAA,MAC9B;AAAA,IACF;AAUA,aAAS,sBAAuB,QAAQ,SAAS;AAC/C,YAAM,MAAM,iBAAiB,aAAa,OAAO;AAEjD,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAM,MAAM,IAAI,CAAC,EAAE,CAAC;AACpB,cAAM,MAAM,IAAI,CAAC,EAAE,CAAC;AAEpB,iBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC5B,mBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC5B,gBAAI,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,KAC1C,MAAM,KAAK,MAAM,GAAI;AACtB,qBAAO,IAAI,MAAM,GAAG,MAAM,GAAG,MAAM,IAAI;AAAA,YACzC,OAAO;AACL,qBAAO,IAAI,MAAM,GAAG,MAAM,GAAG,OAAO,IAAI;AAAA,YAC1C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAQA,aAAS,iBAAkB,QAAQ,SAAS;AAC1C,YAAM,OAAO,OAAO;AACpB,YAAM,OAAO,QAAQ,eAAe,OAAO;AAC3C,UAAI,KAAK,KAAK;AAEd,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAM,KAAK,MAAM,IAAI,CAAC;AACtB,cAAM,IAAI,IAAI,OAAO,IAAI;AACzB,eAAQ,QAAQ,IAAK,OAAO;AAE5B,eAAO,IAAI,KAAK,KAAK,KAAK,IAAI;AAC9B,eAAO,IAAI,KAAK,KAAK,KAAK,IAAI;AAAA,MAChC;AAAA,IACF;AASA,aAAS,gBAAiB,QAAQ,sBAAsB,aAAa;AACnE,YAAM,OAAO,OAAO;AACpB,YAAM,OAAO,WAAW,eAAe,sBAAsB,WAAW;AACxE,UAAI,GAAG;AAEP,WAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,eAAQ,QAAQ,IAAK,OAAO;AAG5B,YAAI,IAAI,GAAG;AACT,iBAAO,IAAI,GAAG,GAAG,KAAK,IAAI;AAAA,QAC5B,WAAW,IAAI,GAAG;AAChB,iBAAO,IAAI,IAAI,GAAG,GAAG,KAAK,IAAI;AAAA,QAChC,OAAO;AACL,iBAAO,IAAI,OAAO,KAAK,GAAG,GAAG,KAAK,IAAI;AAAA,QACxC;AAGA,YAAI,IAAI,GAAG;AACT,iBAAO,IAAI,GAAG,OAAO,IAAI,GAAG,KAAK,IAAI;AAAA,QACvC,WAAW,IAAI,GAAG;AAChB,iBAAO,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI;AAAA,QACzC,OAAO;AACL,iBAAO,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI;AAAA,QACrC;AAAA,MACF;AAGA,aAAO,IAAI,OAAO,GAAG,GAAG,GAAG,IAAI;AAAA,IACjC;AAQA,aAAS,UAAW,QAAQ,MAAM;AAChC,YAAM,OAAO,OAAO;AACpB,UAAI,MAAM;AACV,UAAI,MAAM,OAAO;AACjB,UAAI,WAAW;AACf,UAAI,YAAY;AAEhB,eAAS,MAAM,OAAO,GAAG,MAAM,GAAG,OAAO,GAAG;AAC1C,YAAI,QAAQ,EAAG;AAEf,eAAO,MAAM;AACX,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAI,CAAC,OAAO,WAAW,KAAK,MAAM,CAAC,GAAG;AACpC,kBAAI,OAAO;AAEX,kBAAI,YAAY,KAAK,QAAQ;AAC3B,wBAAU,KAAK,SAAS,MAAM,WAAY,OAAO;AAAA,cACnD;AAEA,qBAAO,IAAI,KAAK,MAAM,GAAG,IAAI;AAC7B;AAEA,kBAAI,aAAa,IAAI;AACnB;AACA,2BAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAEA,iBAAO;AAEP,cAAI,MAAM,KAAK,QAAQ,KAAK;AAC1B,mBAAO;AACP,kBAAM,CAAC;AACP;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAUA,aAAS,WAAY,SAAS,sBAAsB,UAAU;AAE5D,YAAM,SAAS,IAAI,UAAU;AAE7B,eAAS,QAAQ,SAAU,MAAM;AAE/B,eAAO,IAAI,KAAK,KAAK,KAAK,CAAC;AAS3B,eAAO,IAAI,KAAK,UAAU,GAAG,KAAK,sBAAsB,KAAK,MAAM,OAAO,CAAC;AAG3E,aAAK,MAAM,MAAM;AAAA,MACnB,CAAC;AAGD,YAAM,iBAAiB,MAAM,wBAAwB,OAAO;AAC5D,YAAM,mBAAmB,OAAO,uBAAuB,SAAS,oBAAoB;AACpF,YAAM,0BAA0B,iBAAiB,oBAAoB;AAOrE,UAAI,OAAO,gBAAgB,IAAI,KAAK,wBAAwB;AAC1D,eAAO,IAAI,GAAG,CAAC;AAAA,MACjB;AAOA,aAAO,OAAO,gBAAgB,IAAI,MAAM,GAAG;AACzC,eAAO,OAAO,CAAC;AAAA,MACjB;AAMA,YAAM,iBAAiB,yBAAyB,OAAO,gBAAgB,KAAK;AAC5E,eAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,eAAO,IAAI,IAAI,IAAI,KAAO,KAAM,CAAC;AAAA,MACnC;AAEA,aAAO,gBAAgB,QAAQ,SAAS,oBAAoB;AAAA,IAC9D;AAWA,aAAS,gBAAiB,WAAW,SAAS,sBAAsB;AAElE,YAAM,iBAAiB,MAAM,wBAAwB,OAAO;AAG5D,YAAM,mBAAmB,OAAO,uBAAuB,SAAS,oBAAoB;AAGpF,YAAM,qBAAqB,iBAAiB;AAG5C,YAAM,gBAAgB,OAAO,eAAe,SAAS,oBAAoB;AAGzE,YAAM,iBAAiB,iBAAiB;AACxC,YAAM,iBAAiB,gBAAgB;AAEvC,YAAM,yBAAyB,KAAK,MAAM,iBAAiB,aAAa;AAExE,YAAM,wBAAwB,KAAK,MAAM,qBAAqB,aAAa;AAC3E,YAAM,wBAAwB,wBAAwB;AAGtD,YAAM,UAAU,yBAAyB;AAGzC,YAAM,KAAK,IAAI,mBAAmB,OAAO;AAEzC,UAAI,SAAS;AACb,YAAM,SAAS,IAAI,MAAM,aAAa;AACtC,YAAM,SAAS,IAAI,MAAM,aAAa;AACtC,UAAI,cAAc;AAClB,YAAM,SAAS,IAAI,WAAW,UAAU,MAAM;AAG9C,eAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,cAAM,WAAW,IAAI,iBAAiB,wBAAwB;AAG9D,eAAO,CAAC,IAAI,OAAO,MAAM,QAAQ,SAAS,QAAQ;AAGlD,eAAO,CAAC,IAAI,GAAG,OAAO,OAAO,CAAC,CAAC;AAE/B,kBAAU;AACV,sBAAc,KAAK,IAAI,aAAa,QAAQ;AAAA,MAC9C;AAIA,YAAM,OAAO,IAAI,WAAW,cAAc;AAC1C,UAAI,QAAQ;AACZ,UAAI,GAAG;AAGP,WAAK,IAAI,GAAG,IAAI,aAAa,KAAK;AAChC,aAAK,IAAI,GAAG,IAAI,eAAe,KAAK;AAClC,cAAI,IAAI,OAAO,CAAC,EAAE,QAAQ;AACxB,iBAAK,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAGA,WAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC5B,aAAK,IAAI,GAAG,IAAI,eAAe,KAAK;AAClC,eAAK,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,QAC7B;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,aAAS,aAAc,MAAM,SAAS,sBAAsB,aAAa;AACvE,UAAI;AAEJ,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,mBAAW,SAAS,UAAU,IAAI;AAAA,MACpC,WAAW,OAAO,SAAS,UAAU;AACnC,YAAI,mBAAmB;AAEvB,YAAI,CAAC,kBAAkB;AACrB,gBAAM,cAAc,SAAS,SAAS,IAAI;AAG1C,6BAAmB,QAAQ,sBAAsB,aAAa,oBAAoB;AAAA,QACpF;AAIA,mBAAW,SAAS,WAAW,MAAM,oBAAoB,EAAE;AAAA,MAC7D,OAAO;AACL,cAAM,IAAI,MAAM,cAAc;AAAA,MAChC;AAGA,YAAM,cAAc,QAAQ,sBAAsB,UAAU,oBAAoB;AAGhF,UAAI,CAAC,aAAa;AAChB,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAGA,UAAI,CAAC,SAAS;AACZ,kBAAU;AAAA,MAGZ,WAAW,UAAU,aAAa;AAChC,cAAM,IAAI;AAAA,UAAM,0HAE0C,cAAc;AAAA,QACxE;AAAA,MACF;AAEA,YAAM,WAAW,WAAW,SAAS,sBAAsB,QAAQ;AAGnE,YAAM,cAAc,MAAM,cAAc,OAAO;AAC/C,YAAM,UAAU,IAAI,UAAU,WAAW;AAGzC,yBAAmB,SAAS,OAAO;AACnC,yBAAmB,OAAO;AAC1B,4BAAsB,SAAS,OAAO;AAMtC,sBAAgB,SAAS,sBAAsB,CAAC;AAEhD,UAAI,WAAW,GAAG;AAChB,yBAAiB,SAAS,OAAO;AAAA,MACnC;AAGA,gBAAU,SAAS,QAAQ;AAE3B,UAAI,MAAM,WAAW,GAAG;AAEtB,sBAAc,YAAY;AAAA,UAAY;AAAA,UACpC,gBAAgB,KAAK,MAAM,SAAS,oBAAoB;AAAA,QAAC;AAAA,MAC7D;AAGA,kBAAY,UAAU,aAAa,OAAO;AAG1C,sBAAgB,SAAS,sBAAsB,WAAW;AAE1D,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAWA,YAAQ,SAAS,SAAS,OAAQ,MAAM,SAAS;AAC/C,UAAI,OAAO,SAAS,eAAe,SAAS,IAAI;AAC9C,cAAM,IAAI,MAAM,eAAe;AAAA,MACjC;AAEA,UAAI,uBAAuB,QAAQ;AACnC,UAAI;AACJ,UAAI;AAEJ,UAAI,OAAO,YAAY,aAAa;AAElC,+BAAuB,QAAQ,KAAK,QAAQ,sBAAsB,QAAQ,CAAC;AAC3E,kBAAU,QAAQ,KAAK,QAAQ,OAAO;AACtC,eAAO,YAAY,KAAK,QAAQ,WAAW;AAE3C,YAAI,QAAQ,YAAY;AACtB,gBAAM,kBAAkB,QAAQ,UAAU;AAAA,QAC5C;AAAA,MACF;AAEA,aAAO,aAAa,MAAM,SAAS,sBAAsB,IAAI;AAAA,IAC/D;AAAA;AAAA;;;AC9eA,IAAAC,iBAAA;AAAA;AAAA,aAAS,SAAU,KAAK;AACtB,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,SAAS;AAAA,MACrB;AAEA,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AAEA,UAAI,UAAU,IAAI,MAAM,EAAE,QAAQ,KAAK,EAAE,EAAE,MAAM,EAAE;AACnD,UAAI,QAAQ,SAAS,KAAK,QAAQ,WAAW,KAAK,QAAQ,SAAS,GAAG;AACpE,cAAM,IAAI,MAAM,wBAAwB,GAAG;AAAA,MAC7C;AAGA,UAAI,QAAQ,WAAW,KAAK,QAAQ,WAAW,GAAG;AAChD,kBAAU,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,QAAQ,IAAI,SAAU,GAAG;AAClE,iBAAO,CAAC,GAAG,CAAC;AAAA,QACd,CAAC,CAAC;AAAA,MACJ;AAGA,UAAI,QAAQ,WAAW,EAAG,SAAQ,KAAK,KAAK,GAAG;AAE/C,YAAM,WAAW,SAAS,QAAQ,KAAK,EAAE,GAAG,EAAE;AAE9C,aAAO;AAAA,QACL,GAAI,YAAY,KAAM;AAAA,QACtB,GAAI,YAAY,KAAM;AAAA,QACtB,GAAI,YAAY,IAAK;AAAA,QACrB,GAAG,WAAW;AAAA,QACd,KAAK,MAAM,QAAQ,MAAM,GAAG,CAAC,EAAE,KAAK,EAAE;AAAA,MACxC;AAAA,IACF;AAEA,YAAQ,aAAa,SAAS,WAAY,SAAS;AACjD,UAAI,CAAC,QAAS,WAAU,CAAC;AACzB,UAAI,CAAC,QAAQ,MAAO,SAAQ,QAAQ,CAAC;AAErC,YAAM,SAAS,OAAO,QAAQ,WAAW,eACvC,QAAQ,WAAW,QACnB,QAAQ,SAAS,IACf,IACA,QAAQ;AAEZ,YAAM,QAAQ,QAAQ,SAAS,QAAQ,SAAS,KAAK,QAAQ,QAAQ;AACrE,YAAM,QAAQ,QAAQ,SAAS;AAE/B,aAAO;AAAA,QACL;AAAA,QACA,OAAO,QAAQ,IAAI;AAAA,QACnB;AAAA,QACA,OAAO;AAAA,UACL,MAAM,SAAS,QAAQ,MAAM,QAAQ,WAAW;AAAA,UAChD,OAAO,SAAS,QAAQ,MAAM,SAAS,WAAW;AAAA,QACpD;AAAA,QACA,MAAM,QAAQ;AAAA,QACd,cAAc,QAAQ,gBAAgB,CAAC;AAAA,MACzC;AAAA,IACF;AAEA,YAAQ,WAAW,SAAS,SAAU,QAAQ,MAAM;AAClD,aAAO,KAAK,SAAS,KAAK,SAAS,SAAS,KAAK,SAAS,IACtD,KAAK,SAAS,SAAS,KAAK,SAAS,KACrC,KAAK;AAAA,IACX;AAEA,YAAQ,gBAAgB,SAAS,cAAe,QAAQ,MAAM;AAC5D,YAAM,QAAQ,QAAQ,SAAS,QAAQ,IAAI;AAC3C,aAAO,KAAK,OAAO,SAAS,KAAK,SAAS,KAAK,KAAK;AAAA,IACtD;AAEA,YAAQ,gBAAgB,SAAS,cAAe,SAAS,IAAI,MAAM;AACjE,YAAM,OAAO,GAAG,QAAQ;AACxB,YAAM,OAAO,GAAG,QAAQ;AACxB,YAAM,QAAQ,QAAQ,SAAS,MAAM,IAAI;AACzC,YAAM,aAAa,KAAK,OAAO,OAAO,KAAK,SAAS,KAAK,KAAK;AAC9D,YAAM,eAAe,KAAK,SAAS;AACnC,YAAM,UAAU,CAAC,KAAK,MAAM,OAAO,KAAK,MAAM,IAAI;AAElD,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,cAAI,UAAU,IAAI,aAAa,KAAK;AACpC,cAAI,UAAU,KAAK,MAAM;AAEzB,cAAI,KAAK,gBAAgB,KAAK,gBAC5B,IAAI,aAAa,gBAAgB,IAAI,aAAa,cAAc;AAChE,kBAAM,OAAO,KAAK,OAAO,IAAI,gBAAgB,KAAK;AAClD,kBAAM,OAAO,KAAK,OAAO,IAAI,gBAAgB,KAAK;AAClD,sBAAU,QAAQ,KAAK,OAAO,OAAO,IAAI,IAAI,IAAI,CAAC;AAAA,UACpD;AAEA,kBAAQ,QAAQ,IAAI,QAAQ;AAC5B,kBAAQ,QAAQ,IAAI,QAAQ;AAC5B,kBAAQ,QAAQ,IAAI,QAAQ;AAC5B,kBAAQ,MAAM,IAAI,QAAQ;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AClGA;AAAA;AAAA,QAAM,QAAQ;AAEd,aAAS,YAAa,KAAK,QAAQ,MAAM;AACvC,UAAI,UAAU,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAE/C,UAAI,CAAC,OAAO,MAAO,QAAO,QAAQ,CAAC;AACnC,aAAO,SAAS;AAChB,aAAO,QAAQ;AACf,aAAO,MAAM,SAAS,OAAO;AAC7B,aAAO,MAAM,QAAQ,OAAO;AAAA,IAC9B;AAEA,aAAS,mBAAoB;AAC3B,UAAI;AACF,eAAO,SAAS,cAAc,QAAQ;AAAA,MACxC,SAAS,GAAG;AACV,cAAM,IAAI,MAAM,sCAAsC;AAAA,MACxD;AAAA,IACF;AAEA,YAAQ,SAAS,SAAS,OAAQ,QAAQ,QAAQ,SAAS;AACzD,UAAI,OAAO;AACX,UAAI,WAAW;AAEf,UAAI,OAAO,SAAS,gBAAgB,CAAC,UAAU,CAAC,OAAO,aAAa;AAClE,eAAO;AACP,iBAAS;AAAA,MACX;AAEA,UAAI,CAAC,QAAQ;AACX,mBAAW,iBAAiB;AAAA,MAC9B;AAEA,aAAO,MAAM,WAAW,IAAI;AAC5B,YAAM,OAAO,MAAM,cAAc,OAAO,QAAQ,MAAM,IAAI;AAE1D,YAAM,MAAM,SAAS,WAAW,IAAI;AACpC,YAAM,QAAQ,IAAI,gBAAgB,MAAM,IAAI;AAC5C,YAAM,cAAc,MAAM,MAAM,QAAQ,IAAI;AAE5C,kBAAY,KAAK,UAAU,IAAI;AAC/B,UAAI,aAAa,OAAO,GAAG,CAAC;AAE5B,aAAO;AAAA,IACT;AAEA,YAAQ,kBAAkB,SAAS,gBAAiB,QAAQ,QAAQ,SAAS;AAC3E,UAAI,OAAO;AAEX,UAAI,OAAO,SAAS,gBAAgB,CAAC,UAAU,CAAC,OAAO,aAAa;AAClE,eAAO;AACP,iBAAS;AAAA,MACX;AAEA,UAAI,CAAC,KAAM,QAAO,CAAC;AAEnB,YAAM,WAAW,QAAQ,OAAO,QAAQ,QAAQ,IAAI;AAEpD,YAAM,OAAO,KAAK,QAAQ;AAC1B,YAAM,eAAe,KAAK,gBAAgB,CAAC;AAE3C,aAAO,SAAS,UAAU,MAAM,aAAa,OAAO;AAAA,IACtD;AAAA;AAAA;;;AC9DA;AAAA;AAAA,QAAM,QAAQ;AAEd,aAAS,eAAgB,OAAO,QAAQ;AACtC,YAAM,QAAQ,MAAM,IAAI;AACxB,YAAM,MAAM,SAAS,OAAO,MAAM,MAAM;AAExC,aAAO,QAAQ,IACX,MAAM,MAAM,SAAS,eAAe,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,IAAI,MAChE;AAAA,IACN;AAEA,aAAS,OAAQ,KAAK,GAAG,GAAG;AAC1B,UAAI,MAAM,MAAM;AAChB,UAAI,OAAO,MAAM,YAAa,QAAO,MAAM;AAE3C,aAAO;AAAA,IACT;AAEA,aAAS,SAAU,MAAM,MAAM,QAAQ;AACrC,UAAI,OAAO;AACX,UAAI,SAAS;AACb,UAAI,SAAS;AACb,UAAI,aAAa;AAEjB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,MAAM,KAAK,MAAM,IAAI,IAAI;AAC/B,cAAM,MAAM,KAAK,MAAM,IAAI,IAAI;AAE/B,YAAI,CAAC,OAAO,CAAC,OAAQ,UAAS;AAE9B,YAAI,KAAK,CAAC,GAAG;AACX;AAEA,cAAI,EAAE,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC,IAAI;AACtC,oBAAQ,SACJ,OAAO,KAAK,MAAM,QAAQ,MAAM,MAAM,MAAM,IAC5C,OAAO,KAAK,QAAQ,CAAC;AAEzB,qBAAS;AACT,qBAAS;AAAA,UACX;AAEA,cAAI,EAAE,MAAM,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI;AACpC,oBAAQ,OAAO,KAAK,UAAU;AAC9B,yBAAa;AAAA,UACf;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,SAAS,SAAS,OAAQ,QAAQ,SAAS,IAAI;AACrD,YAAM,OAAO,MAAM,WAAW,OAAO;AACrC,YAAM,OAAO,OAAO,QAAQ;AAC5B,YAAM,OAAO,OAAO,QAAQ;AAC5B,YAAM,aAAa,OAAO,KAAK,SAAS;AAExC,YAAM,KAAK,CAAC,KAAK,MAAM,MAAM,IACzB,KACA,WAAW,eAAe,KAAK,MAAM,OAAO,MAAM,IAClD,cAAc,aAAa,MAAM,aAAa;AAElD,YAAM,OACJ,WAAW,eAAe,KAAK,MAAM,MAAM,QAAQ,IACnD,SAAS,SAAS,MAAM,MAAM,KAAK,MAAM,IAAI;AAE/C,YAAM,UAAU,kBAAuB,aAAa,MAAM,aAAa;AAEvE,YAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,eAAe,KAAK,QAAQ;AAEtF,YAAM,SAAS,6CAA6C,QAAQ,UAAU,mCAAmC,KAAK,OAAO;AAE7H,UAAI,OAAO,OAAO,YAAY;AAC5B,WAAG,MAAM,MAAM;AAAA,MACjB;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AChFA;AAAA;AACA,QAAM,aAAa;AAEnB,QAAM,SAAS;AACf,QAAM,iBAAiB;AACvB,QAAM,cAAc;AAEpB,aAAS,aAAc,YAAY,QAAQ,MAAM,MAAM,IAAI;AACzD,YAAM,OAAO,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AACvC,YAAM,UAAU,KAAK;AACrB,YAAM,cAAc,OAAO,KAAK,UAAU,CAAC,MAAM;AAEjD,UAAI,CAAC,eAAe,CAAC,WAAW,GAAG;AACjC,cAAM,IAAI,MAAM,oCAAoC;AAAA,MACtD;AAEA,UAAI,aAAa;AACf,YAAI,UAAU,GAAG;AACf,gBAAM,IAAI,MAAM,4BAA4B;AAAA,QAC9C;AAEA,YAAI,YAAY,GAAG;AACjB,eAAK;AACL,iBAAO;AACP,mBAAS,OAAO;AAAA,QAClB,WAAW,YAAY,GAAG;AACxB,cAAI,OAAO,cAAc,OAAO,OAAO,aAAa;AAClD,iBAAK;AACL,mBAAO;AAAA,UACT,OAAO;AACL,iBAAK;AACL,mBAAO;AACP,mBAAO;AACP,qBAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,UAAU,GAAG;AACf,gBAAM,IAAI,MAAM,4BAA4B;AAAA,QAC9C;AAEA,YAAI,YAAY,GAAG;AACjB,iBAAO;AACP,mBAAS,OAAO;AAAA,QAClB,WAAW,YAAY,KAAK,CAAC,OAAO,YAAY;AAC9C,iBAAO;AACP,iBAAO;AACP,mBAAS;AAAA,QACX;AAEA,eAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,cAAI;AACF,kBAAM,OAAO,OAAO,OAAO,MAAM,IAAI;AACrC,oBAAQ,WAAW,MAAM,QAAQ,IAAI,CAAC;AAAA,UACxC,SAAS,GAAG;AACV,mBAAO,CAAC;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH;AAEA,UAAI;AACF,cAAM,OAAO,OAAO,OAAO,MAAM,IAAI;AACrC,WAAG,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAC;AAAA,MACzC,SAAS,GAAG;AACV,WAAG,CAAC;AAAA,MACN;AAAA,IACF;AAEA,YAAQ,SAAS,OAAO;AACxB,YAAQ,WAAW,aAAa,KAAK,MAAM,eAAe,MAAM;AAChE,YAAQ,YAAY,aAAa,KAAK,MAAM,eAAe,eAAe;AAG1E,YAAQ,WAAW,aAAa,KAAK,MAAM,SAAU,MAAM,GAAG,MAAM;AAClE,aAAO,YAAY,OAAO,MAAM,IAAI;AAAA,IACtC,CAAC;AAAA;AAAA;;;ACzED,IAAA,iBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACWR,IAAM,iBAAN,MAAMC,wBAAuB,WAAU;EAAvC,cAAA;;AAIc,SAAA,OAA+C;AAE/C,SAAA,OAAO;AAMU,SAAA,YAAY;AAE7B,SAAA,YAAsB;EA8C3C;EA3CkB,SAAM;AACpB,QAAI,eAAiC;AACrC,QAAI,KAAK,SAAS,MAAM;AACtB,qBAAe;IACjB,WAAW,KAAK,SAAS,MAAM;AAC7B,qBAAe;IACjB,OAAO;AACL,qBAAe;IACjB;AACA,SAAK,MAAM,UAAU;wDAC+B,YAAY;mDACjB,KAAK,IAAI;;AAGxD,QAAI,KAAK,YAAY;AACnB,WAAK,QAAQ,YAAY,IAAI,KAAK;IACpC;AAEA,WAAO;+DACoD,KAAK,eAAc,CAAE;;EAElF;EAGQ,iBAAc;AACpB,QAAI,KAAK,UAAU;AACjB,aAAO,sBAAsB,KAAK,QAAQ,QAAQ,KAAK,IAAI;IAC7D,WAAW,KAAK,YAAY;AAC1B,aAAO;;;;eAIE,KAAK,UAAU;;IAE1B;AAEA,WAAO;yBACc,KAAK,IAAI;;;;;EAKhC;;AA1DuB,eAAA,SAAS,CAAC,eAAe,aAAa,cAAM;AAGhD,WAAA;EAAlB,SAAQ;;AAEU,WAAA;EAAlB,SAAQ;;AAEU,WAAA;EAAlB,SAAQ;;AAEU,WAAA;EAAlB,SAAQ;;AAE2B,WAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAER,WAAA;EAAlB,SAAQ;;AAdE,iBAAc,WAAA;EAD1B,cAAc,kBAAkB;GACpB,cAAc;;;ACX3B,IAAAC,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACUf,IAAM,eAAe;AAGd,IAAM,qBAAN,MAAMC,4BAA2B,WAAU;EAA3C,cAAA;;AAI6B,SAAA,eAA+B,CAAA;EAiCnE;EA9BkB,SAAM;AACpB,UAAM,iBAAiB,KAAK,aAAa,SAAS;AAElD,WAAO,OAAO,KAAK,aACd,MAAM,GAAG,YAAY,EACrB,IACC,CAAC,EAAE,KAAK,WAAU,MAAO;;;yBAGV,GAAG;qBACP,UAAU,UAAU,CAAC;;WAE/B,CACF;QACD,iBACE,CAAC,GAAG,MAAM,eAAe,KAAK,aAAa,MAAM,CAAC,EAAE,IAClD,MAAM,mEAAmE,IAE3E,IAAI;;;;;;;;;;;EAWZ;;AAnCuB,mBAAA,SAAS,CAAC,aAAaC,eAAM;AAGlBC,YAAA;EAAjC,SAAS,EAAE,MAAM,MAAK,CAAE;;AAJd,qBAAkBA,YAAA;EAD9B,cAAc,uBAAuB;GACzB,kBAAkB;;;ACb/B,IAAAC,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACcR,IAAM,gBAAN,MAAMC,uBAAsB,WAAU;EAAtC,cAAA;;AAI6B,SAAA,eAAgC,CAAA;AAE/C,SAAA,WAAY;AAEZ,SAAA,OAAO;AAUP,SAAA,SAAkB;AAED,SAAA,YAAY;AAEZ,SAAA,WAAW;AAEX,SAAA,iBAAiB;AAEjB,SAAA,UAAU;AAEX,SAAA,sBAAsB;EAqD3D;EAlDkB,SAAM;AACpB,WAAO;0BACe,KAAK,QAAQ,aAAa,UAAU,KAAK,MAAM,CAAC;UAChE,KAAK,mBAAkB,CAAE,IAAI,KAAK,oBAAmB,CAAE;4DACL,KAAK,IAAI;UAC3D,KAAK,eAAc,CAAE;;;EAG7B;EAGQ,qBAAkB;AACxB,QAAI,KAAK,kBAAkB,KAAK,UAAU;AACxC,aAAO,0CAA0C,KAAK,QAAQ;IAChE,WAAW,KAAK,kBAAkB,KAAK,YAAY;AACjD,aAAO,sCAAsC,KAAK,UAAU;IAC9D;AAEA,WAAO;EACT;EAEQ,sBAAmB;AACzB,QAAI,CAAC,KAAK,kBAAkB,KAAK,UAAU;AACzC,aAAO;;mBAEM,KAAK,QAAQ;eACjB,KAAK,IAAI;qBACH,KAAK,SAAS;;IAE/B,WAAW,CAAC,KAAK,kBAAkB,CAAC,KAAK,UAAU;AACjD,aAAO,wCAAwC,KAAK,IAAI;IAC1D;AAEA,WAAO;EACT;EAEQ,iBAAc;AACpB,QAAI,KAAK,SAAS;AAChB,aAAO;;gBAEG,KAAK,mBAAmB;;IAEpC,WAAW,KAAK,YAAY,KAAK,YAAY;AAC3C,aAAO,wBAAwB,KAAK,UAAU,IAAI,KAAK,QAAQ;IACjE,WAAW,KAAK,MAAM;AACpB,aAAO,gDAAgD,KAAK,IAAI;IAClE;AAEA,WAAO;EACT;;AA/EuB,cAAA,SAAS,CAAC,aAAa,eAAeC,eAAM;AAGjCC,YAAA;EAAjC,SAAS,EAAE,MAAM,MAAK,CAAE;;AAENA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAE2BA,YAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAESA,YAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAESA,YAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAESA,YAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAEQA,YAAA;EAAlC,SAAS,EAAE,MAAM,OAAM,CAAE;;AA5Bf,gBAAaA,YAAA;EADzB,cAAc,iBAAiB;GACnB,aAAa;;;;;;;;;ACAnB,IAAM,sBAAN,MAAMC,6BAA4B,WAAU;EAejD,cAAA;AACE,UAAK;AAdC,SAAA,cAA8B,CAAA;AAGnB,SAAA,SAAkB;AAEpB,SAAA,aAAa,oBAAoB,MAAM;AAEvC,SAAA,QAAQ,cAAc,MAAM;AAE5B,SAAA,gBAAgB,cAAc,MAAM,gBAAgB;AAEpD,SAAA,+BAA+B,cAAc,MAAM;AAIlE,SAAK,YAAY,KACf,oBAAoB,aAAa,cAAc,SAAQ,KAAK,aAAa,GAAI,GAC7E,cAAc,aAAa,SAAS,SAAQ,KAAK,QAAQ,GAAI,GAC7D,cAAc,aAAa,mBAAmB,SAAQ,KAAK,gBAAgB,IAAI,MAAO,GACtF,cAAc,aACZ,gCACA,SAAQ,KAAK,+BAA+B,GAAI,CACjD;EAEL;EAEgB,uBAAoB;AAClC,SAAK,YAAY,QAAQ,iBAAe,YAAW,CAAE;EACvD;EAGgB,SAAM;AACpB,UAAM,cAAc,KAAK,WAAW,KAAK,OAAK,EAAE,OAAO,eAAe;AACtE,UAAM,EAAE,WAAU,IAAK,kBAAkB;AAEzC,QAAI,CAAC,eAAe,eAAe,QAAQ;AACzC,aAAO;IACT;AAEA,QAAI,eAAe,iBAAiB,CAAC,eAAe,SAAQ,GAAI;AAC9D,aAAO;IACT;AAEA,UAAM,gBAAgB,cAAc,MAAM,SAAS;AACnD,UAAM,WAAW,KAAK,QAAQ;AAC9B,UAAM,eAAe,WAAW,KAAK,WAAW,KAAK,MAAM,WAAW,EAAE,IAAI;AAE5E,UAAM,QAAQ,KAAK,gBAAgB,IAAI,KAAK,gBAAgB;AAC5D,QAAI,WAAW,GAAG,KAAK;AACvB,QAAI,KAAK,gBAAgB,GAAG;AAC1B,iBAAW,GAAG,KAAK,aAAa;IAClC,WAAW,QAAQ,UAAU;AAC3B,iBAAW,GAAG,KAAK;IACrB;AAEA,WAAO;;;;;iBAKM,KAAK,aAAa,KAAK,IAAI,CAAC;mBAC1B,QAAQ;;;iBAGV,UAAU,KAAK,MAAM,CAAC;mBACpB,KAAK,4BAA4B;8BACtB,KAAK,+BAA+B,WAAW,YAAY;;;EAGvF;EAGQ,eAAY;AAClB,qBAAiB,UAAU,EAAE,MAAM,SAAS,OAAO,oBAAmB,CAAE;AACxE,qBAAiB,KAAK,YAAY;EACpC;;AAxEmBC,YAAA;EAAlB,SAAQ;;AAEQA,YAAA;EAAhB,MAAK;;AAEWA,YAAA;EAAhB,MAAK;;AAEWA,YAAA;EAAhB,MAAK;;AAEWA,YAAA;EAAhB,MAAK;;AAbK,sBAAmBA,YAAA;EAD/B,cAAc,wBAAwB;GAC1B,mBAAmB;;;;;;;;;ACEzB,IAAM,4BAAN,MAAMC,mCAAkC,WAAU;EASvD,cAAA;AACE,UAAK;AARC,SAAA,cAA8B,CAAA;AAGnB,SAAA,SAAkB;AAEpB,SAAA,aAAa,oBAAoB,MAAM;AAItD,SAAK,YAAY,KACf,oBAAoB,aAAa,cAAc,SAAQ,KAAK,aAAa,GAAI,CAAC;EAElF;EAEgB,uBAAoB;AAClC,SAAK,YAAY,QAAQ,iBAAe,YAAW,CAAE;EACvD;EAGgB,SAAM;AACpB,UAAM,sBAAsB,KAAK,WAAW,OAAO,eAAa,UAAU,SAAS,WAAW;AAE9F,QAAI,EAAC,2DAAqB,SAAQ;AAChC,WAAK,MAAM,UAAU;AAErB,aAAO;IACT;AAEA,WAAO;;UAED,oBACC,OAAO,cAAc,aAAa,EAClC,IACC,eAAa;;2BAEE,UAAU,UAAU,kBAAkB,SAAS,CAAC,CAAC;uBACrD,UAAU,QAAQ,SAAS;yBACzB,MAAM,KAAK,YAAY,SAAS,CAAC;;;8BAG5B,mBAAmB,UAAU,EAAE,EAAE;6BAClC,IAAI;yBACR,UAAU,KAAK,MAAM,CAAC;;;aAGlC,CACF;;;EAGT;EAGQ,YAAY,WAAoB;AACtC,QAAI,UAAU,OAAO,iBAAiB;AACpC,UAAI,eAAe,SAAQ,GAAI;AAC7B,yBAAiB,KAAK,YAAY;MACpC,OAAO;AACL,yBAAiB,KAAK,yBAAyB;MACjD;IACF,OAAO;AACL,uBAAiB,KAAK,sBAAsB,EAAE,UAAS,CAAE;IAC3D;EACF;;AA3DmBC,YAAA;EAAlB,SAAQ;;AAEQA,YAAA;EAAhB,MAAK;;AAPK,4BAAyBA,YAAA;EADrC,cAAc,8BAA8B;GAChC,yBAAyB;;;;;;;;;ACC/B,IAAM,yBAAN,MAAMC,gCAA+B,WAAU;EAUpD,cAAA;AACE,UAAK;AATC,SAAA,cAA8B,CAAA;AAGnB,SAAA,SAAkB;AAEpB,SAAA,aAAa,oBAAoB,MAAM;AACvC,SAAA,UAAU;AAIzB,SAAK,YAAY,KACf,oBAAoB,aAAa,cAAc,SAAQ,KAAK,aAAa,GAAI,CAAC;AAEhF,QAAI,eAAe,WAAU,KAAM,eAAe,MAAK,GAAI;AACzD,WAAK,UAAU,CAAC,qBAAqB,MAAM;AAC3C,WAAK,YAAY,KACf,qBAAqB,aAAa,SAAS,SAAQ,KAAK,UAAU,CAAC,GAAI,CAAC;IAE5E;EACF;EAEgB,uBAAoB;AAClC,SAAK,YAAY,QAAQ,iBAAe,YAAW,CAAE;EACvD;EAGgB,SAAM;AACpB,UAAM,EAAE,cAAa,IAAK,kBAAkB;AAE5C,QAAI,EAAC,+CAAe,SAAQ;AAC1B,WAAK,MAAM,UAAU;AAErB,aAAO;IACT;AAEA,UAAM,UAAU,KAAK,0BAA0B,aAAa;AAE5D,WAAO;QACH,QAAQ,IACR,YAAU;;uBAEK,UAAU,UAAU,eAAe,MAAM,CAAC,CAAC;mBAC/C,OAAO,QAAQ,SAAS;qBACtB,MAAM,KAAK,gBAAgB,MAAM,CAAC;0BAC7B,mBAAmB,OAAO,EAAE,EAAE;qBACnC,UAAU,KAAK,MAAM,CAAC;uBACpB,KAAK,OAAO;;;SAG1B,CACF;;EAEL;EAGQ,0BAA0B,SAAmB;AACnD,UAAM,SAAS,YAAY,iBAAgB;AAE3C,UAAM,iBAAiB,KAAK,WACzB,IAAI,eAAU;;AAAG,6BAAU,SAAV,mBAAgB;KAAI,EACrC,OAAO,OAAO;AAEjB,UAAM,cAAc,OAAO,IAAI,YAAU,OAAO,IAAI,EAAE,OAAO,OAAO;AACpE,UAAM,WAAW,eAAe,OAAO,WAAW;AAClD,QAAI,SAAS,SAAS,oBAAoB,KAAK,eAAe,SAAQ,GAAI;AACxE,YAAM,QAAQ,SAAS,QAAQ,oBAAoB;AACnD,eAAS,KAAK,IAAI;IACpB;AACA,UAAM,WAAW,QAAQ,OAAO,YAAU,CAAC,SAAS,SAAS,OAAO,iCAAQ,IAAI,CAAC,CAAC;AAElF,WAAO;EACT;EAEQ,gBAAgB,QAAgB;AACtC,QAAI,KAAK,SAAS;AAChB;IACF;AACA,qBAAiB,KAAK,2BAA2B,EAAE,OAAM,CAAE;EAC7D;;AA3EmBC,YAAA;EAAlB,SAAQ;;AAEQA,YAAA;EAAhB,MAAK;;AACWA,YAAA;EAAhB,MAAK;;AARK,yBAAsBA,YAAA;EADlC,cAAc,2BAA2B;GAC7B,sBAAsB;;;;;;;;;ACL5B,IAAM,2BAAN,MAAMC,kCAAiC,WAAU;EAStD,cAAA;AACE,UAAK;AARC,SAAA,cAA8B,CAAA;AAGnB,SAAA,SAAkB;AAEpB,SAAA,aAAa,oBAAoB,MAAM;AAItD,SAAK,YAAY,KACf,oBAAoB,aAAa,cAAc,SAAQ,KAAK,aAAa,GAAI,CAAC;EAElF;EAEgB,uBAAoB;AAClC,SAAK,YAAY,QAAQ,iBAAe,YAAW,CAAE;EACvD;EAGgB,SAAM;AACpB,UAAM,qBAAqB,KAAK,WAAW,OAAO,eAAa,UAAU,SAAS,UAAU;AAC5F,UAAM,gCAAgC,mBAAmB,OAAO,cAAc,aAAa;AAC3F,UAAM,gCAAgC,8BAA8B,OAClE,eAAa,UAAU,OAAO,cAAc,aAAa,YAAY;AAGvE,QAAI,EAAC,+EAA+B,SAAQ;AAC1C,WAAK,MAAM,UAAU;AAErB,aAAO;IACT;AAEA,WAAO;;UAED,8BAA8B,IAC9B,eAAa;;yBAEE,UAAU,UAAU,kBAAkB,SAAS,CAAC,CAAC;2BAC/C,IAAI;qBACV,UAAU,QAAQ,SAAS;4BACpB,4BAA4B,UAAU,EAAE,EAAE;uBAC/C,MAAM,KAAK,YAAY,SAAS,CAAC;uBACjC,UAAU,KAAK,MAAM,CAAC;;;WAGlC,CACF;;;EAGP;EAGQ,YAAY,WAAoB;AACtC,qBAAiB,KAAK,sBAAsB,EAAE,UAAS,CAAE;EAC3D;;AAnDmBC,YAAA;EAAlB,SAAQ;;AAEQA,YAAA;EAAhB,MAAK;;AAPK,2BAAwBA,YAAA;EADpC,cAAc,6BAA6B;GAC/B,wBAAwB;;;;;;;;;ACH9B,IAAM,2BAAN,MAAMC,kCAAiC,WAAU;EAAjD,cAAA;;AAEc,SAAA,SAAkB;AAElB,SAAA,UAAsB,CAAA;EAgC3C;EA7BkB,SAAM;AACpB,QAAI,CAAC,KAAK,QAAQ,QAAQ;AACxB,WAAK,MAAM,UAAU;AAErB,aAAO;IACT;AAEA,WAAO;;UAED,KAAK,QAAQ,IACb,YAAU;;4BAEQ,4BAA4B,OAAO,EAAE,EAAE;yBAC1C,UAAU,UAAU,eAAe,MAAM,CAAC,CAAC;qBAC/C,OAAO,QAAQ,SAAS;uBACtB,MAAM,KAAK,gBAAgB,MAAM,CAAC;uBAClC,UAAU,KAAK,MAAM,CAAC;;;WAGlC,CACF;;;EAGP;EAGQ,gBAAgB,QAAgB;AACtC,wBAAoB,sBAAsB,MAAM;EAClD;;AAjCmBC,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAJE,2BAAwBA,YAAA;EADpC,cAAc,6BAA6B;GAC/B,wBAAwB;;;;;;;;;ACE9B,IAAM,2BAAN,MAAMC,kCAAiC,WAAU;EAAjD,cAAA;;AAEc,SAAA,SAAkB;AAElB,SAAA,aAAuC,CAAA;EAsC5D;EAnCkB,SAAM;AACpB,UAAM,qBAAqB,KAAK,WAAW,OAAO,cAAc,aAAa;AAE7E,QAAI,mBAAmB,WAAW,GAAG;AACnC,WAAK,MAAM,UAAU;AAErB,aAAO;IACT;AAEA,WAAO;;UAED,mBAAmB,IACnB,eAAa;;yBAEE,UAAU,UAAU,kBAAkB,SAAS,CAAC,CAAC;2BAC/C,IAAI;qBACV,UAAU,QAAQ,SAAS;;;4BAGpB,mBAAmB,UAAU,EAAE,EAAE;uBACtC,MAAM,KAAK,YAAY,SAAS,CAAC;uBACjC,UAAU,KAAK,MAAM,CAAC;;;WAGlC,CACF;;;EAGP;EAGQ,YAAY,WAAoB;AACtC,wBAAoB,mBAAmB,SAAS;AAChD,qBAAiB,KAAK,sBAAsB,EAAE,UAAS,CAAE;EAC3D;;AAvCmBC,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAJE,2BAAwBA,YAAA;EADpC,cAAc,6BAA6B;GAC/B,wBAAwB;;;;;;;;;ACF9B,IAAM,6BAAN,MAAMC,oCAAmC,WAAU;EASxD,cAAA;AACE,UAAK;AARC,SAAA,cAA8B,CAAA;AAGnB,SAAA,SAAkB;AAEpB,SAAA,aAAa,oBAAoB,MAAM;AAItD,SAAK,YAAY,KACf,oBAAoB,aAAa,cAAc,SAAQ,KAAK,aAAa,GAAI,CAAC;EAElF;EAEgB,uBAAoB;AAClC,SAAK,YAAY,QAAQ,iBAAe,YAAW,CAAE;EACvD;EAGgB,SAAM;AACpB,UAAM,uBAAuB,KAAK,WAAW,OAC3C,eAAa,UAAU,SAAS,iBAAiB,UAAU,SAAS,eAAe;AAGrF,QAAI,EAAC,6DAAsB,SAAQ;AACjC,WAAK,MAAM,UAAU;AAErB,aAAO;IACT;AAEA,WAAO;;UAED,qBAAqB,IACrB,eAAa;;yBAEE,UAAU,UAAU,kBAAkB,SAAS,CAAC,CAAC;2BAC/C,IAAI;qBACV,UAAU,QAAQ,SAAS;;;4BAGpB,mBAAmB,UAAU,EAAE,EAAE;uBACtC,MAAM,KAAK,YAAY,SAAS,CAAC;uBACjC,UAAU,KAAK,MAAM,CAAC;;;WAGlC,CACF;;;EAGP;EAGQ,YAAY,WAAoB;AACtC,wBAAoB,mBAAmB,SAAS;AAChD,qBAAiB,KAAK,sBAAsB;EAC9C;;AApDmBC,aAAA;EAAlB,SAAQ;;AAEQA,aAAA;EAAhB,MAAK;;AAPK,6BAA0BA,aAAA;EADtC,cAAc,gCAAgC;GAClC,0BAA0B;;;;;;;;;ACUhC,IAAM,yBAAN,MAAMC,gCAA+B,WAAU;EAYpD,cAAA;AACE,UAAK;AAXC,SAAA,cAA8B,CAAA;AAGnB,SAAA,SAAkB;AAEpB,SAAA,aAAa,oBAAoB,MAAM;AAEvC,SAAA,UAAU;AAKzB,SAAK,YAAY,KACf,oBAAoB,aAAa,cAAc,SAAQ,KAAK,aAAa,GAAI,CAAC;AAEhF,QAAI,eAAe,WAAU,KAAM,eAAe,MAAK,GAAI;AACzD,WAAK,UAAU,CAAC,qBAAqB,MAAM;AAC3C,WAAK,YAAY,KACf,qBAAqB,aAAa,SAAS,SAAQ,KAAK,UAAU,CAAC,GAAI,CAAC;IAE5E;EACF;EAGgB,SAAM;AACpB,UAAM,gBAAgB,YAAY,iBAAgB;AAElD,UAAM,wBAAwB,cAC3B,OAAO,YAAU,CAAC,WAAW,WAAW,MAAM,CAAC,EAC/C,OAAO,YAAU,CAAC,KAAK,mBAAmB,MAAM,CAAC,EACjD,OAAO,YAAU,KAAK,mCAAmC,MAAM,CAAC;AAEnE,QAAI,CAAC,sBAAsB,QAAQ;AACjC,WAAK,MAAM,UAAU;AAErB,aAAO;IACT;AAEA,WAAO;;UAED,sBAAsB,IACtB,YAAU;;yBAEK,UAAU,UAAU,eAAe,MAAM,CAAC,CAAC;qBAC/C,OAAO,QAAQ,SAAS;uBACtB,MAAM,KAAK,gBAAgB,MAAM,CAAC;;;uBAGlC,UAAU,KAAK,MAAM,CAAC;yBACpB,KAAK,OAAO;;;WAG1B,CACF;;;EAGP;EAGQ,gBAAgB,QAAgB;AACtC,QAAI,KAAK,SAAS;AAChB;IACF;AAEA,wBAAoB,sBAAsB,MAAM;EAClD;EAEQ,mBAAmB,QAAgB;AACzC,WAAO,KAAK,WAAW,KACrB,eAAa,UAAU,OAAO,OAAO,MAAM,UAAU,SAAS,OAAO,IAAI;EAE7E;EAEQ,mCAAmC,QAAgB;AACzD,UAAM,mBAAmB,gBAAgB,MAAM;AAE/C,QAAI,oBAAoB,OAAO,QAAQ;AACrC,aAAO,OAAO,OAAO,KAAK,OAAI;AAC5B,cAAM,iBAAiB,EAAE,MAAM,GAAG,EAAE,CAAC;AAErC,eAAO,qBAAqB;MAC9B,CAAC;IACH;AAEA,WAAO;EACT;;AAlFmBC,aAAA;EAAlB,SAAQ;;AAEQA,aAAA;EAAhB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AATK,yBAAsBA,aAAA;EADlC,cAAc,2BAA2B;GAC7B,sBAAsB;;;;;;;;;ACA5B,IAAM,8BAAN,MAAMC,qCAAoC,WAAU;EAWzD,cAAA;AACE,UAAK;AAVC,SAAA,cAA8B,CAAA;AAGnB,SAAA,SAAkB;AAElB,SAAA,UAAsB,CAAA;AAExB,SAAA,UAAU;AAIzB,QAAI,eAAe,WAAU,KAAM,eAAe,MAAK,GAAI;AACzD,WAAK,UAAU,CAAC,qBAAqB,MAAM;AAC3C,WAAK,YAAY,KACf,qBAAqB,aAAa,SAAS,SAAQ,KAAK,UAAU,CAAC,GAAI,CAAC;IAE5E;EACF;EAGgB,SAAM;AACpB,UAAM,EAAE,WAAU,IAAK,oBAAoB;AAC3C,UAAM,EAAE,eAAe,kBAAiB,IAAK,kBAAkB;AAC/D,UAAM,gBAAgB,YAAY,iBAAgB;AAElD,UAAM,cAAc,WAAW,KAAK,OAAK,EAAE,OAAO,eAAe;AACjE,UAAM,qBAAqB,WAAW,OACpC,OAAK,EAAE,SAAS,cAAc,EAAE,SAAS,eAAe,EAAE,SAAS,aAAa;AAElF,UAAM,kBAAkB,mBAAmB,OAAO,OAAK,EAAE,SAAS,gBAAgB;AAElF,QAAI,CAAC,aAAa;AAChB,aAAO;IACT;AAEA,QAAI,qBAAqB,iBAAiB,CAAC,KAAK,QAAQ,QAAQ;AAC9D,WAAK,MAAM,UAAU;AAErB,aAAO;IACT;AAEA,UAAM,iBAAiB,gBAAgB,SAAS,cAAc;AAC9D,UAAM,iBAAiB,KAAK,IAAI,GAAG,IAAI,cAAc;AACrD,UAAM,UAAU,WAAW,0BAA0B,KAAK,OAAO,EAAE,MAAM,GAAG,cAAc;AAE1F,QAAI,CAAC,QAAQ,QAAQ;AACnB,WAAK,MAAM,UAAU;AAErB,aAAO;IACT;AAEA,WAAO;;UAED,QAAQ,IACR,YAAU;;yBAEK,UAAU,UAAU,eAAe,MAAM,CAAC,CAAC;sBAC/C,iCAAQ,SAAQ,SAAS;uBACvB,MAAM,KAAK,gBAAgB,MAAM,CAAC;uBAClC,UAAU,KAAK,MAAM,CAAC;yBACpB,KAAK,OAAO;;;WAG1B,CACF;;;EAGP;EAGQ,gBAAgB,QAAgB;AACtC,QAAI,KAAK,SAAS;AAChB;IACF;AACA,UAAM,YAAY,oBAAoB,aAAa,OAAO,IAAI,OAAO,IAAI;AACzE,QAAI,WAAW;AACb,uBAAiB,KAAK,sBAAsB,EAAE,UAAS,CAAE;IAC3D,OAAO;AACL,uBAAiB,KAAK,2BAA2B,EAAE,OAAM,CAAE;IAC7D;EACF;;AA7EmBC,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEQA,aAAA;EAAhB,MAAK;;AATK,8BAA2BA,aAAA;EADvC,cAAc,gCAAgC;GAClC,2BAA2B;;;;;;;;;ACNjC,IAAM,gCAAN,MAAMC,uCAAsC,WAAU;EAW3D,cAAA;AACE,UAAK;AAVC,SAAA,cAA8B,CAAA;AAGnB,SAAA,SAAkB;AAEpB,SAAA,aAAa,oBAAoB,MAAM;AAEvC,SAAA,kBAAkB,gBAAgB,MAAM;AAIvD,SAAK,YAAY,KACf,oBAAoB,aAAa,cAAc,SAAQ,KAAK,aAAa,GAAI,GAC7E,gBAAgB,aAAa,mBAAmB,SAAQ,KAAK,kBAAkB,GAAI,CAAC;EAExF;EAEgB,uBAAoB;AAClC,SAAK,YAAY,QAAQ,iBAAe,YAAW,CAAE;EACvD;EAGgB,SAAM;AACpB,QAAI,eAAe,SAAQ,GAAI;AAC7B,WAAK,MAAM,UAAU;AAErB,aAAO;IACT;AAEA,UAAM,YAAY,KAAK,WAAW,KAAK,OAAK,EAAE,OAAO,eAAe;AACpE,QAAI,CAAC,WAAW;AACd,WAAK,MAAM,UAAU;AAErB,aAAO;IACT;AAEA,UAAM,iBAAiB,UAAU,YAAY,KAAK,iBAAgB,uCAAW,YAAW,EAAE;AAE1F,WAAO;;mBAEQ,UAAU,cAAc,CAAC;eAC7B,UAAU,QAAQ,SAAS;iBACzB,MAAM,KAAK,YAAY,SAAS,CAAC;;;iBAGjC,UAAU,KAAK,MAAM,CAAC;;;;;EAKrC;EAGQ,YAAY,WAAoB;AACtC,wBAAoB,mBAAmB,SAAS;AAChD,qBAAiB,KAAK,yBAAyB;EACjD;;AArDmBC,aAAA;EAAlB,SAAQ;;AAEQA,aAAA;EAAhB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AATK,gCAA6BA,aAAA;EADzC,cAAc,kCAAkC;GACpC,6BAA6B;;;ACb1C,IAAAC,kBAAe;;;;;;;;;;;;;;;;;;ACmBR,IAAM,mBAAN,MAAMC,0BAAyB,WAAU;EAe9C,cAAA;AACE,UAAK;AAZC,SAAA,cAA8B,CAAA;AAGnB,SAAA,SAAkB;AAEpB,SAAA,aAAa,oBAAoB,MAAM;AAEvC,SAAA,cAAc,cAAc,MAAM;AAElC,SAAA,WAAW,cAAc,MAAM;AAI9C,SAAK,YAAY,KACf,oBAAoB,aAAa,cAAc,SAAQ,KAAK,aAAa,GAAI,GAC7E,cAAc,aAAa,eAAe,SAAQ,KAAK,cAAc,GAAI,GACzE,cAAc,aAAa,YAAY,SAAQ,KAAK,WAAW,GAAI,CAAC;EAExE;EAEgB,uBAAoB;AAClC,SAAK,YAAY,QAAQ,iBAAe,YAAW,CAAE;EACvD;EAGgB,SAAM;AACpB,WAAO;mDACwC,KAAK,sBAAqB,CAAE;;EAE7E;EAGQ,wBAAqB;AAC3B,UAAM,EAAE,QAAQ,QAAQ,WAAW,UAAU,YAAY,aAAa,UAAU,SAAQ,IACtF,cAAc,oBAAoB,KAAK,YAAY,KAAK,aAAa,KAAK,QAAQ;AAEpF,UAAM,qBAAqB,cAAc,sBAAsB;MAC7D;MACA;MACA;MACA;MACA;MACA;MACA;MACA;KACD;AAED,WAAO,mBAAmB,IAAI,UAAO;AACnC,cAAQ,MAAM;QAKZ,KAAK;AACH,iBAAO;cACH,WAAW,SACT;2BACW,UAAU,KAAK,MAAM,CAAC;sDAEjC,IAAI;cACN,UAAU,SACR;2BACW,UAAU,KAAK,MAAM,CAAC;oDAEjC,IAAI;cACN,SAAS,SACP;gCACgB,QAAQ;2BACb,UAAU,KAAK,MAAM,CAAC;mDAEjC,IAAI;;QAGZ,KAAK;AACH,iBAAO;qBACI,UAAU,KAAK,MAAM,CAAC;;QAGnC,KAAK;AACH,iBAAO;qBACI,UAAU,KAAK,MAAM,CAAC;;QAGnC,KAAK;AACH,iBAAO;uBACM,QAAQ;qBACV,UAAU,KAAK,MAAM,CAAC;;QAGnC,KAAK;AACH,iBAAO;qBACI,UAAU,KAAK,MAAM,CAAC;;QAGnC,KAAK;AACH,iBAAO;qBACI,UAAU,KAAK,MAAM,CAAC;;QAGnC,KAAK;AACH,iBAAO;uBACM,WAAW;qBACb,UAAU,KAAK,MAAM,CAAC;;QAGnC;AAEE,kBAAQ,KAAK,2BAA2B,IAAI,EAAE;AAE9C,iBAAO;MACX;IACF,CAAC;EACH;;AAnHuB,iBAAA,SAASC;AAMbC,aAAA;EAAlB,SAAQ;;AAEQA,aAAA;EAAhB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AAbK,mBAAgBA,aAAA;EAD5B,cAAc,oBAAoB;GACtB,gBAAgB;;;ACnB7B,IAAAC,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACSR,IAAM,UAAN,MAAMC,iBAAgB,WAAU;EAAhC,cAAA;;AAI6B,SAAA,OAA6C,CAAA;AAE5D,SAAA,cAAuC,MAAM;AAE9B,SAAA,UAA+B,CAAA;AAE7B,SAAA,WAAW;AAE5B,SAAA,gBAAgB;AAEnB,SAAA,YAAY;AAEZ,SAAA,UAAU;EAoG5B;EAjGkB,SAAM;AACpB,SAAK,UAAU,KAAK,KAAK,SAAS;AAElC,SAAK,MAAM,UAAU;qBACJ,KAAK,SAAS;2BACR,KAAK,aAAa;;AAGzC,SAAK,QAAQ,MAAM,IAAI,KAAK,UAAU,SAAS;AAE/C,WAAO,KAAK,KAAK,IAAI,CAAC,KAAK,UAAS;;AAClC,YAAM,WAAW,UAAU,KAAK;AAEhC,aAAO;;sBAES,KAAK,QAAQ;mBAChB,MAAM,KAAK,WAAW,KAAK,CAAC;wBACvB,QAAQ;8BACH,SAAI,UAAJ,mBAAW,aAAa;;YAEzC,KAAK,aAAa,GAAG,CAAC;2DACyB,IAAI,KAAK;;;IAGhE,CAAC;EACH;EAES,eAAY;AACnB,QAAI,KAAK,cAAc,KAAK,SAAS;AACnC,WAAK,UAAU,CAAC,GAAG,KAAK,WAAW,iBAAiB,QAAQ,CAAC;AAC7D,iBAAW,MAAK;AACd,aAAK,YAAY,GAAG,IAAI;MAC1B,GAAG,CAAC;IACN;EACF;EAGQ,aAAa,KAAuC;AAC1D,QAAI,IAAI,MAAM;AACZ,aAAO,gDAAgD,IAAI,IAAI;IACjE;AAEA,WAAO;EACT;EACQ,WAAW,OAAa;AAC9B,QAAI,KAAK,SAAS;AAChB,WAAK,YAAY,OAAO,KAAK;IAC/B;AACA,SAAK,YAAY;AACjB,SAAK,YAAY,KAAK;EACxB;EAEQ,YAAY,OAAe,kBAAyB;AAC1D,UAAM,aAAa,KAAK,QAAQ,KAAK,SAAS;AAC9C,UAAM,YAAY,KAAK,QAAQ,KAAK;AAEpC,UAAM,iBAAiB,yCAAY,cAAc;AACjD,UAAM,gBAAgB,uCAAW,cAAc;AAE/C,UAAM,kBAAkB,uCAAW;AACnC,UAAM,sBAAsB,+CAAe;AAE3C,QAAI,cAAc,kBAAkB,CAAC,oBAAoB,UAAU,KAAK,WAAW;AACjF,qBAAe,QAAQ,CAAC,EAAE,SAAS,EAAC,CAAE,GAAG;QACvC,UAAU;QACV,QAAQ;QACR,MAAM;OACP;AAED,iBAAW,QAAQ,CAAC,EAAE,OAAO,OAAM,CAAE,GAAG;QACtC,UAAU;QACV,QAAQ;QACR,MAAM;OACP;IACH;AAEA,QAAI,aAAa,mBAAmB,uBAAuB,eAAe;AACxE,UAAI,UAAU,KAAK,aAAa,kBAAkB;AAChD,aAAK,gBAAgB,GACnB,KAAK,MAAM,gBAAgB,QAAQ,oBAAoB,KAAK,IAAI,CAClE;AAEA,kBAAU,QAAQ,CAAC,EAAE,OAAO,GAAG,gBAAgB,QAAQ,oBAAoB,KAAK,KAAI,CAAE,GAAG;UACvF,UAAU,mBAAmB,IAAI;UACjC,MAAM;UACN,QAAQ;SACT;AAED,sBAAc,QAAQ,CAAC,EAAE,SAAS,EAAC,CAAE,GAAG;UACtC,UAAU,mBAAmB,IAAI;UACjC,OAAO,mBAAmB,IAAI;UAC9B,MAAM;UACN,QAAQ;SACT;MACH;IACF;EACF;;AAlHuB,QAAA,SAAS,CAAC,aAAa,eAAeC,eAAM;AAGjCC,aAAA;EAAjC,SAAS,EAAE,MAAM,MAAK,CAAE;;AAENA,aAAA;EAAlB,SAAQ;;AAEyBA,aAAA;EAAjC,SAAS,EAAE,MAAM,MAAK,CAAE;;AAEWA,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAERA,aAAA;EAAlB,SAAQ;;AAEOA,aAAA;EAAf,MAAK;;AAEUA,aAAA;EAAf,MAAK;;AAhBK,UAAOA,aAAA;EADnB,cAAc,UAAU;GACZ,OAAO;;;;;;;;;ACFb,IAAM,sBAAN,MAAMC,6BAA4B,WAAU;EAA5C,cAAA;;AAEG,SAAA,eAA2B,CAAA;AAE3B,SAAA,cAA8B,CAAA;AAGJ,SAAA,YAAwB,CAAA;AAEvC,SAAA,mBAAkD;EA8CvE;EA5CE,qBAAkB;AAChB,SAAK,YAAY,QAAQ,iBAAe,YAAW,CAAE;EACvD;EAGgB,SAAM;AACpB,UAAM,OAAO,KAAK,aAAY;AAE9B,WAAO;mDACwC,CAAC,KAAK,KAAK,KAAK,GAAG,CAAU;0BACtD,IAAI,iBAAiB,KAAK,YAAY,KAAK,IAAI,CAAC;;;EAGxE;EAGQ,eAAY;AAClB,UAAM,OAAO,KAAK,UAAU,IAAI,cAAW;AACzC,UAAI,aAAa,WAAW;AAC1B,eAAO,EAAE,OAAO,WAAW,MAAM,aAAa,UAAU,UAAS;MACnE,WAAW,aAAa,UAAU;AAChC,eAAO,EAAE,OAAO,UAAU,MAAM,UAAU,UAAU,SAAQ;MAC9D,WAAW,aAAa,UAAU;AAChC,eAAO,EAAE,OAAO,UAAU,MAAM,UAAU,UAAU,SAAQ;MAC9D,WAAW,aAAa,OAAO;AAC7B,eAAO,EAAE,OAAO,UAAU,MAAM,WAAW,UAAU,MAAK;MAC5D,WAAW,aAAa,WAAW;AACjC,eAAO,EAAE,OAAO,WAAW,MAAM,WAAW,UAAU,UAAS;MACjE;AAEA,aAAO,EAAE,OAAO,WAAW,MAAM,aAAa,UAAU,cAAa;IACvE,CAAC;AAED,SAAK,eAAe,KAAK,IAAI,CAAC,EAAE,SAAQ,MAAO,QAAQ;AAEvD,WAAO;EACT;EAEQ,YAAY,OAAa;;AAC/B,UAAM,MAAM,KAAK,aAAa,KAAK;AACnC,QAAI,KAAK;AACP,iBAAK,qBAAL,8BAAwB;IAC1B;EACF;;AA/CkCC,aAAA;EAAjC,SAAS,EAAE,MAAM,MAAK,CAAE;;AAENA,aAAA;EAAlB,SAAQ;;AATE,sBAAmBA,aAAA;EAD/B,cAAc,uBAAuB;GACzB,mBAAmB;;;ACPhC,IAAAC,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACSf,IAAM,2BAA2B;EAC/B,MAAM;EACN,SAAS;EACT,QAAQ;EACR,gBAAgB;EAChB,kBAAkB;EAClB,SAAS;EACT,UAAU;;AAGZ,IAAM,uBAAuB;EAC3B,IAAI;EACJ,IAAI;;AAGN,IAAM,uBAAuB;EAC3B,IAAI;EACJ,IAAI;;AAKC,IAAM,YAAN,MAAMC,mBAAkB,WAAU;EAAlC,cAAA;;AAKc,SAAA,OAAmB;AAEF,SAAA,WAAW;AAEX,SAAA,YAAY;AAEZ,SAAA,UAAU;AAE3B,SAAA,UAAyB;AAEP,SAAA,cAAc;AAEd,SAAA,eAAe;AAEjC,SAAA,eAA6D;EAqDlF;EAhDkB,SAAM;AACpB,SAAK,MAAM,UAAU;qBACJ,KAAK,YAAY,SAAS,MAAM;2BAC1B,KAAK,UAAU,IAAI,CAAC;2BACpB,KAAK,UAAU,IAAI,CAAC;qDACM,KAAK,YAAY;;AAGlE,UAAM,cAAc,KAAK,eAAe,qBAAqB,KAAK,IAAI;AAEtE,WAAO;;uBAEY,KAAK,OAAO;yBACV,KAAK,WAAW;0BACf,KAAK,YAAY;oBACvB,KAAK,IAAI;oBACT,KAAK,QAAQ;;UAEvB,KAAK,gBAAe,CAAE;4CACY,MAAM,KAAK,qBAAoB,CAAE;4BACjD,WAAW;;;6CAGM,MAAM,KAAK,sBAAqB,CAAE;;;EAG7E;EAEO,uBAAoB;AACzB,SAAK,cAAc;EACrB;EAEO,wBAAqB;AAC1B,SAAK,eAAe;EACtB;EAEO,kBAAe;AACpB,QAAI,KAAK,SAAS;AAChB,YAAM,OAAO,qBAAqB,KAAK,IAAI;AAC3C,YAAM,QAAQ,KAAK,WACf,yBAAyB,UAAU,IACnC,yBAAyB,KAAK,OAAO;AAEzC,aAAO,kCAAkC,KAAK,SAAS,IAAI;IAC7D;AAEA,WAAO;EACT;;AAtEuB,UAAA,SAAS,CAAC,aAAa,eAAeC,eAAM;AAIhDC,aAAA;EAAlB,SAAQ;;AAE2BA,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAESA,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAESA,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAERA,aAAA;EAAlB,SAAQ;;AAE4BA,aAAA;EAApC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAEUA,aAAA;EAApC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAERA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AArBE,YAASA,aAAA;EADrB,cAAc,YAAY;GACd,SAAS;;;AC/BtB,IAAAC,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;ACSR,IAAM,UAAN,MAAMC,iBAAgB,WAAU;EAAhC,cAAA;;AAIc,SAAA,SAAkB;AAED,SAAA,WAAW;AAEnC,SAAA,QAAmB;EAcjC;EAXkB,SAAM;AACpB,WAAO;0BACe,KAAK,QAAQ,aAAa,UAAU,KAAK,MAAM,CAAC;;8CAE5B,KAAK,KAAK;;;;;;EAMtD;;AApBuB,QAAA,SAAS,CAAC,aAAa,eAAeC,eAAM;AAGhDC,aAAA;EAAlB,SAAQ;;AAE2BA,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAEfA,aAAA;EAAX,SAAQ;;AARE,UAAOA,aAAA;EADnB,cAAc,UAAU;GACZ,OAAO;;;ACTpB,IAAAC,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACMR,IAAM,sBAAN,MAAMC,6BAA4B,WAAU;EAA5C,cAAA;;AAI8B,SAAA,SAAS;EA6B9C;EA1BkB,SAAM;AACpB,WAAO,KAAK,kBAAiB;EAC/B;EAEQ,oBAAiB;AACvB,UAAM,SAAS,KAAK,SAAS,KAAK,KAAK,KAAK;AAC5C,UAAM,gBAAgB;AACtB,UAAM,eAAe,gBAAgB;AACrC,UAAM,iBAAiB,MAAM;AAC7B,UAAM,eAAe,MAAM;AAC3B,UAAM,aAAa,MAAM,eAAe;AAExC,WAAO;;;;;;;eAOI,MAAM;8BACS,cAAc,IAAI,YAAY;8BAC9B,UAAU;;;;EAItC;;AA/BuB,oBAAA,SAAS,CAAC,aAAaC,eAAM;AAGjBC,aAAA;EAAlC,SAAS,EAAE,MAAM,OAAM,CAAE;;AAJf,sBAAmBA,aAAA;EAD/B,cAAc,uBAAuB;GACzB,mBAAmB;;;ACNhC,IAAAC,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACUR,IAAM,gBAAN,MAAMC,uBAAsB,WAAU;EAAtC,cAAA;;AAIc,SAAA,UAA6B;AAE7B,SAAA,WAAW;AAEM,SAAA,WAAW;AAE5B,SAAA,OAAiB;AAEjB,SAAA,OAAoB;AAEpB,SAAA,OAAO;EAkB5B;EAfkB,SAAM;AACpB,UAAM,cAAc,KAAK,SAAS,OAAO,cAAc;AAEvD,WAAO;;gBAEK,KAAK,WAAW,aAAa,EAAE;uBACxB,KAAK,OAAO;oBACf,KAAK,IAAI;;UAEnB,KAAK,WAAW,sBAAsB,KAAK,QAAQ,kBAAkB,IAAI;4BACvD,WAAW,qBAAqB,KAAK,IAAI;yBAC5C,KAAK,IAAI;;;EAGhC;;AA9BuB,cAAA,SAAS,CAAC,aAAa,eAAeC,eAAM;AAGhDC,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAE2BA,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAERA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAdE,gBAAaA,aAAA;EADzB,cAAc,iBAAiB;GACnB,aAAa;;;ACV1B,IAAAC,mBAAe;;;;;;;;;;;;;;;ACSR,IAAM,eAAN,MAAMC,sBAAqB,WAAU;EAArC,cAAA;;AAI+B,SAAA,WAAW;AAE5B,SAAA,QAAQ;AAER,SAAA,cAAc;EAgBnC;EAbkB,SAAM;AACpB,WAAO;;;;mBAIQ,CAAC,OAAO,MAAM,OAAO,IAAI,CAAU;;2DAEK,KAAK,KAAK;0DACX,KAAK,WAAW;;;;EAIxE;;AAtBuB,aAAA,SAAS,CAAC,aAAa,eAAeC,gBAAM;AAG/BC,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAERA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AARE,eAAYA,aAAA;EADxB,cAAc,gBAAgB;GAClB,YAAY;;;ACTzB,IAAAC,mBAAe;;;;;;;;;;;;;;ACSR,IAAM,yBAAN,MAAMC,gCAA+B,WAAU;EAA/C,cAAA;;AAIuB,SAAA,SAAoB;EAoFlD;EAjFkB,SAAM;AACpB,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,MAAM,UAAU;AAErB,aAAO;IACT;AACA,UAAM,EAAE,MAAM,WAAW,YAAY,cAAc,SAAQ,IAAK,KAAK;AACrE,UAAM,WAAW,eAAe,SAAQ;AACxC,UAAM,QAAQ,eAAe,MAAK;AAClC,UAAM,YAAY,eAAe,UAAS;AAC1C,UAAM,aAAa,CAAC,WAAW,YAAY,UAAU,YAAY,EAAE,OAAO,OAAO,EAAE,SAAS;AAC5F,UAAM,YAAY,aAAa,kBAAkB;MAC/C,QAAQ;MACR,YAAY;MACZ,UAAU;MACV,UAAU;KACX;AAED,QAAI,cAAc,CAAC,UAAU;AAC3B,aAAO;;kBAEK,cAAc,SAAS,GAAG;;mBAEzB,MAAM,iBAAiB,KAAK,aAAa,EAAE,QAAQ,KAAK,OAAM,CAAE,CAAC;;;IAGhF;AAEA,QAAI,CAAC,cAAc,UAAU;AAC3B,aAAO;;kBAEK,cAAc,SAAS,GAAG;;mBAEzB,KAAK,WAAW,KAAK,IAAI,CAAC;;;IAGzC;AAEA,QAAI,aAAa,OAAO;AACtB,aAAO;;kBAEK,cAAc,SAAS,GAAG;;mBAEzB,KAAK,WAAW,KAAK,IAAI,CAAC;;;IAGzC;AAEA,QAAI,cAAc,WAAW;AAC3B,aAAO;;kBAEK,cAAc,SAAS,GAAG;;mBAEzB,KAAK,YAAY,KAAK,IAAI,CAAC;;;IAG1C;AAEA,SAAK,MAAM,UAAU;AAErB,WAAO;EACT;EAGQ,aAAU;;AAChB,SAAI,UAAK,WAAL,mBAAa,WAAW;AAC1B,qBAAe,SAAS,KAAK,OAAO,WAAW,QAAQ;IACzD;EACF;EAEQ,cAAW;;AACjB,SAAI,UAAK,WAAL,mBAAa,YAAY;AAC3B,qBAAe,SAAS,KAAK,OAAO,YAAY,QAAQ;IAC1D;EACF;EAEQ,aAAU;;AAChB,SAAI,UAAK,WAAL,mBAAa,UAAU;AACzB,qBAAe,SAAS,KAAK,OAAO,UAAU,QAAQ;IACxD;EACF;;AAtFuB,uBAAA,SAAS,CAACC,gBAAM;AAGXC,aAAA;EAA3B,SAAS,EAAE,MAAM,OAAM,CAAE;;AAJf,yBAAsBA,aAAA;EADlC,cAAc,2BAA2B;GAC7B,sBAAsB;;;ACTnC,IAAAC,mBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuBT,IAAO,sBAAP,cAAmC,WAAU;EAgDjD,cAAA;;AACE,UAAK;AA7CY,SAAA,UAAS,sBAAiB,MAAM,SAAvB,mBAA6B;AAEtC,SAAA,aAAY,sBAAiB,MAAM,SAAvB,mBAA6B;AAElD,SAAA,UAA0C;AAE1C,SAAA,mBAA6B;AAE7B,SAAA,YAAmD;AAEnD,SAAA,WAAkD;AAElD,SAAA,gBAAuD;AAEvD,SAAA,kBAAkB;AAElB,SAAA,cAA8B,CAAA;AAEhC,SAAA,WACN,UAAU,eAAe,KAAK,MAAM,KAAK,UAAU,kBAAkB,KAAK,SAAS;AAE7E,SAAA,SAAO,UAAK,WAAL,mBAAa,WAAQ,UAAK,cAAL,mBAAgB,SAAQ;AAGzC,SAAA,aAAa;AAEb,SAAA,MAAM,qBAAqB,MAAM;AAEjC,SAAA,QAAQ,qBAAqB,MAAM;AAEnC,SAAA,QAAQ;AAEV,SAAA,YAAY;AAEV,SAAA,oBAAqB;AAErB,SAAA,iBAAiB;AAEjB,SAAA,YAAY;AAEK,SAAA,WAAW;AAE5B,SAAA,UAAiD;AAIlE,SAAK,YAAY,KACf,GAAG;MACD,qBAAqB,aAAa,SAAS,SAAM;;AAC/C,aAAK,MAAM;AACX,YAAI,KAAK,cAAc,KAAK,SAAS;AACnC,eAAK,aAAa;AAClB,WAAAC,MAAA,KAAK,cAAL,gBAAAA,IAAA;QACF;MACF,CAAC;MACD,qBAAqB,aAAa,WAAW,SAAQ,KAAK,QAAQ,GAAI;KACvE;AAGH,SACG,eAAe,WAAU,KAAM,eAAe,SAAQ,MACvD,eAAe,MAAK,KACpB,qBAAqB,MAAM,OAC3B;AACA,iBAAK,cAAL;IACF;EACF;EAEgB,eAAY;;AAC1B,eAAK,kBAAL;AACA,SAAK,YAAY,CAAC,KAAK;EACzB;EAEgB,uBAAoB;AAClC,SAAK,YAAY,QAAQ,iBAAe,YAAW,CAAE;AACrD,yBAAqB,WAAW,KAAK;AACrC,iBAAa,KAAK,OAAO;EAC3B;EAGgB,SAAM;;AACpB,eAAK,aAAL;AACA,SAAK,YAAW;AAEhB,UAAM,WAAW,KAAK,QAClB,qEACA,KAAK;AAET,QAAI,QAAQ,eAAe,KAAK,IAAI;AAEpC,QAAI,KAAK,OAAO;AACd,cAAQ;IACV;AAEA,WAAO;;qBAEU,UAAU,KAAK,KAAK,CAAC;qBACrB,KAAK,SAAS;;;mBAGhB,CAAC,OAAO,MAAM,MAAM,IAAI,CAAU;;;;iDAIJ,UAAU,KAAK,QAAQ,CAAC;;YAE7D,KAAK,QAAQ,OAAO,KAAK,eAAc,CAAE;;;;;;;;;;;;;;oDAcD,KAAK,QAAQ,cAAc,QAAQ;cACzE,KAAK;;wEAEqD,QAAQ;;;UAGtE,KAAK,oBACH;;;;4BAIgB,KAAK,cAAc,KAAK,SAAS;yBACpC,KAAK,WAAW,KAAK,IAAI,CAAC;;;iEAGc,KAAK,gBAAgB;kBACpE,KAAK,iBAAiB;;gBAG5B,IAAI;;;QAGR,KAAK,kBACH;iCACuB,CAAC,KAAK,MAAM,MAAM,IAAI,CAAU;iCAChC,KAAK,SAAS;;;;;cAMrC,IAAI;;2CAE6B,KAAK,MAAM;;EAEpD;EAGQ,cAAW;;AACjB,QAAI,KAAK,SAAS,CAAC,KAAK,WAAW;AACjC,WAAK,YAAY;AACjB,YAAM,eAAc,UAAK,eAAL,mBAAiB,cAAc;AACnD,iDAAa,QAAQ,CAAC,EAAE,SAAS,EAAC,GAAI,EAAE,SAAS,EAAC,CAAE,GAAG;QACrD,MAAM;QACN,QAAQ;;IAEZ;EACF;EAEU,aAAU;;AAClB,yBAAqB,WAAW,KAAK;AACrC,QAAI,KAAK,SAAS;AAChB,WAAK,aAAa;AAClB,iBAAK,YAAL;IACF,OAAO;AACL,iBAAK,cAAL;IACF;EACF;EAEQ,iBAAc;AACpB,UAAM,qBAAqB,gBAAgB,MAAM,eAAe,4BAA4B;AAC5F,UAAM,SAAS,qBAAqB,SAAS,mBAAmB,QAAQ,MAAM,EAAE,GAAG,EAAE,IAAI;AAEzF,WAAO,qCAAqC,SAAS,CAAC;EACxD;EAGU,YAAS;AACjB,QAAI;AACF,UAAI,KAAK,KAAK;AACZ,uBAAe,gBAAgB,KAAK,GAAG;AACvC,wBAAgB,YAAY,aAAa;MAC3C;IACF,QAAQ;AACN,sBAAgB,UAAU,gBAAgB;IAC5C;EACF;;AAvMuB,oBAAA,SAASC;AA2BbC,aAAA;EAAlB,MAAK;;AAEaA,aAAA;EAAlB,MAAK;;AAEaA,aAAA;EAAlB,MAAK;;AAEaA,aAAA;EAAlB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AAEaA,aAAA;EAAlB,MAAK;;AAEaA,aAAA;EAAlB,MAAK;;AAEaA,aAAA;EAAlB,MAAK;;AAE8BA,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAERA,aAAA;EAAlB,SAAQ;;;;;;;;;;AC3DJ,IAAM,yBAAN,MAAMC,gCAA+B,oBAAmB;EAC7D,cAAA;AACE,UAAK;AACL,QAAI,CAAC,KAAK,QAAQ;AAChB,YAAM,IAAI,MAAM,+CAA+C;IACjE;AACA,SAAK,YAAY,KAAK,eAAe,KAAK,IAAI;AAC9C,SAAK,gBAAgB,KAAK,eAAe,KAAK,IAAI;AAClD,qBAAiB,UAAU;MACzB,MAAM;MACN,OAAO;MACP,YAAY,EAAE,MAAM,KAAK,OAAO,MAAM,UAAU,UAAS;KAC1D;EACH;EAGQ,MAAM,iBAAc;;AAC1B,QAAI;AACF,WAAK,QAAQ;AACb,YAAM,EAAE,WAAU,IAAK,oBAAoB;AAE3C,YAAM,YAAY,WAAW,KAC3B,OAAE;;AACC,iBAAE,SAAS,iBAAeC,MAAA,EAAE,SAAF,gBAAAA,IAAQ,YAAS,UAAK,WAAL,mBAAa,SACzD,EAAE,SAAS,cACX,EAAE,WAAS,UAAK,WAAL,mBAAa;OAAI;AAGhC,UAAI,WAAW;AACb,cAAM,qBAAqB,gBAAgB,WAAW,UAAU,KAAK;MACvE,OAAO;AACL,cAAM,IAAI,MAAM,+CAA+C;MACjE;AAEA,sBAAgB,MAAK;AAErB,uBAAiB,UAAU;QACzB,MAAM;QACN,OAAO;QACP,YAAY,EAAE,QAAQ,WAAW,QAAM,UAAK,WAAL,mBAAa,SAAQ,UAAS;OACtE;IACH,SAAS,OAAO;AACd,uBAAiB,UAAU;QACzB,MAAM;QACN,OAAO;QACP,YAAY,EAAE,UAAU,+BAAqB,YAAW,UAAS;OAClE;AACD,WAAK,QAAQ;IACf;EACF;;AAjDW,yBAAsBC,aAAA;EADlC,cAAc,2BAA2B;GAC7B,sBAAsB;;;;;;;;;ACN5B,IAAM,yBAAN,MAAMC,gCAA+B,oBAAmB;EAC7D,cAAA;AACE,UAAK;AACL,QAAI,CAAC,KAAK,QAAQ;AAChB,YAAM,IAAI,MAAM,+CAA+C;IACjE;AACA,SAAK,YAAY,KAAK,eAAe,KAAK,IAAI;AAC9C,SAAK,WAAW,KAAK,cAAc,KAAK,IAAI;AAE5C,qBAAiB,UAAU;MACzB,MAAM;MACN,OAAO;MACP,YAAY,EAAE,MAAM,KAAK,OAAO,MAAM,UAAU,UAAS;KAC1D;EACH;EAGQ,gBAAa;;AACnB,QAAI,CAAC,KAAK,SAAS,KAAK,KAAK;AAC3B,WAAK,QAAQ;AACb,iBAAK,cAAL;IACF;EACF;EAEQ,iBAAc;;AACpB,UAAI,UAAK,WAAL,mBAAa,iBAAgB,KAAK,KAAK;AACzC,UAAI;AACF,aAAK,QAAQ;AACb,cAAM,EAAE,cAAc,KAAI,IAAK,KAAK;AACpC,cAAM,EAAE,UAAU,KAAI,IAAK,eAAe,gBAAgB,cAAc,KAAK,GAAG;AAChF,6BAAqB,aAAa,EAAE,MAAM,KAAI,CAAE;AAChD,6BAAqB,gBAAgB,KAAK,MAAM;AAChD,uBAAe,SAAS,UAAU,QAAQ;MAC5C,QAAQ;AACN,aAAK,QAAQ;MACf;IACF;EACF;;AArCW,yBAAsBC,aAAA;EADlC,cAAc,2BAA2B;GAC7B,sBAAsB;;;;;;;;;ACS5B,IAAM,wBAAN,MAAMC,+BAA8B,oBAAmB;EAiB5D,cAAA;AACE,UAAK;AAhBC,SAAA,kBAAkD;AAGvC,SAAA,mBAAuC;AAEvC,SAAA,wBAA4C;AAE5C,SAAA,SAAiC;AAEjC,SAAA,uBACjB,kBAAkB,MAAM;AAEE,SAAA,YAAY;AA2CrB,SAAA,YAAY,MAAK;;AAClC,YAAI,UAAK,WAAL,mBAAa,gBAAe,KAAK,KAAK;AACxC,YAAI;AACF,eAAK,QAAQ;AACb,gBAAM,EAAE,aAAa,WAAW,KAAI,IAAK,KAAK;AAC9C,gBAAM,EAAE,UAAU,uBAAuB,KAAI,IAAK,eAAe,gBAC/D,aACA,KAAK,KACL,SAAS;AAGX,eAAK,mBAAmB;AACxB,eAAK,wBAAwB;AAC7B,eAAK,SAAS,eAAe,SAAQ,IAAK,SAAS;AAEnD,+BAAqB,aAAa,EAAE,MAAM,KAAI,CAAE;AAChD,+BAAqB,gBAAgB,KAAK,MAAM;AAEhD,cAAI,KAAK,wBAAwB,KAAK,uBAAuB;AAC3D,2BAAe,SAAS,KAAK,uBAAuB,KAAK,MAAM;UACjE,OAAO;AACL,2BAAe,SAAS,KAAK,kBAAkB,KAAK,MAAM;UAC5D;QACF,SAAS,GAAG;AACV,2BAAiB,UAAU;YACzB,MAAM;YACN,OAAO;YACP,YAAY;cACV,SAAS,aAAa,QAAQ,EAAE,UAAU;cAC1C,KAAK,KAAK;cACV,aAAa,KAAK,OAAO;cACzB,MAAM,KAAK,OAAO;;WAErB;AACD,eAAK,QAAQ;QACf;MACF;IACF;AA3EE,QAAI,CAAC,KAAK,QAAQ;AAChB,YAAM,IAAI,MAAM,8CAA8C;IAChE;AAEA,SAAK,oBAAoB;AACzB,SAAK,iBAAiBC,eAAc,eAAe;AACnD,SAAK,mBAAmB;AAGxB,SAAK,YAAW;AAEhB,SAAK,YAAY,KACf,qBAAqB,aAAa,SAAS,MAAK;AAC9C,WAAK,YAAW;IAClB,CAAC,CAAC;AAGJ,qBAAiB,UAAU;MACzB,MAAM;MACN,OAAO;MACP,YAAY,EAAE,MAAM,KAAK,OAAO,MAAM,UAAU,SAAQ;KACzD;EACH;EAEgB,uBAAoB;AAClC,UAAM,qBAAoB;AAC1B,iBAAa,KAAK,eAAe;EACnC;EAGQ,cAAW;;AACjB,SAAK,YAAY,CAAC,KAAK;AACvB,QAAI,CAAC,KAAK,SAAS,KAAK,KAAK;AAC3B,WAAK,QAAQ;AACb,iBAAK,cAAL;IACF;EACF;EAyCmB,aAAU;;AAE3B,yBAAqB,WAAW,KAAK;AACrC,eAAK,cAAL;EACF;;AA/FmBC,aAAA;EAAlB,MAAK;;AAEaA,aAAA;EAAlB,MAAK;;AAEaA,aAAA;EAAlB,MAAK;;AAEaA,aAAA;EAAlB,MAAK;;AAGsBA,aAAA;EAA3B,MAAK;;AAdK,wBAAqBA,aAAA;EADjC,cAAc,0BAA0B;GAC5B,qBAAqB;;;ACflC,oBAAuB;AAOvB,IAAM,0BAA0B;AAChC,IAAM,uBAAuB;AAC7B,IAAM,uBAAuB;AAE7B,SAAS,eAAe,IAAY,SAAiB,UAAgB;AACnE,MAAI,OAAO,SAAS;AAClB,WAAO;EACT;AACA,QAAM,OAAO,KAAK,UAAU,IAAI,UAAU,KAAK,KAAK;AAEpD,SAAO,QAAQ,WAAW;AAC5B;AAEA,SAAS,UAAU,OAAe,sBAA2D;AAC3F,QAAM,MAAM,MAAM,UAAU,MAAM,KAChC,cAAAC,QAAW,OAAO,OAAO,EAAE,qBAAoB,CAAE,EAAE,QAAQ,MAC3D,CAAC;AAEH,QAAM,OAAO,KAAK,KAAK,IAAI,MAAM;AAGjC,SAAO,IAAI,OACT,CAAC,MAAM,KAAK,WAET,QAAQ,SAAS,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,SAAS,CAAC,EAAE,KAAK,GAAG,MAAM,MAC/E,CAAA,CAAE;AAEN;AAEO,IAAM,aAAa;EACxB,SAAS,EACP,KACA,MACA,UACA,WAAW,UAAS,GAMrB;AACC,UAAM,YAAY;AAClB,UAAM,cAAc;AACpB,UAAM,OAAyB,CAAA;AAC/B,UAAM,SAAS,UAAU,KAAK,GAAG;AACjC,UAAM,WAAW,OAAO,OAAO;AAC/B,UAAM,SAAS;MACb,EAAE,GAAG,GAAG,GAAG,EAAC;MACZ,EAAE,GAAG,GAAG,GAAG,EAAC;MACZ,EAAE,GAAG,GAAG,GAAG,EAAC;;AAGd,WAAO,QAAQ,CAAC,EAAE,GAAG,EAAC,MAAM;AAC1B,YAAM,MAAM,OAAO,SAAS,wBAAwB,WAAW;AAC/D,YAAM,MAAM,OAAO,SAAS,wBAAwB,WAAW;AAC/D,YAAM,eAAe;AACrB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,cAAM,UAAU,YAAY,uBAAuB,IAAI;AACvD,aAAK,KACH;;qBAEW,MAAM,IAAI,WAAW,SAAS;sBAC7B,MAAM,IAAI,UAAU,cAAc,OAAO;oBAC3C,MAAM,KAAK,UAAU,eAAe,eAAe,UAAU,YAAY;oBACzE,MAAM,KAAK,UAAU,eAAe,eAAe,UAAU,YAAY;uBACtE,QAAQ;6BACF,MAAM,IAAI,cAAc,CAAC;uBAC/B,MAAM,IAAI,UAAU,cAAc,OAAO;mBAC7C,MAAM,IAAI,KAAK,WAAW,IAAI,cAAc,IAAI,KAAK,WAAW,CAAC;mBACjE,MAAM,IAAI,KAAK,WAAW,IAAI,cAAc,IAAI,KAAK,WAAW,CAAC;;WAEzE;MAEL;IACF,CAAC;AAED,UAAM,iBAAiB,KAAK,OAAO,WAAW,MAAM,QAAQ;AAC5D,UAAM,oBAAoB,OAAO,SAAS,IAAI,iBAAiB;AAC/D,UAAM,kBAAkB,OAAO,SAAS,IAAI,iBAAiB,IAAI;AACjE,UAAM,UAA8B,CAAA;AAGpC,WAAO,QAAQ,CAAC,KAA0B,MAAa;AACrD,UAAI,QAAQ,CAAC,GAAG,MAAa;AAC3B,YAAI,OAAO,CAAC,EAAE,CAAC,GAAG;AAChB,cACE,EACG,IAAI,wBAAwB,IAAI,wBAChC,IAAI,OAAO,UAAU,uBAAuB,MAAM,IAAI,wBACtD,IAAI,wBAAwB,IAAI,OAAO,UAAU,uBAAuB,KAE3E;AACA,gBACE,EACE,IAAI,qBACJ,IAAI,mBACJ,IAAI,qBACJ,IAAI,kBAEN;AACA,oBAAM,KAAK,IAAI,WAAW,WAAW;AACrC,oBAAM,KAAK,IAAI,WAAW,WAAW;AACrC,sBAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACvB;UACF;QACF;MACF,CAAC;IACH,CAAC;AAGD,UAAM,mBAA6C,CAAA;AAGnD,YAAQ,QAAQ,CAAC,CAAC,IAAI,EAAE,MAAK;AAxHjC;AA0HM,UAAI,iBAAiB,EAAE,GAAG;AACxB,+BAAiB,EAAE,MAAnB,mBAAsB,KAAK;MAC7B,OAAO;AACL,yBAAiB,EAAE,IAAI,CAAC,EAAE;MAC5B;IACF,CAAC;AAGD,WAAO,QAAQ,gBAAgB,EAE5B,IAAI,CAAC,CAAC,IAAI,GAAG,MAAK;AACjB,YAAM,SAAS,IAAI,OAAO,QACxB,IAAI,MAAM,aAAW,CAAC,eAAe,IAAI,SAAS,QAAQ,CAAC,CAAC;AAG9D,aAAO,CAAC,OAAO,EAAE,GAAG,MAAM;IAC5B,CAAC,EACA,QAAQ,CAAC,CAAC,IAAI,GAAG,MAAK;AACrB,UAAI,QAAQ,QAAK;AACf,aAAK,KACH,iBAAiB,EAAE,OAAO,EAAE,SAAS,QAAQ,MAAM,WAAW,oBAAoB,KAAK;MAE3F,CAAC;IACH,CAAC;AAGH,WAAO,QAAQ,gBAAgB,EAE5B,OAAO,CAAC,CAAC,GAAG,GAAG,MAAM,IAAI,SAAS,CAAC,EAEnC,IAAI,CAAC,CAAC,IAAI,GAAG,MAAK;AACjB,YAAM,SAAS,IAAI,OAAO,QAAM,IAAI,KAAK,aAAW,eAAe,IAAI,SAAS,QAAQ,CAAC,CAAC;AAE1F,aAAO,CAAC,OAAO,EAAE,GAAG,MAAM;IAC5B,CAAC,EAEA,IAAI,CAAC,CAAC,IAAI,GAAG,MAAK;AACjB,UAAI,KAAK,CAAC,GAAG,MAAO,IAAI,IAAI,KAAK,CAAE;AACnC,YAAM,SAAqB,CAAA;AAE3B,iBAAW,MAAM,KAAK;AACpB,cAAM,QAAQ,OAAO,KAAK,UACxB,KAAK,KAAK,aAAW,eAAe,IAAI,SAAS,QAAQ,CAAC,CAAC;AAE7D,YAAI,OAAO;AACT,gBAAM,KAAK,EAAE;QACf,OAAO;AACL,iBAAO,KAAK,CAAC,EAAE,CAAC;QAClB;MACF;AAEA,aAAO,CAAC,IAAI,OAAO,IAAI,UAAQ,CAAC,KAAK,CAAC,GAAG,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC,EACA,QAAQ,CAAC,CAAC,IAAI,MAAM,MAAK;AACxB,aAAO,QAAQ,CAAC,CAAC,IAAI,EAAE,MAAK;AAC1B,aAAK,KACH;;qBAES,EAAE;qBACF,EAAE;qBACF,EAAE;qBACF,EAAE;yBACE,QAAQ;+BACF,YAAY,uBAAuB,EAAE;;;aAGvD;MAEL,CAAC;IACH,CAAC;AAEH,WAAO;EACT;;;;AChMF,IAAAC,mBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACUf,IAAM,qBAAqB;AAGpB,IAAM,YAAN,MAAMC,mBAAkB,WAAU;EAAlC,cAAA;;AAIc,SAAA,MAAM;AAEU,SAAA,OAAO;AAEvB,SAAA,QAAmB;AAEnB,SAAA,WAAoB;AAEpB,SAAA,MAAe;AAIE,SAAA,aAAuB;AAEvB,SAAA,YAAsB;EA+C5D;EA5CkB,SAAM;AACpB,SAAK,QAAQ,OAAO,IAAI,KAAK;AAC7B,SAAK,QAAQ,OAAO,IAAI,OAAO,KAAK,UAAU;AAC9C,SAAK,MAAM,UAAU;qBACJ,KAAK,IAAI;2BACH,KAAK,SAAS,kBAAkB;;AAGvD,WAAO,OAAO,KAAK,eAAc,CAAE,IAAI,KAAK,YAAW,CAAE;EAC3D;EAIQ,cAAW;AACjB,UAAM,OAAO,KAAK,UAAU,UAAU,KAAK,OAAO,KAAK,OAAO,KAAK;AAEnE,WAAO;oBACS,IAAI,UAAU,IAAI;UAC5B,WAAW,SAAS;MACpB,KAAK,KAAK;MACV;MACA,UAAU,KAAK,aAAa,IAAI,OAAO;MACvC,UAAU,KAAK;KAChB,CAAC;;;EAGR;EAEQ,iBAAc;AACpB,QAAI,KAAK,UAAU;AACjB,aAAO,sBAAsB,KAAK,QAAQ,QAAQ,KAAK,OAAO,MAAM;IACtE;AAEA,QAAI,KAAK,WAAW;AAClB,aAAO;;;;;;IAMT;AAEA,WAAO;EACT;;AA/DuB,UAAA,SAAS,CAAC,aAAaC,gBAAM;AAGjCC,aAAA;EAAlB,SAAQ;;AAE0BA,aAAA;EAAlC,SAAS,EAAE,MAAM,OAAM,CAAE;;AAEPA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAE2BA,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAESA,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAlBhB,YAASA,aAAA;EADrB,cAAc,aAAa;GACf,SAAS;;;ACbtB,IAAAC,mBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACSR,IAAM,aAAN,MAAMC,oBAAmB,WAAU;EAAnC,cAAA;;AAIc,SAAA,QAAQ;AAER,SAAA,SAAS;AAET,SAAA,eAAiC;AAEjC,SAAA,UAAmB;EAYxC;EATkB,SAAM;AACpB,SAAK,MAAM,UAAU;eACV,KAAK,KAAK;gBACT,KAAK,MAAM;uBACJ,qCAAqC,KAAK,YAAY,UAAU;;AAGnF,WAAO;EACT;;AApBuB,WAAA,SAAS,CAACC,gBAAM;AAGpBC,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAVE,aAAUA,aAAA;EADtB,cAAc,aAAa;GACf,UAAU;;;ACThB,IAAM,YAAY;;;ACAzB,IAAAC,mBAAe;;;;;;;;;;;;;;;;;;;;;;ACSR,IAAM,eAAN,MAAMC,sBAAqB,WAAU;EAI1B,SAAM;AACpB,WAAO;;;eAGI,SAAS;;;;;;;;;qBASH,CAAC,KAAK,KAAK,KAAK,GAAG,CAAC;;;;;;;EAOvC;;AAvBuB,aAAA,SAAS,CAAC,aAAa,eAAeC,gBAAM;AADxD,eAAYC,aAAA;EADxB,cAAc,iBAAiB;GACnB,YAAY;;;ACTzB,IAAAC,mBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqBR,IAAM,wBAAN,MAAMC,+BAA8B,oBAAmB;EAG5D,cAAA;;AACE,UAAK;AAmFC,SAAA,cAAc,MAAK;AACzB,WAAK,cAAa;IACpB;AApFE,WAAO,iBAAiB,UAAU,KAAK,WAAW;AAElD,qBAAiB,UAAU;MACzB,MAAM;MACN,OAAO;MACP,YAAY,EAAE,QAAM,UAAK,WAAL,mBAAa,SAAQ,iBAAiB,UAAU,SAAQ;KAC7E;EACH;EAEgB,uBAAoB;;AAClC,UAAM,qBAAoB;AAC1B,eAAK,gBAAL,mBAAkB,QAAQ,WAAS,MAAK;AACxC,WAAO,oBAAoB,UAAU,KAAK,WAAW;EACvD;EAGgB,SAAM;AACpB,SAAK,cAAa;AAElB,WAAO;;;;mBAIQ,CAAC,KAAK,MAAM,MAAM,IAAI,CAAC;;;sDAGY,KAAK,eAAc,CAAE;;;;;UAKjE,KAAK,aAAY,CAAE;;2CAEc,KAAK,MAAM;;EAEpD;EAGQ,gBAAa;AACnB,QAAI,CAAC,KAAK,SAAS,KAAK,KAAK;AAE3B,WAAK,UAAU,WAAW,MAAK;AAC7B,aAAK,QAAQ;MACf,GAAG,GAAG;IACR;EACF;EAEQ,iBAAc;AACpB,QAAI,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO;AAC5B,aAAO;IACT;AAEA,UAAM,OAAO,KAAK,sBAAqB,EAAG,QAAQ;AAClD,UAAM,MAAM,KAAK,SAAS,KAAK,OAAO,OAAO;AAC7C,yBAAqB,aAAa,MAAS;AAC3C,yBAAqB,gBAAgB,KAAK,MAAM;AAEhD,WAAO;aACE,IAAI;cACH,gBAAgB,MAAM,SAAS;YACjC,KAAK,GAAG;iBACH,UAAU,UAAU,eAAe,KAAK,MAAM,CAAC,CAAC;cACnD,UAAU,gBAAgB,MAAM,eAAe,gBAAgB,CAAC,CAAC;YACnE,UAAU,GAAG,CAAC;;;EAGxB;EAEQ,eAAY;AAClB,UAAM,WAAW,CAAC,KAAK,OAAO,CAAC,KAAK;AAEpC,WAAO;kBACO,QAAQ;eACX,KAAK,SAAS;;;;;;;EAO3B;;AApFuB,sBAAA,SAASC;AADrB,wBAAqBC,aAAA;EADjC,cAAc,0BAA0B;GAC5B,qBAAqB;;;;;;;;;ACX3B,IAAM,6BAAN,MAAMC,oCAAmC,WAAU;EAIxD,cAAA;;AACE,UAAK;AAHU,SAAA,UAAS,sBAAiB,MAAM,SAAvB,mBAA6B;AAIrD,QAAI,CAAC,KAAK,QAAQ;AAChB,YAAM,IAAI,MAAM,mDAAmD;IACrE;AACA,qBAAiB,UAAU;MACzB,MAAM;MACN,OAAO;MACP,YAAY,EAAE,MAAM,KAAK,OAAO,MAAM,UAAU,UAAS;KAC1D;EACH;EAGgB,SAAM;AACpB,WAAO;;;;mBAIQ,CAAC,OAAO,MAAM,MAAM,IAAI,CAAU;;;;;qBAKhC,UAAU,UAAU,eAAe,KAAK,MAAM,CAAC,CAAC;;;;;;2CAM1B,KAAK,MAAM;;EAEpD;;AAnCW,6BAA0BC,aAAA;EADtC,cAAc,+BAA+B;GACjC,0BAA0B;;;;;;;;;ACChC,IAAM,qBAAN,MAAMC,4BAA2B,oBAAmB;EAIzD,cAAA;AACE,UAAK;AAHqB,SAAA,YAAY;AAItC,QAAI,CAAC,KAAK,QAAQ;AAChB,YAAM,IAAI,MAAM,2CAA2C;IAC7D;AACA,SAAK,YAAY,KAAK,eAAe,KAAK,IAAI;AAC9C,SAAK,oBAAoB;AACzB,SAAK,iBAAiBC,eAAc,eAAe;AACnD,SAAK,mBAAmB;AAGxB,SAAK,mBAAkB;AACvB,SAAK,YAAY,KACf,qBAAqB,aAAa,SAAS,MAAK;AAC9C,WAAK,mBAAkB;IACzB,CAAC,CAAC;AAGJ,qBAAiB,UAAU;MACzB,MAAM;MACN,OAAO;MACP,YAAY,EAAE,MAAM,KAAK,OAAO,MAAM,UAAU,MAAK;KACtD;EACH;EAGQ,qBAAkB;AACxB,SAAK,YAAY,CAAC,KAAK;EACzB;EAGQ,iBAAc;;AACpB,UAAI,UAAK,WAAL,mBAAa,gBAAe,KAAK,KAAK;AACxC,UAAI;AACF,aAAK,QAAQ;AACb,cAAM,EAAE,aAAa,KAAI,IAAK,KAAK;AACnC,cAAM,EAAE,UAAU,KAAI,IAAK,eAAe,mBAAmB,aAAa,KAAK,GAAG;AAClF,6BAAqB,aAAa,EAAE,MAAM,KAAI,CAAE;AAChD,6BAAqB,gBAAgB,KAAK,MAAM;AAChD,uBAAe,SAAS,UAAU,QAAQ;MAC5C,QAAQ;AACN,aAAK,QAAQ;MACf;IACF;EACF;;AA9C4BC,aAAA;EAA3B,MAAK;;AAFK,qBAAkBA,aAAA;EAD9B,cAAc,uBAAuB;GACzB,kBAAkB;;;;;;;;;ACYxB,IAAM,sBAAN,MAAMC,6BAA4B,WAAU;EAejD,cAAA;;AACE,UAAK;AAdC,SAAA,UAAS,sBAAiB,MAAM,SAAvB,mBAA6B;AAEtC,SAAA,cAA8B,CAAA;AAGrB,SAAA,WAAsB;AAEtB,SAAA,YAAwB,CAAA;AAExB,SAAA,gBAAgB,QAAQ,kBAAkB,MAAM,IAAI;AAEpD,SAAA,iBAAiB,kBAAkB,MAAM;AAIxD,SAAK,mBAAkB;AACvB,SAAK,qBAAoB;AAEzB,SAAK,YAAY,KACf,kBAAkB,aAAa,kBAAkB,SAAQ,KAAK,iBAAiB,GAAI,CAAC;EAExF;EAEgB,uBAAoB;AAClC,SAAK,YAAY,QAAQ,iBAAe,YAAW,CAAE;EACvD;EAGgB,SAAM;AACpB,WAAO;QACH,KAAK,eAAc,CAAE;aAChB,KAAK,iBAAgB,CAAE;QAC5B,KAAK,sBAAqB,CAAE;;EAElC;EAGQ,wBAAqB;;AAC3B,QAAI,GAAC,UAAK,mBAAL,mBAAqB,gBAAe;AACvC,aAAO;IACT;AAEA,WAAO;EACT;EAEQ,MAAM,qBAAqB,QAAQ,OAAK;AAQ9C,QAAI,KAAK,aAAa,aAAc,kBAAkB,MAAM,mBAAmB,CAAC,OAAQ;AACtF;IACF;AAEA,QAAI;AACF,YAAM,EAAE,iBAAiB,OAAM,IAAK,qBAAqB;AAEzD,UACE,SACA,kBAAkB,MAAM,kBACxB,eAAe,iBAAiB,eAAe,KAC/C,WAAW,cACX;AACA,cAAM,qBAAqB,qBAAoB;AAC/C,YAAI,CAAC,KAAK,eAAe;AACvB,0BAAgB,MAAK;QACvB;MACF;IACF,SAAS,OAAO;AACd,uBAAiB,UAAU;QACzB,MAAM;QACN,OAAO;QACP,YAAY,EAAE,UAAU,+BAAqB,YAAW,UAAS;OAClE;AACD,2BAAqB,WAAW,IAAI;AACpC,sBAAgB,UAAW,MAAoB,WAAW,kBAAkB;AAC5E,2BAAqB,kBAAiB;AACtC,uBAAiB,OAAM;IACzB;EACF;EAEQ,qBAAkB;AACxB,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,UAAU,KAAK,QAAQ;AAC5B,WAAK,WAAW;AAEhB;IACF;AAEA,QAAI,KAAK,UAAU;AACjB;IACF;AAEA,UAAM,EAAE,aAAa,cAAc,aAAa,UAAU,KAAI,IAAK,KAAK;AACxE,UAAM,cAAc,qCAAU,IAAI,CAAC,EAAE,YAAW,MAAO,aAAa,OAAO;AAC3E,UAAM,aAAa,CAAC,GAAI,OAAO,CAAC,IAAI,IAAK,eAAe,CAAA,CAAI;AAC5D,UAAM,YAAY,kBAAkB,MAAM,sBAAsB,QAAQ,WAAW;AACnF,UAAM,kBAAkB;AACxB,UAAM,UAAU;AAChB,UAAM,qBAAqB,qBAAqB,eAAe,UAAU;AACzE,UAAM,cAAc,aAAa;AACjC,UAAM,cAAc,gBAAgB,CAAC,eAAe,SAAQ;AAG5D,QAAI,eAAe,CAAC,gBAAgB,MAAM,YAAY;AACpD,WAAK,UAAU,KAAK,SAAS;IAC/B;AACA,QAAI,iBAAiB;AACnB,WAAK,UAAU,KAAK,eAAe,SAAQ,IAAK,WAAW,QAAQ;IACrE;AACA,QAAI,SAAS;AACX,WAAK,UAAU,KAAK,KAAK;IAC3B;AACA,QAAI,aAAa;AACf,WAAK,UAAU,KAAK,SAAS;IAC/B;AACA,QAAI,CAAC,eAAe,aAAa,CAAC,gBAAgB,MAAM,YAAY;AAClE,WAAK,UAAU,KAAK,aAAa;IACnC;AAEA,SAAK,WAAW,KAAK,UAAU,CAAC;EAClC;EAEQ,mBAAgB;AACtB,YAAQ,KAAK,UAAU;MACrB,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;gDACiC,MAAM,KAAK,qBAAqB,IAAI,CAAC;;;MAG/E,KAAK;AACH,eAAO;wDACyC,MAAM,KAAK,qBAAqB,IAAI,CAAC;;;MAGvF,KAAK;AACH,eAAO;MACT;AACE,eAAO;IACX;EACF;EAEQ,iBAAc;AACpB,UAAM,gBAAgB,KAAK,UAAU,SAAS;AAE9C,QAAI,CAAC,eAAe;AAClB,aAAO;IACT;AAEA,WAAO;;qBAEU,KAAK,SAAS;4BACP,KAAK,iBAAiB,KAAK,IAAI,CAAC;;;;EAI1D;EAEQ,MAAM,iBAAiB,UAAkB;;AAC/C,UAAM,aAAY,UAAK,eAAL,mBAAiB,cAAc;AACjD,QAAI,WAAW;AACb,YAAM,UAAU,QAAQ,CAAC,EAAE,SAAS,EAAC,GAAI,EAAE,SAAS,EAAC,CAAE,GAAG;QACxD,UAAU;QACV,MAAM;QACN,QAAQ;OACT,EAAE;AACH,WAAK,WAAW;AAChB,gBAAU,QAAQ,CAAC,EAAE,SAAS,EAAC,GAAI,EAAE,SAAS,EAAC,CAAE,GAAG;QAClD,UAAU;QACV,MAAM;QACN,QAAQ;OACT;IACH;EACF;;AA/KiBC,aAAA;EAAhB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AAbK,sBAAmBA,aAAA;EAD/B,cAAc,wBAAwB;GAC1B,mBAAmB;;;;;;;;;ACRzB,IAAM,2BAAN,MAAMC,kCAAiC,WAAU;EAAjD,cAAA;;AACY,SAAA,WAAW,eAAe,SAAQ;EA4BrD;EAzBkB,SAAM;AACpB,QAAI,KAAK,UAAU;AACjB,YAAM,EAAE,UAAU,YAAW,IAAK,cAAc;AAChD,YAAM,EAAE,cAAa,IAAK,kBAAkB;AAC5C,YAAM,SAAS,YAAY,iBAAgB;AAE3C,YAAM,iBACJ,SAAS,UAAU,YAAY,WAAU,+CAAe,WAAU,OAAO;AAE3E,aAAO;;;kBAGK,CAAC,OAAO,KAAK,KAAK,GAAG,CAAU;;UAEvC,iBAAiB,kDAAkD,IAAI;;;IAG7E;AAEA,WAAO,iDAAiD,CAAC,KAAK,KAAK,KAAK,GAAG,CAAU;;kDAEvC,CAAC,KAAK,KAAK,KAAK,GAAG,CAAU;;;EAG7E;;AA3BiBC,aAAA;EAAhB,MAAK;;AADK,2BAAwBA,aAAA;EADpC,cAAc,8BAA8B;GAChC,wBAAwB;;;ACN9B,IAAM,YAAY,MAAmB,IAAI,IAAG;AAKnD,IAAM,MAAN,MAAS;;AAmBT,IAAM,mCAAmC,oBAAI,QAAO;AAOpD,IAAM,eAAN,cAA2B,eAAc;EAKvC,OAAO,MAAoB;AACzB,WAAO;EACT;EAES,OAAO,MAAmB,CAACC,IAAG,GAA6B;AAnDtE;AAoDI,UAAM,aAAaA,SAAQ,KAAK;AAChC,QAAI,cAAc,KAAK,SAAS,QAAW;AAGzC,WAAK,gBAAgB,MAAS;IAChC;AACA,QAAI,cAAc,KAAK,uBAAuB,KAAK,UAAU;AAG3D,WAAK,OAAOA;AACZ,WAAK,YAAW,UAAK,YAAL,mBAAc;AAC9B,WAAK,gBAAiB,KAAK,WAAW,KAAK,OAAQ;IACrD;AACA,WAAO;EACT;EAEQ,gBAAgB,SAA4B;AAClD,QAAI,CAAC,KAAK,aAAa;AACrB,gBAAU;IACZ;AACA,QAAI,OAAO,KAAK,SAAS,YAAY;AAUnC,YAAM,UAAU,KAAK,YAAY;AACjC,UAAI,yBACF,iCAAiC,IAAI,OAAO;AAC9C,UAAI,2BAA2B,QAAW;AACxC,iCAAyB,oBAAI,QAAO;AACpC,yCAAiC,IAAI,SAAS,sBAAsB;MACtE;AACA,UAAI,uBAAuB,IAAI,KAAK,IAAI,MAAM,QAAW;AACvD,aAAK,KAAK,KAAK,KAAK,UAAU,MAAS;MACzC;AACA,6BAAuB,IAAI,KAAK,MAAM,OAAO;AAE7C,UAAI,YAAY,QAAW;AACzB,aAAK,KAAK,KAAK,KAAK,UAAU,OAAO;MACvC;IACF,OAAO;AACJ,WAAK,KAAsB,QAAQ;IACtC;EACF;EAEA,IAAY,qBAAkB;AAtGhC;AAuGI,WAAO,OAAO,KAAK,SAAS,cACxB,sCACG,IAAI,KAAK,YAAY,UAAU,MADlC,mBAEI,IAAI,KAAK,SACb,UAAK,SAAL,mBAAW;EACjB;EAES,eAAY;AAKnB,QAAI,KAAK,uBAAuB,KAAK,UAAU;AAC7C,WAAK,gBAAgB,MAAS;IAChC;EACF;EAES,cAAW;AAGlB,SAAK,gBAAgB,KAAK,QAAQ;EACpC;;AA4BK,IAAM,MAAM,UAAU,YAAY;;;ACtJzC,IAAAC,mBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACQR,IAAM,YAAN,MAAMC,mBAAkB,WAAU;EAAlC,cAAA;;AAIE,SAAA,kBAAyC,UAAS;AAGrB,SAAA,UAAoB;EA2B1D;EAxBkB,SAAM;AACpB,WAAO;;;YAGC,IAAI,KAAK,eAAe,CAAC;;qBAEhB,UAAU,KAAK,OAAO,CAAC;oBACxB,KAAK,oBAAoB,KAAK,IAAI,CAAC;;;;;EAKrD;EAGQ,sBAAmB;;AACzB,SAAK,cACH,IAAI,YAAY,gBAAgB;MAC9B,SAAQ,UAAK,gBAAgB,UAArB,mBAA4B;MACpC,SAAS;MACT,UAAU;KACX,CAAC;EAEN;;AAhCuB,UAAA,SAAS,CAAC,aAAa,eAAe,aAAaC,gBAAM;AAM5CC,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAPhB,YAASA,aAAA;EADrB,cAAc,YAAY;GACd,SAAS;;;ACRtB,IAAAC,mBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACSR,IAAM,qBAAN,MAAMC,4BAA2B,WAAU;EAA3C,cAAA;;AAI+B,SAAA,UAAoB;EAW1D;EARkB,SAAM;AACpB,WAAO;;;+BAGoB,UAAU,KAAK,OAAO,CAAC;;;EAGpD;;AAbuB,mBAAA,SAAS,CAAC,aAAa,eAAeC,gBAAM;AAG/BC,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAJhB,qBAAkBA,aAAA;EAD9B,cAAc,sBAAsB;GACxB,kBAAkB;;;ACT/B,IAAAC,mBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACQR,IAAM,kBAAN,MAAMC,yBAAwB,WAAU;EAAxC,cAAA;;AAIc,SAAA,OAAiB;EAUtC;EAPkB,SAAM;AACpB,WAAO;;oDAEyC,KAAK,IAAI;;;EAG3D;;AAZuB,gBAAA,SAAS,CAAC,aAAa,eAAeC,gBAAM;AAGhDC,aAAA;EAAlB,SAAQ;;AAJE,kBAAeA,aAAA;EAD3B,cAAc,mBAAmB;GACrB,eAAe;;;ACR5B,IAAAC,mBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACWR,IAAM,eAAN,MAAMC,sBAAqB,WAAU;EAArC,cAAA;;AAIE,SAAA,kBAAyC,UAAS;AAGtC,SAAA,OAAoD;AAInC,SAAA,WAAW;AAE5B,SAAA,cAAc;AAEd,SAAA,OAAkB;AAIlB,SAAA,QAAiB;EAsDtC;EA/CkB,SAAM;AACpB,UAAM,aAAa,qBAAqB,KAAK,iBAAiB;AAC9D,UAAM,YAAY,YAAY,KAAK,IAAI;AACvC,UAAM,UAAU;MACd,CAAC,SAAS,GAAG;MACb,CAAC,UAAU,GAAG,QAAQ,KAAK,iBAAiB;;AAG9C,WAAO,OAAO,KAAK,aAAY,CAAE;;;UAG3B,IAAI,KAAK,eAAe,CAAC;gBACnB,SAAS,OAAO,CAAC;eAClB,KAAK,IAAI;uBACD,UAAU,KAAK,YAAY,CAAC;oBAC/B,KAAK,QAAQ;sBACX,KAAK,WAAW;iBACrB,KAAK,yBAAyB,KAAK,IAAI,CAAC;iBACxC,KAAK,SAAS,EAAE;mBACd,UAAU,KAAK,MAAM,CAAC;;;EAGvC;EAGQ,eAAY;AAClB,QAAI,KAAK,MAAM;AACb,aAAO;qBACQ,KAAK,IAAI;eACf,KAAK,IAAI;;eAET,KAAK,IAAI;;IAEpB;AAEA,WAAO;EACT;EAEQ,2BAAwB;;AAC9B,SAAK,cACH,IAAI,YAAY,eAAe;MAC7B,SAAQ,UAAK,gBAAgB,UAArB,mBAA4B;MACpC,SAAS;MACT,UAAU;KACX,CAAC;EAEN;;AAvEuB,aAAA,SAAS,CAAC,aAAa,eAAeC,gBAAM;AAMhDC,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAE2BA,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAERA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAvBE,eAAYA,aAAA;EADxB,cAAc,gBAAgB;GAClB,YAAY;;;ACXzB,IAAAC,mBAAe;;;;;;;;;;;;;;;ACSR,IAAM,eAAN,MAAMC,sBAAqB,WAAU;EAArC,cAAA;;AAIE,SAAA,oBAAuC,UAAS;EA4BzD;EAzBkB,SAAM;AACpB,WAAO;;UAED,IAAI,KAAK,iBAAiB,CAAC;;;;;;;oCAOD,KAAK,UAAU;;;EAGjD;EAGQ,aAAU;AAChB,UAAM,iBAAiB,KAAK,kBAAkB;AAC9C,UAAM,eAAe,iDAAgB,gBAAgB;AACrD,QAAI,cAAc;AAChB,mBAAa,QAAQ;AACrB,mBAAa,MAAK;AAClB,mBAAa,cAAc,IAAI,MAAM,OAAO,CAAC;IAC/C;EACF;;AA9BuB,aAAA,SAAS,CAAC,aAAaC,gBAAM;AADzC,eAAYC,aAAA;EADxB,cAAc,gBAAgB;GAClB,YAAY;;;ACTlB,IAAM,eAAe;;;;;;;ACA5B,IAAAC,mBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACSR,IAAM,sBAAN,MAAMC,6BAA4B,WAAU;EAA5C,cAAA;;AAIc,SAAA,OAAuB;EAuB5C;EApBkB,SAAM;AACpB,WAAO;QACH,KAAK,gBAAe,CAAE;;;EAG5B;EAEQ,kBAAe;AACrB,QAAI,KAAK,SAAS,WAAW;AAC3B,aAAO;sBACS,KAAK,IAAI;;;;;UAKrB,YAAY;IAClB;AAEA,WAAO;EACT;;AAzBuB,oBAAA,SAAS,CAAC,aAAa,eAAeC,gBAAM;AAGhDC,aAAA;EAAlB,SAAQ;;AAJE,sBAAmBA,aAAA;EAD/B,cAAc,wBAAwB;GAC1B,mBAAmB;;;ACThC,IAAAC,mBAAe;;;;;;;;;;;;;;;ACQR,IAAM,UAAN,MAAMC,iBAAgB,WAAU;EA2BrB,SAAM;AACpB,SAAK,MAAM,UAAU;4BACG,KAAK,gBAAgB;+BAClB,KAAK,mBAAmB;uBAChC,KAAK,YAAY;qBACnB,KAAK,UAAU;yBACX,KAAK,cAAc;uBACrB,KAAK,YAAY;oBACpB,KAAK,aAAa,qBAAqB,KAAK,SAAS,GAAG;iBAC3D,KAAK,UAAU,qBAAqB,KAAK,MAAM,GAAG;aACtD,KAAK,OAAO,qBAAqB,KAAK,GAAG,GAAG;qBACpC,KAAK,WAAW,aAAa,iBAAiB,KAAK,SAAS,CAAC,CAAC;uBAC5D,KAAK,WAAW,aAAa,iBAAiB,KAAK,SAAS,CAAC,CAAC;wBAC7D,KAAK,WAAW,aAAa,iBAAiB,KAAK,SAAS,CAAC,CAAC;sBAChE,KAAK,WAAW,aAAa,iBAAiB,KAAK,SAAS,CAAC,CAAC;oBAChE,KAAK,UAAU,aAAa,iBAAiB,KAAK,QAAQ,CAAC,CAAC;sBAC1D,KAAK,UAAU,aAAa,iBAAiB,KAAK,QAAQ,CAAC,CAAC;uBAC3D,KAAK,UAAU,aAAa,iBAAiB,KAAK,QAAQ,CAAC,CAAC;qBAC9D,KAAK,UAAU,aAAa,iBAAiB,KAAK,QAAQ,CAAC,CAAC;;AAG7E,WAAO;EACT;;AAhDuB,QAAA,SAAS,CAAC,aAAaC,gBAAM;AAGjCC,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAxBE,UAAOA,aAAA;EADnB,cAAc,UAAU;GACZ,OAAO;;;ACRpB,IAAAC,mBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACaR,IAAM,wBAAN,MAAMC,+BAA8B,WAAU;EAgBnD,cAAA;AACE,UAAK;AAbC,SAAA,WAAW,IAAI,qBAAqB,MAAM,MAAS;AAG1C,SAAA,UAAU;AAEV,SAAA,WAA+B;AAE/B,SAAA,eAAe;AAEZ,SAAA,SAA0D;AAK5E,SAAK,WAAW,IAAI,qBAClB,aAAU;AACR,cAAQ,QAAQ,WAAQ;AACtB,YAAI,MAAM,gBAAgB;AACxB,eAAK,UAAU;AACf,eAAK,cAAa;QACpB,OAAO;AACL,eAAK,UAAU;QACjB;MACF,CAAC;IACH,GACA,EAAE,WAAW,KAAI,CAAE;EAEvB;EAEgB,eAAY;AAC1B,SAAK,SAAS,QAAQ,IAAI;EAC5B;EAEgB,uBAAoB;AAClC,SAAK,SAAS,WAAU;EAC1B;EAGgB,SAAM;;AACpB,UAAM,cAAY,UAAK,WAAL,mBAAa,gBAAe;AAE9C,WAAO;;UAED,KAAK,cAAa,CAAE;;;;;oBAKV,UAAU,YAAY,cAAc,MAAS,CAAC;gBACnD,UAAK,WAAL,mBAAa,IAAI;;YAEpB,YAAY,kEAAkE,IAAI;;;;EAI5F;EAEQ,gBAAa;;AACnB,QAAK,CAAC,KAAK,WAAW,CAAC,KAAK,YAAa,KAAK,cAAc;AAC1D,aAAO,KAAK,gBAAe;IAC7B;AAEA,WAAO;;;mBAGQ,UAAU,KAAK,QAAQ,CAAC;gBAC5B,UAAK,WAAL,mBAAa,IAAI;sBACX,UAAK,WAAL,mBAAa,SAAS;;;;;EAKzC;EAEQ,kBAAe;AACrB,WAAO;EACT;EAEQ,MAAM,gBAAa;AACzB,QAAI,CAAC,KAAK,QAAQ;AAChB;IACF;AACA,SAAK,WAAW,UAAU,eAAe,KAAK,MAAM;AAEpD,QAAI,KAAK,UAAU;AACjB;IACF;AAEA,SAAK,eAAe;AACpB,SAAK,WAAW,MAAM,UAAU,iBAAiB,KAAK,OAAO,QAAQ;AACrE,SAAK,eAAe;EACtB;;AA9FuB,sBAAA,SAASC;AAMfC,aAAA;EAAhB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AAEcA,aAAA;EAAnB,SAAQ;;AAbE,wBAAqBA,aAAA;EADjC,cAAc,2BAA2B;GAC7B,qBAAqB;;;ACblC,IAAAC,mBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACaf,IAAM,eAAe;AAGd,IAAM,oBAAN,MAAMC,2BAA0B,WAAU;EAmB/C,cAAA;AACE,UAAK;AAhBC,SAAA,cAA8B,CAAA;AAE9B,SAAA,qBAA4C;AAGnC,SAAA,UAAU,CAAC,cAAc,MAAM,QAAQ;AAEvC,SAAA,UAAU,cAAc,MAAM;AAE9B,SAAA,cAAc,cAAc,MAAM;AAElC,SAAA,WAAW,cAAc,MAAM;AAE/B,SAAA,kBAAkB,cAAc,MAAM;AAIrD,SAAK,YAAY,KACf,GAAG;MACD,cAAc,aAAa,WAAW,SAAQ,KAAK,UAAU,GAAI;MACjE,cAAc,aAAa,eAAe,SAAQ,KAAK,cAAc,GAAI;MACzE,cAAc,aAAa,YAAY,SAAQ,KAAK,WAAW,GAAI;MACnE,cAAc,aAAa,mBAAmB,SAAQ,KAAK,kBAAkB,GAAI;KAClF;EAEL;EAEgB,eAAY;AAC1B,SAAK,aAAY;AACjB,SAAK,yBAAwB;EAC/B;EAEgB,uBAAoB;;AAClC,SAAK,YAAY,QAAQ,iBAAe,YAAW,CAAE;AACrD,eAAK,uBAAL,mBAAyB;EAC3B;EAGgB,SAAM;AACpB,WAAO;;sBAEW,CAAC,KAAK,OAAO;mBAChB,CAAC,KAAK,KAAK,KAAK,GAAG,CAAU;;;;;UAKtC,KAAK,UAAU,KAAK,gBAAgB,EAAE,IAAI,KAAK,gBAAe,CAAE;UAChE,KAAK,yBAAwB,CAAE;;;EAGvC;EAGQ,MAAM,eAAY;;AACxB,SAAK,UAAU;AACf,UAAM,UAAS,UAAK,eAAL,mBAAiB,cAAc;AAC9C,QAAI,QAAQ;AACV,YAAM,cAAc,mBAAmB,EAAE,MAAM,EAAC,CAAE;AAClD,YAAM,OAAO,QAAQ,CAAC,EAAE,SAAS,EAAC,GAAI,EAAE,SAAS,EAAC,CAAE,GAAG;QACrD,UAAU;QACV,MAAM;QACN,QAAQ;OACT,EAAE;AACH,WAAK,UAAU;AACf,aAAO,QAAQ,CAAC,EAAE,SAAS,EAAC,GAAI,EAAE,SAAS,EAAC,CAAE,GAAG;QAC/C,UAAU;QACV,MAAM;QACN,QAAQ;OACT;IACH;EACF;EAEQ,gBAAgB,OAAe,IAAW;AAChD,WAAO,CAAC,GAAG,MAAM,KAAK,CAAC,EAAE,IACvB,MAAM;mDACuC,UAAU,EAAE,CAAC;OACzD;EAEL;EAEQ,kBAAe;;AACrB,UAAM,YACJ,UAAK,oBAAL,mBAAsB,UAAS,IAC3B,eAAe,SACb,CAAC,GAAG,KAAK,UAAU,GAAG,KAAK,aAAa,GAAG,KAAK,eAAe,GAC/D,IAAI,IAEN,eAAe,SAAS,CAAC,GAAG,KAAK,UAAU,GAAG,KAAK,aAAa,GAAG,KAAK,OAAO,GAAG,IAAI;AAC5F,UAAM,uBAAuB,WAAW,uBAAuB,OAAO;AAEtE,WAAO,qBAAqB,IAC1B,YAAU;;mBAEG,MAAM,KAAK,gBAAgB,MAAM,CAAC;oBACjC,MAAM;;OAEnB;EAEL;EAEQ,2BAAwB;AAC9B,UAAM,EAAE,SAAS,aAAa,UAAU,MAAK,IAAK,cAAc;AAChE,UAAM,UAAU,OAAO,aAAa,MAAM,IAAI;AAC9C,UAAM,iBAAiB,QAAQ,SAAS,YAAY;AACpD,UAAM,cAAc,KAAK,KAAK,iBAAiB,OAAO;AACtD,QAAI,eAAe,cAAc,UAAU,iBAAiB;AAC5D,oBAAgB,QAAQ,SAAS,SAAS,SAAS,UAAU;AAE7D,QAAI,UAAU,KAAK,SAAS,SAAS,GAAG;AACtC,aAAO;IACT;AAEA,QAAI,UAAU,KAAK,CAAC,GAAG,UAAU,GAAG,SAAS,GAAG,WAAW,EAAE,SAAS,OAAO;AAC3E,aAAO,KAAK,gBAAgB,cAAc,YAAY;IACxD;AAEA,WAAO;EACT;EAEQ,2BAAwB;;AAC9B,UAAM,YAAW,UAAK,eAAL,mBAAiB,cAAc,IAAI,YAAY;AAChE,QAAI,UAAU;AACZ,WAAK,qBAAqB,IAAI,qBAAqB,CAAC,CAAC,OAAO,MAAK;AAC/D,aAAI,mCAAS,mBAAkB,CAAC,KAAK,SAAS;AAC5C,gBAAM,EAAE,MAAM,OAAO,QAAO,IAAK,cAAc;AAC/C,cAAI,QAAQ,SAAS,OAAO;AAC1B,0BAAc,mBAAmB,EAAE,MAAM,OAAO,EAAC,CAAE;UACrD;QACF;MACF,CAAC;AACD,WAAK,mBAAmB,QAAQ,QAAQ;IAC1C;EACF;EAEQ,gBAAgB,QAAgB;AACtC,wBAAoB,sBAAsB,MAAM;EAClD;;AA5IuB,kBAAA,SAASC;AAQfC,aAAA;EAAhB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AAjBK,oBAAiBA,aAAA;EAD7B,cAAc,sBAAsB;GACxB,iBAAiB;;;AChB9B,IAAAC,mBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACeR,IAAM,sBAAN,MAAMC,6BAA4B,WAAU;EAA5C,cAAA;;AAIG,SAAA,YAAY;AAEZ,SAAA,YAAwB;AAGf,SAAA,UAAU;AAEP,SAAA,QAAQ;EA2E9B;EAtEkB,SAAM;AACpB,SAAK,SAAQ;AAEb,WAAO,KAAK,UACR,uEACA,KAAK,gBAAe;EAC1B;EAGQ,MAAM,WAAQ;AACpB,QAAI,KAAK,MAAM,KAAI,MAAO,KAAK,UAAU,KAAI,KAAM,KAAK,UAAU,KAAK,WAAW;AAChF,WAAK,YAAY,KAAK;AACtB,WAAK,YAAY,KAAK;AACtB,WAAK,UAAU;AACf,YAAM,cAAc,aAAa,EAAE,QAAQ,KAAK,OAAO,OAAO,KAAK,MAAK,CAAE;AAC1E,WAAK,UAAU;IACjB;EACF;EAEQ,kBAAe;AACrB,UAAM,EAAE,OAAM,IAAK,cAAc;AACjC,UAAM,UAAU,WAAW,uBAAuB,MAAM;AAExD,QAAI,CAAC,OAAO,QAAQ;AAClB,aAAO;;;;;;;;;;;;;;;;;;;;IAoBT;AAEA,WAAO;;;mBAGQ,CAAC,KAAK,KAAK,KAAK,GAAG,CAAU;;;;;UAKtC,QAAQ,IACR,YAAU;;uBAEG,MAAM,KAAK,gBAAgB,MAAM,CAAC;wBACjC,MAAM;gDACkB,OAAO,EAAE;;WAE9C,CACF;;;EAGP;EAEQ,gBAAgB,QAAgB;AACtC,wBAAoB,sBAAsB,MAAM;EAClD;;AApFuB,oBAAA,SAASC;AAQfC,aAAA;EAAhB,MAAK;;AAEcA,aAAA;EAAnB,SAAQ;;AAEWA,aAAA;EAAnB,SAAQ;;AAbE,sBAAmBA,aAAA;EAD/B,cAAc,wBAAwB;GAC1B,mBAAmB;;;;;;;;;ACGzB,IAAM,oBAAN,MAAMC,2BAA0B,WAAU;EAA1C,cAAA;;AAEY,SAAA,SAAS;AA8ClB,SAAA,oBAAoB,eAAe,SAAS,CAAC,UAAiB;AACpE,WAAK,SAAS;IAChB,CAAC;EAyBH;EApEkB,SAAM;AACpB,UAAM,WAAW,KAAK,OAAO,UAAU;AAEvC,WAAO;2BACgB,CAAC,KAAK,KAAK,KAAK,GAAG,CAAC;uCACR,KAAK,cAAc,KAAK,IAAI,CAAC;;qBAE/C,KAAK,KAAK;mBACZ,KAAK,QAAQ,KAAK,IAAI,CAAC;;;UAGhC,KAAK,iBAAgB,CAAE;;QAEzB,YAAY,KAAK,QACf;oBACU,KAAK,MAAM;oBACX,UAAU,KAAK,KAAK,CAAC;wCAE/B,mCAAmC,UAAU,KAAK,KAAK,CAAC,0BAA0B;;EAE1F;EAGQ,cAAc,OAA0B;AAC9C,SAAK,kBAAkB,MAAM,MAAM;EACrC;EAEQ,UAAO;AACb,QAAI,KAAK,UAAU,aAAa;AAC9B,WAAK,QAAQ;AAEb;IACF;AAEA,SAAK,QAAQ;AACb,oBAAgB,QAAQ,gCAAgC;MACtD,MAAM;MACN,WAAW;KACZ;EACH;EAMQ,mBAAgB;AACtB,QAAI,eAAe,SAAQ,GAAI;AAC7B,aAAO;;;;;;;;;;mBAUM,KAAK,kBAAkB,KAAK,IAAI,CAAC;;;IAGhD;AAEA,WAAO;EACT;EAEQ,oBAAiB;AACvB,qBAAiB,KAAK,yBAAyB;EACjD;;AAxEiBC,aAAA;EAAhB,MAAK;;AAEWA,aAAA;EAAhB,MAAK;;AAJK,oBAAiBA,aAAA;EAD7B,cAAc,sBAAsB;GACxB,iBAAiB;;;AClB9B,IAAAC,mBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACcR,IAAM,cAAN,MAAMC,qBAAoB,WAAU;EAApC,cAAA;;AAQc,SAAA,SAAkB;AAElB,SAAA,UAA4B;AAIX,SAAA,WAAW;AAE5B,SAAA,WAAoB;AAEpB,SAAA,MAAe;AAEE,SAAA,UAAU;AAEV,SAAA,UAAU;EAmEhD;EAhEkB,SAAM;AACpB,WAAO;;oBAES,KAAK,UAAU,OAAO,QAAQ,KAAK,QAAQ,CAAC;uBACzC,KAAK,OAAO;2BACR,UAAU,KAAK,WAAW,CAAC;mBACnC,UAAU,KAAK,MAAM,CAAC;;UAE/B,KAAK,gBAAe,CAAE,IAAI,KAAK,eAAc,CAAE;;;;UAI/C,KAAK,gBAAe,CAAE;;;EAG9B;EAGO,iBAAc;AACnB,QAAI,KAAK,YAAY,WAAW,KAAK,UAAU;AAC7C,aAAO,sBAAsB,KAAK,QAAQ,QAAQ,KAAK,OAAO,WAAW;IAC3E;AACA,QAAI,KAAK,gBAAgB,YAAY,KAAK,QAAQ,KAAK,YAAY,QAAQ;AACzE,aAAO,sBAAsB,KAAK,IAAI;IACxC;AACA,QAAI,KAAK,YAAY,UAAU,KAAK,QAAQ,KAAK,aAAa;AAC5D,YAAM,QAAQ,CAAC,QAAQ,aAAa,EAAE,SAAS,KAAK,WAAW,IAAI,eAAe;AAClF,YAAM,OAAO,KAAK,gBAAgB,gBAAgB,QAAQ;AAC1D,YAAM,WAAW,KAAK,WAAW,KAAK,WAAW;AAEjD,aAAO;;yBAEY,KAAK,WAAW;iBACxB,KAAK,IAAI;qBACL,QAAQ;;sBAEP,KAAK;4BACC,KAAK;iBAChB,IAAI;;;IAGjB;AAEA,WAAO;EACT;EAEO,kBAAe;AACpB,QAAI,KAAK,SAAS;AAChB,aAAO;;;;IAIT;AAEA,WAAO;EACT;EAEO,kBAAe;AACpB,QAAI,KAAK,SAAS;AAChB,aAAO;IACT;AAEA,WAAO;EACT;;AAvFuB,YAAA,SAAS,CAAC,aAAa,eAAeC,gBAAM;AAGhDC,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAE2BA,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAERA,aAAA;EAAlB,SAAQ;;AAEUA,aAAA;EAAlB,SAAQ;;AAE2BA,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAESA,aAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAtBhB,cAAWA,aAAA;EADvB,cAAc,eAAe;GACjB,WAAW;;;;;;;;;ACPjB,IAAM,mBAAN,MAAMC,0BAAyB,WAAU;EAAzC,cAAA;;;AAEG,SAAA,UAAS,sBAAiB,MAAM,SAAvB,mBAA6B;EA0GhD;EAvGkB,SAAM;AACpB,QAAI,CAAC,KAAK,QAAQ;AAChB,YAAM,IAAI,MAAM,oBAAoB;IACtC;AAEA,WAAO;2DACgD,CAAC,KAAK,KAAK,KAAK,GAAG,CAAU;UAC9E,KAAK,eAAc,CAAE,IAAI,KAAK,YAAW,CAAE,IAAI,KAAK,gBAAe,CAAE;UACrE,KAAK,iBAAgB,CAAE;;;EAG/B;EAGQ,iBAAc;;AACpB,QAAI,GAAC,UAAK,WAAL,mBAAa,eAAc;AAC9B,aAAO;IACT;AAEA,WAAO;;;;eAII,KAAK,cAAc,KAAK,IAAI,CAAC;;;;;EAK1C;EAEQ,cAAW;;AACjB,QAAI,GAAC,UAAK,WAAL,mBAAa,YAAW;AAC3B,aAAO;IACT;AAEA,WAAO;;;;eAII,KAAK,WAAW,KAAK,IAAI,CAAC;;;;;EAKvC;EAEQ,kBAAe;;AACrB,QAAI,GAAC,UAAK,WAAL,mBAAa,aAAY;AAC5B,aAAO;IACT;AAEA,WAAO;;;;eAII,KAAK,YAAY,KAAK,IAAI,CAAC;;;;;EAKxC;EAEQ,mBAAgB;;AACtB,QAAI,GAAC,UAAK,WAAL,mBAAa,WAAU;AAC1B,aAAO;IACT;AAEA,WAAO;;;;;iBAKM,KAAK,WAAW,KAAK,IAAI,CAAC;;;;;;EAMzC;EAEQ,gBAAa;;AACnB,SAAI,UAAK,WAAL,mBAAa,cAAc;AAC7B,qBAAe,SAAS,KAAK,OAAO,cAAc,QAAQ;IAC5D;EACF;EAEQ,aAAU;;AAChB,SAAI,UAAK,WAAL,mBAAa,WAAW;AAC1B,qBAAe,SAAS,KAAK,OAAO,WAAW,QAAQ;IACzD;EACF;EAEQ,cAAW;;AACjB,SAAI,UAAK,WAAL,mBAAa,YAAY;AAC3B,qBAAe,SAAS,KAAK,OAAO,YAAY,QAAQ;IAC1D;EACF;EAEQ,aAAU;;AAChB,SAAI,UAAK,WAAL,mBAAa,UAAU;AACzB,qBAAe,SAAS,KAAK,OAAO,UAAU,QAAQ;IACxD;EACF;;AA3GW,mBAAgBC,aAAA;EAD5B,cAAc,oBAAoB;GACtB,gBAAgB;", "names": ["module", "require_utils", "WuiWalletImage", "styles_default", "WuiAllWalletsImage", "styles_default", "__decorate", "styles_default", "WuiListWallet", "styles_default", "__decorate", "W3mAllWalletsWidget", "__decorate", "W3mConnectAnnouncedWidget", "__decorate", "W3mConnectCustomWidget", "__decorate", "W3mConnectExternalWidget", "__decorate", "W3mConnectFeaturedWidget", "__decorate", "W3mConnectInjectedWidget", "__decorate", "W3mConnectMultiChainWidget", "__decorate", "W3mConnectRecentWidget", "__decorate", "W3mConnectRecommendedWidget", "__decorate", "W3mConnectWalletConnectWidget", "__decorate", "styles_default", "W3mConnectorList", "styles_default", "__decorate", "styles_default", "WuiTabs", "styles_default", "__decorate", "W3mConnectingHeader", "__decorate", "styles_default", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles_default", "__decorate", "styles_default", "WuiLink", "styles_default", "__decorate", "styles_default", "WuiLoading<PERSON><PERSON><PERSON><PERSON>", "styles_default", "__decorate", "styles_default", "WuiChipButton", "styles_default", "__decorate", "styles_default", "WuiCtaButton", "styles_default", "__decorate", "styles_default", "W3mMobileDownloadLinks", "styles_default", "__decorate", "styles_default", "_a", "styles_default", "__decorate", "W3mConnectingWcBrowser", "_a", "__decorate", "W3mConnectingWcDesktop", "__decorate", "W3mConnectingWcMobile", "Constants<PERSON><PERSON>", "__decorate", "QRCodeUtil", "styles_default", "WuiQrCode", "styles_default", "__decorate", "styles_default", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles_default", "__decorate", "styles_default", "WuiUxByReown", "styles_default", "__decorate", "styles_default", "W3mConnectingWcQrcode", "styles_default", "__decorate", "W3mConnectingWcUnsupported", "__decorate", "W3mConnectingWcWeb", "Constants<PERSON><PERSON>", "__decorate", "W3mConnectingWcView", "__decorate", "W3mConnectingWcBasicView", "__decorate", "ref", "styles_default", "WuiSwitch", "styles_default", "__decorate", "styles_default", "WuiCertifiedSwitch", "styles_default", "__decorate", "styles_default", "WuiInputElement", "styles_default", "__decorate", "styles_default", "WuiInputText", "styles_default", "__decorate", "styles_default", "WuiSearchBar", "styles_default", "__decorate", "styles_default", "WuiCardSelectLoader", "styles_default", "__decorate", "styles_default", "Wu<PERSON><PERSON><PERSON>", "styles_default", "__decorate", "styles_default", "W3mAllWalletsListItem", "styles_default", "__decorate", "styles_default", "W3mAllWalletsList", "styles_default", "__decorate", "styles_default", "W3mAllWalletsSearch", "styles_default", "__decorate", "W3mAllWalletsView", "__decorate", "styles_default", "WuiListItem", "styles_default", "__decorate", "W3mDownloadsView", "__decorate"]}