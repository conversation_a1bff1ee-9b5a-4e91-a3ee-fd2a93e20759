{"version": 3, "sources": ["../../@noble/hashes/src/_u64.ts", "../../@noble/hashes/src/crypto.ts", "../../@noble/hashes/src/utils.ts", "../../@noble/hashes/src/sha3.ts"], "sourcesContent": ["/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n\nfunction fromBig(\n  n: bigint,\n  le = false\n): {\n  h: number;\n  l: number;\n} {\n  if (le) return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n  return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\n\nfunction split(lst: bigint[], le = false): Uint32Array[] {\n  const len = lst.length;\n  let Ah = new Uint32Array(len);\n  let Al = new Uint32Array(len);\n  for (let i = 0; i < len; i++) {\n    const { h, l } = fromBig(lst[i], le);\n    [Ah[i], Al[i]] = [h, l];\n  }\n  return [Ah, Al];\n}\n\nconst toBig = (h: number, l: number): bigint => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h: number, _l: number, s: number): number => h >>> s;\nconst shrSL = (h: number, l: number, s: number): number => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h: number, l: number, s: number): number => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h: number, l: number, s: number): number => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h: number, l: number, s: number): number => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h: number, l: number, s: number): number => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h: number, l: number): number => l;\nconst rotr32L = (h: number, _l: number): number => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h: number, l: number, s: number): number => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h: number, l: number, s: number): number => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h: number, l: number, s: number): number => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h: number, l: number, s: number): number => (h << (s - 32)) | (l >>> (64 - s));\n\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(\n  Ah: number,\n  Al: number,\n  Bh: number,\n  Bl: number\n): {\n  h: number;\n  l: number;\n} {\n  const l = (Al >>> 0) + (Bl >>> 0);\n  return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al: number, Bl: number, Cl: number): number => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low: number, Ah: number, Bh: number, Ch: number): number =>\n  (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al: number, Bl: number, Cl: number, Dl: number): number =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number): number =>\n  (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al: number, Bl: number, Cl: number, Dl: number, El: number): number =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number): number =>\n  (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n\n// prettier-ignore\nexport {\n  add, add3H, add3L, add4H, add4L, add5H, add5L, fromBig, rotlBH, rotlBL, rotlSH, rotlSL, rotr32H, rotr32L, rotrBH, rotrBL, rotrSH, rotrSL, shrSH, shrSL, split, toBig\n};\n// prettier-ignore\nconst u64: { fromBig: typeof fromBig; split: typeof split; toBig: (h: number, l: number) => bigint; shrSH: (h: number, _l: number, s: number) => number; shrSL: (h: number, l: number, s: number) => number; rotrSH: (h: number, l: number, s: number) => number; rotrSL: (h: number, l: number, s: number) => number; rotrBH: (h: number, l: number, s: number) => number; rotrBL: (h: number, l: number, s: number) => number; rotr32H: (_h: number, l: number) => number; rotr32L: (h: number, _l: number) => number; rotlSH: (h: number, l: number, s: number) => number; rotlSL: (h: number, l: number, s: number) => number; rotlBH: (h: number, l: number, s: number) => number; rotlBL: (h: number, l: number, s: number) => number; add: typeof add; add3L: (Al: number, Bl: number, Cl: number) => number; add3H: (low: number, Ah: number, Bh: number, Ch: number) => number; add4L: (Al: number, Bl: number, Cl: number, Dl: number) => number; add4H: (low: number, Ah: number, Bh: number, Ch: number, Dh: number) => number; add5H: (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number) => number; add5L: (Al: number, Bl: number, Cl: number, Dl: number, El: number) => number; } = {\n  fromBig, split, toBig,\n  shrSH, shrSL,\n  rotrSH, rotrSL, rotrBH, rotrBL,\n  rotr32H, rotr32L,\n  rotlSH, rotlSL, rotlBH, rotlBL,\n  add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n", "/**\n * Internal webcrypto alias.\n * We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n * See utils.ts for details.\n * @module\n */\ndeclare const globalThis: Record<string, any> | undefined;\nexport const crypto: any =\n  typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined;\n", "/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\n\n/** Checks if something is Uint8Array. Be careful: nodejs <PERSON>uffer will return true. */\nexport function isBytes(a: unknown): a is Uint8Array {\n  return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n\n/** Asserts something is positive integer. */\nexport function anumber(n: number): void {\n  if (!Number.isSafeInteger(n) || n < 0) throw new Error('positive integer expected, got ' + n);\n}\n\n/** Asserts something is Uint8Array. */\nexport function abytes(b: Uint8Array | undefined, ...lengths: number[]): void {\n  if (!isBytes(b)) throw new Error('Uint8Array expected');\n  if (lengths.length > 0 && !lengths.includes(b.length))\n    throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n\n/** Asserts something is hash */\nexport function ahash(h: IHash): void {\n  if (typeof h !== 'function' || typeof h.create !== 'function')\n    throw new Error('Hash should be wrapped by utils.createHasher');\n  anumber(h.outputLen);\n  anumber(h.blockLen);\n}\n\n/** Asserts a hash instance has not been destroyed / finished */\nexport function aexists(instance: any, checkFinished = true): void {\n  if (instance.destroyed) throw new Error('Hash instance has been destroyed');\n  if (checkFinished && instance.finished) throw new Error('Hash#digest() has already been called');\n}\n\n/** Asserts output is properly-sized byte array */\nexport function aoutput(out: any, instance: any): void {\n  abytes(out);\n  const min = instance.outputLen;\n  if (out.length < min) {\n    throw new Error('digestInto() expects output buffer of length at least ' + min);\n  }\n}\n\n/** Generic type encompassing 8/16/32-byte arrays - but not 64-byte. */\n// prettier-ignore\nexport type TypedArray = Int8Array | Uint8ClampedArray | Uint8Array |\n  Uint16Array | Int16Array | Uint32Array | Int32Array;\n\n/** Cast u8 / u16 / u32 to u8. */\nexport function u8(arr: TypedArray): Uint8Array {\n  return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n\n/** Cast u8 / u16 / u32 to u32. */\nexport function u32(arr: TypedArray): Uint32Array {\n  return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n\n/** Zeroize a byte array. Warning: JS provides no guarantees. */\nexport function clean(...arrays: TypedArray[]): void {\n  for (let i = 0; i < arrays.length; i++) {\n    arrays[i].fill(0);\n  }\n}\n\n/** Create DataView of an array for easy byte-level manipulation. */\nexport function createView(arr: TypedArray): DataView {\n  return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n\n/** The rotate right (circular right shift) operation for uint32 */\nexport function rotr(word: number, shift: number): number {\n  return (word << (32 - shift)) | (word >>> shift);\n}\n\n/** The rotate left (circular left shift) operation for uint32 */\nexport function rotl(word: number, shift: number): number {\n  return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nexport const isLE: boolean = /* @__PURE__ */ (() =>\n  new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n\n/** The byte swap operation for uint32 */\nexport function byteSwap(word: number): number {\n  return (\n    ((word << 24) & 0xff000000) |\n    ((word << 8) & 0xff0000) |\n    ((word >>> 8) & 0xff00) |\n    ((word >>> 24) & 0xff)\n  );\n}\n/** Conditionally byte swap if on a big-endian platform */\nexport const swap8IfBE: (n: number) => number = isLE\n  ? (n: number) => n\n  : (n: number) => byteSwap(n);\n\n/** @deprecated */\nexport const byteSwapIfBE: typeof swap8IfBE = swap8IfBE;\n/** In place byte swap for Uint32Array */\nexport function byteSwap32(arr: Uint32Array): Uint32Array {\n  for (let i = 0; i < arr.length; i++) {\n    arr[i] = byteSwap(arr[i]);\n  }\n  return arr;\n}\n\nexport const swap32IfBE: (u: Uint32Array) => Uint32Array = isLE\n  ? (u: Uint32Array) => u\n  : byteSwap32;\n\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin: boolean = /* @__PURE__ */ (() =>\n  // @ts-ignore\n  typeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) =>\n  i.toString(16).padStart(2, '0')\n);\n\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes: Uint8Array): string {\n  abytes(bytes);\n  // @ts-ignore\n  if (hasHexBuiltin) return bytes.toHex();\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\n\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 } as const;\nfunction asciiToBase16(ch: number): number | undefined {\n  if (ch >= asciis._0 && ch <= asciis._9) return ch - asciis._0; // '2' => 50-48\n  if (ch >= asciis.A && ch <= asciis.F) return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n  if (ch >= asciis.a && ch <= asciis.f) return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n  return;\n}\n\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex: string): Uint8Array {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  // @ts-ignore\n  if (hasHexBuiltin) return Uint8Array.fromHex(hex);\n  const hl = hex.length;\n  const al = hl / 2;\n  if (hl % 2) throw new Error('hex string expected, got unpadded hex of length ' + hl);\n  const array = new Uint8Array(al);\n  for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n    const n1 = asciiToBase16(hex.charCodeAt(hi));\n    const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n    if (n1 === undefined || n2 === undefined) {\n      const char = hex[hi] + hex[hi + 1];\n      throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n    }\n    array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n  }\n  return array;\n}\n\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nexport const nextTick = async (): Promise<void> => {};\n\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nexport async function asyncLoop(\n  iters: number,\n  tick: number,\n  cb: (i: number) => void\n): Promise<void> {\n  let ts = Date.now();\n  for (let i = 0; i < iters; i++) {\n    cb(i);\n    // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n    const diff = Date.now() - ts;\n    if (diff >= 0 && diff < tick) continue;\n    await nextTick();\n    ts += diff;\n  }\n}\n\n// Global symbols, but ts doesn't see them: https://github.com/microsoft/TypeScript/issues/31535\ndeclare const TextEncoder: any;\ndeclare const TextDecoder: any;\n\n/**\n * Converts string to bytes using UTF8 encoding.\n * @example utf8ToBytes('abc') // Uint8Array.from([97, 98, 99])\n */\nexport function utf8ToBytes(str: string): Uint8Array {\n  if (typeof str !== 'string') throw new Error('string expected');\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\nexport function bytesToUtf8(bytes: Uint8Array): string {\n  return new TextDecoder().decode(bytes);\n}\n\n/** Accepted input of hash functions. Strings are converted to byte arrays. */\nexport type Input = string | Uint8Array;\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data: Input): Uint8Array {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n\n/** KDFs can accept string or Uint8Array for user convenience. */\nexport type KDFInput = string | Uint8Array;\n/**\n * Helper for KDFs: consumes uint8array or string.\n * When string is passed, does utf8 decoding, using TextDecoder.\n */\nexport function kdfInputToBytes(data: KDFInput): Uint8Array {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n\n/** Copies several Uint8Arrays into one. */\nexport function concatBytes(...arrays: Uint8Array[]): Uint8Array {\n  let sum = 0;\n  for (let i = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    abytes(a);\n    sum += a.length;\n  }\n  const res = new Uint8Array(sum);\n  for (let i = 0, pad = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    res.set(a, pad);\n    pad += a.length;\n  }\n  return res;\n}\n\ntype EmptyObj = {};\nexport function checkOpts<T1 extends EmptyObj, T2 extends EmptyObj>(\n  defaults: T1,\n  opts?: T2\n): T1 & T2 {\n  if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n    throw new Error('options should be object or undefined');\n  const merged = Object.assign(defaults, opts);\n  return merged as T1 & T2;\n}\n\n/** Hash interface. */\nexport type IHash = {\n  (data: Uint8Array): Uint8Array;\n  blockLen: number;\n  outputLen: number;\n  create: any;\n};\n\n/** For runtime check if class implements interface */\nexport abstract class Hash<T extends Hash<T>> {\n  abstract blockLen: number; // Bytes per block\n  abstract outputLen: number; // Bytes in output\n  abstract update(buf: Input): this;\n  // Writes digest into buf\n  abstract digestInto(buf: Uint8Array): void;\n  abstract digest(): Uint8Array;\n  /**\n   * Resets internal state. Makes Hash instance unusable.\n   * Reset is impossible for keyed hashes if key is consumed into state. If digest is not consumed\n   * by user, they will need to manually call `destroy()` when zeroing is necessary.\n   */\n  abstract destroy(): void;\n  /**\n   * Clones hash instance. Unsafe: doesn't check whether `to` is valid. Can be used as `clone()`\n   * when no options are passed.\n   * Reasons to use `_cloneInto` instead of clone: 1) performance 2) reuse instance => all internal\n   * buffers are overwritten => causes buffer overwrite which is used for digest in some cases.\n   * There are no guarantees for clean-up because it's impossible in JS.\n   */\n  abstract _cloneInto(to?: T): T;\n  // Safe version that clones internal state\n  abstract clone(): T;\n}\n\n/**\n * XOF: streaming API to read digest in chunks.\n * Same as 'squeeze' in keccak/k12 and 'seek' in blake3, but more generic name.\n * When hash used in XOF mode it is up to user to call '.destroy' afterwards, since we cannot\n * destroy state, next call can require more bytes.\n */\nexport type HashXOF<T extends Hash<T>> = Hash<T> & {\n  xof(bytes: number): Uint8Array; // Read 'bytes' bytes from digest stream\n  xofInto(buf: Uint8Array): Uint8Array; // read buf.length bytes from digest stream into buf\n};\n\n/** Hash function */\nexport type CHash = ReturnType<typeof createHasher>;\n/** Hash function with output */\nexport type CHashO = ReturnType<typeof createOptHasher>;\n/** XOF with output */\nexport type CHashXO = ReturnType<typeof createXOFer>;\n\n/** Wraps hash function, creating an interface on top of it */\nexport function createHasher<T extends Hash<T>>(\n  hashCons: () => Hash<T>\n): {\n  (msg: Input): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(): Hash<T>;\n} {\n  const hashC = (msg: Input): Uint8Array => hashCons().update(toBytes(msg)).digest();\n  const tmp = hashCons();\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = () => hashCons();\n  return hashC;\n}\n\nexport function createOptHasher<H extends Hash<H>, T extends Object>(\n  hashCons: (opts?: T) => Hash<H>\n): {\n  (msg: Input, opts?: T): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(opts?: T): Hash<H>;\n} {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts?: T) => hashCons(opts);\n  return hashC;\n}\n\nexport function createXOFer<H extends HashXOF<H>, T extends Object>(\n  hashCons: (opts?: T) => HashXOF<H>\n): {\n  (msg: Input, opts?: T): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(opts?: T): HashXOF<H>;\n} {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts?: T) => hashCons(opts);\n  return hashC;\n}\nexport const wrapConstructor: typeof createHasher = createHasher;\nexport const wrapConstructorWithOpts: typeof createOptHasher = createOptHasher;\nexport const wrapXOFConstructorWithOpts: typeof createXOFer = createXOFer;\n\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nexport function randomBytes(bytesLength = 32): Uint8Array {\n  if (crypto && typeof crypto.getRandomValues === 'function') {\n    return crypto.getRandomValues(new Uint8Array(bytesLength));\n  }\n  // Legacy Node.js compatibility\n  if (crypto && typeof crypto.randomBytes === 'function') {\n    return Uint8Array.from(crypto.randomBytes(bytesLength));\n  }\n  throw new Error('crypto.getRandomValues must be defined');\n}\n", "/**\n * SHA3 (keccak) hash function, based on a new \"Sponge function\" design.\n * Different from older hashes, the internal state is bigger than output size.\n *\n * Check out [FIPS-202](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.202.pdf),\n * [Website](https://keccak.team/keccak.html),\n * [the differences between SHA-3 and Keccak](https://crypto.stackexchange.com/questions/15727/what-are-the-key-differences-between-the-draft-sha-3-standard-and-the-keccak-sub).\n *\n * Check out `sha3-addons` module for cSHAKE, k12, and others.\n * @module\n */\nimport { rotlBH, rotlBL, rotlSH, rotlSL, split } from './_u64.ts';\n// prettier-ignore\nimport {\n  abytes, aexists, anumber, aoutput,\n  clean, createHasher, createXOFer, Hash,\n  swap32IfBE,\n  toBytes, u32,\n  type CHash, type CHashXO, type HashXOF, type Input\n} from './utils.ts';\n\n// No __PURE__ annotations in sha3 header:\n// EVERYTHING is in fact used on every export.\n// Various per round constants calculations\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst _7n = BigInt(7);\nconst _256n = BigInt(256);\nconst _0x71n = BigInt(0x71);\nconst SHA3_PI: number[] = [];\nconst SHA3_ROTL: number[] = [];\nconst _SHA3_IOTA: bigint[] = [];\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n  // Pi\n  [x, y] = [y, (2 * x + 3 * y) % 5];\n  SHA3_PI.push(2 * (5 * y + x));\n  // Rotational\n  SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n  // Iota\n  let t = _0n;\n  for (let j = 0; j < 7; j++) {\n    R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n    if (R & _2n) t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n  }\n  _SHA3_IOTA.push(t);\n}\nconst IOTAS = split(_SHA3_IOTA, true);\nconst SHA3_IOTA_H = IOTAS[0];\nconst SHA3_IOTA_L = IOTAS[1];\n\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h: number, l: number, s: number) => (s > 32 ? rotlBH(h, l, s) : rotlSH(h, l, s));\nconst rotlL = (h: number, l: number, s: number) => (s > 32 ? rotlBL(h, l, s) : rotlSL(h, l, s));\n\n/** `keccakf1600` internal function, additionally allows to adjust round count. */\nexport function keccakP(s: Uint32Array, rounds: number = 24): void {\n  const B = new Uint32Array(5 * 2);\n  // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n  for (let round = 24 - rounds; round < 24; round++) {\n    // Theta θ\n    for (let x = 0; x < 10; x++) B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n    for (let x = 0; x < 10; x += 2) {\n      const idx1 = (x + 8) % 10;\n      const idx0 = (x + 2) % 10;\n      const B0 = B[idx0];\n      const B1 = B[idx0 + 1];\n      const Th = rotlH(B0, B1, 1) ^ B[idx1];\n      const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n      for (let y = 0; y < 50; y += 10) {\n        s[x + y] ^= Th;\n        s[x + y + 1] ^= Tl;\n      }\n    }\n    // Rho (ρ) and Pi (π)\n    let curH = s[2];\n    let curL = s[3];\n    for (let t = 0; t < 24; t++) {\n      const shift = SHA3_ROTL[t];\n      const Th = rotlH(curH, curL, shift);\n      const Tl = rotlL(curH, curL, shift);\n      const PI = SHA3_PI[t];\n      curH = s[PI];\n      curL = s[PI + 1];\n      s[PI] = Th;\n      s[PI + 1] = Tl;\n    }\n    // Chi (χ)\n    for (let y = 0; y < 50; y += 10) {\n      for (let x = 0; x < 10; x++) B[x] = s[y + x];\n      for (let x = 0; x < 10; x++) s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n    }\n    // Iota (ι)\n    s[0] ^= SHA3_IOTA_H[round];\n    s[1] ^= SHA3_IOTA_L[round];\n  }\n  clean(B);\n}\n\n/** Keccak sponge function. */\nexport class Keccak extends Hash<Keccak> implements HashXOF<Keccak> {\n  protected state: Uint8Array;\n  protected pos = 0;\n  protected posOut = 0;\n  protected finished = false;\n  protected state32: Uint32Array;\n  protected destroyed = false;\n\n  public blockLen: number;\n  public suffix: number;\n  public outputLen: number;\n  protected enableXOF = false;\n  protected rounds: number;\n\n  // NOTE: we accept arguments in bytes instead of bits here.\n  constructor(\n    blockLen: number,\n    suffix: number,\n    outputLen: number,\n    enableXOF = false,\n    rounds: number = 24\n  ) {\n    super();\n    this.blockLen = blockLen;\n    this.suffix = suffix;\n    this.outputLen = outputLen;\n    this.enableXOF = enableXOF;\n    this.rounds = rounds;\n    // Can be passed from user as dkLen\n    anumber(outputLen);\n    // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n    // 0 < blockLen < 200\n    if (!(0 < blockLen && blockLen < 200))\n      throw new Error('only keccak-f1600 function is supported');\n    this.state = new Uint8Array(200);\n    this.state32 = u32(this.state);\n  }\n  clone(): Keccak {\n    return this._cloneInto();\n  }\n  protected keccak(): void {\n    swap32IfBE(this.state32);\n    keccakP(this.state32, this.rounds);\n    swap32IfBE(this.state32);\n    this.posOut = 0;\n    this.pos = 0;\n  }\n  update(data: Input): this {\n    aexists(this);\n    data = toBytes(data);\n    abytes(data);\n    const { blockLen, state } = this;\n    const len = data.length;\n    for (let pos = 0; pos < len; ) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      for (let i = 0; i < take; i++) state[this.pos++] ^= data[pos++];\n      if (this.pos === blockLen) this.keccak();\n    }\n    return this;\n  }\n  protected finish(): void {\n    if (this.finished) return;\n    this.finished = true;\n    const { state, suffix, pos, blockLen } = this;\n    // Do the padding\n    state[pos] ^= suffix;\n    if ((suffix & 0x80) !== 0 && pos === blockLen - 1) this.keccak();\n    state[blockLen - 1] ^= 0x80;\n    this.keccak();\n  }\n  protected writeInto(out: Uint8Array): Uint8Array {\n    aexists(this, false);\n    abytes(out);\n    this.finish();\n    const bufferOut = this.state;\n    const { blockLen } = this;\n    for (let pos = 0, len = out.length; pos < len; ) {\n      if (this.posOut >= blockLen) this.keccak();\n      const take = Math.min(blockLen - this.posOut, len - pos);\n      out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n      this.posOut += take;\n      pos += take;\n    }\n    return out;\n  }\n  xofInto(out: Uint8Array): Uint8Array {\n    // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n    if (!this.enableXOF) throw new Error('XOF is not possible for this instance');\n    return this.writeInto(out);\n  }\n  xof(bytes: number): Uint8Array {\n    anumber(bytes);\n    return this.xofInto(new Uint8Array(bytes));\n  }\n  digestInto(out: Uint8Array): Uint8Array {\n    aoutput(out, this);\n    if (this.finished) throw new Error('digest() was already called');\n    this.writeInto(out);\n    this.destroy();\n    return out;\n  }\n  digest(): Uint8Array {\n    return this.digestInto(new Uint8Array(this.outputLen));\n  }\n  destroy(): void {\n    this.destroyed = true;\n    clean(this.state);\n  }\n  _cloneInto(to?: Keccak): Keccak {\n    const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n    to ||= new Keccak(blockLen, suffix, outputLen, enableXOF, rounds);\n    to.state32.set(this.state32);\n    to.pos = this.pos;\n    to.posOut = this.posOut;\n    to.finished = this.finished;\n    to.rounds = rounds;\n    // Suffix can change in cSHAKE\n    to.suffix = suffix;\n    to.outputLen = outputLen;\n    to.enableXOF = enableXOF;\n    to.destroyed = this.destroyed;\n    return to;\n  }\n}\n\nconst gen = (suffix: number, blockLen: number, outputLen: number) =>\n  createHasher(() => new Keccak(blockLen, suffix, outputLen));\n\n/** SHA3-224 hash function. */\nexport const sha3_224: CHash = /* @__PURE__ */ (() => gen(0x06, 144, 224 / 8))();\n/** SHA3-256 hash function. Different from keccak-256. */\nexport const sha3_256: CHash = /* @__PURE__ */ (() => gen(0x06, 136, 256 / 8))();\n/** SHA3-384 hash function. */\nexport const sha3_384: CHash = /* @__PURE__ */ (() => gen(0x06, 104, 384 / 8))();\n/** SHA3-512 hash function. */\nexport const sha3_512: CHash = /* @__PURE__ */ (() => gen(0x06, 72, 512 / 8))();\n\n/** keccak-224 hash function. */\nexport const keccak_224: CHash = /* @__PURE__ */ (() => gen(0x01, 144, 224 / 8))();\n/** keccak-256 hash function. Different from SHA3-256. */\nexport const keccak_256: CHash = /* @__PURE__ */ (() => gen(0x01, 136, 256 / 8))();\n/** keccak-384 hash function. */\nexport const keccak_384: CHash = /* @__PURE__ */ (() => gen(0x01, 104, 384 / 8))();\n/** keccak-512 hash function. */\nexport const keccak_512: CHash = /* @__PURE__ */ (() => gen(0x01, 72, 512 / 8))();\n\nexport type ShakeOpts = { dkLen?: number };\n\nconst genShake = (suffix: number, blockLen: number, outputLen: number) =>\n  createXOFer<HashXOF<Keccak>, ShakeOpts>(\n    (opts: ShakeOpts = {}) =>\n      new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true)\n  );\n\n/** SHAKE128 XOF with 128-bit security. */\nexport const shake128: CHashXO = /* @__PURE__ */ (() => genShake(0x1f, 168, 128 / 8))();\n/** SHAKE256 XOF with 256-bit security. */\nexport const shake256: CHashXO = /* @__PURE__ */ (() => genShake(0x1f, 136, 256 / 8))();\n"], "mappings": ";;;;;;;;;;AA+EE,YAAA,MAAA;AAA+C,YAAA,UAAA;AAAyG,YAAA,QAAA;AA1E1J,QAAM,aAA6B,OAAO,KAAK,KAAK,CAAC;AACrD,QAAM,OAAuB,OAAO,EAAE;AAEtC,aAAS,QACP,GACA,KAAK,OAAK;AAKV,UAAI;AAAI,eAAO,EAAE,GAAG,OAAO,IAAI,UAAU,GAAG,GAAG,OAAQ,KAAK,OAAQ,UAAU,EAAC;AAC/E,aAAO,EAAE,GAAG,OAAQ,KAAK,OAAQ,UAAU,IAAI,GAAG,GAAG,OAAO,IAAI,UAAU,IAAI,EAAC;IACjF;AAEA,aAAS,MAAM,KAAe,KAAK,OAAK;AACtC,YAAM,MAAM,IAAI;AAChB,UAAI,KAAK,IAAI,YAAY,GAAG;AAC5B,UAAI,KAAK,IAAI,YAAY,GAAG;AAC5B,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAM,EAAE,GAAG,EAAC,IAAK,QAAQ,IAAI,CAAC,GAAG,EAAE;AACnC,SAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;MACxB;AACA,aAAO,CAAC,IAAI,EAAE;IAChB;AAEA,QAAM,QAAQ,CAAC,GAAW,MAAuB,OAAO,MAAM,CAAC,KAAK,OAAQ,OAAO,MAAM,CAAC;AAiDuE,YAAA,QAAA;AA/CjK,QAAM,QAAQ,CAAC,GAAW,IAAY,MAAsB,MAAM;AA+C0E,YAAA,QAAA;AA9C5I,QAAM,QAAQ,CAAC,GAAW,GAAW,MAAuB,KAAM,KAAK,IAAO,MAAM;AA8C+D,YAAA,QAAA;AA5CnJ,QAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,MAAM,IAAM,KAAM,KAAK;AA4CwC,YAAA,SAAA;AA3C5H,QAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,KAAK,IAAO,MAAM;AA2C+C,YAAA,SAAA;AAzCpI,QAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,KAAK,IAAO,MAAO,IAAI;AAyCkB,YAAA,SAAA;AAxC5G,QAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,MAAO,IAAI,KAAQ,KAAM,KAAK;AAwCyB,YAAA,SAAA;AAtCpH,QAAM,UAAU,CAAC,IAAY,MAAsB;AAsCuC,YAAA,UAAA;AArC1F,QAAM,UAAU,CAAC,GAAW,OAAuB;AAqCgD,YAAA,UAAA;AAnCnG,QAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAK,IAAM,MAAO,KAAK;AAmCV,YAAA,SAAA;AAlC1E,QAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAK,IAAM,MAAO,KAAK;AAkCF,YAAA,SAAA;AAhClF,QAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,IAAI,KAAQ,MAAO,KAAK;AAgCjC,YAAA,SAAA;AA/B1D,QAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,IAAI,KAAQ,MAAO,KAAK;AA+BzB,YAAA,SAAA;AA3BlE,aAAS,IACP,IACA,IACA,IACA,IAAU;AAKV,YAAM,KAAK,OAAO,MAAM,OAAO;AAC/B,aAAO,EAAE,GAAI,KAAK,MAAO,IAAI,KAAK,KAAM,KAAM,GAAG,GAAG,IAAI,EAAC;IAC3D;AAEA,QAAM,QAAQ,CAAC,IAAY,IAAY,QAAwB,OAAO,MAAM,OAAO,MAAM,OAAO;AAclF,YAAA,QAAA;AAbd,QAAM,QAAQ,CAAC,KAAa,IAAY,IAAY,OACjD,KAAK,KAAK,MAAO,MAAM,KAAK,KAAM,KAAM;AAYpC,YAAA,QAAA;AAXP,QAAM,QAAQ,CAAC,IAAY,IAAY,IAAY,QAChD,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO;AAUrB,YAAA,QAAA;AAT5B,QAAM,QAAQ,CAAC,KAAa,IAAY,IAAY,IAAY,OAC7D,KAAK,KAAK,KAAK,MAAO,MAAM,KAAK,KAAM,KAAM;AAQ3B,YAAA,QAAA;AAPrB,QAAM,QAAQ,CAAC,IAAY,IAAY,IAAY,IAAY,QAC5D,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO;AAMpB,YAAA,QAAA;AAL1C,QAAM,QAAQ,CAAC,KAAa,IAAY,IAAY,IAAY,IAAY,OACzE,KAAK,KAAK,KAAK,KAAK,MAAO,MAAM,KAAK,KAAM,KAAM;AAIlB,YAAA,QAAA;AAGnC,QAAM,MAAqpC;MACzpC;MAAS;MAAO;MAChB;MAAO;MACP;MAAQ;MAAQ;MAAQ;MACxB;MAAS;MACT;MAAQ;MAAQ;MAAQ;MACxB;MAAK;MAAO;MAAO;MAAO;MAAO;MAAO;;AAE1C,YAAA,UAAe;;;;;;;;;;ACnFF,YAAA,SACX,OAAO,eAAe,YAAY,YAAY,aAAa,WAAW,SAAS;;;;;;;;;;ACOjF,YAAA,UAAA;AAKA,YAAA,UAAA;AAKA,YAAA,SAAA;AAOA,YAAA,QAAA;AAQA,YAAA,UAAA;AAMA,YAAA,UAAA;AAcA,YAAA,KAAA;AAKA,YAAA,MAAA;AAKA,YAAA,QAAA;AAOA,YAAA,aAAA;AAKA,YAAA,OAAA;AAKA,YAAA,OAAA;AASA,YAAA,WAAA;AAgBA,YAAA,aAAA;AAyBA,YAAA,aAAA;AAyBA,YAAA,aAAA;AA4BA,YAAA,YAAA;AAwBA,YAAA,cAAA;AASA,YAAA,cAAA;AAWA,YAAA,UAAA;AAYA,YAAA,kBAAA;AAOA,YAAA,cAAA;AAiBA,YAAA,YAAA;AA+DA,YAAA,eAAA;AAgBA,YAAA,kBAAA;AAgBA,YAAA,cAAA;AAoBA,YAAA,cAAA;AArXA,QAAA,WAAA;AAGA,aAAgB,QAAQ,GAAU;AAChC,aAAO,aAAa,cAAe,YAAY,OAAO,CAAC,KAAK,EAAE,YAAY,SAAS;IACrF;AAGA,aAAgB,QAAQ,GAAS;AAC/B,UAAI,CAAC,OAAO,cAAc,CAAC,KAAK,IAAI;AAAG,cAAM,IAAI,MAAM,oCAAoC,CAAC;IAC9F;AAGA,aAAgB,OAAO,MAA8B,SAAiB;AACpE,UAAI,CAAC,QAAQ,CAAC;AAAG,cAAM,IAAI,MAAM,qBAAqB;AACtD,UAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,MAAM;AAClD,cAAM,IAAI,MAAM,mCAAmC,UAAU,kBAAkB,EAAE,MAAM;IAC3F;AAGA,aAAgB,MAAM,GAAQ;AAC5B,UAAI,OAAO,MAAM,cAAc,OAAO,EAAE,WAAW;AACjD,cAAM,IAAI,MAAM,8CAA8C;AAChE,cAAQ,EAAE,SAAS;AACnB,cAAQ,EAAE,QAAQ;IACpB;AAGA,aAAgB,QAAQ,UAAe,gBAAgB,MAAI;AACzD,UAAI,SAAS;AAAW,cAAM,IAAI,MAAM,kCAAkC;AAC1E,UAAI,iBAAiB,SAAS;AAAU,cAAM,IAAI,MAAM,uCAAuC;IACjG;AAGA,aAAgB,QAAQ,KAAU,UAAa;AAC7C,aAAO,GAAG;AACV,YAAM,MAAM,SAAS;AACrB,UAAI,IAAI,SAAS,KAAK;AACpB,cAAM,IAAI,MAAM,2DAA2D,GAAG;MAChF;IACF;AAQA,aAAgB,GAAG,KAAe;AAChC,aAAO,IAAI,WAAW,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;IAClE;AAGA,aAAgB,IAAI,KAAe;AACjC,aAAO,IAAI,YAAY,IAAI,QAAQ,IAAI,YAAY,KAAK,MAAM,IAAI,aAAa,CAAC,CAAC;IACnF;AAGA,aAAgB,SAAS,QAAoB;AAC3C,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,eAAO,CAAC,EAAE,KAAK,CAAC;MAClB;IACF;AAGA,aAAgB,WAAW,KAAe;AACxC,aAAO,IAAI,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;IAChE;AAGA,aAAgB,KAAK,MAAc,OAAa;AAC9C,aAAQ,QAAS,KAAK,QAAW,SAAS;IAC5C;AAGA,aAAgB,KAAK,MAAc,OAAa;AAC9C,aAAQ,QAAQ,QAAW,SAAU,KAAK,UAAY;IACxD;AAGa,YAAA,QAAiC,MAC5C,IAAI,WAAW,IAAI,YAAY,CAAC,SAAU,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,IAAK;AAGnE,aAAgB,SAAS,MAAY;AACnC,aACI,QAAQ,KAAM,aACd,QAAQ,IAAK,WACb,SAAS,IAAK,QACd,SAAS,KAAM;IAErB;AAEa,YAAA,YAAmC,QAAA,OAC5C,CAAC,MAAc,IACf,CAAC,MAAc,SAAS,CAAC;AAGhB,YAAA,eAAiC,QAAA;AAE9C,aAAgB,WAAW,KAAgB;AACzC,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC;MAC1B;AACA,aAAO;IACT;AAEa,YAAA,aAA8C,QAAA,OACvD,CAAC,MAAmB,IACpB;AAGJ,QAAM,iBAA0C;;MAE9C,OAAO,WAAW,KAAK,CAAA,CAAE,EAAE,UAAU,cAAc,OAAO,WAAW,YAAY;OAAW;AAG9F,QAAM,QAAwB,MAAM,KAAK,EAAE,QAAQ,IAAG,GAAI,CAAC,GAAG,MAC5D,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AAOjC,aAAgB,WAAW,OAAiB;AAC1C,aAAO,KAAK;AAEZ,UAAI;AAAe,eAAO,MAAM,MAAK;AAErC,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,eAAO,MAAM,MAAM,CAAC,CAAC;MACvB;AACA,aAAO;IACT;AAGA,QAAM,SAAS,EAAE,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAG;AAC5D,aAAS,cAAc,IAAU;AAC/B,UAAI,MAAM,OAAO,MAAM,MAAM,OAAO;AAAI,eAAO,KAAK,OAAO;AAC3D,UAAI,MAAM,OAAO,KAAK,MAAM,OAAO;AAAG,eAAO,MAAM,OAAO,IAAI;AAC9D,UAAI,MAAM,OAAO,KAAK,MAAM,OAAO;AAAG,eAAO,MAAM,OAAO,IAAI;AAC9D;IACF;AAMA,aAAgB,WAAW,KAAW;AACpC,UAAI,OAAO,QAAQ;AAAU,cAAM,IAAI,MAAM,8BAA8B,OAAO,GAAG;AAErF,UAAI;AAAe,eAAO,WAAW,QAAQ,GAAG;AAChD,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,KAAK;AAChB,UAAI,KAAK;AAAG,cAAM,IAAI,MAAM,qDAAqD,EAAE;AACnF,YAAM,QAAQ,IAAI,WAAW,EAAE;AAC/B,eAAS,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,MAAM,MAAM,GAAG;AAC/C,cAAM,KAAK,cAAc,IAAI,WAAW,EAAE,CAAC;AAC3C,cAAM,KAAK,cAAc,IAAI,WAAW,KAAK,CAAC,CAAC;AAC/C,YAAI,OAAO,UAAa,OAAO,QAAW;AACxC,gBAAM,OAAO,IAAI,EAAE,IAAI,IAAI,KAAK,CAAC;AACjC,gBAAM,IAAI,MAAM,iDAAiD,OAAO,gBAAgB,EAAE;QAC5F;AACA,cAAM,EAAE,IAAI,KAAK,KAAK;MACxB;AACA,aAAO;IACT;AAOO,QAAM,WAAW,YAA0B;IAAE;AAAvC,YAAA,WAAQ;AAGd,mBAAe,UACpB,OACA,MACA,IAAuB;AAEvB,UAAI,KAAK,KAAK,IAAG;AACjB,eAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,WAAG,CAAC;AAEJ,cAAM,OAAO,KAAK,IAAG,IAAK;AAC1B,YAAI,QAAQ,KAAK,OAAO;AAAM;AAC9B,eAAM,GAAA,QAAA,UAAQ;AACd,cAAM;MACR;IACF;AAUA,aAAgB,YAAY,KAAW;AACrC,UAAI,OAAO,QAAQ;AAAU,cAAM,IAAI,MAAM,iBAAiB;AAC9D,aAAO,IAAI,WAAW,IAAI,YAAW,EAAG,OAAO,GAAG,CAAC;IACrD;AAMA,aAAgB,YAAY,OAAiB;AAC3C,aAAO,IAAI,YAAW,EAAG,OAAO,KAAK;IACvC;AASA,aAAgB,QAAQ,MAAW;AACjC,UAAI,OAAO,SAAS;AAAU,eAAO,YAAY,IAAI;AACrD,aAAO,IAAI;AACX,aAAO;IACT;AAQA,aAAgB,gBAAgB,MAAc;AAC5C,UAAI,OAAO,SAAS;AAAU,eAAO,YAAY,IAAI;AACrD,aAAO,IAAI;AACX,aAAO;IACT;AAGA,aAAgB,eAAe,QAAoB;AACjD,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAM,IAAI,OAAO,CAAC;AAClB,eAAO,CAAC;AACR,eAAO,EAAE;MACX;AACA,YAAM,MAAM,IAAI,WAAW,GAAG;AAC9B,eAAS,IAAI,GAAG,MAAM,GAAG,IAAI,OAAO,QAAQ,KAAK;AAC/C,cAAM,IAAI,OAAO,CAAC;AAClB,YAAI,IAAI,GAAG,GAAG;AACd,eAAO,EAAE;MACX;AACA,aAAO;IACT;AAGA,aAAgB,UACd,UACA,MAAS;AAET,UAAI,SAAS,UAAa,CAAA,EAAG,SAAS,KAAK,IAAI,MAAM;AACnD,cAAM,IAAI,MAAM,uCAAuC;AACzD,YAAM,SAAS,OAAO,OAAO,UAAU,IAAI;AAC3C,aAAO;IACT;AAWA,QAAsB,OAAtB,MAA0B;;AAA1B,YAAA,OAAA;AA4CA,aAAgB,aACd,UAAuB;AAOvB,YAAM,QAAQ,CAAC,QAA2B,SAAQ,EAAG,OAAO,QAAQ,GAAG,CAAC,EAAE,OAAM;AAChF,YAAM,MAAM,SAAQ;AACpB,YAAM,YAAY,IAAI;AACtB,YAAM,WAAW,IAAI;AACrB,YAAM,SAAS,MAAM,SAAQ;AAC7B,aAAO;IACT;AAEA,aAAgB,gBACd,UAA+B;AAO/B,YAAM,QAAQ,CAAC,KAAY,SAAyB,SAAS,IAAI,EAAE,OAAO,QAAQ,GAAG,CAAC,EAAE,OAAM;AAC9F,YAAM,MAAM,SAAS,CAAA,CAAO;AAC5B,YAAM,YAAY,IAAI;AACtB,YAAM,WAAW,IAAI;AACrB,YAAM,SAAS,CAAC,SAAa,SAAS,IAAI;AAC1C,aAAO;IACT;AAEA,aAAgB,YACd,UAAkC;AAOlC,YAAM,QAAQ,CAAC,KAAY,SAAyB,SAAS,IAAI,EAAE,OAAO,QAAQ,GAAG,CAAC,EAAE,OAAM;AAC9F,YAAM,MAAM,SAAS,CAAA,CAAO;AAC5B,YAAM,YAAY,IAAI;AACtB,YAAM,WAAW,IAAI;AACrB,YAAM,SAAS,CAAC,SAAa,SAAS,IAAI;AAC1C,aAAO;IACT;AACa,YAAA,kBAAuC;AACvC,YAAA,0BAAkD;AAClD,YAAA,6BAAiD;AAG9D,aAAgB,YAAY,cAAc,IAAE;AAC1C,UAAI,SAAA,UAAU,OAAO,SAAA,OAAO,oBAAoB,YAAY;AAC1D,eAAO,SAAA,OAAO,gBAAgB,IAAI,WAAW,WAAW,CAAC;MAC3D;AAEA,UAAI,SAAA,UAAU,OAAO,SAAA,OAAO,gBAAgB,YAAY;AACtD,eAAO,WAAW,KAAK,SAAA,OAAO,YAAY,WAAW,CAAC;MACxD;AACA,YAAM,IAAI,MAAM,wCAAwC;IAC1D;;;;;;;;;;AClVA,YAAA,UAAA;AA7CA,QAAA,YAAA;AAEA,QAAA,aAAA;AAWA,QAAM,MAAM,OAAO,CAAC;AACpB,QAAM,MAAM,OAAO,CAAC;AACpB,QAAM,MAAM,OAAO,CAAC;AACpB,QAAM,MAAM,OAAO,CAAC;AACpB,QAAM,QAAQ,OAAO,GAAG;AACxB,QAAM,SAAS,OAAO,GAAI;AAC1B,QAAM,UAAoB,CAAA;AAC1B,QAAM,YAAsB,CAAA;AAC5B,QAAM,aAAuB,CAAA;AAC7B,aAAS,QAAQ,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,SAAS;AAE9D,OAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC;AAChC,cAAQ,KAAK,KAAK,IAAI,IAAI,EAAE;AAE5B,gBAAU,MAAQ,QAAQ,MAAM,QAAQ,KAAM,IAAK,EAAE;AAErD,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,aAAM,KAAK,OAAS,KAAK,OAAO,UAAW;AAC3C,YAAI,IAAI;AAAK,eAAK,QAAS,OAAuB,OAAO,CAAC,KAAK;MACjE;AACA,iBAAW,KAAK,CAAC;IACnB;AACA,QAAM,SAAQ,GAAA,UAAA,OAAM,YAAY,IAAI;AACpC,QAAM,cAAc,MAAM,CAAC;AAC3B,QAAM,cAAc,MAAM,CAAC;AAG3B,QAAM,QAAQ,CAAC,GAAW,GAAW,MAAe,IAAI,MAAK,GAAA,UAAA,QAAO,GAAG,GAAG,CAAC,KAAI,GAAA,UAAA,QAAO,GAAG,GAAG,CAAC;AAC7F,QAAM,QAAQ,CAAC,GAAW,GAAW,MAAe,IAAI,MAAK,GAAA,UAAA,QAAO,GAAG,GAAG,CAAC,KAAI,GAAA,UAAA,QAAO,GAAG,GAAG,CAAC;AAG7F,aAAgB,QAAQ,GAAgB,SAAiB,IAAE;AACzD,YAAM,IAAI,IAAI,YAAY,IAAI,CAAC;AAE/B,eAAS,QAAQ,KAAK,QAAQ,QAAQ,IAAI,SAAS;AAEjD,iBAAS,IAAI,GAAG,IAAI,IAAI;AAAK,YAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AACvF,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,gBAAM,QAAQ,IAAI,KAAK;AACvB,gBAAM,QAAQ,IAAI,KAAK;AACvB,gBAAM,KAAK,EAAE,IAAI;AACjB,gBAAM,KAAK,EAAE,OAAO,CAAC;AACrB,gBAAM,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI;AACpC,gBAAM,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;AACxC,mBAAS,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI;AAC/B,cAAE,IAAI,CAAC,KAAK;AACZ,cAAE,IAAI,IAAI,CAAC,KAAK;UAClB;QACF;AAEA,YAAI,OAAO,EAAE,CAAC;AACd,YAAI,OAAO,EAAE,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,gBAAM,QAAQ,UAAU,CAAC;AACzB,gBAAM,KAAK,MAAM,MAAM,MAAM,KAAK;AAClC,gBAAM,KAAK,MAAM,MAAM,MAAM,KAAK;AAClC,gBAAM,KAAK,QAAQ,CAAC;AACpB,iBAAO,EAAE,EAAE;AACX,iBAAO,EAAE,KAAK,CAAC;AACf,YAAE,EAAE,IAAI;AACR,YAAE,KAAK,CAAC,IAAI;QACd;AAEA,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI;AAC/B,mBAAS,IAAI,GAAG,IAAI,IAAI;AAAK,cAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAC3C,mBAAS,IAAI,GAAG,IAAI,IAAI;AAAK,cAAE,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,KAAK,EAAE;QAC5E;AAEA,UAAE,CAAC,KAAK,YAAY,KAAK;AACzB,UAAE,CAAC,KAAK,YAAY,KAAK;MAC3B;AACA,OAAA,GAAA,WAAA,OAAM,CAAC;IACT;AAGA,QAAa,SAAb,MAAa,gBAAe,WAAA,KAAY;;MAetC,YACE,UACA,QACA,WACA,YAAY,OACZ,SAAiB,IAAE;AAEnB,cAAK;AApBG,aAAA,MAAM;AACN,aAAA,SAAS;AACT,aAAA,WAAW;AAEX,aAAA,YAAY;AAKZ,aAAA,YAAY;AAYpB,aAAK,WAAW;AAChB,aAAK,SAAS;AACd,aAAK,YAAY;AACjB,aAAK,YAAY;AACjB,aAAK,SAAS;AAEd,SAAA,GAAA,WAAA,SAAQ,SAAS;AAGjB,YAAI,EAAE,IAAI,YAAY,WAAW;AAC/B,gBAAM,IAAI,MAAM,yCAAyC;AAC3D,aAAK,QAAQ,IAAI,WAAW,GAAG;AAC/B,aAAK,WAAU,GAAA,WAAA,KAAI,KAAK,KAAK;MAC/B;MACA,QAAK;AACH,eAAO,KAAK,WAAU;MACxB;MACU,SAAM;AACd,SAAA,GAAA,WAAA,YAAW,KAAK,OAAO;AACvB,gBAAQ,KAAK,SAAS,KAAK,MAAM;AACjC,SAAA,GAAA,WAAA,YAAW,KAAK,OAAO;AACvB,aAAK,SAAS;AACd,aAAK,MAAM;MACb;MACA,OAAO,MAAW;AAChB,SAAA,GAAA,WAAA,SAAQ,IAAI;AACZ,gBAAO,GAAA,WAAA,SAAQ,IAAI;AACnB,SAAA,GAAA,WAAA,QAAO,IAAI;AACX,cAAM,EAAE,UAAU,MAAK,IAAK;AAC5B,cAAM,MAAM,KAAK;AACjB,iBAAS,MAAM,GAAG,MAAM,OAAO;AAC7B,gBAAM,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,MAAM,GAAG;AACpD,mBAAS,IAAI,GAAG,IAAI,MAAM;AAAK,kBAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAC9D,cAAI,KAAK,QAAQ;AAAU,iBAAK,OAAM;QACxC;AACA,eAAO;MACT;MACU,SAAM;AACd,YAAI,KAAK;AAAU;AACnB,aAAK,WAAW;AAChB,cAAM,EAAE,OAAO,QAAQ,KAAK,SAAQ,IAAK;AAEzC,cAAM,GAAG,KAAK;AACd,aAAK,SAAS,SAAU,KAAK,QAAQ,WAAW;AAAG,eAAK,OAAM;AAC9D,cAAM,WAAW,CAAC,KAAK;AACvB,aAAK,OAAM;MACb;MACU,UAAU,KAAe;AACjC,SAAA,GAAA,WAAA,SAAQ,MAAM,KAAK;AACnB,SAAA,GAAA,WAAA,QAAO,GAAG;AACV,aAAK,OAAM;AACX,cAAM,YAAY,KAAK;AACvB,cAAM,EAAE,SAAQ,IAAK;AACrB,iBAAS,MAAM,GAAG,MAAM,IAAI,QAAQ,MAAM,OAAO;AAC/C,cAAI,KAAK,UAAU;AAAU,iBAAK,OAAM;AACxC,gBAAM,OAAO,KAAK,IAAI,WAAW,KAAK,QAAQ,MAAM,GAAG;AACvD,cAAI,IAAI,UAAU,SAAS,KAAK,QAAQ,KAAK,SAAS,IAAI,GAAG,GAAG;AAChE,eAAK,UAAU;AACf,iBAAO;QACT;AACA,eAAO;MACT;MACA,QAAQ,KAAe;AAErB,YAAI,CAAC,KAAK;AAAW,gBAAM,IAAI,MAAM,uCAAuC;AAC5E,eAAO,KAAK,UAAU,GAAG;MAC3B;MACA,IAAI,OAAa;AACf,SAAA,GAAA,WAAA,SAAQ,KAAK;AACb,eAAO,KAAK,QAAQ,IAAI,WAAW,KAAK,CAAC;MAC3C;MACA,WAAW,KAAe;AACxB,SAAA,GAAA,WAAA,SAAQ,KAAK,IAAI;AACjB,YAAI,KAAK;AAAU,gBAAM,IAAI,MAAM,6BAA6B;AAChE,aAAK,UAAU,GAAG;AAClB,aAAK,QAAO;AACZ,eAAO;MACT;MACA,SAAM;AACJ,eAAO,KAAK,WAAW,IAAI,WAAW,KAAK,SAAS,CAAC;MACvD;MACA,UAAO;AACL,aAAK,YAAY;AACjB,SAAA,GAAA,WAAA,OAAM,KAAK,KAAK;MAClB;MACA,WAAW,IAAW;AACpB,cAAM,EAAE,UAAU,QAAQ,WAAW,QAAQ,UAAS,IAAK;AAC3D,eAAA,KAAO,IAAI,QAAO,UAAU,QAAQ,WAAW,WAAW,MAAM;AAChE,WAAG,QAAQ,IAAI,KAAK,OAAO;AAC3B,WAAG,MAAM,KAAK;AACd,WAAG,SAAS,KAAK;AACjB,WAAG,WAAW,KAAK;AACnB,WAAG,SAAS;AAEZ,WAAG,SAAS;AACZ,WAAG,YAAY;AACf,WAAG,YAAY;AACf,WAAG,YAAY,KAAK;AACpB,eAAO;MACT;;AA1HF,YAAA,SAAA;AA6HA,QAAM,MAAM,CAAC,QAAgB,UAAkB,eAC7C,GAAA,WAAA,cAAa,MAAM,IAAI,OAAO,UAAU,QAAQ,SAAS,CAAC;AAG/C,YAAA,YAAmC,MAAM,IAAI,GAAM,KAAK,MAAM,CAAC,GAAE;AAEjE,YAAA,YAAmC,MAAM,IAAI,GAAM,KAAK,MAAM,CAAC,GAAE;AAEjE,YAAA,YAAmC,MAAM,IAAI,GAAM,KAAK,MAAM,CAAC,GAAE;AAEjE,YAAA,YAAmC,MAAM,IAAI,GAAM,IAAI,MAAM,CAAC,GAAE;AAGhE,YAAA,cAAqC,MAAM,IAAI,GAAM,KAAK,MAAM,CAAC,GAAE;AAEnE,YAAA,cAAqC,MAAM,IAAI,GAAM,KAAK,MAAM,CAAC,GAAE;AAEnE,YAAA,cAAqC,MAAM,IAAI,GAAM,KAAK,MAAM,CAAC,GAAE;AAEnE,YAAA,cAAqC,MAAM,IAAI,GAAM,IAAI,MAAM,CAAC,GAAE;AAI/E,QAAM,WAAW,CAAC,QAAgB,UAAkB,eAClD,GAAA,WAAA,aACE,CAAC,OAAkB,CAAA,MACjB,IAAI,OAAO,UAAU,QAAQ,KAAK,UAAU,SAAY,YAAY,KAAK,OAAO,IAAI,CAAC;AAI9E,YAAA,YAAqC,MAAM,SAAS,IAAM,KAAK,MAAM,CAAC,GAAE;AAExE,YAAA,YAAqC,MAAM,SAAS,IAAM,KAAK,MAAM,CAAC,GAAE;;;", "names": []}