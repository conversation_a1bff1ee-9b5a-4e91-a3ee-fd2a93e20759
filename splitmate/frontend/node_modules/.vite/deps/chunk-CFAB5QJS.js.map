{"version": 3, "sources": ["../../ox/core/version.ts", "../../ox/core/internal/errors.ts", "../../ox/core/Errors.ts", "../../ox/core/Json.ts", "../../ox/core/internal/bytes.ts", "../../ox/core/internal/hex.ts", "../../ox/core/Bytes.ts", "../../ox/core/Hex.ts", "../../ox/core/Withdrawal.ts", "../../ox/core/BlockOverrides.ts", "../../viem/constants/abis.ts", "../../viem/constants/contract.ts", "../../viem/constants/contracts.ts", "../../viem/utils/abi/decodeFunctionResult.ts", "../../viem/utils/abi/encodeDeployData.ts", "../../viem/utils/abi/prepareEncodeFunctionData.ts", "../../viem/utils/abi/encodeFunctionData.ts", "../../viem/utils/promise/withResolvers.ts", "../../viem/utils/promise/createBatchScheduler.ts", "../../viem/utils/stateOverride.ts", "../../viem/actions/public/call.ts", "../../viem/errors/ccip.ts", "../../viem/utils/address/isAddressEqual.ts", "../../viem/utils/abi/decodeFunctionData.ts", "../../viem/utils/abi/encodeErrorResult.ts", "../../viem/utils/abi/encodeFunctionResult.ts", "../../viem/utils/ens/localBatchGatewayRequest.ts", "../../viem/utils/ccip.ts"], "sourcesContent": ["/** @internal */\nexport const version = '0.1.1'\n", "import { version } from '../version.js'\n\n/** @internal */\nexport function getUrl(url: string) {\n  return url\n}\n\n/** @internal */\nexport function getVersion() {\n  return version\n}\n\n/** @internal */\nexport function prettyPrint(args: unknown) {\n  if (!args) return ''\n  const entries = Object.entries(args)\n    .map(([key, value]) => {\n      if (value === undefined || value === false) return null\n      return [key, value]\n    })\n    .filter(Boolean) as [string, string][]\n  const maxLength = entries.reduce((acc, [key]) => Math.max(acc, key.length), 0)\n  return entries\n    .map(([key, value]) => `  ${`${key}:`.padEnd(maxLength + 1)}  ${value}`)\n    .join('\\n')\n}\n", "import { getVersion } from './internal/errors.js'\n\nexport type GlobalErrorType<name extends string = 'Error'> = Error & {\n  name: name\n}\n\n/**\n * Base error class inherited by all errors thrown by ox.\n *\n * @example\n * ```ts\n * import { Errors } from 'ox'\n * throw new Errors.BaseError('An error occurred')\n * ```\n */\nexport class BaseError<\n  cause extends Error | undefined = undefined,\n> extends Error {\n  details: string\n  docs?: string | undefined\n  docsPath?: string | undefined\n  shortMessage: string\n\n  override cause: cause\n  override name = 'BaseError'\n\n  version = `ox@${getVersion()}`\n\n  constructor(shortMessage: string, options: BaseError.Options<cause> = {}) {\n    const details = (() => {\n      if (options.cause instanceof BaseError) {\n        if (options.cause.details) return options.cause.details\n        if (options.cause.shortMessage) return options.cause.shortMessage\n      }\n      if (\n        options.cause &&\n        'details' in options.cause &&\n        typeof options.cause.details === 'string'\n      )\n        return options.cause.details\n      if (options.cause?.message) return options.cause.message\n      return options.details!\n    })()\n    const docsPath = (() => {\n      if (options.cause instanceof BaseError)\n        return options.cause.docsPath || options.docsPath\n      return options.docsPath\n    })()\n\n    const docsBaseUrl = 'https://oxlib.sh'\n    const docs = `${docsBaseUrl}${docsPath ?? ''}`\n\n    const message = [\n      shortMessage || 'An error occurred.',\n      ...(options.metaMessages ? ['', ...options.metaMessages] : []),\n      ...(details || docsPath\n        ? [\n            '',\n            details ? `Details: ${details}` : undefined,\n            docsPath ? `See: ${docs}` : undefined,\n          ]\n        : []),\n    ]\n      .filter((x) => typeof x === 'string')\n      .join('\\n')\n\n    super(message, options.cause ? { cause: options.cause } : undefined)\n\n    this.cause = options.cause as any\n    this.details = details\n    this.docs = docs\n    this.docsPath = docsPath\n    this.shortMessage = shortMessage\n  }\n\n  walk(): Error\n  walk(fn: (err: unknown) => boolean): Error | null\n  walk(fn?: any): any {\n    return walk(this, fn)\n  }\n}\n\nexport declare namespace BaseError {\n  type Options<cause extends Error | undefined = Error | undefined> = {\n    cause?: cause | undefined\n    details?: string | undefined\n    docsPath?: string | undefined\n    metaMessages?: (string | undefined)[] | undefined\n  }\n}\n\n/** @internal */\nfunction walk(\n  err: unknown,\n  fn?: ((err: unknown) => boolean) | undefined,\n): unknown {\n  if (fn?.(err)) return err\n  if (err && typeof err === 'object' && 'cause' in err && err.cause)\n    return walk(err.cause, fn)\n  return fn ? null : err\n}\n", "import type * as Errors from './Errors.js'\n\nconst bigIntSuffix = '#__bigint'\n\n/**\n * Parses a JSON string, with support for `bigint`.\n *\n * @example\n * ```ts twoslash\n * import { Json } from 'ox'\n *\n * const json = Json.parse('{\"foo\":\"bar\",\"baz\":\"69420694206942069420694206942069420694206942069420#__bigint\"}')\n * // @log: {\n * // @log:   foo: 'bar',\n * // @log:   baz: 69420694206942069420694206942069420694206942069420n\n * // @log: }\n * ```\n *\n * @param string - The value to parse.\n * @param reviver - A function that transforms the results.\n * @returns The parsed value.\n */\nexport function parse(\n  string: string,\n  reviver?: ((this: any, key: string, value: any) => any) | undefined,\n) {\n  return JSON.parse(string, (key, value_) => {\n    const value = value_\n    if (typeof value === 'string' && value.endsWith(bigIntSuffix))\n      return BigInt(value.slice(0, -bigIntSuffix.length))\n    return typeof reviver === 'function' ? reviver(key, value) : value\n  })\n}\n\nexport declare namespace parse {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Stringifies a value to its JSON representation, with support for `bigint`.\n *\n * @example\n * ```ts twoslash\n * import { Json } from 'ox'\n *\n * const json = Json.stringify({\n *   foo: 'bar',\n *   baz: 69420694206942069420694206942069420694206942069420n,\n * })\n * // @log: '{\"foo\":\"bar\",\"baz\":\"69420694206942069420694206942069420694206942069420#__bigint\"}'\n * ```\n *\n * @param value - The value to stringify.\n * @param replacer - A function that transforms the results. It is passed the key and value of the property, and must return the value to be used in the JSON string. If this function returns `undefined`, the property is not included in the resulting JSON string.\n * @param space - A string or number that determines the indentation of the JSON string. If it is a number, it indicates the number of spaces to use as indentation; if it is a string (e.g. `'\\t'`), it uses the string as the indentation character.\n * @returns The JSON string.\n */\nexport function stringify(\n  value: any,\n  replacer?: ((this: any, key: string, value: any) => any) | null | undefined,\n  space?: string | number | undefined,\n) {\n  return JSON.stringify(\n    value,\n    (key, value) => {\n      if (typeof replacer === 'function') return replacer(key, value)\n      if (typeof value === 'bigint') return value.toString() + bigIntSuffix\n      return value\n    },\n    space,\n  )\n}\n\nexport declare namespace stringify {\n  type ErrorType = Errors.GlobalErrorType\n}\n", "import * as Bytes from '../Bytes.js'\nimport type * as Errors from '../Errors.js'\n\n/** @internal */\nexport function assertSize(bytes: Bytes.Bytes, size_: number): void {\n  if (Bytes.size(bytes) > size_)\n    throw new Bytes.SizeOverflowError({\n      givenSize: Bytes.size(bytes),\n      maxSize: size_,\n    })\n}\n\n/** @internal */\nexport declare namespace assertSize {\n  type ErrorType =\n    | Bytes.size.ErrorType\n    | Bytes.SizeOverflowError\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function assertStartOffset(\n  value: Bytes.Bytes,\n  start?: number | undefined,\n) {\n  if (typeof start === 'number' && start > 0 && start > Bytes.size(value) - 1)\n    throw new Bytes.SliceOffsetOutOfBoundsError({\n      offset: start,\n      position: 'start',\n      size: Bytes.size(value),\n    })\n}\n\nexport declare namespace assertStartOffset {\n  export type ErrorType =\n    | Bytes.SliceOffsetOutOfBoundsError\n    | Bytes.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function assertEndOffset(\n  value: Bytes.Bytes,\n  start?: number | undefined,\n  end?: number | undefined,\n) {\n  if (\n    typeof start === 'number' &&\n    typeof end === 'number' &&\n    Bytes.size(value) !== end - start\n  ) {\n    throw new Bytes.SliceOffsetOutOfBoundsError({\n      offset: end,\n      position: 'end',\n      size: Bytes.size(value),\n    })\n  }\n}\n\n/** @internal */\nexport declare namespace assertEndOffset {\n  type ErrorType =\n    | Bytes.SliceOffsetOutOfBoundsError\n    | Bytes.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport const charCodeMap = {\n  zero: 48,\n  nine: 57,\n  A: 65,\n  F: 70,\n  a: 97,\n  f: 102,\n} as const\n\n/** @internal */\nexport function charCodeToBase16(char: number) {\n  if (char >= charCodeMap.zero && char <= charCodeMap.nine)\n    return char - charCodeMap.zero\n  if (char >= charCodeMap.A && char <= charCodeMap.F)\n    return char - (charCodeMap.A - 10)\n  if (char >= charCodeMap.a && char <= charCodeMap.f)\n    return char - (charCodeMap.a - 10)\n  return undefined\n}\n\n/** @internal */\nexport function pad(bytes: Bytes.Bytes, options: pad.Options = {}) {\n  const { dir, size = 32 } = options\n  if (size === 0) return bytes\n  if (bytes.length > size)\n    throw new Bytes.SizeExceedsPaddingSizeError({\n      size: bytes.length,\n      targetSize: size,\n      type: 'Bytes',\n    })\n  const paddedBytes = new Uint8Array(size)\n  for (let i = 0; i < size; i++) {\n    const padEnd = dir === 'right'\n    paddedBytes[padEnd ? i : size - i - 1] =\n      bytes[padEnd ? i : bytes.length - i - 1]!\n  }\n  return paddedBytes\n}\n\n/** @internal */\nexport declare namespace pad {\n  type Options = {\n    dir?: 'left' | 'right' | undefined\n    size?: number | undefined\n  }\n\n  type ReturnType = Bytes.Bytes\n\n  type ErrorType = Bytes.SizeExceedsPaddingSizeError | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function trim(\n  value: Bytes.Bytes,\n  options: trim.Options = {},\n): trim.ReturnType {\n  const { dir = 'left' } = options\n\n  let data = value\n\n  let sliceLength = 0\n  for (let i = 0; i < data.length - 1; i++) {\n    if (data[dir === 'left' ? i : data.length - i - 1]!.toString() === '0')\n      sliceLength++\n    else break\n  }\n  data =\n    dir === 'left'\n      ? data.slice(sliceLength)\n      : data.slice(0, data.length - sliceLength)\n\n  return data as trim.ReturnType\n}\n\n/** @internal */\nexport declare namespace trim {\n  type Options = {\n    dir?: 'left' | 'right' | undefined\n  }\n\n  type ReturnType = Bytes.Bytes\n\n  type ErrorType = Errors.GlobalErrorType\n}\n", "import type * as Errors from '../Errors.js'\nimport * as Hex from '../Hex.js'\n\n/** @internal */\nexport function assertSize(hex: Hex.Hex, size_: number): void {\n  if (Hex.size(hex) > size_)\n    throw new Hex.SizeOverflowError({\n      givenSize: Hex.size(hex),\n      maxSize: size_,\n    })\n}\n\n/** @internal */\nexport declare namespace assertSize {\n  type ErrorType =\n    | Hex.size.ErrorType\n    | Hex.SizeOverflowError\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function assertStartOffset(value: Hex.Hex, start?: number | undefined) {\n  if (typeof start === 'number' && start > 0 && start > Hex.size(value) - 1)\n    throw new Hex.SliceOffsetOutOfBoundsError({\n      offset: start,\n      position: 'start',\n      size: Hex.size(value),\n    })\n}\n\nexport declare namespace assertStartOffset {\n  type ErrorType =\n    | Hex.SliceOffsetOutOfBoundsError\n    | Hex.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function assertEndOffset(\n  value: Hex.Hex,\n  start?: number | undefined,\n  end?: number | undefined,\n) {\n  if (\n    typeof start === 'number' &&\n    typeof end === 'number' &&\n    Hex.size(value) !== end - start\n  ) {\n    throw new Hex.SliceOffsetOutOfBoundsError({\n      offset: end,\n      position: 'end',\n      size: Hex.size(value),\n    })\n  }\n}\n\nexport declare namespace assertEndOffset {\n  type ErrorType =\n    | Hex.SliceOffsetOutOfBoundsError\n    | Hex.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function pad(hex_: Hex.Hex, options: pad.Options = {}) {\n  const { dir, size = 32 } = options\n\n  if (size === 0) return hex_\n\n  const hex = hex_.replace('0x', '')\n  if (hex.length > size * 2)\n    throw new Hex.SizeExceedsPaddingSizeError({\n      size: Math.ceil(hex.length / 2),\n      targetSize: size,\n      type: 'Hex',\n    })\n\n  return `0x${hex[dir === 'right' ? 'padEnd' : 'padStart'](size * 2, '0')}` as Hex.Hex\n}\n\n/** @internal */\nexport declare namespace pad {\n  type Options = {\n    dir?: 'left' | 'right' | undefined\n    size?: number | undefined\n  }\n  type ErrorType = Hex.SizeExceedsPaddingSizeError | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function trim(\n  value: Hex.Hex,\n  options: trim.Options = {},\n): trim.ReturnType {\n  const { dir = 'left' } = options\n\n  let data = value.replace('0x', '')\n\n  let sliceLength = 0\n  for (let i = 0; i < data.length - 1; i++) {\n    if (data[dir === 'left' ? i : data.length - i - 1]!.toString() === '0')\n      sliceLength++\n    else break\n  }\n  data =\n    dir === 'left'\n      ? data.slice(sliceLength)\n      : data.slice(0, data.length - sliceLength)\n\n  if (data === '0') return '0x'\n  if (dir === 'right' && data.length % 2 === 1) return `0x${data}0`\n  return `0x${data}` as trim.ReturnType\n}\n\n/** @internal */\nexport declare namespace trim {\n  type Options = {\n    dir?: 'left' | 'right' | undefined\n  }\n\n  type ReturnType = Hex.Hex\n\n  type ErrorType = Errors.GlobalErrorType\n}\n", "import { equalBytes } from '@noble/curves/abstract/utils'\nimport * as Errors from './Errors.js'\nimport * as Hex from './Hex.js'\nimport * as <PERSON><PERSON> from './Json.js'\nimport * as internal from './internal/bytes.js'\nimport * as internal_hex from './internal/hex.js'\n\nconst decoder = /*#__PURE__*/ new TextDecoder()\nconst encoder = /*#__PURE__*/ new TextEncoder()\n\n/** Root type for a Bytes array. */\nexport type Bytes = Uint8Array\n\n/**\n * Asserts if the given value is {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.assert('abc')\n * // @error: Bytes.InvalidBytesTypeError:\n * // @error: Value `\"abc\"` of type `string` is an invalid Bytes value.\n * // @error: Bytes values must be of type `Uint8Array`.\n * ```\n *\n * @param value - Value to assert.\n */\nexport function assert(value: unknown): asserts value is Bytes {\n  if (value instanceof Uint8Array) return\n  if (!value) throw new InvalidBytesTypeError(value)\n  if (typeof value !== 'object') throw new InvalidBytesTypeError(value)\n  if (!('BYTES_PER_ELEMENT' in value)) throw new InvalidBytesTypeError(value)\n  if (value.BYTES_PER_ELEMENT !== 1 || value.constructor.name !== 'Uint8Array')\n    throw new InvalidBytesTypeError(value)\n}\n\nexport declare namespace assert {\n  type ErrorType = InvalidBytesTypeError | Errors.GlobalErrorType\n}\n\n/**\n * Concatenates two or more {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const bytes = Bytes.concat(\n *   Bytes.from([1]),\n *   Bytes.from([69]),\n *   Bytes.from([420, 69]),\n * )\n * // @log: Uint8Array [ 1, 69, 420, 69 ]\n * ```\n *\n * @param values - Values to concatenate.\n * @returns Concatenated {@link ox#Bytes.Bytes}.\n */\nexport function concat(...values: readonly Bytes[]): Bytes {\n  let length = 0\n  for (const arr of values) {\n    length += arr.length\n  }\n  const result = new Uint8Array(length)\n  for (let i = 0, index = 0; i < values.length; i++) {\n    const arr = values[i]\n    result.set(arr!, index)\n    index += arr!.length\n  }\n  return result\n}\n\nexport declare namespace concat {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Instantiates a {@link ox#Bytes.Bytes} value from a `Uint8Array`, a hex string, or an array of unsigned 8-bit integers.\n *\n * :::tip\n *\n * To instantiate from a **Boolean**, **String**, or **Number**, use one of the following:\n *\n * - `Bytes.fromBoolean`\n *\n * - `Bytes.fromString`\n *\n * - `Bytes.fromNumber`\n *\n * :::\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.from([255, 124, 5, 4])\n * // @log: Uint8Array([255, 124, 5, 4])\n *\n * const data = Bytes.from('0xdeadbeef')\n * // @log: Uint8Array([222, 173, 190, 239])\n * ```\n *\n * @param value - Value to convert.\n * @returns A {@link ox#Bytes.Bytes} instance.\n */\nexport function from(value: Hex.Hex | Bytes | readonly number[]): Bytes {\n  if (value instanceof Uint8Array) return value\n  if (typeof value === 'string') return fromHex(value)\n  return fromArray(value)\n}\n\nexport declare namespace from {\n  type ErrorType =\n    | fromHex.ErrorType\n    | fromArray.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts an array of unsigned 8-bit integers into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromArray([255, 124, 5, 4])\n * // @log: Uint8Array([255, 124, 5, 4])\n * ```\n *\n * @param value - Value to convert.\n * @returns A {@link ox#Bytes.Bytes} instance.\n */\nexport function fromArray(value: readonly number[] | Uint8Array): Bytes {\n  return value instanceof Uint8Array ? value : new Uint8Array(value)\n}\n\nexport declare namespace fromArray {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Encodes a boolean value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromBoolean(true)\n * // @log: Uint8Array([1])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromBoolean(true, { size: 32 })\n * // @log: Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1])\n * ```\n *\n * @param value - Boolean value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nexport function fromBoolean(value: boolean, options: fromBoolean.Options = {}) {\n  const { size } = options\n  const bytes = new Uint8Array(1)\n  bytes[0] = Number(value)\n  if (typeof size === 'number') {\n    internal.assertSize(bytes, size)\n    return padLeft(bytes, size)\n  }\n  return bytes\n}\n\nexport declare namespace fromBoolean {\n  type Options = {\n    /** Size of the output bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | padLeft.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a {@link ox#Hex.Hex} value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromHex('0x48656c6c6f20776f726c6421')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromHex('0x48656c6c6f20776f726c6421', { size: 32 })\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n * ```\n *\n * @param value - {@link ox#Hex.Hex} value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nexport function fromHex(value: Hex.Hex, options: fromHex.Options = {}): Bytes {\n  const { size } = options\n\n  let hex = value\n  if (size) {\n    internal_hex.assertSize(value, size)\n    hex = Hex.padRight(value, size)\n  }\n\n  let hexString = hex.slice(2) as string\n  if (hexString.length % 2) hexString = `0${hexString}`\n\n  const length = hexString.length / 2\n  const bytes = new Uint8Array(length)\n  for (let index = 0, j = 0; index < length; index++) {\n    const nibbleLeft = internal.charCodeToBase16(hexString.charCodeAt(j++))\n    const nibbleRight = internal.charCodeToBase16(hexString.charCodeAt(j++))\n    if (nibbleLeft === undefined || nibbleRight === undefined) {\n      throw new Errors.BaseError(\n        `Invalid byte sequence (\"${hexString[j - 2]}${hexString[j - 1]}\" in \"${hexString}\").`,\n      )\n    }\n    bytes[index] = nibbleLeft * 16 + nibbleRight\n  }\n  return bytes\n}\n\nexport declare namespace fromHex {\n  type Options = {\n    /** Size of the output bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal_hex.assertSize.ErrorType\n    | Hex.padRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a number value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromNumber(420)\n * // @log: Uint8Array([1, 164])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromNumber(420, { size: 4 })\n * // @log: Uint8Array([0, 0, 1, 164])\n * ```\n *\n * @param value - Number value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nexport function fromNumber(\n  value: bigint | number,\n  options?: fromNumber.Options | undefined,\n) {\n  const hex = Hex.fromNumber(value, options)\n  return fromHex(hex)\n}\n\nexport declare namespace fromNumber {\n  export type Options = Hex.fromNumber.Options\n\n  export type ErrorType =\n    | Hex.fromNumber.ErrorType\n    | fromHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a string into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromString('Hello world!')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100, 33])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromString('Hello world!', { size: 32 })\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n * ```\n *\n * @param value - String to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nexport function fromString(\n  value: string,\n  options: fromString.Options = {},\n): Bytes {\n  const { size } = options\n\n  const bytes = encoder.encode(value)\n  if (typeof size === 'number') {\n    internal.assertSize(bytes, size)\n    return padRight(bytes, size)\n  }\n  return bytes\n}\n\nexport declare namespace fromString {\n  type Options = {\n    /** Size of the output bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | padRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Checks if two {@link ox#Bytes.Bytes} values are equal.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.isEqual(Bytes.from([1]), Bytes.from([1]))\n * // @log: true\n *\n * Bytes.isEqual(Bytes.from([1]), Bytes.from([2]))\n * // @log: false\n * ```\n *\n * @param bytesA - First {@link ox#Bytes.Bytes} value.\n * @param bytesB - Second {@link ox#Bytes.Bytes} value.\n * @returns `true` if the two values are equal, otherwise `false`.\n */\nexport function isEqual(bytesA: Bytes, bytesB: Bytes) {\n  return equalBytes(bytesA, bytesB)\n}\n\nexport declare namespace isEqual {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Pads a {@link ox#Bytes.Bytes} value to the left with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padLeft(Bytes.from([1]), 4)\n * // @log: Uint8Array([0, 0, 0, 1])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value to pad.\n * @param size - Size to pad the {@link ox#Bytes.Bytes} value to.\n * @returns Padded {@link ox#Bytes.Bytes} value.\n */\nexport function padLeft(\n  value: Bytes,\n  size?: number | undefined,\n): padLeft.ReturnType {\n  return internal.pad(value, { dir: 'left', size })\n}\n\nexport declare namespace padLeft {\n  type ReturnType = internal.pad.ReturnType\n  type ErrorType = internal.pad.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Pads a {@link ox#Bytes.Bytes} value to the right with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padRight(Bytes.from([1]), 4)\n * // @log: Uint8Array([1, 0, 0, 0])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value to pad.\n * @param size - Size to pad the {@link ox#Bytes.Bytes} value to.\n * @returns Padded {@link ox#Bytes.Bytes} value.\n */\nexport function padRight(\n  value: Bytes,\n  size?: number | undefined,\n): padRight.ReturnType {\n  return internal.pad(value, { dir: 'right', size })\n}\n\nexport declare namespace padRight {\n  type ReturnType = internal.pad.ReturnType\n  type ErrorType = internal.pad.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Generates random {@link ox#Bytes.Bytes} of the specified length.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const bytes = Bytes.random(32)\n * // @log: Uint8Array([... x32])\n * ```\n *\n * @param length - Length of the random {@link ox#Bytes.Bytes} to generate.\n * @returns Random {@link ox#Bytes.Bytes} of the specified length.\n */\nexport function random(length: number): Bytes {\n  return crypto.getRandomValues(new Uint8Array(length))\n}\n\nexport declare namespace random {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Retrieves the size of a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.size(Bytes.from([1, 2, 3, 4]))\n * // @log: 4\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Size of the {@link ox#Bytes.Bytes} value.\n */\nexport function size(value: Bytes): number {\n  return value.length\n}\n\nexport declare namespace size {\n  export type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Returns a section of a {@link ox#Bytes.Bytes} value given a start/end bytes offset.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.slice(\n *   Bytes.from([1, 2, 3, 4, 5, 6, 7, 8, 9]),\n *   1,\n *   4,\n * )\n * // @log: Uint8Array([2, 3, 4])\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value.\n * @param start - Start offset.\n * @param end - End offset.\n * @param options - Slice options.\n * @returns Sliced {@link ox#Bytes.Bytes} value.\n */\nexport function slice(\n  value: Bytes,\n  start?: number | undefined,\n  end?: number | undefined,\n  options: slice.Options = {},\n): Bytes {\n  const { strict } = options\n  internal.assertStartOffset(value, start)\n  const value_ = value.slice(start, end)\n  if (strict) internal.assertEndOffset(value_, start, end)\n  return value_\n}\n\nexport declare namespace slice {\n  type Options = {\n    /** Asserts that the sliced value is the same size as the given start/end offsets. */\n    strict?: boolean | undefined\n  }\n\n  export type ErrorType =\n    | internal.assertStartOffset.ErrorType\n    | internal.assertEndOffset.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a bigint.\n *\n * @example\n * ```ts\n * import { Bytes } from 'ox'\n *\n * Bytes.toBigInt(Bytes.from([1, 164]))\n * // @log: 420n\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Decoding options.\n * @returns Decoded bigint.\n */\nexport function toBigInt(bytes: Bytes, options: toBigInt.Options = {}): bigint {\n  const { size } = options\n  if (typeof size !== 'undefined') internal.assertSize(bytes, size)\n  const hex = Hex.fromBytes(bytes, options)\n  return Hex.toBigInt(hex, options)\n}\n\nexport declare namespace toBigInt {\n  type Options = {\n    /** Whether or not the number of a signed representation. */\n    signed?: boolean | undefined\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | Hex.fromBytes.ErrorType\n    | Hex.toBigInt.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a boolean.\n *\n * @example\n * ```ts\n * import { Bytes } from 'ox'\n *\n * Bytes.toBoolean(Bytes.from([1]))\n * // @log: true\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Decoding options.\n * @returns Decoded boolean.\n */\nexport function toBoolean(\n  bytes: Bytes,\n  options: toBoolean.Options = {},\n): boolean {\n  const { size } = options\n  let bytes_ = bytes\n  if (typeof size !== 'undefined') {\n    internal.assertSize(bytes_, size)\n    bytes_ = trimLeft(bytes_)\n  }\n  if (bytes_.length > 1 || bytes_[0]! > 1)\n    throw new InvalidBytesBooleanError(bytes_)\n  return Boolean(bytes_[0])\n}\n\nexport declare namespace toBoolean {\n  type Options = {\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | trimLeft.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a {@link ox#Bytes.Bytes} value into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toHex(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Options.\n * @returns Decoded {@link ox#Hex.Hex} value.\n */\nexport function toHex(value: Bytes, options: toHex.Options = {}): Hex.Hex {\n  return Hex.fromBytes(value, options)\n}\n\nexport declare namespace toHex {\n  type Options = {\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType = Hex.fromBytes.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a number.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toNumber(Bytes.from([1, 164]))\n * // @log: 420\n * ```\n */\nexport function toNumber(bytes: Bytes, options: toNumber.Options = {}): number {\n  const { size } = options\n  if (typeof size !== 'undefined') internal.assertSize(bytes, size)\n  const hex = Hex.fromBytes(bytes, options)\n  return Hex.toNumber(hex, options)\n}\n\nexport declare namespace toNumber {\n  type Options = {\n    /** Whether or not the number of a signed representation. */\n    signed?: boolean | undefined\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | Hex.fromBytes.ErrorType\n    | Hex.toNumber.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a string.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.toString(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: 'Hello world'\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Options.\n * @returns Decoded string.\n */\nexport function toString(bytes: Bytes, options: toString.Options = {}): string {\n  const { size } = options\n\n  let bytes_ = bytes\n  if (typeof size !== 'undefined') {\n    internal.assertSize(bytes_, size)\n    bytes_ = trimRight(bytes_)\n  }\n  return decoder.decode(bytes_)\n}\n\nexport declare namespace toString {\n  export type Options = {\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  export type ErrorType =\n    | internal.assertSize.ErrorType\n    | trimRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Trims leading zeros from a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.trimLeft(Bytes.from([0, 0, 0, 0, 1, 2, 3]))\n * // @log: Uint8Array([1, 2, 3])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Trimmed {@link ox#Bytes.Bytes} value.\n */\nexport function trimLeft(value: Bytes): Bytes {\n  return internal.trim(value, { dir: 'left' })\n}\n\nexport declare namespace trimLeft {\n  type ErrorType = internal.trim.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Trims trailing zeros from a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.trimRight(Bytes.from([1, 2, 3, 0, 0, 0, 0]))\n * // @log: Uint8Array([1, 2, 3])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Trimmed {@link ox#Bytes.Bytes} value.\n */\nexport function trimRight(value: Bytes): Bytes {\n  return internal.trim(value, { dir: 'right' })\n}\n\nexport declare namespace trimRight {\n  export type ErrorType = internal.trim.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Checks if the given value is {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.validate('0x')\n * // @log: false\n *\n * Bytes.validate(Bytes.from([1, 2, 3]))\n * // @log: true\n * ```\n *\n * @param value - Value to check.\n * @returns `true` if the value is {@link ox#Bytes.Bytes}, otherwise `false`.\n */\nexport function validate(value: unknown): value is Bytes {\n  try {\n    assert(value)\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  export type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Thrown when the bytes value cannot be represented as a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toBoolean(Bytes.from([5]))\n * // @error: Bytes.InvalidBytesBooleanError: Bytes value `[5]` is not a valid boolean.\n * // @error: The bytes array must contain a single byte of either a `0` or `1` value.\n * ```\n */\nexport class InvalidBytesBooleanError extends Errors.BaseError {\n  override readonly name = 'Bytes.InvalidBytesBooleanError'\n\n  constructor(bytes: Bytes) {\n    super(`Bytes value \\`${bytes}\\` is not a valid boolean.`, {\n      metaMessages: [\n        'The bytes array must contain a single byte of either a `0` or `1` value.',\n      ],\n    })\n  }\n}\n\n/**\n * Thrown when a value cannot be converted to bytes.\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Bytes } from 'ox'\n *\n * Bytes.from('foo')\n * // @error: Bytes.InvalidBytesTypeError: Value `foo` of type `string` is an invalid Bytes value.\n * ```\n */\nexport class InvalidBytesTypeError extends Errors.BaseError {\n  override readonly name = 'Bytes.InvalidBytesTypeError'\n\n  constructor(value: unknown) {\n    super(\n      `Value \\`${typeof value === 'object' ? Json.stringify(value) : value}\\` of type \\`${typeof value}\\` is an invalid Bytes value.`,\n      {\n        metaMessages: ['Bytes values must be of type `Bytes`.'],\n      },\n    )\n  }\n}\n\n/**\n * Thrown when a size exceeds the maximum allowed size.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.fromString('Hello World!', { size: 8 })\n * // @error: Bytes.SizeOverflowError: Size cannot exceed `8` bytes. Given size: `12` bytes.\n * ```\n */\nexport class SizeOverflowError extends Errors.BaseError {\n  override readonly name = 'Bytes.SizeOverflowError'\n\n  constructor({ givenSize, maxSize }: { givenSize: number; maxSize: number }) {\n    super(\n      `Size cannot exceed \\`${maxSize}\\` bytes. Given size: \\`${givenSize}\\` bytes.`,\n    )\n  }\n}\n\n/**\n * Thrown when a slice offset is out-of-bounds.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.slice(Bytes.from([1, 2, 3]), 4)\n * // @error: Bytes.SliceOffsetOutOfBoundsError: Slice starting at offset `4` is out-of-bounds (size: `3`).\n * ```\n */\nexport class SliceOffsetOutOfBoundsError extends Errors.BaseError {\n  override readonly name = 'Bytes.SliceOffsetOutOfBoundsError'\n\n  constructor({\n    offset,\n    position,\n    size,\n  }: { offset: number; position: 'start' | 'end'; size: number }) {\n    super(\n      `Slice ${\n        position === 'start' ? 'starting' : 'ending'\n      } at offset \\`${offset}\\` is out-of-bounds (size: \\`${size}\\`).`,\n    )\n  }\n}\n\n/**\n * Thrown when a the padding size exceeds the maximum allowed size.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padLeft(Bytes.fromString('Hello World!'), 8)\n * // @error: [Bytes.SizeExceedsPaddingSizeError: Bytes size (`12`) exceeds padding size (`8`).\n * ```\n */\nexport class SizeExceedsPaddingSizeError extends Errors.BaseError {\n  override readonly name = 'Bytes.SizeExceedsPaddingSizeError'\n\n  constructor({\n    size,\n    targetSize,\n    type,\n  }: {\n    size: number\n    targetSize: number\n    type: 'Hex' | 'Bytes'\n  }) {\n    super(\n      `${type.charAt(0).toUpperCase()}${type\n        .slice(1)\n        .toLowerCase()} size (\\`${size}\\`) exceeds padding size (\\`${targetSize}\\`).`,\n    )\n  }\n}\n", "import { equalBytes } from '@noble/curves/abstract/utils'\nimport * as Bytes from './Bytes.js'\nimport * as Errors from './Errors.js'\nimport * as Json from './Json.js'\nimport * as internal_bytes from './internal/bytes.js'\nimport * as internal from './internal/hex.js'\n\nconst encoder = /*#__PURE__*/ new TextEncoder()\n\nconst hexes = /*#__PURE__*/ Array.from({ length: 256 }, (_v, i) =>\n  i.toString(16).padStart(2, '0'),\n)\n\n/** Root type for a Hex string. */\nexport type Hex = `0x${string}`\n\n/**\n * Asserts if the given value is {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert('abc')\n * // @error: InvalidHexValueTypeError:\n * // @error: Value `\"abc\"` of type `string` is an invalid hex type.\n * // @error: Hex types must be represented as `\"0x\\${string}\"`.\n * ```\n *\n * @param value - The value to assert.\n * @param options - Options.\n */\nexport function assert(\n  value: unknown,\n  options: assert.Options = {},\n): asserts value is Hex {\n  const { strict = false } = options\n  if (!value) throw new InvalidHexTypeError(value)\n  if (typeof value !== 'string') throw new InvalidHexTypeError(value)\n  if (strict) {\n    if (!/^0x[0-9a-fA-F]*$/.test(value)) throw new InvalidHexValueError(value)\n  }\n  if (!value.startsWith('0x')) throw new InvalidHexValueError(value)\n}\n\nexport declare namespace assert {\n  type Options = {\n    /** Checks if the {@link ox#Hex.Hex} value contains invalid hexadecimal characters. @default false */\n    strict?: boolean | undefined\n  }\n\n  type ErrorType =\n    | InvalidHexTypeError\n    | InvalidHexValueError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Concatenates two or more {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.concat('0x123', '0x456')\n * // @log: '0x123456'\n * ```\n *\n * @param values - The {@link ox#Hex.Hex} values to concatenate.\n * @returns The concatenated {@link ox#Hex.Hex} value.\n */\nexport function concat(...values: readonly Hex[]): Hex {\n  return `0x${(values as Hex[]).reduce((acc, x) => acc + x.replace('0x', ''), '')}`\n}\n\nexport declare namespace concat {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Instantiates a {@link ox#Hex.Hex} value from a hex string or {@link ox#Bytes.Bytes} value.\n *\n * :::tip\n *\n * To instantiate from a **Boolean**, **String**, or **Number**, use one of the following:\n *\n * - `Hex.fromBoolean`\n *\n * - `Hex.fromString`\n *\n * - `Hex.fromNumber`\n *\n * :::\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.from('0x48656c6c6f20576f726c6421')\n * // @log: '0x48656c6c6f20576f726c6421'\n *\n * Hex.from(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value to encode.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function from(value: Hex | Bytes.Bytes | readonly number[]): Hex {\n  if (value instanceof Uint8Array) return fromBytes(value)\n  if (Array.isArray(value)) return fromBytes(new Uint8Array(value))\n  return value as never\n}\n\nexport declare namespace from {\n  type Options = {\n    /** The size (in bytes) of the output hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType = fromBytes.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a boolean into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromBoolean(true)\n * // @log: '0x1'\n *\n * Hex.fromBoolean(false)\n * // @log: '0x0'\n *\n * Hex.fromBoolean(true, { size: 32 })\n * // @log: '0x0000000000000000000000000000000000000000000000000000000000000001'\n * ```\n *\n * @param value - The boolean value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function fromBoolean(\n  value: boolean,\n  options: fromBoolean.Options = {},\n): Hex {\n  const hex: Hex = `0x${Number(value)}`\n  if (typeof options.size === 'number') {\n    internal.assertSize(hex, options.size)\n    return padLeft(hex, options.size)\n  }\n  return hex\n}\n\nexport declare namespace fromBoolean {\n  type Options = {\n    /** The size (in bytes) of the output hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | padLeft.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a {@link ox#Bytes.Bytes} value into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.fromBytes(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function fromBytes(\n  value: Bytes.Bytes,\n  options: fromBytes.Options = {},\n): Hex {\n  let string = ''\n  for (let i = 0; i < value.length; i++) string += hexes[value[i]!]\n  const hex = `0x${string}` as const\n\n  if (typeof options.size === 'number') {\n    internal.assertSize(hex, options.size)\n    return padRight(hex, options.size)\n  }\n  return hex\n}\n\nexport declare namespace fromBytes {\n  type Options = {\n    /** The size (in bytes) of the output hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | padRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a number or bigint into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromNumber(420)\n * // @log: '0x1a4'\n *\n * Hex.fromNumber(420, { size: 32 })\n * // @log: '0x00000000000000000000000000000000000000000000000000000000000001a4'\n * ```\n *\n * @param value - The number or bigint value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function fromNumber(\n  value: number | bigint,\n  options: fromNumber.Options = {},\n): Hex {\n  const { signed, size } = options\n\n  const value_ = BigInt(value)\n\n  let maxValue: bigint | number | undefined\n  if (size) {\n    if (signed) maxValue = (1n << (BigInt(size) * 8n - 1n)) - 1n\n    else maxValue = 2n ** (BigInt(size) * 8n) - 1n\n  } else if (typeof value === 'number') {\n    maxValue = BigInt(Number.MAX_SAFE_INTEGER)\n  }\n\n  const minValue = typeof maxValue === 'bigint' && signed ? -maxValue - 1n : 0\n\n  if ((maxValue && value_ > maxValue) || value_ < minValue) {\n    const suffix = typeof value === 'bigint' ? 'n' : ''\n    throw new IntegerOutOfRangeError({\n      max: maxValue ? `${maxValue}${suffix}` : undefined,\n      min: `${minValue}${suffix}`,\n      signed,\n      size,\n      value: `${value}${suffix}`,\n    })\n  }\n\n  const stringValue = (\n    signed && value_ < 0 ? (1n << BigInt(size * 8)) + BigInt(value_) : value_\n  ).toString(16)\n\n  const hex = `0x${stringValue}` as Hex\n  if (size) return padLeft(hex, size) as Hex\n  return hex\n}\n\nexport declare namespace fromNumber {\n  type Options =\n    | {\n        /** Whether or not the number of a signed representation. */\n        signed?: boolean | undefined\n        /** The size (in bytes) of the output hex value. */\n        size: number\n      }\n    | {\n        signed?: undefined\n        /** The size (in bytes) of the output hex value. */\n        size?: number | undefined\n      }\n\n  type ErrorType =\n    | IntegerOutOfRangeError\n    | padLeft.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a string into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n * Hex.fromString('Hello World!')\n * // '0x48656c6c6f20576f726c6421'\n *\n * Hex.fromString('Hello World!', { size: 32 })\n * // '0x48656c6c6f20576f726c64210000000000000000000000000000000000000000'\n * ```\n *\n * @param value - The string value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function fromString(\n  value: string,\n  options: fromString.Options = {},\n): Hex {\n  return fromBytes(encoder.encode(value), options)\n}\n\nexport declare namespace fromString {\n  type Options = {\n    /** The size (in bytes) of the output hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType = fromBytes.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Checks if two {@link ox#Hex.Hex} values are equal.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.isEqual('0xdeadbeef', '0xdeadbeef')\n * // @log: true\n *\n * Hex.isEqual('0xda', '0xba')\n * // @log: false\n * ```\n *\n * @param hexA - The first {@link ox#Hex.Hex} value.\n * @param hexB - The second {@link ox#Hex.Hex} value.\n * @returns `true` if the two {@link ox#Hex.Hex} values are equal, `false` otherwise.\n */\nexport function isEqual(hexA: Hex, hexB: Hex) {\n  return equalBytes(Bytes.fromHex(hexA), Bytes.fromHex(hexB))\n}\n\nexport declare namespace isEqual {\n  type ErrorType = Bytes.fromHex.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Pads a {@link ox#Hex.Hex} value to the left with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.padLeft('0x1234', 4)\n * // @log: '0x00001234'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to pad.\n * @param size - The size (in bytes) of the output hex value.\n * @returns The padded {@link ox#Hex.Hex} value.\n */\nexport function padLeft(\n  value: Hex,\n  size?: number | undefined,\n): padLeft.ReturnType {\n  return internal.pad(value, { dir: 'left', size })\n}\n\nexport declare namespace padLeft {\n  type ReturnType = Hex\n  type ErrorType = internal.pad.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Pads a {@link ox#Hex.Hex} value to the right with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts\n * import { Hex } from 'ox'\n *\n * Hex.padRight('0x1234', 4)\n * // @log: '0x12340000'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to pad.\n * @param size - The size (in bytes) of the output hex value.\n * @returns The padded {@link ox#Hex.Hex} value.\n */\nexport function padRight(\n  value: Hex,\n  size?: number | undefined,\n): padRight.ReturnType {\n  return internal.pad(value, { dir: 'right', size })\n}\n\nexport declare namespace padRight {\n  type ReturnType = Hex\n  type ErrorType = internal.pad.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Generates a random {@link ox#Hex.Hex} value of the specified length.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * const hex = Hex.random(32)\n * // @log: '0x...'\n * ```\n *\n * @returns Random {@link ox#Hex.Hex} value.\n */\nexport function random(length: number): Hex {\n  return fromBytes(Bytes.random(length))\n}\n\nexport declare namespace random {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Returns a section of a {@link ox#Bytes.Bytes} value given a start/end bytes offset.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.slice('0x0123456789', 1, 4)\n * // @log: '0x234567'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to slice.\n * @param start - The start offset (in bytes).\n * @param end - The end offset (in bytes).\n * @param options - Options.\n * @returns The sliced {@link ox#Hex.Hex} value.\n */\nexport function slice(\n  value: Hex,\n  start?: number | undefined,\n  end?: number | undefined,\n  options: slice.Options = {},\n): Hex {\n  const { strict } = options\n  internal.assertStartOffset(value, start)\n  const value_ = `0x${value\n    .replace('0x', '')\n    .slice((start ?? 0) * 2, (end ?? value.length) * 2)}` as const\n  if (strict) internal.assertEndOffset(value_, start, end)\n  return value_\n}\n\nexport declare namespace slice {\n  type Options = {\n    /** Asserts that the sliced value is the same size as the given start/end offsets. */\n    strict?: boolean | undefined\n  }\n\n  type ErrorType =\n    | internal.assertStartOffset.ErrorType\n    | internal.assertEndOffset.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Retrieves the size of a {@link ox#Hex.Hex} value (in bytes).\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.size('0xdeadbeef')\n * // @log: 4\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to get the size of.\n * @returns The size of the {@link ox#Hex.Hex} value (in bytes).\n */\nexport function size(value: Hex): number {\n  return Math.ceil((value.length - 2) / 2)\n}\n\nexport declare namespace size {\n  export type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Trims leading zeros from a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.trimLeft('0x00000000deadbeef')\n * // @log: '0xdeadbeef'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to trim.\n * @returns The trimmed {@link ox#Hex.Hex} value.\n */\nexport function trimLeft(value: Hex): trimLeft.ReturnType {\n  return internal.trim(value, { dir: 'left' })\n}\n\nexport declare namespace trimLeft {\n  type ReturnType = Hex\n\n  type ErrorType = internal.trim.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Trims trailing zeros from a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.trimRight('0xdeadbeef00000000')\n * // @log: '0xdeadbeef'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to trim.\n * @returns The trimmed {@link ox#Hex.Hex} value.\n */\nexport function trimRight(value: Hex): trimRight.ReturnType {\n  return internal.trim(value, { dir: 'right' })\n}\n\nexport declare namespace trimRight {\n  type ReturnType = Hex\n\n  type ErrorType = internal.trim.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a BigInt.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBigInt('0x1a4')\n * // @log: 420n\n *\n * Hex.toBigInt('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // @log: 420n\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded BigInt.\n */\nexport function toBigInt(hex: Hex, options: toBigInt.Options = {}): bigint {\n  const { signed } = options\n\n  if (options.size) internal.assertSize(hex, options.size)\n\n  const value = BigInt(hex)\n  if (!signed) return value\n\n  const size = (hex.length - 2) / 2\n\n  const max_unsigned = (1n << (BigInt(size) * 8n)) - 1n\n  const max_signed = max_unsigned >> 1n\n\n  if (value <= max_signed) return value\n  return value - max_unsigned - 1n\n}\n\nexport declare namespace toBigInt {\n  type Options = {\n    /** Whether or not the number of a signed representation. */\n    signed?: boolean | undefined\n    /** Size (in bytes) of the hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType = internal.assertSize.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBoolean('0x01')\n * // @log: true\n *\n * Hex.toBoolean('0x0000000000000000000000000000000000000000000000000000000000000001', { size: 32 })\n * // @log: true\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded boolean.\n */\nexport function toBoolean(hex: Hex, options: toBoolean.Options = {}): boolean {\n  if (options.size) internal.assertSize(hex, options.size)\n  const hex_ = trimLeft(hex)\n  if (hex_ === '0x') return false\n  if (hex_ === '0x1') return true\n  throw new InvalidHexBooleanError(hex)\n}\n\nexport declare namespace toBoolean {\n  type Options = {\n    /** Size (in bytes) of the hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | trimLeft.ErrorType\n    | InvalidHexBooleanError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * const data = Hex.toBytes('0x48656c6c6f20776f726c6421')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded {@link ox#Bytes.Bytes}.\n */\nexport function toBytes(hex: Hex, options: toBytes.Options = {}): Bytes.Bytes {\n  return Bytes.fromHex(hex, options)\n}\n\nexport declare namespace toBytes {\n  type Options = {\n    /** Size (in bytes) of the hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType = Bytes.fromHex.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a number.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toNumber('0x1a4')\n * // @log: 420\n *\n * Hex.toNumber('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // @log: 420\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded number.\n */\nexport function toNumber(hex: Hex, options: toNumber.Options = {}): number {\n  const { signed, size } = options\n  if (!signed && !size) return Number(hex)\n  return Number(toBigInt(hex, options))\n}\n\nexport declare namespace toNumber {\n  type Options = toBigInt.Options\n\n  type ErrorType = toBigInt.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a string.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toString('0x48656c6c6f20576f726c6421')\n * // @log: 'Hello world!'\n *\n * Hex.toString('0x48656c6c6f20576f726c64210000000000000000000000000000000000000000', {\n *  size: 32,\n * })\n * // @log: 'Hello world'\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded string.\n */\nexport function toString(hex: Hex, options: toString.Options = {}): string {\n  const { size } = options\n\n  let bytes = Bytes.fromHex(hex)\n  if (size) {\n    internal_bytes.assertSize(bytes, size)\n    bytes = Bytes.trimRight(bytes)\n  }\n  return new TextDecoder().decode(bytes)\n}\n\nexport declare namespace toString {\n  type Options = {\n    /** Size (in bytes) of the hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal_bytes.assertSize.ErrorType\n    | Bytes.fromHex.ErrorType\n    | Bytes.trimRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Checks if the given value is {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.validate('0xdeadbeef')\n * // @log: true\n *\n * Hex.validate(Bytes.from([1, 2, 3]))\n * // @log: false\n * ```\n *\n * @param value - The value to check.\n * @param options - Options.\n * @returns `true` if the value is a {@link ox#Hex.Hex}, `false` otherwise.\n */\nexport function validate(\n  value: unknown,\n  options: validate.Options = {},\n): value is Hex {\n  const { strict = false } = options\n  try {\n    assert(value, { strict })\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  type Options = {\n    /** Checks if the {@link ox#Hex.Hex} value contains invalid hexadecimal characters. @default false */\n    strict?: boolean | undefined\n  }\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Thrown when the provided integer is out of range, and cannot be represented as a hex value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromNumber(420182738912731283712937129)\n * // @error: Hex.IntegerOutOfRangeError: Number \\`4.2018273891273126e+26\\` is not in safe unsigned integer range (`0` to `9007199254740991`)\n * ```\n */\nexport class IntegerOutOfRangeError extends Errors.BaseError {\n  override readonly name = 'Hex.IntegerOutOfRangeError'\n\n  constructor({\n    max,\n    min,\n    signed,\n    size,\n    value,\n  }: {\n    max?: string | undefined\n    min: string\n    signed?: boolean | undefined\n    size?: number | undefined\n    value: string\n  }) {\n    super(\n      `Number \\`${value}\\` is not in safe${\n        size ? ` ${size * 8}-bit` : ''\n      }${signed ? ' signed' : ' unsigned'} integer range ${max ? `(\\`${min}\\` to \\`${max}\\`)` : `(above \\`${min}\\`)`}`,\n    )\n  }\n}\n\n/**\n * Thrown when the provided hex value cannot be represented as a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBoolean('0xa')\n * // @error: Hex.InvalidHexBooleanError: Hex value `\"0xa\"` is not a valid boolean.\n * // @error: The hex value must be `\"0x0\"` (false) or `\"0x1\"` (true).\n * ```\n */\nexport class InvalidHexBooleanError extends Errors.BaseError {\n  override readonly name = 'Hex.InvalidHexBooleanError'\n\n  constructor(hex: Hex) {\n    super(`Hex value \\`\"${hex}\"\\` is not a valid boolean.`, {\n      metaMessages: [\n        'The hex value must be `\"0x0\"` (false) or `\"0x1\"` (true).',\n      ],\n    })\n  }\n}\n\n/**\n * Thrown when the provided value is not a valid hex type.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert(1)\n * // @error: Hex.InvalidHexTypeError: Value `1` of type `number` is an invalid hex type.\n * ```\n */\nexport class InvalidHexTypeError extends Errors.BaseError {\n  override readonly name = 'Hex.InvalidHexTypeError'\n\n  constructor(value: unknown) {\n    super(\n      `Value \\`${typeof value === 'object' ? Json.stringify(value) : value}\\` of type \\`${typeof value}\\` is an invalid hex type.`,\n      {\n        metaMessages: ['Hex types must be represented as `\"0x${string}\"`.'],\n      },\n    )\n  }\n}\n\n/**\n * Thrown when the provided hex value is invalid.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert('0x0123456789abcdefg')\n * // @error: Hex.InvalidHexValueError: Value `0x0123456789abcdefg` is an invalid hex value.\n * // @error: Hex values must start with `\"0x\"` and contain only hexadecimal characters (0-9, a-f, A-F).\n * ```\n */\nexport class InvalidHexValueError extends Errors.BaseError {\n  override readonly name = 'Hex.InvalidHexValueError'\n\n  constructor(value: unknown) {\n    super(`Value \\`${value}\\` is an invalid hex value.`, {\n      metaMessages: [\n        'Hex values must start with `\"0x\"` and contain only hexadecimal characters (0-9, a-f, A-F).',\n      ],\n    })\n  }\n}\n\n/**\n * Thrown when the provided hex value is an odd length.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.fromHex('0xabcde')\n * // @error: Hex.InvalidLengthError: Hex value `\"0xabcde\"` is an odd length (5 nibbles).\n * ```\n */\nexport class InvalidLengthError extends Errors.BaseError {\n  override readonly name = 'Hex.InvalidLengthError'\n\n  constructor(value: Hex) {\n    super(\n      `Hex value \\`\"${value}\"\\` is an odd length (${value.length - 2} nibbles).`,\n      {\n        metaMessages: ['It must be an even length.'],\n      },\n    )\n  }\n}\n\n/**\n * Thrown when the size of the value exceeds the expected max size.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromString('Hello World!', { size: 8 })\n * // @error: Hex.SizeOverflowError: Size cannot exceed `8` bytes. Given size: `12` bytes.\n * ```\n */\nexport class SizeOverflowError extends Errors.BaseError {\n  override readonly name = 'Hex.SizeOverflowError'\n\n  constructor({ givenSize, maxSize }: { givenSize: number; maxSize: number }) {\n    super(\n      `Size cannot exceed \\`${maxSize}\\` bytes. Given size: \\`${givenSize}\\` bytes.`,\n    )\n  }\n}\n\n/**\n * Thrown when the slice offset exceeds the bounds of the value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.slice('0x0123456789', 6)\n * // @error: Hex.SliceOffsetOutOfBoundsError: Slice starting at offset `6` is out-of-bounds (size: `5`).\n * ```\n */\nexport class SliceOffsetOutOfBoundsError extends Errors.BaseError {\n  override readonly name = 'Hex.SliceOffsetOutOfBoundsError'\n\n  constructor({\n    offset,\n    position,\n    size,\n  }: { offset: number; position: 'start' | 'end'; size: number }) {\n    super(\n      `Slice ${\n        position === 'start' ? 'starting' : 'ending'\n      } at offset \\`${offset}\\` is out-of-bounds (size: \\`${size}\\`).`,\n    )\n  }\n}\n\n/**\n * Thrown when the size of the value exceeds the pad size.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.padLeft('0x1a4e12a45a21323123aaa87a897a897a898a6567a578a867a98778a667a85a875a87a6a787a65a675a6a9', 32)\n * // @error: Hex.SizeExceedsPaddingSizeError: Hex size (`43`) exceeds padding size (`32`).\n * ```\n */\nexport class SizeExceedsPaddingSizeError extends Errors.BaseError {\n  override readonly name = 'Hex.SizeExceedsPaddingSizeError'\n\n  constructor({\n    size,\n    targetSize,\n    type,\n  }: {\n    size: number\n    targetSize: number\n    type: 'Hex' | 'Bytes'\n  }) {\n    super(\n      `${type.charAt(0).toUpperCase()}${type\n        .slice(1)\n        .toLowerCase()} size (\\`${size}\\`) exceeds padding size (\\`${targetSize}\\`).`,\n    )\n  }\n}\n", "import type * as Errors from './Errors.js'\nimport * as Hex from './Hex.js'\n\n/** A Withdrawal as defined in the [Execution API specification](https://github.com/ethereum/execution-apis/blob/main/src/schemas/withdrawal.yaml). */\nexport type Withdrawal<bigintType = bigint, numberType = number> = {\n  address: Hex.Hex\n  amount: bigintType\n  index: numberType\n  validatorIndex: numberType\n}\n\n/** An RPC Withdrawal as defined in the [Execution API specification](https://github.com/ethereum/execution-apis/blob/main/src/schemas/withdrawal.yaml). */\nexport type Rpc = Withdrawal<Hex.Hex, Hex.Hex>\n\n/**\n * Converts a {@link ox#Withdrawal.Rpc} to an {@link ox#Withdrawal.Withdrawal}.\n *\n * @example\n * ```ts twoslash\n * import { Withdrawal } from 'ox'\n *\n * const withdrawal = Withdrawal.fromRpc({\n *   address: '******************************************',\n *   amount: '0x620323',\n *   index: '0x0',\n *   validatorIndex: '0x1',\n * })\n * // @log: {\n * // @log:   address: '******************************************',\n * // @log:   amount: 6423331n,\n * // @log:   index: 0,\n * // @log:   validatorIndex: 1\n * // @log: }\n * ```\n *\n * @param withdrawal - The RPC withdrawal to convert.\n * @returns An instantiated {@link ox#Withdrawal.Withdrawal}.\n */\nexport function fromRpc(withdrawal: Rpc): Withdrawal {\n  return {\n    ...withdrawal,\n    amount: BigInt(withdrawal.amount),\n    index: Number(withdrawal.index),\n    validatorIndex: Number(withdrawal.validatorIndex),\n  }\n}\n\nexport declare namespace fromRpc {\n  export type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Converts a {@link ox#Withdrawal.Withdrawal} to an {@link ox#Withdrawal.Rpc}.\n *\n * @example\n * ```ts twoslash\n * import { Withdrawal } from 'ox'\n *\n * const withdrawal = Withdrawal.toRpc({\n *   address: '******************************************',\n *   amount: 6423331n,\n *   index: 0,\n *   validatorIndex: 1,\n * })\n * // @log: {\n * // @log:   address: '******************************************',\n * // @log:   amount: '0x620323',\n * // @log:   index: '0x0',\n * // @log:   validatorIndex: '0x1',\n * // @log: }\n * ```\n *\n * @param withdrawal - The Withdrawal to convert.\n * @returns An RPC Withdrawal.\n */\nexport function toRpc(withdrawal: Withdrawal): Rpc {\n  return {\n    address: withdrawal.address,\n    amount: Hex.fromNumber(withdrawal.amount),\n    index: Hex.fromNumber(withdrawal.index),\n    validatorIndex: Hex.fromNumber(withdrawal.validatorIndex),\n  }\n}\n\nexport declare namespace toRpc {\n  export type ErrorType = Errors.GlobalErrorType\n}\n", "import type * as Address from './Address.js'\nimport * as Hex from './Hex.js'\nimport * as Withdrawal from './Withdrawal.js'\n\n/**\n * Block overrides.\n */\nexport type BlockOverrides<bigintType = bigint, numberType = number> = {\n  /** Base fee per gas. */\n  baseFeePerGas?: bigintType | undefined\n  /** Blob base fee. */\n  blobBaseFee?: bigintType | undefined\n  /** Fee recipient (also known as coinbase). */\n  feeRecipient?: Address.Address | undefined\n  /** Gas limit. */\n  gasLimit?: bigintType | undefined\n  /** Block number. */\n  number?: bigintType | undefined\n  /** The previous value of randomness beacon. */\n  prevRandao?: bigintType | undefined\n  /** Block timestamp. */\n  time?: bigintType | undefined\n  /** Withdrawals made by validators. */\n  withdrawals?: Withdrawal.Withdrawal<bigintType, numberType>[] | undefined\n}\n\n/**\n * RPC block overrides.\n */\nexport type Rpc = BlockOverrides<Hex.Hex, Hex.Hex>\n\n/**\n * Converts an {@link ox#BlockOverrides.Rpc} to an {@link ox#BlockOverrides.BlockOverrides}.\n *\n * @example\n * ```ts twoslash\n * import { BlockOverrides } from 'ox'\n *\n * const blockOverrides = BlockOverrides.fromRpc({\n *   baseFeePerGas: '0x1',\n *   blobBaseFee: '0x2',\n *   feeRecipient: '0x0000000000000000000000000000000000000000',\n *   gasLimit: '0x4',\n *   number: '0x5',\n *   prevRandao: '0x6',\n *   time: '0x1234567890',\n *   withdrawals: [\n *     {\n *       address: '0x0000000000000000000000000000000000000000',\n *       amount: '0x1',\n *       index: '0x0',\n *       validatorIndex: '0x1',\n *     },\n *   ],\n * })\n * ```\n *\n * @param rpcBlockOverrides - The RPC block overrides to convert.\n * @returns An instantiated {@link ox#BlockOverrides.BlockOverrides}.\n */\nexport function fromRpc(rpcBlockOverrides: Rpc): BlockOverrides {\n  return {\n    ...(rpcBlockOverrides.baseFeePerGas && {\n      baseFeePerGas: BigInt(rpcBlockOverrides.baseFeePerGas),\n    }),\n    ...(rpcBlockOverrides.blobBaseFee && {\n      blobBaseFee: BigInt(rpcBlockOverrides.blobBaseFee),\n    }),\n    ...(rpcBlockOverrides.feeRecipient && {\n      feeRecipient: rpcBlockOverrides.feeRecipient,\n    }),\n    ...(rpcBlockOverrides.gasLimit && {\n      gasLimit: BigInt(rpcBlockOverrides.gasLimit),\n    }),\n    ...(rpcBlockOverrides.number && {\n      number: BigInt(rpcBlockOverrides.number),\n    }),\n    ...(rpcBlockOverrides.prevRandao && {\n      prevRandao: BigInt(rpcBlockOverrides.prevRandao),\n    }),\n    ...(rpcBlockOverrides.time && {\n      time: BigInt(rpcBlockOverrides.time),\n    }),\n    ...(rpcBlockOverrides.withdrawals && {\n      withdrawals: rpcBlockOverrides.withdrawals.map(Withdrawal.fromRpc),\n    }),\n  }\n}\n\n/**\n * Converts an {@link ox#BlockOverrides.BlockOverrides} to an {@link ox#BlockOverrides.Rpc}.\n *\n * @example\n * ```ts twoslash\n * import { BlockOverrides } from 'ox'\n *\n * const blockOverrides = BlockOverrides.toRpc({\n *   baseFeePerGas: 1n,\n *   blobBaseFee: 2n,\n *   feeRecipient: '0x0000000000000000000000000000000000000000',\n *   gasLimit: 4n,\n *   number: 5n,\n *   prevRandao: 6n,\n *   time: 78187493520n,\n *   withdrawals: [\n *     {\n *       address: '0x0000000000000000000000000000000000000000',\n *       amount: 1n,\n *       index: 0,\n *       validatorIndex: 1,\n *     },\n *   ],\n * })\n * ```\n *\n * @param blockOverrides - The block overrides to convert.\n * @returns An instantiated {@link ox#BlockOverrides.Rpc}.\n */\nexport function toRpc(blockOverrides: BlockOverrides): Rpc {\n  return {\n    ...(typeof blockOverrides.baseFeePerGas === 'bigint' && {\n      baseFeePerGas: Hex.fromNumber(blockOverrides.baseFeePerGas),\n    }),\n    ...(typeof blockOverrides.blobBaseFee === 'bigint' && {\n      blobBaseFee: Hex.fromNumber(blockOverrides.blobBaseFee),\n    }),\n    ...(typeof blockOverrides.feeRecipient === 'string' && {\n      feeRecipient: blockOverrides.feeRecipient,\n    }),\n    ...(typeof blockOverrides.gasLimit === 'bigint' && {\n      gasLimit: Hex.fromNumber(blockOverrides.gasLimit),\n    }),\n    ...(typeof blockOverrides.number === 'bigint' && {\n      number: Hex.fromNumber(blockOverrides.number),\n    }),\n    ...(typeof blockOverrides.prevRandao === 'bigint' && {\n      prevRandao: Hex.fromNumber(blockOverrides.prevRandao),\n    }),\n    ...(typeof blockOverrides.time === 'bigint' && {\n      time: Hex.fromNumber(blockOverrides.time),\n    }),\n    ...(blockOverrides.withdrawals && {\n      withdrawals: blockOverrides.withdrawals.map(Withdrawal.toRpc),\n    }),\n  }\n}\n", "/* [Multicall3](https://github.com/mds1/multicall) */\nexport const multicall3Abi = [\n  {\n    inputs: [\n      {\n        components: [\n          {\n            name: 'target',\n            type: 'address',\n          },\n          {\n            name: 'allowFailure',\n            type: 'bool',\n          },\n          {\n            name: 'callData',\n            type: 'bytes',\n          },\n        ],\n        name: 'calls',\n        type: 'tuple[]',\n      },\n    ],\n    name: 'aggregate3',\n    outputs: [\n      {\n        components: [\n          {\n            name: 'success',\n            type: 'bool',\n          },\n          {\n            name: 'returnData',\n            type: 'bytes',\n          },\n        ],\n        name: 'returnData',\n        type: 'tuple[]',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n] as const\n\nexport const batchGatewayAbi = [\n  {\n    name: 'query',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      {\n        type: 'tuple[]',\n        name: 'queries',\n        components: [\n          {\n            type: 'address',\n            name: 'sender',\n          },\n          {\n            type: 'string[]',\n            name: 'urls',\n          },\n          {\n            type: 'bytes',\n            name: 'data',\n          },\n        ],\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool[]',\n        name: 'failures',\n      },\n      {\n        type: 'bytes[]',\n        name: 'responses',\n      },\n    ],\n  },\n  {\n    name: 'HttpError',\n    type: 'error',\n    inputs: [\n      {\n        type: 'uint16',\n        name: 'status',\n      },\n      {\n        type: 'string',\n        name: 'message',\n      },\n    ],\n  },\n] as const\n\nconst universalResolverErrors = [\n  {\n    inputs: [],\n    name: 'ResolverNotFound',\n    type: 'error',\n  },\n  {\n    inputs: [],\n    name: 'ResolverWildcardNotSupported',\n    type: 'error',\n  },\n  {\n    inputs: [],\n    name: 'ResolverNotContract',\n    type: 'error',\n  },\n  {\n    inputs: [\n      {\n        name: 'returnData',\n        type: 'bytes',\n      },\n    ],\n    name: 'ResolverError',\n    type: 'error',\n  },\n  {\n    inputs: [\n      {\n        components: [\n          {\n            name: 'status',\n            type: 'uint16',\n          },\n          {\n            name: 'message',\n            type: 'string',\n          },\n        ],\n        name: 'errors',\n        type: 'tuple[]',\n      },\n    ],\n    name: 'HttpError',\n    type: 'error',\n  },\n] as const\n\nexport const universalResolverResolveAbi = [\n  ...universalResolverErrors,\n  {\n    name: 'resolve',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { name: 'name', type: 'bytes' },\n      { name: 'data', type: 'bytes' },\n    ],\n    outputs: [\n      { name: '', type: 'bytes' },\n      { name: 'address', type: 'address' },\n    ],\n  },\n  {\n    name: 'resolve',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { name: 'name', type: 'bytes' },\n      { name: 'data', type: 'bytes' },\n      { name: 'gateways', type: 'string[]' },\n    ],\n    outputs: [\n      { name: '', type: 'bytes' },\n      { name: 'address', type: 'address' },\n    ],\n  },\n] as const\n\nexport const universalResolverReverseAbi = [\n  ...universalResolverErrors,\n  {\n    name: 'reverse',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [{ type: 'bytes', name: 'reverseName' }],\n    outputs: [\n      { type: 'string', name: 'resolvedName' },\n      { type: 'address', name: 'resolvedAddress' },\n      { type: 'address', name: 'reverseResolver' },\n      { type: 'address', name: 'resolver' },\n    ],\n  },\n  {\n    name: 'reverse',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { type: 'bytes', name: 'reverseName' },\n      { type: 'string[]', name: 'gateways' },\n    ],\n    outputs: [\n      { type: 'string', name: 'resolvedName' },\n      { type: 'address', name: 'resolvedAddress' },\n      { type: 'address', name: 'reverseResolver' },\n      { type: 'address', name: 'resolver' },\n    ],\n  },\n] as const\n\nexport const textResolverAbi = [\n  {\n    name: 'text',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { name: 'name', type: 'bytes32' },\n      { name: 'key', type: 'string' },\n    ],\n    outputs: [{ name: '', type: 'string' }],\n  },\n] as const\n\nexport const addressResolverAbi = [\n  {\n    name: 'addr',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [{ name: 'name', type: 'bytes32' }],\n    outputs: [{ name: '', type: 'address' }],\n  },\n  {\n    name: 'addr',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { name: 'name', type: 'bytes32' },\n      { name: 'coinType', type: 'uint256' },\n    ],\n    outputs: [{ name: '', type: 'bytes' }],\n  },\n] as const\n\n// ERC-1271\n// isValidSignature(bytes32 hash, bytes signature) → bytes4 magicValue\n/** @internal */\nexport const smartAccountAbi = [\n  {\n    name: 'isValidSignature',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { name: 'hash', type: 'bytes32' },\n      { name: 'signature', type: 'bytes' },\n    ],\n    outputs: [{ name: '', type: 'bytes4' }],\n  },\n] as const\n\n// ERC-6492 - universal deployless signature validator contract\n// constructor(address _signer, bytes32 _hash, bytes _signature) → bytes4 returnValue\n// returnValue is either 0x1 (valid) or 0x0 (invalid)\nexport const universalSignatureValidatorAbi = [\n  {\n    inputs: [\n      {\n        name: '_signer',\n        type: 'address',\n      },\n      {\n        name: '_hash',\n        type: 'bytes32',\n      },\n      {\n        name: '_signature',\n        type: 'bytes',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'constructor',\n  },\n  {\n    inputs: [\n      {\n        name: '_signer',\n        type: 'address',\n      },\n      {\n        name: '_hash',\n        type: 'bytes32',\n      },\n      {\n        name: '_signature',\n        type: 'bytes',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n    name: 'isValidSig',\n  },\n] as const\n\n/** [ERC-20 Token Standard](https://ethereum.org/en/developers/docs/standards/tokens/erc-20) */\nexport const erc20Abi = [\n  {\n    type: 'event',\n    name: 'Approval',\n    inputs: [\n      {\n        indexed: true,\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'value',\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'event',\n    name: 'Transfer',\n    inputs: [\n      {\n        indexed: true,\n        name: 'from',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'to',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'value',\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'allowance',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        name: 'spender',\n        type: 'address',\n      },\n    ],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'approve',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'balanceOf',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'account',\n        type: 'address',\n      },\n    ],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'decimals',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'uint8',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'name',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'string',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'symbol',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'string',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'totalSupply',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'transfer',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'recipient',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'transferFrom',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'sender',\n        type: 'address',\n      },\n      {\n        name: 'recipient',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n  },\n] as const\n\n/**\n * [bytes32-flavored ERC-20](https://docs.makerdao.com/smart-contract-modules/mkr-module#4.-gotchas-potential-source-of-user-error)\n * for tokens (ie. Maker) that use bytes32 instead of string.\n */\nexport const erc20Abi_bytes32 = [\n  {\n    type: 'event',\n    name: 'Approval',\n    inputs: [\n      {\n        indexed: true,\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'value',\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'event',\n    name: 'Transfer',\n    inputs: [\n      {\n        indexed: true,\n        name: 'from',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'to',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'value',\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'allowance',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        name: 'spender',\n        type: 'address',\n      },\n    ],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'approve',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'balanceOf',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'account',\n        type: 'address',\n      },\n    ],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'decimals',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'uint8',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'name',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'bytes32',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'symbol',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'bytes32',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'totalSupply',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'transfer',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'recipient',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'transferFrom',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'sender',\n        type: 'address',\n      },\n      {\n        name: 'recipient',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n  },\n] as const\n\n/** [ERC-1155 Multi Token Standard](https://ethereum.org/en/developers/docs/standards/tokens/erc-1155) */\nexport const erc1155Abi = [\n  {\n    inputs: [\n      {\n        internalType: 'address',\n        name: 'sender',\n        type: 'address',\n      },\n      {\n        internalType: 'uint256',\n        name: 'balance',\n        type: 'uint256',\n      },\n      {\n        internalType: 'uint256',\n        name: 'needed',\n        type: 'uint256',\n      },\n      {\n        internalType: 'uint256',\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n    name: 'ERC1155InsufficientBalance',\n    type: 'error',\n  },\n  {\n    inputs: [\n      {\n        internalType: 'address',\n        name: 'approver',\n        type: 'address',\n      },\n    ],\n    name: 'ERC1155InvalidApprover',\n    type: 'error',\n  },\n  {\n    inputs: [\n      {\n        internalType: 'uint256',\n        name: 'idsLength',\n        type: 'uint256',\n      },\n      {\n        internalType: 'uint256',\n        name: 'valuesLength',\n        type: 'uint256',\n      },\n    ],\n    name: 'ERC1155InvalidArrayLength',\n    type: 'error',\n  },\n  {\n    inputs: [\n      {\n        internalType: 'address',\n        name: 'operator',\n        type: 'address',\n      },\n    ],\n    name: 'ERC1155InvalidOperator',\n    type: 'error',\n  },\n  {\n    inputs: [\n      {\n        internalType: 'address',\n        name: 'receiver',\n        type: 'address',\n      },\n    ],\n    name: 'ERC1155InvalidReceiver',\n    type: 'error',\n  },\n  {\n    inputs: [\n      {\n        internalType: 'address',\n        name: 'sender',\n        type: 'address',\n      },\n    ],\n    name: 'ERC1155InvalidSender',\n    type: 'error',\n  },\n  {\n    inputs: [\n      {\n        internalType: 'address',\n        name: 'operator',\n        type: 'address',\n      },\n      {\n        internalType: 'address',\n        name: 'owner',\n        type: 'address',\n      },\n    ],\n    name: 'ERC1155MissingApprovalForAll',\n    type: 'error',\n  },\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        internalType: 'address',\n        name: 'account',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        internalType: 'address',\n        name: 'operator',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        internalType: 'bool',\n        name: 'approved',\n        type: 'bool',\n      },\n    ],\n    name: 'ApprovalForAll',\n    type: 'event',\n  },\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        internalType: 'address',\n        name: 'operator',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        internalType: 'address',\n        name: 'from',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        internalType: 'address',\n        name: 'to',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        internalType: 'uint256[]',\n        name: 'ids',\n        type: 'uint256[]',\n      },\n      {\n        indexed: false,\n        internalType: 'uint256[]',\n        name: 'values',\n        type: 'uint256[]',\n      },\n    ],\n    name: 'TransferBatch',\n    type: 'event',\n  },\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        internalType: 'address',\n        name: 'operator',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        internalType: 'address',\n        name: 'from',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        internalType: 'address',\n        name: 'to',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        internalType: 'uint256',\n        name: 'id',\n        type: 'uint256',\n      },\n      {\n        indexed: false,\n        internalType: 'uint256',\n        name: 'value',\n        type: 'uint256',\n      },\n    ],\n    name: 'TransferSingle',\n    type: 'event',\n  },\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: false,\n        internalType: 'string',\n        name: 'value',\n        type: 'string',\n      },\n      {\n        indexed: true,\n        internalType: 'uint256',\n        name: 'id',\n        type: 'uint256',\n      },\n    ],\n    name: 'URI',\n    type: 'event',\n  },\n  {\n    inputs: [\n      {\n        internalType: 'address',\n        name: 'account',\n        type: 'address',\n      },\n      {\n        internalType: 'uint256',\n        name: 'id',\n        type: 'uint256',\n      },\n    ],\n    name: 'balanceOf',\n    outputs: [\n      {\n        internalType: 'uint256',\n        name: '',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        internalType: 'address[]',\n        name: 'accounts',\n        type: 'address[]',\n      },\n      {\n        internalType: 'uint256[]',\n        name: 'ids',\n        type: 'uint256[]',\n      },\n    ],\n    name: 'balanceOfBatch',\n    outputs: [\n      {\n        internalType: 'uint256[]',\n        name: '',\n        type: 'uint256[]',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        internalType: 'address',\n        name: 'account',\n        type: 'address',\n      },\n      {\n        internalType: 'address',\n        name: 'operator',\n        type: 'address',\n      },\n    ],\n    name: 'isApprovedForAll',\n    outputs: [\n      {\n        internalType: 'bool',\n        name: '',\n        type: 'bool',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        internalType: 'address',\n        name: 'from',\n        type: 'address',\n      },\n      {\n        internalType: 'address',\n        name: 'to',\n        type: 'address',\n      },\n      {\n        internalType: 'uint256[]',\n        name: 'ids',\n        type: 'uint256[]',\n      },\n      {\n        internalType: 'uint256[]',\n        name: 'values',\n        type: 'uint256[]',\n      },\n      {\n        internalType: 'bytes',\n        name: 'data',\n        type: 'bytes',\n      },\n    ],\n    name: 'safeBatchTransferFrom',\n    outputs: [],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        internalType: 'address',\n        name: 'from',\n        type: 'address',\n      },\n      {\n        internalType: 'address',\n        name: 'to',\n        type: 'address',\n      },\n      {\n        internalType: 'uint256',\n        name: 'id',\n        type: 'uint256',\n      },\n      {\n        internalType: 'uint256',\n        name: 'value',\n        type: 'uint256',\n      },\n      {\n        internalType: 'bytes',\n        name: 'data',\n        type: 'bytes',\n      },\n    ],\n    name: 'safeTransferFrom',\n    outputs: [],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        internalType: 'address',\n        name: 'operator',\n        type: 'address',\n      },\n      {\n        internalType: 'bool',\n        name: 'approved',\n        type: 'bool',\n      },\n    ],\n    name: 'setApprovalForAll',\n    outputs: [],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        internalType: 'bytes4',\n        name: 'interfaceId',\n        type: 'bytes4',\n      },\n    ],\n    name: 'supportsInterface',\n    outputs: [\n      {\n        internalType: 'bool',\n        name: '',\n        type: 'bool',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        internalType: 'uint256',\n        name: '',\n        type: 'uint256',\n      },\n    ],\n    name: 'uri',\n    outputs: [\n      {\n        internalType: 'string',\n        name: '',\n        type: 'string',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n] as const\n\n/** [ERC-721 Non-Fungible Token Standard](https://ethereum.org/en/developers/docs/standards/tokens/erc-721) */\nexport const erc721Abi = [\n  {\n    type: 'event',\n    name: 'Approval',\n    inputs: [\n      {\n        indexed: true,\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'event',\n    name: 'ApprovalForAll',\n    inputs: [\n      {\n        indexed: true,\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'operator',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'approved',\n        type: 'bool',\n      },\n    ],\n  },\n  {\n    type: 'event',\n    name: 'Transfer',\n    inputs: [\n      {\n        indexed: true,\n        name: 'from',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'to',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'approve',\n    stateMutability: 'payable',\n    inputs: [\n      {\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n    outputs: [],\n  },\n  {\n    type: 'function',\n    name: 'balanceOf',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'account',\n        type: 'address',\n      },\n    ],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'getApproved',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'address',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'isApprovedForAll',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        name: 'operator',\n        type: 'address',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'name',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'string',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'ownerOf',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'safeTransferFrom',\n    stateMutability: 'payable',\n    inputs: [\n      {\n        name: 'from',\n        type: 'address',\n      },\n      {\n        name: 'to',\n        type: 'address',\n      },\n      {\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n    outputs: [],\n  },\n  {\n    type: 'function',\n    name: 'safeTransferFrom',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'from',\n        type: 'address',\n      },\n      {\n        name: 'to',\n        type: 'address',\n      },\n      {\n        name: 'id',\n        type: 'uint256',\n      },\n      {\n        name: 'data',\n        type: 'bytes',\n      },\n    ],\n    outputs: [],\n  },\n  {\n    type: 'function',\n    name: 'setApprovalForAll',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'operator',\n        type: 'address',\n      },\n      {\n        name: 'approved',\n        type: 'bool',\n      },\n    ],\n    outputs: [],\n  },\n  {\n    type: 'function',\n    name: 'symbol',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'string',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'tokenByIndex',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'index',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'tokenByIndex',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        name: 'index',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'tokenURI',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'string',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'totalSupply',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'transferFrom',\n    stateMutability: 'payable',\n    inputs: [\n      {\n        name: 'sender',\n        type: 'address',\n      },\n      {\n        name: 'recipient',\n        type: 'address',\n      },\n      {\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n    outputs: [],\n  },\n] as const\n\n/** [ERC-4626 Tokenized Vaults Standard](https://ethereum.org/en/developers/docs/standards/tokens/erc-4626) */\nexport const erc4626Abi = [\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'value',\n        type: 'uint256',\n      },\n    ],\n    name: 'Approval',\n    type: 'event',\n  },\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        name: 'sender',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'receiver',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'assets',\n        type: 'uint256',\n      },\n      {\n        indexed: false,\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    name: 'Deposit',\n    type: 'event',\n  },\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        name: 'from',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'to',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'value',\n        type: 'uint256',\n      },\n    ],\n    name: 'Transfer',\n    type: 'event',\n  },\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        name: 'sender',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'receiver',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'assets',\n        type: 'uint256',\n      },\n      {\n        indexed: false,\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    name: 'Withdraw',\n    type: 'event',\n  },\n  {\n    inputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        name: 'spender',\n        type: 'address',\n      },\n    ],\n    name: 'allowance',\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    name: 'approve',\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [],\n    name: 'asset',\n    outputs: [\n      {\n        name: 'assetTokenAddress',\n        type: 'address',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'account',\n        type: 'address',\n      },\n    ],\n    name: 'balanceOf',\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    name: 'convertToAssets',\n    outputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    name: 'convertToShares',\n    outputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n      {\n        name: 'receiver',\n        type: 'address',\n      },\n    ],\n    name: 'deposit',\n    outputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'caller',\n        type: 'address',\n      },\n    ],\n    name: 'maxDeposit',\n    outputs: [\n      {\n        name: 'maxAssets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'caller',\n        type: 'address',\n      },\n    ],\n    name: 'maxMint',\n    outputs: [\n      {\n        name: 'maxShares',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n    ],\n    name: 'maxRedeem',\n    outputs: [\n      {\n        name: 'maxShares',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n    ],\n    name: 'maxWithdraw',\n    outputs: [\n      {\n        name: 'maxAssets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n      {\n        name: 'receiver',\n        type: 'address',\n      },\n    ],\n    name: 'mint',\n    outputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    name: 'previewDeposit',\n    outputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    name: 'previewMint',\n    outputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    name: 'previewRedeem',\n    outputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    name: 'previewWithdraw',\n    outputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n      {\n        name: 'receiver',\n        type: 'address',\n      },\n      {\n        name: 'owner',\n        type: 'address',\n      },\n    ],\n    name: 'redeem',\n    outputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [],\n    name: 'totalAssets',\n    outputs: [\n      {\n        name: 'totalManagedAssets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [],\n    name: 'totalSupply',\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'to',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    name: 'transfer',\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'from',\n        type: 'address',\n      },\n      {\n        name: 'to',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    name: 'transferFrom',\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n      {\n        name: 'receiver',\n        type: 'address',\n      },\n      {\n        name: 'owner',\n        type: 'address',\n      },\n    ],\n    name: 'withdraw',\n    outputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n] as const\n", "export const aggregate3Signature = '0x82ad56cb'\n", "export const deploylessCallViaBytecodeBytecode =\n  '0x608060405234801561001057600080fd5b5060405161018e38038061018e83398101604081905261002f91610124565b6000808351602085016000f59050803b61004857600080fd5b6000808351602085016000855af16040513d6000823e81610067573d81fd5b3d81f35b634e487b7160e01b600052604160045260246000fd5b600082601f83011261009257600080fd5b81516001600160401b038111156100ab576100ab61006b565b604051601f8201601f19908116603f011681016001600160401b03811182821017156100d9576100d961006b565b6040528181528382016020018510156100f157600080fd5b60005b82811015610110576020818601810151838301820152016100f4565b506000918101602001919091529392505050565b6000806040838503121561013757600080fd5b82516001600160401b0381111561014d57600080fd5b61015985828601610081565b602085015190935090506001600160401b0381111561017757600080fd5b61018385828601610081565b915050925092905056fe'\n\nexport const deploylessCallViaFactoryBytecode =\n  '0x608060405234801561001057600080fd5b506040516102c03803806102c083398101604081905261002f916101e6565b836001600160a01b03163b6000036100e457600080836001600160a01b03168360405161005c9190610270565b6000604051808303816000865af19150503d8060008114610099576040519150601f19603f3d011682016040523d82523d6000602084013e61009e565b606091505b50915091508115806100b857506001600160a01b0386163b155b156100e1578060405163101bb98d60e01b81526004016100d8919061028c565b60405180910390fd5b50505b6000808451602086016000885af16040513d6000823e81610103573d81fd5b3d81f35b80516001600160a01b038116811461011e57600080fd5b919050565b634e487b7160e01b600052604160045260246000fd5b60005b8381101561015457818101518382015260200161013c565b50506000910152565b600082601f83011261016e57600080fd5b81516001600160401b0381111561018757610187610123565b604051601f8201601f19908116603f011681016001600160401b03811182821017156101b5576101b5610123565b6040528181528382016020018510156101cd57600080fd5b6101de826020830160208701610139565b949350505050565b600080600080608085870312156101fc57600080fd5b61020585610107565b60208601519094506001600160401b0381111561022157600080fd5b61022d8782880161015d565b93505061023c60408601610107565b60608601519092506001600160401b0381111561025857600080fd5b6102648782880161015d565b91505092959194509250565b60008251610282818460208701610139565b9190910192915050565b60208152600082518060208401526102ab816040850160208701610139565b601f01601f1916919091016040019291505056fe'\n\nexport const universalSignatureValidatorByteCode =\n  '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'\n", "import type { Abi, AbiStateMutability, ExtractAbiFunctions } from 'abitype'\n\nimport {\n  AbiFunctionNotFoundError,\n  type AbiFunctionNotFoundErrorType,\n  AbiFunctionOutputsNotFoundError,\n  type AbiFunctionOutputsNotFoundErrorType,\n} from '../../errors/abi.js'\nimport type {\n  ContractFunctionArgs,\n  ContractFunctionName,\n  ContractFunctionReturnType,\n  Widen,\n} from '../../types/contract.js'\nimport type { Hex } from '../../types/misc.js'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { IsNarrowable, UnionEvaluate } from '../../types/utils.js'\nimport {\n  type DecodeAbiParametersErrorType,\n  decodeAbiParameters,\n} from './decodeAbiParameters.js'\nimport { type GetAbiItemErrorType, getAbiItem } from './getAbiItem.js'\n\nconst docsPath = '/docs/contract/decodeFunctionResult'\n\nexport type DecodeFunctionResultParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends\n    | ContractFunctionName<abi>\n    | undefined = ContractFunctionName<abi>,\n  args extends ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  > = ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  >,\n  ///\n  hasFunctions = abi extends Abi\n    ? Abi extends abi\n      ? true\n      : [ExtractAbiFunctions<abi>] extends [never]\n        ? false\n        : true\n    : true,\n  allArgs = ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  >,\n  allFunctionNames = ContractFunctionName<abi>,\n> = {\n  abi: abi\n  data: Hex\n} & UnionEvaluate<\n  IsNarrowable<abi, Abi> extends true\n    ? abi['length'] extends 1\n      ? { functionName?: functionName | allFunctionNames | undefined }\n      : { functionName: functionName | allFunctionNames }\n    : { functionName?: functionName | allFunctionNames | undefined }\n> &\n  UnionEvaluate<\n    readonly [] extends allArgs\n      ? {\n          args?:\n            | allArgs // show all options\n            // infer value, widen inferred value of `args` conditionally to match `allArgs`\n            | (abi extends Abi\n                ? args extends allArgs\n                  ? Widen<args>\n                  : never\n                : never)\n            | undefined\n        }\n      : {\n          args?:\n            | allArgs // show all options\n            | (Widen<args> & (args extends allArgs ? unknown : never)) // infer value, widen inferred value of `args` match `allArgs` (e.g. avoid union `args: readonly [123n] | readonly [bigint]`)\n            | undefined\n        }\n  > &\n  (hasFunctions extends true ? unknown : never)\n\nexport type DecodeFunctionResultReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends\n    | ContractFunctionName<abi>\n    | undefined = ContractFunctionName<abi>,\n  args extends ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  > = ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  >,\n> = ContractFunctionReturnType<\n  abi,\n  AbiStateMutability,\n  functionName extends ContractFunctionName<abi>\n    ? functionName\n    : ContractFunctionName<abi>,\n  args\n>\n\nexport type DecodeFunctionResultErrorType =\n  | AbiFunctionNotFoundErrorType\n  | AbiFunctionOutputsNotFoundErrorType\n  | DecodeAbiParametersErrorType\n  | GetAbiItemErrorType\n  | ErrorType\n\nexport function decodeFunctionResult<\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi> | undefined = undefined,\n  const args extends ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  > = ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  >,\n>(\n  parameters: DecodeFunctionResultParameters<abi, functionName, args>,\n): DecodeFunctionResultReturnType<abi, functionName, args> {\n  const { abi, args, functionName, data } =\n    parameters as DecodeFunctionResultParameters\n\n  let abiItem = abi[0]\n  if (functionName) {\n    const item = getAbiItem({ abi, args, name: functionName })\n    if (!item) throw new AbiFunctionNotFoundError(functionName, { docsPath })\n    abiItem = item\n  }\n\n  if (abiItem.type !== 'function')\n    throw new AbiFunctionNotFoundError(undefined, { docsPath })\n  if (!abiItem.outputs)\n    throw new AbiFunctionOutputsNotFoundError(abiItem.name, { docsPath })\n\n  const values = decodeAbiParameters(abiItem.outputs, data)\n  if (values && values.length > 1)\n    return values as DecodeFunctionResultReturnType<abi, functionName, args>\n  if (values && values.length === 1)\n    return values[0] as DecodeFunctionResultReturnType<abi, functionName, args>\n  return undefined as DecodeFunctionResultReturnType<abi, functionName, args>\n}\n", "import type { Abi } from 'abitype'\n\nimport {\n  AbiConstructorNotFoundError,\n  type AbiConstructorNotFoundErrorType,\n  AbiConstructorParamsNotFoundError,\n} from '../../errors/abi.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ContractConstructorArgs } from '../../types/contract.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { UnionEvaluate } from '../../types/utils.js'\nimport { type ConcatHexErrorType, concatHex } from '../data/concat.js'\nimport {\n  type EncodeAbiParametersErrorType,\n  encodeAbiParameters,\n} from './encodeAbiParameters.js'\n\nconst docsPath = '/docs/contract/encodeDeployData'\n\nexport type EncodeDeployDataParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  ///\n  hasConstructor = abi extends Abi\n    ? Abi extends abi\n      ? true\n      : [Extract<abi[number], { type: 'constructor' }>] extends [never]\n        ? false\n        : true\n    : true,\n  allArgs = ContractConstructorArgs<abi>,\n> = {\n  abi: abi\n  bytecode: Hex\n} & UnionEvaluate<\n  hasConstructor extends false\n    ? { args?: undefined }\n    : readonly [] extends allArgs\n      ? { args?: allArgs | undefined }\n      : { args: allArgs }\n>\n\nexport type EncodeDeployDataReturnType = Hex\n\nexport type EncodeDeployDataErrorType =\n  | AbiConstructorNotFoundErrorType\n  | ConcatHexErrorType\n  | EncodeAbiParametersErrorType\n  | ErrorType\n\nexport function encodeDeployData<const abi extends Abi | readonly unknown[]>(\n  parameters: EncodeDeployDataParameters<abi>,\n): EncodeDeployDataReturnType {\n  const { abi, args, bytecode } = parameters as EncodeDeployDataParameters\n  if (!args || args.length === 0) return bytecode\n\n  const description = abi.find((x) => 'type' in x && x.type === 'constructor')\n  if (!description) throw new AbiConstructorNotFoundError({ docsPath })\n  if (!('inputs' in description))\n    throw new AbiConstructorParamsNotFoundError({ docsPath })\n  if (!description.inputs || description.inputs.length === 0)\n    throw new AbiConstructorParamsNotFoundError({ docsPath })\n\n  const data = encodeAbiParameters(description.inputs, args)\n  return concatHex([bytecode, data!])\n}\n", "import type {\n  Abi,\n  AbiStateMutability,\n  ExtractAbiFunction,\n  ExtractAbiFunctions,\n} from 'abitype'\n\nimport {\n  AbiFunctionNotFoundError,\n  type AbiFunctionNotFoundErrorType,\n} from '../../errors/abi.js'\nimport type {\n  ContractFunctionArgs,\n  ContractFunctionName,\n} from '../../types/contract.js'\nimport type { ConcatHexErrorType } from '../data/concat.js'\nimport {\n  type ToFunctionSelectorErrorType,\n  toFunctionSelector,\n} from '../hash/toFunctionSelector.js'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { IsNarrowable, UnionEvaluate } from '../../types/utils.js'\nimport { type FormatAbiItemErrorType, formatAbiItem } from './formatAbiItem.js'\nimport { type GetAbiItemErrorType, getAbiItem } from './getAbiItem.js'\n\nconst docsPath = '/docs/contract/encodeFunctionData'\n\nexport type PrepareEncodeFunctionDataParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends\n    | ContractFunctionName<abi>\n    | undefined = ContractFunctionName<abi>,\n  ///\n  hasFunctions = abi extends Abi\n    ? Abi extends abi\n      ? true\n      : [ExtractAbiFunctions<abi>] extends [never]\n        ? false\n        : true\n    : true,\n  allArgs = ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  >,\n  allFunctionNames = ContractFunctionName<abi>,\n> = {\n  abi: abi\n} & UnionEvaluate<\n  IsNarrowable<abi, Abi> extends true\n    ? abi['length'] extends 1\n      ? { functionName?: functionName | allFunctionNames | Hex | undefined }\n      : { functionName: functionName | allFunctionNames | Hex }\n    : { functionName?: functionName | allFunctionNames | Hex | undefined }\n> &\n  UnionEvaluate<{ args?: allArgs | undefined }> &\n  (hasFunctions extends true ? unknown : never)\n\nexport type PrepareEncodeFunctionDataReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends\n    | ContractFunctionName<abi>\n    | undefined = ContractFunctionName<abi>,\n> = {\n  abi: abi extends Abi\n    ? functionName extends ContractFunctionName<abi>\n      ? [ExtractAbiFunction<abi, functionName>]\n      : abi\n    : Abi\n  functionName: Hex\n}\n\nexport type PrepareEncodeFunctionDataErrorType =\n  | AbiFunctionNotFoundErrorType\n  | ConcatHexErrorType\n  | FormatAbiItemErrorType\n  | GetAbiItemErrorType\n  | ToFunctionSelectorErrorType\n  | ErrorType\n\nexport function prepareEncodeFunctionData<\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi> | undefined = undefined,\n>(\n  parameters: PrepareEncodeFunctionDataParameters<abi, functionName>,\n): PrepareEncodeFunctionDataReturnType<abi, functionName> {\n  const { abi, args, functionName } =\n    parameters as PrepareEncodeFunctionDataParameters\n\n  let abiItem = abi[0]\n  if (functionName) {\n    const item = getAbiItem({\n      abi,\n      args,\n      name: functionName,\n    })\n    if (!item) throw new AbiFunctionNotFoundError(functionName, { docsPath })\n    abiItem = item\n  }\n\n  if (abiItem.type !== 'function')\n    throw new AbiFunctionNotFoundError(undefined, { docsPath })\n\n  return {\n    abi: [abiItem],\n    functionName: toFunctionSelector(formatAbiItem(abiItem)),\n  } as unknown as PrepareEncodeFunctionDataReturnType<abi, functionName>\n}\n", "import type { Abi, AbiStateMutability, ExtractAbiFunctions } from 'abitype'\n\nimport type { AbiFunctionNotFoundErrorType } from '../../errors/abi.js'\nimport type {\n  ContractFunctionArgs,\n  ContractFunctionName,\n} from '../../types/contract.js'\nimport { type ConcatHexErrorType, concatHex } from '../data/concat.js'\nimport type { ToFunctionSelectorErrorType } from '../hash/toFunctionSelector.js'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { IsNarrowable, UnionEvaluate } from '../../types/utils.js'\nimport {\n  type EncodeAbiParametersErrorType,\n  encodeAbiParameters,\n} from './encodeAbiParameters.js'\nimport type { FormatAbiItemErrorType } from './formatAbiItem.js'\nimport type { GetAbiItemErrorType } from './getAbiItem.js'\nimport { prepareEncodeFunctionData } from './prepareEncodeFunctionData.js'\n\nexport type EncodeFunctionDataParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends\n    | ContractFunctionName<abi>\n    | Hex\n    | undefined = ContractFunctionName<abi>,\n  ///\n  hasFunctions = abi extends Abi\n    ? Abi extends abi\n      ? true\n      : [ExtractAbiFunctions<abi>] extends [never]\n        ? false\n        : true\n    : true,\n  allArgs = ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  >,\n  allFunctionNames = ContractFunctionName<abi>,\n> = {\n  abi: abi\n} & UnionEvaluate<\n  IsNarrowable<abi, Abi> extends true\n    ? abi['length'] extends 1\n      ? { functionName?: functionName | allFunctionNames | Hex | undefined }\n      : { functionName: functionName | allFunctionNames | Hex }\n    : { functionName?: functionName | allFunctionNames | Hex | undefined }\n> &\n  UnionEvaluate<\n    readonly [] extends allArgs\n      ? { args?: allArgs | undefined }\n      : { args: allArgs }\n  > &\n  (hasFunctions extends true ? unknown : never)\n\nexport type EncodeFunctionDataReturnType = Hex\n\nexport type EncodeFunctionDataErrorType =\n  | AbiFunctionNotFoundErrorType\n  | ConcatHexErrorType\n  | EncodeAbiParametersErrorType\n  | FormatAbiItemErrorType\n  | GetAbiItemErrorType\n  | ToFunctionSelectorErrorType\n  | ErrorType\n\nexport function encodeFunctionData<\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi> | undefined = undefined,\n>(\n  parameters: EncodeFunctionDataParameters<abi, functionName>,\n): EncodeFunctionDataReturnType {\n  const { args } = parameters as EncodeFunctionDataParameters\n\n  const { abi, functionName } = (() => {\n    if (\n      parameters.abi.length === 1 &&\n      parameters.functionName?.startsWith('0x')\n    )\n      return parameters as { abi: Abi; functionName: Hex }\n    return prepareEncodeFunctionData(parameters)\n  })()\n\n  const abiItem = abi[0]\n  const signature = functionName\n\n  const data =\n    'inputs' in abiItem && abiItem.inputs\n      ? encodeAbiParameters(abiItem.inputs, args ?? [])\n      : undefined\n  return concatHex([signature, data ?? '0x'])\n}\n", "/** @internal */\nexport type PromiseWithResolvers<type> = {\n  promise: Promise<type>\n  resolve: (value: type | PromiseLike<type>) => void\n  reject: (reason?: unknown) => void\n}\n\n/** @internal */\nexport function withResolvers<type>(): PromiseWithResolvers<type> {\n  let resolve: PromiseWithResolvers<type>['resolve'] = () => undefined\n  let reject: PromiseWithResolvers<type>['reject'] = () => undefined\n\n  const promise = new Promise<type>((resolve_, reject_) => {\n    resolve = resolve_\n    reject = reject_\n  })\n\n  return { promise, resolve, reject }\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport { type PromiseWithResolvers, withResol<PERSON> } from './withResolvers.js'\n\ntype Resolved<returnType extends readonly unknown[] = any> = [\n  result: returnType[number],\n  results: returnType,\n]\n\ntype SchedulerItem = {\n  args: unknown\n  resolve: PromiseWithResolvers<unknown>['resolve']\n  reject: PromiseWithResolvers<unknown>['reject']\n}\n\ntype BatchResultsCompareFn<result = unknown> = (a: result, b: result) => number\n\ntype CreateBatchSchedulerArguments<\n  parameters = unknown,\n  returnType extends readonly unknown[] = readonly unknown[],\n> = {\n  fn: (args: parameters[]) => Promise<returnType>\n  id: number | string\n  shouldSplitBatch?: ((args: parameters[]) => boolean) | undefined\n  wait?: number | undefined\n  sort?: BatchResultsCompareFn<returnType[number]> | undefined\n}\n\ntype CreateBatchSchedulerReturnType<\n  parameters = unknown,\n  returnType extends readonly unknown[] = readonly unknown[],\n> = {\n  flush: () => void\n  schedule: parameters extends undefined\n    ? (args?: parameters | undefined) => Promise<Resolved<returnType>>\n    : (args: parameters) => Promise<Resolved<returnType>>\n}\n\nexport type CreateBatchSchedulerErrorType = ErrorType\n\nconst schedulerCache = /*#__PURE__*/ new Map<number | string, SchedulerItem[]>()\n\n/** @internal */\nexport function createBatchScheduler<\n  parameters,\n  returnType extends readonly unknown[],\n>({\n  fn,\n  id,\n  shouldSplitBatch,\n  wait = 0,\n  sort,\n}: CreateBatchSchedulerArguments<\n  parameters,\n  returnType\n>): CreateBatchSchedulerReturnType<parameters, returnType> {\n  const exec = async () => {\n    const scheduler = getScheduler()\n    flush()\n\n    const args = scheduler.map(({ args }) => args)\n\n    if (args.length === 0) return\n\n    fn(args as parameters[])\n      .then((data) => {\n        if (sort && Array.isArray(data)) data.sort(sort)\n        for (let i = 0; i < scheduler.length; i++) {\n          const { resolve } = scheduler[i]\n          resolve?.([data[i], data])\n        }\n      })\n      .catch((err) => {\n        for (let i = 0; i < scheduler.length; i++) {\n          const { reject } = scheduler[i]\n          reject?.(err)\n        }\n      })\n  }\n\n  const flush = () => schedulerCache.delete(id)\n\n  const getBatchedArgs = () =>\n    getScheduler().map(({ args }) => args) as parameters[]\n\n  const getScheduler = () => schedulerCache.get(id) || []\n\n  const setScheduler = (item: SchedulerItem) =>\n    schedulerCache.set(id, [...getScheduler(), item])\n\n  return {\n    flush,\n    async schedule(args: parameters) {\n      const { promise, resolve, reject } = withResolvers()\n\n      const split = shouldSplitBatch?.([...getBatchedArgs(), args])\n\n      if (split) exec()\n\n      const hasActiveScheduler = getScheduler().length > 0\n      if (hasActiveScheduler) {\n        setScheduler({ args, resolve, reject })\n        return promise\n      }\n\n      setScheduler({ args, resolve, reject })\n      setTimeout(exec, wait)\n      return promise\n    },\n  } as unknown as CreateBatchSchedulerReturnType<parameters, returnType>\n}\n", "import {\n  InvalidAddressError,\n  type InvalidAddressErrorType,\n} from '../errors/address.js'\nimport {\n  InvalidBytesLengthError,\n  type InvalidBytesLengthErrorType,\n} from '../errors/data.js'\nimport {\n  AccountStateConflictError,\n  type AccountStateConflictErrorType,\n  StateAssignmentConflictError,\n  type StateAssignmentConflictErrorType,\n} from '../errors/stateOverride.js'\nimport type {\n  RpcAccountStateOverride,\n  RpcStateMapping,\n  RpcStateOverride,\n} from '../types/rpc.js'\nimport type { StateMapping, StateOverride } from '../types/stateOverride.js'\nimport { isAddress } from './address/isAddress.js'\nimport { type NumberToHexErrorType, numberToHex } from './encoding/toHex.js'\n\ntype SerializeStateMappingParameters = StateMapping | undefined\n\ntype SerializeStateMappingErrorType = InvalidBytesLengthErrorType\n\n/** @internal */\nexport function serializeStateMapping(\n  stateMapping: SerializeStateMappingParameters,\n): RpcStateMapping | undefined {\n  if (!stateMapping || stateMapping.length === 0) return undefined\n  return stateMapping.reduce((acc, { slot, value }) => {\n    if (slot.length !== 66)\n      throw new InvalidBytesLengthError({\n        size: slot.length,\n        targetSize: 66,\n        type: 'hex',\n      })\n    if (value.length !== 66)\n      throw new InvalidBytesLengthError({\n        size: value.length,\n        targetSize: 66,\n        type: 'hex',\n      })\n    acc[slot] = value\n    return acc\n  }, {} as RpcStateMapping)\n}\n\ntype SerializeAccountStateOverrideParameters = Omit<\n  StateOverride[number],\n  'address'\n>\n\ntype SerializeAccountStateOverrideErrorType =\n  | NumberToHexErrorType\n  | StateAssignmentConflictErrorType\n  | SerializeStateMappingErrorType\n\n/** @internal */\nexport function serializeAccountStateOverride(\n  parameters: SerializeAccountStateOverrideParameters,\n): RpcAccountStateOverride {\n  const { balance, nonce, state, stateDiff, code } = parameters\n  const rpcAccountStateOverride: RpcAccountStateOverride = {}\n  if (code !== undefined) rpcAccountStateOverride.code = code\n  if (balance !== undefined)\n    rpcAccountStateOverride.balance = numberToHex(balance)\n  if (nonce !== undefined) rpcAccountStateOverride.nonce = numberToHex(nonce)\n  if (state !== undefined)\n    rpcAccountStateOverride.state = serializeStateMapping(state)\n  if (stateDiff !== undefined) {\n    if (rpcAccountStateOverride.state) throw new StateAssignmentConflictError()\n    rpcAccountStateOverride.stateDiff = serializeStateMapping(stateDiff)\n  }\n  return rpcAccountStateOverride\n}\n\ntype SerializeStateOverrideParameters = StateOverride | undefined\n\nexport type SerializeStateOverrideErrorType =\n  | InvalidAddressErrorType\n  | AccountStateConflictErrorType\n  | SerializeAccountStateOverrideErrorType\n\n/** @internal */\nexport function serializeStateOverride(\n  parameters?: SerializeStateOverrideParameters,\n): RpcStateOverride | undefined {\n  if (!parameters) return undefined\n  const rpcStateOverride: RpcStateOverride = {}\n  for (const { address, ...accountState } of parameters) {\n    if (!isAddress(address, { strict: false }))\n      throw new InvalidAddressError({ address })\n    if (rpcStateOverride[address])\n      throw new AccountStateConflictError({ address: address })\n    rpcStateOverride[address] = serializeAccountStateOverride(accountState)\n  }\n  return rpcStateOverride\n}\n", "import { type Address, parseAbi } from 'abitype'\nimport * as BlockOverrides from 'ox/BlockOverrides'\n\nimport type { Account } from '../../accounts/types.js'\nimport {\n  type ParseAccountErrorType,\n  parseAccount,\n} from '../../accounts/utils/parseAccount.js'\nimport type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport { multicall3Abi } from '../../constants/abis.js'\nimport { aggregate3Signature } from '../../constants/contract.js'\nimport {\n  deploylessCallViaBytecodeBytecode,\n  deploylessCallViaFactoryBytecode,\n} from '../../constants/contracts.js'\nimport { BaseError } from '../../errors/base.js'\nimport {\n  ChainDoesNotSupportContract,\n  ClientChainNotConfiguredError,\n} from '../../errors/chain.js'\nimport {\n  CounterfactualDeploymentFailedError,\n  RawContractError,\n  type RawContractErrorType,\n} from '../../errors/contract.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { BlockTag } from '../../types/block.js'\nimport type { Chain } from '../../types/chain.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { RpcTransactionRequest } from '../../types/rpc.js'\nimport type { StateOverride } from '../../types/stateOverride.js'\nimport type { TransactionRequest } from '../../types/transaction.js'\nimport type { ExactPartial, UnionOmit } from '../../types/utils.js'\nimport {\n  type DecodeFunctionResultErrorType,\n  decodeFunctionResult,\n} from '../../utils/abi/decodeFunctionResult.js'\nimport {\n  type EncodeDeployDataErrorType,\n  encodeDeployData,\n} from '../../utils/abi/encodeDeployData.js'\nimport {\n  type EncodeFunctionDataErrorType,\n  encodeFunctionData,\n} from '../../utils/abi/encodeFunctionData.js'\nimport type { RequestErrorType } from '../../utils/buildRequest.js'\nimport {\n  type GetChainContractAddressErrorType,\n  getChainContractAddress,\n} from '../../utils/chain/getChainContractAddress.js'\nimport {\n  type NumberToHexErrorType,\n  numberToHex,\n} from '../../utils/encoding/toHex.js'\nimport {\n  type GetCallErrorReturnType,\n  getCallError,\n} from '../../utils/errors/getCallError.js'\nimport { extract } from '../../utils/formatters/extract.js'\nimport {\n  type FormatTransactionRequestErrorType,\n  type FormattedTransactionRequest,\n  formatTransactionRequest,\n} from '../../utils/formatters/transactionRequest.js'\nimport {\n  type CreateBatchSchedulerErrorType,\n  createBatchScheduler,\n} from '../../utils/promise/createBatchScheduler.js'\nimport {\n  type SerializeStateOverrideErrorType,\n  serializeStateOverride,\n} from '../../utils/stateOverride.js'\nimport { assertRequest } from '../../utils/transaction/assertRequest.js'\nimport type {\n  AssertRequestErrorType,\n  AssertRequestParameters,\n} from '../../utils/transaction/assertRequest.js'\n\nexport type CallParameters<\n  chain extends Chain | undefined = Chain | undefined,\n> = UnionOmit<FormattedCall<chain>, 'from'> & {\n  /** Account attached to the call (msg.sender). */\n  account?: Account | Address | undefined\n  /** Whether or not to enable multicall batching on this call. */\n  batch?: boolean | undefined\n  /** Block overrides for the call. */\n  blockOverrides?: BlockOverrides.BlockOverrides | undefined\n  /** Bytecode to perform the call on. */\n  code?: Hex | undefined\n  /** Contract deployment factory address (ie. Create2 factory, Smart Account factory, etc). */\n  factory?: Address | undefined\n  /** Calldata to execute on the factory to deploy the contract. */\n  factoryData?: Hex | undefined\n  /** State overrides for the call. */\n  stateOverride?: StateOverride | undefined\n} & (\n    | {\n        /** The balance of the account at a block number. */\n        blockNumber?: bigint | undefined\n        blockTag?: undefined\n      }\n    | {\n        blockNumber?: undefined\n        /**\n         * The balance of the account at a block tag.\n         * @default 'latest'\n         */\n        blockTag?: BlockTag | undefined\n      }\n  )\ntype FormattedCall<chain extends Chain | undefined = Chain | undefined> =\n  FormattedTransactionRequest<chain>\n\nexport type CallReturnType = { data: Hex | undefined }\n\nexport type CallErrorType = GetCallErrorReturnType<\n  | ParseAccountErrorType\n  | SerializeStateOverrideErrorType\n  | AssertRequestErrorType\n  | NumberToHexErrorType\n  | FormatTransactionRequestErrorType\n  | ScheduleMulticallErrorType\n  | RequestErrorType\n  | ToDeploylessCallViaBytecodeDataErrorType\n  | ToDeploylessCallViaFactoryDataErrorType\n>\n\n/**\n * Executes a new message call immediately without submitting a transaction to the network.\n *\n * - Docs: https://viem.sh/docs/actions/public/call\n * - JSON-RPC Methods: [`eth_call`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_call)\n *\n * @param client - Client to use\n * @param parameters - {@link CallParameters}\n * @returns The call data. {@link CallReturnType}\n *\n * @example\n * import { createPublicClient, http } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { call } from 'viem/public'\n *\n * const client = createPublicClient({\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const data = await call(client, {\n *   account: '******************************************',\n *   data: '******************************************',\n *   to: '******************************************',\n * })\n */\nexport async function call<chain extends Chain | undefined>(\n  client: Client<Transport, chain>,\n  args: CallParameters<chain>,\n): Promise<CallReturnType> {\n  const {\n    account: account_ = client.account,\n    authorizationList,\n    batch = Boolean(client.batch?.multicall),\n    blockNumber,\n    blockTag = 'latest',\n    accessList,\n    blobs,\n    blockOverrides,\n    code,\n    data: data_,\n    factory,\n    factoryData,\n    gas,\n    gasPrice,\n    maxFeePerBlobGas,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    nonce,\n    to,\n    value,\n    stateOverride,\n    ...rest\n  } = args\n  const account = account_ ? parseAccount(account_) : undefined\n\n  if (code && (factory || factoryData))\n    throw new BaseError(\n      'Cannot provide both `code` & `factory`/`factoryData` as parameters.',\n    )\n  if (code && to)\n    throw new BaseError('Cannot provide both `code` & `to` as parameters.')\n\n  // Check if the call is deployless via bytecode.\n  const deploylessCallViaBytecode = code && data_\n  // Check if the call is deployless via a factory.\n  const deploylessCallViaFactory = factory && factoryData && to && data_\n  const deploylessCall = deploylessCallViaBytecode || deploylessCallViaFactory\n\n  const data = (() => {\n    if (deploylessCallViaBytecode)\n      return toDeploylessCallViaBytecodeData({\n        code,\n        data: data_,\n      })\n    if (deploylessCallViaFactory)\n      return toDeploylessCallViaFactoryData({\n        data: data_,\n        factory,\n        factoryData,\n        to,\n      })\n    return data_\n  })()\n\n  try {\n    assertRequest(args as AssertRequestParameters)\n\n    const blockNumberHex =\n      typeof blockNumber === 'bigint' ? numberToHex(blockNumber) : undefined\n    const block = blockNumberHex || blockTag\n\n    const rpcBlockOverrides = blockOverrides\n      ? BlockOverrides.toRpc(blockOverrides)\n      : undefined\n    const rpcStateOverride = serializeStateOverride(stateOverride)\n\n    const chainFormat = client.chain?.formatters?.transactionRequest?.format\n    const format = chainFormat || formatTransactionRequest\n\n    const request = format({\n      // Pick out extra data that might exist on the chain's transaction request type.\n      ...extract(rest, { format: chainFormat }),\n      from: account?.address,\n      accessList,\n      authorizationList,\n      blobs,\n      data,\n      gas,\n      gasPrice,\n      maxFeePerBlobGas,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      to: deploylessCall ? undefined : to,\n      value,\n    } as TransactionRequest) as TransactionRequest\n\n    if (\n      batch &&\n      shouldPerformMulticall({ request }) &&\n      !rpcStateOverride &&\n      !rpcBlockOverrides\n    ) {\n      try {\n        return await scheduleMulticall(client, {\n          ...request,\n          blockNumber,\n          blockTag,\n        } as unknown as ScheduleMulticallParameters<chain>)\n      } catch (err) {\n        if (\n          !(err instanceof ClientChainNotConfiguredError) &&\n          !(err instanceof ChainDoesNotSupportContract)\n        )\n          throw err\n      }\n    }\n\n    const params = (() => {\n      const base = [\n        request as ExactPartial<RpcTransactionRequest>,\n        block,\n      ] as const\n      if (rpcStateOverride && rpcBlockOverrides)\n        return [...base, rpcStateOverride, rpcBlockOverrides] as const\n      if (rpcStateOverride) return [...base, rpcStateOverride] as const\n      if (rpcBlockOverrides) return [...base, {}, rpcBlockOverrides] as const\n      return base\n    })()\n\n    const response = await client.request({\n      method: 'eth_call',\n      params,\n    })\n    if (response === '0x') return { data: undefined }\n    return { data: response }\n  } catch (err) {\n    const data = getRevertErrorData(err)\n\n    // Check for CCIP-Read offchain lookup signature.\n    const { offchainLookup, offchainLookupSignature } = await import(\n      '../../utils/ccip.js'\n    )\n    if (\n      client.ccipRead !== false &&\n      data?.slice(0, 10) === offchainLookupSignature &&\n      to\n    )\n      return { data: await offchainLookup(client, { data, to }) }\n\n    // Check for counterfactual deployment error.\n    if (deploylessCall && data?.slice(0, 10) === '0x101bb98d')\n      throw new CounterfactualDeploymentFailedError({ factory })\n\n    throw getCallError(err as ErrorType, {\n      ...args,\n      account,\n      chain: client.chain,\n    })\n  }\n}\n\n// We only want to perform a scheduled multicall if:\n// - The request has calldata,\n// - The request has a target address,\n// - The target address is not already the aggregate3 signature,\n// - The request has no other properties (`nonce`, `gas`, etc cannot be sent with a multicall).\nfunction shouldPerformMulticall({ request }: { request: TransactionRequest }) {\n  const { data, to, ...request_ } = request\n  if (!data) return false\n  if (data.startsWith(aggregate3Signature)) return false\n  if (!to) return false\n  if (\n    Object.values(request_).filter((x) => typeof x !== 'undefined').length > 0\n  )\n    return false\n  return true\n}\n\ntype ScheduleMulticallParameters<chain extends Chain | undefined> = Pick<\n  CallParameters<chain>,\n  'blockNumber' | 'blockTag'\n> & {\n  data: Hex\n  multicallAddress?: Address | undefined\n  to: Address\n}\n\ntype ScheduleMulticallErrorType =\n  | GetChainContractAddressErrorType\n  | NumberToHexErrorType\n  | CreateBatchSchedulerErrorType\n  | EncodeFunctionDataErrorType\n  | DecodeFunctionResultErrorType\n  | RawContractErrorType\n  | ErrorType\n\nasync function scheduleMulticall<chain extends Chain | undefined>(\n  client: Client<Transport>,\n  args: ScheduleMulticallParameters<chain>,\n) {\n  const { batchSize = 1024, wait = 0 } =\n    typeof client.batch?.multicall === 'object' ? client.batch.multicall : {}\n  const {\n    blockNumber,\n    blockTag = 'latest',\n    data,\n    multicallAddress: multicallAddress_,\n    to,\n  } = args\n\n  let multicallAddress = multicallAddress_\n  if (!multicallAddress) {\n    if (!client.chain) throw new ClientChainNotConfiguredError()\n\n    multicallAddress = getChainContractAddress({\n      blockNumber,\n      chain: client.chain,\n      contract: 'multicall3',\n    })\n  }\n\n  const blockNumberHex =\n    typeof blockNumber === 'bigint' ? numberToHex(blockNumber) : undefined\n  const block = blockNumberHex || blockTag\n\n  const { schedule } = createBatchScheduler({\n    id: `${client.uid}.${block}`,\n    wait,\n    shouldSplitBatch(args) {\n      const size = args.reduce((size, { data }) => size + (data.length - 2), 0)\n      return size > batchSize * 2\n    },\n    fn: async (\n      requests: {\n        data: Hex\n        to: Address\n      }[],\n    ) => {\n      const calls = requests.map((request) => ({\n        allowFailure: true,\n        callData: request.data,\n        target: request.to,\n      }))\n\n      const calldata = encodeFunctionData({\n        abi: multicall3Abi,\n        args: [calls],\n        functionName: 'aggregate3',\n      })\n\n      const data = await client.request({\n        method: 'eth_call',\n        params: [\n          {\n            data: calldata,\n            to: multicallAddress,\n          },\n          block,\n        ],\n      })\n\n      return decodeFunctionResult({\n        abi: multicall3Abi,\n        args: [calls],\n        functionName: 'aggregate3',\n        data: data || '0x',\n      })\n    },\n  })\n\n  const [{ returnData, success }] = await schedule({ data, to })\n\n  if (!success) throw new RawContractError({ data: returnData })\n  if (returnData === '0x') return { data: undefined }\n  return { data: returnData }\n}\n\ntype ToDeploylessCallViaBytecodeDataErrorType =\n  | EncodeDeployDataErrorType\n  | ErrorType\n\nfunction toDeploylessCallViaBytecodeData(parameters: {\n  code: Hex\n  data: Hex\n}) {\n  const { code, data } = parameters\n  return encodeDeployData({\n    abi: parseAbi(['constructor(bytes, bytes)']),\n    bytecode: deploylessCallViaBytecodeBytecode,\n    args: [code, data],\n  })\n}\n\ntype ToDeploylessCallViaFactoryDataErrorType =\n  | EncodeDeployDataErrorType\n  | ErrorType\n\nfunction toDeploylessCallViaFactoryData(parameters: {\n  data: Hex\n  factory: Address\n  factoryData: Hex\n  to: Address\n}) {\n  const { data, factory, factoryData, to } = parameters\n  return encodeDeployData({\n    abi: parseAbi(['constructor(address, bytes, address, bytes)']),\n    bytecode: deploylessCallViaFactoryBytecode,\n    args: [to, data, factory, factoryData],\n  })\n}\n\n/** @internal */\nexport type GetRevertErrorDataErrorType = ErrorType\n\n/** @internal */\nexport function getRevertErrorData(err: unknown) {\n  if (!(err instanceof BaseError)) return undefined\n  const error = err.walk() as RawContractError\n  return typeof error?.data === 'object' ? error.data?.data : error.data\n}\n", "import type { Address } from 'abitype'\n\nimport type { Hex } from '../types/misc.js'\nimport { stringify } from '../utils/stringify.js'\n\nimport { BaseError } from './base.js'\nimport { getUrl } from './utils.js'\n\nexport type OffchainLookupErrorType = OffchainLookupError & {\n  name: 'OffchainLookupError'\n}\nexport class OffchainLookupError extends BaseError {\n  constructor({\n    callbackSelector,\n    cause,\n    data,\n    extraData,\n    sender,\n    urls,\n  }: {\n    callbackSelector: Hex\n    cause: BaseError\n    data: Hex\n    extraData: Hex\n    sender: Address\n    urls: readonly string[]\n  }) {\n    super(\n      cause.shortMessage ||\n        'An error occurred while fetching for an offchain result.',\n      {\n        cause,\n        metaMessages: [\n          ...(cause.metaMessages || []),\n          cause.metaMessages?.length ? '' : [],\n          'Offchain Gateway Call:',\n          urls && [\n            '  Gateway URL(s):',\n            ...urls.map((url) => `    ${getUrl(url)}`),\n          ],\n          `  Sender: ${sender}`,\n          `  Data: ${data}`,\n          `  Callback selector: ${callbackSelector}`,\n          `  Extra data: ${extraData}`,\n        ].flat(),\n        name: 'OffchainLookupError',\n      },\n    )\n  }\n}\n\nexport type OffchainLookupResponseMalformedErrorType =\n  OffchainLookupResponseMalformedError & {\n    name: 'OffchainLookupResponseMalformedError'\n  }\nexport class OffchainLookupResponseMalformedError extends BaseError {\n  constructor({ result, url }: { result: any; url: string }) {\n    super(\n      'Offchain gateway response is malformed. Response data must be a hex value.',\n      {\n        metaMessages: [\n          `Gateway URL: ${getUrl(url)}`,\n          `Response: ${stringify(result)}`,\n        ],\n        name: 'OffchainLookupResponseMalformedError',\n      },\n    )\n  }\n}\n\n/** @internal */\nexport type OffchainLookupSenderMismatchErrorType =\n  OffchainLookupSenderMismatchError & {\n    name: 'OffchainLookupSenderMismatchError'\n  }\nexport class OffchainLookupSenderMismatchError extends BaseError {\n  constructor({ sender, to }: { sender: Address; to: Address }) {\n    super(\n      'Reverted sender address does not match target contract address (`to`).',\n      {\n        metaMessages: [\n          `Contract address: ${to}`,\n          `OffchainLookup sender address: ${sender}`,\n        ],\n        name: 'OffchainLookupSenderMismatchError',\n      },\n    )\n  }\n}\n", "import type { Address } from 'abitype'\n\nimport {\n  InvalidAddressError,\n  type InvalidAddressErrorType,\n} from '../../errors/address.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport { isAddress } from './isAddress.js'\n\nexport type IsAddressEqualReturnType = boolean\nexport type IsAddressEqualErrorType = InvalidAddressErrorType | ErrorType\n\nexport function isAddressEqual(a: Address, b: Address) {\n  if (!isAddress(a, { strict: false }))\n    throw new InvalidAddressError({ address: a })\n  if (!isAddress(b, { strict: false }))\n    throw new InvalidAddressError({ address: b })\n  return a.toLowerCase() === b.toLowerCase()\n}\n", "import type { Abi, AbiStateMutability } from 'abitype'\n\nimport { AbiFunctionSignatureNotFoundError } from '../../errors/abi.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type {\n  ContractFunctionArgs,\n  ContractFunctionName,\n} from '../../types/contract.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { IsNarrowable, UnionEvaluate } from '../../types/utils.js'\nimport { type SliceErrorType, slice } from '../data/slice.js'\nimport {\n  type ToFunctionSelectorErrorType,\n  toFunctionSelector,\n} from '../hash/toFunctionSelector.js'\nimport {\n  type DecodeAbiParametersErrorType,\n  decodeAbiParameters,\n} from './decodeAbiParameters.js'\nimport { type FormatAbiItemErrorType, formatAbiItem } from './formatAbiItem.js'\n\nexport type DecodeFunctionDataParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n> = {\n  abi: abi\n  data: Hex\n}\n\nexport type DecodeFunctionDataReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  ///\n  allFunctionNames extends\n    ContractFunctionName<abi> = ContractFunctionName<abi>,\n> = IsNarrowable<abi, Abi> extends true\n  ? UnionEvaluate<\n      {\n        [functionName in allFunctionNames]: {\n          args: ContractFunctionArgs<abi, AbiStateMutability, functionName>\n          functionName: functionName\n        }\n      }[allFunctionNames]\n    >\n  : {\n      args: readonly unknown[] | undefined\n      functionName: string\n    }\n\nexport type DecodeFunctionDataErrorType =\n  | AbiFunctionSignatureNotFoundError\n  | DecodeAbiParametersErrorType\n  | FormatAbiItemErrorType\n  | ToFunctionSelectorErrorType\n  | SliceErrorType\n  | ErrorType\n\nexport function decodeFunctionData<const abi extends Abi | readonly unknown[]>(\n  parameters: DecodeFunctionDataParameters<abi>,\n) {\n  const { abi, data } = parameters as DecodeFunctionDataParameters\n  const signature = slice(data, 0, 4)\n  const description = abi.find(\n    (x) =>\n      x.type === 'function' &&\n      signature === toFunctionSelector(formatAbiItem(x)),\n  )\n  if (!description)\n    throw new AbiFunctionSignatureNotFoundError(signature, {\n      docsPath: '/docs/contract/decodeFunctionData',\n    })\n  return {\n    functionName: (description as { name: string }).name,\n    args: ('inputs' in description &&\n    description.inputs &&\n    description.inputs.length > 0\n      ? decodeAbiParameters(description.inputs, slice(data, 4))\n      : undefined) as readonly unknown[] | undefined,\n  } as DecodeFunctionDataReturnType<abi>\n}\n", "import type { Abi, ExtractAbiErrors } from 'abitype'\n\nimport {\n  AbiErrorInputsNotFoundError,\n  AbiErrorNotFoundError,\n} from '../../errors/abi.js'\nimport type {\n  ContractErrorArgs,\n  ContractErrorName,\n} from '../../types/contract.js'\nimport type { Hex } from '../../types/misc.js'\nimport { type ConcatHexErrorType, concatHex } from '../data/concat.js'\nimport {\n  type ToFunctionSelectorErrorType,\n  toFunctionSelector,\n} from '../hash/toFunctionSelector.js'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { IsNarrowable, UnionEvaluate } from '../../types/utils.js'\nimport {\n  type EncodeAbiParametersErrorType,\n  encodeAbiParameters,\n} from './encodeAbiParameters.js'\nimport { type FormatAbiItemErrorType, formatAbiItem } from './formatAbiItem.js'\nimport { type GetAbiItemErrorType, getAbiItem } from './getAbiItem.js'\n\nconst docsPath = '/docs/contract/encodeErrorResult'\n\nexport type EncodeErrorResultParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  errorName extends ContractErrorName<abi> | undefined = ContractErrorName<abi>,\n  ///\n  hasErrors = abi extends Abi\n    ? Abi extends abi\n      ? true\n      : [ExtractAbiErrors<abi>] extends [never]\n        ? false\n        : true\n    : true,\n  allArgs = ContractErrorArgs<\n    abi,\n    errorName extends ContractErrorName<abi>\n      ? errorName\n      : ContractErrorName<abi>\n  >,\n  allErrorNames = ContractErrorName<abi>,\n> = {\n  abi: abi\n  args?: allArgs | undefined\n} & UnionEvaluate<\n  IsNarrowable<abi, Abi> extends true\n    ? abi['length'] extends 1\n      ? { errorName?: errorName | allErrorNames | undefined }\n      : { errorName: errorName | allErrorNames }\n    : { errorName?: errorName | allErrorNames | undefined }\n> &\n  (hasErrors extends true ? unknown : never)\n\nexport type EncodeErrorResultReturnType = Hex\n\nexport type EncodeErrorResultErrorType =\n  | GetAbiItemErrorType\n  | FormatAbiItemErrorType\n  | ToFunctionSelectorErrorType\n  | EncodeAbiParametersErrorType\n  | ConcatHexErrorType\n  | ErrorType\n\nexport function encodeErrorResult<\n  const abi extends Abi | readonly unknown[],\n  errorName extends ContractErrorName<abi> | undefined = undefined,\n>(\n  parameters: EncodeErrorResultParameters<abi, errorName>,\n): EncodeErrorResultReturnType {\n  const { abi, errorName, args } = parameters as EncodeErrorResultParameters\n\n  let abiItem = abi[0]\n  if (errorName) {\n    const item = getAbiItem({ abi, args, name: errorName })\n    if (!item) throw new AbiErrorNotFoundError(errorName, { docsPath })\n    abiItem = item\n  }\n\n  if (abiItem.type !== 'error')\n    throw new AbiErrorNotFoundError(undefined, { docsPath })\n\n  const definition = formatAbiItem(abiItem)\n  const signature = toFunctionSelector(definition)\n\n  let data: Hex = '0x'\n  if (args && args.length > 0) {\n    if (!abiItem.inputs)\n      throw new AbiErrorInputsNotFoundError(abiItem.name, { docsPath })\n    data = encodeAbiParameters(abiItem.inputs, args)\n  }\n  return concatHex([signature, data])\n}\n", "import type { Abi, AbiStateMutability, ExtractAbiFunctions } from 'abitype'\n\nimport {\n  AbiFunctionNotFoundError,\n  AbiFunctionOutputsNotFoundError,\n  InvalidArrayError,\n} from '../../errors/abi.js'\nimport type {\n  ContractFunctionName,\n  ContractFunctionReturnType,\n} from '../../types/contract.js'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { IsNarrowable, UnionEvaluate } from '../../types/utils.js'\nimport {\n  type EncodeAbiParametersErrorType,\n  encodeAbiParameters,\n} from './encodeAbiParameters.js'\nimport { type GetAbiItemErrorType, getAbiItem } from './getAbiItem.js'\n\nconst docsPath = '/docs/contract/encodeFunctionResult'\n\nexport type EncodeFunctionResultParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends\n    | ContractFunctionName<abi>\n    | undefined = ContractFunctionName<abi>,\n  ///\n  hasFunctions = abi extends Abi\n    ? Abi extends abi\n      ? true\n      : [ExtractAbiFunctions<abi>] extends [never]\n        ? false\n        : true\n    : true,\n  allFunctionNames = ContractFunctionName<abi>,\n> = {\n  abi: abi\n  result?:\n    | ContractFunctionReturnType<\n        abi,\n        AbiStateMutability,\n        functionName extends ContractFunctionName<abi>\n          ? functionName\n          : ContractFunctionName<abi>,\n        never // allow all args. required for overloads to work.\n      >\n    | undefined\n} & UnionEvaluate<\n  IsNarrowable<abi, Abi> extends true\n    ? abi['length'] extends 1\n      ? { functionName?: functionName | allFunctionNames | undefined }\n      : { functionName: functionName | allFunctionNames }\n    : { functionName?: functionName | allFunctionNames | undefined }\n> &\n  (hasFunctions extends true ? unknown : never)\n\nexport type EncodeFunctionResultReturnType = Hex\n\nexport type EncodeFunctionResultErrorType =\n  | AbiFunctionOutputsNotFoundError\n  | AbiFunctionNotFoundError\n  | EncodeAbiParametersErrorType\n  | GetAbiItemErrorType\n  | ErrorType\n\nexport function encodeFunctionResult<\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi> | undefined = undefined,\n>(\n  parameters: EncodeFunctionResultParameters<abi, functionName>,\n): EncodeFunctionResultReturnType {\n  const { abi, functionName, result } =\n    parameters as EncodeFunctionResultParameters\n\n  let abiItem = abi[0]\n  if (functionName) {\n    const item = getAbiItem({ abi, name: functionName })\n    if (!item) throw new AbiFunctionNotFoundError(functionName, { docsPath })\n    abiItem = item\n  }\n\n  if (abiItem.type !== 'function')\n    throw new AbiFunctionNotFoundError(undefined, { docsPath })\n\n  if (!abiItem.outputs)\n    throw new AbiFunctionOutputsNotFoundError(abiItem.name, { docsPath })\n\n  const values = (() => {\n    if (abiItem.outputs.length === 0) return []\n    if (abiItem.outputs.length === 1) return [result]\n    if (Array.isArray(result)) return result\n    throw new InvalidArrayError(result)\n  })()\n\n  return encodeAbiParameters(abiItem.outputs, values)\n}\n", "import { batchGatewayAbi } from '../../constants/abis.js'\nimport { solidityError } from '../../constants/solidity.js'\nimport type { Hex } from '../../types/misc.js'\nimport { decodeFunctionData } from '../abi/decodeFunctionData.js'\nimport { encodeErrorResult } from '../abi/encodeErrorResult.js'\nimport { encodeFunctionResult } from '../abi/encodeFunctionResult.js'\nimport type {\n  CcipRequestErrorType,\n  CcipRequestParameters,\n  CcipRequestReturnType,\n} from '../ccip.js'\n\nexport const localBatchGatewayUrl = 'x-batch-gateway:true'\n\nexport async function localBatchGatewayRequest(parameters: {\n  data: Hex\n  ccipRequest: (\n    parameters: CcipRequestParameters,\n  ) => Promise<CcipRequestReturnType>\n}): Promise<Hex> {\n  const { data, ccipRequest } = parameters\n\n  const {\n    args: [queries],\n  } = decodeFunctionData({ abi: batchGatewayAbi, data })\n\n  const failures: boolean[] = []\n  const responses: Hex[] = []\n  await Promise.all(\n    queries.map(async (query, i) => {\n      try {\n        responses[i] = await ccipRequest(query)\n        failures[i] = false\n      } catch (err) {\n        failures[i] = true\n        responses[i] = encodeError(err as CcipRequestErrorType)\n      }\n    }),\n  )\n\n  return encodeFunctionResult({\n    abi: batchGatewayAbi,\n    functionName: 'query',\n    result: [failures, responses],\n  })\n}\n\nfunction encodeError(error: CcipRequestErrorType): Hex {\n  if (error.name === 'HttpRequestError' && error.status)\n    return encodeErrorResult({\n      abi: batchGatewayAbi,\n      errorName: 'HttpError',\n      args: [error.status, error.shortMessage],\n    })\n  return encodeErrorResult({\n    abi: [solidityError],\n    errorName: 'Error',\n    args: ['shortMessage' in error ? error.shortMessage : error.message],\n  })\n}\n", "import type { Abi, Address } from 'abitype'\n\nimport { type CallParameters, call } from '../actions/public/call.js'\nimport type { Transport } from '../clients/transports/createTransport.js'\nimport type { BaseError } from '../errors/base.js'\nimport {\n  OffchainLookupError,\n  type OffchainLookupErrorType as OffchainLookupErrorType_,\n  OffchainLookupResponseMalformedError,\n  type OffchainLookupResponseMalformedErrorType,\n  OffchainLookupSenderMismatchError,\n} from '../errors/ccip.js'\nimport {\n  HttpRequestError,\n  type HttpRequestErrorType,\n} from '../errors/request.js'\nimport type { Chain } from '../types/chain.js'\nimport type { Hex } from '../types/misc.js'\n\nimport type { Client } from '../clients/createClient.js'\nimport type { ErrorType } from '../errors/utils.js'\nimport { decodeErrorResult } from './abi/decodeErrorResult.js'\nimport { encodeAbiParameters } from './abi/encodeAbiParameters.js'\nimport { isAddressEqual } from './address/isAddressEqual.js'\nimport { concat } from './data/concat.js'\nimport { isHex } from './data/isHex.js'\nimport {\n  localBatchGatewayRequest,\n  localBatchGatewayUrl,\n} from './ens/localBatchGatewayRequest.js'\nimport { stringify } from './stringify.js'\n\nexport const offchainLookupSignature = '0x556f1830'\nexport const offchainLookupAbiItem = {\n  name: 'OffchainLookup',\n  type: 'error',\n  inputs: [\n    {\n      name: 'sender',\n      type: 'address',\n    },\n    {\n      name: 'urls',\n      type: 'string[]',\n    },\n    {\n      name: 'callData',\n      type: 'bytes',\n    },\n    {\n      name: 'callbackFunction',\n      type: 'bytes4',\n    },\n    {\n      name: 'extraData',\n      type: 'bytes',\n    },\n  ],\n} as const satisfies Abi[number]\n\nexport type OffchainLookupErrorType = OffchainLookupErrorType_ | ErrorType\n\nexport async function offchainLookup<chain extends Chain | undefined>(\n  client: Client<Transport, chain>,\n  {\n    blockNumber,\n    blockTag,\n    data,\n    to,\n  }: Pick<CallParameters, 'blockNumber' | 'blockTag'> & {\n    data: Hex\n    to: Address\n  },\n): Promise<Hex> {\n  const { args } = decodeErrorResult({\n    data,\n    abi: [offchainLookupAbiItem],\n  })\n  const [sender, urls, callData, callbackSelector, extraData] = args\n\n  const { ccipRead } = client\n  const ccipRequest_ =\n    ccipRead && typeof ccipRead?.request === 'function'\n      ? ccipRead.request\n      : ccipRequest\n\n  try {\n    if (!isAddressEqual(to, sender))\n      throw new OffchainLookupSenderMismatchError({ sender, to })\n\n    const result = urls.includes(localBatchGatewayUrl)\n      ? await localBatchGatewayRequest({\n          data: callData,\n          ccipRequest: ccipRequest_,\n        })\n      : await ccipRequest_({ data: callData, sender, urls })\n\n    const { data: data_ } = await call(client, {\n      blockNumber,\n      blockTag,\n      data: concat([\n        callbackSelector,\n        encodeAbiParameters(\n          [{ type: 'bytes' }, { type: 'bytes' }],\n          [result, extraData],\n        ),\n      ]),\n      to,\n    } as CallParameters)\n\n    return data_!\n  } catch (err) {\n    throw new OffchainLookupError({\n      callbackSelector,\n      cause: err as BaseError,\n      data,\n      extraData,\n      sender,\n      urls,\n    })\n  }\n}\n\nexport type CcipRequestParameters = {\n  data: Hex\n  sender: Address\n  urls: readonly string[]\n}\n\nexport type CcipRequestReturnType = Hex\n\nexport type CcipRequestErrorType =\n  | HttpRequestErrorType\n  | OffchainLookupResponseMalformedErrorType\n  | ErrorType\n\nexport async function ccipRequest({\n  data,\n  sender,\n  urls,\n}: CcipRequestParameters): Promise<CcipRequestReturnType> {\n  let error = new Error('An unknown error occurred.')\n\n  for (let i = 0; i < urls.length; i++) {\n    const url = urls[i]\n    const method = url.includes('{data}') ? 'GET' : 'POST'\n    const body = method === 'POST' ? { data, sender } : undefined\n    const headers: HeadersInit =\n      method === 'POST' ? { 'Content-Type': 'application/json' } : {}\n\n    try {\n      const response = await fetch(\n        url.replace('{sender}', sender.toLowerCase()).replace('{data}', data),\n        {\n          body: JSON.stringify(body),\n          headers,\n          method,\n        },\n      )\n\n      let result: any\n      if (\n        response.headers.get('Content-Type')?.startsWith('application/json')\n      ) {\n        result = (await response.json()).data\n      } else {\n        result = (await response.text()) as any\n      }\n\n      if (!response.ok) {\n        error = new HttpRequestError({\n          body,\n          details: result?.error\n            ? stringify(result.error)\n            : response.statusText,\n          headers: response.headers,\n          status: response.status,\n          url,\n        })\n        continue\n      }\n\n      if (!isHex(result)) {\n        error = new OffchainLookupResponseMalformedError({\n          result,\n          url,\n        })\n        continue\n      }\n\n      return result\n    } catch (err) {\n      error = new HttpRequestError({\n        body,\n        details: (err as Error).message,\n        url,\n      })\n    }\n  }\n\n  throw error\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACO,IAAM,UAAU;;;ACOjB,SAAU,aAAU;AACxB,SAAO;AACT;;;ACKM,IAAOA,aAAP,MAAO,mBAEH,MAAK;EAWb,YAAY,cAAsB,UAAoC,CAAA,GAAE;AACtE,UAAM,WAAW,MAAK;AA7B1B;AA8BM,UAAI,QAAQ,iBAAiB,YAAW;AACtC,YAAI,QAAQ,MAAM;AAAS,iBAAO,QAAQ,MAAM;AAChD,YAAI,QAAQ,MAAM;AAAc,iBAAO,QAAQ,MAAM;MACvD;AACA,UACE,QAAQ,SACR,aAAa,QAAQ,SACrB,OAAO,QAAQ,MAAM,YAAY;AAEjC,eAAO,QAAQ,MAAM;AACvB,WAAI,aAAQ,UAAR,mBAAe;AAAS,eAAO,QAAQ,MAAM;AACjD,aAAO,QAAQ;IACjB,GAAE;AACF,UAAMC,aAAY,MAAK;AACrB,UAAI,QAAQ,iBAAiB;AAC3B,eAAO,QAAQ,MAAM,YAAY,QAAQ;AAC3C,aAAO,QAAQ;IACjB,GAAE;AAEF,UAAM,cAAc;AACpB,UAAM,OAAO,GAAG,WAAW,GAAGA,aAAY,EAAE;AAE5C,UAAM,UAAU;MACd,gBAAgB;MAChB,GAAI,QAAQ,eAAe,CAAC,IAAI,GAAG,QAAQ,YAAY,IAAI,CAAA;MAC3D,GAAI,WAAWA,YACX;QACE;QACA,UAAU,YAAY,OAAO,KAAK;QAClCA,YAAW,QAAQ,IAAI,KAAK;UAE9B,CAAA;MAEH,OAAO,CAAC,MAAM,OAAO,MAAM,QAAQ,EACnC,KAAK,IAAI;AAEZ,UAAM,SAAS,QAAQ,QAAQ,EAAE,OAAO,QAAQ,MAAK,IAAK,MAAS;AAhDrE,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,QAAA;;;;;;AACA,WAAA,eAAA,MAAA,YAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AAES,WAAA,eAAA,MAAA,SAAA;;;;;;AACA,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,WAAA;;;;aAAU,MAAM,WAAU,CAAE;;AA0C1B,SAAK,QAAQ,QAAQ;AACrB,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,WAAWA;AAChB,SAAK,eAAe;EACtB;EAIA,KAAK,IAAQ;AACX,WAAO,KAAK,MAAM,EAAE;EACtB;;AAaF,SAAS,KACP,KACA,IAA4C;AAE5C,MAAI,yBAAK;AAAM,WAAO;AACtB,MAAI,OAAO,OAAO,QAAQ,YAAY,WAAW,OAAO,IAAI;AAC1D,WAAO,KAAK,IAAI,OAAO,EAAE;AAC3B,SAAO,KAAK,OAAO;AACrB;;;AClGA,IAAM,eAAe;AAuDf,SAAUC,WACd,OACA,UACA,OAAmC;AAEnC,SAAO,KAAK,UACV,OACA,CAAC,KAAKC,WAAS;AACb,QAAI,OAAO,aAAa;AAAY,aAAO,SAAS,KAAKA,MAAK;AAC9D,QAAI,OAAOA,WAAU;AAAU,aAAOA,OAAM,SAAQ,IAAK;AACzD,WAAOA;EACT,GACA,KAAK;AAET;;;ACnEM,SAAU,WAAW,OAAoB,OAAa;AAC1D,MAAU,KAAK,KAAK,IAAI;AACtB,UAAM,IAAU,kBAAkB;MAChC,WAAiB,KAAK,KAAK;MAC3B,SAAS;KACV;AACL;AA0DO,IAAM,cAAc;EACzB,MAAM;EACN,MAAM;EACN,GAAG;EACH,GAAG;EACH,GAAG;EACH,GAAG;;AAIC,SAAU,iBAAiB,MAAY;AAC3C,MAAI,QAAQ,YAAY,QAAQ,QAAQ,YAAY;AAClD,WAAO,OAAO,YAAY;AAC5B,MAAI,QAAQ,YAAY,KAAK,QAAQ,YAAY;AAC/C,WAAO,QAAQ,YAAY,IAAI;AACjC,MAAI,QAAQ,YAAY,KAAK,QAAQ,YAAY;AAC/C,WAAO,QAAQ,YAAY,IAAI;AACjC,SAAO;AACT;AAGM,SAAU,IAAI,OAAoB,UAAuB,CAAA,GAAE;AAC/D,QAAM,EAAE,KAAK,MAAAC,QAAO,GAAE,IAAK;AAC3B,MAAIA,UAAS;AAAG,WAAO;AACvB,MAAI,MAAM,SAASA;AACjB,UAAM,IAAU,4BAA4B;MAC1C,MAAM,MAAM;MACZ,YAAYA;MACZ,MAAM;KACP;AACH,QAAM,cAAc,IAAI,WAAWA,KAAI;AACvC,WAAS,IAAI,GAAG,IAAIA,OAAM,KAAK;AAC7B,UAAM,SAAS,QAAQ;AACvB,gBAAY,SAAS,IAAIA,QAAO,IAAI,CAAC,IACnC,MAAM,SAAS,IAAI,MAAM,SAAS,IAAI,CAAC;EAC3C;AACA,SAAO;AACT;;;ACrGM,SAAUC,YAAW,KAAc,OAAa;AACpD,MAAQC,MAAK,GAAG,IAAI;AAClB,UAAM,IAAQC,mBAAkB;MAC9B,WAAeD,MAAK,GAAG;MACvB,SAAS;KACV;AACL;AAWM,SAAU,kBAAkB,OAAgB,OAA0B;AAC1E,MAAI,OAAO,UAAU,YAAY,QAAQ,KAAK,QAAYA,MAAK,KAAK,IAAI;AACtE,UAAM,IAAQE,6BAA4B;MACxC,QAAQ;MACR,UAAU;MACV,MAAUF,MAAK,KAAK;KACrB;AACL;AAUM,SAAU,gBACd,OACA,OACA,KAAwB;AAExB,MACE,OAAO,UAAU,YACjB,OAAO,QAAQ,YACXA,MAAK,KAAK,MAAM,MAAM,OAC1B;AACA,UAAM,IAAQE,6BAA4B;MACxC,QAAQ;MACR,UAAU;MACV,MAAUF,MAAK,KAAK;KACrB;EACH;AACF;AAUM,SAAUG,KAAI,MAAe,UAAuB,CAAA,GAAE;AAC1D,QAAM,EAAE,KAAK,MAAAH,QAAO,GAAE,IAAK;AAE3B,MAAIA,UAAS;AAAG,WAAO;AAEvB,QAAM,MAAM,KAAK,QAAQ,MAAM,EAAE;AACjC,MAAI,IAAI,SAASA,QAAO;AACtB,UAAM,IAAQI,6BAA4B;MACxC,MAAM,KAAK,KAAK,IAAI,SAAS,CAAC;MAC9B,YAAYJ;MACZ,MAAM;KACP;AAEH,SAAO,KAAK,IAAI,QAAQ,UAAU,WAAW,UAAU,EAAEA,QAAO,GAAG,GAAG,CAAC;AACzE;;;ACvEA,IAAM,UAAwB,IAAI,YAAW;AAC7C,IAAM,UAAwB,IAAI,YAAW;AAmGvC,SAAU,KAAK,OAA0C;AAC7D,MAAI,iBAAiB;AAAY,WAAO;AACxC,MAAI,OAAO,UAAU;AAAU,WAAO,QAAQ,KAAK;AACnD,SAAO,UAAU,KAAK;AACxB;AAuBM,SAAU,UAAU,OAAqC;AAC7D,SAAO,iBAAiB,aAAa,QAAQ,IAAI,WAAW,KAAK;AACnE;AA2EM,SAAU,QAAQ,OAAgB,UAA2B,CAAA,GAAE;AACnE,QAAM,EAAE,MAAAK,MAAI,IAAK;AAEjB,MAAI,MAAM;AACV,MAAIA,OAAM;AACR,IAAaC,YAAW,OAAOD,KAAI;AACnC,UAAU,SAAS,OAAOA,KAAI;EAChC;AAEA,MAAI,YAAY,IAAI,MAAM,CAAC;AAC3B,MAAI,UAAU,SAAS;AAAG,gBAAY,IAAI,SAAS;AAEnD,QAAM,SAAS,UAAU,SAAS;AAClC,QAAM,QAAQ,IAAI,WAAW,MAAM;AACnC,WAAS,QAAQ,GAAG,IAAI,GAAG,QAAQ,QAAQ,SAAS;AAClD,UAAM,aAAsB,iBAAiB,UAAU,WAAW,GAAG,CAAC;AACtE,UAAM,cAAuB,iBAAiB,UAAU,WAAW,GAAG,CAAC;AACvE,QAAI,eAAe,UAAa,gBAAgB,QAAW;AACzD,YAAM,IAAWE,WACf,2BAA2B,UAAU,IAAI,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,CAAC,SAAS,SAAS,KAAK;IAEzF;AACA,UAAM,KAAK,IAAI,aAAa,KAAK;EACnC;AACA,SAAO;AACT;AA6EM,SAAU,WACd,OACA,UAA8B,CAAA,GAAE;AAEhC,QAAM,EAAE,MAAAC,MAAI,IAAK;AAEjB,QAAM,QAAQ,QAAQ,OAAO,KAAK;AAClC,MAAI,OAAOA,UAAS,UAAU;AAC5B,IAAS,WAAW,OAAOA,KAAI;AAC/B,WAAOC,UAAS,OAAOD,KAAI;EAC7B;AACA,SAAO;AACT;AAkFM,SAAUE,UACd,OACAC,OAAyB;AAEzB,SAAgB,IAAI,OAAO,EAAE,KAAK,SAAS,MAAAA,MAAI,CAAE;AACnD;AA2CM,SAAU,KAAK,OAAY;AAC/B,SAAO,MAAM;AACf;AA0WM,IAAO,oBAAP,cAAwCC,WAAS;EAGrD,YAAY,EAAE,WAAW,QAAO,GAA0C;AACxE,UACE,wBAAwB,OAAO,2BAA2B,SAAS,WAAW;AAJhE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAyCI,IAAO,8BAAP,cAAkDC,WAAS;EAG/D,YAAY,EACV,MAAAC,OACA,YACA,KAAI,GAKL;AACC,UACE,GAAG,KAAK,OAAO,CAAC,EAAE,YAAW,CAAE,GAAG,KAC/B,MAAM,CAAC,EACP,YAAW,CAAE,YAAYA,KAAI,+BAA+B,UAAU,MAAM;AAdjE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAgBzB;;;;AC72BF,IAAMC,WAAwB,IAAI,YAAW;AAE7C,IAAM,QAAsB,MAAM,KAAK,EAAE,QAAQ,IAAG,GAAI,CAAC,IAAI,MAC3D,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AAsB3B,SAAU,OACd,OACA,UAA0B,CAAA,GAAE;AAE5B,QAAM,EAAE,SAAS,MAAK,IAAK;AAC3B,MAAI,CAAC;AAAO,UAAM,IAAI,oBAAoB,KAAK;AAC/C,MAAI,OAAO,UAAU;AAAU,UAAM,IAAI,oBAAoB,KAAK;AAClE,MAAI,QAAQ;AACV,QAAI,CAAC,mBAAmB,KAAK,KAAK;AAAG,YAAM,IAAI,qBAAqB,KAAK;EAC3E;AACA,MAAI,CAAC,MAAM,WAAW,IAAI;AAAG,UAAM,IAAI,qBAAqB,KAAK;AACnE;AA4BM,SAAUC,WAAU,QAAsB;AAC9C,SAAO,KAAM,OAAiB,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,QAAQ,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AAuEM,SAAU,YACd,OACA,UAA+B,CAAA,GAAE;AAEjC,QAAM,MAAW,KAAK,OAAO,KAAK,CAAC;AACnC,MAAI,OAAO,QAAQ,SAAS,UAAU;AACpC,IAASC,YAAW,KAAK,QAAQ,IAAI;AACrC,WAAO,QAAQ,KAAK,QAAQ,IAAI;EAClC;AACA,SAAO;AACT;AA6BM,SAAU,UACd,OACA,UAA6B,CAAA,GAAE;AAE/B,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAAK,cAAU,MAAM,MAAM,CAAC,CAAE;AAChE,QAAM,MAAM,KAAK,MAAM;AAEvB,MAAI,OAAO,QAAQ,SAAS,UAAU;AACpC,IAASA,YAAW,KAAK,QAAQ,IAAI;AACrC,WAAO,SAAS,KAAK,QAAQ,IAAI;EACnC;AACA,SAAO;AACT;AAgCM,SAAU,WACd,OACA,UAA8B,CAAA,GAAE;AAEhC,QAAM,EAAE,QAAQ,MAAAC,MAAI,IAAK;AAEzB,QAAM,SAAS,OAAO,KAAK;AAE3B,MAAI;AACJ,MAAIA,OAAM;AACR,QAAI;AAAQ,kBAAY,MAAO,OAAOA,KAAI,IAAI,KAAK,MAAO;;AACrD,iBAAW,OAAO,OAAOA,KAAI,IAAI,MAAM;EAC9C,WAAW,OAAO,UAAU,UAAU;AACpC,eAAW,OAAO,OAAO,gBAAgB;EAC3C;AAEA,QAAM,WAAW,OAAO,aAAa,YAAY,SAAS,CAAC,WAAW,KAAK;AAE3E,MAAK,YAAY,SAAS,YAAa,SAAS,UAAU;AACxD,UAAM,SAAS,OAAO,UAAU,WAAW,MAAM;AACjD,UAAM,IAAI,uBAAuB;MAC/B,KAAK,WAAW,GAAG,QAAQ,GAAG,MAAM,KAAK;MACzC,KAAK,GAAG,QAAQ,GAAG,MAAM;MACzB;MACA,MAAAA;MACA,OAAO,GAAG,KAAK,GAAG,MAAM;KACzB;EACH;AAEA,QAAM,eACJ,UAAU,SAAS,KAAK,MAAM,OAAOA,QAAO,CAAC,KAAK,OAAO,MAAM,IAAI,QACnE,SAAS,EAAE;AAEb,QAAM,MAAM,KAAK,WAAW;AAC5B,MAAIA;AAAM,WAAO,QAAQ,KAAKA,KAAI;AAClC,SAAO;AACT;AAuCM,SAAUC,YACd,OACA,UAA8B,CAAA,GAAE;AAEhC,SAAO,UAAUC,SAAQ,OAAO,KAAK,GAAG,OAAO;AACjD;AAoDM,SAAU,QACd,OACAC,OAAyB;AAEzB,SAAgBC,KAAI,OAAO,EAAE,KAAK,QAAQ,MAAAD,MAAI,CAAE;AAClD;AAsBM,SAAU,SACd,OACAA,OAAyB;AAEzB,SAAgBC,KAAI,OAAO,EAAE,KAAK,SAAS,MAAAD,MAAI,CAAE;AACnD;AA6CM,SAAUE,OACd,OACA,OACA,KACA,UAAyB,CAAA,GAAE;AAE3B,QAAM,EAAE,OAAM,IAAK;AACnB,EAAS,kBAAkB,OAAO,KAAK;AACvC,QAAM,SAAS,KAAK,MACjB,QAAQ,MAAM,EAAE,EAChB,OAAO,SAAS,KAAK,IAAI,OAAO,MAAM,UAAU,CAAC,CAAC;AACrD,MAAI;AAAQ,IAAS,gBAAgB,QAAQ,OAAO,GAAG;AACvD,SAAO;AACT;AA4BM,SAAUC,MAAK,OAAU;AAC7B,SAAO,KAAK,MAAM,MAAM,SAAS,KAAK,CAAC;AACzC;AAmQM,SAAU,SACd,OACA,UAA4B,CAAA,GAAE;AAE9B,QAAM,EAAE,SAAS,MAAK,IAAK;AAC3B,MAAI;AACF,WAAO,OAAO,EAAE,OAAM,CAAE;AACxB,WAAO;EACT,QAAQ;AACN,WAAO;EACT;AACF;AAsBM,IAAO,yBAAP,cAA6CC,WAAS;EAG1D,YAAY,EACV,KACA,KACA,QACA,MAAAC,OACA,MAAK,GAON;AACC,UACE,YAAY,KAAK,oBACfA,QAAO,IAAIA,QAAO,CAAC,SAAS,EAC9B,GAAG,SAAS,YAAY,WAAW,kBAAkB,MAAM,MAAM,GAAG,WAAW,GAAG,QAAQ,YAAY,GAAG,KAAK,EAAE;AAlBlG,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAoBzB;;AAsCI,IAAO,sBAAP,cAA0CC,WAAS;EAGvD,YAAY,OAAc;AACxB,UACE,WAAW,OAAO,UAAU,WAAgBC,WAAU,KAAK,IAAI,KAAK,gBAAgB,OAAO,KAAK,8BAChG;MACE,cAAc,CAAC,mDAAmD;KACnE;AAPa,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EASzB;;AAeI,IAAO,uBAAP,cAA2CD,WAAS;EAGxD,YAAY,OAAc;AACxB,UAAM,WAAW,KAAK,+BAA+B;MACnD,cAAc;QACZ;;KAEH;AAPe,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQzB;;AAsCI,IAAOE,qBAAP,cAAwCC,WAAS;EAGrD,YAAY,EAAE,WAAW,QAAO,GAA0C;AACxE,UACE,wBAAwB,OAAO,2BAA2B,SAAS,WAAW;AAJhE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAcI,IAAOC,+BAAP,cAAkDD,WAAS;EAG/D,YAAY,EACV,QACA,UACA,MAAAE,MAAI,GACwD;AAC5D,UACE,SACE,aAAa,UAAU,aAAa,QACtC,gBAAgB,MAAM,gCAAgCA,KAAI,MAAM;AAVlD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYzB;;AAcI,IAAOC,+BAAP,cAAkDH,WAAS;EAG/D,YAAY,EACV,MAAAE,OACA,YACA,KAAI,GAKL;AACC,UACE,GAAG,KAAK,OAAO,CAAC,EAAE,YAAW,CAAE,GAAG,KAC/B,MAAM,CAAC,EACP,YAAW,CAAE,YAAYA,KAAI,+BAA+B,UAAU,MAAM;AAdjE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAgBzB;;;;AC73BI,SAAU,MAAM,YAAsB;AAC1C,SAAO;IACL,SAAS,WAAW;IACpB,QAAY,WAAW,WAAW,MAAM;IACxC,OAAW,WAAW,WAAW,KAAK;IACtC,gBAAoB,WAAW,WAAW,cAAc;;AAE5D;;;ACoCM,SAAUE,OAAM,gBAA8B;AAClD,SAAO;IACL,GAAI,OAAO,eAAe,kBAAkB,YAAY;MACtD,eAAmB,WAAW,eAAe,aAAa;;IAE5D,GAAI,OAAO,eAAe,gBAAgB,YAAY;MACpD,aAAiB,WAAW,eAAe,WAAW;;IAExD,GAAI,OAAO,eAAe,iBAAiB,YAAY;MACrD,cAAc,eAAe;;IAE/B,GAAI,OAAO,eAAe,aAAa,YAAY;MACjD,UAAc,WAAW,eAAe,QAAQ;;IAElD,GAAI,OAAO,eAAe,WAAW,YAAY;MAC/C,QAAY,WAAW,eAAe,MAAM;;IAE9C,GAAI,OAAO,eAAe,eAAe,YAAY;MACnD,YAAgB,WAAW,eAAe,UAAU;;IAEtD,GAAI,OAAO,eAAe,SAAS,YAAY;MAC7C,MAAU,WAAW,eAAe,IAAI;;IAE1C,GAAI,eAAe,eAAe;MAChC,aAAa,eAAe,YAAY,IAAe,KAAK;;;AAGlE;;;AChJO,IAAM,gBAAgB;EAC3B;IACE,QAAQ;MACN;QACE,YAAY;UACV;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;;QAGV,MAAM;QACN,MAAM;;;IAGV,MAAM;IACN,SAAS;MACP;QACE,YAAY;UACV;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;;QAGV,MAAM;QACN,MAAM;;;IAGV,iBAAiB;IACjB,MAAM;;;AAIH,IAAM,kBAAkB;EAC7B;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN;QACE,MAAM;QACN,MAAM;QACN,YAAY;UACV;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;;;;IAKd,SAAS;MACP;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;EAIZ;IACE,MAAM;IACN,MAAM;IACN,QAAQ;MACN;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;;AAMd,IAAM,0BAA0B;EAC9B;IACE,QAAQ,CAAA;IACR,MAAM;IACN,MAAM;;EAER;IACE,QAAQ,CAAA;IACR,MAAM;IACN,MAAM;;EAER;IACE,QAAQ,CAAA;IACR,MAAM;IACN,MAAM;;EAER;IACE,QAAQ;MACN;QACE,MAAM;QACN,MAAM;;;IAGV,MAAM;IACN,MAAM;;EAER;IACE,QAAQ;MACN;QACE,YAAY;UACV;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;;QAGV,MAAM;QACN,MAAM;;;IAGV,MAAM;IACN,MAAM;;;AAIH,IAAM,8BAA8B;EACzC,GAAG;EACH;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN,EAAE,MAAM,QAAQ,MAAM,QAAO;MAC7B,EAAE,MAAM,QAAQ,MAAM,QAAO;;IAE/B,SAAS;MACP,EAAE,MAAM,IAAI,MAAM,QAAO;MACzB,EAAE,MAAM,WAAW,MAAM,UAAS;;;EAGtC;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN,EAAE,MAAM,QAAQ,MAAM,QAAO;MAC7B,EAAE,MAAM,QAAQ,MAAM,QAAO;MAC7B,EAAE,MAAM,YAAY,MAAM,WAAU;;IAEtC,SAAS;MACP,EAAE,MAAM,IAAI,MAAM,QAAO;MACzB,EAAE,MAAM,WAAW,MAAM,UAAS;;;;AAKjC,IAAM,8BAA8B;EACzC,GAAG;EACH;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ,CAAC,EAAE,MAAM,SAAS,MAAM,cAAa,CAAE;IAC/C,SAAS;MACP,EAAE,MAAM,UAAU,MAAM,eAAc;MACtC,EAAE,MAAM,WAAW,MAAM,kBAAiB;MAC1C,EAAE,MAAM,WAAW,MAAM,kBAAiB;MAC1C,EAAE,MAAM,WAAW,MAAM,WAAU;;;EAGvC;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN,EAAE,MAAM,SAAS,MAAM,cAAa;MACpC,EAAE,MAAM,YAAY,MAAM,WAAU;;IAEtC,SAAS;MACP,EAAE,MAAM,UAAU,MAAM,eAAc;MACtC,EAAE,MAAM,WAAW,MAAM,kBAAiB;MAC1C,EAAE,MAAM,WAAW,MAAM,kBAAiB;MAC1C,EAAE,MAAM,WAAW,MAAM,WAAU;;;;AAKlC,IAAM,kBAAkB;EAC7B;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN,EAAE,MAAM,QAAQ,MAAM,UAAS;MAC/B,EAAE,MAAM,OAAO,MAAM,SAAQ;;IAE/B,SAAS,CAAC,EAAE,MAAM,IAAI,MAAM,SAAQ,CAAE;;;AAInC,IAAM,qBAAqB;EAChC;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ,CAAC,EAAE,MAAM,QAAQ,MAAM,UAAS,CAAE;IAC1C,SAAS,CAAC,EAAE,MAAM,IAAI,MAAM,UAAS,CAAE;;EAEzC;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN,EAAE,MAAM,QAAQ,MAAM,UAAS;MAC/B,EAAE,MAAM,YAAY,MAAM,UAAS;;IAErC,SAAS,CAAC,EAAE,MAAM,IAAI,MAAM,QAAO,CAAE;;;AAuBlC,IAAM,iCAAiC;EAC5C;IACE,QAAQ;MACN;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;IAGV,iBAAiB;IACjB,MAAM;;EAER;IACE,QAAQ;MACN;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;IAGV,SAAS;MACP;QACE,MAAM;;;IAGV,iBAAiB;IACjB,MAAM;IACN,MAAM;;;;;AC5SH,IAAM,sBAAsB;;;ACA5B,IAAM,oCACX;AAEK,IAAM,mCACX;AAEK,IAAM,sCACX;;;ACiBF,IAAM,WAAW;AAsGX,SAAU,qBAiBd,YAAmE;AAEnE,QAAM,EAAE,KAAK,MAAM,cAAc,KAAI,IACnC;AAEF,MAAI,UAAU,IAAI,CAAC;AACnB,MAAI,cAAc;AAChB,UAAM,OAAO,WAAW,EAAE,KAAK,MAAM,MAAM,aAAY,CAAE;AACzD,QAAI,CAAC;AAAM,YAAM,IAAI,yBAAyB,cAAc,EAAE,SAAQ,CAAE;AACxE,cAAU;EACZ;AAEA,MAAI,QAAQ,SAAS;AACnB,UAAM,IAAI,yBAAyB,QAAW,EAAE,SAAQ,CAAE;AAC5D,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,gCAAgC,QAAQ,MAAM,EAAE,SAAQ,CAAE;AAEtE,QAAM,SAAS,oBAAoB,QAAQ,SAAS,IAAI;AACxD,MAAI,UAAU,OAAO,SAAS;AAC5B,WAAO;AACT,MAAI,UAAU,OAAO,WAAW;AAC9B,WAAO,OAAO,CAAC;AACjB,SAAO;AACT;;;ACrJA,IAAMC,YAAW;AAgCX,SAAU,iBACd,YAA2C;AAE3C,QAAM,EAAE,KAAK,MAAM,SAAQ,IAAK;AAChC,MAAI,CAAC,QAAQ,KAAK,WAAW;AAAG,WAAO;AAEvC,QAAM,cAAc,IAAI,KAAK,CAAC,MAAM,UAAU,KAAK,EAAE,SAAS,aAAa;AAC3E,MAAI,CAAC;AAAa,UAAM,IAAI,4BAA4B,EAAE,UAAAA,UAAQ,CAAE;AACpE,MAAI,EAAE,YAAY;AAChB,UAAM,IAAI,kCAAkC,EAAE,UAAAA,UAAQ,CAAE;AAC1D,MAAI,CAAC,YAAY,UAAU,YAAY,OAAO,WAAW;AACvD,UAAM,IAAI,kCAAkC,EAAE,UAAAA,UAAQ,CAAE;AAE1D,QAAM,OAAO,oBAAoB,YAAY,QAAQ,IAAI;AACzD,SAAO,UAAU,CAAC,UAAU,IAAK,CAAC;AACpC;;;ACrCA,IAAMC,YAAW;AAyDX,SAAU,0BAId,YAAkE;AAElE,QAAM,EAAE,KAAK,MAAM,aAAY,IAC7B;AAEF,MAAI,UAAU,IAAI,CAAC;AACnB,MAAI,cAAc;AAChB,UAAM,OAAO,WAAW;MACtB;MACA;MACA,MAAM;KACP;AACD,QAAI,CAAC;AAAM,YAAM,IAAI,yBAAyB,cAAc,EAAE,UAAAA,UAAQ,CAAE;AACxE,cAAU;EACZ;AAEA,MAAI,QAAQ,SAAS;AACnB,UAAM,IAAI,yBAAyB,QAAW,EAAE,UAAAA,UAAQ,CAAE;AAE5D,SAAO;IACL,KAAK,CAAC,OAAO;IACb,cAAc,mBAAmB,cAAc,OAAO,CAAC;;AAE3D;;;ACzCM,SAAU,mBAId,YAA2D;AAE3D,QAAM,EAAE,KAAI,IAAK;AAEjB,QAAM,EAAE,KAAK,aAAY,KAAM,MAAK;AAvEtC;AAwEI,QACE,WAAW,IAAI,WAAW,OAC1B,gBAAW,iBAAX,mBAAyB,WAAW;AAEpC,aAAO;AACT,WAAO,0BAA0B,UAAU;EAC7C,GAAE;AAEF,QAAM,UAAU,IAAI,CAAC;AACrB,QAAM,YAAY;AAElB,QAAM,OACJ,YAAY,WAAW,QAAQ,SAC3B,oBAAoB,QAAQ,QAAQ,QAAQ,CAAA,CAAE,IAC9C;AACN,SAAO,UAAU,CAAC,WAAW,QAAQ,IAAI,CAAC;AAC5C;;;ACvFM,SAAU,gBAAa;AAC3B,MAAI,UAAiD,MAAM;AAC3D,MAAI,SAA+C,MAAM;AAEzD,QAAM,UAAU,IAAI,QAAc,CAAC,UAAU,YAAW;AACtD,cAAU;AACV,aAAS;EACX,CAAC;AAED,SAAO,EAAE,SAAS,SAAS,OAAM;AACnC;;;ACqBA,IAAM,iBAA+B,oBAAI,IAAG;AAGtC,SAAU,qBAGd,EACA,IACA,IACA,kBACA,OAAO,GACP,KAAI,GAIL;AACC,QAAM,OAAO,YAAW;AACtB,UAAM,YAAY,aAAY;AAC9B,UAAK;AAEL,UAAM,OAAO,UAAU,IAAI,CAAC,EAAE,MAAAC,MAAI,MAAOA,KAAI;AAE7C,QAAI,KAAK,WAAW;AAAG;AAEvB,OAAG,IAAoB,EACpB,KAAK,CAAC,SAAQ;AACb,UAAI,QAAQ,MAAM,QAAQ,IAAI;AAAG,aAAK,KAAK,IAAI;AAC/C,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAM,EAAE,QAAO,IAAK,UAAU,CAAC;AAC/B,2CAAU,CAAC,KAAK,CAAC,GAAG,IAAI;MAC1B;IACF,CAAC,EACA,MAAM,CAAC,QAAO;AACb,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAM,EAAE,OAAM,IAAK,UAAU,CAAC;AAC9B,yCAAS;MACX;IACF,CAAC;EACL;AAEA,QAAM,QAAQ,MAAM,eAAe,OAAO,EAAE;AAE5C,QAAM,iBAAiB,MACrB,aAAY,EAAG,IAAI,CAAC,EAAE,KAAI,MAAO,IAAI;AAEvC,QAAM,eAAe,MAAM,eAAe,IAAI,EAAE,KAAK,CAAA;AAErD,QAAM,eAAe,CAAC,SACpB,eAAe,IAAI,IAAI,CAAC,GAAG,aAAY,GAAI,IAAI,CAAC;AAElD,SAAO;IACL;IACA,MAAM,SAAS,MAAgB;AAC7B,YAAM,EAAE,SAAS,SAAS,OAAM,IAAK,cAAa;AAElD,YAAM,QAAQ,qDAAmB,CAAC,GAAG,eAAc,GAAI,IAAI;AAE3D,UAAI;AAAO,aAAI;AAEf,YAAM,qBAAqB,aAAY,EAAG,SAAS;AACnD,UAAI,oBAAoB;AACtB,qBAAa,EAAE,MAAM,SAAS,OAAM,CAAE;AACtC,eAAO;MACT;AAEA,mBAAa,EAAE,MAAM,SAAS,OAAM,CAAE;AACtC,iBAAW,MAAM,IAAI;AACrB,aAAO;IACT;;AAEJ;;;ACjFM,SAAU,sBACd,cAA6C;AAE7C,MAAI,CAAC,gBAAgB,aAAa,WAAW;AAAG,WAAO;AACvD,SAAO,aAAa,OAAO,CAAC,KAAK,EAAE,MAAM,MAAK,MAAM;AAClD,QAAI,KAAK,WAAW;AAClB,YAAM,IAAI,wBAAwB;QAChC,MAAM,KAAK;QACX,YAAY;QACZ,MAAM;OACP;AACH,QAAI,MAAM,WAAW;AACnB,YAAM,IAAI,wBAAwB;QAChC,MAAM,MAAM;QACZ,YAAY;QACZ,MAAM;OACP;AACH,QAAI,IAAI,IAAI;AACZ,WAAO;EACT,GAAG,CAAA,CAAqB;AAC1B;AAaM,SAAU,8BACd,YAAmD;AAEnD,QAAM,EAAE,SAAS,OAAO,OAAO,WAAW,KAAI,IAAK;AACnD,QAAM,0BAAmD,CAAA;AACzD,MAAI,SAAS;AAAW,4BAAwB,OAAO;AACvD,MAAI,YAAY;AACd,4BAAwB,UAAU,YAAY,OAAO;AACvD,MAAI,UAAU;AAAW,4BAAwB,QAAQ,YAAY,KAAK;AAC1E,MAAI,UAAU;AACZ,4BAAwB,QAAQ,sBAAsB,KAAK;AAC7D,MAAI,cAAc,QAAW;AAC3B,QAAI,wBAAwB;AAAO,YAAM,IAAI,6BAA4B;AACzE,4BAAwB,YAAY,sBAAsB,SAAS;EACrE;AACA,SAAO;AACT;AAUM,SAAU,uBACd,YAA6C;AAE7C,MAAI,CAAC;AAAY,WAAO;AACxB,QAAM,mBAAqC,CAAA;AAC3C,aAAW,EAAE,SAAS,GAAG,aAAY,KAAM,YAAY;AACrD,QAAI,CAAC,UAAU,SAAS,EAAE,QAAQ,MAAK,CAAE;AACvC,YAAM,IAAI,oBAAoB,EAAE,QAAO,CAAE;AAC3C,QAAI,iBAAiB,OAAO;AAC1B,YAAM,IAAI,0BAA0B,EAAE,QAAgB,CAAE;AAC1D,qBAAiB,OAAO,IAAI,8BAA8B,YAAY;EACxE;AACA,SAAO;AACT;;;ACqDA,eAAsB,KACpB,QACA,MAA2B;AA3J7B;AA6JE,QAAM,EACJ,SAAS,WAAW,OAAO,SAC3B,mBACA,QAAQ,SAAQ,YAAO,UAAP,mBAAc,SAAS,GACvC,aACA,WAAW,UACX,YACA,OACA,gBACA,MACA,MAAM,OACN,SACA,aACA,KACA,UACA,kBACA,cACA,sBACA,OACA,IACA,OACA,eACA,GAAG,KAAI,IACL;AACJ,QAAM,UAAU,WAAW,aAAa,QAAQ,IAAI;AAEpD,MAAI,SAAS,WAAW;AACtB,UAAM,IAAI,UACR,qEAAqE;AAEzE,MAAI,QAAQ;AACV,UAAM,IAAI,UAAU,kDAAkD;AAGxE,QAAM,4BAA4B,QAAQ;AAE1C,QAAM,2BAA2B,WAAW,eAAe,MAAM;AACjE,QAAM,iBAAiB,6BAA6B;AAEpD,QAAM,QAAQ,MAAK;AACjB,QAAI;AACF,aAAO,gCAAgC;QACrC;QACA,MAAM;OACP;AACH,QAAI;AACF,aAAO,+BAA+B;QACpC,MAAM;QACN;QACA;QACA;OACD;AACH,WAAO;EACT,GAAE;AAEF,MAAI;AACF,kBAAc,IAA+B;AAE7C,UAAM,iBACJ,OAAO,gBAAgB,WAAW,YAAY,WAAW,IAAI;AAC/D,UAAM,QAAQ,kBAAkB;AAEhC,UAAM,oBAAoB,iBACPC,OAAM,cAAc,IACnC;AACJ,UAAM,mBAAmB,uBAAuB,aAAa;AAE7D,UAAM,eAAc,wBAAO,UAAP,mBAAc,eAAd,mBAA0B,uBAA1B,mBAA8C;AAClE,UAAM,SAAS,eAAe;AAE9B,UAAM,UAAU,OAAO;;MAErB,GAAG,QAAQ,MAAM,EAAE,QAAQ,YAAW,CAAE;MACxC,MAAM,mCAAS;MACf;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,iBAAiB,SAAY;MACjC;KACqB;AAEvB,QACE,SACA,uBAAuB,EAAE,QAAO,CAAE,KAClC,CAAC,oBACD,CAAC,mBACD;AACA,UAAI;AACF,eAAO,MAAM,kBAAkB,QAAQ;UACrC,GAAG;UACH;UACA;SACgD;MACpD,SAAS,KAAK;AACZ,YACE,EAAE,eAAe,kCACjB,EAAE,eAAe;AAEjB,gBAAM;MACV;IACF;AAEA,UAAM,UAAU,MAAK;AACnB,YAAM,OAAO;QACX;QACA;;AAEF,UAAI,oBAAoB;AACtB,eAAO,CAAC,GAAG,MAAM,kBAAkB,iBAAiB;AACtD,UAAI;AAAkB,eAAO,CAAC,GAAG,MAAM,gBAAgB;AACvD,UAAI;AAAmB,eAAO,CAAC,GAAG,MAAM,CAAA,GAAI,iBAAiB;AAC7D,aAAO;IACT,GAAE;AAEF,UAAM,WAAW,MAAM,OAAO,QAAQ;MACpC,QAAQ;MACR;KACD;AACD,QAAI,aAAa;AAAM,aAAO,EAAE,MAAM,OAAS;AAC/C,WAAO,EAAE,MAAM,SAAQ;EACzB,SAAS,KAAK;AACZ,UAAMC,QAAO,mBAAmB,GAAG;AAGnC,UAAM,EAAE,gBAAAC,iBAAgB,yBAAAC,yBAAuB,IAAK,MAAM,OACxD,oBAAqB;AAEvB,QACE,OAAO,aAAa,UACpBF,SAAA,gBAAAA,MAAM,MAAM,GAAG,SAAQE,4BACvB;AAEA,aAAO,EAAE,MAAM,MAAMD,gBAAe,QAAQ,EAAE,MAAAD,OAAM,GAAE,CAAE,EAAC;AAG3D,QAAI,mBAAkBA,SAAA,gBAAAA,MAAM,MAAM,GAAG,SAAQ;AAC3C,YAAM,IAAI,oCAAoC,EAAE,QAAO,CAAE;AAE3D,UAAM,aAAa,KAAkB;MACnC,GAAG;MACH;MACA,OAAO,OAAO;KACf;EACH;AACF;AAOA,SAAS,uBAAuB,EAAE,QAAO,GAAmC;AAC1E,QAAM,EAAE,MAAM,IAAI,GAAG,SAAQ,IAAK;AAClC,MAAI,CAAC;AAAM,WAAO;AAClB,MAAI,KAAK,WAAW,mBAAmB;AAAG,WAAO;AACjD,MAAI,CAAC;AAAI,WAAO;AAChB,MACE,OAAO,OAAO,QAAQ,EAAE,OAAO,CAAC,MAAM,OAAO,MAAM,WAAW,EAAE,SAAS;AAEzE,WAAO;AACT,SAAO;AACT;AAoBA,eAAe,kBACb,QACA,MAAwC;AA3V1C;AA6VE,QAAM,EAAE,YAAY,MAAM,OAAO,EAAC,IAChC,SAAO,YAAO,UAAP,mBAAc,eAAc,WAAW,OAAO,MAAM,YAAY,CAAA;AACzE,QAAM,EACJ,aACA,WAAW,UACX,MACA,kBAAkB,mBAClB,GAAE,IACA;AAEJ,MAAI,mBAAmB;AACvB,MAAI,CAAC,kBAAkB;AACrB,QAAI,CAAC,OAAO;AAAO,YAAM,IAAI,8BAA6B;AAE1D,uBAAmB,wBAAwB;MACzC;MACA,OAAO,OAAO;MACd,UAAU;KACX;EACH;AAEA,QAAM,iBACJ,OAAO,gBAAgB,WAAW,YAAY,WAAW,IAAI;AAC/D,QAAM,QAAQ,kBAAkB;AAEhC,QAAM,EAAE,SAAQ,IAAK,qBAAqB;IACxC,IAAI,GAAG,OAAO,GAAG,IAAI,KAAK;IAC1B;IACA,iBAAiBG,OAAI;AACnB,YAAMC,QAAOD,MAAK,OAAO,CAACC,OAAM,EAAE,MAAAJ,MAAI,MAAOI,SAAQJ,MAAK,SAAS,IAAI,CAAC;AACxE,aAAOI,QAAO,YAAY;IAC5B;IACA,IAAI,OACF,aAIE;AACF,YAAM,QAAQ,SAAS,IAAI,CAAC,aAAa;QACvC,cAAc;QACd,UAAU,QAAQ;QAClB,QAAQ,QAAQ;QAChB;AAEF,YAAM,WAAW,mBAAmB;QAClC,KAAK;QACL,MAAM,CAAC,KAAK;QACZ,cAAc;OACf;AAED,YAAMJ,QAAO,MAAM,OAAO,QAAQ;QAChC,QAAQ;QACR,QAAQ;UACN;YACE,MAAM;YACN,IAAI;;UAEN;;OAEH;AAED,aAAO,qBAAqB;QAC1B,KAAK;QACL,MAAM,CAAC,KAAK;QACZ,cAAc;QACd,MAAMA,SAAQ;OACf;IACH;GACD;AAED,QAAM,CAAC,EAAE,YAAY,QAAO,CAAE,IAAI,MAAM,SAAS,EAAE,MAAM,GAAE,CAAE;AAE7D,MAAI,CAAC;AAAS,UAAM,IAAI,iBAAiB,EAAE,MAAM,WAAU,CAAE;AAC7D,MAAI,eAAe;AAAM,WAAO,EAAE,MAAM,OAAS;AACjD,SAAO,EAAE,MAAM,WAAU;AAC3B;AAMA,SAAS,gCAAgC,YAGxC;AACC,QAAM,EAAE,MAAM,KAAI,IAAK;AACvB,SAAO,iBAAiB;IACtB,KAAK,SAAS,CAAC,2BAA2B,CAAC;IAC3C,UAAU;IACV,MAAM,CAAC,MAAM,IAAI;GAClB;AACH;AAMA,SAAS,+BAA+B,YAKvC;AACC,QAAM,EAAE,MAAM,SAAS,aAAa,GAAE,IAAK;AAC3C,SAAO,iBAAiB;IACtB,KAAK,SAAS,CAAC,6CAA6C,CAAC;IAC7D,UAAU;IACV,MAAM,CAAC,IAAI,MAAM,SAAS,WAAW;GACtC;AACH;AAMM,SAAU,mBAAmB,KAAY;AAhd/C;AAidE,MAAI,EAAE,eAAe;AAAY,WAAO;AACxC,QAAM,QAAQ,IAAI,KAAI;AACtB,SAAO,QAAO,+BAAO,UAAS,YAAW,WAAM,SAAN,mBAAY,OAAO,MAAM;AACpE;;;ACzcM,IAAO,sBAAP,cAAmC,UAAS;EAChD,YAAY,EACV,kBACA,OACA,MACA,WACA,QACA,KAAI,GAQL;AAvBH;AAwBI,UACE,MAAM,gBACJ,4DACF;MACE;MACA,cAAc;QACZ,GAAI,MAAM,gBAAgB,CAAA;UAC1B,WAAM,iBAAN,mBAAoB,UAAS,KAAK,CAAA;QAClC;QACA,QAAQ;UACN;UACA,GAAG,KAAK,IAAI,CAAC,QAAQ,OAAO,OAAO,GAAG,CAAC,EAAE;;QAE3C,aAAa,MAAM;QACnB,WAAW,IAAI;QACf,wBAAwB,gBAAgB;QACxC,iBAAiB,SAAS;QAC1B,KAAI;MACN,MAAM;KACP;EAEL;;AAOI,IAAO,uCAAP,cAAoD,UAAS;EACjE,YAAY,EAAE,QAAQ,IAAG,GAAgC;AACvD,UACE,8EACA;MACE,cAAc;QACZ,gBAAgB,OAAO,GAAG,CAAC;QAC3B,aAAa,UAAU,MAAM,CAAC;;MAEhC,MAAM;KACP;EAEL;;AAQI,IAAO,oCAAP,cAAiD,UAAS;EAC9D,YAAY,EAAE,QAAQ,GAAE,GAAoC;AAC1D,UACE,0EACA;MACE,cAAc;QACZ,qBAAqB,EAAE;QACvB,kCAAkC,MAAM;;MAE1C,MAAM;KACP;EAEL;;;;AC3EI,SAAU,eAAe,GAAY,GAAU;AACnD,MAAI,CAAC,UAAU,GAAG,EAAE,QAAQ,MAAK,CAAE;AACjC,UAAM,IAAI,oBAAoB,EAAE,SAAS,EAAC,CAAE;AAC9C,MAAI,CAAC,UAAU,GAAG,EAAE,QAAQ,MAAK,CAAE;AACjC,UAAM,IAAI,oBAAoB,EAAE,SAAS,EAAC,CAAE;AAC9C,SAAO,EAAE,YAAW,MAAO,EAAE,YAAW;AAC1C;;;ACqCM,SAAU,mBACd,YAA6C;AAE7C,QAAM,EAAE,KAAK,KAAI,IAAK;AACtB,QAAM,YAAY,MAAM,MAAM,GAAG,CAAC;AAClC,QAAM,cAAc,IAAI,KACtB,CAAC,MACC,EAAE,SAAS,cACX,cAAc,mBAAmB,cAAc,CAAC,CAAC,CAAC;AAEtD,MAAI,CAAC;AACH,UAAM,IAAI,kCAAkC,WAAW;MACrD,UAAU;KACX;AACH,SAAO;IACL,cAAe,YAAiC;IAChD,MAAO,YAAY,eACnB,YAAY,UACZ,YAAY,OAAO,SAAS,IACxB,oBAAoB,YAAY,QAAQ,MAAM,MAAM,CAAC,CAAC,IACtD;;AAER;;;ACnDA,IAAMK,YAAW;AA0CX,SAAU,kBAId,YAAuD;AAEvD,QAAM,EAAE,KAAK,WAAW,KAAI,IAAK;AAEjC,MAAI,UAAU,IAAI,CAAC;AACnB,MAAI,WAAW;AACb,UAAM,OAAO,WAAW,EAAE,KAAK,MAAM,MAAM,UAAS,CAAE;AACtD,QAAI,CAAC;AAAM,YAAM,IAAI,sBAAsB,WAAW,EAAE,UAAAA,UAAQ,CAAE;AAClE,cAAU;EACZ;AAEA,MAAI,QAAQ,SAAS;AACnB,UAAM,IAAI,sBAAsB,QAAW,EAAE,UAAAA,UAAQ,CAAE;AAEzD,QAAM,aAAa,cAAc,OAAO;AACxC,QAAM,YAAY,mBAAmB,UAAU;AAE/C,MAAI,OAAY;AAChB,MAAI,QAAQ,KAAK,SAAS,GAAG;AAC3B,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,4BAA4B,QAAQ,MAAM,EAAE,UAAAA,UAAQ,CAAE;AAClE,WAAO,oBAAoB,QAAQ,QAAQ,IAAI;EACjD;AACA,SAAO,UAAU,CAAC,WAAW,IAAI,CAAC;AACpC;;;AC3EA,IAAMC,YAAW;AA8CX,SAAU,qBAId,YAA6D;AAE7D,QAAM,EAAE,KAAK,cAAc,OAAM,IAC/B;AAEF,MAAI,UAAU,IAAI,CAAC;AACnB,MAAI,cAAc;AAChB,UAAM,OAAO,WAAW,EAAE,KAAK,MAAM,aAAY,CAAE;AACnD,QAAI,CAAC;AAAM,YAAM,IAAI,yBAAyB,cAAc,EAAE,UAAAA,UAAQ,CAAE;AACxE,cAAU;EACZ;AAEA,MAAI,QAAQ,SAAS;AACnB,UAAM,IAAI,yBAAyB,QAAW,EAAE,UAAAA,UAAQ,CAAE;AAE5D,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,gCAAgC,QAAQ,MAAM,EAAE,UAAAA,UAAQ,CAAE;AAEtE,QAAM,UAAU,MAAK;AACnB,QAAI,QAAQ,QAAQ,WAAW;AAAG,aAAO,CAAA;AACzC,QAAI,QAAQ,QAAQ,WAAW;AAAG,aAAO,CAAC,MAAM;AAChD,QAAI,MAAM,QAAQ,MAAM;AAAG,aAAO;AAClC,UAAM,IAAI,kBAAkB,MAAM;EACpC,GAAE;AAEF,SAAO,oBAAoB,QAAQ,SAAS,MAAM;AACpD;;;ACrFO,IAAM,uBAAuB;AAEpC,eAAsB,yBAAyB,YAK9C;AACC,QAAM,EAAE,MAAM,aAAAC,aAAW,IAAK;AAE9B,QAAM,EACJ,MAAM,CAAC,OAAO,EAAC,IACb,mBAAmB,EAAE,KAAK,iBAAiB,KAAI,CAAE;AAErD,QAAM,WAAsB,CAAA;AAC5B,QAAM,YAAmB,CAAA;AACzB,QAAM,QAAQ,IACZ,QAAQ,IAAI,OAAO,OAAO,MAAK;AAC7B,QAAI;AACF,gBAAU,CAAC,IAAI,MAAMA,aAAY,KAAK;AACtC,eAAS,CAAC,IAAI;IAChB,SAAS,KAAK;AACZ,eAAS,CAAC,IAAI;AACd,gBAAU,CAAC,IAAI,YAAY,GAA2B;IACxD;EACF,CAAC,CAAC;AAGJ,SAAO,qBAAqB;IAC1B,KAAK;IACL,cAAc;IACd,QAAQ,CAAC,UAAU,SAAS;GAC7B;AACH;AAEA,SAAS,YAAY,OAA2B;AAC9C,MAAI,MAAM,SAAS,sBAAsB,MAAM;AAC7C,WAAO,kBAAkB;MACvB,KAAK;MACL,WAAW;MACX,MAAM,CAAC,MAAM,QAAQ,MAAM,YAAY;KACxC;AACH,SAAO,kBAAkB;IACvB,KAAK,CAAC,aAAa;IACnB,WAAW;IACX,MAAM,CAAC,kBAAkB,QAAQ,MAAM,eAAe,MAAM,OAAO;GACpE;AACH;;;AC3BO,IAAM,0BAA0B;AAChC,IAAM,wBAAwB;EACnC,MAAM;EACN,MAAM;EACN,QAAQ;IACN;MACE,MAAM;MACN,MAAM;;IAER;MACE,MAAM;MACN,MAAM;;IAER;MACE,MAAM;MACN,MAAM;;IAER;MACE,MAAM;MACN,MAAM;;IAER;MACE,MAAM;MACN,MAAM;;;;AAOZ,eAAsB,eACpB,QACA,EACE,aACA,UACA,MACA,GAAE,GAIH;AAED,QAAM,EAAE,KAAI,IAAK,kBAAkB;IACjC;IACA,KAAK,CAAC,qBAAqB;GAC5B;AACD,QAAM,CAAC,QAAQ,MAAM,UAAU,kBAAkB,SAAS,IAAI;AAE9D,QAAM,EAAE,SAAQ,IAAK;AACrB,QAAM,eACJ,YAAY,QAAO,qCAAU,aAAY,aACrC,SAAS,UACT;AAEN,MAAI;AACF,QAAI,CAAC,eAAe,IAAI,MAAM;AAC5B,YAAM,IAAI,kCAAkC,EAAE,QAAQ,GAAE,CAAE;AAE5D,UAAM,SAAS,KAAK,SAAS,oBAAoB,IAC7C,MAAM,yBAAyB;MAC7B,MAAM;MACN,aAAa;KACd,IACD,MAAM,aAAa,EAAE,MAAM,UAAU,QAAQ,KAAI,CAAE;AAEvD,UAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,QAAQ;MACzC;MACA;MACA,MAAM,OAAO;QACX;QACA,oBACE,CAAC,EAAE,MAAM,QAAO,GAAI,EAAE,MAAM,QAAO,CAAE,GACrC,CAAC,QAAQ,SAAS,CAAC;OAEtB;MACD;KACiB;AAEnB,WAAO;EACT,SAAS,KAAK;AACZ,UAAM,IAAI,oBAAoB;MAC5B;MACA,OAAO;MACP;MACA;MACA;MACA;KACD;EACH;AACF;AAeA,eAAsB,YAAY,EAChC,MACA,QACA,KAAI,GACkB;AA1IxB;AA2IE,MAAI,QAAQ,IAAI,MAAM,4BAA4B;AAElD,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,MAAM,KAAK,CAAC;AAClB,UAAM,SAAS,IAAI,SAAS,QAAQ,IAAI,QAAQ;AAChD,UAAM,OAAO,WAAW,SAAS,EAAE,MAAM,OAAM,IAAK;AACpD,UAAM,UACJ,WAAW,SAAS,EAAE,gBAAgB,mBAAkB,IAAK,CAAA;AAE/D,QAAI;AACF,YAAM,WAAW,MAAM,MACrB,IAAI,QAAQ,YAAY,OAAO,YAAW,CAAE,EAAE,QAAQ,UAAU,IAAI,GACpE;QACE,MAAM,KAAK,UAAU,IAAI;QACzB;QACA;OACD;AAGH,UAAI;AACJ,WACE,cAAS,QAAQ,IAAI,cAAc,MAAnC,mBAAsC,WAAW,qBACjD;AACA,kBAAU,MAAM,SAAS,KAAI,GAAI;MACnC,OAAO;AACL,iBAAU,MAAM,SAAS,KAAI;MAC/B;AAEA,UAAI,CAAC,SAAS,IAAI;AAChB,gBAAQ,IAAI,iBAAiB;UAC3B;UACA,UAAS,iCAAQ,SACb,UAAU,OAAO,KAAK,IACtB,SAAS;UACb,SAAS,SAAS;UAClB,QAAQ,SAAS;UACjB;SACD;AACD;MACF;AAEA,UAAI,CAAC,MAAM,MAAM,GAAG;AAClB,gBAAQ,IAAI,qCAAqC;UAC/C;UACA;SACD;AACD;MACF;AAEA,aAAO;IACT,SAAS,KAAK;AACZ,cAAQ,IAAI,iBAAiB;QAC3B;QACA,SAAU,IAAc;QACxB;OACD;IACH;EACF;AAEA,QAAM;AACR;", "names": ["BaseError", "docs<PERSON><PERSON>", "stringify", "value", "size", "assertSize", "size", "SizeOverflowError", "SliceOffsetOutOfBoundsError", "pad", "SizeExceedsPaddingSizeError", "size", "assertSize", "BaseError", "size", "padRight", "padRight", "size", "BaseError", "BaseError", "size", "encoder", "concat", "assertSize", "size", "fromString", "encoder", "size", "pad", "slice", "size", "BaseError", "size", "BaseError", "stringify", "SizeOverflowError", "BaseError", "SliceOffsetOutOfBoundsError", "size", "SizeExceedsPaddingSizeError", "toRpc", "docs<PERSON><PERSON>", "docs<PERSON><PERSON>", "args", "toRpc", "data", "offchainLookup", "offchainLookupSignature", "args", "size", "docs<PERSON><PERSON>", "docs<PERSON><PERSON>", "ccipRequest"]}