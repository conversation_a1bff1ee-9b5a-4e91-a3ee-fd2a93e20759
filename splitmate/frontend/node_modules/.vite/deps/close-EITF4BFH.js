import {
  svg
} from "./chunk-KAPNL25W.js";
import "./chunk-4CFW2BUT.js";

// node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/close.js
var closeSvg = svg`<svg fill="none" viewBox="0 0 16 16">
  <path
    fill="currentColor"
    fill-rule="evenodd"
    d="M2.54 2.54a1 1 0 0 1 1.42 0L8 6.6l4.04-4.05a1 1 0 1 1 1.42 1.42L9.4 8l4.05 4.04a1 1 0 0 1-1.42 1.42L8 9.4l-4.04 4.05a1 1 0 0 1-1.42-1.42L6.6 8 2.54 3.96a1 1 0 0 1 0-1.42Z"
    clip-rule="evenodd"
  />
</svg>`;
export {
  closeSvg
};
//# sourceMappingURL=close-EITF4BFH.js.map
