import {
  CaipNetworksUtil,
  ConstantsUtil as ConstantsUtil3,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  HelpersUtil,
  LoggerUtil,
  PresetsUtil,
  Provider<PERSON>til,
  WalletUtil
} from "./chunk-LTYM4H7V.js";
import {
  Account<PERSON>ontroller,
  AlertController,
  ApiController,
  AssetUtil,
  BlockchainApiController,
  ChainController,
  ConnectionController,
  ConnectorController,
  ConstantsUtil,
  ConstantsUtil2,
  CoreHelperUtil,
  EnsController,
  EventsController,
  ModalController,
  NetworkUtil,
  OnRampController,
  OptionsController,
  ParseUtil,
  PublicStateController,
  RouterController,
  SIWXUtil,
  SendController,
  SnackController,
  StorageUtil,
  ThemeController,
  setColorTheme,
  setThemeVariables
} from "./chunk-WB3F7RHB.js";
import {
  A,
  C,
  E,
  IEvents,
  Po,
  Qe,
  Qo,
  concat,
  detect,
  esm_default,
  esm_exports,
  f,
  f2,
  formatJsonRpcError,
  formatJsonRpcRequest,
  formatJsonRpcResult,
  fromString,
  getBigIntRpcId,
  h,
  i,
  import_pino,
  isJsonRpcError,
  isJsonRpcRequest,
  isJsonRpcResponse,
  isJsonRpcResult,
  k,
  o,
  payloadId,
  r,
  require_cjs,
  require_cjs2,
  require_cjs3,
  safeJsonParse,
  safeJsonStringify,
  sn,
  toString,
  y
} from "./chunk-Z7G2EXAU.js";
import {
  HashMD
} from "./chunk-R3RRKMVU.js";
import {
  LruMap,
  checksumAddress,
  defineFormatter,
  hexToBigInt,
  hexToNumber,
  isHex,
  keccak256,
  keccak_256,
  numberToHex,
  toHex as toHex2
} from "./chunk-ZLP42NMG.js";
import {
  rotl,
  wrapConstructor
} from "./chunk-AKZ6HF36.js";
import "./chunk-KAPNL25W.js";
import "./chunk-DLPCIHFH.js";
import "./chunk-YH4ABYEH.js";
import "./chunk-632RNAPF.js";
import "./chunk-XNL5SN4J.js";
import "./chunk-3VXV4F7X.js";
import "./chunk-S3WBF25I.js";
import "./chunk-TC6LULPN.js";
import "./chunk-ELGFDWOT.js";
import {
  toHex
} from "./chunk-DS754V24.js";
import "./chunk-FJXQD5XT.js";
import "./chunk-JXRJCF63.js";
import "./chunk-ZDCKSDER.js";
import "./chunk-6XYBD7FP.js";
import "./chunk-OXML7QZZ.js";
import {
  require_events
} from "./chunk-UW7JWTTR.js";
import {
  __toESM
} from "./chunk-4CFW2BUT.js";

// node_modules/@reown/appkit/node_modules/@walletconnect/core/dist/index.es.js
var import_events3 = __toESM(require_events());

// node_modules/@reown/appkit/node_modules/@walletconnect/types/dist/index.es.js
var import_events2 = __toESM(require_events());
var a = Object.defineProperty;
var u = (e, s, r2) => s in e ? a(e, s, { enumerable: true, configurable: true, writable: true, value: r2 }) : e[s] = r2;
var c = (e, s, r2) => u(e, typeof s != "symbol" ? s + "" : s, r2);
var h2 = class extends IEvents {
  constructor(s) {
    super(), this.opts = s, c(this, "protocol", "wc"), c(this, "version", 2);
  }
};
var p = Object.defineProperty;
var b = (e, s, r2) => s in e ? p(e, s, { enumerable: true, configurable: true, writable: true, value: r2 }) : e[s] = r2;
var v = (e, s, r2) => b(e, typeof s != "symbol" ? s + "" : s, r2);
var I = class extends IEvents {
  constructor(s, r2) {
    super(), this.core = s, this.logger = r2, v(this, "records", /* @__PURE__ */ new Map());
  }
};
var y2 = class {
  constructor(s, r2) {
    this.logger = s, this.core = r2;
  }
};
var m = class extends IEvents {
  constructor(s, r2) {
    super(), this.relayer = s, this.logger = r2;
  }
};
var d = class extends IEvents {
  constructor(s) {
    super();
  }
};
var f3 = class {
  constructor(s, r2, t, q3) {
    this.core = s, this.logger = r2, this.name = t;
  }
};
var P = class extends IEvents {
  constructor(s, r2) {
    super(), this.relayer = s, this.logger = r2;
  }
};
var S = class extends IEvents {
  constructor(s, r2) {
    super(), this.core = s, this.logger = r2;
  }
};
var M = class {
  constructor(s, r2, t) {
    this.core = s, this.logger = r2, this.store = t;
  }
};
var O = class {
  constructor(s, r2) {
    this.projectId = s, this.logger = r2;
  }
};
var R = class {
  constructor(s, r2, t) {
    this.core = s, this.logger = r2, this.telemetryEnabled = t;
  }
};
var T = Object.defineProperty;
var k2 = (e, s, r2) => s in e ? T(e, s, { enumerable: true, configurable: true, writable: true, value: r2 }) : e[s] = r2;
var i2 = (e, s, r2) => k2(e, typeof s != "symbol" ? s + "" : s, r2);
var J = class {
  constructor(s) {
    this.opts = s, i2(this, "protocol", "wc"), i2(this, "version", 2);
  }
};
var V = class {
  constructor(s) {
    this.client = s;
  }
};

// node_modules/@reown/appkit/node_modules/@walletconnect/core/dist/index.es.js
var import_time2 = __toESM(require_cjs());

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/dist/index.es.js
var import_time = __toESM(require_cjs());
var import_window_getters = __toESM(require_cjs2());
var import_window_metadata = __toESM(require_cjs3());

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/_esm/accounts/utils/publicKeyToAddress.js
function publicKeyToAddress(publicKey) {
  const address = keccak256(`0x${publicKey.substring(4)}`).substring(26);
  return checksumAddress(`0x${address}`);
}

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/signature/recoverPublicKey.js
async function recoverPublicKey({ hash, signature }) {
  const hashHex = isHex(hash) ? hash : toHex2(hash);
  const { secp256k1: secp256k12 } = await import("./secp256k1-PNI5P4L2.js");
  const signature_ = (() => {
    if (typeof signature === "object" && "r" in signature && "s" in signature) {
      const { r: r2, s, v: v4, yParity } = signature;
      const yParityOrV2 = Number(yParity ?? v4);
      const recoveryBit2 = toRecoveryBit(yParityOrV2);
      return new secp256k12.Signature(hexToBigInt(r2), hexToBigInt(s)).addRecoveryBit(recoveryBit2);
    }
    const signatureHex = isHex(signature) ? signature : toHex2(signature);
    const yParityOrV = hexToNumber(`0x${signatureHex.slice(130)}`);
    const recoveryBit = toRecoveryBit(yParityOrV);
    return secp256k12.Signature.fromCompact(signatureHex.substring(2, 130)).addRecoveryBit(recoveryBit);
  })();
  const publicKey = signature_.recoverPublicKey(hashHex.substring(2)).toHex(false);
  return `0x${publicKey}`;
}
function toRecoveryBit(yParityOrV) {
  if (yParityOrV === 0 || yParityOrV === 1)
    return yParityOrV;
  if (yParityOrV === 27)
    return 0;
  if (yParityOrV === 28)
    return 1;
  throw new Error("Invalid yParityOrV value");
}

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/signature/recoverAddress.js
async function recoverAddress({ hash, signature }) {
  return publicKeyToAddress(await recoverPublicKey({ hash, signature }));
}

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/formatters/transaction.js
var transactionType = {
  "0x0": "legacy",
  "0x1": "eip2930",
  "0x2": "eip1559",
  "0x3": "eip4844",
  "0x4": "eip7702"
};
function formatTransaction(transaction) {
  const transaction_ = {
    ...transaction,
    blockHash: transaction.blockHash ? transaction.blockHash : null,
    blockNumber: transaction.blockNumber ? BigInt(transaction.blockNumber) : null,
    chainId: transaction.chainId ? hexToNumber(transaction.chainId) : void 0,
    gas: transaction.gas ? BigInt(transaction.gas) : void 0,
    gasPrice: transaction.gasPrice ? BigInt(transaction.gasPrice) : void 0,
    maxFeePerBlobGas: transaction.maxFeePerBlobGas ? BigInt(transaction.maxFeePerBlobGas) : void 0,
    maxFeePerGas: transaction.maxFeePerGas ? BigInt(transaction.maxFeePerGas) : void 0,
    maxPriorityFeePerGas: transaction.maxPriorityFeePerGas ? BigInt(transaction.maxPriorityFeePerGas) : void 0,
    nonce: transaction.nonce ? hexToNumber(transaction.nonce) : void 0,
    to: transaction.to ? transaction.to : null,
    transactionIndex: transaction.transactionIndex ? Number(transaction.transactionIndex) : null,
    type: transaction.type ? transactionType[transaction.type] : void 0,
    typeHex: transaction.type ? transaction.type : void 0,
    value: transaction.value ? BigInt(transaction.value) : void 0,
    v: transaction.v ? BigInt(transaction.v) : void 0
  };
  if (transaction.authorizationList)
    transaction_.authorizationList = formatAuthorizationList(transaction.authorizationList);
  transaction_.yParity = (() => {
    if (transaction.yParity)
      return Number(transaction.yParity);
    if (typeof transaction_.v === "bigint") {
      if (transaction_.v === 0n || transaction_.v === 27n)
        return 0;
      if (transaction_.v === 1n || transaction_.v === 28n)
        return 1;
      if (transaction_.v >= 35n)
        return transaction_.v % 2n === 0n ? 1 : 0;
    }
    return void 0;
  })();
  if (transaction_.type === "legacy") {
    delete transaction_.accessList;
    delete transaction_.maxFeePerBlobGas;
    delete transaction_.maxFeePerGas;
    delete transaction_.maxPriorityFeePerGas;
    delete transaction_.yParity;
  }
  if (transaction_.type === "eip2930") {
    delete transaction_.maxFeePerBlobGas;
    delete transaction_.maxFeePerGas;
    delete transaction_.maxPriorityFeePerGas;
  }
  if (transaction_.type === "eip1559") {
    delete transaction_.maxFeePerBlobGas;
  }
  return transaction_;
}
var defineTransaction = defineFormatter("transaction", formatTransaction);
function formatAuthorizationList(authorizationList) {
  return authorizationList.map((authorization) => ({
    contractAddress: authorization.address,
    chainId: Number(authorization.chainId),
    nonce: Number(authorization.nonce),
    r: authorization.r,
    s: authorization.s,
    yParity: Number(authorization.yParity)
  }));
}

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/formatters/block.js
function formatBlock(block) {
  const transactions = (block.transactions ?? []).map((transaction) => {
    if (typeof transaction === "string")
      return transaction;
    return formatTransaction(transaction);
  });
  return {
    ...block,
    baseFeePerGas: block.baseFeePerGas ? BigInt(block.baseFeePerGas) : null,
    blobGasUsed: block.blobGasUsed ? BigInt(block.blobGasUsed) : void 0,
    difficulty: block.difficulty ? BigInt(block.difficulty) : void 0,
    excessBlobGas: block.excessBlobGas ? BigInt(block.excessBlobGas) : void 0,
    gasLimit: block.gasLimit ? BigInt(block.gasLimit) : void 0,
    gasUsed: block.gasUsed ? BigInt(block.gasUsed) : void 0,
    hash: block.hash ? block.hash : null,
    logsBloom: block.logsBloom ? block.logsBloom : null,
    nonce: block.nonce ? block.nonce : null,
    number: block.number ? BigInt(block.number) : null,
    size: block.size ? BigInt(block.size) : void 0,
    timestamp: block.timestamp ? BigInt(block.timestamp) : void 0,
    transactions,
    totalDifficulty: block.totalDifficulty ? BigInt(block.totalDifficulty) : null
  };
}
var defineBlock = defineFormatter("block", formatBlock);

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/_esm/actions/public/getTransactionCount.js
async function getTransactionCount(client, { address, blockTag = "latest", blockNumber }) {
  const count = await client.request({
    method: "eth_getTransactionCount",
    params: [address, blockNumber ? numberToHex(blockNumber) : blockTag]
  }, { dedupe: Boolean(blockNumber) });
  return hexToNumber(count);
}

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/_esm/constants/blob.js
var blobsPerTransaction = 6;
var bytesPerFieldElement = 32;
var fieldElementsPerBlob = 4096;
var bytesPerBlob = bytesPerFieldElement * fieldElementsPerBlob;
var maxBytesPerTransaction = bytesPerBlob * blobsPerTransaction - // terminator byte (0x80).
1 - // zero byte (0x00) appended to each field element.
1 * fieldElementsPerBlob * blobsPerTransaction;

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/formatters/log.js
function formatLog(log, { args, eventName } = {}) {
  return {
    ...log,
    blockHash: log.blockHash ? log.blockHash : null,
    blockNumber: log.blockNumber ? BigInt(log.blockNumber) : null,
    logIndex: log.logIndex ? Number(log.logIndex) : null,
    transactionHash: log.transactionHash ? log.transactionHash : null,
    transactionIndex: log.transactionIndex ? Number(log.transactionIndex) : null,
    ...eventName ? { args, eventName } : {}
  };
}

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/_esm/actions/wallet/sendTransaction.js
var supportsWalletNamespace = new LruMap(128);

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/promise/withDedupe.js
var promiseCache = new LruMap(8192);

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/rpc/id.js
function createIdStore() {
  return {
    current: 0,
    take() {
      return this.current++;
    },
    reset() {
      this.current = 0;
    }
  };
}
var idCache = createIdStore();

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/formatters/transactionReceipt.js
var receiptStatuses = {
  "0x0": "reverted",
  "0x1": "success"
};
function formatTransactionReceipt(transactionReceipt) {
  const receipt = {
    ...transactionReceipt,
    blockNumber: transactionReceipt.blockNumber ? BigInt(transactionReceipt.blockNumber) : null,
    contractAddress: transactionReceipt.contractAddress ? transactionReceipt.contractAddress : null,
    cumulativeGasUsed: transactionReceipt.cumulativeGasUsed ? BigInt(transactionReceipt.cumulativeGasUsed) : null,
    effectiveGasPrice: transactionReceipt.effectiveGasPrice ? BigInt(transactionReceipt.effectiveGasPrice) : null,
    gasUsed: transactionReceipt.gasUsed ? BigInt(transactionReceipt.gasUsed) : null,
    logs: transactionReceipt.logs ? transactionReceipt.logs.map((log) => formatLog(log)) : null,
    to: transactionReceipt.to ? transactionReceipt.to : null,
    transactionIndex: transactionReceipt.transactionIndex ? hexToNumber(transactionReceipt.transactionIndex) : null,
    status: transactionReceipt.status ? receiptStatuses[transactionReceipt.status] : null,
    type: transactionReceipt.type ? transactionType[transactionReceipt.type] || transactionReceipt.type : null
  };
  if (transactionReceipt.blobGasPrice)
    receipt.blobGasPrice = BigInt(transactionReceipt.blobGasPrice);
  if (transactionReceipt.blobGasUsed)
    receipt.blobGasUsed = BigInt(transactionReceipt.blobGasUsed);
  return receipt;
}
var defineTransactionReceipt = defineFormatter("transactionReceipt", formatTransactionReceipt);

// node_modules/@reown/appkit/node_modules/@noble/hashes/esm/ripemd160.js
var Rho = new Uint8Array([7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8]);
var Id = new Uint8Array(new Array(16).fill(0).map((_, i3) => i3));
var Pi = Id.map((i3) => (9 * i3 + 5) % 16);
var idxL = [Id];
var idxR = [Pi];
for (let i3 = 0; i3 < 4; i3++)
  for (let j2 of [idxL, idxR])
    j2.push(j2[i3].map((k5) => Rho[k5]));
var shifts = [
  [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8],
  [12, 13, 11, 15, 6, 9, 9, 7, 12, 15, 11, 13, 7, 8, 7, 7],
  [13, 15, 14, 11, 7, 7, 6, 8, 13, 14, 13, 12, 5, 5, 6, 9],
  [14, 11, 12, 14, 8, 6, 5, 5, 15, 12, 15, 14, 9, 9, 8, 6],
  [15, 12, 13, 13, 9, 5, 8, 6, 14, 11, 12, 11, 8, 6, 5, 5]
].map((i3) => new Uint8Array(i3));
var shiftsL = idxL.map((idx, i3) => idx.map((j2) => shifts[i3][j2]));
var shiftsR = idxR.map((idx, i3) => idx.map((j2) => shifts[i3][j2]));
var Kl = new Uint32Array([
  0,
  1518500249,
  1859775393,
  2400959708,
  2840853838
]);
var Kr = new Uint32Array([
  1352829926,
  1548603684,
  1836072691,
  2053994217,
  0
]);
function f4(group, x2, y5, z2) {
  if (group === 0)
    return x2 ^ y5 ^ z2;
  else if (group === 1)
    return x2 & y5 | ~x2 & z2;
  else if (group === 2)
    return (x2 | ~y5) ^ z2;
  else if (group === 3)
    return x2 & z2 | y5 & ~z2;
  else
    return x2 ^ (y5 | ~z2);
}
var R_BUF = new Uint32Array(16);
var RIPEMD160 = class extends HashMD {
  constructor() {
    super(64, 20, 8, true);
    this.h0 = 1732584193 | 0;
    this.h1 = 4023233417 | 0;
    this.h2 = 2562383102 | 0;
    this.h3 = 271733878 | 0;
    this.h4 = 3285377520 | 0;
  }
  get() {
    const { h0, h1, h2: h22, h3: h32, h4 } = this;
    return [h0, h1, h22, h32, h4];
  }
  set(h0, h1, h22, h32, h4) {
    this.h0 = h0 | 0;
    this.h1 = h1 | 0;
    this.h2 = h22 | 0;
    this.h3 = h32 | 0;
    this.h4 = h4 | 0;
  }
  process(view, offset) {
    for (let i3 = 0; i3 < 16; i3++, offset += 4)
      R_BUF[i3] = view.getUint32(offset, true);
    let al = this.h0 | 0, ar3 = al, bl = this.h1 | 0, br3 = bl, cl = this.h2 | 0, cr3 = cl, dl = this.h3 | 0, dr3 = dl, el = this.h4 | 0, er3 = el;
    for (let group = 0; group < 5; group++) {
      const rGroup = 4 - group;
      const hbl = Kl[group], hbr = Kr[group];
      const rl = idxL[group], rr3 = idxR[group];
      const sl = shiftsL[group], sr3 = shiftsR[group];
      for (let i3 = 0; i3 < 16; i3++) {
        const tl = rotl(al + f4(group, bl, cl, dl) + R_BUF[rl[i3]] + hbl, sl[i3]) + el | 0;
        al = el, el = dl, dl = rotl(cl, 10) | 0, cl = bl, bl = tl;
      }
      for (let i3 = 0; i3 < 16; i3++) {
        const tr3 = rotl(ar3 + f4(rGroup, br3, cr3, dr3) + R_BUF[rr3[i3]] + hbr, sr3[i3]) + er3 | 0;
        ar3 = er3, er3 = dr3, dr3 = rotl(cr3, 10) | 0, cr3 = br3, br3 = tr3;
      }
    }
    this.set(this.h1 + cl + dr3 | 0, this.h2 + dl + er3 | 0, this.h3 + el + ar3 | 0, this.h4 + al + br3 | 0, this.h0 + bl + cr3 | 0);
  }
  roundClean() {
    R_BUF.fill(0);
  }
  destroy() {
    this.destroyed = true;
    this.buffer.fill(0);
    this.set(0, 0, 0, 0, 0);
  }
};
var ripemd160 = wrapConstructor(() => new RIPEMD160());

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/nonceManager.js
function createNonceManager(parameters) {
  const { source } = parameters;
  const deltaMap = /* @__PURE__ */ new Map();
  const nonceMap = new LruMap(8192);
  const promiseMap = /* @__PURE__ */ new Map();
  const getKey = ({ address, chainId }) => `${address}.${chainId}`;
  return {
    async consume({ address, chainId, client }) {
      const key = getKey({ address, chainId });
      const promise = this.get({ address, chainId, client });
      this.increment({ address, chainId });
      const nonce = await promise;
      await source.set({ address, chainId }, nonce);
      nonceMap.set(key, nonce);
      return nonce;
    },
    async increment({ address, chainId }) {
      const key = getKey({ address, chainId });
      const delta = deltaMap.get(key) ?? 0;
      deltaMap.set(key, delta + 1);
    },
    async get({ address, chainId, client }) {
      const key = getKey({ address, chainId });
      let promise = promiseMap.get(key);
      if (!promise) {
        promise = (async () => {
          try {
            const nonce = await source.get({ address, chainId, client });
            const previousNonce = nonceMap.get(key) ?? 0;
            if (previousNonce > 0 && nonce <= previousNonce)
              return previousNonce + 1;
            nonceMap.delete(key);
            return nonce;
          } finally {
            this.reset({ address, chainId });
          }
        })();
        promiseMap.set(key, promise);
      }
      const delta = deltaMap.get(key) ?? 0;
      return delta + await promise;
    },
    reset({ address, chainId }) {
      const key = getKey({ address, chainId });
      deltaMap.delete(key);
      promiseMap.delete(key);
    }
  };
}
function jsonRpc() {
  return {
    async get(parameters) {
      const { address, client } = parameters;
      return getTransactionCount(client, {
        address,
        blockTag: "pending"
      });
    },
    set() {
    }
  };
}
var nonceManager = createNonceManager({
  source: jsonRpc()
});

// node_modules/@reown/appkit/node_modules/ox/_esm/core/version.js
var version = "0.1.1";

// node_modules/@reown/appkit/node_modules/ox/_esm/core/internal/errors.js
function getVersion() {
  return version;
}

// node_modules/@reown/appkit/node_modules/ox/_esm/core/Errors.js
var BaseError2 = class _BaseError extends Error {
  constructor(shortMessage, options = {}) {
    const details = (() => {
      var _a;
      if (options.cause instanceof _BaseError) {
        if (options.cause.details)
          return options.cause.details;
        if (options.cause.shortMessage)
          return options.cause.shortMessage;
      }
      if ((_a = options.cause) == null ? void 0 : _a.message)
        return options.cause.message;
      return options.details;
    })();
    const docsPath = (() => {
      if (options.cause instanceof _BaseError)
        return options.cause.docsPath || options.docsPath;
      return options.docsPath;
    })();
    const docsBaseUrl = "https://oxlib.sh";
    const docs = `${docsBaseUrl}${docsPath ?? ""}`;
    const message = [
      shortMessage || "An error occurred.",
      ...options.metaMessages ? ["", ...options.metaMessages] : [],
      ...details || docsPath ? [
        "",
        details ? `Details: ${details}` : void 0,
        docsPath ? `See: ${docs}` : void 0
      ] : []
    ].filter((x2) => typeof x2 === "string").join("\n");
    super(message, options.cause ? { cause: options.cause } : void 0);
    Object.defineProperty(this, "details", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "docs", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "docsPath", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "shortMessage", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "cause", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "BaseError"
    });
    Object.defineProperty(this, "version", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: `ox@${getVersion()}`
    });
    this.cause = options.cause;
    this.details = details;
    this.docs = docs;
    this.docsPath = docsPath;
    this.shortMessage = shortMessage;
  }
  walk(fn3) {
    return walk(this, fn3);
  }
};
function walk(err, fn3) {
  if (fn3 == null ? void 0 : fn3(err))
    return err;
  if (err && typeof err === "object" && "cause" in err && err.cause)
    return walk(err.cause, fn3);
  return fn3 ? null : err;
}

// node_modules/@reown/appkit/node_modules/ox/_esm/core/internal/bytes.js
function assertSize(bytes, size_) {
  if (size2(bytes) > size_)
    throw new SizeOverflowError({
      givenSize: size2(bytes),
      maxSize: size_
    });
}
var charCodeMap = {
  zero: 48,
  nine: 57,
  A: 65,
  F: 70,
  a: 97,
  f: 102
};
function charCodeToBase16(char) {
  if (char >= charCodeMap.zero && char <= charCodeMap.nine)
    return char - charCodeMap.zero;
  if (char >= charCodeMap.A && char <= charCodeMap.F)
    return char - (charCodeMap.A - 10);
  if (char >= charCodeMap.a && char <= charCodeMap.f)
    return char - (charCodeMap.a - 10);
  return void 0;
}
function pad2(bytes, options = {}) {
  const { dir, size: size4 = 32 } = options;
  if (size4 === 0)
    return bytes;
  if (bytes.length > size4)
    throw new SizeExceedsPaddingSizeError({
      size: bytes.length,
      targetSize: size4,
      type: "Bytes"
    });
  const paddedBytes = new Uint8Array(size4);
  for (let i3 = 0; i3 < size4; i3++) {
    const padEnd = dir === "right";
    paddedBytes[padEnd ? i3 : size4 - i3 - 1] = bytes[padEnd ? i3 : bytes.length - i3 - 1];
  }
  return paddedBytes;
}

// node_modules/@reown/appkit/node_modules/ox/_esm/core/internal/hex.js
function assertSize2(hex, size_) {
  if (size3(hex) > size_)
    throw new SizeOverflowError2({
      givenSize: size3(hex),
      maxSize: size_
    });
}
function pad3(hex_, options = {}) {
  const { dir, size: size4 = 32 } = options;
  if (size4 === 0)
    return hex_;
  const hex = hex_.replace("0x", "");
  if (hex.length > size4 * 2)
    throw new SizeExceedsPaddingSizeError2({
      size: Math.ceil(hex.length / 2),
      targetSize: size4,
      type: "Hex"
    });
  return `0x${hex[dir === "right" ? "padEnd" : "padStart"](size4 * 2, "0")}`;
}

// node_modules/@reown/appkit/node_modules/ox/_esm/core/Bytes.js
var decoder = new TextDecoder();
var encoder = new TextEncoder();
function from(value) {
  if (value instanceof Uint8Array)
    return value;
  if (typeof value === "string")
    return fromHex2(value);
  return fromArray(value);
}
function fromArray(value) {
  return value instanceof Uint8Array ? value : new Uint8Array(value);
}
function fromHex2(value, options = {}) {
  const { size: size4 } = options;
  let hex = value;
  if (size4) {
    assertSize2(value, size4);
    hex = padRight(value, size4);
  }
  let hexString = hex.slice(2);
  if (hexString.length % 2)
    hexString = `0${hexString}`;
  const length = hexString.length / 2;
  const bytes = new Uint8Array(length);
  for (let index = 0, j2 = 0; index < length; index++) {
    const nibbleLeft = charCodeToBase16(hexString.charCodeAt(j2++));
    const nibbleRight = charCodeToBase16(hexString.charCodeAt(j2++));
    if (nibbleLeft === void 0 || nibbleRight === void 0) {
      throw new BaseError2(`Invalid byte sequence ("${hexString[j2 - 2]}${hexString[j2 - 1]}" in "${hexString}").`);
    }
    bytes[index] = nibbleLeft * 16 + nibbleRight;
  }
  return bytes;
}
function fromString2(value, options = {}) {
  const { size: size4 } = options;
  const bytes = encoder.encode(value);
  if (typeof size4 === "number") {
    assertSize(bytes, size4);
    return padRight2(bytes, size4);
  }
  return bytes;
}
function padRight2(value, size4) {
  return pad2(value, { dir: "right", size: size4 });
}
function size2(value) {
  return value.length;
}
var SizeOverflowError = class extends BaseError2 {
  constructor({ givenSize, maxSize }) {
    super(`Size cannot exceed \`${maxSize}\` bytes. Given size: \`${givenSize}\` bytes.`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Bytes.SizeOverflowError"
    });
  }
};
var SizeExceedsPaddingSizeError = class extends BaseError2 {
  constructor({ size: size4, targetSize, type }) {
    super(`${type.charAt(0).toUpperCase()}${type.slice(1).toLowerCase()} size (\`${size4}\`) exceeds padding size (\`${targetSize}\`).`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Bytes.SizeExceedsPaddingSizeError"
    });
  }
};

// node_modules/@reown/appkit/node_modules/ox/_esm/core/Hex.js
var encoder2 = new TextEncoder();
var hexes = Array.from({ length: 256 }, (_v, i3) => i3.toString(16).padStart(2, "0"));
function concat3(...values) {
  return `0x${values.reduce((acc, x2) => acc + x2.replace("0x", ""), "")}`;
}
function fromBoolean(value, options = {}) {
  const hex = `0x${Number(value)}`;
  if (typeof options.size === "number") {
    assertSize2(hex, options.size);
    return padLeft(hex, options.size);
  }
  return hex;
}
function fromBytes2(value, options = {}) {
  let string = "";
  for (let i3 = 0; i3 < value.length; i3++)
    string += hexes[value[i3]];
  const hex = `0x${string}`;
  if (typeof options.size === "number") {
    assertSize2(hex, options.size);
    return padRight(hex, options.size);
  }
  return hex;
}
function fromNumber(value, options = {}) {
  const { signed, size: size4 } = options;
  const value_ = BigInt(value);
  let maxValue;
  if (size4) {
    if (signed)
      maxValue = (1n << BigInt(size4) * 8n - 1n) - 1n;
    else
      maxValue = 2n ** (BigInt(size4) * 8n) - 1n;
  } else if (typeof value === "number") {
    maxValue = BigInt(Number.MAX_SAFE_INTEGER);
  }
  const minValue = typeof maxValue === "bigint" && signed ? -maxValue - 1n : 0;
  if (maxValue && value_ > maxValue || value_ < minValue) {
    const suffix = typeof value === "bigint" ? "n" : "";
    throw new IntegerOutOfRangeError({
      max: maxValue ? `${maxValue}${suffix}` : void 0,
      min: `${minValue}${suffix}`,
      signed,
      size: size4,
      value: `${value}${suffix}`
    });
  }
  const stringValue = (signed && value_ < 0 ? (1n << BigInt(size4 * 8)) + BigInt(value_) : value_).toString(16);
  const hex = `0x${stringValue}`;
  if (size4)
    return padLeft(hex, size4);
  return hex;
}
function fromString3(value, options = {}) {
  return fromBytes2(encoder2.encode(value), options);
}
function padLeft(value, size4) {
  return pad3(value, { dir: "left", size: size4 });
}
function padRight(value, size4) {
  return pad3(value, { dir: "right", size: size4 });
}
function size3(value) {
  return Math.ceil((value.length - 2) / 2);
}
var IntegerOutOfRangeError = class extends BaseError2 {
  constructor({ max, min, signed, size: size4, value }) {
    super(`Number \`${value}\` is not in safe${size4 ? ` ${size4 * 8}-bit` : ""}${signed ? " signed" : " unsigned"} integer range ${max ? `(\`${min}\` to \`${max}\`)` : `(above \`${min}\`)`}`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Hex.IntegerOutOfRangeError"
    });
  }
};
var SizeOverflowError2 = class extends BaseError2 {
  constructor({ givenSize, maxSize }) {
    super(`Size cannot exceed \`${maxSize}\` bytes. Given size: \`${givenSize}\` bytes.`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Hex.SizeOverflowError"
    });
  }
};
var SizeExceedsPaddingSizeError2 = class extends BaseError2 {
  constructor({ size: size4, targetSize, type }) {
    super(`${type.charAt(0).toUpperCase()}${type.slice(1).toLowerCase()} size (\`${size4}\`) exceeds padding size (\`${targetSize}\`).`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Hex.SizeExceedsPaddingSizeError"
    });
  }
};

// node_modules/@reown/appkit/node_modules/ox/_esm/core/Hash.js
function keccak2562(value, options = {}) {
  const { as = typeof value === "string" ? "Hex" : "Bytes" } = options;
  const bytes = keccak_256(from(value));
  if (as === "Bytes")
    return bytes;
  return fromBytes2(bytes);
}

// node_modules/@reown/appkit/node_modules/ox/_esm/core/internal/lru.js
var LruMap2 = class extends Map {
  constructor(size4) {
    super();
    Object.defineProperty(this, "maxSize", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    this.maxSize = size4;
  }
  get(key) {
    const value = super.get(key);
    if (super.has(key) && value !== void 0) {
      this.delete(key);
      super.set(key, value);
    }
    return value;
  }
  set(key, value) {
    super.set(key, value);
    if (this.maxSize && this.size > this.maxSize) {
      const firstKey = this.keys().next().value;
      if (firstKey)
        this.delete(firstKey);
    }
    return this;
  }
};

// node_modules/@reown/appkit/node_modules/ox/_esm/core/Caches.js
var caches = {
  checksum: new LruMap2(8192)
};
var checksum = caches.checksum;

// node_modules/@reown/appkit/node_modules/ox/_esm/core/Address.js
var addressRegex = /^0x[a-fA-F0-9]{40}$/;
function assert(value, options = {}) {
  const { strict = true } = options;
  if (!addressRegex.test(value))
    throw new InvalidAddressError2({
      address: value,
      cause: new InvalidInputError()
    });
  if (strict) {
    if (value.toLowerCase() === value)
      return;
    if (checksum2(value) !== value)
      throw new InvalidAddressError2({
        address: value,
        cause: new InvalidChecksumError()
      });
  }
}
function checksum2(address) {
  if (checksum.has(address))
    return checksum.get(address);
  assert(address, { strict: false });
  const hexAddress = address.substring(2).toLowerCase();
  const hash = keccak2562(fromString2(hexAddress), { as: "Bytes" });
  const characters = hexAddress.split("");
  for (let i3 = 0; i3 < 40; i3 += 2) {
    if (hash[i3 >> 1] >> 4 >= 8 && characters[i3]) {
      characters[i3] = characters[i3].toUpperCase();
    }
    if ((hash[i3 >> 1] & 15) >= 8 && characters[i3 + 1]) {
      characters[i3 + 1] = characters[i3 + 1].toUpperCase();
    }
  }
  const result = `0x${characters.join("")}`;
  checksum.set(address, result);
  return result;
}
var InvalidAddressError2 = class extends BaseError2 {
  constructor({ address, cause }) {
    super(`Address "${address}" is invalid.`, {
      cause
    });
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Address.InvalidAddressError"
    });
  }
};
var InvalidInputError = class extends BaseError2 {
  constructor() {
    super("Address is not a 20 byte (40 hexadecimal character) value.");
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Address.InvalidInputError"
    });
  }
};
var InvalidChecksumError = class extends BaseError2 {
  constructor() {
    super("Address does not match its checksum counterpart.");
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Address.InvalidChecksumError"
    });
  }
};

// node_modules/@reown/appkit/node_modules/ox/_esm/core/Solidity.js
var arrayRegex2 = /^(.*)\[([0-9]*)\]$/;
var bytesRegex2 = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/;
var integerRegex2 = /^(u?int)(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/;
var maxInt8 = 2n ** (8n - 1n) - 1n;
var maxInt16 = 2n ** (16n - 1n) - 1n;
var maxInt24 = 2n ** (24n - 1n) - 1n;
var maxInt32 = 2n ** (32n - 1n) - 1n;
var maxInt40 = 2n ** (40n - 1n) - 1n;
var maxInt48 = 2n ** (48n - 1n) - 1n;
var maxInt56 = 2n ** (56n - 1n) - 1n;
var maxInt64 = 2n ** (64n - 1n) - 1n;
var maxInt72 = 2n ** (72n - 1n) - 1n;
var maxInt80 = 2n ** (80n - 1n) - 1n;
var maxInt88 = 2n ** (88n - 1n) - 1n;
var maxInt96 = 2n ** (96n - 1n) - 1n;
var maxInt104 = 2n ** (104n - 1n) - 1n;
var maxInt112 = 2n ** (112n - 1n) - 1n;
var maxInt120 = 2n ** (120n - 1n) - 1n;
var maxInt128 = 2n ** (128n - 1n) - 1n;
var maxInt136 = 2n ** (136n - 1n) - 1n;
var maxInt144 = 2n ** (144n - 1n) - 1n;
var maxInt152 = 2n ** (152n - 1n) - 1n;
var maxInt160 = 2n ** (160n - 1n) - 1n;
var maxInt168 = 2n ** (168n - 1n) - 1n;
var maxInt176 = 2n ** (176n - 1n) - 1n;
var maxInt184 = 2n ** (184n - 1n) - 1n;
var maxInt192 = 2n ** (192n - 1n) - 1n;
var maxInt200 = 2n ** (200n - 1n) - 1n;
var maxInt208 = 2n ** (208n - 1n) - 1n;
var maxInt216 = 2n ** (216n - 1n) - 1n;
var maxInt224 = 2n ** (224n - 1n) - 1n;
var maxInt232 = 2n ** (232n - 1n) - 1n;
var maxInt240 = 2n ** (240n - 1n) - 1n;
var maxInt248 = 2n ** (248n - 1n) - 1n;
var maxInt256 = 2n ** (256n - 1n) - 1n;
var minInt8 = -(2n ** (8n - 1n));
var minInt16 = -(2n ** (16n - 1n));
var minInt24 = -(2n ** (24n - 1n));
var minInt32 = -(2n ** (32n - 1n));
var minInt40 = -(2n ** (40n - 1n));
var minInt48 = -(2n ** (48n - 1n));
var minInt56 = -(2n ** (56n - 1n));
var minInt64 = -(2n ** (64n - 1n));
var minInt72 = -(2n ** (72n - 1n));
var minInt80 = -(2n ** (80n - 1n));
var minInt88 = -(2n ** (88n - 1n));
var minInt96 = -(2n ** (96n - 1n));
var minInt104 = -(2n ** (104n - 1n));
var minInt112 = -(2n ** (112n - 1n));
var minInt120 = -(2n ** (120n - 1n));
var minInt128 = -(2n ** (128n - 1n));
var minInt136 = -(2n ** (136n - 1n));
var minInt144 = -(2n ** (144n - 1n));
var minInt152 = -(2n ** (152n - 1n));
var minInt160 = -(2n ** (160n - 1n));
var minInt168 = -(2n ** (168n - 1n));
var minInt176 = -(2n ** (176n - 1n));
var minInt184 = -(2n ** (184n - 1n));
var minInt192 = -(2n ** (192n - 1n));
var minInt200 = -(2n ** (200n - 1n));
var minInt208 = -(2n ** (208n - 1n));
var minInt216 = -(2n ** (216n - 1n));
var minInt224 = -(2n ** (224n - 1n));
var minInt232 = -(2n ** (232n - 1n));
var minInt240 = -(2n ** (240n - 1n));
var minInt248 = -(2n ** (248n - 1n));
var minInt256 = -(2n ** (256n - 1n));
var maxUint8 = 2n ** 8n - 1n;
var maxUint16 = 2n ** 16n - 1n;
var maxUint24 = 2n ** 24n - 1n;
var maxUint32 = 2n ** 32n - 1n;
var maxUint40 = 2n ** 40n - 1n;
var maxUint48 = 2n ** 48n - 1n;
var maxUint56 = 2n ** 56n - 1n;
var maxUint64 = 2n ** 64n - 1n;
var maxUint72 = 2n ** 72n - 1n;
var maxUint80 = 2n ** 80n - 1n;
var maxUint88 = 2n ** 88n - 1n;
var maxUint96 = 2n ** 96n - 1n;
var maxUint104 = 2n ** 104n - 1n;
var maxUint112 = 2n ** 112n - 1n;
var maxUint120 = 2n ** 120n - 1n;
var maxUint128 = 2n ** 128n - 1n;
var maxUint136 = 2n ** 136n - 1n;
var maxUint144 = 2n ** 144n - 1n;
var maxUint152 = 2n ** 152n - 1n;
var maxUint160 = 2n ** 160n - 1n;
var maxUint168 = 2n ** 168n - 1n;
var maxUint176 = 2n ** 176n - 1n;
var maxUint184 = 2n ** 184n - 1n;
var maxUint192 = 2n ** 192n - 1n;
var maxUint200 = 2n ** 200n - 1n;
var maxUint208 = 2n ** 208n - 1n;
var maxUint216 = 2n ** 216n - 1n;
var maxUint224 = 2n ** 224n - 1n;
var maxUint232 = 2n ** 232n - 1n;
var maxUint240 = 2n ** 240n - 1n;
var maxUint248 = 2n ** 248n - 1n;
var maxUint2562 = 2n ** 256n - 1n;

// node_modules/@reown/appkit/node_modules/ox/_esm/core/internal/cursor.js
var staticCursor = {
  bytes: new Uint8Array(),
  dataView: new DataView(new ArrayBuffer(0)),
  position: 0,
  positionReadCount: /* @__PURE__ */ new Map(),
  recursiveReadCount: 0,
  recursiveReadLimit: Number.POSITIVE_INFINITY,
  assertReadLimit() {
    if (this.recursiveReadCount >= this.recursiveReadLimit)
      throw new RecursiveReadLimitExceededError({
        count: this.recursiveReadCount + 1,
        limit: this.recursiveReadLimit
      });
  },
  assertPosition(position) {
    if (position < 0 || position > this.bytes.length - 1)
      throw new PositionOutOfBoundsError2({
        length: this.bytes.length,
        position
      });
  },
  decrementPosition(offset) {
    if (offset < 0)
      throw new NegativeOffsetError({ offset });
    const position = this.position - offset;
    this.assertPosition(position);
    this.position = position;
  },
  getReadCount(position) {
    return this.positionReadCount.get(position || this.position) || 0;
  },
  incrementPosition(offset) {
    if (offset < 0)
      throw new NegativeOffsetError({ offset });
    const position = this.position + offset;
    this.assertPosition(position);
    this.position = position;
  },
  inspectByte(position_) {
    const position = position_ ?? this.position;
    this.assertPosition(position);
    return this.bytes[position];
  },
  inspectBytes(length, position_) {
    const position = position_ ?? this.position;
    this.assertPosition(position + length - 1);
    return this.bytes.subarray(position, position + length);
  },
  inspectUint8(position_) {
    const position = position_ ?? this.position;
    this.assertPosition(position);
    return this.bytes[position];
  },
  inspectUint16(position_) {
    const position = position_ ?? this.position;
    this.assertPosition(position + 1);
    return this.dataView.getUint16(position);
  },
  inspectUint24(position_) {
    const position = position_ ?? this.position;
    this.assertPosition(position + 2);
    return (this.dataView.getUint16(position) << 8) + this.dataView.getUint8(position + 2);
  },
  inspectUint32(position_) {
    const position = position_ ?? this.position;
    this.assertPosition(position + 3);
    return this.dataView.getUint32(position);
  },
  pushByte(byte) {
    this.assertPosition(this.position);
    this.bytes[this.position] = byte;
    this.position++;
  },
  pushBytes(bytes) {
    this.assertPosition(this.position + bytes.length - 1);
    this.bytes.set(bytes, this.position);
    this.position += bytes.length;
  },
  pushUint8(value) {
    this.assertPosition(this.position);
    this.bytes[this.position] = value;
    this.position++;
  },
  pushUint16(value) {
    this.assertPosition(this.position + 1);
    this.dataView.setUint16(this.position, value);
    this.position += 2;
  },
  pushUint24(value) {
    this.assertPosition(this.position + 2);
    this.dataView.setUint16(this.position, value >> 8);
    this.dataView.setUint8(this.position + 2, value & ~4294967040);
    this.position += 3;
  },
  pushUint32(value) {
    this.assertPosition(this.position + 3);
    this.dataView.setUint32(this.position, value);
    this.position += 4;
  },
  readByte() {
    this.assertReadLimit();
    this._touch();
    const value = this.inspectByte();
    this.position++;
    return value;
  },
  readBytes(length, size4) {
    this.assertReadLimit();
    this._touch();
    const value = this.inspectBytes(length);
    this.position += size4 ?? length;
    return value;
  },
  readUint8() {
    this.assertReadLimit();
    this._touch();
    const value = this.inspectUint8();
    this.position += 1;
    return value;
  },
  readUint16() {
    this.assertReadLimit();
    this._touch();
    const value = this.inspectUint16();
    this.position += 2;
    return value;
  },
  readUint24() {
    this.assertReadLimit();
    this._touch();
    const value = this.inspectUint24();
    this.position += 3;
    return value;
  },
  readUint32() {
    this.assertReadLimit();
    this._touch();
    const value = this.inspectUint32();
    this.position += 4;
    return value;
  },
  get remaining() {
    return this.bytes.length - this.position;
  },
  setPosition(position) {
    const oldPosition = this.position;
    this.assertPosition(position);
    this.position = position;
    return () => this.position = oldPosition;
  },
  _touch() {
    if (this.recursiveReadLimit === Number.POSITIVE_INFINITY)
      return;
    const count = this.getReadCount();
    this.positionReadCount.set(this.position, count + 1);
    if (count > 0)
      this.recursiveReadCount++;
  }
};
var NegativeOffsetError = class extends BaseError2 {
  constructor({ offset }) {
    super(`Offset \`${offset}\` cannot be negative.`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Cursor.NegativeOffsetError"
    });
  }
};
var PositionOutOfBoundsError2 = class extends BaseError2 {
  constructor({ length, position }) {
    super(`Position \`${position}\` is out of bounds (\`0 < position < ${length}\`).`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Cursor.PositionOutOfBoundsError"
    });
  }
};
var RecursiveReadLimitExceededError = class extends BaseError2 {
  constructor({ count, limit }) {
    super(`Recursive read limit of \`${limit}\` exceeded (recursive read count: \`${count}\`).`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Cursor.RecursiveReadLimitExceededError"
    });
  }
};

// node_modules/@reown/appkit/node_modules/ox/_esm/core/AbiParameters.js
function encodePacked2(types, values) {
  if (types.length !== values.length)
    throw new LengthMismatchError({
      expectedLength: types.length,
      givenLength: values.length
    });
  const data = [];
  for (let i3 = 0; i3 < types.length; i3++) {
    const type = types[i3];
    const value = values[i3];
    data.push(encodePacked2.encode(type, value));
  }
  return concat3(...data);
}
(function(encodePacked3) {
  function encode4(type, value, isArray = false) {
    if (type === "address") {
      const address = value;
      assert(address);
      return padLeft(address.toLowerCase(), isArray ? 32 : 0);
    }
    if (type === "string")
      return fromString3(value);
    if (type === "bytes")
      return value;
    if (type === "bool")
      return padLeft(fromBoolean(value), isArray ? 32 : 1);
    const intMatch = type.match(integerRegex2);
    if (intMatch) {
      const [_type, baseType, bits = "256"] = intMatch;
      const size4 = Number.parseInt(bits) / 8;
      return fromNumber(value, {
        size: isArray ? 32 : size4,
        signed: baseType === "int"
      });
    }
    const bytesMatch = type.match(bytesRegex2);
    if (bytesMatch) {
      const [_type, size4] = bytesMatch;
      if (Number.parseInt(size4) !== (value.length - 2) / 2)
        throw new BytesSizeMismatchError2({
          expectedSize: Number.parseInt(size4),
          value
        });
      return padRight(value, isArray ? 32 : 0);
    }
    const arrayMatch = type.match(arrayRegex2);
    if (arrayMatch && Array.isArray(value)) {
      const [_type, childType] = arrayMatch;
      const data = [];
      for (let i3 = 0; i3 < value.length; i3++) {
        data.push(encode4(childType, value[i3], true));
      }
      if (data.length === 0)
        return "0x";
      return concat3(...data);
    }
    throw new InvalidTypeError(type);
  }
  encodePacked3.encode = encode4;
})(encodePacked2 || (encodePacked2 = {}));
var BytesSizeMismatchError2 = class extends BaseError2 {
  constructor({ expectedSize, value }) {
    super(`Size of bytes "${value}" (bytes${size3(value)}) does not match expected size (bytes${expectedSize}).`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "AbiParameters.BytesSizeMismatchError"
    });
  }
};
var LengthMismatchError = class extends BaseError2 {
  constructor({ expectedLength, givenLength }) {
    super([
      "ABI encoding parameters/values length mismatch.",
      `Expected length (parameters): ${expectedLength}`,
      `Given length (values): ${givenLength}`
    ].join("\n"));
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "AbiParameters.LengthMismatchError"
    });
  }
};
var InvalidTypeError = class extends BaseError2 {
  constructor(type) {
    super(`Type \`${type}\` is not a valid ABI Type.`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "AbiParameters.InvalidTypeError"
    });
  }
};

// node_modules/@reown/appkit/node_modules/@walletconnect/utils/dist/index.es.js
var ae = ":";
function Ne(t) {
  const [e, n2] = t.split(ae);
  return { namespace: e, reference: n2 };
}
function ue(t, e) {
  return t.includes(":") ? [t] : e.chains || [];
}
var Zo = Object.defineProperty;
var Yo = Object.defineProperties;
var Go = Object.getOwnPropertyDescriptors;
var Tn = Object.getOwnPropertySymbols;
var Wo = Object.prototype.hasOwnProperty;
var Xo = Object.prototype.propertyIsEnumerable;
var Rn = (t, e, n2) => e in t ? Zo(t, e, { enumerable: true, configurable: true, writable: true, value: n2 }) : t[e] = n2;
var _n = (t, e) => {
  for (var n2 in e || (e = {})) Wo.call(e, n2) && Rn(t, n2, e[n2]);
  if (Tn) for (var n2 of Tn(e)) Xo.call(e, n2) && Rn(t, n2, e[n2]);
  return t;
};
var Jo = (t, e) => Yo(t, Go(e));
var $n = "ReactNative";
var Y = { reactNative: "react-native", node: "node", browser: "browser", unknown: "unknown" };
var jn = "js";
function _e() {
  return typeof process < "u" && typeof process.versions < "u" && typeof process.versions.node < "u";
}
function pt() {
  return !(0, import_window_getters.getDocument)() && !!(0, import_window_getters.getNavigator)() && navigator.product === $n;
}
function ei() {
  return pt() && typeof global < "u" && typeof (global == null ? void 0 : global.Platform) < "u" && (global == null ? void 0 : global.Platform.OS) === "android";
}
function ni() {
  return pt() && typeof global < "u" && typeof (global == null ? void 0 : global.Platform) < "u" && (global == null ? void 0 : global.Platform.OS) === "ios";
}
function Tt() {
  return !_e() && !!(0, import_window_getters.getNavigator)() && !!(0, import_window_getters.getDocument)();
}
function xt() {
  return pt() ? Y.reactNative : _e() ? Y.node : Tt() ? Y.browser : Y.unknown;
}
function ri() {
  var t;
  try {
    return pt() && typeof global < "u" && typeof (global == null ? void 0 : global.Application) < "u" ? (t = global.Application) == null ? void 0 : t.applicationId : void 0;
  } catch {
    return;
  }
}
function Cn(t, e) {
  const n2 = new URLSearchParams(t);
  for (const r2 of Object.keys(e).sort()) if (e.hasOwnProperty(r2)) {
    const o2 = e[r2];
    o2 !== void 0 && n2.set(r2, o2);
  }
  return n2.toString();
}
function oi(t) {
  var e, n2;
  const r2 = Pn();
  try {
    return t != null && t.url && r2.url && new URL(t.url).host !== new URL(r2.url).host && (console.warn(`The configured WalletConnect 'metadata.url':${t.url} differs from the actual page url:${r2.url}. This is probably unintended and can lead to issues.`), t.url = r2.url), (e = t == null ? void 0 : t.icons) != null && e.length && t.icons.length > 0 && (t.icons = t.icons.filter((o2) => o2 !== "")), Jo(_n(_n({}, r2), t), { url: (t == null ? void 0 : t.url) || r2.url, name: (t == null ? void 0 : t.name) || r2.name, description: (t == null ? void 0 : t.description) || r2.description, icons: (n2 = t == null ? void 0 : t.icons) != null && n2.length && t.icons.length > 0 ? t.icons : r2.icons });
  } catch (o2) {
    return console.warn("Error populating app metadata", o2), t || r2;
  }
}
function Pn() {
  return (0, import_window_metadata.getWindowMetadata)() || { name: "", description: "", url: "", icons: [""] };
}
function kn() {
  if (xt() === Y.reactNative && typeof global < "u" && typeof (global == null ? void 0 : global.Platform) < "u") {
    const { OS: n2, Version: r2 } = global.Platform;
    return [n2, r2].join("-");
  }
  const t = detect();
  if (t === null) return "unknown";
  const e = t.os ? t.os.replace(" ", "").toLowerCase() : "unknown";
  return t.type === "browser" ? [e, t.name, t.version].join("-") : [e, t.version].join("-");
}
function Vn() {
  var t;
  const e = xt();
  return e === Y.browser ? [e, ((t = (0, import_window_getters.getLocation)()) == null ? void 0 : t.host) || "unknown"].join(":") : e;
}
function Mn(t, e, n2) {
  const r2 = kn(), o2 = Vn();
  return [[t, e].join("-"), [jn, n2].join("-"), r2, o2].join("/");
}
function si({ protocol: t, version: e, relayUrl: n2, sdkVersion: r2, auth: o2, projectId: i3, useOnCloseEvent: s, bundleId: c3, packageName: a2 }) {
  const u3 = n2.split("?"), l3 = Mn(t, e, r2), f6 = { auth: o2, ua: l3, projectId: i3, useOnCloseEvent: s || void 0, packageName: a2 || void 0, bundleId: c3 || void 0 }, h4 = Cn(u3[1] || "", f6);
  return u3[0] + "?" + h4;
}
function gt(t, e) {
  return t.filter((n2) => e.includes(n2)).length === t.length;
}
function fi(t) {
  return Object.fromEntries(t.entries());
}
function li(t) {
  return new Map(Object.entries(t));
}
function gi(t = import_time.FIVE_MINUTES, e) {
  const n2 = (0, import_time.toMiliseconds)(t || import_time.FIVE_MINUTES);
  let r2, o2, i3, s;
  return { resolve: (c3) => {
    i3 && r2 && (clearTimeout(i3), r2(c3), s = Promise.resolve(c3));
  }, reject: (c3) => {
    i3 && o2 && (clearTimeout(i3), o2(c3));
  }, done: () => new Promise((c3, a2) => {
    if (s) return c3(s);
    i3 = setTimeout(() => {
      const u3 = new Error(e);
      s = Promise.reject(u3), a2(u3);
    }, n2), r2 = c3, o2 = a2;
  }) };
}
function yi(t, e, n2) {
  return new Promise(async (r2, o2) => {
    const i3 = setTimeout(() => o2(new Error(n2)), e);
    try {
      const s = await t;
      r2(s);
    } catch (s) {
      o2(s);
    }
    clearTimeout(i3);
  });
}
function $e(t, e) {
  if (typeof e == "string" && e.startsWith(`${t}:`)) return e;
  if (t.toLowerCase() === "topic") {
    if (typeof e != "string") throw new Error('Value must be "string" for expirer target type: topic');
    return `topic:${e}`;
  } else if (t.toLowerCase() === "id") {
    if (typeof e != "number") throw new Error('Value must be "number" for expirer target type: id');
    return `id:${e}`;
  }
  throw new Error(`Unknown expirer target type: ${t}`);
}
function mi(t) {
  return $e("topic", t);
}
function wi(t) {
  return $e("id", t);
}
function bi(t) {
  const [e, n2] = t.split(":"), r2 = { id: void 0, topic: void 0 };
  if (e === "topic" && typeof n2 == "string") r2.topic = n2;
  else if (e === "id" && Number.isInteger(Number(n2))) r2.id = Number(n2);
  else throw new Error(`Invalid target, expected id:number or topic:string, got ${e}:${n2}`);
  return r2;
}
function Ei(t, e) {
  return (0, import_time.fromMiliseconds)((e || Date.now()) + (0, import_time.toMiliseconds)(t));
}
function vi(t) {
  return Date.now() >= (0, import_time.toMiliseconds)(t);
}
function xi(t, e) {
  return `${t}${e ? `:${e}` : ""}`;
}
function ot(t = [], e = []) {
  return [.../* @__PURE__ */ new Set([...t, ...e])];
}
async function Si({ id: t, topic: e, wcDeepLink: n2 }) {
  var r2;
  try {
    if (!n2) return;
    const o2 = typeof n2 == "string" ? JSON.parse(n2) : n2, i3 = o2 == null ? void 0 : o2.href;
    if (typeof i3 != "string") return;
    const s = Kn(i3, t, e), c3 = xt();
    if (c3 === Y.browser) {
      if (!((r2 = (0, import_window_getters.getDocument)()) != null && r2.hasFocus())) {
        console.warn("Document does not have focus, skipping deeplink.");
        return;
      }
      Fn(s);
    } else c3 === Y.reactNative && typeof (global == null ? void 0 : global.Linking) < "u" && await global.Linking.openURL(s);
  } catch (o2) {
    console.error(o2);
  }
}
function Kn(t, e, n2) {
  const r2 = `requestId=${e}&sessionTopic=${n2}`;
  t.endsWith("/") && (t = t.slice(0, -1));
  let o2 = `${t}`;
  if (t.startsWith("https://t.me")) {
    const i3 = t.includes("?") ? "&startapp=" : "?startapp=";
    o2 = `${o2}${i3}${Yn(r2, true)}`;
  } else o2 = `${o2}/wc?${r2}`;
  return o2;
}
function Fn(t) {
  let e = "_self";
  Zn() ? e = "_top" : (zn() || t.startsWith("https://") || t.startsWith("http://")) && (e = "_blank"), window.open(t, e, "noreferrer noopener");
}
async function Oi(t, e) {
  let n2 = "";
  try {
    if (Tt() && (n2 = localStorage.getItem(e), n2)) return n2;
    n2 = await t.getItem(e);
  } catch (r2) {
    console.error(r2);
  }
  return n2;
}
function Ai(t, e) {
  if (!t.includes(e)) return null;
  const n2 = t.split(/([&,?,=])/), r2 = n2.indexOf(e);
  return n2[r2 + 2];
}
function Bi() {
  return typeof crypto < "u" && crypto != null && crypto.randomUUID ? crypto.randomUUID() : "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/gu, (t) => {
    const e = Math.random() * 16 | 0;
    return (t === "x" ? e : e & 3 | 8).toString(16);
  });
}
function Ii() {
  return typeof process < "u" && process.env.IS_VITEST === "true";
}
function zn() {
  return typeof window < "u" && (!!window.TelegramWebviewProxy || !!window.Telegram || !!window.TelegramWebviewProxyProto);
}
function Zn() {
  try {
    return window.self !== window.top;
  } catch {
    return false;
  }
}
function Yn(t, e = false) {
  const n2 = Buffer.from(t).toString("base64");
  return e ? n2.replace(/[=]/g, "") : n2;
}
function je(t) {
  return Buffer.from(t, "base64").toString("utf-8");
}
function Ni(t) {
  return new Promise((e) => setTimeout(e, t));
}
function Wt(t) {
  if (!Number.isSafeInteger(t) || t < 0) throw new Error("positive integer expected, got " + t);
}
function Ui(t) {
  return t instanceof Uint8Array || ArrayBuffer.isView(t) && t.constructor.name === "Uint8Array";
}
function Xt(t, ...e) {
  if (!Ui(t)) throw new Error("Uint8Array expected");
  if (e.length > 0 && !e.includes(t.length)) throw new Error("Uint8Array expected of length " + e + ", got length=" + t.length);
}
function Ce(t) {
  if (typeof t != "function" || typeof t.create != "function") throw new Error("Hash should be wrapped by utils.wrapConstructor");
  Wt(t.outputLen), Wt(t.blockLen);
}
function Rt(t, e = true) {
  if (t.destroyed) throw new Error("Hash instance has been destroyed");
  if (e && t.finished) throw new Error("Hash#digest() has already been called");
}
function Gn(t, e) {
  Xt(t);
  const n2 = e.outputLen;
  if (t.length < n2) throw new Error("digestInto() expects output buffer of length at least " + n2);
}
var le = BigInt(2 ** 32 - 1);
var Wn = BigInt(32);
function Ti(t, e = false) {
  return e ? { h: Number(t & le), l: Number(t >> Wn & le) } : { h: Number(t >> Wn & le) | 0, l: Number(t & le) | 0 };
}
function Ri(t, e = false) {
  let n2 = new Uint32Array(t.length), r2 = new Uint32Array(t.length);
  for (let o2 = 0; o2 < t.length; o2++) {
    const { h: i3, l: s } = Ti(t[o2], e);
    [n2[o2], r2[o2]] = [i3, s];
  }
  return [n2, r2];
}
var _i = (t, e, n2) => t << n2 | e >>> 32 - n2;
var $i = (t, e, n2) => e << n2 | t >>> 32 - n2;
var Li = (t, e, n2) => e << n2 - 32 | t >>> 64 - n2;
var ji = (t, e, n2) => t << n2 - 32 | e >>> 64 - n2;
var _t = typeof globalThis == "object" && "crypto" in globalThis ? globalThis.crypto : void 0;
function Ci(t) {
  return new Uint32Array(t.buffer, t.byteOffset, Math.floor(t.byteLength / 4));
}
function Pe(t) {
  return new DataView(t.buffer, t.byteOffset, t.byteLength);
}
function ct(t, e) {
  return t << 32 - e | t >>> e;
}
var Xn = new Uint8Array(new Uint32Array([287454020]).buffer)[0] === 68;
function Pi2(t) {
  return t << 24 & 4278190080 | t << 8 & 16711680 | t >>> 8 & 65280 | t >>> 24 & 255;
}
function Jn(t) {
  for (let e = 0; e < t.length; e++) t[e] = Pi2(t[e]);
}
function ki(t) {
  if (typeof t != "string") throw new Error("utf8ToBytes expected string, got " + typeof t);
  return new Uint8Array(new TextEncoder().encode(t));
}
function $t(t) {
  return typeof t == "string" && (t = ki(t)), Xt(t), t;
}
function Vi(...t) {
  let e = 0;
  for (let r2 = 0; r2 < t.length; r2++) {
    const o2 = t[r2];
    Xt(o2), e += o2.length;
  }
  const n2 = new Uint8Array(e);
  for (let r2 = 0, o2 = 0; r2 < t.length; r2++) {
    const i3 = t[r2];
    n2.set(i3, o2), o2 += i3.length;
  }
  return n2;
}
var ke = class {
  clone() {
    return this._cloneInto();
  }
};
function Qn(t) {
  const e = (r2) => t().update($t(r2)).digest(), n2 = t();
  return e.outputLen = n2.outputLen, e.blockLen = n2.blockLen, e.create = () => t(), e;
}
function Lt(t = 32) {
  if (_t && typeof _t.getRandomValues == "function") return _t.getRandomValues(new Uint8Array(t));
  if (_t && typeof _t.randomBytes == "function") return _t.randomBytes(t);
  throw new Error("crypto.getRandomValues must be defined");
}
var tr = [];
var er = [];
var nr = [];
var Mi = BigInt(0);
var Jt = BigInt(1);
var Di = BigInt(2);
var Hi = BigInt(7);
var qi = BigInt(256);
var Ki = BigInt(113);
for (let t = 0, e = Jt, n2 = 1, r2 = 0; t < 24; t++) {
  [n2, r2] = [r2, (2 * n2 + 3 * r2) % 5], tr.push(2 * (5 * r2 + n2)), er.push((t + 1) * (t + 2) / 2 % 64);
  let o2 = Mi;
  for (let i3 = 0; i3 < 7; i3++) e = (e << Jt ^ (e >> Hi) * Ki) % qi, e & Di && (o2 ^= Jt << (Jt << BigInt(i3)) - Jt);
  nr.push(o2);
}
var [Fi, zi] = Ri(nr, true);
var rr = (t, e, n2) => n2 > 32 ? Li(t, e, n2) : _i(t, e, n2);
var or = (t, e, n2) => n2 > 32 ? ji(t, e, n2) : $i(t, e, n2);
function Zi(t, e = 24) {
  const n2 = new Uint32Array(10);
  for (let r2 = 24 - e; r2 < 24; r2++) {
    for (let s = 0; s < 10; s++) n2[s] = t[s] ^ t[s + 10] ^ t[s + 20] ^ t[s + 30] ^ t[s + 40];
    for (let s = 0; s < 10; s += 2) {
      const c3 = (s + 8) % 10, a2 = (s + 2) % 10, u3 = n2[a2], l3 = n2[a2 + 1], f6 = rr(u3, l3, 1) ^ n2[c3], h4 = or(u3, l3, 1) ^ n2[c3 + 1];
      for (let y5 = 0; y5 < 50; y5 += 10) t[s + y5] ^= f6, t[s + y5 + 1] ^= h4;
    }
    let o2 = t[2], i3 = t[3];
    for (let s = 0; s < 24; s++) {
      const c3 = er[s], a2 = rr(o2, i3, c3), u3 = or(o2, i3, c3), l3 = tr[s];
      o2 = t[l3], i3 = t[l3 + 1], t[l3] = a2, t[l3 + 1] = u3;
    }
    for (let s = 0; s < 50; s += 10) {
      for (let c3 = 0; c3 < 10; c3++) n2[c3] = t[s + c3];
      for (let c3 = 0; c3 < 10; c3++) t[s + c3] ^= ~n2[(c3 + 2) % 10] & n2[(c3 + 4) % 10];
    }
    t[0] ^= Fi[r2], t[1] ^= zi[r2];
  }
  n2.fill(0);
}
var En = class _En extends ke {
  constructor(e, n2, r2, o2 = false, i3 = 24) {
    if (super(), this.blockLen = e, this.suffix = n2, this.outputLen = r2, this.enableXOF = o2, this.rounds = i3, this.pos = 0, this.posOut = 0, this.finished = false, this.destroyed = false, Wt(r2), 0 >= this.blockLen || this.blockLen >= 200) throw new Error("Sha3 supports only keccak-f1600 function");
    this.state = new Uint8Array(200), this.state32 = Ci(this.state);
  }
  keccak() {
    Xn || Jn(this.state32), Zi(this.state32, this.rounds), Xn || Jn(this.state32), this.posOut = 0, this.pos = 0;
  }
  update(e) {
    Rt(this);
    const { blockLen: n2, state: r2 } = this;
    e = $t(e);
    const o2 = e.length;
    for (let i3 = 0; i3 < o2; ) {
      const s = Math.min(n2 - this.pos, o2 - i3);
      for (let c3 = 0; c3 < s; c3++) r2[this.pos++] ^= e[i3++];
      this.pos === n2 && this.keccak();
    }
    return this;
  }
  finish() {
    if (this.finished) return;
    this.finished = true;
    const { state: e, suffix: n2, pos: r2, blockLen: o2 } = this;
    e[r2] ^= n2, (n2 & 128) !== 0 && r2 === o2 - 1 && this.keccak(), e[o2 - 1] ^= 128, this.keccak();
  }
  writeInto(e) {
    Rt(this, false), Xt(e), this.finish();
    const n2 = this.state, { blockLen: r2 } = this;
    for (let o2 = 0, i3 = e.length; o2 < i3; ) {
      this.posOut >= r2 && this.keccak();
      const s = Math.min(r2 - this.posOut, i3 - o2);
      e.set(n2.subarray(this.posOut, this.posOut + s), o2), this.posOut += s, o2 += s;
    }
    return e;
  }
  xofInto(e) {
    if (!this.enableXOF) throw new Error("XOF is not possible for this instance");
    return this.writeInto(e);
  }
  xof(e) {
    return Wt(e), this.xofInto(new Uint8Array(e));
  }
  digestInto(e) {
    if (Gn(e, this), this.finished) throw new Error("digest() was already called");
    return this.writeInto(e), this.destroy(), e;
  }
  digest() {
    return this.digestInto(new Uint8Array(this.outputLen));
  }
  destroy() {
    this.destroyed = true, this.state.fill(0);
  }
  _cloneInto(e) {
    const { blockLen: n2, suffix: r2, outputLen: o2, rounds: i3, enableXOF: s } = this;
    return e || (e = new _En(n2, r2, o2, s, i3)), e.state32.set(this.state32), e.pos = this.pos, e.posOut = this.posOut, e.finished = this.finished, e.rounds = i3, e.suffix = r2, e.outputLen = o2, e.enableXOF = s, e.destroyed = this.destroyed, e;
  }
};
var Yi = (t, e, n2) => Qn(() => new En(e, t, n2));
var Gi = Yi(1, 136, 256 / 8);
var Wi = "https://rpc.walletconnect.org/v1";
function Ve(t) {
  const e = `Ethereum Signed Message:
${t.length}`, n2 = new TextEncoder().encode(e + t);
  return "0x" + Buffer.from(Gi(n2)).toString("hex");
}
async function ir(t, e, n2, r2, o2, i3) {
  switch (n2.t) {
    case "eip191":
      return await sr(t, e, n2.s);
    case "eip1271":
      return await cr(t, e, n2.s, r2, o2, i3);
    default:
      throw new Error(`verifySignature failed: Attempted to verify CacaoSignature with unknown type: ${n2.t}`);
  }
}
async function sr(t, e, n2) {
  return (await recoverAddress({ hash: Ve(e), signature: n2 })).toLowerCase() === t.toLowerCase();
}
async function cr(t, e, n2, r2, o2, i3) {
  const s = Ne(r2);
  if (!s.namespace || !s.reference) throw new Error(`isValidEip1271Signature failed: chainId must be in CAIP-2 format, received: ${r2}`);
  try {
    const c3 = "0x1626ba7e", a2 = "0000000000000000000000000000000000000000000000000000000000000040", u3 = "0000000000000000000000000000000000000000000000000000000000000041", l3 = n2.substring(2), f6 = Ve(e).substring(2), h4 = c3 + f6 + a2 + u3 + l3, y5 = await fetch(`${i3 || Wi}/?chainId=${r2}&projectId=${o2}`, { method: "POST", body: JSON.stringify({ id: Xi(), jsonrpc: "2.0", method: "eth_call", params: [{ to: t, data: h4 }, "latest"] }) }), { result: E5 } = await y5.json();
    return E5 ? E5.slice(0, c3.length).toLowerCase() === c3.toLowerCase() : false;
  } catch (c3) {
    return console.error("isValidEip1271Signature: ", c3), false;
  }
}
function Xi() {
  return Date.now() + Math.floor(Math.random() * 1e3);
}
function Ji(t) {
  const e = atob(t), n2 = new Uint8Array(e.length);
  for (let s = 0; s < e.length; s++) n2[s] = e.charCodeAt(s);
  const r2 = n2[0];
  if (r2 === 0) throw new Error("No signatures found");
  const o2 = 1 + r2 * 64;
  if (n2.length < o2) throw new Error("Transaction data too short for claimed signature count");
  if (n2.length < 100) throw new Error("Transaction too short");
  const i3 = Buffer.from(t, "base64").slice(1, 65);
  return esm_default.encode(i3);
}
var Qi = Object.defineProperty;
var ts = Object.defineProperties;
var es = Object.getOwnPropertyDescriptors;
var ar = Object.getOwnPropertySymbols;
var ns = Object.prototype.hasOwnProperty;
var rs = Object.prototype.propertyIsEnumerable;
var ur = (t, e, n2) => e in t ? Qi(t, e, { enumerable: true, configurable: true, writable: true, value: n2 }) : t[e] = n2;
var Me = (t, e) => {
  for (var n2 in e || (e = {})) ns.call(e, n2) && ur(t, n2, e[n2]);
  if (ar) for (var n2 of ar(e)) rs.call(e, n2) && ur(t, n2, e[n2]);
  return t;
};
var fr = (t, e) => ts(t, es(e));
var os = "did:pkh:";
var de = (t) => t == null ? void 0 : t.split(":");
var lr = (t) => {
  const e = t && de(t);
  if (e) return t.includes(os) ? e[3] : e[1];
};
var dr = (t) => {
  const e = t && de(t);
  if (e) return e[2] + ":" + e[3];
};
var De = (t) => {
  const e = t && de(t);
  if (e) return e.pop();
};
async function is(t) {
  const { cacao: e, projectId: n2 } = t, { s: r2, p: o2 } = e, i3 = hr(o2, o2.iss), s = De(o2.iss);
  return await ir(s, i3, r2, dr(o2.iss), n2);
}
var hr = (t, e) => {
  const n2 = `${t.domain} wants you to sign in with your Ethereum account:`, r2 = De(e);
  if (!t.aud && !t.uri) throw new Error("Either `aud` or `uri` is required to construct the message");
  let o2 = t.statement || void 0;
  const i3 = `URI: ${t.aud || t.uri}`, s = `Version: ${t.version}`, c3 = `Chain ID: ${lr(e)}`, a2 = `Nonce: ${t.nonce}`, u3 = `Issued At: ${t.iat}`, l3 = t.exp ? `Expiration Time: ${t.exp}` : void 0, f6 = t.nbf ? `Not Before: ${t.nbf}` : void 0, h4 = t.requestId ? `Request ID: ${t.requestId}` : void 0, y5 = t.resources ? `Resources:${t.resources.map((p3) => `
- ${p3}`).join("")}` : void 0, E5 = pe(t.resources);
  if (E5) {
    const p3 = yt(E5);
    o2 = Ke(o2, p3);
  }
  return [n2, r2, "", o2, "", i3, s, c3, a2, u3, l3, f6, h4, y5].filter((p3) => p3 != null).join(`
`);
};
function mr(t) {
  return Buffer.from(JSON.stringify(t)).toString("base64");
}
function wr(t) {
  return JSON.parse(Buffer.from(t, "base64").toString("utf-8"));
}
function at(t) {
  if (!t) throw new Error("No recap provided, value is undefined");
  if (!t.att) throw new Error("No `att` property found");
  const e = Object.keys(t.att);
  if (!(e != null && e.length)) throw new Error("No resources found in `att` property");
  e.forEach((n2) => {
    const r2 = t.att[n2];
    if (Array.isArray(r2)) throw new Error(`Resource must be an object: ${n2}`);
    if (typeof r2 != "object") throw new Error(`Resource must be an object: ${n2}`);
    if (!Object.keys(r2).length) throw new Error(`Resource object is empty: ${n2}`);
    Object.keys(r2).forEach((o2) => {
      const i3 = r2[o2];
      if (!Array.isArray(i3)) throw new Error(`Ability limits ${o2} must be an array of objects, found: ${i3}`);
      if (!i3.length) throw new Error(`Value of ${o2} is empty array, must be an array with objects`);
      i3.forEach((s) => {
        if (typeof s != "object") throw new Error(`Ability limits (${o2}) must be an array of objects, found: ${s}`);
      });
    });
  });
}
function br(t, e, n2, r2 = {}) {
  return n2 == null ? void 0 : n2.sort((o2, i3) => o2.localeCompare(i3)), { att: { [t]: He(e, n2, r2) } };
}
function He(t, e, n2 = {}) {
  e = e == null ? void 0 : e.sort((o2, i3) => o2.localeCompare(i3));
  const r2 = e.map((o2) => ({ [`${t}/${o2}`]: [n2] }));
  return Object.assign({}, ...r2);
}
function he(t) {
  return at(t), `urn:recap:${mr(t).replace(/=/g, "")}`;
}
function yt(t) {
  const e = wr(t.replace("urn:recap:", ""));
  return at(e), e;
}
function fs(t, e, n2) {
  const r2 = br(t, e, n2);
  return he(r2);
}
function qe(t) {
  return t && t.includes("urn:recap:");
}
function ls(t, e) {
  const n2 = yt(t), r2 = yt(e), o2 = vr(n2, r2);
  return he(o2);
}
function vr(t, e) {
  at(t), at(e);
  const n2 = Object.keys(t.att).concat(Object.keys(e.att)).sort((o2, i3) => o2.localeCompare(i3)), r2 = { att: {} };
  return n2.forEach((o2) => {
    var i3, s;
    Object.keys(((i3 = t.att) == null ? void 0 : i3[o2]) || {}).concat(Object.keys(((s = e.att) == null ? void 0 : s[o2]) || {})).sort((c3, a2) => c3.localeCompare(a2)).forEach((c3) => {
      var a2, u3;
      r2.att[o2] = fr(Me({}, r2.att[o2]), { [c3]: ((a2 = t.att[o2]) == null ? void 0 : a2[c3]) || ((u3 = e.att[o2]) == null ? void 0 : u3[c3]) });
    });
  }), r2;
}
function Ke(t = "", e) {
  at(e);
  const n2 = "I further authorize the stated URI to perform the following actions on my behalf: ";
  if (t.includes(n2)) return t;
  const r2 = [];
  let o2 = 0;
  Object.keys(e.att).forEach((c3) => {
    const a2 = Object.keys(e.att[c3]).map((f6) => ({ ability: f6.split("/")[0], action: f6.split("/")[1] }));
    a2.sort((f6, h4) => f6.action.localeCompare(h4.action));
    const u3 = {};
    a2.forEach((f6) => {
      u3[f6.ability] || (u3[f6.ability] = []), u3[f6.ability].push(f6.action);
    });
    const l3 = Object.keys(u3).map((f6) => (o2++, `(${o2}) '${f6}': '${u3[f6].join("', '")}' for '${c3}'.`));
    r2.push(l3.join(", ").replace(".,", "."));
  });
  const i3 = r2.join(" "), s = `${n2}${i3}`;
  return `${t ? t + " " : ""}${s}`;
}
function ds(t) {
  var e;
  const n2 = yt(t);
  at(n2);
  const r2 = (e = n2.att) == null ? void 0 : e.eip155;
  return r2 ? Object.keys(r2).map((o2) => o2.split("/")[1]) : [];
}
function hs(t) {
  const e = yt(t);
  at(e);
  const n2 = [];
  return Object.values(e.att).forEach((r2) => {
    Object.values(r2).forEach((o2) => {
      var i3;
      (i3 = o2 == null ? void 0 : o2[0]) != null && i3.chains && n2.push(o2[0].chains);
    });
  }), [...new Set(n2.flat())];
}
function pe(t) {
  if (!t) return;
  const e = t == null ? void 0 : t[t.length - 1];
  return qe(e) ? e : void 0;
}
function Fe(t) {
  if (!Number.isSafeInteger(t) || t < 0) throw new Error("positive integer expected, got " + t);
}
function Sr(t) {
  return t instanceof Uint8Array || ArrayBuffer.isView(t) && t.constructor.name === "Uint8Array";
}
function tt(t, ...e) {
  if (!Sr(t)) throw new Error("Uint8Array expected");
  if (e.length > 0 && !e.includes(t.length)) throw new Error("Uint8Array expected of length " + e + ", got length=" + t.length);
}
function Or(t, e = true) {
  if (t.destroyed) throw new Error("Hash instance has been destroyed");
  if (e && t.finished) throw new Error("Hash#digest() has already been called");
}
function ps(t, e) {
  tt(t);
  const n2 = e.outputLen;
  if (t.length < n2) throw new Error("digestInto() expects output buffer of length at least " + n2);
}
function Ar(t) {
  if (typeof t != "boolean") throw new Error(`boolean expected, not ${t}`);
}
var mt = (t) => new Uint32Array(t.buffer, t.byteOffset, Math.floor(t.byteLength / 4));
var gs = (t) => new DataView(t.buffer, t.byteOffset, t.byteLength);
var ys = new Uint8Array(new Uint32Array([287454020]).buffer)[0] === 68;
if (!ys) throw new Error("Non little-endian hardware is not supported");
function ms(t) {
  if (typeof t != "string") throw new Error("string expected");
  return new Uint8Array(new TextEncoder().encode(t));
}
function ze(t) {
  if (typeof t == "string") t = ms(t);
  else if (Sr(t)) t = Ze(t);
  else throw new Error("Uint8Array expected, got " + typeof t);
  return t;
}
function ws(t, e) {
  if (e == null || typeof e != "object") throw new Error("options must be defined");
  return Object.assign(t, e);
}
function bs(t, e) {
  if (t.length !== e.length) return false;
  let n2 = 0;
  for (let r2 = 0; r2 < t.length; r2++) n2 |= t[r2] ^ e[r2];
  return n2 === 0;
}
var Es = (t, e) => {
  function n2(r2, ...o2) {
    if (tt(r2), t.nonceLength !== void 0) {
      const l3 = o2[0];
      if (!l3) throw new Error("nonce / iv required");
      t.varSizeNonce ? tt(l3) : tt(l3, t.nonceLength);
    }
    const i3 = t.tagLength;
    i3 && o2[1] !== void 0 && tt(o2[1]);
    const s = e(r2, ...o2), c3 = (l3, f6) => {
      if (f6 !== void 0) {
        if (l3 !== 2) throw new Error("cipher output not supported");
        tt(f6);
      }
    };
    let a2 = false;
    return { encrypt(l3, f6) {
      if (a2) throw new Error("cannot encrypt() twice with same key + nonce");
      return a2 = true, tt(l3), c3(s.encrypt.length, f6), s.encrypt(l3, f6);
    }, decrypt(l3, f6) {
      if (tt(l3), i3 && l3.length < i3) throw new Error("invalid ciphertext length: smaller than tagLength=" + i3);
      return c3(s.decrypt.length, f6), s.decrypt(l3, f6);
    } };
  }
  return Object.assign(n2, t), n2;
};
function Br(t, e, n2 = true) {
  if (e === void 0) return new Uint8Array(t);
  if (e.length !== t) throw new Error("invalid output length, expected " + t + ", got: " + e.length);
  if (n2 && !vs(e)) throw new Error("invalid output, must be aligned");
  return e;
}
function Ir(t, e, n2, r2) {
  if (typeof t.setBigUint64 == "function") return t.setBigUint64(e, n2, r2);
  const o2 = BigInt(32), i3 = BigInt(4294967295), s = Number(n2 >> o2 & i3), c3 = Number(n2 & i3), a2 = r2 ? 4 : 0, u3 = r2 ? 0 : 4;
  t.setUint32(e + a2, s, r2), t.setUint32(e + u3, c3, r2);
}
function vs(t) {
  return t.byteOffset % 4 === 0;
}
function Ze(t) {
  return Uint8Array.from(t);
}
function jt(...t) {
  for (let e = 0; e < t.length; e++) t[e].fill(0);
}
var Nr = (t) => Uint8Array.from(t.split("").map((e) => e.charCodeAt(0)));
var xs = Nr("expand 16-byte k");
var Ss = Nr("expand 32-byte k");
var Os = mt(xs);
var As = mt(Ss);
function V2(t, e) {
  return t << e | t >>> 32 - e;
}
function Ye(t) {
  return t.byteOffset % 4 === 0;
}
var ge = 64;
var Bs = 16;
var Ur = 2 ** 32 - 1;
var Tr = new Uint32Array();
function Is(t, e, n2, r2, o2, i3, s, c3) {
  const a2 = o2.length, u3 = new Uint8Array(ge), l3 = mt(u3), f6 = Ye(o2) && Ye(i3), h4 = f6 ? mt(o2) : Tr, y5 = f6 ? mt(i3) : Tr;
  for (let E5 = 0; E5 < a2; s++) {
    if (t(e, n2, r2, l3, s, c3), s >= Ur) throw new Error("arx: counter overflow");
    const p3 = Math.min(ge, a2 - E5);
    if (f6 && p3 === ge) {
      const d3 = E5 / 4;
      if (E5 % 4 !== 0) throw new Error("arx: invalid block position");
      for (let v4 = 0, m2; v4 < Bs; v4++) m2 = d3 + v4, y5[m2] = h4[m2] ^ l3[v4];
      E5 += ge;
      continue;
    }
    for (let d3 = 0, v4; d3 < p3; d3++) v4 = E5 + d3, i3[v4] = o2[v4] ^ u3[d3];
    E5 += p3;
  }
}
function Ns(t, e) {
  const { allowShortKeys: n2, extendNonceFn: r2, counterLength: o2, counterRight: i3, rounds: s } = ws({ allowShortKeys: false, counterLength: 8, counterRight: false, rounds: 20 }, e);
  if (typeof t != "function") throw new Error("core must be a function");
  return Fe(o2), Fe(s), Ar(i3), Ar(n2), (c3, a2, u3, l3, f6 = 0) => {
    tt(c3), tt(a2), tt(u3);
    const h4 = u3.length;
    if (l3 === void 0 && (l3 = new Uint8Array(h4)), tt(l3), Fe(f6), f6 < 0 || f6 >= Ur) throw new Error("arx: counter overflow");
    if (l3.length < h4) throw new Error(`arx: output (${l3.length}) is shorter than data (${h4})`);
    const y5 = [];
    let E5 = c3.length, p3, d3;
    if (E5 === 32) y5.push(p3 = Ze(c3)), d3 = As;
    else if (E5 === 16 && n2) p3 = new Uint8Array(32), p3.set(c3), p3.set(c3, 16), d3 = Os, y5.push(p3);
    else throw new Error(`arx: invalid 32-byte key, got length=${E5}`);
    Ye(a2) || y5.push(a2 = Ze(a2));
    const v4 = mt(p3);
    if (r2) {
      if (a2.length !== 24) throw new Error("arx: extended nonce must be 24 bytes");
      r2(d3, v4, mt(a2.subarray(0, 16)), v4), a2 = a2.subarray(16);
    }
    const m2 = 16 - o2;
    if (m2 !== a2.length) throw new Error(`arx: nonce must be ${m2} or 16 bytes`);
    if (m2 !== 12) {
      const N3 = new Uint8Array(12);
      N3.set(a2, i3 ? 0 : 12 - a2.length), a2 = N3, y5.push(a2);
    }
    const O4 = mt(a2);
    return Is(t, d3, v4, O4, u3, l3, f6, s), jt(...y5), l3;
  };
}
var F = (t, e) => t[e++] & 255 | (t[e++] & 255) << 8;
var Us = class {
  constructor(e) {
    this.blockLen = 16, this.outputLen = 16, this.buffer = new Uint8Array(16), this.r = new Uint16Array(10), this.h = new Uint16Array(10), this.pad = new Uint16Array(8), this.pos = 0, this.finished = false, e = ze(e), tt(e, 32);
    const n2 = F(e, 0), r2 = F(e, 2), o2 = F(e, 4), i3 = F(e, 6), s = F(e, 8), c3 = F(e, 10), a2 = F(e, 12), u3 = F(e, 14);
    this.r[0] = n2 & 8191, this.r[1] = (n2 >>> 13 | r2 << 3) & 8191, this.r[2] = (r2 >>> 10 | o2 << 6) & 7939, this.r[3] = (o2 >>> 7 | i3 << 9) & 8191, this.r[4] = (i3 >>> 4 | s << 12) & 255, this.r[5] = s >>> 1 & 8190, this.r[6] = (s >>> 14 | c3 << 2) & 8191, this.r[7] = (c3 >>> 11 | a2 << 5) & 8065, this.r[8] = (a2 >>> 8 | u3 << 8) & 8191, this.r[9] = u3 >>> 5 & 127;
    for (let l3 = 0; l3 < 8; l3++) this.pad[l3] = F(e, 16 + 2 * l3);
  }
  process(e, n2, r2 = false) {
    const o2 = r2 ? 0 : 2048, { h: i3, r: s } = this, c3 = s[0], a2 = s[1], u3 = s[2], l3 = s[3], f6 = s[4], h4 = s[5], y5 = s[6], E5 = s[7], p3 = s[8], d3 = s[9], v4 = F(e, n2 + 0), m2 = F(e, n2 + 2), O4 = F(e, n2 + 4), N3 = F(e, n2 + 6), $4 = F(e, n2 + 8), B3 = F(e, n2 + 10), A4 = F(e, n2 + 12), T2 = F(e, n2 + 14);
    let S4 = i3[0] + (v4 & 8191), L3 = i3[1] + ((v4 >>> 13 | m2 << 3) & 8191), U4 = i3[2] + ((m2 >>> 10 | O4 << 6) & 8191), _ = i3[3] + ((O4 >>> 7 | N3 << 9) & 8191), j2 = i3[4] + ((N3 >>> 4 | $4 << 12) & 8191), g = i3[5] + ($4 >>> 1 & 8191), w2 = i3[6] + (($4 >>> 14 | B3 << 2) & 8191), b4 = i3[7] + ((B3 >>> 11 | A4 << 5) & 8191), I3 = i3[8] + ((A4 >>> 8 | T2 << 8) & 8191), R4 = i3[9] + (T2 >>> 5 | o2), x2 = 0, C5 = x2 + S4 * c3 + L3 * (5 * d3) + U4 * (5 * p3) + _ * (5 * E5) + j2 * (5 * y5);
    x2 = C5 >>> 13, C5 &= 8191, C5 += g * (5 * h4) + w2 * (5 * f6) + b4 * (5 * l3) + I3 * (5 * u3) + R4 * (5 * a2), x2 += C5 >>> 13, C5 &= 8191;
    let P3 = x2 + S4 * a2 + L3 * c3 + U4 * (5 * d3) + _ * (5 * p3) + j2 * (5 * E5);
    x2 = P3 >>> 13, P3 &= 8191, P3 += g * (5 * y5) + w2 * (5 * h4) + b4 * (5 * f6) + I3 * (5 * l3) + R4 * (5 * u3), x2 += P3 >>> 13, P3 &= 8191;
    let k5 = x2 + S4 * u3 + L3 * a2 + U4 * c3 + _ * (5 * d3) + j2 * (5 * p3);
    x2 = k5 >>> 13, k5 &= 8191, k5 += g * (5 * E5) + w2 * (5 * y5) + b4 * (5 * h4) + I3 * (5 * f6) + R4 * (5 * l3), x2 += k5 >>> 13, k5 &= 8191;
    let M4 = x2 + S4 * l3 + L3 * u3 + U4 * a2 + _ * c3 + j2 * (5 * d3);
    x2 = M4 >>> 13, M4 &= 8191, M4 += g * (5 * p3) + w2 * (5 * E5) + b4 * (5 * y5) + I3 * (5 * h4) + R4 * (5 * f6), x2 += M4 >>> 13, M4 &= 8191;
    let D2 = x2 + S4 * f6 + L3 * l3 + U4 * u3 + _ * a2 + j2 * c3;
    x2 = D2 >>> 13, D2 &= 8191, D2 += g * (5 * d3) + w2 * (5 * p3) + b4 * (5 * E5) + I3 * (5 * y5) + R4 * (5 * h4), x2 += D2 >>> 13, D2 &= 8191;
    let z2 = x2 + S4 * h4 + L3 * f6 + U4 * l3 + _ * u3 + j2 * a2;
    x2 = z2 >>> 13, z2 &= 8191, z2 += g * c3 + w2 * (5 * d3) + b4 * (5 * p3) + I3 * (5 * E5) + R4 * (5 * y5), x2 += z2 >>> 13, z2 &= 8191;
    let Z2 = x2 + S4 * y5 + L3 * h4 + U4 * f6 + _ * l3 + j2 * u3;
    x2 = Z2 >>> 13, Z2 &= 8191, Z2 += g * a2 + w2 * c3 + b4 * (5 * d3) + I3 * (5 * p3) + R4 * (5 * E5), x2 += Z2 >>> 13, Z2 &= 8191;
    let st2 = x2 + S4 * E5 + L3 * y5 + U4 * h4 + _ * f6 + j2 * l3;
    x2 = st2 >>> 13, st2 &= 8191, st2 += g * u3 + w2 * a2 + b4 * c3 + I3 * (5 * d3) + R4 * (5 * p3), x2 += st2 >>> 13, st2 &= 8191;
    let W3 = x2 + S4 * p3 + L3 * E5 + U4 * y5 + _ * h4 + j2 * f6;
    x2 = W3 >>> 13, W3 &= 8191, W3 += g * l3 + w2 * u3 + b4 * a2 + I3 * c3 + R4 * (5 * d3), x2 += W3 >>> 13, W3 &= 8191;
    let J3 = x2 + S4 * d3 + L3 * p3 + U4 * E5 + _ * y5 + j2 * h4;
    x2 = J3 >>> 13, J3 &= 8191, J3 += g * f6 + w2 * l3 + b4 * u3 + I3 * a2 + R4 * c3, x2 += J3 >>> 13, J3 &= 8191, x2 = (x2 << 2) + x2 | 0, x2 = x2 + C5 | 0, C5 = x2 & 8191, x2 = x2 >>> 13, P3 += x2, i3[0] = C5, i3[1] = P3, i3[2] = k5, i3[3] = M4, i3[4] = D2, i3[5] = z2, i3[6] = Z2, i3[7] = st2, i3[8] = W3, i3[9] = J3;
  }
  finalize() {
    const { h: e, pad: n2 } = this, r2 = new Uint16Array(10);
    let o2 = e[1] >>> 13;
    e[1] &= 8191;
    for (let c3 = 2; c3 < 10; c3++) e[c3] += o2, o2 = e[c3] >>> 13, e[c3] &= 8191;
    e[0] += o2 * 5, o2 = e[0] >>> 13, e[0] &= 8191, e[1] += o2, o2 = e[1] >>> 13, e[1] &= 8191, e[2] += o2, r2[0] = e[0] + 5, o2 = r2[0] >>> 13, r2[0] &= 8191;
    for (let c3 = 1; c3 < 10; c3++) r2[c3] = e[c3] + o2, o2 = r2[c3] >>> 13, r2[c3] &= 8191;
    r2[9] -= 8192;
    let i3 = (o2 ^ 1) - 1;
    for (let c3 = 0; c3 < 10; c3++) r2[c3] &= i3;
    i3 = ~i3;
    for (let c3 = 0; c3 < 10; c3++) e[c3] = e[c3] & i3 | r2[c3];
    e[0] = (e[0] | e[1] << 13) & 65535, e[1] = (e[1] >>> 3 | e[2] << 10) & 65535, e[2] = (e[2] >>> 6 | e[3] << 7) & 65535, e[3] = (e[3] >>> 9 | e[4] << 4) & 65535, e[4] = (e[4] >>> 12 | e[5] << 1 | e[6] << 14) & 65535, e[5] = (e[6] >>> 2 | e[7] << 11) & 65535, e[6] = (e[7] >>> 5 | e[8] << 8) & 65535, e[7] = (e[8] >>> 8 | e[9] << 5) & 65535;
    let s = e[0] + n2[0];
    e[0] = s & 65535;
    for (let c3 = 1; c3 < 8; c3++) s = (e[c3] + n2[c3] | 0) + (s >>> 16) | 0, e[c3] = s & 65535;
    jt(r2);
  }
  update(e) {
    Or(this);
    const { buffer: n2, blockLen: r2 } = this;
    e = ze(e);
    const o2 = e.length;
    for (let i3 = 0; i3 < o2; ) {
      const s = Math.min(r2 - this.pos, o2 - i3);
      if (s === r2) {
        for (; r2 <= o2 - i3; i3 += r2) this.process(e, i3);
        continue;
      }
      n2.set(e.subarray(i3, i3 + s), this.pos), this.pos += s, i3 += s, this.pos === r2 && (this.process(n2, 0, false), this.pos = 0);
    }
    return this;
  }
  destroy() {
    jt(this.h, this.r, this.buffer, this.pad);
  }
  digestInto(e) {
    Or(this), ps(e, this), this.finished = true;
    const { buffer: n2, h: r2 } = this;
    let { pos: o2 } = this;
    if (o2) {
      for (n2[o2++] = 1; o2 < 16; o2++) n2[o2] = 0;
      this.process(n2, 0, true);
    }
    this.finalize();
    let i3 = 0;
    for (let s = 0; s < 8; s++) e[i3++] = r2[s] >>> 0, e[i3++] = r2[s] >>> 8;
    return e;
  }
  digest() {
    const { buffer: e, outputLen: n2 } = this;
    this.digestInto(e);
    const r2 = e.slice(0, n2);
    return this.destroy(), r2;
  }
};
function Ts(t) {
  const e = (r2, o2) => t(o2).update(ze(r2)).digest(), n2 = t(new Uint8Array(32));
  return e.outputLen = n2.outputLen, e.blockLen = n2.blockLen, e.create = (r2) => t(r2), e;
}
var Rs = Ts((t) => new Us(t));
function _s(t, e, n2, r2, o2, i3 = 20) {
  let s = t[0], c3 = t[1], a2 = t[2], u3 = t[3], l3 = e[0], f6 = e[1], h4 = e[2], y5 = e[3], E5 = e[4], p3 = e[5], d3 = e[6], v4 = e[7], m2 = o2, O4 = n2[0], N3 = n2[1], $4 = n2[2], B3 = s, A4 = c3, T2 = a2, S4 = u3, L3 = l3, U4 = f6, _ = h4, j2 = y5, g = E5, w2 = p3, b4 = d3, I3 = v4, R4 = m2, x2 = O4, C5 = N3, P3 = $4;
  for (let M4 = 0; M4 < i3; M4 += 2) B3 = B3 + L3 | 0, R4 = V2(R4 ^ B3, 16), g = g + R4 | 0, L3 = V2(L3 ^ g, 12), B3 = B3 + L3 | 0, R4 = V2(R4 ^ B3, 8), g = g + R4 | 0, L3 = V2(L3 ^ g, 7), A4 = A4 + U4 | 0, x2 = V2(x2 ^ A4, 16), w2 = w2 + x2 | 0, U4 = V2(U4 ^ w2, 12), A4 = A4 + U4 | 0, x2 = V2(x2 ^ A4, 8), w2 = w2 + x2 | 0, U4 = V2(U4 ^ w2, 7), T2 = T2 + _ | 0, C5 = V2(C5 ^ T2, 16), b4 = b4 + C5 | 0, _ = V2(_ ^ b4, 12), T2 = T2 + _ | 0, C5 = V2(C5 ^ T2, 8), b4 = b4 + C5 | 0, _ = V2(_ ^ b4, 7), S4 = S4 + j2 | 0, P3 = V2(P3 ^ S4, 16), I3 = I3 + P3 | 0, j2 = V2(j2 ^ I3, 12), S4 = S4 + j2 | 0, P3 = V2(P3 ^ S4, 8), I3 = I3 + P3 | 0, j2 = V2(j2 ^ I3, 7), B3 = B3 + U4 | 0, P3 = V2(P3 ^ B3, 16), b4 = b4 + P3 | 0, U4 = V2(U4 ^ b4, 12), B3 = B3 + U4 | 0, P3 = V2(P3 ^ B3, 8), b4 = b4 + P3 | 0, U4 = V2(U4 ^ b4, 7), A4 = A4 + _ | 0, R4 = V2(R4 ^ A4, 16), I3 = I3 + R4 | 0, _ = V2(_ ^ I3, 12), A4 = A4 + _ | 0, R4 = V2(R4 ^ A4, 8), I3 = I3 + R4 | 0, _ = V2(_ ^ I3, 7), T2 = T2 + j2 | 0, x2 = V2(x2 ^ T2, 16), g = g + x2 | 0, j2 = V2(j2 ^ g, 12), T2 = T2 + j2 | 0, x2 = V2(x2 ^ T2, 8), g = g + x2 | 0, j2 = V2(j2 ^ g, 7), S4 = S4 + L3 | 0, C5 = V2(C5 ^ S4, 16), w2 = w2 + C5 | 0, L3 = V2(L3 ^ w2, 12), S4 = S4 + L3 | 0, C5 = V2(C5 ^ S4, 8), w2 = w2 + C5 | 0, L3 = V2(L3 ^ w2, 7);
  let k5 = 0;
  r2[k5++] = s + B3 | 0, r2[k5++] = c3 + A4 | 0, r2[k5++] = a2 + T2 | 0, r2[k5++] = u3 + S4 | 0, r2[k5++] = l3 + L3 | 0, r2[k5++] = f6 + U4 | 0, r2[k5++] = h4 + _ | 0, r2[k5++] = y5 + j2 | 0, r2[k5++] = E5 + g | 0, r2[k5++] = p3 + w2 | 0, r2[k5++] = d3 + b4 | 0, r2[k5++] = v4 + I3 | 0, r2[k5++] = m2 + R4 | 0, r2[k5++] = O4 + x2 | 0, r2[k5++] = N3 + C5 | 0, r2[k5++] = $4 + P3 | 0;
}
var $s = Ns(_s, { counterRight: false, counterLength: 4, allowShortKeys: false });
var Ls = new Uint8Array(16);
var Rr = (t, e) => {
  t.update(e);
  const n2 = e.length % 16;
  n2 && t.update(Ls.subarray(n2));
};
var js = new Uint8Array(32);
function _r(t, e, n2, r2, o2) {
  const i3 = t(e, n2, js), s = Rs.create(i3);
  o2 && Rr(s, o2), Rr(s, r2);
  const c3 = new Uint8Array(16), a2 = gs(c3);
  Ir(a2, 0, BigInt(o2 ? o2.length : 0), true), Ir(a2, 8, BigInt(r2.length), true), s.update(c3);
  const u3 = s.digest();
  return jt(i3, c3), u3;
}
var Cs = (t) => (e, n2, r2) => ({ encrypt(i3, s) {
  const c3 = i3.length;
  s = Br(c3 + 16, s, false), s.set(i3);
  const a2 = s.subarray(0, -16);
  t(e, n2, a2, a2, 1);
  const u3 = _r(t, e, n2, a2, r2);
  return s.set(u3, c3), jt(u3), s;
}, decrypt(i3, s) {
  s = Br(i3.length - 16, s, false);
  const c3 = i3.subarray(0, -16), a2 = i3.subarray(-16), u3 = _r(t, e, n2, c3, r2);
  if (!bs(a2, u3)) throw new Error("invalid tag");
  return s.set(i3.subarray(0, -16)), t(e, n2, s, s, 1), jt(u3), s;
} });
var $r = Es({ blockSize: 64, nonceLength: 12, tagLength: 16 }, Cs($s));
var Lr = class extends ke {
  constructor(e, n2) {
    super(), this.finished = false, this.destroyed = false, Ce(e);
    const r2 = $t(n2);
    if (this.iHash = e.create(), typeof this.iHash.update != "function") throw new Error("Expected instance of class which extends utils.Hash");
    this.blockLen = this.iHash.blockLen, this.outputLen = this.iHash.outputLen;
    const o2 = this.blockLen, i3 = new Uint8Array(o2);
    i3.set(r2.length > o2 ? e.create().update(r2).digest() : r2);
    for (let s = 0; s < i3.length; s++) i3[s] ^= 54;
    this.iHash.update(i3), this.oHash = e.create();
    for (let s = 0; s < i3.length; s++) i3[s] ^= 106;
    this.oHash.update(i3), i3.fill(0);
  }
  update(e) {
    return Rt(this), this.iHash.update(e), this;
  }
  digestInto(e) {
    Rt(this), Xt(e, this.outputLen), this.finished = true, this.iHash.digestInto(e), this.oHash.update(e), this.oHash.digestInto(e), this.destroy();
  }
  digest() {
    const e = new Uint8Array(this.oHash.outputLen);
    return this.digestInto(e), e;
  }
  _cloneInto(e) {
    e || (e = Object.create(Object.getPrototypeOf(this), {}));
    const { oHash: n2, iHash: r2, finished: o2, destroyed: i3, blockLen: s, outputLen: c3 } = this;
    return e = e, e.finished = o2, e.destroyed = i3, e.blockLen = s, e.outputLen = c3, e.oHash = n2._cloneInto(e.oHash), e.iHash = r2._cloneInto(e.iHash), e;
  }
  destroy() {
    this.destroyed = true, this.oHash.destroy(), this.iHash.destroy();
  }
};
var ye = (t, e, n2) => new Lr(t, e).update(n2).digest();
ye.create = (t, e) => new Lr(t, e);
function Ps(t, e, n2) {
  return Ce(t), n2 === void 0 && (n2 = new Uint8Array(t.outputLen)), ye(t, $t(n2), $t(e));
}
var Ge = new Uint8Array([0]);
var jr = new Uint8Array();
function ks(t, e, n2, r2 = 32) {
  if (Ce(t), Wt(r2), r2 > 255 * t.outputLen) throw new Error("Length should be <= 255*HashLen");
  const o2 = Math.ceil(r2 / t.outputLen);
  n2 === void 0 && (n2 = jr);
  const i3 = new Uint8Array(o2 * t.outputLen), s = ye.create(t, e), c3 = s._cloneInto(), a2 = new Uint8Array(s.outputLen);
  for (let u3 = 0; u3 < o2; u3++) Ge[0] = u3 + 1, c3.update(u3 === 0 ? jr : a2).update(n2).update(Ge).digestInto(a2), i3.set(a2, t.outputLen * u3), s._cloneInto(c3);
  return s.destroy(), c3.destroy(), a2.fill(0), Ge.fill(0), i3.slice(0, r2);
}
var Vs = (t, e, n2, r2, o2) => ks(t, Ps(t, e, n2), r2, o2);
function Ms(t, e, n2, r2) {
  if (typeof t.setBigUint64 == "function") return t.setBigUint64(e, n2, r2);
  const o2 = BigInt(32), i3 = BigInt(4294967295), s = Number(n2 >> o2 & i3), c3 = Number(n2 & i3), a2 = r2 ? 4 : 0, u3 = r2 ? 0 : 4;
  t.setUint32(e + a2, s, r2), t.setUint32(e + u3, c3, r2);
}
function Ds(t, e, n2) {
  return t & e ^ ~t & n2;
}
function Hs(t, e, n2) {
  return t & e ^ t & n2 ^ e & n2;
}
var qs = class extends ke {
  constructor(e, n2, r2, o2) {
    super(), this.blockLen = e, this.outputLen = n2, this.padOffset = r2, this.isLE = o2, this.finished = false, this.length = 0, this.pos = 0, this.destroyed = false, this.buffer = new Uint8Array(e), this.view = Pe(this.buffer);
  }
  update(e) {
    Rt(this);
    const { view: n2, buffer: r2, blockLen: o2 } = this;
    e = $t(e);
    const i3 = e.length;
    for (let s = 0; s < i3; ) {
      const c3 = Math.min(o2 - this.pos, i3 - s);
      if (c3 === o2) {
        const a2 = Pe(e);
        for (; o2 <= i3 - s; s += o2) this.process(a2, s);
        continue;
      }
      r2.set(e.subarray(s, s + c3), this.pos), this.pos += c3, s += c3, this.pos === o2 && (this.process(n2, 0), this.pos = 0);
    }
    return this.length += e.length, this.roundClean(), this;
  }
  digestInto(e) {
    Rt(this), Gn(e, this), this.finished = true;
    const { buffer: n2, view: r2, blockLen: o2, isLE: i3 } = this;
    let { pos: s } = this;
    n2[s++] = 128, this.buffer.subarray(s).fill(0), this.padOffset > o2 - s && (this.process(r2, 0), s = 0);
    for (let f6 = s; f6 < o2; f6++) n2[f6] = 0;
    Ms(r2, o2 - 8, BigInt(this.length * 8), i3), this.process(r2, 0);
    const c3 = Pe(e), a2 = this.outputLen;
    if (a2 % 4) throw new Error("_sha2: outputLen should be aligned to 32bit");
    const u3 = a2 / 4, l3 = this.get();
    if (u3 > l3.length) throw new Error("_sha2: outputLen bigger than state");
    for (let f6 = 0; f6 < u3; f6++) c3.setUint32(4 * f6, l3[f6], i3);
  }
  digest() {
    const { buffer: e, outputLen: n2 } = this;
    this.digestInto(e);
    const r2 = e.slice(0, n2);
    return this.destroy(), r2;
  }
  _cloneInto(e) {
    e || (e = new this.constructor()), e.set(...this.get());
    const { blockLen: n2, buffer: r2, length: o2, finished: i3, destroyed: s, pos: c3 } = this;
    return e.length = o2, e.pos = c3, e.finished = i3, e.destroyed = s, o2 % n2 && e.buffer.set(r2), e;
  }
};
var Ks = new Uint32Array([1116352408, 1899447441, 3049323471, 3921009573, 961987163, 1508970993, 2453635748, 2870763221, 3624381080, 310598401, 607225278, 1426881987, 1925078388, 2162078206, 2614888103, 3248222580, 3835390401, 4022224774, 264347078, 604807628, 770255983, 1249150122, 1555081692, 1996064986, 2554220882, 2821834349, 2952996808, 3210313671, 3336571891, 3584528711, 113926993, 338241895, 666307205, 773529912, 1294757372, 1396182291, 1695183700, 1986661051, 2177026350, 2456956037, 2730485921, 2820302411, 3259730800, 3345764771, 3516065817, 3600352804, 4094571909, 275423344, 430227734, 506948616, 659060556, 883997877, 958139571, 1322822218, 1537002063, 1747873779, 1955562222, 2024104815, 2227730452, 2361852424, 2428436474, 2756734187, 3204031479, 3329325298]);
var wt = new Uint32Array([1779033703, 3144134277, 1013904242, 2773480762, 1359893119, 2600822924, 528734635, 1541459225]);
var bt = new Uint32Array(64);
var Fs = class extends qs {
  constructor() {
    super(64, 32, 8, false), this.A = wt[0] | 0, this.B = wt[1] | 0, this.C = wt[2] | 0, this.D = wt[3] | 0, this.E = wt[4] | 0, this.F = wt[5] | 0, this.G = wt[6] | 0, this.H = wt[7] | 0;
  }
  get() {
    const { A: e, B: n2, C: r2, D: o2, E: i3, F: s, G: c3, H: a2 } = this;
    return [e, n2, r2, o2, i3, s, c3, a2];
  }
  set(e, n2, r2, o2, i3, s, c3, a2) {
    this.A = e | 0, this.B = n2 | 0, this.C = r2 | 0, this.D = o2 | 0, this.E = i3 | 0, this.F = s | 0, this.G = c3 | 0, this.H = a2 | 0;
  }
  process(e, n2) {
    for (let f6 = 0; f6 < 16; f6++, n2 += 4) bt[f6] = e.getUint32(n2, false);
    for (let f6 = 16; f6 < 64; f6++) {
      const h4 = bt[f6 - 15], y5 = bt[f6 - 2], E5 = ct(h4, 7) ^ ct(h4, 18) ^ h4 >>> 3, p3 = ct(y5, 17) ^ ct(y5, 19) ^ y5 >>> 10;
      bt[f6] = p3 + bt[f6 - 7] + E5 + bt[f6 - 16] | 0;
    }
    let { A: r2, B: o2, C: i3, D: s, E: c3, F: a2, G: u3, H: l3 } = this;
    for (let f6 = 0; f6 < 64; f6++) {
      const h4 = ct(c3, 6) ^ ct(c3, 11) ^ ct(c3, 25), y5 = l3 + h4 + Ds(c3, a2, u3) + Ks[f6] + bt[f6] | 0, p3 = (ct(r2, 2) ^ ct(r2, 13) ^ ct(r2, 22)) + Hs(r2, o2, i3) | 0;
      l3 = u3, u3 = a2, a2 = c3, c3 = s + y5 | 0, s = i3, i3 = o2, o2 = r2, r2 = y5 + p3 | 0;
    }
    r2 = r2 + this.A | 0, o2 = o2 + this.B | 0, i3 = i3 + this.C | 0, s = s + this.D | 0, c3 = c3 + this.E | 0, a2 = a2 + this.F | 0, u3 = u3 + this.G | 0, l3 = l3 + this.H | 0, this.set(r2, o2, i3, s, c3, a2, u3, l3);
  }
  roundClean() {
    bt.fill(0);
  }
  destroy() {
    this.set(0, 0, 0, 0, 0, 0, 0, 0), this.buffer.fill(0);
  }
};
var Qt = Qn(() => new Fs());
var me = BigInt(0);
var we = BigInt(1);
var zs = BigInt(2);
function St(t) {
  return t instanceof Uint8Array || ArrayBuffer.isView(t) && t.constructor.name === "Uint8Array";
}
function te(t) {
  if (!St(t)) throw new Error("Uint8Array expected");
}
function Ct(t, e) {
  if (typeof e != "boolean") throw new Error(t + " boolean expected, got " + e);
}
var Zs = Array.from({ length: 256 }, (t, e) => e.toString(16).padStart(2, "0"));
function Pt(t) {
  te(t);
  let e = "";
  for (let n2 = 0; n2 < t.length; n2++) e += Zs[t[n2]];
  return e;
}
function kt(t) {
  const e = t.toString(16);
  return e.length & 1 ? "0" + e : e;
}
function We(t) {
  if (typeof t != "string") throw new Error("hex string expected, got " + typeof t);
  return t === "" ? me : BigInt("0x" + t);
}
var ut = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };
function Cr(t) {
  if (t >= ut._0 && t <= ut._9) return t - ut._0;
  if (t >= ut.A && t <= ut.F) return t - (ut.A - 10);
  if (t >= ut.a && t <= ut.f) return t - (ut.a - 10);
}
function Vt(t) {
  if (typeof t != "string") throw new Error("hex string expected, got " + typeof t);
  const e = t.length, n2 = e / 2;
  if (e % 2) throw new Error("hex string expected, got unpadded hex of length " + e);
  const r2 = new Uint8Array(n2);
  for (let o2 = 0, i3 = 0; o2 < n2; o2++, i3 += 2) {
    const s = Cr(t.charCodeAt(i3)), c3 = Cr(t.charCodeAt(i3 + 1));
    if (s === void 0 || c3 === void 0) {
      const a2 = t[i3] + t[i3 + 1];
      throw new Error('hex string expected, got non-hex character "' + a2 + '" at index ' + i3);
    }
    r2[o2] = s * 16 + c3;
  }
  return r2;
}
function Ot(t) {
  return We(Pt(t));
}
function ee(t) {
  return te(t), We(Pt(Uint8Array.from(t).reverse()));
}
function Mt(t, e) {
  return Vt(t.toString(16).padStart(e * 2, "0"));
}
function be(t, e) {
  return Mt(t, e).reverse();
}
function Ys(t) {
  return Vt(kt(t));
}
function et(t, e, n2) {
  let r2;
  if (typeof e == "string") try {
    r2 = Vt(e);
  } catch (i3) {
    throw new Error(t + " must be hex string or Uint8Array, cause: " + i3);
  }
  else if (St(e)) r2 = Uint8Array.from(e);
  else throw new Error(t + " must be hex string or Uint8Array");
  const o2 = r2.length;
  if (typeof n2 == "number" && o2 !== n2) throw new Error(t + " of length " + n2 + " expected, got " + o2);
  return r2;
}
function ne(...t) {
  let e = 0;
  for (let r2 = 0; r2 < t.length; r2++) {
    const o2 = t[r2];
    te(o2), e += o2.length;
  }
  const n2 = new Uint8Array(e);
  for (let r2 = 0, o2 = 0; r2 < t.length; r2++) {
    const i3 = t[r2];
    n2.set(i3, o2), o2 += i3.length;
  }
  return n2;
}
function Gs(t, e) {
  if (t.length !== e.length) return false;
  let n2 = 0;
  for (let r2 = 0; r2 < t.length; r2++) n2 |= t[r2] ^ e[r2];
  return n2 === 0;
}
function Ws(t) {
  if (typeof t != "string") throw new Error("string expected");
  return new Uint8Array(new TextEncoder().encode(t));
}
var Xe = (t) => typeof t == "bigint" && me <= t;
function Ee(t, e, n2) {
  return Xe(t) && Xe(e) && Xe(n2) && e <= t && t < n2;
}
function ft(t, e, n2, r2) {
  if (!Ee(e, n2, r2)) throw new Error("expected valid " + t + ": " + n2 + " <= n < " + r2 + ", got " + e);
}
function Pr(t) {
  let e;
  for (e = 0; t > me; t >>= we, e += 1) ;
  return e;
}
function Xs(t, e) {
  return t >> BigInt(e) & we;
}
function Js(t, e, n2) {
  return t | (n2 ? we : me) << BigInt(e);
}
var Je = (t) => (zs << BigInt(t - 1)) - we;
var Qe2 = (t) => new Uint8Array(t);
var kr = (t) => Uint8Array.from(t);
function Vr(t, e, n2) {
  if (typeof t != "number" || t < 2) throw new Error("hashLen must be a number");
  if (typeof e != "number" || e < 2) throw new Error("qByteLen must be a number");
  if (typeof n2 != "function") throw new Error("hmacFn must be a function");
  let r2 = Qe2(t), o2 = Qe2(t), i3 = 0;
  const s = () => {
    r2.fill(1), o2.fill(0), i3 = 0;
  }, c3 = (...f6) => n2(o2, r2, ...f6), a2 = (f6 = Qe2()) => {
    o2 = c3(kr([0]), f6), r2 = c3(), f6.length !== 0 && (o2 = c3(kr([1]), f6), r2 = c3());
  }, u3 = () => {
    if (i3++ >= 1e3) throw new Error("drbg: tried 1000 values");
    let f6 = 0;
    const h4 = [];
    for (; f6 < e; ) {
      r2 = c3();
      const y5 = r2.slice();
      h4.push(y5), f6 += r2.length;
    }
    return ne(...h4);
  };
  return (f6, h4) => {
    s(), a2(f6);
    let y5;
    for (; !(y5 = h4(u3())); ) a2();
    return s(), y5;
  };
}
var Qs = { bigint: (t) => typeof t == "bigint", function: (t) => typeof t == "function", boolean: (t) => typeof t == "boolean", string: (t) => typeof t == "string", stringOrUint8Array: (t) => typeof t == "string" || St(t), isSafeInteger: (t) => Number.isSafeInteger(t), array: (t) => Array.isArray(t), field: (t, e) => e.Fp.isValid(t), hash: (t) => typeof t == "function" && Number.isSafeInteger(t.outputLen) };
function Dt(t, e, n2 = {}) {
  const r2 = (o2, i3, s) => {
    const c3 = Qs[i3];
    if (typeof c3 != "function") throw new Error("invalid validator function");
    const a2 = t[o2];
    if (!(s && a2 === void 0) && !c3(a2, t)) throw new Error("param " + String(o2) + " is invalid. Expected " + i3 + ", got " + a2);
  };
  for (const [o2, i3] of Object.entries(e)) r2(o2, i3, false);
  for (const [o2, i3] of Object.entries(n2)) r2(o2, i3, true);
  return t;
}
var tc = () => {
  throw new Error("not implemented");
};
function tn(t) {
  const e = /* @__PURE__ */ new WeakMap();
  return (n2, ...r2) => {
    const o2 = e.get(n2);
    if (o2 !== void 0) return o2;
    const i3 = t(n2, ...r2);
    return e.set(n2, i3), i3;
  };
}
var ec = Object.freeze({ __proto__: null, isBytes: St, abytes: te, abool: Ct, bytesToHex: Pt, numberToHexUnpadded: kt, hexToNumber: We, hexToBytes: Vt, bytesToNumberBE: Ot, bytesToNumberLE: ee, numberToBytesBE: Mt, numberToBytesLE: be, numberToVarBytesBE: Ys, ensureBytes: et, concatBytes: ne, equalBytes: Gs, utf8ToBytes: Ws, inRange: Ee, aInRange: ft, bitLen: Pr, bitGet: Xs, bitSet: Js, bitMask: Je, createHmacDrbg: Vr, validateObject: Dt, notImplemented: tc, memoized: tn });
var q = BigInt(0);
var H = BigInt(1);
var At = BigInt(2);
var nc = BigInt(3);
var en = BigInt(4);
var Mr = BigInt(5);
var Dr = BigInt(8);
function X(t, e) {
  const n2 = t % e;
  return n2 >= q ? n2 : e + n2;
}
function Hr(t, e, n2) {
  if (e < q) throw new Error("invalid exponent, negatives unsupported");
  if (n2 <= q) throw new Error("invalid modulus");
  if (n2 === H) return q;
  let r2 = H;
  for (; e > q; ) e & H && (r2 = r2 * t % n2), t = t * t % n2, e >>= H;
  return r2;
}
function it(t, e, n2) {
  let r2 = t;
  for (; e-- > q; ) r2 *= r2, r2 %= n2;
  return r2;
}
function nn(t, e) {
  if (t === q) throw new Error("invert: expected non-zero number");
  if (e <= q) throw new Error("invert: expected positive modulus, got " + e);
  let n2 = X(t, e), r2 = e, o2 = q, i3 = H;
  for (; n2 !== q; ) {
    const c3 = r2 / n2, a2 = r2 % n2, u3 = o2 - i3 * c3;
    r2 = n2, n2 = a2, o2 = i3, i3 = u3;
  }
  if (r2 !== H) throw new Error("invert: does not exist");
  return X(o2, e);
}
function rc(t) {
  const e = (t - H) / At;
  let n2, r2, o2;
  for (n2 = t - H, r2 = 0; n2 % At === q; n2 /= At, r2++) ;
  for (o2 = At; o2 < t && Hr(o2, e, t) !== t - H; o2++) if (o2 > 1e3) throw new Error("Cannot find square root: likely non-prime P");
  if (r2 === 1) {
    const s = (t + H) / en;
    return function(a2, u3) {
      const l3 = a2.pow(u3, s);
      if (!a2.eql(a2.sqr(l3), u3)) throw new Error("Cannot find square root");
      return l3;
    };
  }
  const i3 = (n2 + H) / At;
  return function(c3, a2) {
    if (c3.pow(a2, e) === c3.neg(c3.ONE)) throw new Error("Cannot find square root");
    let u3 = r2, l3 = c3.pow(c3.mul(c3.ONE, o2), n2), f6 = c3.pow(a2, i3), h4 = c3.pow(a2, n2);
    for (; !c3.eql(h4, c3.ONE); ) {
      if (c3.eql(h4, c3.ZERO)) return c3.ZERO;
      let y5 = 1;
      for (let p3 = c3.sqr(h4); y5 < u3 && !c3.eql(p3, c3.ONE); y5++) p3 = c3.sqr(p3);
      const E5 = c3.pow(l3, H << BigInt(u3 - y5 - 1));
      l3 = c3.sqr(E5), f6 = c3.mul(f6, E5), h4 = c3.mul(h4, l3), u3 = y5;
    }
    return f6;
  };
}
function oc(t) {
  if (t % en === nc) {
    const e = (t + H) / en;
    return function(r2, o2) {
      const i3 = r2.pow(o2, e);
      if (!r2.eql(r2.sqr(i3), o2)) throw new Error("Cannot find square root");
      return i3;
    };
  }
  if (t % Dr === Mr) {
    const e = (t - Mr) / Dr;
    return function(r2, o2) {
      const i3 = r2.mul(o2, At), s = r2.pow(i3, e), c3 = r2.mul(o2, s), a2 = r2.mul(r2.mul(c3, At), s), u3 = r2.mul(c3, r2.sub(a2, r2.ONE));
      if (!r2.eql(r2.sqr(u3), o2)) throw new Error("Cannot find square root");
      return u3;
    };
  }
  return rc(t);
}
var ic = ["create", "isValid", "is0", "neg", "inv", "sqrt", "sqr", "eql", "add", "sub", "mul", "pow", "div", "addN", "subN", "mulN", "sqrN"];
function sc(t) {
  const e = { ORDER: "bigint", MASK: "bigint", BYTES: "isSafeInteger", BITS: "isSafeInteger" }, n2 = ic.reduce((r2, o2) => (r2[o2] = "function", r2), e);
  return Dt(t, n2);
}
function cc(t, e, n2) {
  if (n2 < q) throw new Error("invalid exponent, negatives unsupported");
  if (n2 === q) return t.ONE;
  if (n2 === H) return e;
  let r2 = t.ONE, o2 = e;
  for (; n2 > q; ) n2 & H && (r2 = t.mul(r2, o2)), o2 = t.sqr(o2), n2 >>= H;
  return r2;
}
function ac(t, e) {
  const n2 = new Array(e.length), r2 = e.reduce((i3, s, c3) => t.is0(s) ? i3 : (n2[c3] = i3, t.mul(i3, s)), t.ONE), o2 = t.inv(r2);
  return e.reduceRight((i3, s, c3) => t.is0(s) ? i3 : (n2[c3] = t.mul(i3, n2[c3]), t.mul(i3, s)), o2), n2;
}
function qr(t, e) {
  const n2 = e !== void 0 ? e : t.toString(2).length, r2 = Math.ceil(n2 / 8);
  return { nBitLength: n2, nByteLength: r2 };
}
function Kr2(t, e, n2 = false, r2 = {}) {
  if (t <= q) throw new Error("invalid field: expected ORDER > 0, got " + t);
  const { nBitLength: o2, nByteLength: i3 } = qr(t, e);
  if (i3 > 2048) throw new Error("invalid field: expected ORDER of <= 2048 bytes");
  let s;
  const c3 = Object.freeze({ ORDER: t, isLE: n2, BITS: o2, BYTES: i3, MASK: Je(o2), ZERO: q, ONE: H, create: (a2) => X(a2, t), isValid: (a2) => {
    if (typeof a2 != "bigint") throw new Error("invalid field element: expected bigint, got " + typeof a2);
    return q <= a2 && a2 < t;
  }, is0: (a2) => a2 === q, isOdd: (a2) => (a2 & H) === H, neg: (a2) => X(-a2, t), eql: (a2, u3) => a2 === u3, sqr: (a2) => X(a2 * a2, t), add: (a2, u3) => X(a2 + u3, t), sub: (a2, u3) => X(a2 - u3, t), mul: (a2, u3) => X(a2 * u3, t), pow: (a2, u3) => cc(c3, a2, u3), div: (a2, u3) => X(a2 * nn(u3, t), t), sqrN: (a2) => a2 * a2, addN: (a2, u3) => a2 + u3, subN: (a2, u3) => a2 - u3, mulN: (a2, u3) => a2 * u3, inv: (a2) => nn(a2, t), sqrt: r2.sqrt || ((a2) => (s || (s = oc(t)), s(c3, a2))), invertBatch: (a2) => ac(c3, a2), cmov: (a2, u3, l3) => l3 ? u3 : a2, toBytes: (a2) => n2 ? be(a2, i3) : Mt(a2, i3), fromBytes: (a2) => {
    if (a2.length !== i3) throw new Error("Field.fromBytes: expected " + i3 + " bytes, got " + a2.length);
    return n2 ? ee(a2) : Ot(a2);
  } });
  return Object.freeze(c3);
}
function Fr(t) {
  if (typeof t != "bigint") throw new Error("field order must be bigint");
  const e = t.toString(2).length;
  return Math.ceil(e / 8);
}
function zr(t) {
  const e = Fr(t);
  return e + Math.ceil(e / 2);
}
function uc(t, e, n2 = false) {
  const r2 = t.length, o2 = Fr(e), i3 = zr(e);
  if (r2 < 16 || r2 < i3 || r2 > 1024) throw new Error("expected " + i3 + "-1024 bytes of input, got " + r2);
  const s = n2 ? ee(t) : Ot(t), c3 = X(s, e - H) + H;
  return n2 ? be(c3, o2) : Mt(c3, o2);
}
var Zr = BigInt(0);
var ve = BigInt(1);
function rn(t, e) {
  const n2 = e.negate();
  return t ? n2 : e;
}
function Yr(t, e) {
  if (!Number.isSafeInteger(t) || t <= 0 || t > e) throw new Error("invalid window size, expected [1.." + e + "], got W=" + t);
}
function on(t, e) {
  Yr(t, e);
  const n2 = Math.ceil(e / t) + 1, r2 = 2 ** (t - 1);
  return { windows: n2, windowSize: r2 };
}
function fc(t, e) {
  if (!Array.isArray(t)) throw new Error("array expected");
  t.forEach((n2, r2) => {
    if (!(n2 instanceof e)) throw new Error("invalid point at index " + r2);
  });
}
function lc(t, e) {
  if (!Array.isArray(t)) throw new Error("array of scalars expected");
  t.forEach((n2, r2) => {
    if (!e.isValid(n2)) throw new Error("invalid scalar at index " + r2);
  });
}
var sn2 = /* @__PURE__ */ new WeakMap();
var Gr = /* @__PURE__ */ new WeakMap();
function cn(t) {
  return Gr.get(t) || 1;
}
function dc(t, e) {
  return { constTimeNegate: rn, hasPrecomputes(n2) {
    return cn(n2) !== 1;
  }, unsafeLadder(n2, r2, o2 = t.ZERO) {
    let i3 = n2;
    for (; r2 > Zr; ) r2 & ve && (o2 = o2.add(i3)), i3 = i3.double(), r2 >>= ve;
    return o2;
  }, precomputeWindow(n2, r2) {
    const { windows: o2, windowSize: i3 } = on(r2, e), s = [];
    let c3 = n2, a2 = c3;
    for (let u3 = 0; u3 < o2; u3++) {
      a2 = c3, s.push(a2);
      for (let l3 = 1; l3 < i3; l3++) a2 = a2.add(c3), s.push(a2);
      c3 = a2.double();
    }
    return s;
  }, wNAF(n2, r2, o2) {
    const { windows: i3, windowSize: s } = on(n2, e);
    let c3 = t.ZERO, a2 = t.BASE;
    const u3 = BigInt(2 ** n2 - 1), l3 = 2 ** n2, f6 = BigInt(n2);
    for (let h4 = 0; h4 < i3; h4++) {
      const y5 = h4 * s;
      let E5 = Number(o2 & u3);
      o2 >>= f6, E5 > s && (E5 -= l3, o2 += ve);
      const p3 = y5, d3 = y5 + Math.abs(E5) - 1, v4 = h4 % 2 !== 0, m2 = E5 < 0;
      E5 === 0 ? a2 = a2.add(rn(v4, r2[p3])) : c3 = c3.add(rn(m2, r2[d3]));
    }
    return { p: c3, f: a2 };
  }, wNAFUnsafe(n2, r2, o2, i3 = t.ZERO) {
    const { windows: s, windowSize: c3 } = on(n2, e), a2 = BigInt(2 ** n2 - 1), u3 = 2 ** n2, l3 = BigInt(n2);
    for (let f6 = 0; f6 < s; f6++) {
      const h4 = f6 * c3;
      if (o2 === Zr) break;
      let y5 = Number(o2 & a2);
      if (o2 >>= l3, y5 > c3 && (y5 -= u3, o2 += ve), y5 === 0) continue;
      let E5 = r2[h4 + Math.abs(y5) - 1];
      y5 < 0 && (E5 = E5.negate()), i3 = i3.add(E5);
    }
    return i3;
  }, getPrecomputes(n2, r2, o2) {
    let i3 = sn2.get(r2);
    return i3 || (i3 = this.precomputeWindow(r2, n2), n2 !== 1 && sn2.set(r2, o2(i3))), i3;
  }, wNAFCached(n2, r2, o2) {
    const i3 = cn(n2);
    return this.wNAF(i3, this.getPrecomputes(i3, n2, o2), r2);
  }, wNAFCachedUnsafe(n2, r2, o2, i3) {
    const s = cn(n2);
    return s === 1 ? this.unsafeLadder(n2, r2, i3) : this.wNAFUnsafe(s, this.getPrecomputes(s, n2, o2), r2, i3);
  }, setWindowSize(n2, r2) {
    Yr(r2, e), Gr.set(n2, r2), sn2.delete(n2);
  } };
}
function hc(t, e, n2, r2) {
  if (fc(n2, t), lc(r2, e), n2.length !== r2.length) throw new Error("arrays of points and scalars must have equal length");
  const o2 = t.ZERO, i3 = Pr(BigInt(n2.length)), s = i3 > 12 ? i3 - 3 : i3 > 4 ? i3 - 2 : i3 ? 2 : 1, c3 = (1 << s) - 1, a2 = new Array(c3 + 1).fill(o2), u3 = Math.floor((e.BITS - 1) / s) * s;
  let l3 = o2;
  for (let f6 = u3; f6 >= 0; f6 -= s) {
    a2.fill(o2);
    for (let y5 = 0; y5 < r2.length; y5++) {
      const E5 = r2[y5], p3 = Number(E5 >> BigInt(f6) & BigInt(c3));
      a2[p3] = a2[p3].add(n2[y5]);
    }
    let h4 = o2;
    for (let y5 = a2.length - 1, E5 = o2; y5 > 0; y5--) E5 = E5.add(a2[y5]), h4 = h4.add(E5);
    if (l3 = l3.add(h4), f6 !== 0) for (let y5 = 0; y5 < s; y5++) l3 = l3.double();
  }
  return l3;
}
function Wr(t) {
  return sc(t.Fp), Dt(t, { n: "bigint", h: "bigint", Gx: "field", Gy: "field" }, { nBitLength: "isSafeInteger", nByteLength: "isSafeInteger" }), Object.freeze({ ...qr(t.n, t.nBitLength), ...t, p: t.Fp.ORDER });
}
BigInt(0), BigInt(1), BigInt(2), BigInt(8);
var Ht = BigInt(0);
var an = BigInt(1);
function pc(t) {
  return Dt(t, { a: "bigint" }, { montgomeryBits: "isSafeInteger", nByteLength: "isSafeInteger", adjustScalarBytes: "function", domain: "function", powPminus2: "function", Gu: "bigint" }), Object.freeze({ ...t });
}
function gc(t) {
  const e = pc(t), { P: n2 } = e, r2 = (m2) => X(m2, n2), o2 = e.montgomeryBits, i3 = Math.ceil(o2 / 8), s = e.nByteLength, c3 = e.adjustScalarBytes || ((m2) => m2), a2 = e.powPminus2 || ((m2) => Hr(m2, n2 - BigInt(2), n2));
  function u3(m2, O4, N3) {
    const $4 = r2(m2 * (O4 - N3));
    return O4 = r2(O4 - $4), N3 = r2(N3 + $4), [O4, N3];
  }
  const l3 = (e.a - BigInt(2)) / BigInt(4);
  function f6(m2, O4) {
    ft("u", m2, Ht, n2), ft("scalar", O4, Ht, n2);
    const N3 = O4, $4 = m2;
    let B3 = an, A4 = Ht, T2 = m2, S4 = an, L3 = Ht, U4;
    for (let j2 = BigInt(o2 - 1); j2 >= Ht; j2--) {
      const g = N3 >> j2 & an;
      L3 ^= g, U4 = u3(L3, B3, T2), B3 = U4[0], T2 = U4[1], U4 = u3(L3, A4, S4), A4 = U4[0], S4 = U4[1], L3 = g;
      const w2 = B3 + A4, b4 = r2(w2 * w2), I3 = B3 - A4, R4 = r2(I3 * I3), x2 = b4 - R4, C5 = T2 + S4, P3 = T2 - S4, k5 = r2(P3 * w2), M4 = r2(C5 * I3), D2 = k5 + M4, z2 = k5 - M4;
      T2 = r2(D2 * D2), S4 = r2($4 * r2(z2 * z2)), B3 = r2(b4 * R4), A4 = r2(x2 * (b4 + r2(l3 * x2)));
    }
    U4 = u3(L3, B3, T2), B3 = U4[0], T2 = U4[1], U4 = u3(L3, A4, S4), A4 = U4[0], S4 = U4[1];
    const _ = a2(A4);
    return r2(B3 * _);
  }
  function h4(m2) {
    return be(r2(m2), i3);
  }
  function y5(m2) {
    const O4 = et("u coordinate", m2, i3);
    return s === 32 && (O4[31] &= 127), ee(O4);
  }
  function E5(m2) {
    const O4 = et("scalar", m2), N3 = O4.length;
    if (N3 !== i3 && N3 !== s) {
      let $4 = "" + i3 + " or " + s;
      throw new Error("invalid scalar, expected " + $4 + " bytes, got " + N3);
    }
    return ee(c3(O4));
  }
  function p3(m2, O4) {
    const N3 = y5(O4), $4 = E5(m2), B3 = f6(N3, $4);
    if (B3 === Ht) throw new Error("invalid private or public key received");
    return h4(B3);
  }
  const d3 = h4(e.Gu);
  function v4(m2) {
    return p3(m2, d3);
  }
  return { scalarMult: p3, scalarMultBase: v4, getSharedSecret: (m2, O4) => p3(m2, O4), getPublicKey: (m2) => v4(m2), utils: { randomPrivateKey: () => e.randomBytes(e.nByteLength) }, GuBytes: d3 };
}
var un = BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949");
BigInt(0);
var yc = BigInt(1);
var Xr = BigInt(2);
var mc = BigInt(3);
var wc = BigInt(5);
BigInt(8);
function bc(t) {
  const e = BigInt(10), n2 = BigInt(20), r2 = BigInt(40), o2 = BigInt(80), i3 = un, c3 = t * t % i3 * t % i3, a2 = it(c3, Xr, i3) * c3 % i3, u3 = it(a2, yc, i3) * t % i3, l3 = it(u3, wc, i3) * u3 % i3, f6 = it(l3, e, i3) * l3 % i3, h4 = it(f6, n2, i3) * f6 % i3, y5 = it(h4, r2, i3) * h4 % i3, E5 = it(y5, o2, i3) * y5 % i3, p3 = it(E5, o2, i3) * y5 % i3, d3 = it(p3, e, i3) * l3 % i3;
  return { pow_p_5_8: it(d3, Xr, i3) * t % i3, b2: c3 };
}
function Ec(t) {
  return t[0] &= 248, t[31] &= 127, t[31] |= 64, t;
}
var fn = gc({ P: un, a: BigInt(486662), montgomeryBits: 255, nByteLength: 32, Gu: BigInt(9), powPminus2: (t) => {
  const e = un, { pow_p_5_8: n2, b2: r2 } = bc(t);
  return X(it(n2, mc, e) * r2, e);
}, adjustScalarBytes: Ec, randomBytes: Lt });
function Jr(t) {
  t.lowS !== void 0 && Ct("lowS", t.lowS), t.prehash !== void 0 && Ct("prehash", t.prehash);
}
function vc(t) {
  const e = Wr(t);
  Dt(e, { a: "field", b: "field" }, { allowedPrivateKeyLengths: "array", wrapPrivateKey: "boolean", isTorsionFree: "function", clearCofactor: "function", allowInfinityPoint: "boolean", fromBytes: "function", toBytes: "function" });
  const { endo: n2, Fp: r2, a: o2 } = e;
  if (n2) {
    if (!r2.eql(o2, r2.ZERO)) throw new Error("invalid endomorphism, can only be defined for Koblitz curves that have a=0");
    if (typeof n2 != "object" || typeof n2.beta != "bigint" || typeof n2.splitScalar != "function") throw new Error("invalid endomorphism, expected beta: bigint and splitScalar: function");
  }
  return Object.freeze({ ...e });
}
var { bytesToNumberBE: xc, hexToBytes: Sc } = ec;
var Oc = class extends Error {
  constructor(e = "") {
    super(e);
  }
};
var lt = { Err: Oc, _tlv: { encode: (t, e) => {
  const { Err: n2 } = lt;
  if (t < 0 || t > 256) throw new n2("tlv.encode: wrong tag");
  if (e.length & 1) throw new n2("tlv.encode: unpadded data");
  const r2 = e.length / 2, o2 = kt(r2);
  if (o2.length / 2 & 128) throw new n2("tlv.encode: long form length too big");
  const i3 = r2 > 127 ? kt(o2.length / 2 | 128) : "";
  return kt(t) + i3 + o2 + e;
}, decode(t, e) {
  const { Err: n2 } = lt;
  let r2 = 0;
  if (t < 0 || t > 256) throw new n2("tlv.encode: wrong tag");
  if (e.length < 2 || e[r2++] !== t) throw new n2("tlv.decode: wrong tlv");
  const o2 = e[r2++], i3 = !!(o2 & 128);
  let s = 0;
  if (!i3) s = o2;
  else {
    const a2 = o2 & 127;
    if (!a2) throw new n2("tlv.decode(long): indefinite length not supported");
    if (a2 > 4) throw new n2("tlv.decode(long): byte length is too big");
    const u3 = e.subarray(r2, r2 + a2);
    if (u3.length !== a2) throw new n2("tlv.decode: length bytes not complete");
    if (u3[0] === 0) throw new n2("tlv.decode(long): zero leftmost byte");
    for (const l3 of u3) s = s << 8 | l3;
    if (r2 += a2, s < 128) throw new n2("tlv.decode(long): not minimal encoding");
  }
  const c3 = e.subarray(r2, r2 + s);
  if (c3.length !== s) throw new n2("tlv.decode: wrong value length");
  return { v: c3, l: e.subarray(r2 + s) };
} }, _int: { encode(t) {
  const { Err: e } = lt;
  if (t < dt) throw new e("integer: negative integers are not allowed");
  let n2 = kt(t);
  if (Number.parseInt(n2[0], 16) & 8 && (n2 = "00" + n2), n2.length & 1) throw new e("unexpected DER parsing assertion: unpadded hex");
  return n2;
}, decode(t) {
  const { Err: e } = lt;
  if (t[0] & 128) throw new e("invalid signature integer: negative");
  if (t[0] === 0 && !(t[1] & 128)) throw new e("invalid signature integer: unnecessary leading zero");
  return xc(t);
} }, toSig(t) {
  const { Err: e, _int: n2, _tlv: r2 } = lt, o2 = typeof t == "string" ? Sc(t) : t;
  te(o2);
  const { v: i3, l: s } = r2.decode(48, o2);
  if (s.length) throw new e("invalid signature: left bytes after parsing");
  const { v: c3, l: a2 } = r2.decode(2, i3), { v: u3, l: l3 } = r2.decode(2, a2);
  if (l3.length) throw new e("invalid signature: left bytes after parsing");
  return { r: n2.decode(c3), s: n2.decode(u3) };
}, hexFromSig(t) {
  const { _tlv: e, _int: n2 } = lt, r2 = e.encode(2, n2.encode(t.r)), o2 = e.encode(2, n2.encode(t.s)), i3 = r2 + o2;
  return e.encode(48, i3);
} };
var dt = BigInt(0);
var K = BigInt(1);
BigInt(2);
var Qr = BigInt(3);
BigInt(4);
function Ac(t) {
  const e = vc(t), { Fp: n2 } = e, r2 = Kr2(e.n, e.nBitLength), o2 = e.toBytes || ((p3, d3, v4) => {
    const m2 = d3.toAffine();
    return ne(Uint8Array.from([4]), n2.toBytes(m2.x), n2.toBytes(m2.y));
  }), i3 = e.fromBytes || ((p3) => {
    const d3 = p3.subarray(1), v4 = n2.fromBytes(d3.subarray(0, n2.BYTES)), m2 = n2.fromBytes(d3.subarray(n2.BYTES, 2 * n2.BYTES));
    return { x: v4, y: m2 };
  });
  function s(p3) {
    const { a: d3, b: v4 } = e, m2 = n2.sqr(p3), O4 = n2.mul(m2, p3);
    return n2.add(n2.add(O4, n2.mul(p3, d3)), v4);
  }
  if (!n2.eql(n2.sqr(e.Gy), s(e.Gx))) throw new Error("bad generator point: equation left != right");
  function c3(p3) {
    return Ee(p3, K, e.n);
  }
  function a2(p3) {
    const { allowedPrivateKeyLengths: d3, nByteLength: v4, wrapPrivateKey: m2, n: O4 } = e;
    if (d3 && typeof p3 != "bigint") {
      if (St(p3) && (p3 = Pt(p3)), typeof p3 != "string" || !d3.includes(p3.length)) throw new Error("invalid private key");
      p3 = p3.padStart(v4 * 2, "0");
    }
    let N3;
    try {
      N3 = typeof p3 == "bigint" ? p3 : Ot(et("private key", p3, v4));
    } catch {
      throw new Error("invalid private key, expected hex or " + v4 + " bytes, got " + typeof p3);
    }
    return m2 && (N3 = X(N3, O4)), ft("private key", N3, K, O4), N3;
  }
  function u3(p3) {
    if (!(p3 instanceof h4)) throw new Error("ProjectivePoint expected");
  }
  const l3 = tn((p3, d3) => {
    const { px: v4, py: m2, pz: O4 } = p3;
    if (n2.eql(O4, n2.ONE)) return { x: v4, y: m2 };
    const N3 = p3.is0();
    d3 == null && (d3 = N3 ? n2.ONE : n2.inv(O4));
    const $4 = n2.mul(v4, d3), B3 = n2.mul(m2, d3), A4 = n2.mul(O4, d3);
    if (N3) return { x: n2.ZERO, y: n2.ZERO };
    if (!n2.eql(A4, n2.ONE)) throw new Error("invZ was invalid");
    return { x: $4, y: B3 };
  }), f6 = tn((p3) => {
    if (p3.is0()) {
      if (e.allowInfinityPoint && !n2.is0(p3.py)) return;
      throw new Error("bad point: ZERO");
    }
    const { x: d3, y: v4 } = p3.toAffine();
    if (!n2.isValid(d3) || !n2.isValid(v4)) throw new Error("bad point: x or y not FE");
    const m2 = n2.sqr(v4), O4 = s(d3);
    if (!n2.eql(m2, O4)) throw new Error("bad point: equation left != right");
    if (!p3.isTorsionFree()) throw new Error("bad point: not in prime-order subgroup");
    return true;
  });
  class h4 {
    constructor(d3, v4, m2) {
      if (this.px = d3, this.py = v4, this.pz = m2, d3 == null || !n2.isValid(d3)) throw new Error("x required");
      if (v4 == null || !n2.isValid(v4)) throw new Error("y required");
      if (m2 == null || !n2.isValid(m2)) throw new Error("z required");
      Object.freeze(this);
    }
    static fromAffine(d3) {
      const { x: v4, y: m2 } = d3 || {};
      if (!d3 || !n2.isValid(v4) || !n2.isValid(m2)) throw new Error("invalid affine point");
      if (d3 instanceof h4) throw new Error("projective point not allowed");
      const O4 = (N3) => n2.eql(N3, n2.ZERO);
      return O4(v4) && O4(m2) ? h4.ZERO : new h4(v4, m2, n2.ONE);
    }
    get x() {
      return this.toAffine().x;
    }
    get y() {
      return this.toAffine().y;
    }
    static normalizeZ(d3) {
      const v4 = n2.invertBatch(d3.map((m2) => m2.pz));
      return d3.map((m2, O4) => m2.toAffine(v4[O4])).map(h4.fromAffine);
    }
    static fromHex(d3) {
      const v4 = h4.fromAffine(i3(et("pointHex", d3)));
      return v4.assertValidity(), v4;
    }
    static fromPrivateKey(d3) {
      return h4.BASE.multiply(a2(d3));
    }
    static msm(d3, v4) {
      return hc(h4, r2, d3, v4);
    }
    _setWindowSize(d3) {
      E5.setWindowSize(this, d3);
    }
    assertValidity() {
      f6(this);
    }
    hasEvenY() {
      const { y: d3 } = this.toAffine();
      if (n2.isOdd) return !n2.isOdd(d3);
      throw new Error("Field doesn't support isOdd");
    }
    equals(d3) {
      u3(d3);
      const { px: v4, py: m2, pz: O4 } = this, { px: N3, py: $4, pz: B3 } = d3, A4 = n2.eql(n2.mul(v4, B3), n2.mul(N3, O4)), T2 = n2.eql(n2.mul(m2, B3), n2.mul($4, O4));
      return A4 && T2;
    }
    negate() {
      return new h4(this.px, n2.neg(this.py), this.pz);
    }
    double() {
      const { a: d3, b: v4 } = e, m2 = n2.mul(v4, Qr), { px: O4, py: N3, pz: $4 } = this;
      let B3 = n2.ZERO, A4 = n2.ZERO, T2 = n2.ZERO, S4 = n2.mul(O4, O4), L3 = n2.mul(N3, N3), U4 = n2.mul($4, $4), _ = n2.mul(O4, N3);
      return _ = n2.add(_, _), T2 = n2.mul(O4, $4), T2 = n2.add(T2, T2), B3 = n2.mul(d3, T2), A4 = n2.mul(m2, U4), A4 = n2.add(B3, A4), B3 = n2.sub(L3, A4), A4 = n2.add(L3, A4), A4 = n2.mul(B3, A4), B3 = n2.mul(_, B3), T2 = n2.mul(m2, T2), U4 = n2.mul(d3, U4), _ = n2.sub(S4, U4), _ = n2.mul(d3, _), _ = n2.add(_, T2), T2 = n2.add(S4, S4), S4 = n2.add(T2, S4), S4 = n2.add(S4, U4), S4 = n2.mul(S4, _), A4 = n2.add(A4, S4), U4 = n2.mul(N3, $4), U4 = n2.add(U4, U4), S4 = n2.mul(U4, _), B3 = n2.sub(B3, S4), T2 = n2.mul(U4, L3), T2 = n2.add(T2, T2), T2 = n2.add(T2, T2), new h4(B3, A4, T2);
    }
    add(d3) {
      u3(d3);
      const { px: v4, py: m2, pz: O4 } = this, { px: N3, py: $4, pz: B3 } = d3;
      let A4 = n2.ZERO, T2 = n2.ZERO, S4 = n2.ZERO;
      const L3 = e.a, U4 = n2.mul(e.b, Qr);
      let _ = n2.mul(v4, N3), j2 = n2.mul(m2, $4), g = n2.mul(O4, B3), w2 = n2.add(v4, m2), b4 = n2.add(N3, $4);
      w2 = n2.mul(w2, b4), b4 = n2.add(_, j2), w2 = n2.sub(w2, b4), b4 = n2.add(v4, O4);
      let I3 = n2.add(N3, B3);
      return b4 = n2.mul(b4, I3), I3 = n2.add(_, g), b4 = n2.sub(b4, I3), I3 = n2.add(m2, O4), A4 = n2.add($4, B3), I3 = n2.mul(I3, A4), A4 = n2.add(j2, g), I3 = n2.sub(I3, A4), S4 = n2.mul(L3, b4), A4 = n2.mul(U4, g), S4 = n2.add(A4, S4), A4 = n2.sub(j2, S4), S4 = n2.add(j2, S4), T2 = n2.mul(A4, S4), j2 = n2.add(_, _), j2 = n2.add(j2, _), g = n2.mul(L3, g), b4 = n2.mul(U4, b4), j2 = n2.add(j2, g), g = n2.sub(_, g), g = n2.mul(L3, g), b4 = n2.add(b4, g), _ = n2.mul(j2, b4), T2 = n2.add(T2, _), _ = n2.mul(I3, b4), A4 = n2.mul(w2, A4), A4 = n2.sub(A4, _), _ = n2.mul(w2, j2), S4 = n2.mul(I3, S4), S4 = n2.add(S4, _), new h4(A4, T2, S4);
    }
    subtract(d3) {
      return this.add(d3.negate());
    }
    is0() {
      return this.equals(h4.ZERO);
    }
    wNAF(d3) {
      return E5.wNAFCached(this, d3, h4.normalizeZ);
    }
    multiplyUnsafe(d3) {
      const { endo: v4, n: m2 } = e;
      ft("scalar", d3, dt, m2);
      const O4 = h4.ZERO;
      if (d3 === dt) return O4;
      if (this.is0() || d3 === K) return this;
      if (!v4 || E5.hasPrecomputes(this)) return E5.wNAFCachedUnsafe(this, d3, h4.normalizeZ);
      let { k1neg: N3, k1: $4, k2neg: B3, k2: A4 } = v4.splitScalar(d3), T2 = O4, S4 = O4, L3 = this;
      for (; $4 > dt || A4 > dt; ) $4 & K && (T2 = T2.add(L3)), A4 & K && (S4 = S4.add(L3)), L3 = L3.double(), $4 >>= K, A4 >>= K;
      return N3 && (T2 = T2.negate()), B3 && (S4 = S4.negate()), S4 = new h4(n2.mul(S4.px, v4.beta), S4.py, S4.pz), T2.add(S4);
    }
    multiply(d3) {
      const { endo: v4, n: m2 } = e;
      ft("scalar", d3, K, m2);
      let O4, N3;
      if (v4) {
        const { k1neg: $4, k1: B3, k2neg: A4, k2: T2 } = v4.splitScalar(d3);
        let { p: S4, f: L3 } = this.wNAF(B3), { p: U4, f: _ } = this.wNAF(T2);
        S4 = E5.constTimeNegate($4, S4), U4 = E5.constTimeNegate(A4, U4), U4 = new h4(n2.mul(U4.px, v4.beta), U4.py, U4.pz), O4 = S4.add(U4), N3 = L3.add(_);
      } else {
        const { p: $4, f: B3 } = this.wNAF(d3);
        O4 = $4, N3 = B3;
      }
      return h4.normalizeZ([O4, N3])[0];
    }
    multiplyAndAddUnsafe(d3, v4, m2) {
      const O4 = h4.BASE, N3 = (B3, A4) => A4 === dt || A4 === K || !B3.equals(O4) ? B3.multiplyUnsafe(A4) : B3.multiply(A4), $4 = N3(this, v4).add(N3(d3, m2));
      return $4.is0() ? void 0 : $4;
    }
    toAffine(d3) {
      return l3(this, d3);
    }
    isTorsionFree() {
      const { h: d3, isTorsionFree: v4 } = e;
      if (d3 === K) return true;
      if (v4) return v4(h4, this);
      throw new Error("isTorsionFree() has not been declared for the elliptic curve");
    }
    clearCofactor() {
      const { h: d3, clearCofactor: v4 } = e;
      return d3 === K ? this : v4 ? v4(h4, this) : this.multiplyUnsafe(e.h);
    }
    toRawBytes(d3 = true) {
      return Ct("isCompressed", d3), this.assertValidity(), o2(h4, this, d3);
    }
    toHex(d3 = true) {
      return Ct("isCompressed", d3), Pt(this.toRawBytes(d3));
    }
  }
  h4.BASE = new h4(e.Gx, e.Gy, n2.ONE), h4.ZERO = new h4(n2.ZERO, n2.ONE, n2.ZERO);
  const y5 = e.nBitLength, E5 = dc(h4, e.endo ? Math.ceil(y5 / 2) : y5);
  return { CURVE: e, ProjectivePoint: h4, normPrivateKeyToScalar: a2, weierstrassEquation: s, isWithinCurveOrder: c3 };
}
function Bc(t) {
  const e = Wr(t);
  return Dt(e, { hash: "hash", hmac: "function", randomBytes: "function" }, { bits2int: "function", bits2int_modN: "function", lowS: "boolean" }), Object.freeze({ lowS: true, ...e });
}
function Ic(t) {
  const e = Bc(t), { Fp: n2, n: r2 } = e, o2 = n2.BYTES + 1, i3 = 2 * n2.BYTES + 1;
  function s(g) {
    return X(g, r2);
  }
  function c3(g) {
    return nn(g, r2);
  }
  const { ProjectivePoint: a2, normPrivateKeyToScalar: u3, weierstrassEquation: l3, isWithinCurveOrder: f6 } = Ac({ ...e, toBytes(g, w2, b4) {
    const I3 = w2.toAffine(), R4 = n2.toBytes(I3.x), x2 = ne;
    return Ct("isCompressed", b4), b4 ? x2(Uint8Array.from([w2.hasEvenY() ? 2 : 3]), R4) : x2(Uint8Array.from([4]), R4, n2.toBytes(I3.y));
  }, fromBytes(g) {
    const w2 = g.length, b4 = g[0], I3 = g.subarray(1);
    if (w2 === o2 && (b4 === 2 || b4 === 3)) {
      const R4 = Ot(I3);
      if (!Ee(R4, K, n2.ORDER)) throw new Error("Point is not on curve");
      const x2 = l3(R4);
      let C5;
      try {
        C5 = n2.sqrt(x2);
      } catch (M4) {
        const D2 = M4 instanceof Error ? ": " + M4.message : "";
        throw new Error("Point is not on curve" + D2);
      }
      const P3 = (C5 & K) === K;
      return (b4 & 1) === 1 !== P3 && (C5 = n2.neg(C5)), { x: R4, y: C5 };
    } else if (w2 === i3 && b4 === 4) {
      const R4 = n2.fromBytes(I3.subarray(0, n2.BYTES)), x2 = n2.fromBytes(I3.subarray(n2.BYTES, 2 * n2.BYTES));
      return { x: R4, y: x2 };
    } else {
      const R4 = o2, x2 = i3;
      throw new Error("invalid Point, expected length of " + R4 + ", or uncompressed " + x2 + ", got " + w2);
    }
  } }), h4 = (g) => Pt(Mt(g, e.nByteLength));
  function y5(g) {
    const w2 = r2 >> K;
    return g > w2;
  }
  function E5(g) {
    return y5(g) ? s(-g) : g;
  }
  const p3 = (g, w2, b4) => Ot(g.slice(w2, b4));
  class d3 {
    constructor(w2, b4, I3) {
      this.r = w2, this.s = b4, this.recovery = I3, this.assertValidity();
    }
    static fromCompact(w2) {
      const b4 = e.nByteLength;
      return w2 = et("compactSignature", w2, b4 * 2), new d3(p3(w2, 0, b4), p3(w2, b4, 2 * b4));
    }
    static fromDER(w2) {
      const { r: b4, s: I3 } = lt.toSig(et("DER", w2));
      return new d3(b4, I3);
    }
    assertValidity() {
      ft("r", this.r, K, r2), ft("s", this.s, K, r2);
    }
    addRecoveryBit(w2) {
      return new d3(this.r, this.s, w2);
    }
    recoverPublicKey(w2) {
      const { r: b4, s: I3, recovery: R4 } = this, x2 = B3(et("msgHash", w2));
      if (R4 == null || ![0, 1, 2, 3].includes(R4)) throw new Error("recovery id invalid");
      const C5 = R4 === 2 || R4 === 3 ? b4 + e.n : b4;
      if (C5 >= n2.ORDER) throw new Error("recovery id 2 or 3 invalid");
      const P3 = (R4 & 1) === 0 ? "02" : "03", k5 = a2.fromHex(P3 + h4(C5)), M4 = c3(C5), D2 = s(-x2 * M4), z2 = s(I3 * M4), Z2 = a2.BASE.multiplyAndAddUnsafe(k5, D2, z2);
      if (!Z2) throw new Error("point at infinify");
      return Z2.assertValidity(), Z2;
    }
    hasHighS() {
      return y5(this.s);
    }
    normalizeS() {
      return this.hasHighS() ? new d3(this.r, s(-this.s), this.recovery) : this;
    }
    toDERRawBytes() {
      return Vt(this.toDERHex());
    }
    toDERHex() {
      return lt.hexFromSig({ r: this.r, s: this.s });
    }
    toCompactRawBytes() {
      return Vt(this.toCompactHex());
    }
    toCompactHex() {
      return h4(this.r) + h4(this.s);
    }
  }
  const v4 = { isValidPrivateKey(g) {
    try {
      return u3(g), true;
    } catch {
      return false;
    }
  }, normPrivateKeyToScalar: u3, randomPrivateKey: () => {
    const g = zr(e.n);
    return uc(e.randomBytes(g), e.n);
  }, precompute(g = 8, w2 = a2.BASE) {
    return w2._setWindowSize(g), w2.multiply(BigInt(3)), w2;
  } };
  function m2(g, w2 = true) {
    return a2.fromPrivateKey(g).toRawBytes(w2);
  }
  function O4(g) {
    const w2 = St(g), b4 = typeof g == "string", I3 = (w2 || b4) && g.length;
    return w2 ? I3 === o2 || I3 === i3 : b4 ? I3 === 2 * o2 || I3 === 2 * i3 : g instanceof a2;
  }
  function N3(g, w2, b4 = true) {
    if (O4(g)) throw new Error("first arg must be private key");
    if (!O4(w2)) throw new Error("second arg must be public key");
    return a2.fromHex(w2).multiply(u3(g)).toRawBytes(b4);
  }
  const $4 = e.bits2int || function(g) {
    if (g.length > 8192) throw new Error("input is too large");
    const w2 = Ot(g), b4 = g.length * 8 - e.nBitLength;
    return b4 > 0 ? w2 >> BigInt(b4) : w2;
  }, B3 = e.bits2int_modN || function(g) {
    return s($4(g));
  }, A4 = Je(e.nBitLength);
  function T2(g) {
    return ft("num < 2^" + e.nBitLength, g, dt, A4), Mt(g, e.nByteLength);
  }
  function S4(g, w2, b4 = L3) {
    if (["recovered", "canonical"].some((W3) => W3 in b4)) throw new Error("sign() legacy options not supported");
    const { hash: I3, randomBytes: R4 } = e;
    let { lowS: x2, prehash: C5, extraEntropy: P3 } = b4;
    x2 == null && (x2 = true), g = et("msgHash", g), Jr(b4), C5 && (g = et("prehashed msgHash", I3(g)));
    const k5 = B3(g), M4 = u3(w2), D2 = [T2(M4), T2(k5)];
    if (P3 != null && P3 !== false) {
      const W3 = P3 === true ? R4(n2.BYTES) : P3;
      D2.push(et("extraEntropy", W3));
    }
    const z2 = ne(...D2), Z2 = k5;
    function st2(W3) {
      const J3 = $4(W3);
      if (!f6(J3)) return;
      const Be4 = c3(J3), zt3 = a2.BASE.multiply(J3).toAffine(), vt2 = s(zt3.x);
      if (vt2 === dt) return;
      const Zt3 = s(Be4 * s(Z2 + vt2 * M4));
      if (Zt3 === dt) return;
      let Ut3 = (zt3.x === vt2 ? 0 : 2) | Number(zt3.y & K), vn2 = Zt3;
      return x2 && y5(Zt3) && (vn2 = E5(Zt3), Ut3 ^= 1), new d3(vt2, vn2, Ut3);
    }
    return { seed: z2, k2sig: st2 };
  }
  const L3 = { lowS: e.lowS, prehash: false }, U4 = { lowS: e.lowS, prehash: false };
  function _(g, w2, b4 = L3) {
    const { seed: I3, k2sig: R4 } = S4(g, w2, b4), x2 = e;
    return Vr(x2.hash.outputLen, x2.nByteLength, x2.hmac)(I3, R4);
  }
  a2.BASE._setWindowSize(8);
  function j2(g, w2, b4, I3 = U4) {
    var _a;
    const R4 = g;
    w2 = et("msgHash", w2), b4 = et("publicKey", b4);
    const { lowS: x2, prehash: C5, format: P3 } = I3;
    if (Jr(I3), "strict" in I3) throw new Error("options.strict was renamed to lowS");
    if (P3 !== void 0 && P3 !== "compact" && P3 !== "der") throw new Error("format must be compact or der");
    const k5 = typeof R4 == "string" || St(R4), M4 = !k5 && !P3 && typeof R4 == "object" && R4 !== null && typeof R4.r == "bigint" && typeof R4.s == "bigint";
    if (!k5 && !M4) throw new Error("invalid signature, expected Uint8Array, hex string or Signature instance");
    let D2, z2;
    try {
      if (M4 && (D2 = new d3(R4.r, R4.s)), k5) {
        try {
          P3 !== "compact" && (D2 = d3.fromDER(R4));
        } catch (Ut3) {
          if (!(Ut3 instanceof lt.Err)) throw Ut3;
        }
        !D2 && P3 !== "der" && (D2 = d3.fromCompact(R4));
      }
      z2 = a2.fromHex(b4);
    } catch {
      return false;
    }
    if (!D2 || x2 && D2.hasHighS()) return false;
    C5 && (w2 = e.hash(w2));
    const { r: Z2, s: st2 } = D2, W3 = B3(w2), J3 = c3(st2), Be4 = s(W3 * J3), zt3 = s(Z2 * J3), vt2 = (_a = a2.BASE.multiplyAndAddUnsafe(z2, Be4, zt3)) == null ? void 0 : _a.toAffine();
    return vt2 ? s(vt2.x) === Z2 : false;
  }
  return { CURVE: e, getPublicKey: m2, getSharedSecret: N3, sign: _, verify: j2, ProjectivePoint: a2, Signature: d3, utils: v4 };
}
function Nc(t) {
  return { hash: t, hmac: (e, ...n2) => ye(t, e, Vi(...n2)), randomBytes: Lt };
}
function Uc(t, e) {
  const n2 = (r2) => Ic({ ...t, ...Nc(r2) });
  return { ...n2(e), create: n2 };
}
var to = Kr2(BigInt("0xffffffff00000001000000000000000000000000ffffffffffffffffffffffff"));
var Tc = to.create(BigInt("-3"));
var Rc = BigInt("0x5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b");
var _c = Uc({ a: Tc, b: Rc, Fp: to, n: BigInt("0xffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551"), Gx: BigInt("0x6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296"), Gy: BigInt("0x4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5"), h: BigInt(1), lowS: false }, Qt);
var ln = "base10";
var G = "base16";
var qt = "base64pad";
var xe = "base64url";
var Kt = "utf8";
var dn = 0;
var Ft = 1;
var re = 2;
var $c = 0;
var eo = 1;
var oe = 12;
var hn = 32;
function Lc() {
  const t = fn.utils.randomPrivateKey(), e = fn.getPublicKey(t);
  return { privateKey: toString(t, G), publicKey: toString(e, G) };
}
function jc() {
  const t = Lt(hn);
  return toString(t, G);
}
function Cc(t, e) {
  const n2 = fn.getSharedSecret(fromString(t, G), fromString(e, G)), r2 = Vs(Qt, n2, void 0, void 0, hn);
  return toString(r2, G);
}
function Pc(t) {
  const e = Qt(fromString(t, G));
  return toString(e, G);
}
function kc(t) {
  const e = Qt(fromString(t, Kt));
  return toString(e, G);
}
function pn(t) {
  return fromString(`${t}`, ln);
}
function Bt(t) {
  return Number(toString(t, ln));
}
function no(t) {
  return t.replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
}
function ro(t) {
  const e = t.replace(/-/g, "+").replace(/_/g, "/"), n2 = (4 - e.length % 4) % 4;
  return e + "=".repeat(n2);
}
function Vc(t) {
  const e = pn(typeof t.type < "u" ? t.type : dn);
  if (Bt(e) === Ft && typeof t.senderPublicKey > "u") throw new Error("Missing sender public key for type 1 envelope");
  const n2 = typeof t.senderPublicKey < "u" ? fromString(t.senderPublicKey, G) : void 0, r2 = typeof t.iv < "u" ? fromString(t.iv, G) : Lt(oe), o2 = fromString(t.symKey, G), i3 = $r(o2, r2).encrypt(fromString(t.message, Kt)), s = gn({ type: e, sealed: i3, iv: r2, senderPublicKey: n2 });
  return t.encoding === xe ? no(s) : s;
}
function Mc(t) {
  const e = fromString(t.symKey, G), { sealed: n2, iv: r2 } = Se({ encoded: t.encoded, encoding: t.encoding }), o2 = $r(e, r2).decrypt(n2);
  if (o2 === null) throw new Error("Failed to decrypt");
  return toString(o2, Kt);
}
function Dc(t, e) {
  const n2 = pn(re), r2 = Lt(oe), o2 = fromString(t, Kt), i3 = gn({ type: n2, sealed: o2, iv: r2 });
  return e === xe ? no(i3) : i3;
}
function Hc(t, e) {
  const { sealed: n2 } = Se({ encoded: t, encoding: e });
  return toString(n2, Kt);
}
function gn(t) {
  if (Bt(t.type) === re) return toString(concat([t.type, t.sealed]), qt);
  if (Bt(t.type) === Ft) {
    if (typeof t.senderPublicKey > "u") throw new Error("Missing sender public key for type 1 envelope");
    return toString(concat([t.type, t.senderPublicKey, t.iv, t.sealed]), qt);
  }
  return toString(concat([t.type, t.iv, t.sealed]), qt);
}
function Se(t) {
  const e = (t.encoding || qt) === xe ? ro(t.encoded) : t.encoded, n2 = fromString(e, qt), r2 = n2.slice($c, eo), o2 = eo;
  if (Bt(r2) === Ft) {
    const a2 = o2 + hn, u3 = a2 + oe, l3 = n2.slice(o2, a2), f6 = n2.slice(a2, u3), h4 = n2.slice(u3);
    return { type: r2, sealed: h4, iv: f6, senderPublicKey: l3 };
  }
  if (Bt(r2) === re) {
    const a2 = n2.slice(o2), u3 = Lt(oe);
    return { type: r2, sealed: a2, iv: u3 };
  }
  const i3 = o2 + oe, s = n2.slice(o2, i3), c3 = n2.slice(i3);
  return { type: r2, sealed: c3, iv: s };
}
function qc(t, e) {
  const n2 = Se({ encoded: t, encoding: e == null ? void 0 : e.encoding });
  return oo({ type: Bt(n2.type), senderPublicKey: typeof n2.senderPublicKey < "u" ? toString(n2.senderPublicKey, G) : void 0, receiverPublicKey: e == null ? void 0 : e.receiverPublicKey });
}
function oo(t) {
  const e = (t == null ? void 0 : t.type) || dn;
  if (e === Ft) {
    if (typeof (t == null ? void 0 : t.senderPublicKey) > "u") throw new Error("missing sender public key");
    if (typeof (t == null ? void 0 : t.receiverPublicKey) > "u") throw new Error("missing receiver public key");
  }
  return { type: e, senderPublicKey: t == null ? void 0 : t.senderPublicKey, receiverPublicKey: t == null ? void 0 : t.receiverPublicKey };
}
function Kc(t) {
  return t.type === Ft && typeof t.senderPublicKey == "string" && typeof t.receiverPublicKey == "string";
}
function Fc(t) {
  return t.type === re;
}
function io(t) {
  const e = Buffer.from(t.x, "base64"), n2 = Buffer.from(t.y, "base64");
  return concat([new Uint8Array([4]), e, n2]);
}
function zc(t, e) {
  const [n2, r2, o2] = t.split("."), i3 = Buffer.from(ro(o2), "base64");
  if (i3.length !== 64) throw new Error("Invalid signature length");
  const s = i3.slice(0, 32), c3 = i3.slice(32, 64), a2 = `${n2}.${r2}`, u3 = Qt(a2), l3 = io(e);
  if (!_c.verify(concat([s, c3]), u3, l3)) throw new Error("Invalid signature");
  return sn(t).payload;
}
var so = "irn";
function Zc(t) {
  return (t == null ? void 0 : t.relay) || { protocol: so };
}
function Yc(t) {
  const e = C[t];
  if (typeof e > "u") throw new Error(`Relay Protocol not supported: ${t}`);
  return e;
}
function co(t, e = "-") {
  const n2 = {}, r2 = "relay" + e;
  return Object.keys(t).forEach((o2) => {
    if (o2.startsWith(r2)) {
      const i3 = o2.replace(r2, ""), s = t[o2];
      n2[i3] = s;
    }
  }), n2;
}
function Gc(t) {
  if (!t.includes("wc:")) {
    const u3 = je(t);
    u3 != null && u3.includes("wc:") && (t = u3);
  }
  t = t.includes("wc://") ? t.replace("wc://", "") : t, t = t.includes("wc:") ? t.replace("wc:", "") : t;
  const e = t.indexOf(":"), n2 = t.indexOf("?") !== -1 ? t.indexOf("?") : void 0, r2 = t.substring(0, e), o2 = t.substring(e + 1, n2).split("@"), i3 = typeof n2 < "u" ? t.substring(n2) : "", s = new URLSearchParams(i3), c3 = {};
  s.forEach((u3, l3) => {
    c3[l3] = u3;
  });
  const a2 = typeof c3.methods == "string" ? c3.methods.split(",") : void 0;
  return { protocol: r2, topic: ao(o2[0]), version: parseInt(o2[1], 10), symKey: c3.symKey, relay: co(c3), methods: a2, expiryTimestamp: c3.expiryTimestamp ? parseInt(c3.expiryTimestamp, 10) : void 0 };
}
function ao(t) {
  return t.startsWith("//") ? t.substring(2) : t;
}
function uo(t, e = "-") {
  const n2 = "relay", r2 = {};
  return Object.keys(t).forEach((o2) => {
    const i3 = o2, s = n2 + e + i3;
    t[i3] && (r2[s] = t[i3]);
  }), r2;
}
function Wc(t) {
  const e = new URLSearchParams(), n2 = uo(t.relay);
  Object.keys(n2).sort().forEach((o2) => {
    e.set(o2, n2[o2]);
  }), e.set("symKey", t.symKey), t.expiryTimestamp && e.set("expiryTimestamp", t.expiryTimestamp.toString()), t.methods && e.set("methods", t.methods.join(","));
  const r2 = e.toString();
  return `${t.protocol}:${t.topic}@${t.version}?${r2}`;
}
function Xc(t, e, n2) {
  return `${t}?wc_ev=${n2}&topic=${e}`;
}
var Jc = Object.defineProperty;
var Qc = Object.defineProperties;
var ta = Object.getOwnPropertyDescriptors;
var fo = Object.getOwnPropertySymbols;
var ea = Object.prototype.hasOwnProperty;
var na = Object.prototype.propertyIsEnumerable;
var lo = (t, e, n2) => e in t ? Jc(t, e, { enumerable: true, configurable: true, writable: true, value: n2 }) : t[e] = n2;
var ra = (t, e) => {
  for (var n2 in e || (e = {})) ea.call(e, n2) && lo(t, n2, e[n2]);
  if (fo) for (var n2 of fo(e)) na.call(e, n2) && lo(t, n2, e[n2]);
  return t;
};
var oa = (t, e) => Qc(t, ta(e));
function It(t) {
  const e = [];
  return t.forEach((n2) => {
    const [r2, o2] = n2.split(":");
    e.push(`${r2}:${o2}`);
  }), e;
}
function ho(t) {
  const e = [];
  return Object.values(t).forEach((n2) => {
    e.push(...It(n2.accounts));
  }), e;
}
function po(t, e) {
  const n2 = [];
  return Object.values(t).forEach((r2) => {
    It(r2.accounts).includes(e) && n2.push(...r2.methods);
  }), n2;
}
function go(t, e) {
  const n2 = [];
  return Object.values(t).forEach((r2) => {
    It(r2.accounts).includes(e) && n2.push(...r2.events);
  }), n2;
}
function yn(t) {
  return t.includes(":");
}
function yo(t) {
  return yn(t) ? t.split(":")[0] : t;
}
function ie(t) {
  var e, n2, r2;
  const o2 = {};
  if (!Oe(t)) return o2;
  for (const [i3, s] of Object.entries(t)) {
    const c3 = yn(i3) ? [i3] : s.chains, a2 = s.methods || [], u3 = s.events || [], l3 = yo(i3);
    o2[l3] = oa(ra({}, o2[l3]), { chains: ot(c3, (e = o2[l3]) == null ? void 0 : e.chains), methods: ot(a2, (n2 = o2[l3]) == null ? void 0 : n2.methods), events: ot(u3, (r2 = o2[l3]) == null ? void 0 : r2.events) });
  }
  return o2;
}
function mo(t) {
  const e = {};
  return t == null ? void 0 : t.forEach((n2) => {
    var r2;
    const [o2, i3] = n2.split(":");
    e[o2] || (e[o2] = { accounts: [], chains: [], events: [], methods: [] }), e[o2].accounts.push(n2), (r2 = e[o2].chains) == null || r2.push(`${o2}:${i3}`);
  }), e;
}
function ca(t, e) {
  e = e.map((r2) => r2.replace("did:pkh:", ""));
  const n2 = mo(e);
  for (const [r2, o2] of Object.entries(n2)) o2.methods ? o2.methods = ot(o2.methods, t) : o2.methods = t, o2.events = ["chainChanged", "accountsChanged"];
  return n2;
}
function aa(t, e) {
  var n2, r2, o2, i3, s, c3;
  const a2 = ie(t), u3 = ie(e), l3 = {}, f6 = Object.keys(a2).concat(Object.keys(u3));
  for (const h4 of f6) l3[h4] = { chains: ot((n2 = a2[h4]) == null ? void 0 : n2.chains, (r2 = u3[h4]) == null ? void 0 : r2.chains), methods: ot((o2 = a2[h4]) == null ? void 0 : o2.methods, (i3 = u3[h4]) == null ? void 0 : i3.methods), events: ot((s = a2[h4]) == null ? void 0 : s.events, (c3 = u3[h4]) == null ? void 0 : c3.events) };
  return l3;
}
var wo = { INVALID_METHOD: { message: "Invalid method.", code: 1001 }, INVALID_EVENT: { message: "Invalid event.", code: 1002 }, INVALID_UPDATE_REQUEST: { message: "Invalid update request.", code: 1003 }, INVALID_EXTEND_REQUEST: { message: "Invalid extend request.", code: 1004 }, INVALID_SESSION_SETTLE_REQUEST: { message: "Invalid session settle request.", code: 1005 }, UNAUTHORIZED_METHOD: { message: "Unauthorized method.", code: 3001 }, UNAUTHORIZED_EVENT: { message: "Unauthorized event.", code: 3002 }, UNAUTHORIZED_UPDATE_REQUEST: { message: "Unauthorized update request.", code: 3003 }, UNAUTHORIZED_EXTEND_REQUEST: { message: "Unauthorized extend request.", code: 3004 }, USER_REJECTED: { message: "User rejected.", code: 5e3 }, USER_REJECTED_CHAINS: { message: "User rejected chains.", code: 5001 }, USER_REJECTED_METHODS: { message: "User rejected methods.", code: 5002 }, USER_REJECTED_EVENTS: { message: "User rejected events.", code: 5003 }, UNSUPPORTED_CHAINS: { message: "Unsupported chains.", code: 5100 }, UNSUPPORTED_METHODS: { message: "Unsupported methods.", code: 5101 }, UNSUPPORTED_EVENTS: { message: "Unsupported events.", code: 5102 }, UNSUPPORTED_ACCOUNTS: { message: "Unsupported accounts.", code: 5103 }, UNSUPPORTED_NAMESPACE_KEY: { message: "Unsupported namespace key.", code: 5104 }, USER_DISCONNECTED: { message: "User disconnected.", code: 6e3 }, SESSION_SETTLEMENT_FAILED: { message: "Session settlement failed.", code: 7e3 }, WC_METHOD_UNSUPPORTED: { message: "Unsupported wc_ method.", code: 10001 } };
var bo = { NOT_INITIALIZED: { message: "Not initialized.", code: 1 }, NO_MATCHING_KEY: { message: "No matching key.", code: 2 }, RESTORE_WILL_OVERRIDE: { message: "Restore will override.", code: 3 }, RESUBSCRIBED: { message: "Resubscribed.", code: 4 }, MISSING_OR_INVALID: { message: "Missing or invalid.", code: 5 }, EXPIRED: { message: "Expired.", code: 6 }, UNKNOWN_TYPE: { message: "Unknown type.", code: 7 }, MISMATCHED_TOPIC: { message: "Mismatched topic.", code: 8 }, NON_CONFORMING_NAMESPACES: { message: "Non conforming namespaces.", code: 9 } };
function ht(t, e) {
  const { message: n2, code: r2 } = bo[t];
  return { message: e ? `${n2} ${e}` : n2, code: r2 };
}
function Nt(t, e) {
  const { message: n2, code: r2 } = wo[t];
  return { message: e ? `${n2} ${e}` : n2, code: r2 };
}
function se(t, e) {
  return Array.isArray(t) ? typeof e < "u" && t.length ? t.every(e) : true : false;
}
function Oe(t) {
  return Object.getPrototypeOf(t) === Object.prototype && Object.keys(t).length;
}
function Et(t) {
  return typeof t > "u";
}
function nt(t, e) {
  return e && Et(t) ? true : typeof t == "string" && !!t.trim().length;
}
function Ae(t, e) {
  return e && Et(t) ? true : typeof t == "number" && !isNaN(t);
}
function ua(t, e) {
  const { requiredNamespaces: n2 } = e, r2 = Object.keys(t.namespaces), o2 = Object.keys(n2);
  let i3 = true;
  return gt(o2, r2) ? (r2.forEach((s) => {
    const { accounts: c3, methods: a2, events: u3 } = t.namespaces[s], l3 = It(c3), f6 = n2[s];
    (!gt(ue(s, f6), l3) || !gt(f6.methods, a2) || !gt(f6.events, u3)) && (i3 = false);
  }), i3) : false;
}
function ce(t) {
  return nt(t, false) && t.includes(":") ? t.split(":").length === 2 : false;
}
function Eo(t) {
  if (nt(t, false) && t.includes(":")) {
    const e = t.split(":");
    if (e.length === 3) {
      const n2 = e[0] + ":" + e[1];
      return !!e[2] && ce(n2);
    }
  }
  return false;
}
function fa(t) {
  function e(n2) {
    try {
      return typeof new URL(n2) < "u";
    } catch {
      return false;
    }
  }
  try {
    if (nt(t, false)) {
      if (e(t)) return true;
      const n2 = je(t);
      return e(n2);
    }
  } catch {
  }
  return false;
}
function la(t) {
  var e;
  return (e = t == null ? void 0 : t.proposer) == null ? void 0 : e.publicKey;
}
function da(t) {
  return t == null ? void 0 : t.topic;
}
function ha(t, e) {
  let n2 = null;
  return nt(t == null ? void 0 : t.publicKey, false) || (n2 = ht("MISSING_OR_INVALID", `${e} controller public key should be a string`)), n2;
}
function mn(t) {
  let e = true;
  return se(t) ? t.length && (e = t.every((n2) => nt(n2, false))) : e = false, e;
}
function vo(t, e, n2) {
  let r2 = null;
  return se(e) && e.length ? e.forEach((o2) => {
    r2 || ce(o2) || (r2 = Nt("UNSUPPORTED_CHAINS", `${n2}, chain ${o2} should be a string and conform to "namespace:chainId" format`));
  }) : ce(t) || (r2 = Nt("UNSUPPORTED_CHAINS", `${n2}, chains must be defined as "namespace:chainId" e.g. "eip155:1": {...} in the namespace key OR as an array of CAIP-2 chainIds e.g. eip155: { chains: ["eip155:1", "eip155:5"] }`)), r2;
}
function xo(t, e, n2) {
  let r2 = null;
  return Object.entries(t).forEach(([o2, i3]) => {
    if (r2) return;
    const s = vo(o2, ue(o2, i3), `${e} ${n2}`);
    s && (r2 = s);
  }), r2;
}
function So(t, e) {
  let n2 = null;
  return se(t) ? t.forEach((r2) => {
    n2 || Eo(r2) || (n2 = Nt("UNSUPPORTED_ACCOUNTS", `${e}, account ${r2} should be a string and conform to "namespace:chainId:address" format`));
  }) : n2 = Nt("UNSUPPORTED_ACCOUNTS", `${e}, accounts should be an array of strings conforming to "namespace:chainId:address" format`), n2;
}
function Oo(t, e) {
  let n2 = null;
  return Object.values(t).forEach((r2) => {
    if (n2) return;
    const o2 = So(r2 == null ? void 0 : r2.accounts, `${e} namespace`);
    o2 && (n2 = o2);
  }), n2;
}
function Ao(t, e) {
  let n2 = null;
  return mn(t == null ? void 0 : t.methods) ? mn(t == null ? void 0 : t.events) || (n2 = Nt("UNSUPPORTED_EVENTS", `${e}, events should be an array of strings or empty array for no events`)) : n2 = Nt("UNSUPPORTED_METHODS", `${e}, methods should be an array of strings or empty array for no methods`), n2;
}
function wn(t, e) {
  let n2 = null;
  return Object.values(t).forEach((r2) => {
    if (n2) return;
    const o2 = Ao(r2, `${e}, namespace`);
    o2 && (n2 = o2);
  }), n2;
}
function pa(t, e, n2) {
  let r2 = null;
  if (t && Oe(t)) {
    const o2 = wn(t, e);
    o2 && (r2 = o2);
    const i3 = xo(t, e, n2);
    i3 && (r2 = i3);
  } else r2 = ht("MISSING_OR_INVALID", `${e}, ${n2} should be an object with data`);
  return r2;
}
function Bo(t, e) {
  let n2 = null;
  if (t && Oe(t)) {
    const r2 = wn(t, e);
    r2 && (n2 = r2);
    const o2 = Oo(t, e);
    o2 && (n2 = o2);
  } else n2 = ht("MISSING_OR_INVALID", `${e}, namespaces should be an object with data`);
  return n2;
}
function Io(t) {
  return nt(t.protocol, true);
}
function ga(t, e) {
  let n2 = false;
  return e && !t ? n2 = true : t && se(t) && t.length && t.forEach((r2) => {
    n2 = Io(r2);
  }), n2;
}
function ya(t) {
  return typeof t == "number";
}
function ma(t) {
  return typeof t < "u" && typeof t !== null;
}
function wa(t) {
  return !(!t || typeof t != "object" || !t.code || !Ae(t.code, false) || !t.message || !nt(t.message, false));
}
function ba(t) {
  return !(Et(t) || !nt(t.method, false));
}
function Ea(t) {
  return !(Et(t) || Et(t.result) && Et(t.error) || !Ae(t.id, false) || !nt(t.jsonrpc, false));
}
function va(t) {
  return !(Et(t) || !nt(t.name, false));
}
function xa(t, e) {
  return !(!ce(e) || !ho(t).includes(e));
}
function Sa(t, e, n2) {
  return nt(n2, false) ? po(t, e).includes(n2) : false;
}
function Oa(t, e, n2) {
  return nt(n2, false) ? go(t, e).includes(n2) : false;
}
function No(t, e, n2) {
  let r2 = null;
  const o2 = Aa(t), i3 = Ba(e), s = Object.keys(o2), c3 = Object.keys(i3), a2 = Uo(Object.keys(t)), u3 = Uo(Object.keys(e)), l3 = a2.filter((f6) => !u3.includes(f6));
  return l3.length && (r2 = ht("NON_CONFORMING_NAMESPACES", `${n2} namespaces keys don't satisfy requiredNamespaces.
      Required: ${l3.toString()}
      Received: ${Object.keys(e).toString()}`)), gt(s, c3) || (r2 = ht("NON_CONFORMING_NAMESPACES", `${n2} namespaces chains don't satisfy required namespaces.
      Required: ${s.toString()}
      Approved: ${c3.toString()}`)), Object.keys(e).forEach((f6) => {
    if (!f6.includes(":") || r2) return;
    const h4 = It(e[f6].accounts);
    h4.includes(f6) || (r2 = ht("NON_CONFORMING_NAMESPACES", `${n2} namespaces accounts don't satisfy namespace accounts for ${f6}
        Required: ${f6}
        Approved: ${h4.toString()}`));
  }), s.forEach((f6) => {
    r2 || (gt(o2[f6].methods, i3[f6].methods) ? gt(o2[f6].events, i3[f6].events) || (r2 = ht("NON_CONFORMING_NAMESPACES", `${n2} namespaces events don't satisfy namespace events for ${f6}`)) : r2 = ht("NON_CONFORMING_NAMESPACES", `${n2} namespaces methods don't satisfy namespace methods for ${f6}`));
  }), r2;
}
function Aa(t) {
  const e = {};
  return Object.keys(t).forEach((n2) => {
    var r2;
    n2.includes(":") ? e[n2] = t[n2] : (r2 = t[n2].chains) == null || r2.forEach((o2) => {
      e[o2] = { methods: t[n2].methods, events: t[n2].events };
    });
  }), e;
}
function Uo(t) {
  return [...new Set(t.map((e) => e.includes(":") ? e.split(":")[0] : e))];
}
function Ba(t) {
  const e = {};
  return Object.keys(t).forEach((n2) => {
    if (n2.includes(":")) e[n2] = t[n2];
    else {
      const r2 = It(t[n2].accounts);
      r2 == null ? void 0 : r2.forEach((o2) => {
        e[o2] = { accounts: t[n2].accounts.filter((i3) => i3.includes(`${o2}:`)), methods: t[n2].methods, events: t[n2].events };
      });
    }
  }), e;
}
function Ia(t, e) {
  return Ae(t, false) && t <= e.max && t >= e.min;
}
function Na() {
  const t = xt();
  return new Promise((e) => {
    switch (t) {
      case Y.browser:
        e(To());
        break;
      case Y.reactNative:
        e(Ro());
        break;
      case Y.node:
        e(_o());
        break;
      default:
        e(true);
    }
  });
}
function To() {
  return Tt() && (navigator == null ? void 0 : navigator.onLine);
}
async function Ro() {
  if (pt() && typeof global < "u" && global != null && global.NetInfo) {
    const t = await (global == null ? void 0 : global.NetInfo.fetch());
    return t == null ? void 0 : t.isConnected;
  }
  return true;
}
function _o() {
  return true;
}
function Ua(t) {
  switch (xt()) {
    case Y.browser:
      $o(t);
      break;
    case Y.reactNative:
      Lo(t);
      break;
    case Y.node:
      break;
  }
}
function $o(t) {
  !pt() && Tt() && (window.addEventListener("online", () => t(true)), window.addEventListener("offline", () => t(false)));
}
function Lo(t) {
  pt() && typeof global < "u" && global != null && global.NetInfo && (global == null ? void 0 : global.NetInfo.addEventListener((e) => t(e == null ? void 0 : e.isConnected)));
}
function Ta() {
  var t;
  return Tt() && (0, import_window_getters.getDocument)() ? ((t = (0, import_window_getters.getDocument)()) == null ? void 0 : t.visibilityState) === "visible" : true;
}
var bn = {};
var Ra = class {
  static get(e) {
    return bn[e];
  }
  static set(e, n2) {
    bn[e] = n2;
  }
  static delete(e) {
    delete bn[e];
  }
};

// node_modules/@reown/appkit/node_modules/@walletconnect/core/dist/index.es.js
var import_window_getters2 = __toESM(require_cjs2());
var ze2 = "wc";
var Le = 2;
var he2 = "core";
var B = `${ze2}@2:${he2}:`;
var Et2 = { name: he2, logger: "error" };
var It2 = { database: ":memory:" };
var Tt2 = "crypto";
var ke2 = "client_ed25519_seed";
var Ct2 = import_time2.ONE_DAY;
var Pt2 = "keychain";
var St2 = "0.3";
var Ot2 = "messages";
var Rt2 = "0.3";
var je2 = import_time2.SIX_HOURS;
var At2 = "publisher";
var xt2 = "irn";
var Nt2 = "error";
var Ue = "wss://relay.walletconnect.org";
var $t2 = "relayer";
var C2 = { message: "relayer_message", message_ack: "relayer_message_ack", connect: "relayer_connect", disconnect: "relayer_disconnect", error: "relayer_error", connection_stalled: "relayer_connection_stalled", transport_closed: "relayer_transport_closed", publish: "relayer_publish" };
var zt = "_subscription";
var L = { payload: "payload", connect: "connect", disconnect: "disconnect", error: "error" };
var Lt2 = 0.1;
var _e2 = "2.21.0";
var Q = { link_mode: "link_mode", relay: "relay" };
var le2 = { inbound: "inbound", outbound: "outbound" };
var kt2 = "0.3";
var jt2 = "WALLETCONNECT_CLIENT_ID";
var Fe2 = "WALLETCONNECT_LINK_MODE_APPS";
var $ = { created: "subscription_created", deleted: "subscription_deleted", expired: "subscription_expired", disabled: "subscription_disabled", sync: "subscription_sync", resubscribed: "subscription_resubscribed" };
var Ut = "subscription";
var Ft2 = "0.3";
var Ys2 = import_time2.FIVE_SECONDS * 1e3;
var Mt2 = "pairing";
var Kt2 = "0.3";
var se2 = { wc_pairingDelete: { req: { ttl: import_time2.ONE_DAY, prompt: false, tag: 1e3 }, res: { ttl: import_time2.ONE_DAY, prompt: false, tag: 1001 } }, wc_pairingPing: { req: { ttl: import_time2.THIRTY_SECONDS, prompt: false, tag: 1002 }, res: { ttl: import_time2.THIRTY_SECONDS, prompt: false, tag: 1003 } }, unregistered_method: { req: { ttl: import_time2.ONE_DAY, prompt: false, tag: 0 }, res: { ttl: import_time2.ONE_DAY, prompt: false, tag: 0 } } };
var re2 = { create: "pairing_create", expire: "pairing_expire", delete: "pairing_delete", ping: "pairing_ping" };
var F2 = { created: "history_created", updated: "history_updated", deleted: "history_deleted", sync: "history_sync" };
var Bt2 = "history";
var Vt2 = "0.3";
var qt2 = "expirer";
var M2 = { created: "expirer_created", deleted: "expirer_deleted", expired: "expirer_expired", sync: "expirer_sync" };
var Gt = "0.3";
var Wt2 = "verify-api";
var Zs2 = "https://verify.walletconnect.com";
var Ht2 = "https://verify.walletconnect.org";
var ue2 = Ht2;
var Yt2 = `${ue2}/v3`;
var Jt2 = [Zs2, Ht2];
var Xt2 = "echo";
var Zt = "https://echo.walletconnect.com";
var G2 = { pairing_started: "pairing_started", pairing_uri_validation_success: "pairing_uri_validation_success", pairing_uri_not_expired: "pairing_uri_not_expired", store_new_pairing: "store_new_pairing", subscribing_pairing_topic: "subscribing_pairing_topic", subscribe_pairing_topic_success: "subscribe_pairing_topic_success", existing_pairing: "existing_pairing", pairing_not_expired: "pairing_not_expired", emit_inactive_pairing: "emit_inactive_pairing", emit_session_proposal: "emit_session_proposal", subscribing_to_pairing_topic: "subscribing_to_pairing_topic" };
var Y2 = { no_wss_connection: "no_wss_connection", no_internet_connection: "no_internet_connection", malformed_pairing_uri: "malformed_pairing_uri", active_pairing_already_exists: "active_pairing_already_exists", subscribe_pairing_topic_failure: "subscribe_pairing_topic_failure", pairing_expired: "pairing_expired", proposal_expired: "proposal_expired", proposal_listener_not_found: "proposal_listener_not_found" };
var er2 = { session_approve_started: "session_approve_started", proposal_not_expired: "proposal_not_expired", session_namespaces_validation_success: "session_namespaces_validation_success", create_session_topic: "create_session_topic", subscribing_session_topic: "subscribing_session_topic", subscribe_session_topic_success: "subscribe_session_topic_success", publishing_session_approve: "publishing_session_approve", session_approve_publish_success: "session_approve_publish_success", store_session: "store_session", publishing_session_settle: "publishing_session_settle", session_settle_publish_success: "session_settle_publish_success" };
var tr2 = { no_internet_connection: "no_internet_connection", no_wss_connection: "no_wss_connection", proposal_expired: "proposal_expired", subscribe_session_topic_failure: "subscribe_session_topic_failure", session_approve_publish_failure: "session_approve_publish_failure", session_settle_publish_failure: "session_settle_publish_failure", session_approve_namespace_validation_failure: "session_approve_namespace_validation_failure", proposal_not_found: "proposal_not_found" };
var ir2 = { authenticated_session_approve_started: "authenticated_session_approve_started", authenticated_session_not_expired: "authenticated_session_not_expired", chains_caip2_compliant: "chains_caip2_compliant", chains_evm_compliant: "chains_evm_compliant", create_authenticated_session_topic: "create_authenticated_session_topic", cacaos_verified: "cacaos_verified", store_authenticated_session: "store_authenticated_session", subscribing_authenticated_session_topic: "subscribing_authenticated_session_topic", subscribe_authenticated_session_topic_success: "subscribe_authenticated_session_topic_success", publishing_authenticated_session_approve: "publishing_authenticated_session_approve", authenticated_session_approve_publish_success: "authenticated_session_approve_publish_success" };
var sr2 = { no_internet_connection: "no_internet_connection", no_wss_connection: "no_wss_connection", missing_session_authenticate_request: "missing_session_authenticate_request", session_authenticate_request_expired: "session_authenticate_request_expired", chains_caip2_compliant_failure: "chains_caip2_compliant_failure", chains_evm_compliant_failure: "chains_evm_compliant_failure", invalid_cacao: "invalid_cacao", subscribe_authenticated_session_topic_failure: "subscribe_authenticated_session_topic_failure", authenticated_session_approve_publish_failure: "authenticated_session_approve_publish_failure", authenticated_session_pending_request_not_found: "authenticated_session_pending_request_not_found" };
var Qt2 = 0.1;
var ei2 = "event-client";
var ti = 86400;
var ii = "https://pulse.walletconnect.org/batch";
function rr2(r2, e) {
  if (r2.length >= 255) throw new TypeError("Alphabet too long");
  for (var t = new Uint8Array(256), i3 = 0; i3 < t.length; i3++) t[i3] = 255;
  for (var s = 0; s < r2.length; s++) {
    var n2 = r2.charAt(s), o2 = n2.charCodeAt(0);
    if (t[o2] !== 255) throw new TypeError(n2 + " is ambiguous");
    t[o2] = s;
  }
  var a2 = r2.length, c3 = r2.charAt(0), h4 = Math.log(a2) / Math.log(256), l3 = Math.log(256) / Math.log(a2);
  function d3(u3) {
    if (u3 instanceof Uint8Array || (ArrayBuffer.isView(u3) ? u3 = new Uint8Array(u3.buffer, u3.byteOffset, u3.byteLength) : Array.isArray(u3) && (u3 = Uint8Array.from(u3))), !(u3 instanceof Uint8Array)) throw new TypeError("Expected Uint8Array");
    if (u3.length === 0) return "";
    for (var b4 = 0, x2 = 0, I3 = 0, D2 = u3.length; I3 !== D2 && u3[I3] === 0; ) I3++, b4++;
    for (var j2 = (D2 - I3) * l3 + 1 >>> 0, T2 = new Uint8Array(j2); I3 !== D2; ) {
      for (var q3 = u3[I3], J3 = 0, K3 = j2 - 1; (q3 !== 0 || J3 < x2) && K3 !== -1; K3--, J3++) q3 += 256 * T2[K3] >>> 0, T2[K3] = q3 % a2 >>> 0, q3 = q3 / a2 >>> 0;
      if (q3 !== 0) throw new Error("Non-zero carry");
      x2 = J3, I3++;
    }
    for (var H3 = j2 - x2; H3 !== j2 && T2[H3] === 0; ) H3++;
    for (var me4 = c3.repeat(b4); H3 < j2; ++H3) me4 += r2.charAt(T2[H3]);
    return me4;
  }
  function g(u3) {
    if (typeof u3 != "string") throw new TypeError("Expected String");
    if (u3.length === 0) return new Uint8Array();
    var b4 = 0;
    if (u3[b4] !== " ") {
      for (var x2 = 0, I3 = 0; u3[b4] === c3; ) x2++, b4++;
      for (var D2 = (u3.length - b4) * h4 + 1 >>> 0, j2 = new Uint8Array(D2); u3[b4]; ) {
        var T2 = t[u3.charCodeAt(b4)];
        if (T2 === 255) return;
        for (var q3 = 0, J3 = D2 - 1; (T2 !== 0 || q3 < I3) && J3 !== -1; J3--, q3++) T2 += a2 * j2[J3] >>> 0, j2[J3] = T2 % 256 >>> 0, T2 = T2 / 256 >>> 0;
        if (T2 !== 0) throw new Error("Non-zero carry");
        I3 = q3, b4++;
      }
      if (u3[b4] !== " ") {
        for (var K3 = D2 - I3; K3 !== D2 && j2[K3] === 0; ) K3++;
        for (var H3 = new Uint8Array(x2 + (D2 - K3)), me4 = x2; K3 !== D2; ) H3[me4++] = j2[K3++];
        return H3;
      }
    }
  }
  function _(u3) {
    var b4 = g(u3);
    if (b4) return b4;
    throw new Error(`Non-${e} character`);
  }
  return { encode: d3, decodeUnsafe: g, decode: _ };
}
var nr2 = rr2;
var or2 = nr2;
var si2 = (r2) => {
  if (r2 instanceof Uint8Array && r2.constructor.name === "Uint8Array") return r2;
  if (r2 instanceof ArrayBuffer) return new Uint8Array(r2);
  if (ArrayBuffer.isView(r2)) return new Uint8Array(r2.buffer, r2.byteOffset, r2.byteLength);
  throw new Error("Unknown type, must be binary type");
};
var ar2 = (r2) => new TextEncoder().encode(r2);
var cr2 = (r2) => new TextDecoder().decode(r2);
var hr2 = class {
  constructor(e, t, i3) {
    this.name = e, this.prefix = t, this.baseEncode = i3;
  }
  encode(e) {
    if (e instanceof Uint8Array) return `${this.prefix}${this.baseEncode(e)}`;
    throw Error("Unknown type, must be binary type");
  }
};
var lr2 = class {
  constructor(e, t, i3) {
    if (this.name = e, this.prefix = t, t.codePointAt(0) === void 0) throw new Error("Invalid prefix character");
    this.prefixCodePoint = t.codePointAt(0), this.baseDecode = i3;
  }
  decode(e) {
    if (typeof e == "string") {
      if (e.codePointAt(0) !== this.prefixCodePoint) throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);
      return this.baseDecode(e.slice(this.prefix.length));
    } else throw Error("Can only multibase decode strings");
  }
  or(e) {
    return ri2(this, e);
  }
};
var ur2 = class {
  constructor(e) {
    this.decoders = e;
  }
  or(e) {
    return ri2(this, e);
  }
  decode(e) {
    const t = e[0], i3 = this.decoders[t];
    if (i3) return i3.decode(e);
    throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`);
  }
};
var ri2 = (r2, e) => new ur2({ ...r2.decoders || { [r2.prefix]: r2 }, ...e.decoders || { [e.prefix]: e } });
var dr2 = class {
  constructor(e, t, i3, s) {
    this.name = e, this.prefix = t, this.baseEncode = i3, this.baseDecode = s, this.encoder = new hr2(e, t, i3), this.decoder = new lr2(e, t, s);
  }
  encode(e) {
    return this.encoder.encode(e);
  }
  decode(e) {
    return this.decoder.decode(e);
  }
};
var Ee2 = ({ name: r2, prefix: e, encode: t, decode: i3 }) => new dr2(r2, e, t, i3);
var de2 = ({ prefix: r2, name: e, alphabet: t }) => {
  const { encode: i3, decode: s } = or2(t, e);
  return Ee2({ prefix: r2, name: e, encode: i3, decode: (n2) => si2(s(n2)) });
};
var gr = (r2, e, t, i3) => {
  const s = {};
  for (let l3 = 0; l3 < e.length; ++l3) s[e[l3]] = l3;
  let n2 = r2.length;
  for (; r2[n2 - 1] === "="; ) --n2;
  const o2 = new Uint8Array(n2 * t / 8 | 0);
  let a2 = 0, c3 = 0, h4 = 0;
  for (let l3 = 0; l3 < n2; ++l3) {
    const d3 = s[r2[l3]];
    if (d3 === void 0) throw new SyntaxError(`Non-${i3} character`);
    c3 = c3 << t | d3, a2 += t, a2 >= 8 && (a2 -= 8, o2[h4++] = 255 & c3 >> a2);
  }
  if (a2 >= t || 255 & c3 << 8 - a2) throw new SyntaxError("Unexpected end of data");
  return o2;
};
var pr = (r2, e, t) => {
  const i3 = e[e.length - 1] === "=", s = (1 << t) - 1;
  let n2 = "", o2 = 0, a2 = 0;
  for (let c3 = 0; c3 < r2.length; ++c3) for (a2 = a2 << 8 | r2[c3], o2 += 8; o2 > t; ) o2 -= t, n2 += e[s & a2 >> o2];
  if (o2 && (n2 += e[s & a2 << t - o2]), i3) for (; n2.length * t & 7; ) n2 += "=";
  return n2;
};
var P2 = ({ name: r2, prefix: e, bitsPerChar: t, alphabet: i3 }) => Ee2({ prefix: e, name: r2, encode(s) {
  return pr(s, i3, t);
}, decode(s) {
  return gr(s, i3, t, r2);
} });
var yr = Ee2({ prefix: "\0", name: "identity", encode: (r2) => cr2(r2), decode: (r2) => ar2(r2) });
var br2 = Object.freeze({ __proto__: null, identity: yr });
var mr2 = P2({ prefix: "0", name: "base2", alphabet: "01", bitsPerChar: 1 });
var fr2 = Object.freeze({ __proto__: null, base2: mr2 });
var Dr2 = P2({ prefix: "7", name: "base8", alphabet: "01234567", bitsPerChar: 3 });
var vr2 = Object.freeze({ __proto__: null, base8: Dr2 });
var wr2 = de2({ prefix: "9", name: "base10", alphabet: "0123456789" });
var _r2 = Object.freeze({ __proto__: null, base10: wr2 });
var Er = P2({ prefix: "f", name: "base16", alphabet: "0123456789abcdef", bitsPerChar: 4 });
var Ir2 = P2({ prefix: "F", name: "base16upper", alphabet: "0123456789ABCDEF", bitsPerChar: 4 });
var Tr2 = Object.freeze({ __proto__: null, base16: Er, base16upper: Ir2 });
var Cr2 = P2({ prefix: "b", name: "base32", alphabet: "abcdefghijklmnopqrstuvwxyz234567", bitsPerChar: 5 });
var Pr2 = P2({ prefix: "B", name: "base32upper", alphabet: "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567", bitsPerChar: 5 });
var Sr2 = P2({ prefix: "c", name: "base32pad", alphabet: "abcdefghijklmnopqrstuvwxyz234567=", bitsPerChar: 5 });
var Or2 = P2({ prefix: "C", name: "base32padupper", alphabet: "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=", bitsPerChar: 5 });
var Rr2 = P2({ prefix: "v", name: "base32hex", alphabet: "0123456789abcdefghijklmnopqrstuv", bitsPerChar: 5 });
var Ar2 = P2({ prefix: "V", name: "base32hexupper", alphabet: "0123456789ABCDEFGHIJKLMNOPQRSTUV", bitsPerChar: 5 });
var xr = P2({ prefix: "t", name: "base32hexpad", alphabet: "0123456789abcdefghijklmnopqrstuv=", bitsPerChar: 5 });
var Nr2 = P2({ prefix: "T", name: "base32hexpadupper", alphabet: "0123456789ABCDEFGHIJKLMNOPQRSTUV=", bitsPerChar: 5 });
var $r2 = P2({ prefix: "h", name: "base32z", alphabet: "ybndrfg8ejkmcpqxot1uwisza345h769", bitsPerChar: 5 });
var zr2 = Object.freeze({ __proto__: null, base32: Cr2, base32upper: Pr2, base32pad: Sr2, base32padupper: Or2, base32hex: Rr2, base32hexupper: Ar2, base32hexpad: xr, base32hexpadupper: Nr2, base32z: $r2 });
var Lr2 = de2({ prefix: "k", name: "base36", alphabet: "0123456789abcdefghijklmnopqrstuvwxyz" });
var kr2 = de2({ prefix: "K", name: "base36upper", alphabet: "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ" });
var jr2 = Object.freeze({ __proto__: null, base36: Lr2, base36upper: kr2 });
var Ur2 = de2({ name: "base58btc", prefix: "z", alphabet: "**********************************************************" });
var Fr2 = de2({ name: "base58flickr", prefix: "Z", alphabet: "**********************************************************" });
var Mr2 = Object.freeze({ __proto__: null, base58btc: Ur2, base58flickr: Fr2 });
var Kr3 = P2({ prefix: "m", name: "base64", alphabet: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", bitsPerChar: 6 });
var Br2 = P2({ prefix: "M", name: "base64pad", alphabet: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=", bitsPerChar: 6 });
var Vr2 = P2({ prefix: "u", name: "base64url", alphabet: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_", bitsPerChar: 6 });
var qr2 = P2({ prefix: "U", name: "base64urlpad", alphabet: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=", bitsPerChar: 6 });
var Gr2 = Object.freeze({ __proto__: null, base64: Kr3, base64pad: Br2, base64url: Vr2, base64urlpad: qr2 });
var ni2 = Array.from("🚀🪐☄🛰🌌🌑🌒🌓🌔🌕🌖🌗🌘🌍🌏🌎🐉☀💻🖥💾💿😂❤😍🤣😊🙏💕😭😘👍😅👏😁🔥🥰💔💖💙😢🤔😆🙄💪😉☺👌🤗💜😔😎😇🌹🤦🎉💞✌✨🤷😱😌🌸🙌😋💗💚😏💛🙂💓🤩😄😀🖤😃💯🙈👇🎶😒🤭❣😜💋👀😪😑💥🙋😞😩😡🤪👊🥳😥🤤👉💃😳✋😚😝😴🌟😬🙃🍀🌷😻😓⭐✅🥺🌈😈🤘💦✔😣🏃💐☹🎊💘😠☝😕🌺🎂🌻😐🖕💝🙊😹🗣💫💀👑🎵🤞😛🔴😤🌼😫⚽🤙☕🏆🤫👈😮🙆🍻🍃🐶💁😲🌿🧡🎁⚡🌞🎈❌✊👋😰🤨😶🤝🚶💰🍓💢🤟🙁🚨💨🤬✈🎀🍺🤓😙💟🌱😖👶🥴▶➡❓💎💸⬇😨🌚🦋😷🕺⚠🙅😟😵👎🤲🤠🤧📌🔵💅🧐🐾🍒😗🤑🌊🤯🐷☎💧😯💆👆🎤🙇🍑❄🌴💣🐸💌📍🥀🤢👅💡💩👐📸👻🤐🤮🎼🥵🚩🍎🍊👼💍📣🥂");
var Wr2 = ni2.reduce((r2, e, t) => (r2[t] = e, r2), []);
var Hr2 = ni2.reduce((r2, e, t) => (r2[e.codePointAt(0)] = t, r2), []);
function Yr2(r2) {
  return r2.reduce((e, t) => (e += Wr2[t], e), "");
}
function Jr2(r2) {
  const e = [];
  for (const t of r2) {
    const i3 = Hr2[t.codePointAt(0)];
    if (i3 === void 0) throw new Error(`Non-base256emoji character: ${t}`);
    e.push(i3);
  }
  return new Uint8Array(e);
}
var Xr2 = Ee2({ prefix: "🚀", name: "base256emoji", encode: Yr2, decode: Jr2 });
var Zr2 = Object.freeze({ __proto__: null, base256emoji: Xr2 });
var Qr2 = ai;
var oi2 = 128;
var en2 = 127;
var tn2 = ~en2;
var sn3 = Math.pow(2, 31);
function ai(r2, e, t) {
  e = e || [], t = t || 0;
  for (var i3 = t; r2 >= sn3; ) e[t++] = r2 & 255 | oi2, r2 /= 128;
  for (; r2 & tn2; ) e[t++] = r2 & 255 | oi2, r2 >>>= 7;
  return e[t] = r2 | 0, ai.bytes = t - i3 + 1, e;
}
var rn2 = Me2;
var nn2 = 128;
var ci = 127;
function Me2(r2, i3) {
  var t = 0, i3 = i3 || 0, s = 0, n2 = i3, o2, a2 = r2.length;
  do {
    if (n2 >= a2) throw Me2.bytes = 0, new RangeError("Could not decode varint");
    o2 = r2[n2++], t += s < 28 ? (o2 & ci) << s : (o2 & ci) * Math.pow(2, s), s += 7;
  } while (o2 >= nn2);
  return Me2.bytes = n2 - i3, t;
}
var on2 = Math.pow(2, 7);
var an2 = Math.pow(2, 14);
var cn2 = Math.pow(2, 21);
var hn2 = Math.pow(2, 28);
var ln2 = Math.pow(2, 35);
var un2 = Math.pow(2, 42);
var dn2 = Math.pow(2, 49);
var gn2 = Math.pow(2, 56);
var pn2 = Math.pow(2, 63);
var yn2 = function(r2) {
  return r2 < on2 ? 1 : r2 < an2 ? 2 : r2 < cn2 ? 3 : r2 < hn2 ? 4 : r2 < ln2 ? 5 : r2 < un2 ? 6 : r2 < dn2 ? 7 : r2 < gn2 ? 8 : r2 < pn2 ? 9 : 10;
};
var bn2 = { encode: Qr2, decode: rn2, encodingLength: yn2 };
var hi = bn2;
var li2 = (r2, e, t = 0) => (hi.encode(r2, e, t), e);
var ui = (r2) => hi.encodingLength(r2);
var Ke2 = (r2, e) => {
  const t = e.byteLength, i3 = ui(r2), s = i3 + ui(t), n2 = new Uint8Array(s + t);
  return li2(r2, n2, 0), li2(t, n2, i3), n2.set(e, s), new mn2(r2, t, e, n2);
};
var mn2 = class {
  constructor(e, t, i3, s) {
    this.code = e, this.size = t, this.digest = i3, this.bytes = s;
  }
};
var di = ({ name: r2, code: e, encode: t }) => new fn2(r2, e, t);
var fn2 = class {
  constructor(e, t, i3) {
    this.name = e, this.code = t, this.encode = i3;
  }
  digest(e) {
    if (e instanceof Uint8Array) {
      const t = this.encode(e);
      return t instanceof Uint8Array ? Ke2(this.code, t) : t.then((i3) => Ke2(this.code, i3));
    } else throw Error("Unknown type, must be binary type");
  }
};
var gi2 = (r2) => async (e) => new Uint8Array(await crypto.subtle.digest(r2, e));
var Dn = di({ name: "sha2-256", code: 18, encode: gi2("SHA-256") });
var vn = di({ name: "sha2-512", code: 19, encode: gi2("SHA-512") });
var wn2 = Object.freeze({ __proto__: null, sha256: Dn, sha512: vn });
var pi = 0;
var _n2 = "identity";
var yi2 = si2;
var En2 = (r2) => Ke2(pi, yi2(r2));
var In = { code: pi, name: _n2, encode: yi2, digest: En2 };
var Tn2 = Object.freeze({ __proto__: null, identity: In });
new TextEncoder(), new TextDecoder();
var bi2 = { ...br2, ...fr2, ...vr2, ..._r2, ...Tr2, ...zr2, ...jr2, ...Mr2, ...Gr2, ...Zr2 };
({ ...wn2, ...Tn2 });
function Cn2(r2 = 0) {
  return globalThis.Buffer != null && globalThis.Buffer.allocUnsafe != null ? globalThis.Buffer.allocUnsafe(r2) : new Uint8Array(r2);
}
function mi2(r2, e, t, i3) {
  return { name: r2, prefix: e, encoder: { name: r2, prefix: e, encode: t }, decoder: { decode: i3 } };
}
var fi2 = mi2("utf8", "u", (r2) => "u" + new TextDecoder("utf8").decode(r2), (r2) => new TextEncoder().encode(r2.substring(1)));
var Be = mi2("ascii", "a", (r2) => {
  let e = "a";
  for (let t = 0; t < r2.length; t++) e += String.fromCharCode(r2[t]);
  return e;
}, (r2) => {
  r2 = r2.substring(1);
  const e = Cn2(r2.length);
  for (let t = 0; t < r2.length; t++) e[t] = r2.charCodeAt(t);
  return e;
});
var Pn2 = { utf8: fi2, "utf-8": fi2, hex: bi2.base16, latin1: Be, ascii: Be, binary: Be, ...bi2 };
function Sn2(r2, e = "utf8") {
  const t = Pn2[e];
  if (!t) throw new Error(`Unsupported encoding "${e}"`);
  return (e === "utf8" || e === "utf-8") && globalThis.Buffer != null && globalThis.Buffer.from != null ? globalThis.Buffer.from(r2, "utf8") : t.decoder.decode(`${t.prefix}${r2}`);
}
var On2 = Object.defineProperty;
var Rn2 = (r2, e, t) => e in r2 ? On2(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var W = (r2, e, t) => Rn2(r2, typeof e != "symbol" ? e + "" : e, t);
var Di2 = class {
  constructor(e, t) {
    this.core = e, this.logger = t, W(this, "keychain", /* @__PURE__ */ new Map()), W(this, "name", Pt2), W(this, "version", St2), W(this, "initialized", false), W(this, "storagePrefix", B), W(this, "init", async () => {
      if (!this.initialized) {
        const i3 = await this.getKeyChain();
        typeof i3 < "u" && (this.keychain = i3), this.initialized = true;
      }
    }), W(this, "has", (i3) => (this.isInitialized(), this.keychain.has(i3))), W(this, "set", async (i3, s) => {
      this.isInitialized(), this.keychain.set(i3, s), await this.persist();
    }), W(this, "get", (i3) => {
      this.isInitialized();
      const s = this.keychain.get(i3);
      if (typeof s > "u") {
        const { message: n2 } = ht("NO_MATCHING_KEY", `${this.name}: ${i3}`);
        throw new Error(n2);
      }
      return s;
    }), W(this, "del", async (i3) => {
      this.isInitialized(), this.keychain.delete(i3), await this.persist();
    }), this.core = e, this.logger = E(t, this.name);
  }
  get context() {
    return y(this.logger);
  }
  get storageKey() {
    return this.storagePrefix + this.version + this.core.customStoragePrefix + "//" + this.name;
  }
  async setKeyChain(e) {
    await this.core.storage.setItem(this.storageKey, fi(e));
  }
  async getKeyChain() {
    const e = await this.core.storage.getItem(this.storageKey);
    return typeof e < "u" ? li(e) : void 0;
  }
  async persist() {
    await this.setKeyChain(this.keychain);
  }
  isInitialized() {
    if (!this.initialized) {
      const { message: e } = ht("NOT_INITIALIZED", this.name);
      throw new Error(e);
    }
  }
};
var An = Object.defineProperty;
var xn2 = (r2, e, t) => e in r2 ? An(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var S2 = (r2, e, t) => xn2(r2, typeof e != "symbol" ? e + "" : e, t);
var vi2 = class {
  constructor(e, t, i3) {
    this.core = e, this.logger = t, S2(this, "name", Tt2), S2(this, "keychain"), S2(this, "randomSessionIdentifier", jc()), S2(this, "initialized", false), S2(this, "init", async () => {
      this.initialized || (await this.keychain.init(), this.initialized = true);
    }), S2(this, "hasKeys", (s) => (this.isInitialized(), this.keychain.has(s))), S2(this, "getClientId", async () => {
      this.isInitialized();
      const s = await this.getClientSeed(), n2 = Po(s);
      return Qe(n2.publicKey);
    }), S2(this, "generateKeyPair", () => {
      this.isInitialized();
      const s = Lc();
      return this.setPrivateKey(s.publicKey, s.privateKey);
    }), S2(this, "signJWT", async (s) => {
      this.isInitialized();
      const n2 = await this.getClientSeed(), o2 = Po(n2), a2 = this.randomSessionIdentifier, c3 = Ct2;
      return await Qo(a2, s, c3, o2);
    }), S2(this, "generateSharedKey", (s, n2, o2) => {
      this.isInitialized();
      const a2 = this.getPrivateKey(s), c3 = Cc(a2, n2);
      return this.setSymKey(c3, o2);
    }), S2(this, "setSymKey", async (s, n2) => {
      this.isInitialized();
      const o2 = n2 || Pc(s);
      return await this.keychain.set(o2, s), o2;
    }), S2(this, "deleteKeyPair", async (s) => {
      this.isInitialized(), await this.keychain.del(s);
    }), S2(this, "deleteSymKey", async (s) => {
      this.isInitialized(), await this.keychain.del(s);
    }), S2(this, "encode", async (s, n2, o2) => {
      this.isInitialized();
      const a2 = oo(o2), c3 = safeJsonStringify(n2);
      if (Fc(a2)) return Dc(c3, o2 == null ? void 0 : o2.encoding);
      if (Kc(a2)) {
        const g = a2.senderPublicKey, _ = a2.receiverPublicKey;
        s = await this.generateSharedKey(g, _);
      }
      const h4 = this.getSymKey(s), { type: l3, senderPublicKey: d3 } = a2;
      return Vc({ type: l3, symKey: h4, message: c3, senderPublicKey: d3, encoding: o2 == null ? void 0 : o2.encoding });
    }), S2(this, "decode", async (s, n2, o2) => {
      this.isInitialized();
      const a2 = qc(n2, o2);
      if (Fc(a2)) {
        const c3 = Hc(n2, o2 == null ? void 0 : o2.encoding);
        return safeJsonParse(c3);
      }
      if (Kc(a2)) {
        const c3 = a2.receiverPublicKey, h4 = a2.senderPublicKey;
        s = await this.generateSharedKey(c3, h4);
      }
      try {
        const c3 = this.getSymKey(s), h4 = Mc({ symKey: c3, encoded: n2, encoding: o2 == null ? void 0 : o2.encoding });
        return safeJsonParse(h4);
      } catch (c3) {
        this.logger.error(`Failed to decode message from topic: '${s}', clientId: '${await this.getClientId()}'`), this.logger.error(c3);
      }
    }), S2(this, "getPayloadType", (s, n2 = qt) => {
      const o2 = Se({ encoded: s, encoding: n2 });
      return Bt(o2.type);
    }), S2(this, "getPayloadSenderPublicKey", (s, n2 = qt) => {
      const o2 = Se({ encoded: s, encoding: n2 });
      return o2.senderPublicKey ? toString(o2.senderPublicKey, G) : void 0;
    }), this.core = e, this.logger = E(t, this.name), this.keychain = i3 || new Di2(this.core, this.logger);
  }
  get context() {
    return y(this.logger);
  }
  async setPrivateKey(e, t) {
    return await this.keychain.set(e, t), e;
  }
  getPrivateKey(e) {
    return this.keychain.get(e);
  }
  async getClientSeed() {
    let e = "";
    try {
      e = this.keychain.get(ke2);
    } catch {
      e = jc(), await this.keychain.set(ke2, e);
    }
    return Sn2(e, "base16");
  }
  getSymKey(e) {
    return this.keychain.get(e);
  }
  isInitialized() {
    if (!this.initialized) {
      const { message: e } = ht("NOT_INITIALIZED", this.name);
      throw new Error(e);
    }
  }
};
var Nn = Object.defineProperty;
var $n2 = Object.defineProperties;
var zn2 = Object.getOwnPropertyDescriptors;
var wi2 = Object.getOwnPropertySymbols;
var Ln = Object.prototype.hasOwnProperty;
var kn2 = Object.prototype.propertyIsEnumerable;
var Ve2 = (r2, e, t) => e in r2 ? Nn(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var jn2 = (r2, e) => {
  for (var t in e || (e = {})) Ln.call(e, t) && Ve2(r2, t, e[t]);
  if (wi2) for (var t of wi2(e)) kn2.call(e, t) && Ve2(r2, t, e[t]);
  return r2;
};
var Un = (r2, e) => $n2(r2, zn2(e));
var k3 = (r2, e, t) => Ve2(r2, typeof e != "symbol" ? e + "" : e, t);
var _i2 = class extends y2 {
  constructor(e, t) {
    super(e, t), this.logger = e, this.core = t, k3(this, "messages", /* @__PURE__ */ new Map()), k3(this, "messagesWithoutClientAck", /* @__PURE__ */ new Map()), k3(this, "name", Ot2), k3(this, "version", Rt2), k3(this, "initialized", false), k3(this, "storagePrefix", B), k3(this, "init", async () => {
      if (!this.initialized) {
        this.logger.trace("Initialized");
        try {
          const i3 = await this.getRelayerMessages();
          typeof i3 < "u" && (this.messages = i3);
          const s = await this.getRelayerMessagesWithoutClientAck();
          typeof s < "u" && (this.messagesWithoutClientAck = s), this.logger.debug(`Successfully Restored records for ${this.name}`), this.logger.trace({ type: "method", method: "restore", size: this.messages.size });
        } catch (i3) {
          this.logger.debug(`Failed to Restore records for ${this.name}`), this.logger.error(i3);
        } finally {
          this.initialized = true;
        }
      }
    }), k3(this, "set", async (i3, s, n2) => {
      this.isInitialized();
      const o2 = kc(s);
      let a2 = this.messages.get(i3);
      if (typeof a2 > "u" && (a2 = {}), typeof a2[o2] < "u") return o2;
      if (a2[o2] = s, this.messages.set(i3, a2), n2 === le2.inbound) {
        const c3 = this.messagesWithoutClientAck.get(i3) || {};
        this.messagesWithoutClientAck.set(i3, Un(jn2({}, c3), { [o2]: s }));
      }
      return await this.persist(), o2;
    }), k3(this, "get", (i3) => {
      this.isInitialized();
      let s = this.messages.get(i3);
      return typeof s > "u" && (s = {}), s;
    }), k3(this, "getWithoutAck", (i3) => {
      this.isInitialized();
      const s = {};
      for (const n2 of i3) {
        const o2 = this.messagesWithoutClientAck.get(n2) || {};
        s[n2] = Object.values(o2);
      }
      return s;
    }), k3(this, "has", (i3, s) => {
      this.isInitialized();
      const n2 = this.get(i3), o2 = kc(s);
      return typeof n2[o2] < "u";
    }), k3(this, "ack", async (i3, s) => {
      this.isInitialized();
      const n2 = this.messagesWithoutClientAck.get(i3);
      if (typeof n2 > "u") return;
      const o2 = kc(s);
      delete n2[o2], Object.keys(n2).length === 0 ? this.messagesWithoutClientAck.delete(i3) : this.messagesWithoutClientAck.set(i3, n2), await this.persist();
    }), k3(this, "del", async (i3) => {
      this.isInitialized(), this.messages.delete(i3), this.messagesWithoutClientAck.delete(i3), await this.persist();
    }), this.logger = E(e, this.name), this.core = t;
  }
  get context() {
    return y(this.logger);
  }
  get storageKey() {
    return this.storagePrefix + this.version + this.core.customStoragePrefix + "//" + this.name;
  }
  get storageKeyWithoutClientAck() {
    return this.storagePrefix + this.version + this.core.customStoragePrefix + "//" + this.name + "_withoutClientAck";
  }
  async setRelayerMessages(e) {
    await this.core.storage.setItem(this.storageKey, fi(e));
  }
  async setRelayerMessagesWithoutClientAck(e) {
    await this.core.storage.setItem(this.storageKeyWithoutClientAck, fi(e));
  }
  async getRelayerMessages() {
    const e = await this.core.storage.getItem(this.storageKey);
    return typeof e < "u" ? li(e) : void 0;
  }
  async getRelayerMessagesWithoutClientAck() {
    const e = await this.core.storage.getItem(this.storageKeyWithoutClientAck);
    return typeof e < "u" ? li(e) : void 0;
  }
  async persist() {
    await this.setRelayerMessages(this.messages), await this.setRelayerMessagesWithoutClientAck(this.messagesWithoutClientAck);
  }
  isInitialized() {
    if (!this.initialized) {
      const { message: e } = ht("NOT_INITIALIZED", this.name);
      throw new Error(e);
    }
  }
};
var Fn2 = Object.defineProperty;
var Mn2 = Object.defineProperties;
var Kn2 = Object.getOwnPropertyDescriptors;
var Ei2 = Object.getOwnPropertySymbols;
var Bn = Object.prototype.hasOwnProperty;
var Vn2 = Object.prototype.propertyIsEnumerable;
var qe2 = (r2, e, t) => e in r2 ? Fn2(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var Ie2 = (r2, e) => {
  for (var t in e || (e = {})) Bn.call(e, t) && qe2(r2, t, e[t]);
  if (Ei2) for (var t of Ei2(e)) Vn2.call(e, t) && qe2(r2, t, e[t]);
  return r2;
};
var Ge2 = (r2, e) => Mn2(r2, Kn2(e));
var V3 = (r2, e, t) => qe2(r2, typeof e != "symbol" ? e + "" : e, t);
var qn = class extends m {
  constructor(e, t) {
    super(e, t), this.relayer = e, this.logger = t, V3(this, "events", new import_events3.EventEmitter()), V3(this, "name", At2), V3(this, "queue", /* @__PURE__ */ new Map()), V3(this, "publishTimeout", (0, import_time2.toMiliseconds)(import_time2.ONE_MINUTE)), V3(this, "initialPublishTimeout", (0, import_time2.toMiliseconds)(import_time2.ONE_SECOND * 15)), V3(this, "needsTransportRestart", false), V3(this, "publish", async (i3, s, n2) => {
      var o2;
      this.logger.debug("Publishing Payload"), this.logger.trace({ type: "method", method: "publish", params: { topic: i3, message: s, opts: n2 } });
      const a2 = (n2 == null ? void 0 : n2.ttl) || je2, c3 = Zc(n2), h4 = (n2 == null ? void 0 : n2.prompt) || false, l3 = (n2 == null ? void 0 : n2.tag) || 0, d3 = (n2 == null ? void 0 : n2.id) || getBigIntRpcId().toString(), g = { topic: i3, message: s, opts: { ttl: a2, relay: c3, prompt: h4, tag: l3, id: d3, attestation: n2 == null ? void 0 : n2.attestation, tvf: n2 == null ? void 0 : n2.tvf } }, _ = `Failed to publish payload, please try again. id:${d3} tag:${l3}`;
      try {
        const u3 = new Promise(async (b4) => {
          const x2 = ({ id: D2 }) => {
            g.opts.id === D2 && (this.removeRequestFromQueue(D2), this.relayer.events.removeListener(C2.publish, x2), b4(g));
          };
          this.relayer.events.on(C2.publish, x2);
          const I3 = yi(new Promise((D2, j2) => {
            this.rpcPublish({ topic: i3, message: s, ttl: a2, prompt: h4, tag: l3, id: d3, attestation: n2 == null ? void 0 : n2.attestation, tvf: n2 == null ? void 0 : n2.tvf }).then(D2).catch((T2) => {
              this.logger.warn(T2, T2 == null ? void 0 : T2.message), j2(T2);
            });
          }), this.initialPublishTimeout, `Failed initial publish, retrying.... id:${d3} tag:${l3}`);
          try {
            await I3, this.events.removeListener(C2.publish, x2);
          } catch (D2) {
            this.queue.set(d3, Ge2(Ie2({}, g), { attempt: 1 })), this.logger.warn(D2, D2 == null ? void 0 : D2.message);
          }
        });
        this.logger.trace({ type: "method", method: "publish", params: { id: d3, topic: i3, message: s, opts: n2 } }), await yi(u3, this.publishTimeout, _);
      } catch (u3) {
        if (this.logger.debug("Failed to Publish Payload"), this.logger.error(u3), (o2 = n2 == null ? void 0 : n2.internal) != null && o2.throwOnFailedPublish) throw u3;
      } finally {
        this.queue.delete(d3);
      }
    }), V3(this, "on", (i3, s) => {
      this.events.on(i3, s);
    }), V3(this, "once", (i3, s) => {
      this.events.once(i3, s);
    }), V3(this, "off", (i3, s) => {
      this.events.off(i3, s);
    }), V3(this, "removeListener", (i3, s) => {
      this.events.removeListener(i3, s);
    }), this.relayer = e, this.logger = E(t, this.name), this.registerEventListeners();
  }
  get context() {
    return y(this.logger);
  }
  async rpcPublish(e) {
    var t, i3, s, n2;
    const { topic: o2, message: a2, ttl: c3 = je2, prompt: h4, tag: l3, id: d3, attestation: g, tvf: _ } = e, u3 = { method: Yc(Zc().protocol).publish, params: Ie2({ topic: o2, message: a2, ttl: c3, prompt: h4, tag: l3, attestation: g }, _), id: d3 };
    Et((t = u3.params) == null ? void 0 : t.prompt) && ((i3 = u3.params) == null || delete i3.prompt), Et((s = u3.params) == null ? void 0 : s.tag) && ((n2 = u3.params) == null || delete n2.tag), this.logger.debug("Outgoing Relay Payload"), this.logger.trace({ type: "message", direction: "outgoing", request: u3 });
    const b4 = await this.relayer.request(u3);
    return this.relayer.events.emit(C2.publish, e), this.logger.debug("Successfully Published Payload"), b4;
  }
  removeRequestFromQueue(e) {
    this.queue.delete(e);
  }
  checkQueue() {
    this.queue.forEach(async (e, t) => {
      const i3 = e.attempt + 1;
      this.queue.set(t, Ge2(Ie2({}, e), { attempt: i3 }));
      const { topic: s, message: n2, opts: o2, attestation: a2 } = e;
      this.logger.warn({}, `Publisher: queue->publishing: ${e.opts.id}, tag: ${e.opts.tag}, attempt: ${i3}`), await this.rpcPublish(Ge2(Ie2({}, e), { topic: s, message: n2, ttl: o2.ttl, prompt: o2.prompt, tag: o2.tag, id: o2.id, attestation: a2, tvf: o2.tvf })), this.logger.warn({}, `Publisher: queue->published: ${e.opts.id}`);
    });
  }
  registerEventListeners() {
    this.relayer.core.heartbeat.on(r.pulse, () => {
      if (this.needsTransportRestart) {
        this.needsTransportRestart = false, this.relayer.events.emit(C2.connection_stalled);
        return;
      }
      this.checkQueue();
    }), this.relayer.on(C2.message_ack, (e) => {
      this.removeRequestFromQueue(e.id.toString());
    });
  }
};
var Gn2 = Object.defineProperty;
var Wn2 = (r2, e, t) => e in r2 ? Gn2(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var ne2 = (r2, e, t) => Wn2(r2, typeof e != "symbol" ? e + "" : e, t);
var Hn = class {
  constructor() {
    ne2(this, "map", /* @__PURE__ */ new Map()), ne2(this, "set", (e, t) => {
      const i3 = this.get(e);
      this.exists(e, t) || this.map.set(e, [...i3, t]);
    }), ne2(this, "get", (e) => this.map.get(e) || []), ne2(this, "exists", (e, t) => this.get(e).includes(t)), ne2(this, "delete", (e, t) => {
      if (typeof t > "u") {
        this.map.delete(e);
        return;
      }
      if (!this.map.has(e)) return;
      const i3 = this.get(e);
      if (!this.exists(e, t)) return;
      const s = i3.filter((n2) => n2 !== t);
      if (!s.length) {
        this.map.delete(e);
        return;
      }
      this.map.set(e, s);
    }), ne2(this, "clear", () => {
      this.map.clear();
    });
  }
  get topics() {
    return Array.from(this.map.keys());
  }
};
var Yn2 = Object.defineProperty;
var Jn2 = Object.defineProperties;
var Xn2 = Object.getOwnPropertyDescriptors;
var Ii2 = Object.getOwnPropertySymbols;
var Zn2 = Object.prototype.hasOwnProperty;
var Qn2 = Object.prototype.propertyIsEnumerable;
var We2 = (r2, e, t) => e in r2 ? Yn2(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var ge2 = (r2, e) => {
  for (var t in e || (e = {})) Zn2.call(e, t) && We2(r2, t, e[t]);
  if (Ii2) for (var t of Ii2(e)) Qn2.call(e, t) && We2(r2, t, e[t]);
  return r2;
};
var He2 = (r2, e) => Jn2(r2, Xn2(e));
var f5 = (r2, e, t) => We2(r2, typeof e != "symbol" ? e + "" : e, t);
var Ti2 = class extends P {
  constructor(e, t) {
    super(e, t), this.relayer = e, this.logger = t, f5(this, "subscriptions", /* @__PURE__ */ new Map()), f5(this, "topicMap", new Hn()), f5(this, "events", new import_events3.EventEmitter()), f5(this, "name", Ut), f5(this, "version", Ft2), f5(this, "pending", /* @__PURE__ */ new Map()), f5(this, "cached", []), f5(this, "initialized", false), f5(this, "storagePrefix", B), f5(this, "subscribeTimeout", (0, import_time2.toMiliseconds)(import_time2.ONE_MINUTE)), f5(this, "initialSubscribeTimeout", (0, import_time2.toMiliseconds)(import_time2.ONE_SECOND * 15)), f5(this, "clientId"), f5(this, "batchSubscribeTopicsLimit", 500), f5(this, "init", async () => {
      this.initialized || (this.logger.trace("Initialized"), this.registerEventListeners(), await this.restore()), this.initialized = true;
    }), f5(this, "subscribe", async (i3, s) => {
      this.isInitialized(), this.logger.debug("Subscribing Topic"), this.logger.trace({ type: "method", method: "subscribe", params: { topic: i3, opts: s } });
      try {
        const n2 = Zc(s), o2 = { topic: i3, relay: n2, transportType: s == null ? void 0 : s.transportType };
        this.pending.set(i3, o2);
        const a2 = await this.rpcSubscribe(i3, n2, s);
        return typeof a2 == "string" && (this.onSubscribe(a2, o2), this.logger.debug("Successfully Subscribed Topic"), this.logger.trace({ type: "method", method: "subscribe", params: { topic: i3, opts: s } })), a2;
      } catch (n2) {
        throw this.logger.debug("Failed to Subscribe Topic"), this.logger.error(n2), n2;
      }
    }), f5(this, "unsubscribe", async (i3, s) => {
      this.isInitialized(), typeof (s == null ? void 0 : s.id) < "u" ? await this.unsubscribeById(i3, s.id, s) : await this.unsubscribeByTopic(i3, s);
    }), f5(this, "isSubscribed", (i3) => new Promise((s) => {
      s(this.topicMap.topics.includes(i3));
    })), f5(this, "isKnownTopic", (i3) => new Promise((s) => {
      s(this.topicMap.topics.includes(i3) || this.pending.has(i3) || this.cached.some((n2) => n2.topic === i3));
    })), f5(this, "on", (i3, s) => {
      this.events.on(i3, s);
    }), f5(this, "once", (i3, s) => {
      this.events.once(i3, s);
    }), f5(this, "off", (i3, s) => {
      this.events.off(i3, s);
    }), f5(this, "removeListener", (i3, s) => {
      this.events.removeListener(i3, s);
    }), f5(this, "start", async () => {
      await this.onConnect();
    }), f5(this, "stop", async () => {
      await this.onDisconnect();
    }), f5(this, "restart", async () => {
      await this.restore(), await this.onRestart();
    }), f5(this, "checkPending", async () => {
      if (this.pending.size === 0 && (!this.initialized || !this.relayer.connected)) return;
      const i3 = [];
      this.pending.forEach((s) => {
        i3.push(s);
      }), await this.batchSubscribe(i3);
    }), f5(this, "registerEventListeners", () => {
      this.relayer.core.heartbeat.on(r.pulse, async () => {
        await this.checkPending();
      }), this.events.on($.created, async (i3) => {
        const s = $.created;
        this.logger.info(`Emitting ${s}`), this.logger.debug({ type: "event", event: s, data: i3 }), await this.persist();
      }), this.events.on($.deleted, async (i3) => {
        const s = $.deleted;
        this.logger.info(`Emitting ${s}`), this.logger.debug({ type: "event", event: s, data: i3 }), await this.persist();
      });
    }), this.relayer = e, this.logger = E(t, this.name), this.clientId = "";
  }
  get context() {
    return y(this.logger);
  }
  get storageKey() {
    return this.storagePrefix + this.version + this.relayer.core.customStoragePrefix + "//" + this.name;
  }
  get length() {
    return this.subscriptions.size;
  }
  get ids() {
    return Array.from(this.subscriptions.keys());
  }
  get values() {
    return Array.from(this.subscriptions.values());
  }
  get topics() {
    return this.topicMap.topics;
  }
  get hasAnyTopics() {
    return this.topicMap.topics.length > 0 || this.pending.size > 0 || this.cached.length > 0 || this.subscriptions.size > 0;
  }
  hasSubscription(e, t) {
    let i3 = false;
    try {
      i3 = this.getSubscription(e).topic === t;
    } catch {
    }
    return i3;
  }
  reset() {
    this.cached = [], this.initialized = true;
  }
  onDisable() {
    this.values.length > 0 && (this.cached = this.values), this.subscriptions.clear(), this.topicMap.clear();
  }
  async unsubscribeByTopic(e, t) {
    const i3 = this.topicMap.get(e);
    await Promise.all(i3.map(async (s) => await this.unsubscribeById(e, s, t)));
  }
  async unsubscribeById(e, t, i3) {
    this.logger.debug("Unsubscribing Topic"), this.logger.trace({ type: "method", method: "unsubscribe", params: { topic: e, id: t, opts: i3 } });
    try {
      const s = Zc(i3);
      await this.restartToComplete({ topic: e, id: t, relay: s }), await this.rpcUnsubscribe(e, t, s);
      const n2 = Nt("USER_DISCONNECTED", `${this.name}, ${e}`);
      await this.onUnsubscribe(e, t, n2), this.logger.debug("Successfully Unsubscribed Topic"), this.logger.trace({ type: "method", method: "unsubscribe", params: { topic: e, id: t, opts: i3 } });
    } catch (s) {
      throw this.logger.debug("Failed to Unsubscribe Topic"), this.logger.error(s), s;
    }
  }
  async rpcSubscribe(e, t, i3) {
    var s;
    (!i3 || (i3 == null ? void 0 : i3.transportType) === Q.relay) && await this.restartToComplete({ topic: e, id: e, relay: t });
    const n2 = { method: Yc(t.protocol).subscribe, params: { topic: e } };
    this.logger.debug("Outgoing Relay Payload"), this.logger.trace({ type: "payload", direction: "outgoing", request: n2 });
    const o2 = (s = i3 == null ? void 0 : i3.internal) == null ? void 0 : s.throwOnFailedPublish;
    try {
      const a2 = await this.getSubscriptionId(e);
      if ((i3 == null ? void 0 : i3.transportType) === Q.link_mode) return setTimeout(() => {
        (this.relayer.connected || this.relayer.connecting) && this.relayer.request(n2).catch((l3) => this.logger.warn(l3));
      }, (0, import_time2.toMiliseconds)(import_time2.ONE_SECOND)), a2;
      const c3 = new Promise(async (l3) => {
        const d3 = (g) => {
          g.topic === e && (this.events.removeListener($.created, d3), l3(g.id));
        };
        this.events.on($.created, d3);
        try {
          const g = await yi(new Promise((_, u3) => {
            this.relayer.request(n2).catch((b4) => {
              this.logger.warn(b4, b4 == null ? void 0 : b4.message), u3(b4);
            }).then(_);
          }), this.initialSubscribeTimeout, `Subscribing to ${e} failed, please try again`);
          this.events.removeListener($.created, d3), l3(g);
        } catch {
        }
      }), h4 = await yi(c3, this.subscribeTimeout, `Subscribing to ${e} failed, please try again`);
      if (!h4 && o2) throw new Error(`Subscribing to ${e} failed, please try again`);
      return h4 ? a2 : null;
    } catch (a2) {
      if (this.logger.debug("Outgoing Relay Subscribe Payload stalled"), this.relayer.events.emit(C2.connection_stalled), o2) throw a2;
    }
    return null;
  }
  async rpcBatchSubscribe(e) {
    if (!e.length) return;
    const t = e[0].relay, i3 = { method: Yc(t.protocol).batchSubscribe, params: { topics: e.map((s) => s.topic) } };
    this.logger.debug("Outgoing Relay Payload"), this.logger.trace({ type: "payload", direction: "outgoing", request: i3 });
    try {
      await await yi(new Promise((s) => {
        this.relayer.request(i3).catch((n2) => this.logger.warn(n2)).then(s);
      }), this.subscribeTimeout, "rpcBatchSubscribe failed, please try again");
    } catch {
      this.relayer.events.emit(C2.connection_stalled);
    }
  }
  async rpcBatchFetchMessages(e) {
    if (!e.length) return;
    const t = e[0].relay, i3 = { method: Yc(t.protocol).batchFetchMessages, params: { topics: e.map((n2) => n2.topic) } };
    this.logger.debug("Outgoing Relay Payload"), this.logger.trace({ type: "payload", direction: "outgoing", request: i3 });
    let s;
    try {
      s = await await yi(new Promise((n2, o2) => {
        this.relayer.request(i3).catch((a2) => {
          this.logger.warn(a2), o2(a2);
        }).then(n2);
      }), this.subscribeTimeout, "rpcBatchFetchMessages failed, please try again");
    } catch {
      this.relayer.events.emit(C2.connection_stalled);
    }
    return s;
  }
  rpcUnsubscribe(e, t, i3) {
    const s = { method: Yc(i3.protocol).unsubscribe, params: { topic: e, id: t } };
    return this.logger.debug("Outgoing Relay Payload"), this.logger.trace({ type: "payload", direction: "outgoing", request: s }), this.relayer.request(s);
  }
  onSubscribe(e, t) {
    this.setSubscription(e, He2(ge2({}, t), { id: e })), this.pending.delete(t.topic);
  }
  onBatchSubscribe(e) {
    e.length && e.forEach((t) => {
      this.setSubscription(t.id, ge2({}, t)), this.pending.delete(t.topic);
    });
  }
  async onUnsubscribe(e, t, i3) {
    this.events.removeAllListeners(t), this.hasSubscription(t, e) && this.deleteSubscription(t, i3), await this.relayer.messages.del(e);
  }
  async setRelayerSubscriptions(e) {
    await this.relayer.core.storage.setItem(this.storageKey, e);
  }
  async getRelayerSubscriptions() {
    return await this.relayer.core.storage.getItem(this.storageKey);
  }
  setSubscription(e, t) {
    this.logger.debug("Setting subscription"), this.logger.trace({ type: "method", method: "setSubscription", id: e, subscription: t }), this.addSubscription(e, t);
  }
  addSubscription(e, t) {
    this.subscriptions.set(e, ge2({}, t)), this.topicMap.set(t.topic, e), this.events.emit($.created, t);
  }
  getSubscription(e) {
    this.logger.debug("Getting subscription"), this.logger.trace({ type: "method", method: "getSubscription", id: e });
    const t = this.subscriptions.get(e);
    if (!t) {
      const { message: i3 } = ht("NO_MATCHING_KEY", `${this.name}: ${e}`);
      throw new Error(i3);
    }
    return t;
  }
  deleteSubscription(e, t) {
    this.logger.debug("Deleting subscription"), this.logger.trace({ type: "method", method: "deleteSubscription", id: e, reason: t });
    const i3 = this.getSubscription(e);
    this.subscriptions.delete(e), this.topicMap.delete(i3.topic, e), this.events.emit($.deleted, He2(ge2({}, i3), { reason: t }));
  }
  async persist() {
    await this.setRelayerSubscriptions(this.values), this.events.emit($.sync);
  }
  async onRestart() {
    if (this.cached.length) {
      const e = [...this.cached], t = Math.ceil(this.cached.length / this.batchSubscribeTopicsLimit);
      for (let i3 = 0; i3 < t; i3++) {
        const s = e.splice(0, this.batchSubscribeTopicsLimit);
        await this.batchSubscribe(s);
      }
    }
    this.events.emit($.resubscribed);
  }
  async restore() {
    try {
      const e = await this.getRelayerSubscriptions();
      if (typeof e > "u" || !e.length) return;
      if (this.subscriptions.size) {
        const { message: t } = ht("RESTORE_WILL_OVERRIDE", this.name);
        throw this.logger.error(t), this.logger.error(`${this.name}: ${JSON.stringify(this.values)}`), new Error(t);
      }
      this.cached = e, this.logger.debug(`Successfully Restored subscriptions for ${this.name}`), this.logger.trace({ type: "method", method: "restore", subscriptions: this.values });
    } catch (e) {
      this.logger.debug(`Failed to Restore subscriptions for ${this.name}`), this.logger.error(e);
    }
  }
  async batchSubscribe(e) {
    e.length && (await this.rpcBatchSubscribe(e), this.onBatchSubscribe(await Promise.all(e.map(async (t) => He2(ge2({}, t), { id: await this.getSubscriptionId(t.topic) })))));
  }
  async batchFetchMessages(e) {
    if (!e.length) return;
    this.logger.trace(`Fetching batch messages for ${e.length} subscriptions`);
    const t = await this.rpcBatchFetchMessages(e);
    t && t.messages && (await Ni((0, import_time2.toMiliseconds)(import_time2.ONE_SECOND)), await this.relayer.handleBatchMessageEvents(t.messages));
  }
  async onConnect() {
    await this.restart(), this.reset();
  }
  onDisconnect() {
    this.onDisable();
  }
  isInitialized() {
    if (!this.initialized) {
      const { message: e } = ht("NOT_INITIALIZED", this.name);
      throw new Error(e);
    }
  }
  async restartToComplete(e) {
    !this.relayer.connected && !this.relayer.connecting && (this.cached.push(e), await this.relayer.transportOpen());
  }
  async getClientId() {
    return this.clientId || (this.clientId = await this.relayer.core.crypto.getClientId()), this.clientId;
  }
  async getSubscriptionId(e) {
    return kc(e + await this.getClientId());
  }
};
var eo2 = Object.defineProperty;
var Ci2 = Object.getOwnPropertySymbols;
var to2 = Object.prototype.hasOwnProperty;
var io2 = Object.prototype.propertyIsEnumerable;
var Ye2 = (r2, e, t) => e in r2 ? eo2(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var Pi3 = (r2, e) => {
  for (var t in e || (e = {})) to2.call(e, t) && Ye2(r2, t, e[t]);
  if (Ci2) for (var t of Ci2(e)) io2.call(e, t) && Ye2(r2, t, e[t]);
  return r2;
};
var y3 = (r2, e, t) => Ye2(r2, typeof e != "symbol" ? e + "" : e, t);
var Si2 = class extends d {
  constructor(e) {
    super(e), y3(this, "protocol", "wc"), y3(this, "version", 2), y3(this, "core"), y3(this, "logger"), y3(this, "events", new import_events3.EventEmitter()), y3(this, "provider"), y3(this, "messages"), y3(this, "subscriber"), y3(this, "publisher"), y3(this, "name", $t2), y3(this, "transportExplicitlyClosed", false), y3(this, "initialized", false), y3(this, "connectionAttemptInProgress", false), y3(this, "relayUrl"), y3(this, "projectId"), y3(this, "packageName"), y3(this, "bundleId"), y3(this, "hasExperiencedNetworkDisruption", false), y3(this, "pingTimeout"), y3(this, "heartBeatTimeout", (0, import_time2.toMiliseconds)(import_time2.THIRTY_SECONDS + import_time2.FIVE_SECONDS)), y3(this, "reconnectTimeout"), y3(this, "connectPromise"), y3(this, "reconnectInProgress", false), y3(this, "requestsInFlight", []), y3(this, "connectTimeout", (0, import_time2.toMiliseconds)(import_time2.ONE_SECOND * 15)), y3(this, "request", async (t) => {
      var i3, s;
      this.logger.debug("Publishing Request Payload");
      const n2 = t.id || getBigIntRpcId().toString();
      await this.toEstablishConnection();
      try {
        this.logger.trace({ id: n2, method: t.method, topic: (i3 = t.params) == null ? void 0 : i3.topic }, "relayer.request - publishing...");
        const o2 = `${n2}:${((s = t.params) == null ? void 0 : s.tag) || ""}`;
        this.requestsInFlight.push(o2);
        const a2 = await this.provider.request(t);
        return this.requestsInFlight = this.requestsInFlight.filter((c3) => c3 !== o2), a2;
      } catch (o2) {
        throw this.logger.debug(`Failed to Publish Request: ${n2}`), o2;
      }
    }), y3(this, "resetPingTimeout", () => {
      _e() && (clearTimeout(this.pingTimeout), this.pingTimeout = setTimeout(() => {
        var t, i3, s, n2;
        try {
          this.logger.debug({}, "pingTimeout: Connection stalled, terminating..."), (n2 = (s = (i3 = (t = this.provider) == null ? void 0 : t.connection) == null ? void 0 : i3.socket) == null ? void 0 : s.terminate) == null || n2.call(s);
        } catch (o2) {
          this.logger.warn(o2, o2 == null ? void 0 : o2.message);
        }
      }, this.heartBeatTimeout));
    }), y3(this, "onPayloadHandler", (t) => {
      this.onProviderPayload(t), this.resetPingTimeout();
    }), y3(this, "onConnectHandler", () => {
      this.logger.warn({}, "Relayer connected 🛜"), this.startPingTimeout(), this.events.emit(C2.connect);
    }), y3(this, "onDisconnectHandler", () => {
      this.logger.warn({}, "Relayer disconnected 🛑"), this.requestsInFlight = [], this.onProviderDisconnect();
    }), y3(this, "onProviderErrorHandler", (t) => {
      this.logger.fatal(`Fatal socket error: ${t.message}`), this.events.emit(C2.error, t), this.logger.fatal("Fatal socket error received, closing transport"), this.transportClose();
    }), y3(this, "registerProviderListeners", () => {
      this.provider.on(L.payload, this.onPayloadHandler), this.provider.on(L.connect, this.onConnectHandler), this.provider.on(L.disconnect, this.onDisconnectHandler), this.provider.on(L.error, this.onProviderErrorHandler);
    }), this.core = e.core, this.logger = typeof e.logger < "u" && typeof e.logger != "string" ? E(e.logger, this.name) : (0, import_pino.default)(k({ level: e.logger || Nt2 })), this.messages = new _i2(this.logger, e.core), this.subscriber = new Ti2(this, this.logger), this.publisher = new qn(this, this.logger), this.relayUrl = (e == null ? void 0 : e.relayUrl) || Ue, this.projectId = e.projectId, ei() ? this.packageName = ri() : ni() && (this.bundleId = ri()), this.provider = {};
  }
  async init() {
    if (this.logger.trace("Initialized"), this.registerEventListeners(), await Promise.all([this.messages.init(), this.subscriber.init()]), this.initialized = true, this.subscriber.hasAnyTopics) try {
      await this.transportOpen();
    } catch (e) {
      this.logger.warn(e, e == null ? void 0 : e.message);
    }
  }
  get context() {
    return y(this.logger);
  }
  get connected() {
    var e, t, i3;
    return ((i3 = (t = (e = this.provider) == null ? void 0 : e.connection) == null ? void 0 : t.socket) == null ? void 0 : i3.readyState) === 1 || false;
  }
  get connecting() {
    var e, t, i3;
    return ((i3 = (t = (e = this.provider) == null ? void 0 : e.connection) == null ? void 0 : t.socket) == null ? void 0 : i3.readyState) === 0 || this.connectPromise !== void 0 || false;
  }
  async publish(e, t, i3) {
    this.isInitialized(), await this.publisher.publish(e, t, i3), await this.recordMessageEvent({ topic: e, message: t, publishedAt: Date.now(), transportType: Q.relay }, le2.outbound);
  }
  async subscribe(e, t) {
    var i3, s, n2;
    this.isInitialized(), (!(t != null && t.transportType) || (t == null ? void 0 : t.transportType) === "relay") && await this.toEstablishConnection();
    const o2 = typeof ((i3 = t == null ? void 0 : t.internal) == null ? void 0 : i3.throwOnFailedPublish) > "u" ? true : (s = t == null ? void 0 : t.internal) == null ? void 0 : s.throwOnFailedPublish;
    let a2 = ((n2 = this.subscriber.topicMap.get(e)) == null ? void 0 : n2[0]) || "", c3;
    const h4 = (l3) => {
      l3.topic === e && (this.subscriber.off($.created, h4), c3());
    };
    return await Promise.all([new Promise((l3) => {
      c3 = l3, this.subscriber.on($.created, h4);
    }), new Promise(async (l3, d3) => {
      a2 = await this.subscriber.subscribe(e, Pi3({ internal: { throwOnFailedPublish: o2 } }, t)).catch((g) => {
        o2 && d3(g);
      }) || a2, l3();
    })]), a2;
  }
  async unsubscribe(e, t) {
    this.isInitialized(), await this.subscriber.unsubscribe(e, t);
  }
  on(e, t) {
    this.events.on(e, t);
  }
  once(e, t) {
    this.events.once(e, t);
  }
  off(e, t) {
    this.events.off(e, t);
  }
  removeListener(e, t) {
    this.events.removeListener(e, t);
  }
  async transportDisconnect() {
    this.provider.disconnect && (this.hasExperiencedNetworkDisruption || this.connected) ? await yi(this.provider.disconnect(), 2e3, "provider.disconnect()").catch(() => this.onProviderDisconnect()) : this.onProviderDisconnect();
  }
  async transportClose() {
    this.transportExplicitlyClosed = true, await this.transportDisconnect();
  }
  async transportOpen(e) {
    if (!this.subscriber.hasAnyTopics) {
      this.logger.warn("Starting WS connection skipped because the client has no topics to work with.");
      return;
    }
    if (this.connectPromise ? (this.logger.debug({}, "Waiting for existing connection attempt to resolve..."), await this.connectPromise, this.logger.debug({}, "Existing connection attempt resolved")) : (this.connectPromise = new Promise(async (t, i3) => {
      await this.connect(e).then(t).catch(i3).finally(() => {
        this.connectPromise = void 0;
      });
    }), await this.connectPromise), !this.connected) throw new Error(`Couldn't establish socket connection to the relay server: ${this.relayUrl}`);
  }
  async restartTransport(e) {
    this.logger.debug({}, "Restarting transport..."), !this.connectionAttemptInProgress && (this.relayUrl = e || this.relayUrl, await this.confirmOnlineStateOrThrow(), await this.transportClose(), await this.transportOpen());
  }
  async confirmOnlineStateOrThrow() {
    if (!await Na()) throw new Error("No internet connection detected. Please restart your network and try again.");
  }
  async handleBatchMessageEvents(e) {
    if ((e == null ? void 0 : e.length) === 0) {
      this.logger.trace("Batch message events is empty. Ignoring...");
      return;
    }
    const t = e.sort((i3, s) => i3.publishedAt - s.publishedAt);
    this.logger.debug(`Batch of ${t.length} message events sorted`);
    for (const i3 of t) try {
      await this.onMessageEvent(i3);
    } catch (s) {
      this.logger.warn(s, "Error while processing batch message event: " + (s == null ? void 0 : s.message));
    }
    this.logger.trace(`Batch of ${t.length} message events processed`);
  }
  async onLinkMessageEvent(e, t) {
    const { topic: i3 } = e;
    if (!t.sessionExists) {
      const s = Ei(import_time2.FIVE_MINUTES), n2 = { topic: i3, expiry: s, relay: { protocol: "irn" }, active: false };
      await this.core.pairing.pairings.set(i3, n2);
    }
    this.events.emit(C2.message, e), await this.recordMessageEvent(e, le2.inbound);
  }
  async connect(e) {
    await this.confirmOnlineStateOrThrow(), e && e !== this.relayUrl && (this.relayUrl = e, await this.transportDisconnect()), this.connectionAttemptInProgress = true, this.transportExplicitlyClosed = false;
    let t = 1;
    for (; t < 6; ) {
      try {
        if (this.transportExplicitlyClosed) break;
        this.logger.debug({}, `Connecting to ${this.relayUrl}, attempt: ${t}...`), await this.createProvider(), await new Promise(async (i3, s) => {
          const n2 = () => {
            s(new Error("Connection interrupted while trying to subscribe"));
          };
          this.provider.once(L.disconnect, n2), await yi(new Promise((o2, a2) => {
            this.provider.connect().then(o2).catch(a2);
          }), this.connectTimeout, `Socket stalled when trying to connect to ${this.relayUrl}`).catch((o2) => {
            s(o2);
          }).finally(() => {
            this.provider.off(L.disconnect, n2), clearTimeout(this.reconnectTimeout);
          }), await new Promise(async (o2, a2) => {
            const c3 = () => {
              a2(new Error("Connection interrupted while trying to subscribe"));
            };
            this.provider.once(L.disconnect, c3), await this.subscriber.start().then(o2).catch(a2).finally(() => {
              this.provider.off(L.disconnect, c3);
            });
          }), this.hasExperiencedNetworkDisruption = false, i3();
        });
      } catch (i3) {
        await this.subscriber.stop();
        const s = i3;
        this.logger.warn({}, s.message), this.hasExperiencedNetworkDisruption = true;
      } finally {
        this.connectionAttemptInProgress = false;
      }
      if (this.connected) {
        this.logger.debug({}, `Connected to ${this.relayUrl} successfully on attempt: ${t}`);
        break;
      }
      await new Promise((i3) => setTimeout(i3, (0, import_time2.toMiliseconds)(t * 1))), t++;
    }
  }
  startPingTimeout() {
    var e, t, i3, s, n2;
    if (_e()) try {
      (t = (e = this.provider) == null ? void 0 : e.connection) != null && t.socket && ((n2 = (s = (i3 = this.provider) == null ? void 0 : i3.connection) == null ? void 0 : s.socket) == null || n2.on("ping", () => {
        this.resetPingTimeout();
      })), this.resetPingTimeout();
    } catch (o2) {
      this.logger.warn(o2, o2 == null ? void 0 : o2.message);
    }
  }
  async createProvider() {
    this.provider.connection && this.unregisterProviderListeners();
    const e = await this.core.crypto.signJWT(this.relayUrl);
    this.provider = new o(new f(si({ sdkVersion: _e2, protocol: this.protocol, version: this.version, relayUrl: this.relayUrl, projectId: this.projectId, auth: e, useOnCloseEvent: true, bundleId: this.bundleId, packageName: this.packageName }))), this.registerProviderListeners();
  }
  async recordMessageEvent(e, t) {
    const { topic: i3, message: s } = e;
    await this.messages.set(i3, s, t);
  }
  async shouldIgnoreMessageEvent(e) {
    const { topic: t, message: i3 } = e;
    if (!i3 || i3.length === 0) return this.logger.warn(`Ignoring invalid/empty message: ${i3}`), true;
    if (!await this.subscriber.isKnownTopic(t)) return this.logger.warn(`Ignoring message for unknown topic ${t}`), true;
    const s = this.messages.has(t, i3);
    return s && this.logger.warn(`Ignoring duplicate message: ${i3}`), s;
  }
  async onProviderPayload(e) {
    if (this.logger.debug("Incoming Relay Payload"), this.logger.trace({ type: "payload", direction: "incoming", payload: e }), isJsonRpcRequest(e)) {
      if (!e.method.endsWith(zt)) return;
      const t = e.params, { topic: i3, message: s, publishedAt: n2, attestation: o2 } = t.data, a2 = { topic: i3, message: s, publishedAt: n2, transportType: Q.relay, attestation: o2 };
      this.logger.debug("Emitting Relayer Payload"), this.logger.trace(Pi3({ type: "event", event: t.id }, a2)), this.events.emit(t.id, a2), await this.acknowledgePayload(e), await this.onMessageEvent(a2);
    } else isJsonRpcResponse(e) && this.events.emit(C2.message_ack, e);
  }
  async onMessageEvent(e) {
    await this.shouldIgnoreMessageEvent(e) || (await this.recordMessageEvent(e, le2.inbound), this.events.emit(C2.message, e));
  }
  async acknowledgePayload(e) {
    const t = formatJsonRpcResult(e.id, true);
    await this.provider.connection.send(t);
  }
  unregisterProviderListeners() {
    this.provider.off(L.payload, this.onPayloadHandler), this.provider.off(L.connect, this.onConnectHandler), this.provider.off(L.disconnect, this.onDisconnectHandler), this.provider.off(L.error, this.onProviderErrorHandler), clearTimeout(this.pingTimeout);
  }
  async registerEventListeners() {
    let e = await Na();
    Ua(async (t) => {
      e !== t && (e = t, t ? await this.transportOpen().catch((i3) => this.logger.error(i3, i3 == null ? void 0 : i3.message)) : (this.hasExperiencedNetworkDisruption = true, await this.transportDisconnect(), this.transportExplicitlyClosed = false));
    }), this.core.heartbeat.on(r.pulse, async () => {
      if (!this.transportExplicitlyClosed && !this.connected && Ta()) try {
        await this.confirmOnlineStateOrThrow(), await this.transportOpen();
      } catch (t) {
        this.logger.warn(t, t == null ? void 0 : t.message);
      }
    });
  }
  async onProviderDisconnect() {
    clearTimeout(this.pingTimeout), this.events.emit(C2.disconnect), this.connectionAttemptInProgress = false, !this.reconnectInProgress && (this.reconnectInProgress = true, await this.subscriber.stop(), this.subscriber.hasAnyTopics && (this.transportExplicitlyClosed || (this.reconnectTimeout = setTimeout(async () => {
      await this.transportOpen().catch((e) => this.logger.error(e, e == null ? void 0 : e.message)), this.reconnectTimeout = void 0, this.reconnectInProgress = false;
    }, (0, import_time2.toMiliseconds)(Lt2)))));
  }
  isInitialized() {
    if (!this.initialized) {
      const { message: e } = ht("NOT_INITIALIZED", this.name);
      throw new Error(e);
    }
  }
  async toEstablishConnection() {
    if (await this.confirmOnlineStateOrThrow(), !this.connected) {
      if (this.connectPromise) {
        await this.connectPromise;
        return;
      }
      await this.connect();
    }
  }
};
function so2() {
}
function Oi2(r2) {
  if (!r2 || typeof r2 != "object") return false;
  const e = Object.getPrototypeOf(r2);
  return e === null || e === Object.prototype || Object.getPrototypeOf(e) === null ? Object.prototype.toString.call(r2) === "[object Object]" : false;
}
function Ri2(r2) {
  return Object.getOwnPropertySymbols(r2).filter((e) => Object.prototype.propertyIsEnumerable.call(r2, e));
}
function Ai2(r2) {
  return r2 == null ? r2 === void 0 ? "[object Undefined]" : "[object Null]" : Object.prototype.toString.call(r2);
}
var ro2 = "[object RegExp]";
var no2 = "[object String]";
var oo2 = "[object Number]";
var ao2 = "[object Boolean]";
var xi2 = "[object Arguments]";
var co2 = "[object Symbol]";
var ho2 = "[object Date]";
var lo2 = "[object Map]";
var uo2 = "[object Set]";
var go2 = "[object Array]";
var po2 = "[object Function]";
var yo2 = "[object ArrayBuffer]";
var Je2 = "[object Object]";
var bo2 = "[object Error]";
var mo2 = "[object DataView]";
var fo2 = "[object Uint8Array]";
var Do = "[object Uint8ClampedArray]";
var vo2 = "[object Uint16Array]";
var wo2 = "[object Uint32Array]";
var _o2 = "[object BigUint64Array]";
var Eo2 = "[object Int8Array]";
var Io2 = "[object Int16Array]";
var To2 = "[object Int32Array]";
var Co2 = "[object BigInt64Array]";
var Po3 = "[object Float32Array]";
var So2 = "[object Float64Array]";
function Oo2(r2, e) {
  return r2 === e || Number.isNaN(r2) && Number.isNaN(e);
}
function Ro2(r2, e, t) {
  return pe2(r2, e, void 0, void 0, void 0, void 0, t);
}
function pe2(r2, e, t, i3, s, n2, o2) {
  const a2 = o2(r2, e, t, i3, s, n2);
  if (a2 !== void 0) return a2;
  if (typeof r2 == typeof e) switch (typeof r2) {
    case "bigint":
    case "string":
    case "boolean":
    case "symbol":
    case "undefined":
      return r2 === e;
    case "number":
      return r2 === e || Object.is(r2, e);
    case "function":
      return r2 === e;
    case "object":
      return ye2(r2, e, n2, o2);
  }
  return ye2(r2, e, n2, o2);
}
function ye2(r2, e, t, i3) {
  if (Object.is(r2, e)) return true;
  let s = Ai2(r2), n2 = Ai2(e);
  if (s === xi2 && (s = Je2), n2 === xi2 && (n2 = Je2), s !== n2) return false;
  switch (s) {
    case no2:
      return r2.toString() === e.toString();
    case oo2: {
      const c3 = r2.valueOf(), h4 = e.valueOf();
      return Oo2(c3, h4);
    }
    case ao2:
    case ho2:
    case co2:
      return Object.is(r2.valueOf(), e.valueOf());
    case ro2:
      return r2.source === e.source && r2.flags === e.flags;
    case po2:
      return r2 === e;
  }
  t = t ?? /* @__PURE__ */ new Map();
  const o2 = t.get(r2), a2 = t.get(e);
  if (o2 != null && a2 != null) return o2 === e;
  t.set(r2, e), t.set(e, r2);
  try {
    switch (s) {
      case lo2: {
        if (r2.size !== e.size) return false;
        for (const [c3, h4] of r2.entries()) if (!e.has(c3) || !pe2(h4, e.get(c3), c3, r2, e, t, i3)) return false;
        return true;
      }
      case uo2: {
        if (r2.size !== e.size) return false;
        const c3 = Array.from(r2.values()), h4 = Array.from(e.values());
        for (let l3 = 0; l3 < c3.length; l3++) {
          const d3 = c3[l3], g = h4.findIndex((_) => pe2(d3, _, void 0, r2, e, t, i3));
          if (g === -1) return false;
          h4.splice(g, 1);
        }
        return true;
      }
      case go2:
      case fo2:
      case Do:
      case vo2:
      case wo2:
      case _o2:
      case Eo2:
      case Io2:
      case To2:
      case Co2:
      case Po3:
      case So2: {
        if (typeof Buffer < "u" && Buffer.isBuffer(r2) !== Buffer.isBuffer(e) || r2.length !== e.length) return false;
        for (let c3 = 0; c3 < r2.length; c3++) if (!pe2(r2[c3], e[c3], c3, r2, e, t, i3)) return false;
        return true;
      }
      case yo2:
        return r2.byteLength !== e.byteLength ? false : ye2(new Uint8Array(r2), new Uint8Array(e), t, i3);
      case mo2:
        return r2.byteLength !== e.byteLength || r2.byteOffset !== e.byteOffset ? false : ye2(new Uint8Array(r2), new Uint8Array(e), t, i3);
      case bo2:
        return r2.name === e.name && r2.message === e.message;
      case Je2: {
        if (!(ye2(r2.constructor, e.constructor, t, i3) || Oi2(r2) && Oi2(e))) return false;
        const h4 = [...Object.keys(r2), ...Ri2(r2)], l3 = [...Object.keys(e), ...Ri2(e)];
        if (h4.length !== l3.length) return false;
        for (let d3 = 0; d3 < h4.length; d3++) {
          const g = h4[d3], _ = r2[g];
          if (!Object.hasOwn(e, g)) return false;
          const u3 = e[g];
          if (!pe2(_, u3, g, r2, e, t, i3)) return false;
        }
        return true;
      }
      default:
        return false;
    }
  } finally {
    t.delete(r2), t.delete(e);
  }
}
function Ao2(r2, e) {
  return Ro2(r2, e, so2);
}
var xo2 = Object.defineProperty;
var Ni2 = Object.getOwnPropertySymbols;
var No2 = Object.prototype.hasOwnProperty;
var $o2 = Object.prototype.propertyIsEnumerable;
var Xe2 = (r2, e, t) => e in r2 ? xo2(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var $i2 = (r2, e) => {
  for (var t in e || (e = {})) No2.call(e, t) && Xe2(r2, t, e[t]);
  if (Ni2) for (var t of Ni2(e)) $o2.call(e, t) && Xe2(r2, t, e[t]);
  return r2;
};
var z = (r2, e, t) => Xe2(r2, typeof e != "symbol" ? e + "" : e, t);
var zi2 = class extends f3 {
  constructor(e, t, i3, s = B, n2 = void 0) {
    super(e, t, i3, s), this.core = e, this.logger = t, this.name = i3, z(this, "map", /* @__PURE__ */ new Map()), z(this, "version", kt2), z(this, "cached", []), z(this, "initialized", false), z(this, "getKey"), z(this, "storagePrefix", B), z(this, "recentlyDeleted", []), z(this, "recentlyDeletedLimit", 200), z(this, "init", async () => {
      this.initialized || (this.logger.trace("Initialized"), await this.restore(), this.cached.forEach((o2) => {
        this.getKey && o2 !== null && !Et(o2) ? this.map.set(this.getKey(o2), o2) : la(o2) ? this.map.set(o2.id, o2) : da(o2) && this.map.set(o2.topic, o2);
      }), this.cached = [], this.initialized = true);
    }), z(this, "set", async (o2, a2) => {
      this.isInitialized(), this.map.has(o2) ? await this.update(o2, a2) : (this.logger.debug("Setting value"), this.logger.trace({ type: "method", method: "set", key: o2, value: a2 }), this.map.set(o2, a2), await this.persist());
    }), z(this, "get", (o2) => (this.isInitialized(), this.logger.debug("Getting value"), this.logger.trace({ type: "method", method: "get", key: o2 }), this.getData(o2))), z(this, "getAll", (o2) => (this.isInitialized(), o2 ? this.values.filter((a2) => Object.keys(o2).every((c3) => Ao2(a2[c3], o2[c3]))) : this.values)), z(this, "update", async (o2, a2) => {
      this.isInitialized(), this.logger.debug("Updating value"), this.logger.trace({ type: "method", method: "update", key: o2, update: a2 });
      const c3 = $i2($i2({}, this.getData(o2)), a2);
      this.map.set(o2, c3), await this.persist();
    }), z(this, "delete", async (o2, a2) => {
      this.isInitialized(), this.map.has(o2) && (this.logger.debug("Deleting value"), this.logger.trace({ type: "method", method: "delete", key: o2, reason: a2 }), this.map.delete(o2), this.addToRecentlyDeleted(o2), await this.persist());
    }), this.logger = E(t, this.name), this.storagePrefix = s, this.getKey = n2;
  }
  get context() {
    return y(this.logger);
  }
  get storageKey() {
    return this.storagePrefix + this.version + this.core.customStoragePrefix + "//" + this.name;
  }
  get length() {
    return this.map.size;
  }
  get keys() {
    return Array.from(this.map.keys());
  }
  get values() {
    return Array.from(this.map.values());
  }
  addToRecentlyDeleted(e) {
    this.recentlyDeleted.push(e), this.recentlyDeleted.length >= this.recentlyDeletedLimit && this.recentlyDeleted.splice(0, this.recentlyDeletedLimit / 2);
  }
  async setDataStore(e) {
    await this.core.storage.setItem(this.storageKey, e);
  }
  async getDataStore() {
    return await this.core.storage.getItem(this.storageKey);
  }
  getData(e) {
    const t = this.map.get(e);
    if (!t) {
      if (this.recentlyDeleted.includes(e)) {
        const { message: s } = ht("MISSING_OR_INVALID", `Record was recently deleted - ${this.name}: ${e}`);
        throw this.logger.error(s), new Error(s);
      }
      const { message: i3 } = ht("NO_MATCHING_KEY", `${this.name}: ${e}`);
      throw this.logger.error(i3), new Error(i3);
    }
    return t;
  }
  async persist() {
    await this.setDataStore(this.values);
  }
  async restore() {
    try {
      const e = await this.getDataStore();
      if (typeof e > "u" || !e.length) return;
      if (this.map.size) {
        const { message: t } = ht("RESTORE_WILL_OVERRIDE", this.name);
        throw this.logger.error(t), new Error(t);
      }
      this.cached = e, this.logger.debug(`Successfully Restored value for ${this.name}`), this.logger.trace({ type: "method", method: "restore", value: this.values });
    } catch (e) {
      this.logger.debug(`Failed to Restore value for ${this.name}`), this.logger.error(e);
    }
  }
  isInitialized() {
    if (!this.initialized) {
      const { message: e } = ht("NOT_INITIALIZED", this.name);
      throw new Error(e);
    }
  }
};
var zo = Object.defineProperty;
var Lo2 = (r2, e, t) => e in r2 ? zo(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var p2 = (r2, e, t) => Lo2(r2, typeof e != "symbol" ? e + "" : e, t);
var Li2 = class {
  constructor(e, t) {
    this.core = e, this.logger = t, p2(this, "name", Mt2), p2(this, "version", Kt2), p2(this, "events", new import_events3.default()), p2(this, "pairings"), p2(this, "initialized", false), p2(this, "storagePrefix", B), p2(this, "ignoredPayloadTypes", [Ft]), p2(this, "registeredMethods", []), p2(this, "init", async () => {
      this.initialized || (await this.pairings.init(), await this.cleanup(), this.registerRelayerEvents(), this.registerExpirerEvents(), this.initialized = true, this.logger.trace("Initialized"));
    }), p2(this, "register", ({ methods: i3 }) => {
      this.isInitialized(), this.registeredMethods = [.../* @__PURE__ */ new Set([...this.registeredMethods, ...i3])];
    }), p2(this, "create", async (i3) => {
      this.isInitialized();
      const s = jc(), n2 = await this.core.crypto.setSymKey(s), o2 = Ei(import_time2.FIVE_MINUTES), a2 = { protocol: xt2 }, c3 = { topic: n2, expiry: o2, relay: a2, active: false, methods: i3 == null ? void 0 : i3.methods }, h4 = Wc({ protocol: this.core.protocol, version: this.core.version, topic: n2, symKey: s, relay: a2, expiryTimestamp: o2, methods: i3 == null ? void 0 : i3.methods });
      return this.events.emit(re2.create, c3), this.core.expirer.set(n2, o2), await this.pairings.set(n2, c3), await this.core.relayer.subscribe(n2, { transportType: i3 == null ? void 0 : i3.transportType }), { topic: n2, uri: h4 };
    }), p2(this, "pair", async (i3) => {
      this.isInitialized();
      const s = this.core.eventClient.createEvent({ properties: { topic: i3 == null ? void 0 : i3.uri, trace: [G2.pairing_started] } });
      this.isValidPair(i3, s);
      const { topic: n2, symKey: o2, relay: a2, expiryTimestamp: c3, methods: h4 } = Gc(i3.uri);
      s.props.properties.topic = n2, s.addTrace(G2.pairing_uri_validation_success), s.addTrace(G2.pairing_uri_not_expired);
      let l3;
      if (this.pairings.keys.includes(n2)) {
        if (l3 = this.pairings.get(n2), s.addTrace(G2.existing_pairing), l3.active) throw s.setError(Y2.active_pairing_already_exists), new Error(`Pairing already exists: ${n2}. Please try again with a new connection URI.`);
        s.addTrace(G2.pairing_not_expired);
      }
      const d3 = c3 || Ei(import_time2.FIVE_MINUTES), g = { topic: n2, relay: a2, expiry: d3, active: false, methods: h4 };
      this.core.expirer.set(n2, d3), await this.pairings.set(n2, g), s.addTrace(G2.store_new_pairing), i3.activatePairing && await this.activate({ topic: n2 }), this.events.emit(re2.create, g), s.addTrace(G2.emit_inactive_pairing), this.core.crypto.keychain.has(n2) || await this.core.crypto.setSymKey(o2, n2), s.addTrace(G2.subscribing_pairing_topic);
      try {
        await this.core.relayer.confirmOnlineStateOrThrow();
      } catch {
        s.setError(Y2.no_internet_connection);
      }
      try {
        await this.core.relayer.subscribe(n2, { relay: a2 });
      } catch (_) {
        throw s.setError(Y2.subscribe_pairing_topic_failure), _;
      }
      return s.addTrace(G2.subscribe_pairing_topic_success), g;
    }), p2(this, "activate", async ({ topic: i3 }) => {
      this.isInitialized();
      const s = Ei(import_time2.FIVE_MINUTES);
      this.core.expirer.set(i3, s), await this.pairings.update(i3, { active: true, expiry: s });
    }), p2(this, "ping", async (i3) => {
      this.isInitialized(), await this.isValidPing(i3), this.logger.warn("ping() is deprecated and will be removed in the next major release.");
      const { topic: s } = i3;
      if (this.pairings.keys.includes(s)) {
        const n2 = await this.sendRequest(s, "wc_pairingPing", {}), { done: o2, resolve: a2, reject: c3 } = gi();
        this.events.once(xi("pairing_ping", n2), ({ error: h4 }) => {
          h4 ? c3(h4) : a2();
        }), await o2();
      }
    }), p2(this, "updateExpiry", async ({ topic: i3, expiry: s }) => {
      this.isInitialized(), await this.pairings.update(i3, { expiry: s });
    }), p2(this, "updateMetadata", async ({ topic: i3, metadata: s }) => {
      this.isInitialized(), await this.pairings.update(i3, { peerMetadata: s });
    }), p2(this, "getPairings", () => (this.isInitialized(), this.pairings.values)), p2(this, "disconnect", async (i3) => {
      this.isInitialized(), await this.isValidDisconnect(i3);
      const { topic: s } = i3;
      this.pairings.keys.includes(s) && (await this.sendRequest(s, "wc_pairingDelete", Nt("USER_DISCONNECTED")), await this.deletePairing(s));
    }), p2(this, "formatUriFromPairing", (i3) => {
      this.isInitialized();
      const { topic: s, relay: n2, expiry: o2, methods: a2 } = i3, c3 = this.core.crypto.keychain.get(s);
      return Wc({ protocol: this.core.protocol, version: this.core.version, topic: s, symKey: c3, relay: n2, expiryTimestamp: o2, methods: a2 });
    }), p2(this, "sendRequest", async (i3, s, n2) => {
      const o2 = formatJsonRpcRequest(s, n2), a2 = await this.core.crypto.encode(i3, o2), c3 = se2[s].req;
      return this.core.history.set(i3, o2), this.core.relayer.publish(i3, a2, c3), o2.id;
    }), p2(this, "sendResult", async (i3, s, n2) => {
      const o2 = formatJsonRpcResult(i3, n2), a2 = await this.core.crypto.encode(s, o2), c3 = (await this.core.history.get(s, i3)).request.method, h4 = se2[c3].res;
      await this.core.relayer.publish(s, a2, h4), await this.core.history.resolve(o2);
    }), p2(this, "sendError", async (i3, s, n2) => {
      const o2 = formatJsonRpcError(i3, n2), a2 = await this.core.crypto.encode(s, o2), c3 = (await this.core.history.get(s, i3)).request.method, h4 = se2[c3] ? se2[c3].res : se2.unregistered_method.res;
      await this.core.relayer.publish(s, a2, h4), await this.core.history.resolve(o2);
    }), p2(this, "deletePairing", async (i3, s) => {
      await this.core.relayer.unsubscribe(i3), await Promise.all([this.pairings.delete(i3, Nt("USER_DISCONNECTED")), this.core.crypto.deleteSymKey(i3), s ? Promise.resolve() : this.core.expirer.del(i3)]);
    }), p2(this, "cleanup", async () => {
      const i3 = this.pairings.getAll().filter((s) => vi(s.expiry));
      await Promise.all(i3.map((s) => this.deletePairing(s.topic)));
    }), p2(this, "onRelayEventRequest", async (i3) => {
      const { topic: s, payload: n2 } = i3;
      switch (n2.method) {
        case "wc_pairingPing":
          return await this.onPairingPingRequest(s, n2);
        case "wc_pairingDelete":
          return await this.onPairingDeleteRequest(s, n2);
        default:
          return await this.onUnknownRpcMethodRequest(s, n2);
      }
    }), p2(this, "onRelayEventResponse", async (i3) => {
      const { topic: s, payload: n2 } = i3, o2 = (await this.core.history.get(s, n2.id)).request.method;
      switch (o2) {
        case "wc_pairingPing":
          return this.onPairingPingResponse(s, n2);
        default:
          return this.onUnknownRpcMethodResponse(o2);
      }
    }), p2(this, "onPairingPingRequest", async (i3, s) => {
      const { id: n2 } = s;
      try {
        this.isValidPing({ topic: i3 }), await this.sendResult(n2, i3, true), this.events.emit(re2.ping, { id: n2, topic: i3 });
      } catch (o2) {
        await this.sendError(n2, i3, o2), this.logger.error(o2);
      }
    }), p2(this, "onPairingPingResponse", (i3, s) => {
      const { id: n2 } = s;
      setTimeout(() => {
        isJsonRpcResult(s) ? this.events.emit(xi("pairing_ping", n2), {}) : isJsonRpcError(s) && this.events.emit(xi("pairing_ping", n2), { error: s.error });
      }, 500);
    }), p2(this, "onPairingDeleteRequest", async (i3, s) => {
      const { id: n2 } = s;
      try {
        this.isValidDisconnect({ topic: i3 }), await this.deletePairing(i3), this.events.emit(re2.delete, { id: n2, topic: i3 });
      } catch (o2) {
        await this.sendError(n2, i3, o2), this.logger.error(o2);
      }
    }), p2(this, "onUnknownRpcMethodRequest", async (i3, s) => {
      const { id: n2, method: o2 } = s;
      try {
        if (this.registeredMethods.includes(o2)) return;
        const a2 = Nt("WC_METHOD_UNSUPPORTED", o2);
        await this.sendError(n2, i3, a2), this.logger.error(a2);
      } catch (a2) {
        await this.sendError(n2, i3, a2), this.logger.error(a2);
      }
    }), p2(this, "onUnknownRpcMethodResponse", (i3) => {
      this.registeredMethods.includes(i3) || this.logger.error(Nt("WC_METHOD_UNSUPPORTED", i3));
    }), p2(this, "isValidPair", (i3, s) => {
      var n2;
      if (!ma(i3)) {
        const { message: a2 } = ht("MISSING_OR_INVALID", `pair() params: ${i3}`);
        throw s.setError(Y2.malformed_pairing_uri), new Error(a2);
      }
      if (!fa(i3.uri)) {
        const { message: a2 } = ht("MISSING_OR_INVALID", `pair() uri: ${i3.uri}`);
        throw s.setError(Y2.malformed_pairing_uri), new Error(a2);
      }
      const o2 = Gc(i3 == null ? void 0 : i3.uri);
      if (!((n2 = o2 == null ? void 0 : o2.relay) != null && n2.protocol)) {
        const { message: a2 } = ht("MISSING_OR_INVALID", "pair() uri#relay-protocol");
        throw s.setError(Y2.malformed_pairing_uri), new Error(a2);
      }
      if (!(o2 != null && o2.symKey)) {
        const { message: a2 } = ht("MISSING_OR_INVALID", "pair() uri#symKey");
        throw s.setError(Y2.malformed_pairing_uri), new Error(a2);
      }
      if (o2 != null && o2.expiryTimestamp && (0, import_time2.toMiliseconds)(o2 == null ? void 0 : o2.expiryTimestamp) < Date.now()) {
        s.setError(Y2.pairing_expired);
        const { message: a2 } = ht("EXPIRED", "pair() URI has expired. Please try again with a new connection URI.");
        throw new Error(a2);
      }
    }), p2(this, "isValidPing", async (i3) => {
      if (!ma(i3)) {
        const { message: n2 } = ht("MISSING_OR_INVALID", `ping() params: ${i3}`);
        throw new Error(n2);
      }
      const { topic: s } = i3;
      await this.isValidPairingTopic(s);
    }), p2(this, "isValidDisconnect", async (i3) => {
      if (!ma(i3)) {
        const { message: n2 } = ht("MISSING_OR_INVALID", `disconnect() params: ${i3}`);
        throw new Error(n2);
      }
      const { topic: s } = i3;
      await this.isValidPairingTopic(s);
    }), p2(this, "isValidPairingTopic", async (i3) => {
      if (!nt(i3, false)) {
        const { message: s } = ht("MISSING_OR_INVALID", `pairing topic should be a string: ${i3}`);
        throw new Error(s);
      }
      if (!this.pairings.keys.includes(i3)) {
        const { message: s } = ht("NO_MATCHING_KEY", `pairing topic doesn't exist: ${i3}`);
        throw new Error(s);
      }
      if (vi(this.pairings.get(i3).expiry)) {
        await this.deletePairing(i3);
        const { message: s } = ht("EXPIRED", `pairing topic: ${i3}`);
        throw new Error(s);
      }
    }), this.core = e, this.logger = E(t, this.name), this.pairings = new zi2(this.core, this.logger, this.name, this.storagePrefix);
  }
  get context() {
    return y(this.logger);
  }
  isInitialized() {
    if (!this.initialized) {
      const { message: e } = ht("NOT_INITIALIZED", this.name);
      throw new Error(e);
    }
  }
  registerRelayerEvents() {
    this.core.relayer.on(C2.message, async (e) => {
      const { topic: t, message: i3, transportType: s } = e;
      if (this.pairings.keys.includes(t) && s !== Q.link_mode && !this.ignoredPayloadTypes.includes(this.core.crypto.getPayloadType(i3))) try {
        const n2 = await this.core.crypto.decode(t, i3);
        isJsonRpcRequest(n2) ? (this.core.history.set(t, n2), await this.onRelayEventRequest({ topic: t, payload: n2 })) : isJsonRpcResponse(n2) && (await this.core.history.resolve(n2), await this.onRelayEventResponse({ topic: t, payload: n2 }), this.core.history.delete(t, n2.id)), await this.core.relayer.messages.ack(t, i3);
      } catch (n2) {
        this.logger.error(n2);
      }
    });
  }
  registerExpirerEvents() {
    this.core.expirer.on(M2.expired, async (e) => {
      const { topic: t } = bi(e.target);
      t && this.pairings.keys.includes(t) && (await this.deletePairing(t, true), this.events.emit(re2.expire, { topic: t }));
    });
  }
};
var ko = Object.defineProperty;
var jo = (r2, e, t) => e in r2 ? ko(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var O2 = (r2, e, t) => jo(r2, typeof e != "symbol" ? e + "" : e, t);
var ki2 = class extends I {
  constructor(e, t) {
    super(e, t), this.core = e, this.logger = t, O2(this, "records", /* @__PURE__ */ new Map()), O2(this, "events", new import_events3.EventEmitter()), O2(this, "name", Bt2), O2(this, "version", Vt2), O2(this, "cached", []), O2(this, "initialized", false), O2(this, "storagePrefix", B), O2(this, "init", async () => {
      this.initialized || (this.logger.trace("Initialized"), await this.restore(), this.cached.forEach((i3) => this.records.set(i3.id, i3)), this.cached = [], this.registerEventListeners(), this.initialized = true);
    }), O2(this, "set", (i3, s, n2) => {
      if (this.isInitialized(), this.logger.debug("Setting JSON-RPC request history record"), this.logger.trace({ type: "method", method: "set", topic: i3, request: s, chainId: n2 }), this.records.has(s.id)) return;
      const o2 = { id: s.id, topic: i3, request: { method: s.method, params: s.params || null }, chainId: n2, expiry: Ei(import_time2.THIRTY_DAYS) };
      this.records.set(o2.id, o2), this.persist(), this.events.emit(F2.created, o2);
    }), O2(this, "resolve", async (i3) => {
      if (this.isInitialized(), this.logger.debug("Updating JSON-RPC response history record"), this.logger.trace({ type: "method", method: "update", response: i3 }), !this.records.has(i3.id)) return;
      const s = await this.getRecord(i3.id);
      typeof s.response > "u" && (s.response = isJsonRpcError(i3) ? { error: i3.error } : { result: i3.result }, this.records.set(s.id, s), this.persist(), this.events.emit(F2.updated, s));
    }), O2(this, "get", async (i3, s) => (this.isInitialized(), this.logger.debug("Getting record"), this.logger.trace({ type: "method", method: "get", topic: i3, id: s }), await this.getRecord(s))), O2(this, "delete", (i3, s) => {
      this.isInitialized(), this.logger.debug("Deleting record"), this.logger.trace({ type: "method", method: "delete", id: s }), this.values.forEach((n2) => {
        if (n2.topic === i3) {
          if (typeof s < "u" && n2.id !== s) return;
          this.records.delete(n2.id), this.events.emit(F2.deleted, n2);
        }
      }), this.persist();
    }), O2(this, "exists", async (i3, s) => (this.isInitialized(), this.records.has(s) ? (await this.getRecord(s)).topic === i3 : false)), O2(this, "on", (i3, s) => {
      this.events.on(i3, s);
    }), O2(this, "once", (i3, s) => {
      this.events.once(i3, s);
    }), O2(this, "off", (i3, s) => {
      this.events.off(i3, s);
    }), O2(this, "removeListener", (i3, s) => {
      this.events.removeListener(i3, s);
    }), this.logger = E(t, this.name);
  }
  get context() {
    return y(this.logger);
  }
  get storageKey() {
    return this.storagePrefix + this.version + this.core.customStoragePrefix + "//" + this.name;
  }
  get size() {
    return this.records.size;
  }
  get keys() {
    return Array.from(this.records.keys());
  }
  get values() {
    return Array.from(this.records.values());
  }
  get pending() {
    const e = [];
    return this.values.forEach((t) => {
      if (typeof t.response < "u") return;
      const i3 = { topic: t.topic, request: formatJsonRpcRequest(t.request.method, t.request.params, t.id), chainId: t.chainId };
      return e.push(i3);
    }), e;
  }
  async setJsonRpcRecords(e) {
    await this.core.storage.setItem(this.storageKey, e);
  }
  async getJsonRpcRecords() {
    return await this.core.storage.getItem(this.storageKey);
  }
  getRecord(e) {
    this.isInitialized();
    const t = this.records.get(e);
    if (!t) {
      const { message: i3 } = ht("NO_MATCHING_KEY", `${this.name}: ${e}`);
      throw new Error(i3);
    }
    return t;
  }
  async persist() {
    await this.setJsonRpcRecords(this.values), this.events.emit(F2.sync);
  }
  async restore() {
    try {
      const e = await this.getJsonRpcRecords();
      if (typeof e > "u" || !e.length) return;
      if (this.records.size) {
        const { message: t } = ht("RESTORE_WILL_OVERRIDE", this.name);
        throw this.logger.error(t), new Error(t);
      }
      this.cached = e, this.logger.debug(`Successfully Restored records for ${this.name}`), this.logger.trace({ type: "method", method: "restore", records: this.values });
    } catch (e) {
      this.logger.debug(`Failed to Restore records for ${this.name}`), this.logger.error(e);
    }
  }
  registerEventListeners() {
    this.events.on(F2.created, (e) => {
      const t = F2.created;
      this.logger.info(`Emitting ${t}`), this.logger.debug({ type: "event", event: t, record: e });
    }), this.events.on(F2.updated, (e) => {
      const t = F2.updated;
      this.logger.info(`Emitting ${t}`), this.logger.debug({ type: "event", event: t, record: e });
    }), this.events.on(F2.deleted, (e) => {
      const t = F2.deleted;
      this.logger.info(`Emitting ${t}`), this.logger.debug({ type: "event", event: t, record: e });
    }), this.core.heartbeat.on(r.pulse, () => {
      this.cleanup();
    });
  }
  cleanup() {
    try {
      this.isInitialized();
      let e = false;
      this.records.forEach((t) => {
        (0, import_time2.toMiliseconds)(t.expiry || 0) - Date.now() <= 0 && (this.logger.info(`Deleting expired history log: ${t.id}`), this.records.delete(t.id), this.events.emit(F2.deleted, t, false), e = true);
      }), e && this.persist();
    } catch (e) {
      this.logger.warn(e);
    }
  }
  isInitialized() {
    if (!this.initialized) {
      const { message: e } = ht("NOT_INITIALIZED", this.name);
      throw new Error(e);
    }
  }
};
var Uo2 = Object.defineProperty;
var Fo = (r2, e, t) => e in r2 ? Uo2(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var A2 = (r2, e, t) => Fo(r2, typeof e != "symbol" ? e + "" : e, t);
var ji2 = class extends S {
  constructor(e, t) {
    super(e, t), this.core = e, this.logger = t, A2(this, "expirations", /* @__PURE__ */ new Map()), A2(this, "events", new import_events3.EventEmitter()), A2(this, "name", qt2), A2(this, "version", Gt), A2(this, "cached", []), A2(this, "initialized", false), A2(this, "storagePrefix", B), A2(this, "init", async () => {
      this.initialized || (this.logger.trace("Initialized"), await this.restore(), this.cached.forEach((i3) => this.expirations.set(i3.target, i3)), this.cached = [], this.registerEventListeners(), this.initialized = true);
    }), A2(this, "has", (i3) => {
      try {
        const s = this.formatTarget(i3);
        return typeof this.getExpiration(s) < "u";
      } catch {
        return false;
      }
    }), A2(this, "set", (i3, s) => {
      this.isInitialized();
      const n2 = this.formatTarget(i3), o2 = { target: n2, expiry: s };
      this.expirations.set(n2, o2), this.checkExpiry(n2, o2), this.events.emit(M2.created, { target: n2, expiration: o2 });
    }), A2(this, "get", (i3) => {
      this.isInitialized();
      const s = this.formatTarget(i3);
      return this.getExpiration(s);
    }), A2(this, "del", (i3) => {
      if (this.isInitialized(), this.has(i3)) {
        const s = this.formatTarget(i3), n2 = this.getExpiration(s);
        this.expirations.delete(s), this.events.emit(M2.deleted, { target: s, expiration: n2 });
      }
    }), A2(this, "on", (i3, s) => {
      this.events.on(i3, s);
    }), A2(this, "once", (i3, s) => {
      this.events.once(i3, s);
    }), A2(this, "off", (i3, s) => {
      this.events.off(i3, s);
    }), A2(this, "removeListener", (i3, s) => {
      this.events.removeListener(i3, s);
    }), this.logger = E(t, this.name);
  }
  get context() {
    return y(this.logger);
  }
  get storageKey() {
    return this.storagePrefix + this.version + this.core.customStoragePrefix + "//" + this.name;
  }
  get length() {
    return this.expirations.size;
  }
  get keys() {
    return Array.from(this.expirations.keys());
  }
  get values() {
    return Array.from(this.expirations.values());
  }
  formatTarget(e) {
    if (typeof e == "string") return mi(e);
    if (typeof e == "number") return wi(e);
    const { message: t } = ht("UNKNOWN_TYPE", `Target type: ${typeof e}`);
    throw new Error(t);
  }
  async setExpirations(e) {
    await this.core.storage.setItem(this.storageKey, e);
  }
  async getExpirations() {
    return await this.core.storage.getItem(this.storageKey);
  }
  async persist() {
    await this.setExpirations(this.values), this.events.emit(M2.sync);
  }
  async restore() {
    try {
      const e = await this.getExpirations();
      if (typeof e > "u" || !e.length) return;
      if (this.expirations.size) {
        const { message: t } = ht("RESTORE_WILL_OVERRIDE", this.name);
        throw this.logger.error(t), new Error(t);
      }
      this.cached = e, this.logger.debug(`Successfully Restored expirations for ${this.name}`), this.logger.trace({ type: "method", method: "restore", expirations: this.values });
    } catch (e) {
      this.logger.debug(`Failed to Restore expirations for ${this.name}`), this.logger.error(e);
    }
  }
  getExpiration(e) {
    const t = this.expirations.get(e);
    if (!t) {
      const { message: i3 } = ht("NO_MATCHING_KEY", `${this.name}: ${e}`);
      throw this.logger.warn(i3), new Error(i3);
    }
    return t;
  }
  checkExpiry(e, t) {
    const { expiry: i3 } = t;
    (0, import_time2.toMiliseconds)(i3) - Date.now() <= 0 && this.expire(e, t);
  }
  expire(e, t) {
    this.expirations.delete(e), this.events.emit(M2.expired, { target: e, expiration: t });
  }
  checkExpirations() {
    this.core.relayer.connected && this.expirations.forEach((e, t) => this.checkExpiry(t, e));
  }
  registerEventListeners() {
    this.core.heartbeat.on(r.pulse, () => this.checkExpirations()), this.events.on(M2.created, (e) => {
      const t = M2.created;
      this.logger.info(`Emitting ${t}`), this.logger.debug({ type: "event", event: t, data: e }), this.persist();
    }), this.events.on(M2.expired, (e) => {
      const t = M2.expired;
      this.logger.info(`Emitting ${t}`), this.logger.debug({ type: "event", event: t, data: e }), this.persist();
    }), this.events.on(M2.deleted, (e) => {
      const t = M2.deleted;
      this.logger.info(`Emitting ${t}`), this.logger.debug({ type: "event", event: t, data: e }), this.persist();
    });
  }
  isInitialized() {
    if (!this.initialized) {
      const { message: e } = ht("NOT_INITIALIZED", this.name);
      throw new Error(e);
    }
  }
};
var Mo = Object.defineProperty;
var Ko = (r2, e, t) => e in r2 ? Mo(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var w = (r2, e, t) => Ko(r2, typeof e != "symbol" ? e + "" : e, t);
var Ui2 = class extends M {
  constructor(e, t, i3) {
    super(e, t, i3), this.core = e, this.logger = t, this.store = i3, w(this, "name", Wt2), w(this, "abortController"), w(this, "isDevEnv"), w(this, "verifyUrlV3", Yt2), w(this, "storagePrefix", B), w(this, "version", Le), w(this, "publicKey"), w(this, "fetchPromise"), w(this, "init", async () => {
      var s;
      this.isDevEnv || (this.publicKey = await this.store.getItem(this.storeKey), this.publicKey && (0, import_time2.toMiliseconds)((s = this.publicKey) == null ? void 0 : s.expiresAt) < Date.now() && (this.logger.debug("verify v2 public key expired"), await this.removePublicKey()));
    }), w(this, "register", async (s) => {
      if (!Tt() || this.isDevEnv) return;
      const n2 = window.location.origin, { id: o2, decryptedId: a2 } = s, c3 = `${this.verifyUrlV3}/attestation?projectId=${this.core.projectId}&origin=${n2}&id=${o2}&decryptedId=${a2}`;
      try {
        const h4 = (0, import_window_getters2.getDocument)(), l3 = this.startAbortTimer(import_time2.ONE_SECOND * 5), d3 = await new Promise((g, _) => {
          const u3 = () => {
            window.removeEventListener("message", x2), h4.body.removeChild(b4), _("attestation aborted");
          };
          this.abortController.signal.addEventListener("abort", u3);
          const b4 = h4.createElement("iframe");
          b4.src = c3, b4.style.display = "none", b4.addEventListener("error", u3, { signal: this.abortController.signal });
          const x2 = (I3) => {
            if (I3.data && typeof I3.data == "string") try {
              const D2 = JSON.parse(I3.data);
              if (D2.type === "verify_attestation") {
                if (sn(D2.attestation).payload.id !== o2) return;
                clearInterval(l3), h4.body.removeChild(b4), this.abortController.signal.removeEventListener("abort", u3), window.removeEventListener("message", x2), g(D2.attestation === null ? "" : D2.attestation);
              }
            } catch (D2) {
              this.logger.warn(D2);
            }
          };
          h4.body.appendChild(b4), window.addEventListener("message", x2, { signal: this.abortController.signal });
        });
        return this.logger.debug("jwt attestation", d3), d3;
      } catch (h4) {
        this.logger.warn(h4);
      }
      return "";
    }), w(this, "resolve", async (s) => {
      if (this.isDevEnv) return "";
      const { attestationId: n2, hash: o2, encryptedId: a2 } = s;
      if (n2 === "") {
        this.logger.debug("resolve: attestationId is empty, skipping");
        return;
      }
      if (n2) {
        if (sn(n2).payload.id !== a2) return;
        const h4 = await this.isValidJwtAttestation(n2);
        if (h4) {
          if (!h4.isVerified) {
            this.logger.warn("resolve: jwt attestation: origin url not verified");
            return;
          }
          return h4;
        }
      }
      if (!o2) return;
      const c3 = this.getVerifyUrl(s == null ? void 0 : s.verifyUrl);
      return this.fetchAttestation(o2, c3);
    }), w(this, "fetchAttestation", async (s, n2) => {
      this.logger.debug(`resolving attestation: ${s} from url: ${n2}`);
      const o2 = this.startAbortTimer(import_time2.ONE_SECOND * 5), a2 = await fetch(`${n2}/attestation/${s}?v2Supported=true`, { signal: this.abortController.signal });
      return clearTimeout(o2), a2.status === 200 ? await a2.json() : void 0;
    }), w(this, "getVerifyUrl", (s) => {
      let n2 = s || ue2;
      return Jt2.includes(n2) || (this.logger.info(`verify url: ${n2}, not included in trusted list, assigning default: ${ue2}`), n2 = ue2), n2;
    }), w(this, "fetchPublicKey", async () => {
      try {
        this.logger.debug(`fetching public key from: ${this.verifyUrlV3}`);
        const s = this.startAbortTimer(import_time2.FIVE_SECONDS), n2 = await fetch(`${this.verifyUrlV3}/public-key`, { signal: this.abortController.signal });
        return clearTimeout(s), await n2.json();
      } catch (s) {
        this.logger.warn(s);
      }
    }), w(this, "persistPublicKey", async (s) => {
      this.logger.debug("persisting public key to local storage", s), await this.store.setItem(this.storeKey, s), this.publicKey = s;
    }), w(this, "removePublicKey", async () => {
      this.logger.debug("removing verify v2 public key from storage"), await this.store.removeItem(this.storeKey), this.publicKey = void 0;
    }), w(this, "isValidJwtAttestation", async (s) => {
      const n2 = await this.getPublicKey();
      try {
        if (n2) return this.validateAttestation(s, n2);
      } catch (a2) {
        this.logger.error(a2), this.logger.warn("error validating attestation");
      }
      const o2 = await this.fetchAndPersistPublicKey();
      try {
        if (o2) return this.validateAttestation(s, o2);
      } catch (a2) {
        this.logger.error(a2), this.logger.warn("error validating attestation");
      }
    }), w(this, "getPublicKey", async () => this.publicKey ? this.publicKey : await this.fetchAndPersistPublicKey()), w(this, "fetchAndPersistPublicKey", async () => {
      if (this.fetchPromise) return await this.fetchPromise, this.publicKey;
      this.fetchPromise = new Promise(async (n2) => {
        const o2 = await this.fetchPublicKey();
        o2 && (await this.persistPublicKey(o2), n2(o2));
      });
      const s = await this.fetchPromise;
      return this.fetchPromise = void 0, s;
    }), w(this, "validateAttestation", (s, n2) => {
      const o2 = zc(s, n2.publicKey), a2 = { hasExpired: (0, import_time2.toMiliseconds)(o2.exp) < Date.now(), payload: o2 };
      if (a2.hasExpired) throw this.logger.warn("resolve: jwt attestation expired"), new Error("JWT attestation expired");
      return { origin: a2.payload.origin, isScam: a2.payload.isScam, isVerified: a2.payload.isVerified };
    }), this.logger = E(t, this.name), this.abortController = new AbortController(), this.isDevEnv = Ii(), this.init();
  }
  get storeKey() {
    return this.storagePrefix + this.version + this.core.customStoragePrefix + "//verify:public:key";
  }
  get context() {
    return y(this.logger);
  }
  startAbortTimer(e) {
    return this.abortController = new AbortController(), setTimeout(() => this.abortController.abort(), (0, import_time2.toMiliseconds)(e));
  }
};
var Bo2 = Object.defineProperty;
var Vo = (r2, e, t) => e in r2 ? Bo2(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var Fi2 = (r2, e, t) => Vo(r2, typeof e != "symbol" ? e + "" : e, t);
var Mi2 = class extends O {
  constructor(e, t) {
    super(e, t), this.projectId = e, this.logger = t, Fi2(this, "context", Xt2), Fi2(this, "registerDeviceToken", async (i3) => {
      const { clientId: s, token: n2, notificationType: o2, enableEncrypted: a2 = false } = i3, c3 = `${Zt}/${this.projectId}/clients`;
      await fetch(c3, { method: "POST", headers: { "Content-Type": "application/json" }, body: JSON.stringify({ client_id: s, type: o2, token: n2, always_raw: a2 }) });
    }), this.logger = E(t, this.context);
  }
};
var qo = Object.defineProperty;
var Ki2 = Object.getOwnPropertySymbols;
var Go2 = Object.prototype.hasOwnProperty;
var Wo2 = Object.prototype.propertyIsEnumerable;
var Ze2 = (r2, e, t) => e in r2 ? qo(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var be2 = (r2, e) => {
  for (var t in e || (e = {})) Go2.call(e, t) && Ze2(r2, t, e[t]);
  if (Ki2) for (var t of Ki2(e)) Wo2.call(e, t) && Ze2(r2, t, e[t]);
  return r2;
};
var E2 = (r2, e, t) => Ze2(r2, typeof e != "symbol" ? e + "" : e, t);
var Bi2 = class extends R {
  constructor(e, t, i3 = true) {
    super(e, t, i3), this.core = e, this.logger = t, E2(this, "context", ei2), E2(this, "storagePrefix", B), E2(this, "storageVersion", Qt2), E2(this, "events", /* @__PURE__ */ new Map()), E2(this, "shouldPersist", false), E2(this, "init", async () => {
      if (!Ii()) try {
        const s = { eventId: Bi(), timestamp: Date.now(), domain: this.getAppDomain(), props: { event: "INIT", type: "", properties: { client_id: await this.core.crypto.getClientId(), user_agent: Mn(this.core.relayer.protocol, this.core.relayer.version, _e2) } } };
        await this.sendEvent([s]);
      } catch (s) {
        this.logger.warn(s);
      }
    }), E2(this, "createEvent", (s) => {
      const { event: n2 = "ERROR", type: o2 = "", properties: { topic: a2, trace: c3 } } = s, h4 = Bi(), l3 = this.core.projectId || "", d3 = Date.now(), g = be2({ eventId: h4, timestamp: d3, props: { event: n2, type: o2, properties: { topic: a2, trace: c3 } }, bundleId: l3, domain: this.getAppDomain() }, this.setMethods(h4));
      return this.telemetryEnabled && (this.events.set(h4, g), this.shouldPersist = true), g;
    }), E2(this, "getEvent", (s) => {
      const { eventId: n2, topic: o2 } = s;
      if (n2) return this.events.get(n2);
      const a2 = Array.from(this.events.values()).find((c3) => c3.props.properties.topic === o2);
      if (a2) return be2(be2({}, a2), this.setMethods(a2.eventId));
    }), E2(this, "deleteEvent", (s) => {
      const { eventId: n2 } = s;
      this.events.delete(n2), this.shouldPersist = true;
    }), E2(this, "setEventListeners", () => {
      this.core.heartbeat.on(r.pulse, async () => {
        this.shouldPersist && await this.persist(), this.events.forEach((s) => {
          (0, import_time2.fromMiliseconds)(Date.now()) - (0, import_time2.fromMiliseconds)(s.timestamp) > ti && (this.events.delete(s.eventId), this.shouldPersist = true);
        });
      });
    }), E2(this, "setMethods", (s) => ({ addTrace: (n2) => this.addTrace(s, n2), setError: (n2) => this.setError(s, n2) })), E2(this, "addTrace", (s, n2) => {
      const o2 = this.events.get(s);
      o2 && (o2.props.properties.trace.push(n2), this.events.set(s, o2), this.shouldPersist = true);
    }), E2(this, "setError", (s, n2) => {
      const o2 = this.events.get(s);
      o2 && (o2.props.type = n2, o2.timestamp = Date.now(), this.events.set(s, o2), this.shouldPersist = true);
    }), E2(this, "persist", async () => {
      await this.core.storage.setItem(this.storageKey, Array.from(this.events.values())), this.shouldPersist = false;
    }), E2(this, "restore", async () => {
      try {
        const s = await this.core.storage.getItem(this.storageKey) || [];
        if (!s.length) return;
        s.forEach((n2) => {
          this.events.set(n2.eventId, be2(be2({}, n2), this.setMethods(n2.eventId)));
        });
      } catch (s) {
        this.logger.warn(s);
      }
    }), E2(this, "submit", async () => {
      if (!this.telemetryEnabled || this.events.size === 0) return;
      const s = [];
      for (const [n2, o2] of this.events) o2.props.type && s.push(o2);
      if (s.length !== 0) try {
        if ((await this.sendEvent(s)).ok) for (const n2 of s) this.events.delete(n2.eventId), this.shouldPersist = true;
      } catch (n2) {
        this.logger.warn(n2);
      }
    }), E2(this, "sendEvent", async (s) => {
      const n2 = this.getAppDomain() ? "" : "&sp=desktop";
      return await fetch(`${ii}?projectId=${this.core.projectId}&st=events_sdk&sv=js-${_e2}${n2}`, { method: "POST", body: JSON.stringify(s) });
    }), E2(this, "getAppDomain", () => Pn().url), this.logger = E(t, this.context), this.telemetryEnabled = i3, i3 ? this.restore().then(async () => {
      await this.submit(), this.setEventListeners();
    }) : this.persist();
  }
  get storageKey() {
    return this.storagePrefix + this.storageVersion + this.core.customStoragePrefix + "//" + this.context;
  }
};
var Ho = Object.defineProperty;
var Vi2 = Object.getOwnPropertySymbols;
var Yo2 = Object.prototype.hasOwnProperty;
var Jo2 = Object.prototype.propertyIsEnumerable;
var Qe3 = (r2, e, t) => e in r2 ? Ho(r2, e, { enumerable: true, configurable: true, writable: true, value: t }) : r2[e] = t;
var qi2 = (r2, e) => {
  for (var t in e || (e = {})) Yo2.call(e, t) && Qe3(r2, t, e[t]);
  if (Vi2) for (var t of Vi2(e)) Jo2.call(e, t) && Qe3(r2, t, e[t]);
  return r2;
};
var v2 = (r2, e, t) => Qe3(r2, typeof e != "symbol" ? e + "" : e, t);
var Te = class _Te extends h2 {
  constructor(e) {
    var t;
    super(e), v2(this, "protocol", ze2), v2(this, "version", Le), v2(this, "name", he2), v2(this, "relayUrl"), v2(this, "projectId"), v2(this, "customStoragePrefix"), v2(this, "events", new import_events3.EventEmitter()), v2(this, "logger"), v2(this, "heartbeat"), v2(this, "relayer"), v2(this, "crypto"), v2(this, "storage"), v2(this, "history"), v2(this, "expirer"), v2(this, "pairing"), v2(this, "verify"), v2(this, "echoClient"), v2(this, "linkModeSupportedApps"), v2(this, "eventClient"), v2(this, "initialized", false), v2(this, "logChunkController"), v2(this, "on", (a2, c3) => this.events.on(a2, c3)), v2(this, "once", (a2, c3) => this.events.once(a2, c3)), v2(this, "off", (a2, c3) => this.events.off(a2, c3)), v2(this, "removeListener", (a2, c3) => this.events.removeListener(a2, c3)), v2(this, "dispatchEnvelope", ({ topic: a2, message: c3, sessionExists: h4 }) => {
      if (!a2 || !c3) return;
      const l3 = { topic: a2, message: c3, publishedAt: Date.now(), transportType: Q.link_mode };
      this.relayer.onLinkMessageEvent(l3, { sessionExists: h4 });
    });
    const i3 = this.getGlobalCore(e == null ? void 0 : e.customStoragePrefix);
    if (i3) try {
      return this.customStoragePrefix = i3.customStoragePrefix, this.logger = i3.logger, this.heartbeat = i3.heartbeat, this.crypto = i3.crypto, this.history = i3.history, this.expirer = i3.expirer, this.storage = i3.storage, this.relayer = i3.relayer, this.pairing = i3.pairing, this.verify = i3.verify, this.echoClient = i3.echoClient, this.linkModeSupportedApps = i3.linkModeSupportedApps, this.eventClient = i3.eventClient, this.initialized = i3.initialized, this.logChunkController = i3.logChunkController, i3;
    } catch (a2) {
      console.warn("Failed to copy global core", a2);
    }
    this.projectId = e == null ? void 0 : e.projectId, this.relayUrl = (e == null ? void 0 : e.relayUrl) || Ue, this.customStoragePrefix = e != null && e.customStoragePrefix ? `:${e.customStoragePrefix}` : "";
    const s = k({ level: typeof (e == null ? void 0 : e.logger) == "string" && e.logger ? e.logger : Et2.logger, name: he2 }), { logger: n2, chunkLoggerController: o2 } = A({ opts: s, maxSizeInBytes: e == null ? void 0 : e.maxLogBlobSizeInBytes, loggerOverride: e == null ? void 0 : e.logger });
    this.logChunkController = o2, (t = this.logChunkController) != null && t.downloadLogsBlobInBrowser && (window.downloadLogsBlobInBrowser = async () => {
      var a2, c3;
      (a2 = this.logChunkController) != null && a2.downloadLogsBlobInBrowser && ((c3 = this.logChunkController) == null || c3.downloadLogsBlobInBrowser({ clientId: await this.crypto.getClientId() }));
    }), this.logger = E(n2, this.name), this.heartbeat = new i(), this.crypto = new vi2(this, this.logger, e == null ? void 0 : e.keychain), this.history = new ki2(this, this.logger), this.expirer = new ji2(this, this.logger), this.storage = e != null && e.storage ? e.storage : new h(qi2(qi2({}, It2), e == null ? void 0 : e.storageOptions)), this.relayer = new Si2({ core: this, logger: this.logger, relayUrl: this.relayUrl, projectId: this.projectId }), this.pairing = new Li2(this, this.logger), this.verify = new Ui2(this, this.logger, this.storage), this.echoClient = new Mi2(this.projectId || "", this.logger), this.linkModeSupportedApps = [], this.eventClient = new Bi2(this, this.logger, e == null ? void 0 : e.telemetryEnabled), this.setGlobalCore(this);
  }
  static async init(e) {
    const t = new _Te(e);
    await t.initialize();
    const i3 = await t.crypto.getClientId();
    return await t.storage.setItem(jt2, i3), t;
  }
  get context() {
    return y(this.logger);
  }
  async start() {
    this.initialized || await this.initialize();
  }
  async getLogsBlob() {
    var e;
    return (e = this.logChunkController) == null ? void 0 : e.logsToBlob({ clientId: await this.crypto.getClientId() });
  }
  async addLinkModeSupportedApp(e) {
    this.linkModeSupportedApps.includes(e) || (this.linkModeSupportedApps.push(e), await this.storage.setItem(Fe2, this.linkModeSupportedApps));
  }
  async initialize() {
    this.logger.trace("Initialized");
    try {
      await this.crypto.init(), await this.history.init(), await this.expirer.init(), await this.relayer.init(), await this.heartbeat.init(), await this.pairing.init(), this.linkModeSupportedApps = await this.storage.getItem(Fe2) || [], this.initialized = true, this.logger.info("Core Initialization Success");
    } catch (e) {
      throw this.logger.warn(`Core Initialization Failure at epoch ${Date.now()}`, e), this.logger.error(e.message), e;
    }
  }
  getGlobalCore(e = "") {
    try {
      if (this.isGlobalCoreDisabled()) return;
      const t = `_walletConnectCore_${e}`, i3 = `${t}_count`;
      return globalThis[i3] = (globalThis[i3] || 0) + 1, globalThis[i3] > 1 && console.warn(`WalletConnect Core is already initialized. This is probably a mistake and can lead to unexpected behavior. Init() was called ${globalThis[i3]} times.`), globalThis[t];
    } catch (t) {
      console.warn("Failed to get global WalletConnect core", t);
      return;
    }
  }
  setGlobalCore(e) {
    var t;
    try {
      if (this.isGlobalCoreDisabled()) return;
      const i3 = `_walletConnectCore_${((t = e.opts) == null ? void 0 : t.customStoragePrefix) || ""}`;
      globalThis[i3] = e;
    } catch (i3) {
      console.warn("Failed to set global WalletConnect core", i3);
    }
  }
  isGlobalCoreDisabled() {
    try {
      return typeof process < "u" && process.env.DISABLE_GLOBAL_CORE === "true";
    } catch {
      return true;
    }
  }
};
var Xo2 = Te;

// node_modules/@reown/appkit/node_modules/@walletconnect/sign-client/dist/index.es.js
var import_time3 = __toESM(require_cjs());
var import_events4 = __toESM(require_events());
var De2 = "wc";
var Le2 = 2;
var ke3 = "client";
var we2 = `${De2}@${Le2}:${ke3}:`;
var me2 = { name: ke3, logger: "error", controller: false, relayUrl: "wss://relay.walletconnect.org" };
var Me3 = "WALLETCONNECT_DEEPLINK_CHOICE";
var pt2 = "proposal";
var $e2 = "Proposal expired";
var ht2 = "session";
var J2 = import_time3.SEVEN_DAYS;
var dt2 = "engine";
var N = { wc_sessionPropose: { req: { ttl: import_time3.FIVE_MINUTES, prompt: true, tag: 1100 }, res: { ttl: import_time3.FIVE_MINUTES, prompt: false, tag: 1101 }, reject: { ttl: import_time3.FIVE_MINUTES, prompt: false, tag: 1120 }, autoReject: { ttl: import_time3.FIVE_MINUTES, prompt: false, tag: 1121 } }, wc_sessionSettle: { req: { ttl: import_time3.FIVE_MINUTES, prompt: false, tag: 1102 }, res: { ttl: import_time3.FIVE_MINUTES, prompt: false, tag: 1103 } }, wc_sessionUpdate: { req: { ttl: import_time3.ONE_DAY, prompt: false, tag: 1104 }, res: { ttl: import_time3.ONE_DAY, prompt: false, tag: 1105 } }, wc_sessionExtend: { req: { ttl: import_time3.ONE_DAY, prompt: false, tag: 1106 }, res: { ttl: import_time3.ONE_DAY, prompt: false, tag: 1107 } }, wc_sessionRequest: { req: { ttl: import_time3.FIVE_MINUTES, prompt: true, tag: 1108 }, res: { ttl: import_time3.FIVE_MINUTES, prompt: false, tag: 1109 } }, wc_sessionEvent: { req: { ttl: import_time3.FIVE_MINUTES, prompt: true, tag: 1110 }, res: { ttl: import_time3.FIVE_MINUTES, prompt: false, tag: 1111 } }, wc_sessionDelete: { req: { ttl: import_time3.ONE_DAY, prompt: false, tag: 1112 }, res: { ttl: import_time3.ONE_DAY, prompt: false, tag: 1113 } }, wc_sessionPing: { req: { ttl: import_time3.ONE_DAY, prompt: false, tag: 1114 }, res: { ttl: import_time3.ONE_DAY, prompt: false, tag: 1115 } }, wc_sessionAuthenticate: { req: { ttl: import_time3.ONE_HOUR, prompt: true, tag: 1116 }, res: { ttl: import_time3.ONE_HOUR, prompt: false, tag: 1117 }, reject: { ttl: import_time3.FIVE_MINUTES, prompt: false, tag: 1118 }, autoReject: { ttl: import_time3.FIVE_MINUTES, prompt: false, tag: 1119 } } };
var _e3 = { min: import_time3.FIVE_MINUTES, max: import_time3.SEVEN_DAYS };
var $2 = { idle: "IDLE", active: "ACTIVE" };
var Ke3 = { eth_sendTransaction: { key: "" }, eth_sendRawTransaction: { key: "" }, wallet_sendCalls: { key: "" }, solana_signTransaction: { key: "signature" }, solana_signAllTransactions: { key: "transactions" }, solana_signAndSendTransaction: { key: "signature" } };
var ut2 = "request";
var gt2 = ["wc_sessionPropose", "wc_sessionRequest", "wc_authRequest", "wc_sessionAuthenticate"];
var yt2 = "wc";
var wt2 = "auth";
var mt2 = "authKeys";
var _t2 = "pairingTopics";
var Et3 = "requests";
var ae2 = `${yt2}@${1.5}:${wt2}:`;
var ce2 = `${ae2}:PUB_KEY`;
var vs2 = Object.defineProperty;
var Is2 = Object.defineProperties;
var Ts2 = Object.getOwnPropertyDescriptors;
var ft2 = Object.getOwnPropertySymbols;
var qs3 = Object.prototype.hasOwnProperty;
var Ps2 = Object.prototype.propertyIsEnumerable;
var Ue2 = (S4, n2, e) => n2 in S4 ? vs2(S4, n2, { enumerable: true, configurable: true, writable: true, value: e }) : S4[n2] = e;
var v3 = (S4, n2) => {
  for (var e in n2 || (n2 = {})) qs3.call(n2, e) && Ue2(S4, e, n2[e]);
  if (ft2) for (var e of ft2(n2)) Ps2.call(n2, e) && Ue2(S4, e, n2[e]);
  return S4;
};
var b2 = (S4, n2) => Is2(S4, Ts2(n2));
var c2 = (S4, n2, e) => Ue2(S4, typeof n2 != "symbol" ? n2 + "" : n2, e);
var Ns2 = class extends V {
  constructor(n2) {
    super(n2), c2(this, "name", dt2), c2(this, "events", new import_events4.default()), c2(this, "initialized", false), c2(this, "requestQueue", { state: $2.idle, queue: [] }), c2(this, "sessionRequestQueue", { state: $2.idle, queue: [] }), c2(this, "requestQueueDelay", import_time3.ONE_SECOND), c2(this, "expectedPairingMethodMap", /* @__PURE__ */ new Map()), c2(this, "recentlyDeletedMap", /* @__PURE__ */ new Map()), c2(this, "recentlyDeletedLimit", 200), c2(this, "relayMessageCache", []), c2(this, "pendingSessions", /* @__PURE__ */ new Map()), c2(this, "init", async () => {
      this.initialized || (await this.cleanup(), this.registerRelayerEvents(), this.registerExpirerEvents(), this.registerPairingEvents(), await this.registerLinkModeListeners(), this.client.core.pairing.register({ methods: Object.keys(N) }), this.initialized = true, setTimeout(async () => {
        await this.processPendingMessageEvents(), this.sessionRequestQueue.queue = this.getPendingSessionRequests(), this.processSessionRequestQueue();
      }, (0, import_time3.toMiliseconds)(this.requestQueueDelay)));
    }), c2(this, "connect", async (e) => {
      this.isInitialized(), await this.confirmOnlineStateOrThrow();
      const t = b2(v3({}, e), { requiredNamespaces: e.requiredNamespaces || {}, optionalNamespaces: e.optionalNamespaces || {} });
      await this.isValidConnect(t), t.optionalNamespaces = aa(t.requiredNamespaces, t.optionalNamespaces), t.requiredNamespaces = {};
      const { pairingTopic: s, requiredNamespaces: i3, optionalNamespaces: r2, sessionProperties: o2, scopedProperties: a2, relays: l3 } = t;
      let p3 = s, h4, u3 = false;
      try {
        if (p3) {
          const T2 = this.client.core.pairing.pairings.get(p3);
          this.client.logger.warn("connect() with existing pairing topic is deprecated and will be removed in the next major release."), u3 = T2.active;
        }
      } catch (T2) {
        throw this.client.logger.error(`connect() -> pairing.get(${p3}) failed`), T2;
      }
      if (!p3 || !u3) {
        const { topic: T2, uri: K3 } = await this.client.core.pairing.create();
        p3 = T2, h4 = K3;
      }
      if (!p3) {
        const { message: T2 } = ht("NO_MATCHING_KEY", `connect() pairing topic: ${p3}`);
        throw new Error(T2);
      }
      const d3 = await this.client.core.crypto.generateKeyPair(), w2 = N.wc_sessionPropose.req.ttl || import_time3.FIVE_MINUTES, m2 = Ei(w2), f6 = b2(v3(v3({ requiredNamespaces: i3, optionalNamespaces: r2, relays: l3 ?? [{ protocol: xt2 }], proposer: { publicKey: d3, metadata: this.client.metadata }, expiryTimestamp: m2, pairingTopic: p3 }, o2 && { sessionProperties: o2 }), a2 && { scopedProperties: a2 }), { id: payloadId() }), _ = xi("session_connect", f6.id), { reject: g, resolve: A4, done: D2 } = gi(w2, $e2), I3 = ({ id: T2 }) => {
        T2 === f6.id && (this.client.events.off("proposal_expire", I3), this.pendingSessions.delete(f6.id), this.events.emit(_, { error: { message: $e2, code: 0 } }));
      };
      return this.client.events.on("proposal_expire", I3), this.events.once(_, ({ error: T2, session: K3 }) => {
        this.client.events.off("proposal_expire", I3), T2 ? g(T2) : K3 && A4(K3);
      }), await this.sendRequest({ topic: p3, method: "wc_sessionPropose", params: f6, throwOnFailedPublish: true, clientRpcId: f6.id }), await this.setProposal(f6.id, f6), { uri: h4, approval: D2 };
    }), c2(this, "pair", async (e) => {
      this.isInitialized(), await this.confirmOnlineStateOrThrow();
      try {
        return await this.client.core.pairing.pair(e);
      } catch (t) {
        throw this.client.logger.error("pair() failed"), t;
      }
    }), c2(this, "approve", async (e) => {
      var t, s, i3;
      const r2 = this.client.core.eventClient.createEvent({ properties: { topic: (t = e == null ? void 0 : e.id) == null ? void 0 : t.toString(), trace: [er2.session_approve_started] } });
      try {
        this.isInitialized(), await this.confirmOnlineStateOrThrow();
      } catch (q3) {
        throw r2.setError(tr2.no_internet_connection), q3;
      }
      try {
        await this.isValidProposalId(e == null ? void 0 : e.id);
      } catch (q3) {
        throw this.client.logger.error(`approve() -> proposal.get(${e == null ? void 0 : e.id}) failed`), r2.setError(tr2.proposal_not_found), q3;
      }
      try {
        await this.isValidApprove(e);
      } catch (q3) {
        throw this.client.logger.error("approve() -> isValidApprove() failed"), r2.setError(tr2.session_approve_namespace_validation_failure), q3;
      }
      const { id: o2, relayProtocol: a2, namespaces: l3, sessionProperties: p3, scopedProperties: h4, sessionConfig: u3 } = e, d3 = this.client.proposal.get(o2);
      this.client.core.eventClient.deleteEvent({ eventId: r2.eventId });
      const { pairingTopic: w2, proposer: m2, requiredNamespaces: f6, optionalNamespaces: _ } = d3;
      let g = (s = this.client.core.eventClient) == null ? void 0 : s.getEvent({ topic: w2 });
      g || (g = (i3 = this.client.core.eventClient) == null ? void 0 : i3.createEvent({ type: er2.session_approve_started, properties: { topic: w2, trace: [er2.session_approve_started, er2.session_namespaces_validation_success] } }));
      const A4 = await this.client.core.crypto.generateKeyPair(), D2 = m2.publicKey, I3 = await this.client.core.crypto.generateSharedKey(A4, D2), T2 = v3(v3(v3({ relay: { protocol: a2 ?? "irn" }, namespaces: l3, controller: { publicKey: A4, metadata: this.client.metadata }, expiry: Ei(J2) }, p3 && { sessionProperties: p3 }), h4 && { scopedProperties: h4 }), u3 && { sessionConfig: u3 }), K3 = Q.relay;
      g.addTrace(er2.subscribing_session_topic);
      try {
        await this.client.core.relayer.subscribe(I3, { transportType: K3 });
      } catch (q3) {
        throw g.setError(tr2.subscribe_session_topic_failure), q3;
      }
      g.addTrace(er2.subscribe_session_topic_success);
      const fe3 = b2(v3({}, T2), { topic: I3, requiredNamespaces: f6, optionalNamespaces: _, pairingTopic: w2, acknowledged: false, self: T2.controller, peer: { publicKey: m2.publicKey, metadata: m2.metadata }, controller: A4, transportType: Q.relay });
      await this.client.session.set(I3, fe3), g.addTrace(er2.store_session);
      try {
        g.addTrace(er2.publishing_session_settle), await this.sendRequest({ topic: I3, method: "wc_sessionSettle", params: T2, throwOnFailedPublish: true }).catch((q3) => {
          throw g == null ? void 0 : g.setError(tr2.session_settle_publish_failure), q3;
        }), g.addTrace(er2.session_settle_publish_success), g.addTrace(er2.publishing_session_approve), await this.sendResult({ id: o2, topic: w2, result: { relay: { protocol: a2 ?? "irn" }, responderPublicKey: A4 }, throwOnFailedPublish: true }).catch((q3) => {
          throw g == null ? void 0 : g.setError(tr2.session_approve_publish_failure), q3;
        }), g.addTrace(er2.session_approve_publish_success);
      } catch (q3) {
        throw this.client.logger.error(q3), this.client.session.delete(I3, Nt("USER_DISCONNECTED")), await this.client.core.relayer.unsubscribe(I3), q3;
      }
      return this.client.core.eventClient.deleteEvent({ eventId: g.eventId }), await this.client.core.pairing.updateMetadata({ topic: w2, metadata: m2.metadata }), await this.client.proposal.delete(o2, Nt("USER_DISCONNECTED")), await this.client.core.pairing.activate({ topic: w2 }), await this.setExpiry(I3, Ei(J2)), { topic: I3, acknowledged: () => Promise.resolve(this.client.session.get(I3)) };
    }), c2(this, "reject", async (e) => {
      this.isInitialized(), await this.confirmOnlineStateOrThrow();
      try {
        await this.isValidReject(e);
      } catch (r2) {
        throw this.client.logger.error("reject() -> isValidReject() failed"), r2;
      }
      const { id: t, reason: s } = e;
      let i3;
      try {
        i3 = this.client.proposal.get(t).pairingTopic;
      } catch (r2) {
        throw this.client.logger.error(`reject() -> proposal.get(${t}) failed`), r2;
      }
      i3 && (await this.sendError({ id: t, topic: i3, error: s, rpcOpts: N.wc_sessionPropose.reject }), await this.client.proposal.delete(t, Nt("USER_DISCONNECTED")));
    }), c2(this, "update", async (e) => {
      this.isInitialized(), await this.confirmOnlineStateOrThrow();
      try {
        await this.isValidUpdate(e);
      } catch (h4) {
        throw this.client.logger.error("update() -> isValidUpdate() failed"), h4;
      }
      const { topic: t, namespaces: s } = e, { done: i3, resolve: r2, reject: o2 } = gi(), a2 = payloadId(), l3 = getBigIntRpcId().toString(), p3 = this.client.session.get(t).namespaces;
      return this.events.once(xi("session_update", a2), ({ error: h4 }) => {
        h4 ? o2(h4) : r2();
      }), await this.client.session.update(t, { namespaces: s }), await this.sendRequest({ topic: t, method: "wc_sessionUpdate", params: { namespaces: s }, throwOnFailedPublish: true, clientRpcId: a2, relayRpcId: l3 }).catch((h4) => {
        this.client.logger.error(h4), this.client.session.update(t, { namespaces: p3 }), o2(h4);
      }), { acknowledged: i3 };
    }), c2(this, "extend", async (e) => {
      this.isInitialized(), await this.confirmOnlineStateOrThrow();
      try {
        await this.isValidExtend(e);
      } catch (a2) {
        throw this.client.logger.error("extend() -> isValidExtend() failed"), a2;
      }
      const { topic: t } = e, s = payloadId(), { done: i3, resolve: r2, reject: o2 } = gi();
      return this.events.once(xi("session_extend", s), ({ error: a2 }) => {
        a2 ? o2(a2) : r2();
      }), await this.setExpiry(t, Ei(J2)), this.sendRequest({ topic: t, method: "wc_sessionExtend", params: {}, clientRpcId: s, throwOnFailedPublish: true }).catch((a2) => {
        o2(a2);
      }), { acknowledged: i3 };
    }), c2(this, "request", async (e) => {
      this.isInitialized();
      try {
        await this.isValidRequest(e);
      } catch (_) {
        throw this.client.logger.error("request() -> isValidRequest() failed"), _;
      }
      const { chainId: t, request: s, topic: i3, expiry: r2 = N.wc_sessionRequest.req.ttl } = e, o2 = this.client.session.get(i3);
      (o2 == null ? void 0 : o2.transportType) === Q.relay && await this.confirmOnlineStateOrThrow();
      const a2 = payloadId(), l3 = getBigIntRpcId().toString(), { done: p3, resolve: h4, reject: u3 } = gi(r2, "Request expired. Please try again.");
      this.events.once(xi("session_request", a2), ({ error: _, result: g }) => {
        _ ? u3(_) : h4(g);
      });
      const d3 = "wc_sessionRequest", w2 = this.getAppLinkIfEnabled(o2.peer.metadata, o2.transportType);
      if (w2) return await this.sendRequest({ clientRpcId: a2, relayRpcId: l3, topic: i3, method: d3, params: { request: b2(v3({}, s), { expiryTimestamp: Ei(r2) }), chainId: t }, expiry: r2, throwOnFailedPublish: true, appLink: w2 }).catch((_) => u3(_)), this.client.events.emit("session_request_sent", { topic: i3, request: s, chainId: t, id: a2 }), await p3();
      const m2 = { request: b2(v3({}, s), { expiryTimestamp: Ei(r2) }), chainId: t }, f6 = this.shouldSetTVF(d3, m2);
      return await Promise.all([new Promise(async (_) => {
        await this.sendRequest(v3({ clientRpcId: a2, relayRpcId: l3, topic: i3, method: d3, params: m2, expiry: r2, throwOnFailedPublish: true }, f6 && { tvf: this.getTVFParams(a2, m2) })).catch((g) => u3(g)), this.client.events.emit("session_request_sent", { topic: i3, request: s, chainId: t, id: a2 }), _();
      }), new Promise(async (_) => {
        var g;
        if (!((g = o2.sessionConfig) != null && g.disableDeepLink)) {
          const A4 = await Oi(this.client.core.storage, Me3);
          await Si({ id: a2, topic: i3, wcDeepLink: A4 });
        }
        _();
      }), p3()]).then((_) => _[2]);
    }), c2(this, "respond", async (e) => {
      this.isInitialized(), await this.isValidRespond(e);
      const { topic: t, response: s } = e, { id: i3 } = s, r2 = this.client.session.get(t);
      r2.transportType === Q.relay && await this.confirmOnlineStateOrThrow();
      const o2 = this.getAppLinkIfEnabled(r2.peer.metadata, r2.transportType);
      isJsonRpcResult(s) ? await this.sendResult({ id: i3, topic: t, result: s.result, throwOnFailedPublish: true, appLink: o2 }) : isJsonRpcError(s) && await this.sendError({ id: i3, topic: t, error: s.error, appLink: o2 }), this.cleanupAfterResponse(e);
    }), c2(this, "ping", async (e) => {
      this.isInitialized(), await this.confirmOnlineStateOrThrow();
      try {
        await this.isValidPing(e);
      } catch (s) {
        throw this.client.logger.error("ping() -> isValidPing() failed"), s;
      }
      const { topic: t } = e;
      if (this.client.session.keys.includes(t)) {
        const s = payloadId(), i3 = getBigIntRpcId().toString(), { done: r2, resolve: o2, reject: a2 } = gi();
        this.events.once(xi("session_ping", s), ({ error: l3 }) => {
          l3 ? a2(l3) : o2();
        }), await Promise.all([this.sendRequest({ topic: t, method: "wc_sessionPing", params: {}, throwOnFailedPublish: true, clientRpcId: s, relayRpcId: i3 }), r2()]);
      } else this.client.core.pairing.pairings.keys.includes(t) && (this.client.logger.warn("ping() on pairing topic is deprecated and will be removed in the next major release."), await this.client.core.pairing.ping({ topic: t }));
    }), c2(this, "emit", async (e) => {
      this.isInitialized(), await this.confirmOnlineStateOrThrow(), await this.isValidEmit(e);
      const { topic: t, event: s, chainId: i3 } = e, r2 = getBigIntRpcId().toString(), o2 = payloadId();
      await this.sendRequest({ topic: t, method: "wc_sessionEvent", params: { event: s, chainId: i3 }, throwOnFailedPublish: true, relayRpcId: r2, clientRpcId: o2 });
    }), c2(this, "disconnect", async (e) => {
      this.isInitialized(), await this.confirmOnlineStateOrThrow(), await this.isValidDisconnect(e);
      const { topic: t } = e;
      if (this.client.session.keys.includes(t)) await this.sendRequest({ topic: t, method: "wc_sessionDelete", params: Nt("USER_DISCONNECTED"), throwOnFailedPublish: true }), await this.deleteSession({ topic: t, emitEvent: false });
      else if (this.client.core.pairing.pairings.keys.includes(t)) await this.client.core.pairing.disconnect({ topic: t });
      else {
        const { message: s } = ht("MISMATCHED_TOPIC", `Session or pairing topic not found: ${t}`);
        throw new Error(s);
      }
    }), c2(this, "find", (e) => (this.isInitialized(), this.client.session.getAll().filter((t) => ua(t, e)))), c2(this, "getPendingSessionRequests", () => this.client.pendingRequest.getAll()), c2(this, "authenticate", async (e, t) => {
      var s;
      this.isInitialized(), this.isValidAuthenticate(e);
      const i3 = t && this.client.core.linkModeSupportedApps.includes(t) && ((s = this.client.metadata.redirect) == null ? void 0 : s.linkMode), r2 = i3 ? Q.link_mode : Q.relay;
      r2 === Q.relay && await this.confirmOnlineStateOrThrow();
      const { chains: o2, statement: a2 = "", uri: l3, domain: p3, nonce: h4, type: u3, exp: d3, nbf: w2, methods: m2 = [], expiry: f6 } = e, _ = [...e.resources || []], { topic: g, uri: A4 } = await this.client.core.pairing.create({ methods: ["wc_sessionAuthenticate"], transportType: r2 });
      this.client.logger.info({ message: "Generated new pairing", pairing: { topic: g, uri: A4 } });
      const D2 = await this.client.core.crypto.generateKeyPair(), I3 = Pc(D2);
      if (await Promise.all([this.client.auth.authKeys.set(ce2, { responseTopic: I3, publicKey: D2 }), this.client.auth.pairingTopics.set(I3, { topic: I3, pairingTopic: g })]), await this.client.core.relayer.subscribe(I3, { transportType: r2 }), this.client.logger.info(`sending request to new pairing topic: ${g}`), m2.length > 0) {
        const { namespace: x2 } = Ne(o2[0]);
        let L3 = fs(x2, "request", m2);
        pe(_) && (L3 = ls(L3, _.pop())), _.push(L3);
      }
      const T2 = f6 && f6 > N.wc_sessionAuthenticate.req.ttl ? f6 : N.wc_sessionAuthenticate.req.ttl, K3 = { authPayload: { type: u3 ?? "caip122", chains: o2, statement: a2, aud: l3, domain: p3, version: "1", nonce: h4, iat: (/* @__PURE__ */ new Date()).toISOString(), exp: d3, nbf: w2, resources: _ }, requester: { publicKey: D2, metadata: this.client.metadata }, expiryTimestamp: Ei(T2) }, fe3 = { eip155: { chains: o2, methods: [.../* @__PURE__ */ new Set(["personal_sign", ...m2])], events: ["chainChanged", "accountsChanged"] } }, q3 = { requiredNamespaces: {}, optionalNamespaces: fe3, relays: [{ protocol: "irn" }], pairingTopic: g, proposer: { publicKey: D2, metadata: this.client.metadata }, expiryTimestamp: Ei(N.wc_sessionPropose.req.ttl), id: payloadId() }, { done: Rt4, resolve: je4, reject: Se4 } = gi(T2, "Request expired"), te3 = payloadId(), le4 = xi("session_connect", q3.id), Re2 = xi("session_request", te3), pe4 = async ({ error: x2, session: L3 }) => {
        this.events.off(Re2, ve3), x2 ? Se4(x2) : L3 && je4({ session: L3 });
      }, ve3 = async (x2) => {
        var L3, Fe4, Qe5;
        if (await this.deletePendingAuthRequest(te3, { message: "fulfilled", code: 0 }), x2.error) {
          const ie4 = Nt("WC_METHOD_UNSUPPORTED", "wc_sessionAuthenticate");
          return x2.error.code === ie4.code ? void 0 : (this.events.off(le4, pe4), Se4(x2.error.message));
        }
        await this.deleteProposal(q3.id), this.events.off(le4, pe4);
        const { cacaos: He4, responder: Q3 } = x2.result, Te3 = [], ze4 = [];
        for (const ie4 of He4) {
          await is({ cacao: ie4, projectId: this.client.core.projectId }) || (this.client.logger.error(ie4, "Signature verification failed"), Se4(Nt("SESSION_SETTLEMENT_FAILED", "Signature verification failed")));
          const { p: qe4 } = ie4, Pe4 = pe(qe4.resources), Ye4 = [dr(qe4.iss)], vt2 = De(qe4.iss);
          if (Pe4) {
            const Ne3 = ds(Pe4), It4 = hs(Pe4);
            Te3.push(...Ne3), Ye4.push(...It4);
          }
          for (const Ne3 of Ye4) ze4.push(`${Ne3}:${vt2}`);
        }
        const se4 = await this.client.core.crypto.generateSharedKey(D2, Q3.publicKey);
        let he4;
        Te3.length > 0 && (he4 = { topic: se4, acknowledged: true, self: { publicKey: D2, metadata: this.client.metadata }, peer: Q3, controller: Q3.publicKey, expiry: Ei(J2), requiredNamespaces: {}, optionalNamespaces: {}, relay: { protocol: "irn" }, pairingTopic: g, namespaces: ca([...new Set(Te3)], [...new Set(ze4)]), transportType: r2 }, await this.client.core.relayer.subscribe(se4, { transportType: r2 }), await this.client.session.set(se4, he4), g && await this.client.core.pairing.updateMetadata({ topic: g, metadata: Q3.metadata }), he4 = this.client.session.get(se4)), (L3 = this.client.metadata.redirect) != null && L3.linkMode && (Fe4 = Q3.metadata.redirect) != null && Fe4.linkMode && (Qe5 = Q3.metadata.redirect) != null && Qe5.universal && t && (this.client.core.addLinkModeSupportedApp(Q3.metadata.redirect.universal), this.client.session.update(se4, { transportType: Q.link_mode })), je4({ auths: He4, session: he4 });
      };
      this.events.once(le4, pe4), this.events.once(Re2, ve3);
      let Ie4;
      try {
        if (i3) {
          const x2 = formatJsonRpcRequest("wc_sessionAuthenticate", K3, te3);
          this.client.core.history.set(g, x2);
          const L3 = await this.client.core.crypto.encode("", x2, { type: re, encoding: xe });
          Ie4 = Xc(t, g, L3);
        } else await Promise.all([this.sendRequest({ topic: g, method: "wc_sessionAuthenticate", params: K3, expiry: e.expiry, throwOnFailedPublish: true, clientRpcId: te3 }), this.sendRequest({ topic: g, method: "wc_sessionPropose", params: q3, expiry: N.wc_sessionPropose.req.ttl, throwOnFailedPublish: true, clientRpcId: q3.id })]);
      } catch (x2) {
        throw this.events.off(le4, pe4), this.events.off(Re2, ve3), x2;
      }
      return await this.setProposal(q3.id, q3), await this.setAuthRequest(te3, { request: b2(v3({}, K3), { verifyContext: {} }), pairingTopic: g, transportType: r2 }), { uri: Ie4 ?? A4, response: Rt4 };
    }), c2(this, "approveSessionAuthenticate", async (e) => {
      const { id: t, auths: s } = e, i3 = this.client.core.eventClient.createEvent({ properties: { topic: t.toString(), trace: [ir2.authenticated_session_approve_started] } });
      try {
        this.isInitialized();
      } catch (f6) {
        throw i3.setError(sr2.no_internet_connection), f6;
      }
      const r2 = this.getPendingAuthRequest(t);
      if (!r2) throw i3.setError(sr2.authenticated_session_pending_request_not_found), new Error(`Could not find pending auth request with id ${t}`);
      const o2 = r2.transportType || Q.relay;
      o2 === Q.relay && await this.confirmOnlineStateOrThrow();
      const a2 = r2.requester.publicKey, l3 = await this.client.core.crypto.generateKeyPair(), p3 = Pc(a2), h4 = { type: Ft, receiverPublicKey: a2, senderPublicKey: l3 }, u3 = [], d3 = [];
      for (const f6 of s) {
        if (!await is({ cacao: f6, projectId: this.client.core.projectId })) {
          i3.setError(sr2.invalid_cacao);
          const I3 = Nt("SESSION_SETTLEMENT_FAILED", "Signature verification failed");
          throw await this.sendError({ id: t, topic: p3, error: I3, encodeOpts: h4 }), new Error(I3.message);
        }
        i3.addTrace(ir2.cacaos_verified);
        const { p: _ } = f6, g = pe(_.resources), A4 = [dr(_.iss)], D2 = De(_.iss);
        if (g) {
          const I3 = ds(g), T2 = hs(g);
          u3.push(...I3), A4.push(...T2);
        }
        for (const I3 of A4) d3.push(`${I3}:${D2}`);
      }
      const w2 = await this.client.core.crypto.generateSharedKey(l3, a2);
      i3.addTrace(ir2.create_authenticated_session_topic);
      let m2;
      if ((u3 == null ? void 0 : u3.length) > 0) {
        m2 = { topic: w2, acknowledged: true, self: { publicKey: l3, metadata: this.client.metadata }, peer: { publicKey: a2, metadata: r2.requester.metadata }, controller: a2, expiry: Ei(J2), authentication: s, requiredNamespaces: {}, optionalNamespaces: {}, relay: { protocol: "irn" }, pairingTopic: r2.pairingTopic, namespaces: ca([...new Set(u3)], [...new Set(d3)]), transportType: o2 }, i3.addTrace(ir2.subscribing_authenticated_session_topic);
        try {
          await this.client.core.relayer.subscribe(w2, { transportType: o2 });
        } catch (f6) {
          throw i3.setError(sr2.subscribe_authenticated_session_topic_failure), f6;
        }
        i3.addTrace(ir2.subscribe_authenticated_session_topic_success), await this.client.session.set(w2, m2), i3.addTrace(ir2.store_authenticated_session), await this.client.core.pairing.updateMetadata({ topic: r2.pairingTopic, metadata: r2.requester.metadata });
      }
      i3.addTrace(ir2.publishing_authenticated_session_approve);
      try {
        await this.sendResult({ topic: p3, id: t, result: { cacaos: s, responder: { publicKey: l3, metadata: this.client.metadata } }, encodeOpts: h4, throwOnFailedPublish: true, appLink: this.getAppLinkIfEnabled(r2.requester.metadata, o2) });
      } catch (f6) {
        throw i3.setError(sr2.authenticated_session_approve_publish_failure), f6;
      }
      return await this.client.auth.requests.delete(t, { message: "fulfilled", code: 0 }), await this.client.core.pairing.activate({ topic: r2.pairingTopic }), this.client.core.eventClient.deleteEvent({ eventId: i3.eventId }), { session: m2 };
    }), c2(this, "rejectSessionAuthenticate", async (e) => {
      this.isInitialized();
      const { id: t, reason: s } = e, i3 = this.getPendingAuthRequest(t);
      if (!i3) throw new Error(`Could not find pending auth request with id ${t}`);
      i3.transportType === Q.relay && await this.confirmOnlineStateOrThrow();
      const r2 = i3.requester.publicKey, o2 = await this.client.core.crypto.generateKeyPair(), a2 = Pc(r2), l3 = { type: Ft, receiverPublicKey: r2, senderPublicKey: o2 };
      await this.sendError({ id: t, topic: a2, error: s, encodeOpts: l3, rpcOpts: N.wc_sessionAuthenticate.reject, appLink: this.getAppLinkIfEnabled(i3.requester.metadata, i3.transportType) }), await this.client.auth.requests.delete(t, { message: "rejected", code: 0 }), await this.client.proposal.delete(t, Nt("USER_DISCONNECTED"));
    }), c2(this, "formatAuthMessage", (e) => {
      this.isInitialized();
      const { request: t, iss: s } = e;
      return hr(t, s);
    }), c2(this, "processRelayMessageCache", () => {
      setTimeout(async () => {
        if (this.relayMessageCache.length !== 0) for (; this.relayMessageCache.length > 0; ) try {
          const e = this.relayMessageCache.shift();
          e && await this.onRelayMessage(e);
        } catch (e) {
          this.client.logger.error(e);
        }
      }, 50);
    }), c2(this, "cleanupDuplicatePairings", async (e) => {
      if (e.pairingTopic) try {
        const t = this.client.core.pairing.pairings.get(e.pairingTopic), s = this.client.core.pairing.pairings.getAll().filter((i3) => {
          var r2, o2;
          return ((r2 = i3.peerMetadata) == null ? void 0 : r2.url) && ((o2 = i3.peerMetadata) == null ? void 0 : o2.url) === e.peer.metadata.url && i3.topic && i3.topic !== t.topic;
        });
        if (s.length === 0) return;
        this.client.logger.info(`Cleaning up ${s.length} duplicate pairing(s)`), await Promise.all(s.map((i3) => this.client.core.pairing.disconnect({ topic: i3.topic }))), this.client.logger.info("Duplicate pairings clean up finished");
      } catch (t) {
        this.client.logger.error(t);
      }
    }), c2(this, "deleteSession", async (e) => {
      var t;
      const { topic: s, expirerHasDeleted: i3 = false, emitEvent: r2 = true, id: o2 = 0 } = e, { self: a2 } = this.client.session.get(s);
      await this.client.core.relayer.unsubscribe(s), await this.client.session.delete(s, Nt("USER_DISCONNECTED")), this.addToRecentlyDeleted(s, "session"), this.client.core.crypto.keychain.has(a2.publicKey) && await this.client.core.crypto.deleteKeyPair(a2.publicKey), this.client.core.crypto.keychain.has(s) && await this.client.core.crypto.deleteSymKey(s), i3 || this.client.core.expirer.del(s), this.client.core.storage.removeItem(Me3).catch((l3) => this.client.logger.warn(l3)), this.getPendingSessionRequests().forEach((l3) => {
        l3.topic === s && this.deletePendingSessionRequest(l3.id, Nt("USER_DISCONNECTED"));
      }), s === ((t = this.sessionRequestQueue.queue[0]) == null ? void 0 : t.topic) && (this.sessionRequestQueue.state = $2.idle), r2 && this.client.events.emit("session_delete", { id: o2, topic: s });
    }), c2(this, "deleteProposal", async (e, t) => {
      if (t) try {
        const s = this.client.proposal.get(e), i3 = this.client.core.eventClient.getEvent({ topic: s.pairingTopic });
        i3 == null ? void 0 : i3.setError(tr2.proposal_expired);
      } catch {
      }
      await Promise.all([this.client.proposal.delete(e, Nt("USER_DISCONNECTED")), t ? Promise.resolve() : this.client.core.expirer.del(e)]), this.addToRecentlyDeleted(e, "proposal");
    }), c2(this, "deletePendingSessionRequest", async (e, t, s = false) => {
      await Promise.all([this.client.pendingRequest.delete(e, t), s ? Promise.resolve() : this.client.core.expirer.del(e)]), this.addToRecentlyDeleted(e, "request"), this.sessionRequestQueue.queue = this.sessionRequestQueue.queue.filter((i3) => i3.id !== e), s && (this.sessionRequestQueue.state = $2.idle, this.client.events.emit("session_request_expire", { id: e }));
    }), c2(this, "deletePendingAuthRequest", async (e, t, s = false) => {
      await Promise.all([this.client.auth.requests.delete(e, t), s ? Promise.resolve() : this.client.core.expirer.del(e)]);
    }), c2(this, "setExpiry", async (e, t) => {
      this.client.session.keys.includes(e) && (this.client.core.expirer.set(e, t), await this.client.session.update(e, { expiry: t }));
    }), c2(this, "setProposal", async (e, t) => {
      this.client.core.expirer.set(e, Ei(N.wc_sessionPropose.req.ttl)), await this.client.proposal.set(e, t);
    }), c2(this, "setAuthRequest", async (e, t) => {
      const { request: s, pairingTopic: i3, transportType: r2 = Q.relay } = t;
      this.client.core.expirer.set(e, s.expiryTimestamp), await this.client.auth.requests.set(e, { authPayload: s.authPayload, requester: s.requester, expiryTimestamp: s.expiryTimestamp, id: e, pairingTopic: i3, verifyContext: s.verifyContext, transportType: r2 });
    }), c2(this, "setPendingSessionRequest", async (e) => {
      const { id: t, topic: s, params: i3, verifyContext: r2 } = e, o2 = i3.request.expiryTimestamp || Ei(N.wc_sessionRequest.req.ttl);
      this.client.core.expirer.set(t, o2), await this.client.pendingRequest.set(t, { id: t, topic: s, params: i3, verifyContext: r2 });
    }), c2(this, "sendRequest", async (e) => {
      const { topic: t, method: s, params: i3, expiry: r2, relayRpcId: o2, clientRpcId: a2, throwOnFailedPublish: l3, appLink: p3, tvf: h4 } = e, u3 = formatJsonRpcRequest(s, i3, a2);
      let d3;
      const w2 = !!p3;
      try {
        const _ = w2 ? xe : qt;
        d3 = await this.client.core.crypto.encode(t, u3, { encoding: _ });
      } catch (_) {
        throw await this.cleanup(), this.client.logger.error(`sendRequest() -> core.crypto.encode() for topic ${t} failed`), _;
      }
      let m2;
      if (gt2.includes(s)) {
        const _ = kc(JSON.stringify(u3)), g = kc(d3);
        m2 = await this.client.core.verify.register({ id: g, decryptedId: _ });
      }
      const f6 = N[s].req;
      if (f6.attestation = m2, r2 && (f6.ttl = r2), o2 && (f6.id = o2), this.client.core.history.set(t, u3), w2) {
        const _ = Xc(p3, t, d3);
        await global.Linking.openURL(_, this.client.name);
      } else {
        const _ = N[s].req;
        r2 && (_.ttl = r2), o2 && (_.id = o2), _.tvf = b2(v3({}, h4), { correlationId: u3.id }), l3 ? (_.internal = b2(v3({}, _.internal), { throwOnFailedPublish: true }), await this.client.core.relayer.publish(t, d3, _)) : this.client.core.relayer.publish(t, d3, _).catch((g) => this.client.logger.error(g));
      }
      return u3.id;
    }), c2(this, "sendResult", async (e) => {
      const { id: t, topic: s, result: i3, throwOnFailedPublish: r2, encodeOpts: o2, appLink: a2 } = e, l3 = formatJsonRpcResult(t, i3);
      let p3;
      const h4 = a2 && typeof (global == null ? void 0 : global.Linking) < "u";
      try {
        const w2 = h4 ? xe : qt;
        p3 = await this.client.core.crypto.encode(s, l3, b2(v3({}, o2 || {}), { encoding: w2 }));
      } catch (w2) {
        throw await this.cleanup(), this.client.logger.error(`sendResult() -> core.crypto.encode() for topic ${s} failed`), w2;
      }
      let u3, d3;
      try {
        u3 = await this.client.core.history.get(s, t);
        const w2 = u3.request;
        try {
          this.shouldSetTVF(w2.method, w2.params) && (d3 = this.getTVFParams(t, w2.params, i3));
        } catch (m2) {
          this.client.logger.warn("sendResult() -> getTVFParams() failed", m2);
        }
      } catch (w2) {
        throw this.client.logger.error(`sendResult() -> history.get(${s}, ${t}) failed`), w2;
      }
      if (h4) {
        const w2 = Xc(a2, s, p3);
        await global.Linking.openURL(w2, this.client.name);
      } else {
        const w2 = u3.request.method, m2 = N[w2].res;
        m2.tvf = b2(v3({}, d3), { correlationId: t }), r2 ? (m2.internal = b2(v3({}, m2.internal), { throwOnFailedPublish: true }), await this.client.core.relayer.publish(s, p3, m2)) : this.client.core.relayer.publish(s, p3, m2).catch((f6) => this.client.logger.error(f6));
      }
      await this.client.core.history.resolve(l3);
    }), c2(this, "sendError", async (e) => {
      const { id: t, topic: s, error: i3, encodeOpts: r2, rpcOpts: o2, appLink: a2 } = e, l3 = formatJsonRpcError(t, i3);
      let p3;
      const h4 = a2 && typeof (global == null ? void 0 : global.Linking) < "u";
      try {
        const d3 = h4 ? xe : qt;
        p3 = await this.client.core.crypto.encode(s, l3, b2(v3({}, r2 || {}), { encoding: d3 }));
      } catch (d3) {
        throw await this.cleanup(), this.client.logger.error(`sendError() -> core.crypto.encode() for topic ${s} failed`), d3;
      }
      let u3;
      try {
        u3 = await this.client.core.history.get(s, t);
      } catch (d3) {
        throw this.client.logger.error(`sendError() -> history.get(${s}, ${t}) failed`), d3;
      }
      if (h4) {
        const d3 = Xc(a2, s, p3);
        await global.Linking.openURL(d3, this.client.name);
      } else {
        const d3 = u3.request.method, w2 = o2 || N[d3].res;
        this.client.core.relayer.publish(s, p3, w2);
      }
      await this.client.core.history.resolve(l3);
    }), c2(this, "cleanup", async () => {
      const e = [], t = [];
      this.client.session.getAll().forEach((s) => {
        let i3 = false;
        vi(s.expiry) && (i3 = true), this.client.core.crypto.keychain.has(s.topic) || (i3 = true), i3 && e.push(s.topic);
      }), this.client.proposal.getAll().forEach((s) => {
        vi(s.expiryTimestamp) && t.push(s.id);
      }), await Promise.all([...e.map((s) => this.deleteSession({ topic: s })), ...t.map((s) => this.deleteProposal(s))]);
    }), c2(this, "onProviderMessageEvent", async (e) => {
      !this.initialized || this.relayMessageCache.length > 0 ? this.relayMessageCache.push(e) : await this.onRelayMessage(e);
    }), c2(this, "onRelayEventRequest", async (e) => {
      this.requestQueue.queue.push(e), await this.processRequestsQueue();
    }), c2(this, "processRequestsQueue", async () => {
      if (this.requestQueue.state === $2.active) {
        this.client.logger.info("Request queue already active, skipping...");
        return;
      }
      for (this.client.logger.info(`Request queue starting with ${this.requestQueue.queue.length} requests`); this.requestQueue.queue.length > 0; ) {
        this.requestQueue.state = $2.active;
        const e = this.requestQueue.queue.shift();
        if (e) try {
          await this.processRequest(e);
        } catch (t) {
          this.client.logger.warn(t);
        }
      }
      this.requestQueue.state = $2.idle;
    }), c2(this, "processRequest", async (e) => {
      const { topic: t, payload: s, attestation: i3, transportType: r2, encryptedId: o2 } = e, a2 = s.method;
      if (!this.shouldIgnorePairingRequest({ topic: t, requestMethod: a2 })) switch (a2) {
        case "wc_sessionPropose":
          return await this.onSessionProposeRequest({ topic: t, payload: s, attestation: i3, encryptedId: o2 });
        case "wc_sessionSettle":
          return await this.onSessionSettleRequest(t, s);
        case "wc_sessionUpdate":
          return await this.onSessionUpdateRequest(t, s);
        case "wc_sessionExtend":
          return await this.onSessionExtendRequest(t, s);
        case "wc_sessionPing":
          return await this.onSessionPingRequest(t, s);
        case "wc_sessionDelete":
          return await this.onSessionDeleteRequest(t, s);
        case "wc_sessionRequest":
          return await this.onSessionRequest({ topic: t, payload: s, attestation: i3, encryptedId: o2, transportType: r2 });
        case "wc_sessionEvent":
          return await this.onSessionEventRequest(t, s);
        case "wc_sessionAuthenticate":
          return await this.onSessionAuthenticateRequest({ topic: t, payload: s, attestation: i3, encryptedId: o2, transportType: r2 });
        default:
          return this.client.logger.info(`Unsupported request method ${a2}`);
      }
    }), c2(this, "onRelayEventResponse", async (e) => {
      const { topic: t, payload: s, transportType: i3 } = e, r2 = (await this.client.core.history.get(t, s.id)).request.method;
      switch (r2) {
        case "wc_sessionPropose":
          return this.onSessionProposeResponse(t, s, i3);
        case "wc_sessionSettle":
          return this.onSessionSettleResponse(t, s);
        case "wc_sessionUpdate":
          return this.onSessionUpdateResponse(t, s);
        case "wc_sessionExtend":
          return this.onSessionExtendResponse(t, s);
        case "wc_sessionPing":
          return this.onSessionPingResponse(t, s);
        case "wc_sessionRequest":
          return this.onSessionRequestResponse(t, s);
        case "wc_sessionAuthenticate":
          return this.onSessionAuthenticateResponse(t, s);
        default:
          return this.client.logger.info(`Unsupported response method ${r2}`);
      }
    }), c2(this, "onRelayEventUnknownPayload", (e) => {
      const { topic: t } = e, { message: s } = ht("MISSING_OR_INVALID", `Decoded payload on topic ${t} is not identifiable as a JSON-RPC request or a response.`);
      throw new Error(s);
    }), c2(this, "shouldIgnorePairingRequest", (e) => {
      const { topic: t, requestMethod: s } = e, i3 = this.expectedPairingMethodMap.get(t);
      return !i3 || i3.includes(s) ? false : !!(i3.includes("wc_sessionAuthenticate") && this.client.events.listenerCount("session_authenticate") > 0);
    }), c2(this, "onSessionProposeRequest", async (e) => {
      const { topic: t, payload: s, attestation: i3, encryptedId: r2 } = e, { params: o2, id: a2 } = s;
      try {
        const l3 = this.client.core.eventClient.getEvent({ topic: t });
        this.client.events.listenerCount("session_proposal") === 0 && (console.warn("No listener for session_proposal event"), l3 == null ? void 0 : l3.setError(Y2.proposal_listener_not_found)), this.isValidConnect(v3({}, s.params));
        const p3 = o2.expiryTimestamp || Ei(N.wc_sessionPropose.req.ttl), h4 = v3({ id: a2, pairingTopic: t, expiryTimestamp: p3 }, o2);
        await this.setProposal(a2, h4);
        const u3 = await this.getVerifyContext({ attestationId: i3, hash: kc(JSON.stringify(s)), encryptedId: r2, metadata: h4.proposer.metadata });
        l3 == null ? void 0 : l3.addTrace(G2.emit_session_proposal), this.client.events.emit("session_proposal", { id: a2, params: h4, verifyContext: u3 });
      } catch (l3) {
        await this.sendError({ id: a2, topic: t, error: l3, rpcOpts: N.wc_sessionPropose.autoReject }), this.client.logger.error(l3);
      }
    }), c2(this, "onSessionProposeResponse", async (e, t, s) => {
      const { id: i3 } = t;
      if (isJsonRpcResult(t)) {
        const { result: r2 } = t;
        this.client.logger.trace({ type: "method", method: "onSessionProposeResponse", result: r2 });
        const o2 = this.client.proposal.get(i3);
        this.client.logger.trace({ type: "method", method: "onSessionProposeResponse", proposal: o2 });
        const a2 = o2.proposer.publicKey;
        this.client.logger.trace({ type: "method", method: "onSessionProposeResponse", selfPublicKey: a2 });
        const l3 = r2.responderPublicKey;
        this.client.logger.trace({ type: "method", method: "onSessionProposeResponse", peerPublicKey: l3 });
        const p3 = await this.client.core.crypto.generateSharedKey(a2, l3);
        this.pendingSessions.set(i3, { sessionTopic: p3, pairingTopic: e, proposalId: i3, publicKey: a2 });
        const h4 = await this.client.core.relayer.subscribe(p3, { transportType: s });
        this.client.logger.trace({ type: "method", method: "onSessionProposeResponse", subscriptionId: h4 }), await this.client.core.pairing.activate({ topic: e });
      } else if (isJsonRpcError(t)) {
        await this.client.proposal.delete(i3, Nt("USER_DISCONNECTED"));
        const r2 = xi("session_connect", i3);
        if (this.events.listenerCount(r2) === 0) throw new Error(`emitting ${r2} without any listeners, 954`);
        this.events.emit(r2, { error: t.error });
      }
    }), c2(this, "onSessionSettleRequest", async (e, t) => {
      const { id: s, params: i3 } = t;
      try {
        this.isValidSessionSettleRequest(i3);
        const { relay: r2, controller: o2, expiry: a2, namespaces: l3, sessionProperties: p3, scopedProperties: h4, sessionConfig: u3 } = t.params, d3 = [...this.pendingSessions.values()].find((f6) => f6.sessionTopic === e);
        if (!d3) return this.client.logger.error(`Pending session not found for topic ${e}`);
        const w2 = this.client.proposal.get(d3.proposalId), m2 = b2(v3(v3(v3({ topic: e, relay: r2, expiry: a2, namespaces: l3, acknowledged: true, pairingTopic: d3.pairingTopic, requiredNamespaces: w2.requiredNamespaces, optionalNamespaces: w2.optionalNamespaces, controller: o2.publicKey, self: { publicKey: d3.publicKey, metadata: this.client.metadata }, peer: { publicKey: o2.publicKey, metadata: o2.metadata } }, p3 && { sessionProperties: p3 }), h4 && { scopedProperties: h4 }), u3 && { sessionConfig: u3 }), { transportType: Q.relay });
        await this.client.session.set(m2.topic, m2), await this.setExpiry(m2.topic, m2.expiry), await this.client.core.pairing.updateMetadata({ topic: d3.pairingTopic, metadata: m2.peer.metadata }), this.client.events.emit("session_connect", { session: m2 }), this.events.emit(xi("session_connect", d3.proposalId), { session: m2 }), this.pendingSessions.delete(d3.proposalId), this.deleteProposal(d3.proposalId, false), this.cleanupDuplicatePairings(m2), await this.sendResult({ id: t.id, topic: e, result: true, throwOnFailedPublish: true });
      } catch (r2) {
        await this.sendError({ id: s, topic: e, error: r2 }), this.client.logger.error(r2);
      }
    }), c2(this, "onSessionSettleResponse", async (e, t) => {
      const { id: s } = t;
      isJsonRpcResult(t) ? (await this.client.session.update(e, { acknowledged: true }), this.events.emit(xi("session_approve", s), {})) : isJsonRpcError(t) && (await this.client.session.delete(e, Nt("USER_DISCONNECTED")), this.events.emit(xi("session_approve", s), { error: t.error }));
    }), c2(this, "onSessionUpdateRequest", async (e, t) => {
      const { params: s, id: i3 } = t;
      try {
        const r2 = `${e}_session_update`, o2 = Ra.get(r2);
        if (o2 && this.isRequestOutOfSync(o2, i3)) {
          this.client.logger.warn(`Discarding out of sync request - ${i3}`), this.sendError({ id: i3, topic: e, error: Nt("INVALID_UPDATE_REQUEST") });
          return;
        }
        this.isValidUpdate(v3({ topic: e }, s));
        try {
          Ra.set(r2, i3), await this.client.session.update(e, { namespaces: s.namespaces }), await this.sendResult({ id: i3, topic: e, result: true, throwOnFailedPublish: true });
        } catch (a2) {
          throw Ra.delete(r2), a2;
        }
        this.client.events.emit("session_update", { id: i3, topic: e, params: s });
      } catch (r2) {
        await this.sendError({ id: i3, topic: e, error: r2 }), this.client.logger.error(r2);
      }
    }), c2(this, "isRequestOutOfSync", (e, t) => t.toString().slice(0, -3) < e.toString().slice(0, -3)), c2(this, "onSessionUpdateResponse", (e, t) => {
      const { id: s } = t, i3 = xi("session_update", s);
      if (this.events.listenerCount(i3) === 0) throw new Error(`emitting ${i3} without any listeners`);
      isJsonRpcResult(t) ? this.events.emit(xi("session_update", s), {}) : isJsonRpcError(t) && this.events.emit(xi("session_update", s), { error: t.error });
    }), c2(this, "onSessionExtendRequest", async (e, t) => {
      const { id: s } = t;
      try {
        this.isValidExtend({ topic: e }), await this.setExpiry(e, Ei(J2)), await this.sendResult({ id: s, topic: e, result: true, throwOnFailedPublish: true }), this.client.events.emit("session_extend", { id: s, topic: e });
      } catch (i3) {
        await this.sendError({ id: s, topic: e, error: i3 }), this.client.logger.error(i3);
      }
    }), c2(this, "onSessionExtendResponse", (e, t) => {
      const { id: s } = t, i3 = xi("session_extend", s);
      if (this.events.listenerCount(i3) === 0) throw new Error(`emitting ${i3} without any listeners`);
      isJsonRpcResult(t) ? this.events.emit(xi("session_extend", s), {}) : isJsonRpcError(t) && this.events.emit(xi("session_extend", s), { error: t.error });
    }), c2(this, "onSessionPingRequest", async (e, t) => {
      const { id: s } = t;
      try {
        this.isValidPing({ topic: e }), await this.sendResult({ id: s, topic: e, result: true, throwOnFailedPublish: true }), this.client.events.emit("session_ping", { id: s, topic: e });
      } catch (i3) {
        await this.sendError({ id: s, topic: e, error: i3 }), this.client.logger.error(i3);
      }
    }), c2(this, "onSessionPingResponse", (e, t) => {
      const { id: s } = t, i3 = xi("session_ping", s);
      setTimeout(() => {
        if (this.events.listenerCount(i3) === 0) throw new Error(`emitting ${i3} without any listeners 2176`);
        isJsonRpcResult(t) ? this.events.emit(xi("session_ping", s), {}) : isJsonRpcError(t) && this.events.emit(xi("session_ping", s), { error: t.error });
      }, 500);
    }), c2(this, "onSessionDeleteRequest", async (e, t) => {
      const { id: s } = t;
      try {
        this.isValidDisconnect({ topic: e, reason: t.params }), Promise.all([new Promise((i3) => {
          this.client.core.relayer.once(C2.publish, async () => {
            i3(await this.deleteSession({ topic: e, id: s }));
          });
        }), this.sendResult({ id: s, topic: e, result: true, throwOnFailedPublish: true }), this.cleanupPendingSentRequestsForTopic({ topic: e, error: Nt("USER_DISCONNECTED") })]).catch((i3) => this.client.logger.error(i3));
      } catch (i3) {
        this.client.logger.error(i3);
      }
    }), c2(this, "onSessionRequest", async (e) => {
      var t, s, i3;
      const { topic: r2, payload: o2, attestation: a2, encryptedId: l3, transportType: p3 } = e, { id: h4, params: u3 } = o2;
      try {
        await this.isValidRequest(v3({ topic: r2 }, u3));
        const d3 = this.client.session.get(r2), w2 = await this.getVerifyContext({ attestationId: a2, hash: kc(JSON.stringify(formatJsonRpcRequest("wc_sessionRequest", u3, h4))), encryptedId: l3, metadata: d3.peer.metadata, transportType: p3 }), m2 = { id: h4, topic: r2, params: u3, verifyContext: w2 };
        await this.setPendingSessionRequest(m2), p3 === Q.link_mode && (t = d3.peer.metadata.redirect) != null && t.universal && this.client.core.addLinkModeSupportedApp((s = d3.peer.metadata.redirect) == null ? void 0 : s.universal), (i3 = this.client.signConfig) != null && i3.disableRequestQueue ? this.emitSessionRequest(m2) : (this.addSessionRequestToSessionRequestQueue(m2), this.processSessionRequestQueue());
      } catch (d3) {
        await this.sendError({ id: h4, topic: r2, error: d3 }), this.client.logger.error(d3);
      }
    }), c2(this, "onSessionRequestResponse", (e, t) => {
      const { id: s } = t, i3 = xi("session_request", s);
      if (this.events.listenerCount(i3) === 0) throw new Error(`emitting ${i3} without any listeners`);
      isJsonRpcResult(t) ? this.events.emit(xi("session_request", s), { result: t.result }) : isJsonRpcError(t) && this.events.emit(xi("session_request", s), { error: t.error });
    }), c2(this, "onSessionEventRequest", async (e, t) => {
      const { id: s, params: i3 } = t;
      try {
        const r2 = `${e}_session_event_${i3.event.name}`, o2 = Ra.get(r2);
        if (o2 && this.isRequestOutOfSync(o2, s)) {
          this.client.logger.info(`Discarding out of sync request - ${s}`);
          return;
        }
        this.isValidEmit(v3({ topic: e }, i3)), this.client.events.emit("session_event", { id: s, topic: e, params: i3 }), Ra.set(r2, s);
      } catch (r2) {
        await this.sendError({ id: s, topic: e, error: r2 }), this.client.logger.error(r2);
      }
    }), c2(this, "onSessionAuthenticateResponse", (e, t) => {
      const { id: s } = t;
      this.client.logger.trace({ type: "method", method: "onSessionAuthenticateResponse", topic: e, payload: t }), isJsonRpcResult(t) ? this.events.emit(xi("session_request", s), { result: t.result }) : isJsonRpcError(t) && this.events.emit(xi("session_request", s), { error: t.error });
    }), c2(this, "onSessionAuthenticateRequest", async (e) => {
      var t;
      const { topic: s, payload: i3, attestation: r2, encryptedId: o2, transportType: a2 } = e;
      try {
        const { requester: l3, authPayload: p3, expiryTimestamp: h4 } = i3.params, u3 = await this.getVerifyContext({ attestationId: r2, hash: kc(JSON.stringify(i3)), encryptedId: o2, metadata: l3.metadata, transportType: a2 }), d3 = { requester: l3, pairingTopic: s, id: i3.id, authPayload: p3, verifyContext: u3, expiryTimestamp: h4 };
        await this.setAuthRequest(i3.id, { request: d3, pairingTopic: s, transportType: a2 }), a2 === Q.link_mode && (t = l3.metadata.redirect) != null && t.universal && this.client.core.addLinkModeSupportedApp(l3.metadata.redirect.universal), this.client.events.emit("session_authenticate", { topic: s, params: i3.params, id: i3.id, verifyContext: u3 });
      } catch (l3) {
        this.client.logger.error(l3);
        const p3 = i3.params.requester.publicKey, h4 = await this.client.core.crypto.generateKeyPair(), u3 = this.getAppLinkIfEnabled(i3.params.requester.metadata, a2), d3 = { type: Ft, receiverPublicKey: p3, senderPublicKey: h4 };
        await this.sendError({ id: i3.id, topic: s, error: l3, encodeOpts: d3, rpcOpts: N.wc_sessionAuthenticate.autoReject, appLink: u3 });
      }
    }), c2(this, "addSessionRequestToSessionRequestQueue", (e) => {
      this.sessionRequestQueue.queue.push(e);
    }), c2(this, "cleanupAfterResponse", (e) => {
      this.deletePendingSessionRequest(e.response.id, { message: "fulfilled", code: 0 }), setTimeout(() => {
        this.sessionRequestQueue.state = $2.idle, this.processSessionRequestQueue();
      }, (0, import_time3.toMiliseconds)(this.requestQueueDelay));
    }), c2(this, "cleanupPendingSentRequestsForTopic", ({ topic: e, error: t }) => {
      const s = this.client.core.history.pending;
      s.length > 0 && s.filter((i3) => i3.topic === e && i3.request.method === "wc_sessionRequest").forEach((i3) => {
        const r2 = i3.request.id, o2 = xi("session_request", r2);
        if (this.events.listenerCount(o2) === 0) throw new Error(`emitting ${o2} without any listeners`);
        this.events.emit(xi("session_request", i3.request.id), { error: t });
      });
    }), c2(this, "processSessionRequestQueue", () => {
      if (this.sessionRequestQueue.state === $2.active) {
        this.client.logger.info("session request queue is already active.");
        return;
      }
      const e = this.sessionRequestQueue.queue[0];
      if (!e) {
        this.client.logger.info("session request queue is empty.");
        return;
      }
      try {
        this.sessionRequestQueue.state = $2.active, this.emitSessionRequest(e);
      } catch (t) {
        this.client.logger.error(t);
      }
    }), c2(this, "emitSessionRequest", (e) => {
      this.client.events.emit("session_request", e);
    }), c2(this, "onPairingCreated", (e) => {
      if (e.methods && this.expectedPairingMethodMap.set(e.topic, e.methods), e.active) return;
      const t = this.client.proposal.getAll().find((s) => s.pairingTopic === e.topic);
      t && this.onSessionProposeRequest({ topic: e.topic, payload: formatJsonRpcRequest("wc_sessionPropose", b2(v3({}, t), { requiredNamespaces: t.requiredNamespaces, optionalNamespaces: t.optionalNamespaces, relays: t.relays, proposer: t.proposer, sessionProperties: t.sessionProperties, scopedProperties: t.scopedProperties }), t.id) });
    }), c2(this, "isValidConnect", async (e) => {
      if (!ma(e)) {
        const { message: l3 } = ht("MISSING_OR_INVALID", `connect() params: ${JSON.stringify(e)}`);
        throw new Error(l3);
      }
      const { pairingTopic: t, requiredNamespaces: s, optionalNamespaces: i3, sessionProperties: r2, scopedProperties: o2, relays: a2 } = e;
      if (Et(t) || await this.isValidPairingTopic(t), !ga(a2, true)) {
        const { message: l3 } = ht("MISSING_OR_INVALID", `connect() relays: ${a2}`);
        throw new Error(l3);
      }
      if (!Et(s) && Oe(s) !== 0) {
        const l3 = "requiredNamespaces are deprecated and are automatically assigned to optionalNamespaces";
        ["fatal", "error", "silent"].includes(this.client.logger.level) ? console.warn(l3) : this.client.logger.warn(l3), this.validateNamespaces(s, "requiredNamespaces");
      }
      if (!Et(i3) && Oe(i3) !== 0 && this.validateNamespaces(i3, "optionalNamespaces"), Et(r2) || this.validateSessionProps(r2, "sessionProperties"), !Et(o2)) {
        this.validateSessionProps(o2, "scopedProperties");
        const l3 = Object.keys(s || {}).concat(Object.keys(i3 || {}));
        if (!Object.keys(o2).every((p3) => l3.includes(p3))) throw new Error(`Scoped properties must be a subset of required/optional namespaces, received: ${JSON.stringify(o2)}, required/optional namespaces: ${JSON.stringify(l3)}`);
      }
    }), c2(this, "validateNamespaces", (e, t) => {
      const s = pa(e, "connect()", t);
      if (s) throw new Error(s.message);
    }), c2(this, "isValidApprove", async (e) => {
      if (!ma(e)) throw new Error(ht("MISSING_OR_INVALID", `approve() params: ${e}`).message);
      const { id: t, namespaces: s, relayProtocol: i3, sessionProperties: r2, scopedProperties: o2 } = e;
      this.checkRecentlyDeleted(t), await this.isValidProposalId(t);
      const a2 = this.client.proposal.get(t), l3 = Bo(s, "approve()");
      if (l3) throw new Error(l3.message);
      const p3 = No(a2.requiredNamespaces, s, "approve()");
      if (p3) throw new Error(p3.message);
      if (!nt(i3, true)) {
        const { message: h4 } = ht("MISSING_OR_INVALID", `approve() relayProtocol: ${i3}`);
        throw new Error(h4);
      }
      if (Et(r2) || this.validateSessionProps(r2, "sessionProperties"), !Et(o2)) {
        this.validateSessionProps(o2, "scopedProperties");
        const h4 = new Set(Object.keys(s));
        if (!Object.keys(o2).every((u3) => h4.has(u3))) throw new Error(`Scoped properties must be a subset of approved namespaces, received: ${JSON.stringify(o2)}, approved namespaces: ${Array.from(h4).join(", ")}`);
      }
    }), c2(this, "isValidReject", async (e) => {
      if (!ma(e)) {
        const { message: i3 } = ht("MISSING_OR_INVALID", `reject() params: ${e}`);
        throw new Error(i3);
      }
      const { id: t, reason: s } = e;
      if (this.checkRecentlyDeleted(t), await this.isValidProposalId(t), !wa(s)) {
        const { message: i3 } = ht("MISSING_OR_INVALID", `reject() reason: ${JSON.stringify(s)}`);
        throw new Error(i3);
      }
    }), c2(this, "isValidSessionSettleRequest", (e) => {
      if (!ma(e)) {
        const { message: l3 } = ht("MISSING_OR_INVALID", `onSessionSettleRequest() params: ${e}`);
        throw new Error(l3);
      }
      const { relay: t, controller: s, namespaces: i3, expiry: r2 } = e;
      if (!Io(t)) {
        const { message: l3 } = ht("MISSING_OR_INVALID", "onSessionSettleRequest() relay protocol should be a string");
        throw new Error(l3);
      }
      const o2 = ha(s, "onSessionSettleRequest()");
      if (o2) throw new Error(o2.message);
      const a2 = Bo(i3, "onSessionSettleRequest()");
      if (a2) throw new Error(a2.message);
      if (vi(r2)) {
        const { message: l3 } = ht("EXPIRED", "onSessionSettleRequest()");
        throw new Error(l3);
      }
    }), c2(this, "isValidUpdate", async (e) => {
      if (!ma(e)) {
        const { message: a2 } = ht("MISSING_OR_INVALID", `update() params: ${e}`);
        throw new Error(a2);
      }
      const { topic: t, namespaces: s } = e;
      this.checkRecentlyDeleted(t), await this.isValidSessionTopic(t);
      const i3 = this.client.session.get(t), r2 = Bo(s, "update()");
      if (r2) throw new Error(r2.message);
      const o2 = No(i3.requiredNamespaces, s, "update()");
      if (o2) throw new Error(o2.message);
    }), c2(this, "isValidExtend", async (e) => {
      if (!ma(e)) {
        const { message: s } = ht("MISSING_OR_INVALID", `extend() params: ${e}`);
        throw new Error(s);
      }
      const { topic: t } = e;
      this.checkRecentlyDeleted(t), await this.isValidSessionTopic(t);
    }), c2(this, "isValidRequest", async (e) => {
      if (!ma(e)) {
        const { message: a2 } = ht("MISSING_OR_INVALID", `request() params: ${e}`);
        throw new Error(a2);
      }
      const { topic: t, request: s, chainId: i3, expiry: r2 } = e;
      this.checkRecentlyDeleted(t), await this.isValidSessionTopic(t);
      const { namespaces: o2 } = this.client.session.get(t);
      if (!xa(o2, i3)) {
        const { message: a2 } = ht("MISSING_OR_INVALID", `request() chainId: ${i3}`);
        throw new Error(a2);
      }
      if (!ba(s)) {
        const { message: a2 } = ht("MISSING_OR_INVALID", `request() ${JSON.stringify(s)}`);
        throw new Error(a2);
      }
      if (!Sa(o2, i3, s.method)) {
        const { message: a2 } = ht("MISSING_OR_INVALID", `request() method: ${s.method}`);
        throw new Error(a2);
      }
      if (r2 && !Ia(r2, _e3)) {
        const { message: a2 } = ht("MISSING_OR_INVALID", `request() expiry: ${r2}. Expiry must be a number (in seconds) between ${_e3.min} and ${_e3.max}`);
        throw new Error(a2);
      }
    }), c2(this, "isValidRespond", async (e) => {
      var t;
      if (!ma(e)) {
        const { message: r2 } = ht("MISSING_OR_INVALID", `respond() params: ${e}`);
        throw new Error(r2);
      }
      const { topic: s, response: i3 } = e;
      try {
        await this.isValidSessionTopic(s);
      } catch (r2) {
        throw (t = e == null ? void 0 : e.response) != null && t.id && this.cleanupAfterResponse(e), r2;
      }
      if (!Ea(i3)) {
        const { message: r2 } = ht("MISSING_OR_INVALID", `respond() response: ${JSON.stringify(i3)}`);
        throw new Error(r2);
      }
    }), c2(this, "isValidPing", async (e) => {
      if (!ma(e)) {
        const { message: s } = ht("MISSING_OR_INVALID", `ping() params: ${e}`);
        throw new Error(s);
      }
      const { topic: t } = e;
      await this.isValidSessionOrPairingTopic(t);
    }), c2(this, "isValidEmit", async (e) => {
      if (!ma(e)) {
        const { message: o2 } = ht("MISSING_OR_INVALID", `emit() params: ${e}`);
        throw new Error(o2);
      }
      const { topic: t, event: s, chainId: i3 } = e;
      await this.isValidSessionTopic(t);
      const { namespaces: r2 } = this.client.session.get(t);
      if (!xa(r2, i3)) {
        const { message: o2 } = ht("MISSING_OR_INVALID", `emit() chainId: ${i3}`);
        throw new Error(o2);
      }
      if (!va(s)) {
        const { message: o2 } = ht("MISSING_OR_INVALID", `emit() event: ${JSON.stringify(s)}`);
        throw new Error(o2);
      }
      if (!Oa(r2, i3, s.name)) {
        const { message: o2 } = ht("MISSING_OR_INVALID", `emit() event: ${JSON.stringify(s)}`);
        throw new Error(o2);
      }
    }), c2(this, "isValidDisconnect", async (e) => {
      if (!ma(e)) {
        const { message: s } = ht("MISSING_OR_INVALID", `disconnect() params: ${e}`);
        throw new Error(s);
      }
      const { topic: t } = e;
      await this.isValidSessionOrPairingTopic(t);
    }), c2(this, "isValidAuthenticate", (e) => {
      const { chains: t, uri: s, domain: i3, nonce: r2 } = e;
      if (!Array.isArray(t) || t.length === 0) throw new Error("chains is required and must be a non-empty array");
      if (!nt(s, false)) throw new Error("uri is required parameter");
      if (!nt(i3, false)) throw new Error("domain is required parameter");
      if (!nt(r2, false)) throw new Error("nonce is required parameter");
      if ([...new Set(t.map((a2) => Ne(a2).namespace))].length > 1) throw new Error("Multi-namespace requests are not supported. Please request single namespace only.");
      const { namespace: o2 } = Ne(t[0]);
      if (o2 !== "eip155") throw new Error("Only eip155 namespace is supported for authenticated sessions. Please use .connect() for non-eip155 chains.");
    }), c2(this, "getVerifyContext", async (e) => {
      const { attestationId: t, hash: s, encryptedId: i3, metadata: r2, transportType: o2 } = e, a2 = { verified: { verifyUrl: r2.verifyUrl || ue2, validation: "UNKNOWN", origin: r2.url || "" } };
      try {
        if (o2 === Q.link_mode) {
          const p3 = this.getAppLinkIfEnabled(r2, o2);
          return a2.verified.validation = p3 && new URL(p3).origin === new URL(r2.url).origin ? "VALID" : "INVALID", a2;
        }
        const l3 = await this.client.core.verify.resolve({ attestationId: t, hash: s, encryptedId: i3, verifyUrl: r2.verifyUrl });
        l3 && (a2.verified.origin = l3.origin, a2.verified.isScam = l3.isScam, a2.verified.validation = l3.origin === new URL(r2.url).origin ? "VALID" : "INVALID");
      } catch (l3) {
        this.client.logger.warn(l3);
      }
      return this.client.logger.debug(`Verify context: ${JSON.stringify(a2)}`), a2;
    }), c2(this, "validateSessionProps", (e, t) => {
      Object.values(e).forEach((s, i3) => {
        if (s == null) {
          const { message: r2 } = ht("MISSING_OR_INVALID", `${t} must contain an existing value for each key. Received: ${s} for key ${Object.keys(e)[i3]}`);
          throw new Error(r2);
        }
      });
    }), c2(this, "getPendingAuthRequest", (e) => {
      const t = this.client.auth.requests.get(e);
      return typeof t == "object" ? t : void 0;
    }), c2(this, "addToRecentlyDeleted", (e, t) => {
      if (this.recentlyDeletedMap.set(e, t), this.recentlyDeletedMap.size >= this.recentlyDeletedLimit) {
        let s = 0;
        const i3 = this.recentlyDeletedLimit / 2;
        for (const r2 of this.recentlyDeletedMap.keys()) {
          if (s++ >= i3) break;
          this.recentlyDeletedMap.delete(r2);
        }
      }
    }), c2(this, "checkRecentlyDeleted", (e) => {
      const t = this.recentlyDeletedMap.get(e);
      if (t) {
        const { message: s } = ht("MISSING_OR_INVALID", `Record was recently deleted - ${t}: ${e}`);
        throw new Error(s);
      }
    }), c2(this, "isLinkModeEnabled", (e, t) => {
      var s, i3, r2, o2, a2, l3, p3, h4, u3;
      return !e || t !== Q.link_mode ? false : ((i3 = (s = this.client.metadata) == null ? void 0 : s.redirect) == null ? void 0 : i3.linkMode) === true && ((o2 = (r2 = this.client.metadata) == null ? void 0 : r2.redirect) == null ? void 0 : o2.universal) !== void 0 && ((l3 = (a2 = this.client.metadata) == null ? void 0 : a2.redirect) == null ? void 0 : l3.universal) !== "" && ((p3 = e == null ? void 0 : e.redirect) == null ? void 0 : p3.universal) !== void 0 && ((h4 = e == null ? void 0 : e.redirect) == null ? void 0 : h4.universal) !== "" && ((u3 = e == null ? void 0 : e.redirect) == null ? void 0 : u3.linkMode) === true && this.client.core.linkModeSupportedApps.includes(e.redirect.universal) && typeof (global == null ? void 0 : global.Linking) < "u";
    }), c2(this, "getAppLinkIfEnabled", (e, t) => {
      var s;
      return this.isLinkModeEnabled(e, t) ? (s = e == null ? void 0 : e.redirect) == null ? void 0 : s.universal : void 0;
    }), c2(this, "handleLinkModeMessage", ({ url: e }) => {
      if (!e || !e.includes("wc_ev") || !e.includes("topic")) return;
      const t = Ai(e, "topic") || "", s = decodeURIComponent(Ai(e, "wc_ev") || ""), i3 = this.client.session.keys.includes(t);
      i3 && this.client.session.update(t, { transportType: Q.link_mode }), this.client.core.dispatchEnvelope({ topic: t, message: s, sessionExists: i3 });
    }), c2(this, "registerLinkModeListeners", async () => {
      var e;
      if (Ii() || pt() && (e = this.client.metadata.redirect) != null && e.linkMode) {
        const t = global == null ? void 0 : global.Linking;
        if (typeof t < "u") {
          t.addEventListener("url", this.handleLinkModeMessage, this.client.name);
          const s = await t.getInitialURL();
          s && setTimeout(() => {
            this.handleLinkModeMessage({ url: s });
          }, 50);
        }
      }
    }), c2(this, "shouldSetTVF", (e, t) => {
      if (!t || e !== "wc_sessionRequest") return false;
      const { request: s } = t;
      return Object.keys(Ke3).includes(s.method);
    }), c2(this, "getTVFParams", (e, t, s) => {
      var i3, r2;
      try {
        const o2 = t.request.method, a2 = this.extractTxHashesFromResult(o2, s);
        return b2(v3({ correlationId: e, rpcMethods: [o2], chainId: t.chainId }, this.isValidContractData(t.request.params) && { contractAddresses: [(r2 = (i3 = t.request.params) == null ? void 0 : i3[0]) == null ? void 0 : r2.to] }), { txHashes: a2 });
      } catch (o2) {
        this.client.logger.warn("Error getting TVF params", o2);
      }
      return {};
    }), c2(this, "isValidContractData", (e) => {
      var t;
      if (!e) return false;
      try {
        const s = (e == null ? void 0 : e.data) || ((t = e == null ? void 0 : e[0]) == null ? void 0 : t.data);
        if (!s.startsWith("0x")) return false;
        const i3 = s.slice(2);
        return /^[0-9a-fA-F]*$/.test(i3) ? i3.length % 2 === 0 : false;
      } catch {
      }
      return false;
    }), c2(this, "extractTxHashesFromResult", (e, t) => {
      try {
        const s = Ke3[e];
        if (typeof t == "string") return [t];
        const i3 = t[s.key];
        if (se(i3)) return e === "solana_signAllTransactions" ? i3.map((r2) => Ji(r2)) : i3;
        if (typeof i3 == "string") return [i3];
      } catch (s) {
        this.client.logger.warn("Error extracting tx hashes from result", s);
      }
      return [];
    });
  }
  async processPendingMessageEvents() {
    try {
      const n2 = this.client.session.keys, e = this.client.core.relayer.messages.getWithoutAck(n2);
      for (const [t, s] of Object.entries(e)) for (const i3 of s) try {
        await this.onProviderMessageEvent({ topic: t, message: i3, publishedAt: Date.now() });
      } catch {
        this.client.logger.warn(`Error processing pending message event for topic: ${t}, message: ${i3}`);
      }
    } catch (n2) {
      this.client.logger.warn("processPendingMessageEvents failed", n2);
    }
  }
  isInitialized() {
    if (!this.initialized) {
      const { message: n2 } = ht("NOT_INITIALIZED", this.name);
      throw new Error(n2);
    }
  }
  async confirmOnlineStateOrThrow() {
    await this.client.core.relayer.confirmOnlineStateOrThrow();
  }
  registerRelayerEvents() {
    this.client.core.relayer.on(C2.message, (n2) => {
      this.onProviderMessageEvent(n2);
    });
  }
  async onRelayMessage(n2) {
    const { topic: e, message: t, attestation: s, transportType: i3 } = n2, { publicKey: r2 } = this.client.auth.authKeys.keys.includes(ce2) ? this.client.auth.authKeys.get(ce2) : { responseTopic: void 0, publicKey: void 0 };
    try {
      const o2 = await this.client.core.crypto.decode(e, t, { receiverPublicKey: r2, encoding: i3 === Q.link_mode ? xe : qt });
      isJsonRpcRequest(o2) ? (this.client.core.history.set(e, o2), await this.onRelayEventRequest({ topic: e, payload: o2, attestation: s, transportType: i3, encryptedId: kc(t) })) : isJsonRpcResponse(o2) ? (await this.client.core.history.resolve(o2), await this.onRelayEventResponse({ topic: e, payload: o2, transportType: i3 }), this.client.core.history.delete(e, o2.id)) : await this.onRelayEventUnknownPayload({ topic: e, payload: o2, transportType: i3 }), await this.client.core.relayer.messages.ack(e, t);
    } catch (o2) {
      this.client.logger.error(o2);
    }
  }
  registerExpirerEvents() {
    this.client.core.expirer.on(M2.expired, async (n2) => {
      const { topic: e, id: t } = bi(n2.target);
      if (t && this.client.pendingRequest.keys.includes(t)) return await this.deletePendingSessionRequest(t, ht("EXPIRED"), true);
      if (t && this.client.auth.requests.keys.includes(t)) return await this.deletePendingAuthRequest(t, ht("EXPIRED"), true);
      e ? this.client.session.keys.includes(e) && (await this.deleteSession({ topic: e, expirerHasDeleted: true }), this.client.events.emit("session_expire", { topic: e })) : t && (await this.deleteProposal(t, true), this.client.events.emit("proposal_expire", { id: t }));
    });
  }
  registerPairingEvents() {
    this.client.core.pairing.events.on(re2.create, (n2) => this.onPairingCreated(n2)), this.client.core.pairing.events.on(re2.delete, (n2) => {
      this.addToRecentlyDeleted(n2.topic, "pairing");
    });
  }
  isValidPairingTopic(n2) {
    if (!nt(n2, false)) {
      const { message: e } = ht("MISSING_OR_INVALID", `pairing topic should be a string: ${n2}`);
      throw new Error(e);
    }
    if (!this.client.core.pairing.pairings.keys.includes(n2)) {
      const { message: e } = ht("NO_MATCHING_KEY", `pairing topic doesn't exist: ${n2}`);
      throw new Error(e);
    }
    if (vi(this.client.core.pairing.pairings.get(n2).expiry)) {
      const { message: e } = ht("EXPIRED", `pairing topic: ${n2}`);
      throw new Error(e);
    }
  }
  async isValidSessionTopic(n2) {
    if (!nt(n2, false)) {
      const { message: e } = ht("MISSING_OR_INVALID", `session topic should be a string: ${n2}`);
      throw new Error(e);
    }
    if (this.checkRecentlyDeleted(n2), !this.client.session.keys.includes(n2)) {
      const { message: e } = ht("NO_MATCHING_KEY", `session topic doesn't exist: ${n2}`);
      throw new Error(e);
    }
    if (vi(this.client.session.get(n2).expiry)) {
      await this.deleteSession({ topic: n2 });
      const { message: e } = ht("EXPIRED", `session topic: ${n2}`);
      throw new Error(e);
    }
    if (!this.client.core.crypto.keychain.has(n2)) {
      const { message: e } = ht("MISSING_OR_INVALID", `session topic does not exist in keychain: ${n2}`);
      throw await this.deleteSession({ topic: n2 }), new Error(e);
    }
  }
  async isValidSessionOrPairingTopic(n2) {
    if (this.checkRecentlyDeleted(n2), this.client.session.keys.includes(n2)) await this.isValidSessionTopic(n2);
    else if (this.client.core.pairing.pairings.keys.includes(n2)) this.isValidPairingTopic(n2);
    else if (nt(n2, false)) {
      const { message: e } = ht("NO_MATCHING_KEY", `session or pairing topic doesn't exist: ${n2}`);
      throw new Error(e);
    } else {
      const { message: e } = ht("MISSING_OR_INVALID", `session or pairing topic should be a string: ${n2}`);
      throw new Error(e);
    }
  }
  async isValidProposalId(n2) {
    if (!ya(n2)) {
      const { message: e } = ht("MISSING_OR_INVALID", `proposal id should be a number: ${n2}`);
      throw new Error(e);
    }
    if (!this.client.proposal.keys.includes(n2)) {
      const { message: e } = ht("NO_MATCHING_KEY", `proposal id doesn't exist: ${n2}`);
      throw new Error(e);
    }
    if (vi(this.client.proposal.get(n2).expiryTimestamp)) {
      await this.deleteProposal(n2);
      const { message: e } = ht("EXPIRED", `proposal id: ${n2}`);
      throw new Error(e);
    }
  }
};
var Os2 = class extends zi2 {
  constructor(n2, e) {
    super(n2, e, pt2, we2), this.core = n2, this.logger = e;
  }
};
var St3 = class extends zi2 {
  constructor(n2, e) {
    super(n2, e, ht2, we2), this.core = n2, this.logger = e;
  }
};
var bs2 = class extends zi2 {
  constructor(n2, e) {
    super(n2, e, ut2, we2, (t) => t.id), this.core = n2, this.logger = e;
  }
};
var As2 = class extends zi2 {
  constructor(n2, e) {
    super(n2, e, mt2, ae2, () => ce2), this.core = n2, this.logger = e;
  }
};
var xs2 = class extends zi2 {
  constructor(n2, e) {
    super(n2, e, _t2, ae2), this.core = n2, this.logger = e;
  }
};
var Cs2 = class extends zi2 {
  constructor(n2, e) {
    super(n2, e, Et3, ae2, (t) => t.id), this.core = n2, this.logger = e;
  }
};
var Vs2 = Object.defineProperty;
var Ds2 = (S4, n2, e) => n2 in S4 ? Vs2(S4, n2, { enumerable: true, configurable: true, writable: true, value: e }) : S4[n2] = e;
var Ge3 = (S4, n2, e) => Ds2(S4, typeof n2 != "symbol" ? n2 + "" : n2, e);
var Ls2 = class {
  constructor(n2, e) {
    this.core = n2, this.logger = e, Ge3(this, "authKeys"), Ge3(this, "pairingTopics"), Ge3(this, "requests"), this.authKeys = new As2(this.core, this.logger), this.pairingTopics = new xs2(this.core, this.logger), this.requests = new Cs2(this.core, this.logger);
  }
  async init() {
    await this.authKeys.init(), await this.pairingTopics.init(), await this.requests.init();
  }
};
var ks2 = Object.defineProperty;
var Ms2 = (S4, n2, e) => n2 in S4 ? ks2(S4, n2, { enumerable: true, configurable: true, writable: true, value: e }) : S4[n2] = e;
var E3 = (S4, n2, e) => Ms2(S4, typeof n2 != "symbol" ? n2 + "" : n2, e);
var Ee3 = class _Ee extends J {
  constructor(n2) {
    super(n2), E3(this, "protocol", De2), E3(this, "version", Le2), E3(this, "name", me2.name), E3(this, "metadata"), E3(this, "core"), E3(this, "logger"), E3(this, "events", new import_events4.EventEmitter()), E3(this, "engine"), E3(this, "session"), E3(this, "proposal"), E3(this, "pendingRequest"), E3(this, "auth"), E3(this, "signConfig"), E3(this, "on", (t, s) => this.events.on(t, s)), E3(this, "once", (t, s) => this.events.once(t, s)), E3(this, "off", (t, s) => this.events.off(t, s)), E3(this, "removeListener", (t, s) => this.events.removeListener(t, s)), E3(this, "removeAllListeners", (t) => this.events.removeAllListeners(t)), E3(this, "connect", async (t) => {
      try {
        return await this.engine.connect(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), E3(this, "pair", async (t) => {
      try {
        return await this.engine.pair(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), E3(this, "approve", async (t) => {
      try {
        return await this.engine.approve(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), E3(this, "reject", async (t) => {
      try {
        return await this.engine.reject(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), E3(this, "update", async (t) => {
      try {
        return await this.engine.update(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), E3(this, "extend", async (t) => {
      try {
        return await this.engine.extend(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), E3(this, "request", async (t) => {
      try {
        return await this.engine.request(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), E3(this, "respond", async (t) => {
      try {
        return await this.engine.respond(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), E3(this, "ping", async (t) => {
      try {
        return await this.engine.ping(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), E3(this, "emit", async (t) => {
      try {
        return await this.engine.emit(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), E3(this, "disconnect", async (t) => {
      try {
        return await this.engine.disconnect(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), E3(this, "find", (t) => {
      try {
        return this.engine.find(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), E3(this, "getPendingSessionRequests", () => {
      try {
        return this.engine.getPendingSessionRequests();
      } catch (t) {
        throw this.logger.error(t.message), t;
      }
    }), E3(this, "authenticate", async (t, s) => {
      try {
        return await this.engine.authenticate(t, s);
      } catch (i3) {
        throw this.logger.error(i3.message), i3;
      }
    }), E3(this, "formatAuthMessage", (t) => {
      try {
        return this.engine.formatAuthMessage(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), E3(this, "approveSessionAuthenticate", async (t) => {
      try {
        return await this.engine.approveSessionAuthenticate(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), E3(this, "rejectSessionAuthenticate", async (t) => {
      try {
        return await this.engine.rejectSessionAuthenticate(t);
      } catch (s) {
        throw this.logger.error(s.message), s;
      }
    }), this.name = (n2 == null ? void 0 : n2.name) || me2.name, this.metadata = oi(n2 == null ? void 0 : n2.metadata), this.signConfig = n2 == null ? void 0 : n2.signConfig;
    const e = typeof (n2 == null ? void 0 : n2.logger) < "u" && typeof (n2 == null ? void 0 : n2.logger) != "string" ? n2.logger : (0, import_pino.default)(k({ level: (n2 == null ? void 0 : n2.logger) || me2.logger }));
    this.core = (n2 == null ? void 0 : n2.core) || new Xo2(n2), this.logger = E(e, this.name), this.session = new St3(this.core, this.logger), this.proposal = new Os2(this.core, this.logger), this.pendingRequest = new bs2(this.core, this.logger), this.engine = new Ns2(this), this.auth = new Ls2(this.core, this.logger);
  }
  static async init(n2) {
    const e = new _Ee(n2);
    return await e.initialize(), e;
  }
  get context() {
    return y(this.logger);
  }
  get pairing() {
    return this.core.pairing.pairings;
  }
  async initialize() {
    this.logger.trace("Initialized");
    try {
      await this.core.start(), await this.session.init(), await this.proposal.init(), await this.pendingRequest.init(), await this.auth.init(), await this.engine.init(), this.logger.info("SignClient Initialization Success"), setTimeout(() => {
        this.engine.processRelayMessageCache();
      }, (0, import_time3.toMiliseconds)(import_time3.ONE_SECOND));
    } catch (n2) {
      throw this.logger.info("SignClient Initialization Failure"), this.logger.error(n2.message), n2;
    }
  }
};

// node_modules/@reown/appkit/node_modules/@walletconnect/universal-provider/dist/index.es.js
var import_events5 = __toESM(require_events());
var et2 = "error";
var St4 = "wss://relay.walletconnect.org";
var Dt2 = "wc";
var qt3 = "universal_provider";
var U3 = `${Dt2}@2:${qt3}:`;
var st = "https://rpc.walletconnect.org/v1/";
var I2 = "generic";
var jt3 = `${st}bundler`;
var u2 = { DEFAULT_CHAIN_CHANGED: "default_chain_changed" };
function Rt3() {
}
function k4(s) {
  return s == null || typeof s != "object" && typeof s != "function";
}
function W2(s) {
  return ArrayBuffer.isView(s) && !(s instanceof DataView);
}
function _t3(s) {
  if (k4(s)) return s;
  if (Array.isArray(s) || W2(s) || s instanceof ArrayBuffer || typeof SharedArrayBuffer < "u" && s instanceof SharedArrayBuffer) return s.slice(0);
  const t = Object.getPrototypeOf(s), e = t.constructor;
  if (s instanceof Date || s instanceof Map || s instanceof Set) return new e(s);
  if (s instanceof RegExp) {
    const i3 = new e(s);
    return i3.lastIndex = s.lastIndex, i3;
  }
  if (s instanceof DataView) return new e(s.buffer.slice(0));
  if (s instanceof Error) {
    const i3 = new e(s.message);
    return i3.stack = s.stack, i3.name = s.name, i3.cause = s.cause, i3;
  }
  if (typeof File < "u" && s instanceof File) return new e([s], s.name, { type: s.type, lastModified: s.lastModified });
  if (typeof s == "object") {
    const i3 = Object.create(t);
    return Object.assign(i3, s);
  }
  return s;
}
function it3(s) {
  return typeof s == "object" && s !== null;
}
function rt(s) {
  return Object.getOwnPropertySymbols(s).filter((t) => Object.prototype.propertyIsEnumerable.call(s, t));
}
function nt2(s) {
  return s == null ? s === void 0 ? "[object Undefined]" : "[object Null]" : Object.prototype.toString.call(s);
}
var Ut2 = "[object RegExp]";
var at2 = "[object String]";
var ct2 = "[object Number]";
var ot2 = "[object Boolean]";
var ht3 = "[object Arguments]";
var Ft3 = "[object Symbol]";
var Lt3 = "[object Date]";
var Mt3 = "[object Map]";
var xt3 = "[object Set]";
var Bt3 = "[object Array]";
var Gt2 = "[object ArrayBuffer]";
var Jt3 = "[object Object]";
var zt2 = "[object DataView]";
var kt4 = "[object Uint8Array]";
var Wt3 = "[object Uint8ClampedArray]";
var Kt3 = "[object Uint16Array]";
var Vt3 = "[object Uint32Array]";
var Xt3 = "[object Int8Array]";
var Yt3 = "[object Int16Array]";
var Qt3 = "[object Int32Array]";
var Zt2 = "[object Float32Array]";
var Tt3 = "[object Float64Array]";
function te2(s, t) {
  return $3(s, void 0, s, /* @__PURE__ */ new Map(), t);
}
function $3(s, t, e, i3 = /* @__PURE__ */ new Map(), n2 = void 0) {
  const a2 = n2 == null ? void 0 : n2(s, t, e, i3);
  if (a2 != null) return a2;
  if (k4(s)) return s;
  if (i3.has(s)) return i3.get(s);
  if (Array.isArray(s)) {
    const r2 = new Array(s.length);
    i3.set(s, r2);
    for (let c3 = 0; c3 < s.length; c3++) r2[c3] = $3(s[c3], c3, e, i3, n2);
    return Object.hasOwn(s, "index") && (r2.index = s.index), Object.hasOwn(s, "input") && (r2.input = s.input), r2;
  }
  if (s instanceof Date) return new Date(s.getTime());
  if (s instanceof RegExp) {
    const r2 = new RegExp(s.source, s.flags);
    return r2.lastIndex = s.lastIndex, r2;
  }
  if (s instanceof Map) {
    const r2 = /* @__PURE__ */ new Map();
    i3.set(s, r2);
    for (const [c3, o2] of s) r2.set(c3, $3(o2, c3, e, i3, n2));
    return r2;
  }
  if (s instanceof Set) {
    const r2 = /* @__PURE__ */ new Set();
    i3.set(s, r2);
    for (const c3 of s) r2.add($3(c3, void 0, e, i3, n2));
    return r2;
  }
  if (typeof Buffer < "u" && Buffer.isBuffer(s)) return s.subarray();
  if (W2(s)) {
    const r2 = new (Object.getPrototypeOf(s)).constructor(s.length);
    i3.set(s, r2);
    for (let c3 = 0; c3 < s.length; c3++) r2[c3] = $3(s[c3], c3, e, i3, n2);
    return r2;
  }
  if (s instanceof ArrayBuffer || typeof SharedArrayBuffer < "u" && s instanceof SharedArrayBuffer) return s.slice(0);
  if (s instanceof DataView) {
    const r2 = new DataView(s.buffer.slice(0), s.byteOffset, s.byteLength);
    return i3.set(s, r2), y4(r2, s, e, i3, n2), r2;
  }
  if (typeof File < "u" && s instanceof File) {
    const r2 = new File([s], s.name, { type: s.type });
    return i3.set(s, r2), y4(r2, s, e, i3, n2), r2;
  }
  if (s instanceof Blob) {
    const r2 = new Blob([s], { type: s.type });
    return i3.set(s, r2), y4(r2, s, e, i3, n2), r2;
  }
  if (s instanceof Error) {
    const r2 = new s.constructor();
    return i3.set(s, r2), r2.message = s.message, r2.name = s.name, r2.stack = s.stack, r2.cause = s.cause, y4(r2, s, e, i3, n2), r2;
  }
  if (typeof s == "object" && ee3(s)) {
    const r2 = Object.create(Object.getPrototypeOf(s));
    return i3.set(s, r2), y4(r2, s, e, i3, n2), r2;
  }
  return s;
}
function y4(s, t, e = s, i3, n2) {
  const a2 = [...Object.keys(t), ...rt(t)];
  for (let r2 = 0; r2 < a2.length; r2++) {
    const c3 = a2[r2], o2 = Object.getOwnPropertyDescriptor(s, c3);
    (o2 == null || o2.writable) && (s[c3] = $3(t[c3], c3, e, i3, n2));
  }
}
function ee3(s) {
  switch (nt2(s)) {
    case ht3:
    case Bt3:
    case Gt2:
    case zt2:
    case ot2:
    case Lt3:
    case Zt2:
    case Tt3:
    case Xt3:
    case Yt3:
    case Qt3:
    case Mt3:
    case ct2:
    case Jt3:
    case Ut2:
    case xt3:
    case at2:
    case Ft3:
    case kt4:
    case Wt3:
    case Kt3:
    case Vt3:
      return true;
    default:
      return false;
  }
}
function se3(s, t) {
  return te2(s, (e, i3, n2, a2) => {
    const r2 = t == null ? void 0 : t(e, i3, n2, a2);
    if (r2 != null) return r2;
    if (typeof s == "object") switch (Object.prototype.toString.call(s)) {
      case ct2:
      case at2:
      case ot2: {
        const c3 = new s.constructor(s == null ? void 0 : s.valueOf());
        return y4(c3, s), c3;
      }
      case ht3: {
        const c3 = {};
        return y4(c3, s), c3.length = s.length, c3[Symbol.iterator] = s[Symbol.iterator], c3;
      }
      default:
        return;
    }
  });
}
function pt3(s) {
  return se3(s);
}
function dt3(s) {
  return s !== null && typeof s == "object" && nt2(s) === "[object Arguments]";
}
function ie3(s) {
  return W2(s);
}
function re3(s) {
  var _a;
  if (typeof s != "object" || s == null) return false;
  if (Object.getPrototypeOf(s) === null) return true;
  if (Object.prototype.toString.call(s) !== "[object Object]") {
    const e = s[Symbol.toStringTag];
    return e == null || !((_a = Object.getOwnPropertyDescriptor(s, Symbol.toStringTag)) == null ? void 0 : _a.writable) ? false : s.toString() === `[object ${e}]`;
  }
  let t = s;
  for (; Object.getPrototypeOf(t) !== null; ) t = Object.getPrototypeOf(t);
  return Object.getPrototypeOf(s) === t;
}
function ne3(s, ...t) {
  const e = t.slice(0, -1), i3 = t[t.length - 1];
  let n2 = s;
  for (let a2 = 0; a2 < e.length; a2++) {
    const r2 = e[a2];
    n2 = F3(n2, r2, i3, /* @__PURE__ */ new Map());
  }
  return n2;
}
function F3(s, t, e, i3) {
  if (k4(s) && (s = Object(s)), t == null || typeof t != "object") return s;
  if (i3.has(t)) return _t3(i3.get(t));
  if (i3.set(t, s), Array.isArray(t)) {
    t = t.slice();
    for (let a2 = 0; a2 < t.length; a2++) t[a2] = t[a2] ?? void 0;
  }
  const n2 = [...Object.keys(t), ...rt(t)];
  for (let a2 = 0; a2 < n2.length; a2++) {
    const r2 = n2[a2];
    let c3 = t[r2], o2 = s[r2];
    if (dt3(c3) && (c3 = { ...c3 }), dt3(o2) && (o2 = { ...o2 }), typeof Buffer < "u" && Buffer.isBuffer(c3) && (c3 = pt3(c3)), Array.isArray(c3)) if (typeof o2 == "object" && o2 != null) {
      const w2 = [], v4 = Reflect.ownKeys(o2);
      for (let P3 = 0; P3 < v4.length; P3++) {
        const p3 = v4[P3];
        w2[p3] = o2[p3];
      }
      o2 = w2;
    } else o2 = [];
    const m2 = e(o2, c3, r2, s, t, i3);
    m2 != null ? s[r2] = m2 : Array.isArray(c3) || it3(o2) && it3(c3) ? s[r2] = F3(o2, c3, e, i3) : o2 == null && re3(c3) ? s[r2] = F3({}, c3, e, i3) : o2 == null && ie3(c3) ? s[r2] = pt3(c3) : (o2 === void 0 || c3 !== void 0) && (s[r2] = c3);
  }
  return s;
}
function ae3(s, ...t) {
  return ne3(s, ...t, Rt3);
}
var ce3 = Object.defineProperty;
var oe2 = Object.defineProperties;
var he3 = Object.getOwnPropertyDescriptors;
var ut3 = Object.getOwnPropertySymbols;
var pe3 = Object.prototype.hasOwnProperty;
var de3 = Object.prototype.propertyIsEnumerable;
var lt2 = (s, t, e) => t in s ? ce3(s, t, { enumerable: true, configurable: true, writable: true, value: e }) : s[t] = e;
var L2 = (s, t) => {
  for (var e in t || (t = {})) pe3.call(t, e) && lt2(s, e, t[e]);
  if (ut3) for (var e of ut3(t)) de3.call(t, e) && lt2(s, e, t[e]);
  return s;
};
var ue3 = (s, t) => oe2(s, he3(t));
function d2(s, t, e) {
  var i3;
  const n2 = Ne(s);
  return ((i3 = t.rpcMap) == null ? void 0 : i3[n2.reference]) || `${st}?chainId=${n2.namespace}:${n2.reference}&projectId=${e}`;
}
function b3(s) {
  return s.includes(":") ? s.split(":")[1] : s;
}
function ft3(s) {
  return s.map((t) => `${t.split(":")[0]}:${t.split(":")[1]}`);
}
function le3(s, t) {
  const e = Object.keys(t.namespaces).filter((n2) => n2.includes(s));
  if (!e.length) return [];
  const i3 = [];
  return e.forEach((n2) => {
    const a2 = t.namespaces[n2].accounts;
    i3.push(...a2);
  }), i3;
}
function M3(s = {}, t = {}) {
  const e = mt3(s), i3 = mt3(t);
  return ae3(e, i3);
}
function mt3(s) {
  var t, e, i3, n2, a2;
  const r2 = {};
  if (!Oe(s)) return r2;
  for (const [c3, o2] of Object.entries(s)) {
    const m2 = yn(c3) ? [c3] : o2.chains, w2 = o2.methods || [], v4 = o2.events || [], P3 = o2.rpcMap || {}, p3 = yo(c3);
    r2[p3] = ue3(L2(L2({}, r2[p3]), o2), { chains: ot(m2, (t = r2[p3]) == null ? void 0 : t.chains), methods: ot(w2, (e = r2[p3]) == null ? void 0 : e.methods), events: ot(v4, (i3 = r2[p3]) == null ? void 0 : i3.events) }), (Oe(P3) || Oe(((n2 = r2[p3]) == null ? void 0 : n2.rpcMap) || {})) && (r2[p3].rpcMap = L2(L2({}, P3), (a2 = r2[p3]) == null ? void 0 : a2.rpcMap));
  }
  return r2;
}
function vt(s) {
  return s.includes(":") ? s.split(":")[2] : s;
}
function gt3(s) {
  const t = {};
  for (const [e, i3] of Object.entries(s)) {
    const n2 = i3.methods || [], a2 = i3.events || [], r2 = i3.accounts || [], c3 = yn(e) ? [e] : i3.chains ? i3.chains : ft3(i3.accounts);
    t[e] = { chains: c3, methods: n2, events: a2, accounts: r2 };
  }
  return t;
}
function K2(s) {
  return typeof s == "number" ? s : s.includes("0x") ? parseInt(s, 16) : (s = s.includes(":") ? s.split(":")[1] : s, isNaN(Number(s)) ? s : Number(s));
}
var Pt3 = {};
var h3 = (s) => Pt3[s];
var V4 = (s, t) => {
  Pt3[s] = t;
};
var fe2 = Object.defineProperty;
var me3 = (s, t, e) => t in s ? fe2(s, t, { enumerable: true, configurable: true, writable: true, value: e }) : s[t] = e;
var O3 = (s, t, e) => me3(s, typeof t != "symbol" ? t + "" : t, e);
var ve2 = class {
  constructor(t) {
    O3(this, "name", "polkadot"), O3(this, "client"), O3(this, "httpProviders"), O3(this, "events"), O3(this, "namespace"), O3(this, "chainId"), this.namespace = t.namespace, this.events = h3("events"), this.client = h3("client"), this.chainId = this.getDefaultChain(), this.httpProviders = this.createHttpProviders();
  }
  updateNamespace(t) {
    this.namespace = Object.assign(this.namespace, t);
  }
  requestAccounts() {
    return this.getAccounts();
  }
  getDefaultChain() {
    if (this.chainId) return this.chainId;
    if (this.namespace.defaultChain) return this.namespace.defaultChain;
    const t = this.namespace.chains[0];
    if (!t) throw new Error("ChainId not found");
    return t.split(":")[1];
  }
  request(t) {
    return this.namespace.methods.includes(t.request.method) ? this.client.request(t) : this.getHttpProvider().request(t.request);
  }
  setDefaultChain(t, e) {
    this.httpProviders[t] || this.setHttpProvider(t, e), this.chainId = t, this.events.emit(u2.DEFAULT_CHAIN_CHANGED, `${this.name}:${t}`);
  }
  getAccounts() {
    const t = this.namespace.accounts;
    return t ? t.filter((e) => e.split(":")[1] === this.chainId.toString()).map((e) => e.split(":")[2]) || [] : [];
  }
  createHttpProviders() {
    const t = {};
    return this.namespace.chains.forEach((e) => {
      var i3;
      const n2 = b3(e);
      t[n2] = this.createHttpProvider(n2, (i3 = this.namespace.rpcMap) == null ? void 0 : i3[e]);
    }), t;
  }
  getHttpProvider() {
    const t = `${this.name}:${this.chainId}`, e = this.httpProviders[t];
    if (typeof e > "u") throw new Error(`JSON-RPC provider for ${t} not found`);
    return e;
  }
  setHttpProvider(t, e) {
    const i3 = this.createHttpProvider(t, e);
    i3 && (this.httpProviders[t] = i3);
  }
  createHttpProvider(t, e) {
    const i3 = e || d2(t, this.namespace, this.client.core.projectId);
    if (!i3) throw new Error(`No RPC url provided for chainId: ${t}`);
    return new o(new f2(i3, h3("disableProviderPing")));
  }
};
var ge3 = Object.defineProperty;
var Pe3 = Object.defineProperties;
var we3 = Object.getOwnPropertyDescriptors;
var wt3 = Object.getOwnPropertySymbols;
var ye3 = Object.prototype.hasOwnProperty;
var be3 = Object.prototype.propertyIsEnumerable;
var X2 = (s, t, e) => t in s ? ge3(s, t, { enumerable: true, configurable: true, writable: true, value: e }) : s[t] = e;
var yt3 = (s, t) => {
  for (var e in t || (t = {})) ye3.call(t, e) && X2(s, e, t[e]);
  if (wt3) for (var e of wt3(t)) be3.call(t, e) && X2(s, e, t[e]);
  return s;
};
var bt2 = (s, t) => Pe3(s, we3(t));
var A3 = (s, t, e) => X2(s, typeof t != "symbol" ? t + "" : t, e);
var Ie3 = class {
  constructor(t) {
    A3(this, "name", "eip155"), A3(this, "client"), A3(this, "chainId"), A3(this, "namespace"), A3(this, "httpProviders"), A3(this, "events"), this.namespace = t.namespace, this.events = h3("events"), this.client = h3("client"), this.httpProviders = this.createHttpProviders(), this.chainId = parseInt(this.getDefaultChain());
  }
  async request(t) {
    switch (t.request.method) {
      case "eth_requestAccounts":
        return this.getAccounts();
      case "eth_accounts":
        return this.getAccounts();
      case "wallet_switchEthereumChain":
        return await this.handleSwitchChain(t);
      case "eth_chainId":
        return parseInt(this.getDefaultChain());
      case "wallet_getCapabilities":
        return await this.getCapabilities(t);
      case "wallet_getCallsStatus":
        return await this.getCallStatus(t);
    }
    return this.namespace.methods.includes(t.request.method) ? await this.client.request(t) : this.getHttpProvider().request(t.request);
  }
  updateNamespace(t) {
    this.namespace = Object.assign(this.namespace, t);
  }
  setDefaultChain(t, e) {
    this.httpProviders[t] || this.setHttpProvider(parseInt(t), e), this.chainId = parseInt(t), this.events.emit(u2.DEFAULT_CHAIN_CHANGED, `${this.name}:${t}`);
  }
  requestAccounts() {
    return this.getAccounts();
  }
  getDefaultChain() {
    if (this.chainId) return this.chainId.toString();
    if (this.namespace.defaultChain) return this.namespace.defaultChain;
    const t = this.namespace.chains[0];
    if (!t) throw new Error("ChainId not found");
    return t.split(":")[1];
  }
  createHttpProvider(t, e) {
    const i3 = e || d2(`${this.name}:${t}`, this.namespace, this.client.core.projectId);
    if (!i3) throw new Error(`No RPC url provided for chainId: ${t}`);
    return new o(new f2(i3, h3("disableProviderPing")));
  }
  setHttpProvider(t, e) {
    const i3 = this.createHttpProvider(t, e);
    i3 && (this.httpProviders[t] = i3);
  }
  createHttpProviders() {
    const t = {};
    return this.namespace.chains.forEach((e) => {
      var i3;
      const n2 = parseInt(b3(e));
      t[n2] = this.createHttpProvider(n2, (i3 = this.namespace.rpcMap) == null ? void 0 : i3[e]);
    }), t;
  }
  getAccounts() {
    const t = this.namespace.accounts;
    return t ? [...new Set(t.filter((e) => e.split(":")[1] === this.chainId.toString()).map((e) => e.split(":")[2]))] : [];
  }
  getHttpProvider() {
    const t = this.chainId, e = this.httpProviders[t];
    if (typeof e > "u") throw new Error(`JSON-RPC provider for ${t} not found`);
    return e;
  }
  async handleSwitchChain(t) {
    var e, i3;
    let n2 = t.request.params ? (e = t.request.params[0]) == null ? void 0 : e.chainId : "0x0";
    n2 = n2.startsWith("0x") ? n2 : `0x${n2}`;
    const a2 = parseInt(n2, 16);
    if (this.isChainApproved(a2)) this.setDefaultChain(`${a2}`);
    else if (this.namespace.methods.includes("wallet_switchEthereumChain")) await this.client.request({ topic: t.topic, request: { method: t.request.method, params: [{ chainId: n2 }] }, chainId: (i3 = this.namespace.chains) == null ? void 0 : i3[0] }), this.setDefaultChain(`${a2}`);
    else throw new Error(`Failed to switch to chain 'eip155:${a2}'. The chain is not approved or the wallet does not support 'wallet_switchEthereumChain' method.`);
    return null;
  }
  isChainApproved(t) {
    return this.namespace.chains.includes(`${this.name}:${t}`);
  }
  async getCapabilities(t) {
    var e, i3, n2, a2, r2;
    const c3 = (i3 = (e = t.request) == null ? void 0 : e.params) == null ? void 0 : i3[0], o2 = ((a2 = (n2 = t.request) == null ? void 0 : n2.params) == null ? void 0 : a2[1]) || [], m2 = `${c3}${o2.join(",")}`;
    if (!c3) throw new Error("Missing address parameter in `wallet_getCapabilities` request");
    const w2 = this.client.session.get(t.topic), v4 = ((r2 = w2 == null ? void 0 : w2.sessionProperties) == null ? void 0 : r2.capabilities) || {};
    if (v4 != null && v4[m2]) return v4 == null ? void 0 : v4[m2];
    const P3 = await this.client.request(t);
    try {
      await this.client.session.update(t.topic, { sessionProperties: bt2(yt3({}, w2.sessionProperties || {}), { capabilities: bt2(yt3({}, v4 || {}), { [m2]: P3 }) }) });
    } catch (p3) {
      console.warn("Failed to update session with capabilities", p3);
    }
    return P3;
  }
  async getCallStatus(t) {
    var e, i3;
    const n2 = this.client.session.get(t.topic), a2 = (e = n2.sessionProperties) == null ? void 0 : e.bundler_name;
    if (a2) {
      const c3 = this.getBundlerUrl(t.chainId, a2);
      try {
        return await this.getUserOperationReceipt(c3, t);
      } catch (o2) {
        console.warn("Failed to fetch call status from bundler", o2, c3);
      }
    }
    const r2 = (i3 = n2.sessionProperties) == null ? void 0 : i3.bundler_url;
    if (r2) try {
      return await this.getUserOperationReceipt(r2, t);
    } catch (c3) {
      console.warn("Failed to fetch call status from custom bundler", c3, r2);
    }
    if (this.namespace.methods.includes(t.request.method)) return await this.client.request(t);
    throw new Error("Fetching call status not approved by the wallet.");
  }
  async getUserOperationReceipt(t, e) {
    var i3;
    const n2 = new URL(t), a2 = await fetch(n2, { method: "POST", headers: { "Content-Type": "application/json" }, body: JSON.stringify(formatJsonRpcRequest("eth_getUserOperationReceipt", [(i3 = e.request.params) == null ? void 0 : i3[0]])) });
    if (!a2.ok) throw new Error(`Failed to fetch user operation receipt - ${a2.status}`);
    return await a2.json();
  }
  getBundlerUrl(t, e) {
    return `${jt3}?projectId=${this.client.core.projectId}&chainId=${t}&bundler=${e}`;
  }
};
var $e3 = Object.defineProperty;
var Oe3 = (s, t, e) => t in s ? $e3(s, t, { enumerable: true, configurable: true, writable: true, value: e }) : s[t] = e;
var C4 = (s, t, e) => Oe3(s, typeof t != "symbol" ? t + "" : t, e);
var Ae2 = class {
  constructor(t) {
    C4(this, "name", "solana"), C4(this, "client"), C4(this, "httpProviders"), C4(this, "events"), C4(this, "namespace"), C4(this, "chainId"), this.namespace = t.namespace, this.events = h3("events"), this.client = h3("client"), this.chainId = this.getDefaultChain(), this.httpProviders = this.createHttpProviders();
  }
  updateNamespace(t) {
    this.namespace = Object.assign(this.namespace, t);
  }
  requestAccounts() {
    return this.getAccounts();
  }
  request(t) {
    return this.namespace.methods.includes(t.request.method) ? this.client.request(t) : this.getHttpProvider().request(t.request);
  }
  setDefaultChain(t, e) {
    this.httpProviders[t] || this.setHttpProvider(t, e), this.chainId = t, this.events.emit(u2.DEFAULT_CHAIN_CHANGED, `${this.name}:${t}`);
  }
  getDefaultChain() {
    if (this.chainId) return this.chainId;
    if (this.namespace.defaultChain) return this.namespace.defaultChain;
    const t = this.namespace.chains[0];
    if (!t) throw new Error("ChainId not found");
    return t.split(":")[1];
  }
  getAccounts() {
    const t = this.namespace.accounts;
    return t ? [...new Set(t.filter((e) => e.split(":")[1] === this.chainId.toString()).map((e) => e.split(":")[2]))] : [];
  }
  createHttpProviders() {
    const t = {};
    return this.namespace.chains.forEach((e) => {
      var i3;
      const n2 = b3(e);
      t[n2] = this.createHttpProvider(n2, (i3 = this.namespace.rpcMap) == null ? void 0 : i3[e]);
    }), t;
  }
  getHttpProvider() {
    const t = `${this.name}:${this.chainId}`, e = this.httpProviders[t];
    if (typeof e > "u") throw new Error(`JSON-RPC provider for ${t} not found`);
    return e;
  }
  setHttpProvider(t, e) {
    const i3 = this.createHttpProvider(t, e);
    i3 && (this.httpProviders[t] = i3);
  }
  createHttpProvider(t, e) {
    const i3 = e || d2(t, this.namespace, this.client.core.projectId);
    if (!i3) throw new Error(`No RPC url provided for chainId: ${t}`);
    return new o(new f2(i3, h3("disableProviderPing")));
  }
};
var Ce3 = Object.defineProperty;
var He3 = (s, t, e) => t in s ? Ce3(s, t, { enumerable: true, configurable: true, writable: true, value: e }) : s[t] = e;
var H2 = (s, t, e) => He3(s, typeof t != "symbol" ? t + "" : t, e);
var Ee4 = class {
  constructor(t) {
    H2(this, "name", "cosmos"), H2(this, "client"), H2(this, "httpProviders"), H2(this, "events"), H2(this, "namespace"), H2(this, "chainId"), this.namespace = t.namespace, this.events = h3("events"), this.client = h3("client"), this.chainId = this.getDefaultChain(), this.httpProviders = this.createHttpProviders();
  }
  updateNamespace(t) {
    this.namespace = Object.assign(this.namespace, t);
  }
  requestAccounts() {
    return this.getAccounts();
  }
  getDefaultChain() {
    if (this.chainId) return this.chainId;
    if (this.namespace.defaultChain) return this.namespace.defaultChain;
    const t = this.namespace.chains[0];
    if (!t) throw new Error("ChainId not found");
    return t.split(":")[1];
  }
  request(t) {
    return this.namespace.methods.includes(t.request.method) ? this.client.request(t) : this.getHttpProvider().request(t.request);
  }
  setDefaultChain(t, e) {
    this.httpProviders[t] || this.setHttpProvider(t, e), this.chainId = t, this.events.emit(u2.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);
  }
  getAccounts() {
    const t = this.namespace.accounts;
    return t ? [...new Set(t.filter((e) => e.split(":")[1] === this.chainId.toString()).map((e) => e.split(":")[2]))] : [];
  }
  createHttpProviders() {
    const t = {};
    return this.namespace.chains.forEach((e) => {
      var i3;
      const n2 = b3(e);
      t[n2] = this.createHttpProvider(n2, (i3 = this.namespace.rpcMap) == null ? void 0 : i3[e]);
    }), t;
  }
  getHttpProvider() {
    const t = `${this.name}:${this.chainId}`, e = this.httpProviders[t];
    if (typeof e > "u") throw new Error(`JSON-RPC provider for ${t} not found`);
    return e;
  }
  setHttpProvider(t, e) {
    const i3 = this.createHttpProvider(t, e);
    i3 && (this.httpProviders[t] = i3);
  }
  createHttpProvider(t, e) {
    const i3 = e || d2(t, this.namespace, this.client.core.projectId);
    if (!i3) throw new Error(`No RPC url provided for chainId: ${t}`);
    return new o(new f2(i3, h3("disableProviderPing")));
  }
};
var Ne2 = Object.defineProperty;
var Se3 = (s, t, e) => t in s ? Ne2(s, t, { enumerable: true, configurable: true, writable: true, value: e }) : s[t] = e;
var E4 = (s, t, e) => Se3(s, typeof t != "symbol" ? t + "" : t, e);
var De3 = class {
  constructor(t) {
    E4(this, "name", "algorand"), E4(this, "client"), E4(this, "httpProviders"), E4(this, "events"), E4(this, "namespace"), E4(this, "chainId"), this.namespace = t.namespace, this.events = h3("events"), this.client = h3("client"), this.chainId = this.getDefaultChain(), this.httpProviders = this.createHttpProviders();
  }
  updateNamespace(t) {
    this.namespace = Object.assign(this.namespace, t);
  }
  requestAccounts() {
    return this.getAccounts();
  }
  request(t) {
    return this.namespace.methods.includes(t.request.method) ? this.client.request(t) : this.getHttpProvider().request(t.request);
  }
  setDefaultChain(t, e) {
    if (!this.httpProviders[t]) {
      const i3 = e || d2(`${this.name}:${t}`, this.namespace, this.client.core.projectId);
      if (!i3) throw new Error(`No RPC url provided for chainId: ${t}`);
      this.setHttpProvider(t, i3);
    }
    this.chainId = t, this.events.emit(u2.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);
  }
  getDefaultChain() {
    if (this.chainId) return this.chainId;
    if (this.namespace.defaultChain) return this.namespace.defaultChain;
    const t = this.namespace.chains[0];
    if (!t) throw new Error("ChainId not found");
    return t.split(":")[1];
  }
  getAccounts() {
    const t = this.namespace.accounts;
    return t ? [...new Set(t.filter((e) => e.split(":")[1] === this.chainId.toString()).map((e) => e.split(":")[2]))] : [];
  }
  createHttpProviders() {
    const t = {};
    return this.namespace.chains.forEach((e) => {
      var i3;
      t[e] = this.createHttpProvider(e, (i3 = this.namespace.rpcMap) == null ? void 0 : i3[e]);
    }), t;
  }
  getHttpProvider() {
    const t = `${this.name}:${this.chainId}`, e = this.httpProviders[t];
    if (typeof e > "u") throw new Error(`JSON-RPC provider for ${t} not found`);
    return e;
  }
  setHttpProvider(t, e) {
    const i3 = this.createHttpProvider(t, e);
    i3 && (this.httpProviders[t] = i3);
  }
  createHttpProvider(t, e) {
    const i3 = e || d2(t, this.namespace, this.client.core.projectId);
    return typeof i3 > "u" ? void 0 : new o(new f2(i3, h3("disableProviderPing")));
  }
};
var qe3 = Object.defineProperty;
var je3 = (s, t, e) => t in s ? qe3(s, t, { enumerable: true, configurable: true, writable: true, value: e }) : s[t] = e;
var N2 = (s, t, e) => je3(s, typeof t != "symbol" ? t + "" : t, e);
var Re = class {
  constructor(t) {
    N2(this, "name", "cip34"), N2(this, "client"), N2(this, "httpProviders"), N2(this, "events"), N2(this, "namespace"), N2(this, "chainId"), this.namespace = t.namespace, this.events = h3("events"), this.client = h3("client"), this.chainId = this.getDefaultChain(), this.httpProviders = this.createHttpProviders();
  }
  updateNamespace(t) {
    this.namespace = Object.assign(this.namespace, t);
  }
  requestAccounts() {
    return this.getAccounts();
  }
  getDefaultChain() {
    if (this.chainId) return this.chainId;
    if (this.namespace.defaultChain) return this.namespace.defaultChain;
    const t = this.namespace.chains[0];
    if (!t) throw new Error("ChainId not found");
    return t.split(":")[1];
  }
  request(t) {
    return this.namespace.methods.includes(t.request.method) ? this.client.request(t) : this.getHttpProvider().request(t.request);
  }
  setDefaultChain(t, e) {
    this.httpProviders[t] || this.setHttpProvider(t, e), this.chainId = t, this.events.emit(u2.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);
  }
  getAccounts() {
    const t = this.namespace.accounts;
    return t ? [...new Set(t.filter((e) => e.split(":")[1] === this.chainId.toString()).map((e) => e.split(":")[2]))] : [];
  }
  createHttpProviders() {
    const t = {};
    return this.namespace.chains.forEach((e) => {
      const i3 = this.getCardanoRPCUrl(e), n2 = b3(e);
      t[n2] = this.createHttpProvider(n2, i3);
    }), t;
  }
  getHttpProvider() {
    const t = `${this.name}:${this.chainId}`, e = this.httpProviders[t];
    if (typeof e > "u") throw new Error(`JSON-RPC provider for ${t} not found`);
    return e;
  }
  getCardanoRPCUrl(t) {
    const e = this.namespace.rpcMap;
    if (e) return e[t];
  }
  setHttpProvider(t, e) {
    const i3 = this.createHttpProvider(t, e);
    i3 && (this.httpProviders[t] = i3);
  }
  createHttpProvider(t, e) {
    const i3 = e || this.getCardanoRPCUrl(t);
    if (!i3) throw new Error(`No RPC url provided for chainId: ${t}`);
    return new o(new f2(i3, h3("disableProviderPing")));
  }
};
var _e4 = Object.defineProperty;
var Ue3 = (s, t, e) => t in s ? _e4(s, t, { enumerable: true, configurable: true, writable: true, value: e }) : s[t] = e;
var S3 = (s, t, e) => Ue3(s, typeof t != "symbol" ? t + "" : t, e);
var Fe3 = class {
  constructor(t) {
    S3(this, "name", "elrond"), S3(this, "client"), S3(this, "httpProviders"), S3(this, "events"), S3(this, "namespace"), S3(this, "chainId"), this.namespace = t.namespace, this.events = h3("events"), this.client = h3("client"), this.chainId = this.getDefaultChain(), this.httpProviders = this.createHttpProviders();
  }
  updateNamespace(t) {
    this.namespace = Object.assign(this.namespace, t);
  }
  requestAccounts() {
    return this.getAccounts();
  }
  request(t) {
    return this.namespace.methods.includes(t.request.method) ? this.client.request(t) : this.getHttpProvider().request(t.request);
  }
  setDefaultChain(t, e) {
    this.httpProviders[t] || this.setHttpProvider(t, e), this.chainId = t, this.events.emit(u2.DEFAULT_CHAIN_CHANGED, `${this.name}:${t}`);
  }
  getDefaultChain() {
    if (this.chainId) return this.chainId;
    if (this.namespace.defaultChain) return this.namespace.defaultChain;
    const t = this.namespace.chains[0];
    if (!t) throw new Error("ChainId not found");
    return t.split(":")[1];
  }
  getAccounts() {
    const t = this.namespace.accounts;
    return t ? [...new Set(t.filter((e) => e.split(":")[1] === this.chainId.toString()).map((e) => e.split(":")[2]))] : [];
  }
  createHttpProviders() {
    const t = {};
    return this.namespace.chains.forEach((e) => {
      var i3;
      const n2 = b3(e);
      t[n2] = this.createHttpProvider(n2, (i3 = this.namespace.rpcMap) == null ? void 0 : i3[e]);
    }), t;
  }
  getHttpProvider() {
    const t = `${this.name}:${this.chainId}`, e = this.httpProviders[t];
    if (typeof e > "u") throw new Error(`JSON-RPC provider for ${t} not found`);
    return e;
  }
  setHttpProvider(t, e) {
    const i3 = this.createHttpProvider(t, e);
    i3 && (this.httpProviders[t] = i3);
  }
  createHttpProvider(t, e) {
    const i3 = e || d2(t, this.namespace, this.client.core.projectId);
    if (!i3) throw new Error(`No RPC url provided for chainId: ${t}`);
    return new o(new f2(i3, h3("disableProviderPing")));
  }
};
var Le3 = Object.defineProperty;
var Me4 = (s, t, e) => t in s ? Le3(s, t, { enumerable: true, configurable: true, writable: true, value: e }) : s[t] = e;
var D = (s, t, e) => Me4(s, typeof t != "symbol" ? t + "" : t, e);
var xe2 = class {
  constructor(t) {
    D(this, "name", "multiversx"), D(this, "client"), D(this, "httpProviders"), D(this, "events"), D(this, "namespace"), D(this, "chainId"), this.namespace = t.namespace, this.events = h3("events"), this.client = h3("client"), this.chainId = this.getDefaultChain(), this.httpProviders = this.createHttpProviders();
  }
  updateNamespace(t) {
    this.namespace = Object.assign(this.namespace, t);
  }
  requestAccounts() {
    return this.getAccounts();
  }
  request(t) {
    return this.namespace.methods.includes(t.request.method) ? this.client.request(t) : this.getHttpProvider().request(t.request);
  }
  setDefaultChain(t, e) {
    this.httpProviders[t] || this.setHttpProvider(t, e), this.chainId = t, this.events.emit(u2.DEFAULT_CHAIN_CHANGED, `${this.name}:${t}`);
  }
  getDefaultChain() {
    if (this.chainId) return this.chainId;
    if (this.namespace.defaultChain) return this.namespace.defaultChain;
    const t = this.namespace.chains[0];
    if (!t) throw new Error("ChainId not found");
    return t.split(":")[1];
  }
  getAccounts() {
    const t = this.namespace.accounts;
    return t ? [...new Set(t.filter((e) => e.split(":")[1] === this.chainId.toString()).map((e) => e.split(":")[2]))] : [];
  }
  createHttpProviders() {
    const t = {};
    return this.namespace.chains.forEach((e) => {
      var i3;
      const n2 = b3(e);
      t[n2] = this.createHttpProvider(n2, (i3 = this.namespace.rpcMap) == null ? void 0 : i3[e]);
    }), t;
  }
  getHttpProvider() {
    const t = `${this.name}:${this.chainId}`, e = this.httpProviders[t];
    if (typeof e > "u") throw new Error(`JSON-RPC provider for ${t} not found`);
    return e;
  }
  setHttpProvider(t, e) {
    const i3 = this.createHttpProvider(t, e);
    i3 && (this.httpProviders[t] = i3);
  }
  createHttpProvider(t, e) {
    const i3 = e || d2(t, this.namespace, this.client.core.projectId);
    if (!i3) throw new Error(`No RPC url provided for chainId: ${t}`);
    return new o(new f2(i3, h3("disableProviderPing")));
  }
};
var Be3 = Object.defineProperty;
var Ge4 = (s, t, e) => t in s ? Be3(s, t, { enumerable: true, configurable: true, writable: true, value: e }) : s[t] = e;
var q2 = (s, t, e) => Ge4(s, typeof t != "symbol" ? t + "" : t, e);
var Je3 = class {
  constructor(t) {
    q2(this, "name", "near"), q2(this, "client"), q2(this, "httpProviders"), q2(this, "events"), q2(this, "namespace"), q2(this, "chainId"), this.namespace = t.namespace, this.events = h3("events"), this.client = h3("client"), this.chainId = this.getDefaultChain(), this.httpProviders = this.createHttpProviders();
  }
  updateNamespace(t) {
    this.namespace = Object.assign(this.namespace, t);
  }
  requestAccounts() {
    return this.getAccounts();
  }
  getDefaultChain() {
    if (this.chainId) return this.chainId;
    if (this.namespace.defaultChain) return this.namespace.defaultChain;
    const t = this.namespace.chains[0];
    if (!t) throw new Error("ChainId not found");
    return t.split(":")[1];
  }
  request(t) {
    return this.namespace.methods.includes(t.request.method) ? this.client.request(t) : this.getHttpProvider().request(t.request);
  }
  setDefaultChain(t, e) {
    if (this.chainId = t, !this.httpProviders[t]) {
      const i3 = e || d2(`${this.name}:${t}`, this.namespace);
      if (!i3) throw new Error(`No RPC url provided for chainId: ${t}`);
      this.setHttpProvider(t, i3);
    }
    this.events.emit(u2.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);
  }
  getAccounts() {
    const t = this.namespace.accounts;
    return t ? t.filter((e) => e.split(":")[1] === this.chainId.toString()).map((e) => e.split(":")[2]) || [] : [];
  }
  createHttpProviders() {
    const t = {};
    return this.namespace.chains.forEach((e) => {
      var i3;
      t[e] = this.createHttpProvider(e, (i3 = this.namespace.rpcMap) == null ? void 0 : i3[e]);
    }), t;
  }
  getHttpProvider() {
    const t = `${this.name}:${this.chainId}`, e = this.httpProviders[t];
    if (typeof e > "u") throw new Error(`JSON-RPC provider for ${t} not found`);
    return e;
  }
  setHttpProvider(t, e) {
    const i3 = this.createHttpProvider(t, e);
    i3 && (this.httpProviders[t] = i3);
  }
  createHttpProvider(t, e) {
    const i3 = e || d2(t, this.namespace);
    return typeof i3 > "u" ? void 0 : new o(new f2(i3, h3("disableProviderPing")));
  }
};
var ze3 = Object.defineProperty;
var ke4 = (s, t, e) => t in s ? ze3(s, t, { enumerable: true, configurable: true, writable: true, value: e }) : s[t] = e;
var j = (s, t, e) => ke4(s, typeof t != "symbol" ? t + "" : t, e);
var We4 = class {
  constructor(t) {
    j(this, "name", "tezos"), j(this, "client"), j(this, "httpProviders"), j(this, "events"), j(this, "namespace"), j(this, "chainId"), this.namespace = t.namespace, this.events = h3("events"), this.client = h3("client"), this.chainId = this.getDefaultChain(), this.httpProviders = this.createHttpProviders();
  }
  updateNamespace(t) {
    this.namespace = Object.assign(this.namespace, t);
  }
  requestAccounts() {
    return this.getAccounts();
  }
  getDefaultChain() {
    if (this.chainId) return this.chainId;
    if (this.namespace.defaultChain) return this.namespace.defaultChain;
    const t = this.namespace.chains[0];
    if (!t) throw new Error("ChainId not found");
    return t.split(":")[1];
  }
  request(t) {
    return this.namespace.methods.includes(t.request.method) ? this.client.request(t) : this.getHttpProvider().request(t.request);
  }
  setDefaultChain(t, e) {
    if (this.chainId = t, !this.httpProviders[t]) {
      const i3 = e || d2(`${this.name}:${t}`, this.namespace);
      if (!i3) throw new Error(`No RPC url provided for chainId: ${t}`);
      this.setHttpProvider(t, i3);
    }
    this.events.emit(u2.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);
  }
  getAccounts() {
    const t = this.namespace.accounts;
    return t ? t.filter((e) => e.split(":")[1] === this.chainId.toString()).map((e) => e.split(":")[2]) || [] : [];
  }
  createHttpProviders() {
    const t = {};
    return this.namespace.chains.forEach((e) => {
      t[e] = this.createHttpProvider(e);
    }), t;
  }
  getHttpProvider() {
    const t = `${this.name}:${this.chainId}`, e = this.httpProviders[t];
    if (typeof e > "u") throw new Error(`JSON-RPC provider for ${t} not found`);
    return e;
  }
  setHttpProvider(t, e) {
    const i3 = this.createHttpProvider(t, e);
    i3 && (this.httpProviders[t] = i3);
  }
  createHttpProvider(t, e) {
    const i3 = e || d2(t, this.namespace);
    return typeof i3 > "u" ? void 0 : new o(new f2(i3));
  }
};
var Ke4 = Object.defineProperty;
var Ve3 = (s, t, e) => t in s ? Ke4(s, t, { enumerable: true, configurable: true, writable: true, value: e }) : s[t] = e;
var R3 = (s, t, e) => Ve3(s, typeof t != "symbol" ? t + "" : t, e);
var Xe3 = class {
  constructor(t) {
    R3(this, "name", I2), R3(this, "client"), R3(this, "httpProviders"), R3(this, "events"), R3(this, "namespace"), R3(this, "chainId"), this.namespace = t.namespace, this.events = h3("events"), this.client = h3("client"), this.chainId = this.getDefaultChain(), this.httpProviders = this.createHttpProviders();
  }
  updateNamespace(t) {
    this.namespace.chains = [...new Set((this.namespace.chains || []).concat(t.chains || []))], this.namespace.accounts = [...new Set((this.namespace.accounts || []).concat(t.accounts || []))], this.namespace.methods = [...new Set((this.namespace.methods || []).concat(t.methods || []))], this.namespace.events = [...new Set((this.namespace.events || []).concat(t.events || []))], this.httpProviders = this.createHttpProviders();
  }
  requestAccounts() {
    return this.getAccounts();
  }
  request(t) {
    return this.namespace.methods.includes(t.request.method) ? this.client.request(t) : this.getHttpProvider(t.chainId).request(t.request);
  }
  setDefaultChain(t, e) {
    this.httpProviders[t] || this.setHttpProvider(t, e), this.chainId = t, this.events.emit(u2.DEFAULT_CHAIN_CHANGED, `${this.name}:${t}`);
  }
  getDefaultChain() {
    if (this.chainId) return this.chainId;
    if (this.namespace.defaultChain) return this.namespace.defaultChain;
    const t = this.namespace.chains[0];
    if (!t) throw new Error("ChainId not found");
    return t.split(":")[1];
  }
  getAccounts() {
    const t = this.namespace.accounts;
    return t ? [...new Set(t.filter((e) => e.split(":")[1] === this.chainId.toString()).map((e) => e.split(":")[2]))] : [];
  }
  createHttpProviders() {
    var t, e;
    const i3 = {};
    return (e = (t = this.namespace) == null ? void 0 : t.accounts) == null || e.forEach((n2) => {
      const a2 = Ne(n2);
      i3[`${a2.namespace}:${a2.reference}`] = this.createHttpProvider(n2);
    }), i3;
  }
  getHttpProvider(t) {
    const e = this.httpProviders[t];
    if (typeof e > "u") throw new Error(`JSON-RPC provider for ${t} not found`);
    return e;
  }
  setHttpProvider(t, e) {
    const i3 = this.createHttpProvider(t, e);
    i3 && (this.httpProviders[t] = i3);
  }
  createHttpProvider(t, e) {
    const i3 = e || d2(t, this.namespace, this.client.core.projectId);
    if (!i3) throw new Error(`No RPC url provided for chainId: ${t}`);
    return new o(new f2(i3, h3("disableProviderPing")));
  }
};
var Ye3 = Object.defineProperty;
var Qe4 = Object.defineProperties;
var Ze4 = Object.getOwnPropertyDescriptors;
var It3 = Object.getOwnPropertySymbols;
var Te2 = Object.prototype.hasOwnProperty;
var ts2 = Object.prototype.propertyIsEnumerable;
var Y3 = (s, t, e) => t in s ? Ye3(s, t, { enumerable: true, configurable: true, writable: true, value: e }) : s[t] = e;
var x = (s, t) => {
  for (var e in t || (t = {})) Te2.call(t, e) && Y3(s, e, t[e]);
  if (It3) for (var e of It3(t)) ts2.call(t, e) && Y3(s, e, t[e]);
  return s;
};
var Q2 = (s, t) => Qe4(s, Ze4(t));
var l2 = (s, t, e) => Y3(s, typeof t != "symbol" ? t + "" : t, e);
var B2 = class _B {
  constructor(t) {
    l2(this, "client"), l2(this, "namespaces"), l2(this, "optionalNamespaces"), l2(this, "sessionProperties"), l2(this, "scopedProperties"), l2(this, "events", new import_events5.default()), l2(this, "rpcProviders", {}), l2(this, "session"), l2(this, "providerOpts"), l2(this, "logger"), l2(this, "uri"), l2(this, "disableProviderPing", false), this.providerOpts = t, this.logger = typeof (t == null ? void 0 : t.logger) < "u" && typeof (t == null ? void 0 : t.logger) != "string" ? t.logger : (0, import_pino.default)(k({ level: (t == null ? void 0 : t.logger) || et2 })), this.disableProviderPing = (t == null ? void 0 : t.disableProviderPing) || false;
  }
  static async init(t) {
    const e = new _B(t);
    return await e.initialize(), e;
  }
  async request(t, e, i3) {
    const [n2, a2] = this.validateChain(e);
    if (!this.session) throw new Error("Please call connect() before request()");
    return await this.getProvider(n2).request({ request: x({}, t), chainId: `${n2}:${a2}`, topic: this.session.topic, expiry: i3 });
  }
  sendAsync(t, e, i3, n2) {
    const a2 = (/* @__PURE__ */ new Date()).getTime();
    this.request(t, i3, n2).then((r2) => e(null, formatJsonRpcResult(a2, r2))).catch((r2) => e(r2, void 0));
  }
  async enable() {
    if (!this.client) throw new Error("Sign Client not initialized");
    return this.session || await this.connect({ namespaces: this.namespaces, optionalNamespaces: this.optionalNamespaces, sessionProperties: this.sessionProperties, scopedProperties: this.scopedProperties }), await this.requestAccounts();
  }
  async disconnect() {
    var t;
    if (!this.session) throw new Error("Please call connect() before enable()");
    await this.client.disconnect({ topic: (t = this.session) == null ? void 0 : t.topic, reason: Nt("USER_DISCONNECTED") }), await this.cleanup();
  }
  async connect(t) {
    if (!this.client) throw new Error("Sign Client not initialized");
    if (this.setNamespaces(t), await this.cleanupPendingPairings(), !t.skipPairing) return await this.pair(t.pairingTopic);
  }
  async authenticate(t, e) {
    if (!this.client) throw new Error("Sign Client not initialized");
    this.setNamespaces(t), await this.cleanupPendingPairings();
    const { uri: i3, response: n2 } = await this.client.authenticate(t, e);
    i3 && (this.uri = i3, this.events.emit("display_uri", i3));
    const a2 = await n2();
    if (this.session = a2.session, this.session) {
      const r2 = gt3(this.session.namespaces);
      this.namespaces = M3(this.namespaces, r2), await this.persist("namespaces", this.namespaces), this.onConnect();
    }
    return a2;
  }
  on(t, e) {
    this.events.on(t, e);
  }
  once(t, e) {
    this.events.once(t, e);
  }
  removeListener(t, e) {
    this.events.removeListener(t, e);
  }
  off(t, e) {
    this.events.off(t, e);
  }
  get isWalletConnect() {
    return true;
  }
  async pair(t) {
    const { uri: e, approval: i3 } = await this.client.connect({ pairingTopic: t, requiredNamespaces: this.namespaces, optionalNamespaces: this.optionalNamespaces, sessionProperties: this.sessionProperties, scopedProperties: this.scopedProperties });
    e && (this.uri = e, this.events.emit("display_uri", e));
    const n2 = await i3();
    this.session = n2;
    const a2 = gt3(n2.namespaces);
    return this.namespaces = M3(this.namespaces, a2), await this.persist("namespaces", this.namespaces), await this.persist("optionalNamespaces", this.optionalNamespaces), this.onConnect(), this.session;
  }
  setDefaultChain(t, e) {
    try {
      if (!this.session) return;
      const [i3, n2] = this.validateChain(t), a2 = this.getProvider(i3);
      a2.name === I2 ? a2.setDefaultChain(`${i3}:${n2}`, e) : a2.setDefaultChain(n2, e);
    } catch (i3) {
      if (!/Please call connect/.test(i3.message)) throw i3;
    }
  }
  async cleanupPendingPairings(t = {}) {
    this.logger.info("Cleaning up inactive pairings...");
    const e = this.client.pairing.getAll();
    if (se(e)) {
      for (const i3 of e) t.deletePairings ? this.client.core.expirer.set(i3.topic, 0) : await this.client.core.relayer.subscriber.unsubscribe(i3.topic);
      this.logger.info(`Inactive pairings cleared: ${e.length}`);
    }
  }
  abortPairingAttempt() {
    this.logger.warn("abortPairingAttempt is deprecated. This is now a no-op.");
  }
  async checkStorage() {
    this.namespaces = await this.getFromStore("namespaces") || {}, this.optionalNamespaces = await this.getFromStore("optionalNamespaces") || {}, this.session && this.createProviders();
  }
  async initialize() {
    this.logger.trace("Initialized"), await this.createClient(), await this.checkStorage(), this.registerEventListeners();
  }
  async createClient() {
    var t, e;
    if (this.client = this.providerOpts.client || await Ee3.init({ core: this.providerOpts.core, logger: this.providerOpts.logger || et2, relayUrl: this.providerOpts.relayUrl || St4, projectId: this.providerOpts.projectId, metadata: this.providerOpts.metadata, storageOptions: this.providerOpts.storageOptions, storage: this.providerOpts.storage, name: this.providerOpts.name, customStoragePrefix: this.providerOpts.customStoragePrefix, telemetryEnabled: this.providerOpts.telemetryEnabled }), this.providerOpts.session) try {
      this.session = this.client.session.get(this.providerOpts.session.topic);
    } catch (i3) {
      throw this.logger.error("Failed to get session", i3), new Error(`The provided session: ${(e = (t = this.providerOpts) == null ? void 0 : t.session) == null ? void 0 : e.topic} doesn't exist in the Sign client`);
    }
    else {
      const i3 = this.client.session.getAll();
      this.session = i3[0];
    }
    this.logger.trace("SignClient Initialized");
  }
  createProviders() {
    if (!this.client) throw new Error("Sign Client not initialized");
    if (!this.session) throw new Error("Session not initialized. Please call connect() before enable()");
    const t = [...new Set(Object.keys(this.session.namespaces).map((e) => yo(e)))];
    V4("client", this.client), V4("events", this.events), V4("disableProviderPing", this.disableProviderPing), t.forEach((e) => {
      if (!this.session) return;
      const i3 = le3(e, this.session), n2 = ft3(i3), a2 = M3(this.namespaces, this.optionalNamespaces), r2 = Q2(x({}, a2[e]), { accounts: i3, chains: n2 });
      switch (e) {
        case "eip155":
          this.rpcProviders[e] = new Ie3({ namespace: r2 });
          break;
        case "algorand":
          this.rpcProviders[e] = new De3({ namespace: r2 });
          break;
        case "solana":
          this.rpcProviders[e] = new Ae2({ namespace: r2 });
          break;
        case "cosmos":
          this.rpcProviders[e] = new Ee4({ namespace: r2 });
          break;
        case "polkadot":
          this.rpcProviders[e] = new ve2({ namespace: r2 });
          break;
        case "cip34":
          this.rpcProviders[e] = new Re({ namespace: r2 });
          break;
        case "elrond":
          this.rpcProviders[e] = new Fe3({ namespace: r2 });
          break;
        case "multiversx":
          this.rpcProviders[e] = new xe2({ namespace: r2 });
          break;
        case "near":
          this.rpcProviders[e] = new Je3({ namespace: r2 });
          break;
        case "tezos":
          this.rpcProviders[e] = new We4({ namespace: r2 });
          break;
        default:
          this.rpcProviders[I2] ? this.rpcProviders[I2].updateNamespace(r2) : this.rpcProviders[I2] = new Xe3({ namespace: r2 });
      }
    });
  }
  registerEventListeners() {
    if (typeof this.client > "u") throw new Error("Sign Client is not initialized");
    this.client.on("session_ping", (t) => {
      var e;
      const { topic: i3 } = t;
      i3 === ((e = this.session) == null ? void 0 : e.topic) && this.events.emit("session_ping", t);
    }), this.client.on("session_event", (t) => {
      var e;
      const { params: i3, topic: n2 } = t;
      if (n2 !== ((e = this.session) == null ? void 0 : e.topic)) return;
      const { event: a2 } = i3;
      if (a2.name === "accountsChanged") {
        const r2 = a2.data;
        r2 && se(r2) && this.events.emit("accountsChanged", r2.map(vt));
      } else if (a2.name === "chainChanged") {
        const r2 = i3.chainId, c3 = i3.event.data, o2 = yo(r2), m2 = K2(r2) !== K2(c3) ? `${o2}:${K2(c3)}` : r2;
        this.onChainChanged(m2);
      } else this.events.emit(a2.name, a2.data);
      this.events.emit("session_event", t);
    }), this.client.on("session_update", ({ topic: t, params: e }) => {
      var i3, n2;
      if (t !== ((i3 = this.session) == null ? void 0 : i3.topic)) return;
      const { namespaces: a2 } = e, r2 = (n2 = this.client) == null ? void 0 : n2.session.get(t);
      this.session = Q2(x({}, r2), { namespaces: a2 }), this.onSessionUpdate(), this.events.emit("session_update", { topic: t, params: e });
    }), this.client.on("session_delete", async (t) => {
      var e;
      t.topic === ((e = this.session) == null ? void 0 : e.topic) && (await this.cleanup(), this.events.emit("session_delete", t), this.events.emit("disconnect", Q2(x({}, Nt("USER_DISCONNECTED")), { data: t.topic })));
    }), this.on(u2.DEFAULT_CHAIN_CHANGED, (t) => {
      this.onChainChanged(t, true);
    });
  }
  getProvider(t) {
    return this.rpcProviders[t] || this.rpcProviders[I2];
  }
  onSessionUpdate() {
    Object.keys(this.rpcProviders).forEach((t) => {
      var e;
      this.getProvider(t).updateNamespace((e = this.session) == null ? void 0 : e.namespaces[t]);
    });
  }
  setNamespaces(t) {
    const { namespaces: e = {}, optionalNamespaces: i3 = {}, sessionProperties: n2, scopedProperties: a2 } = t;
    this.optionalNamespaces = M3(e, i3), this.sessionProperties = n2, this.scopedProperties = a2;
  }
  validateChain(t) {
    const [e, i3] = (t == null ? void 0 : t.split(":")) || ["", ""];
    if (!this.namespaces || !Object.keys(this.namespaces).length) return [e, i3];
    if (e && !Object.keys(this.namespaces || {}).map((r2) => yo(r2)).includes(e)) throw new Error(`Namespace '${e}' is not configured. Please call connect() first with namespace config.`);
    if (e && i3) return [e, i3];
    const n2 = yo(Object.keys(this.namespaces)[0]), a2 = this.rpcProviders[n2].getDefaultChain();
    return [n2, a2];
  }
  async requestAccounts() {
    const [t] = this.validateChain();
    return await this.getProvider(t).requestAccounts();
  }
  async onChainChanged(t, e = false) {
    if (!this.namespaces) return;
    const [i3, n2] = this.validateChain(t);
    if (!n2) return;
    this.updateNamespaceChain(i3, n2), this.events.emit("chainChanged", n2);
    const a2 = this.getProvider(i3).getDefaultChain();
    e || this.getProvider(i3).setDefaultChain(n2), this.emitAccountsChangedOnChainChange({ namespace: i3, previousChainId: a2, newChainId: t }), await this.persist("namespaces", this.namespaces);
  }
  emitAccountsChangedOnChainChange({ namespace: t, previousChainId: e, newChainId: i3 }) {
    var n2, a2;
    try {
      if (e === i3) return;
      const r2 = (a2 = (n2 = this.session) == null ? void 0 : n2.namespaces[t]) == null ? void 0 : a2.accounts;
      if (!r2) return;
      const c3 = r2.filter((o2) => o2.includes(`${i3}:`)).map(vt);
      if (!se(c3)) return;
      this.events.emit("accountsChanged", c3);
    } catch (r2) {
      this.logger.warn("Failed to emit accountsChanged on chain change", r2);
    }
  }
  updateNamespaceChain(t, e) {
    if (!this.namespaces) return;
    const i3 = this.namespaces[t] ? t : `${t}:${e}`, n2 = { chains: [], methods: [], events: [], defaultChain: e };
    this.namespaces[i3] ? this.namespaces[i3] && (this.namespaces[i3].defaultChain = e) : this.namespaces[i3] = n2;
  }
  onConnect() {
    this.createProviders(), this.events.emit("connect", { session: this.session });
  }
  async cleanup() {
    this.namespaces = void 0, this.optionalNamespaces = void 0, this.sessionProperties = void 0, await this.deleteFromStore("namespaces"), await this.deleteFromStore("optionalNamespaces"), await this.deleteFromStore("sessionProperties"), this.session = void 0, await this.cleanupPendingPairings({ deletePairings: true }), await this.cleanupStorage();
  }
  async persist(t, e) {
    var i3;
    const n2 = ((i3 = this.session) == null ? void 0 : i3.topic) || "";
    await this.client.core.storage.setItem(`${U3}/${t}${n2}`, e);
  }
  async getFromStore(t) {
    var e;
    const i3 = ((e = this.session) == null ? void 0 : e.topic) || "";
    return await this.client.core.storage.getItem(`${U3}/${t}${i3}`);
  }
  async deleteFromStore(t) {
    var e;
    const i3 = ((e = this.session) == null ? void 0 : e.topic) || "";
    await this.client.core.storage.removeItem(`${U3}/${t}${i3}`);
  }
  async cleanupStorage() {
    var t;
    try {
      if (((t = this.client) == null ? void 0 : t.session.length) > 0) return;
      const e = await this.client.core.storage.getKeys();
      for (const i3 of e) i3.startsWith(U3) && await this.client.core.storage.removeItem(i3);
    } catch (e) {
      this.logger.warn("Failed to cleanup storage", e);
    }
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/ConnectorControllerUtil.js
function checkNamespaceConnectorId(namespace, connectorId) {
  return ConnectorController.getConnectorId(namespace) === connectorId;
}

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/ChainControllerUtil.js
function getChainsToDisconnect(namespace) {
  const namespaces = Array.from(ChainController.state.chains.keys());
  let chains = [];
  if (namespace) {
    chains.push([namespace, ChainController.state.chains.get(namespace)]);
    if (checkNamespaceConnectorId(namespace, ConstantsUtil.CONNECTOR_ID.WALLET_CONNECT)) {
      namespaces.forEach((ns2) => {
        if (ns2 !== namespace && checkNamespaceConnectorId(ns2, ConstantsUtil.CONNECTOR_ID.WALLET_CONNECT)) {
          chains.push([ns2, ChainController.state.chains.get(ns2)]);
        }
      });
    } else if (checkNamespaceConnectorId(namespace, ConstantsUtil.CONNECTOR_ID.AUTH)) {
      namespaces.forEach((ns2) => {
        if (ns2 !== namespace && checkNamespaceConnectorId(ns2, ConstantsUtil.CONNECTOR_ID.AUTH)) {
          chains.push([ns2, ChainController.state.chains.get(ns2)]);
        }
      });
    }
  } else {
    chains = Array.from(ChainController.state.chains.entries());
  }
  return chains;
}

// node_modules/@reown/appkit/dist/esm/src/utils/ConstantsUtil.js
var WcConstantsUtil = {
  ERROR_CODE_UNRECOGNIZED_CHAIN_ID: 4902,
  ERROR_CODE_DEFAULT: 5e3,
  ERROR_INVALID_CHAIN_ID: 32603,
  DEFAULT_ALLOWED_ANCESTORS: [
    "http://localhost:*",
    "https://*.pages.dev",
    "https://*.vercel.app",
    "https://*.ngrok-free.app",
    "https://secure-mobile.walletconnect.com",
    "https://secure-mobile.walletconnect.org"
  ]
};

// node_modules/@reown/appkit/dist/esm/src/networks/utils.js
function defineChain2(chain) {
  return {
    formatters: void 0,
    fees: void 0,
    serializers: void 0,
    ...chain
  };
}

// node_modules/@reown/appkit/dist/esm/src/networks/solana/solana.js
var solana = defineChain2({
  id: "5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp",
  name: "Solana",
  network: "solana-mainnet",
  nativeCurrency: { name: "Solana", symbol: "SOL", decimals: 9 },
  rpcUrls: {
    default: { http: ["https://rpc.walletconnect.org/v1"] }
  },
  blockExplorers: { default: { name: "Solscan", url: "https://solscan.io" } },
  testnet: false,
  chainNamespace: "solana",
  caipNetworkId: "solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp",
  deprecatedCaipNetworkId: "solana:4sGjMW1sUnHzSxGspuhpqLDx6wiyjNtZ"
});

// node_modules/@reown/appkit/dist/esm/src/networks/solana/solanaDevnet.js
var solanaDevnet = defineChain2({
  id: "EtWTRABZaYq6iMfeYKouRu166VU2xqa1",
  name: "Solana Devnet",
  network: "solana-devnet",
  nativeCurrency: { name: "Solana", symbol: "SOL", decimals: 9 },
  rpcUrls: {
    default: { http: ["https://rpc.walletconnect.org/v1"] }
  },
  blockExplorers: { default: { name: "Solscan", url: "https://solscan.io" } },
  testnet: true,
  chainNamespace: "solana",
  caipNetworkId: "solana:EtWTRABZaYq6iMfeYKouRu166VU2xqa1",
  deprecatedCaipNetworkId: "solana:8E9rvCKLFQia2Y35HXjjpWzj8weVo44K"
});

// node_modules/@reown/appkit/dist/esm/src/networks/solana/solanaTestnet.js
var solanaTestnet = defineChain2({
  id: "4uhcVJyU9pJkvQyS88uRDiswHXSCkY3z",
  name: "Solana Testnet",
  network: "solana-testnet",
  nativeCurrency: { name: "Solana", symbol: "SOL", decimals: 9 },
  rpcUrls: {
    default: { http: ["https://rpc.walletconnect.org/v1"] }
  },
  blockExplorers: { default: { name: "Solscan", url: "https://solscan.io" } },
  testnet: true,
  chainNamespace: "solana",
  caipNetworkId: "solana:4uhcVJyU9pJkvQyS88uRDiswHXSCkY3z"
});

// node_modules/@reown/appkit/dist/esm/src/networks/bitcoin.js
var bitcoin = defineChain2({
  id: "000000000019d6689c085ae165831e93",
  caipNetworkId: "bip122:000000000019d6689c085ae165831e93",
  chainNamespace: "bip122",
  name: "Bitcoin",
  nativeCurrency: {
    name: "Bitcoin",
    symbol: "BTC",
    decimals: 8
  },
  rpcUrls: {
    default: { http: ["https://rpc.walletconnect.org/v1"] }
  }
});
var bitcoinTestnet = defineChain2({
  id: "000000000933ea01ad0ee984209779ba",
  caipNetworkId: "bip122:000000000933ea01ad0ee984209779ba",
  chainNamespace: "bip122",
  name: "Bitcoin Testnet",
  nativeCurrency: {
    name: "Bitcoin",
    symbol: "BTC",
    decimals: 8
  },
  rpcUrls: {
    default: { http: ["https://rpc.walletconnect.org/v1"] }
  },
  testnet: true
});

// node_modules/@reown/appkit/dist/esm/src/utils/HelpersUtil.js
var DEFAULT_METHODS = {
  solana: [
    "solana_signMessage",
    "solana_signTransaction",
    "solana_requestAccounts",
    "solana_getAccounts",
    "solana_signAllTransactions",
    "solana_signAndSendTransaction"
  ],
  eip155: [
    "eth_accounts",
    "eth_requestAccounts",
    "eth_sendRawTransaction",
    "eth_sign",
    "eth_signTransaction",
    "eth_signTypedData",
    "eth_signTypedData_v3",
    "eth_signTypedData_v4",
    "eth_sendTransaction",
    "personal_sign",
    "wallet_switchEthereumChain",
    "wallet_addEthereumChain",
    "wallet_getPermissions",
    "wallet_requestPermissions",
    "wallet_registerOnboarding",
    "wallet_watchAsset",
    "wallet_scanQRCode",
    // EIP-5792
    "wallet_getCallsStatus",
    "wallet_showCallsStatus",
    "wallet_sendCalls",
    "wallet_getCapabilities",
    // EIP-7715
    "wallet_grantPermissions",
    "wallet_revokePermissions",
    //EIP-7811
    "wallet_getAssets"
  ],
  bip122: ["sendTransfer", "signMessage", "signPsbt", "getAccountAddresses"]
};
var WcHelpersUtil = {
  getMethodsByChainNamespace(chainNamespace) {
    return DEFAULT_METHODS[chainNamespace] || [];
  },
  createDefaultNamespace(chainNamespace) {
    return {
      methods: this.getMethodsByChainNamespace(chainNamespace),
      events: ["accountsChanged", "chainChanged"],
      chains: [],
      rpcMap: {}
    };
  },
  applyNamespaceOverrides(baseNamespaces, overrides) {
    if (!overrides) {
      return { ...baseNamespaces };
    }
    const result = { ...baseNamespaces };
    const namespacesToOverride = /* @__PURE__ */ new Set();
    if (overrides.methods) {
      Object.keys(overrides.methods).forEach((ns2) => namespacesToOverride.add(ns2));
    }
    if (overrides.chains) {
      Object.keys(overrides.chains).forEach((ns2) => namespacesToOverride.add(ns2));
    }
    if (overrides.events) {
      Object.keys(overrides.events).forEach((ns2) => namespacesToOverride.add(ns2));
    }
    if (overrides.rpcMap) {
      Object.keys(overrides.rpcMap).forEach((chainId) => {
        const [ns2] = chainId.split(":");
        if (ns2) {
          namespacesToOverride.add(ns2);
        }
      });
    }
    namespacesToOverride.forEach((ns2) => {
      if (!result[ns2]) {
        result[ns2] = this.createDefaultNamespace(ns2);
      }
    });
    if (overrides.methods) {
      Object.entries(overrides.methods).forEach(([ns2, methods]) => {
        if (result[ns2]) {
          result[ns2].methods = methods;
        }
      });
    }
    if (overrides.chains) {
      Object.entries(overrides.chains).forEach(([ns2, chains]) => {
        if (result[ns2]) {
          result[ns2].chains = chains;
        }
      });
    }
    if (overrides.events) {
      Object.entries(overrides.events).forEach(([ns2, events]) => {
        if (result[ns2]) {
          result[ns2].events = events;
        }
      });
    }
    if (overrides.rpcMap) {
      const processedNamespaces = /* @__PURE__ */ new Set();
      Object.entries(overrides.rpcMap).forEach(([chainId, rpcUrl]) => {
        const [ns2, id] = chainId.split(":");
        if (!ns2 || !id || !result[ns2]) {
          return;
        }
        if (!result[ns2].rpcMap) {
          result[ns2].rpcMap = {};
        }
        if (!processedNamespaces.has(ns2)) {
          result[ns2].rpcMap = {};
          processedNamespaces.add(ns2);
        }
        result[ns2].rpcMap[id] = rpcUrl;
      });
    }
    return result;
  },
  createNamespaces(caipNetworks, configOverride) {
    const defaultNamespaces = caipNetworks.reduce((acc, chain) => {
      const { id, chainNamespace, rpcUrls } = chain;
      const rpcUrl = rpcUrls.default.http[0];
      if (!acc[chainNamespace]) {
        acc[chainNamespace] = this.createDefaultNamespace(chainNamespace);
      }
      const caipNetworkId = `${chainNamespace}:${id}`;
      const namespace = acc[chainNamespace];
      namespace.chains.push(caipNetworkId);
      switch (caipNetworkId) {
        case solana.caipNetworkId:
          namespace.chains.push(solana.deprecatedCaipNetworkId);
          break;
        case solanaDevnet.caipNetworkId:
          namespace.chains.push(solanaDevnet.deprecatedCaipNetworkId);
          break;
        default:
      }
      if ((namespace == null ? void 0 : namespace.rpcMap) && rpcUrl) {
        namespace.rpcMap[id] = rpcUrl;
      }
      return acc;
    }, {});
    return this.applyNamespaceOverrides(defaultNamespaces, configOverride);
  },
  resolveReownName: async (name) => {
    var _a;
    const wcNameAddress = await EnsController.resolveName(name);
    const networkNameAddresses = Object.values(wcNameAddress == null ? void 0 : wcNameAddress.addresses) || [];
    return ((_a = networkNameAddresses[0]) == null ? void 0 : _a.address) || false;
  },
  getChainsFromNamespaces(namespaces = {}) {
    return Object.values(namespaces).flatMap((namespace) => {
      const chains = namespace.chains || [];
      const accountsChains = namespace.accounts.map((account) => {
        const [chainNamespace, chainId] = account.split(":");
        return `${chainNamespace}:${chainId}`;
      });
      return Array.from(/* @__PURE__ */ new Set([...chains, ...accountsChains]));
    });
  },
  isSessionEventData(data) {
    return typeof data === "object" && data !== null && "id" in data && "topic" in data && "params" in data && typeof data.params === "object" && data.params !== null && "chainId" in data.params && "event" in data.params && typeof data.params.event === "object" && data.params.event !== null;
  },
  isOriginAllowed(currentOrigin, allowedPatterns, defaultAllowedOrigins) {
    for (const pattern of [...allowedPatterns, ...defaultAllowedOrigins]) {
      if (pattern.includes("*")) {
        const escapedPattern = pattern.replace(/[.*+?^${}()|[\]\\]/gu, "\\$&");
        const regexString = `^${escapedPattern.replace(/\\\*/gu, ".*")}$`;
        const regex = new RegExp(regexString, "u");
        if (regex.test(currentOrigin)) {
          return true;
        }
      } else {
        try {
          if (new URL(pattern).origin === currentOrigin) {
            return true;
          }
        } catch (e) {
          if (pattern === currentOrigin) {
            return true;
          }
        }
      }
    }
    return false;
  }
};

// node_modules/@reown/appkit/dist/esm/src/connectors/WalletConnectConnector.js
var WalletConnectConnector = class {
  constructor({ provider, namespace }) {
    this.id = ConstantsUtil.CONNECTOR_ID.WALLET_CONNECT;
    this.name = PresetsUtil.ConnectorNamesMap[ConstantsUtil.CONNECTOR_ID.WALLET_CONNECT];
    this.type = "WALLET_CONNECT";
    this.imageId = PresetsUtil.ConnectorImageIds[ConstantsUtil.CONNECTOR_ID.WALLET_CONNECT];
    this.getCaipNetworks = ChainController.getCaipNetworks.bind(ChainController);
    this.caipNetworks = this.getCaipNetworks();
    this.provider = provider;
    this.chain = namespace;
  }
  get chains() {
    return this.getCaipNetworks();
  }
  async connectWalletConnect() {
    const isAuthenticated = await this.authenticate();
    if (!isAuthenticated) {
      const caipNetworks = this.getCaipNetworks();
      const universalProviderConfigOverride = OptionsController.state.universalProviderConfigOverride;
      const namespaces = WcHelpersUtil.createNamespaces(caipNetworks, universalProviderConfigOverride);
      await this.provider.connect({ optionalNamespaces: namespaces });
    }
    return {
      clientId: await this.provider.client.core.crypto.getClientId(),
      session: this.provider.session
    };
  }
  async disconnect() {
    await this.provider.disconnect();
  }
  async authenticate() {
    const chains = this.chains.map((network) => network.caipNetworkId);
    return SIWXUtil.universalProviderAuthenticate({
      universalProvider: this.provider,
      chains,
      methods: OPTIONAL_METHODS
    });
  }
};
var OPTIONAL_METHODS = [
  "eth_accounts",
  "eth_requestAccounts",
  "eth_sendRawTransaction",
  "eth_sign",
  "eth_signTransaction",
  "eth_signTypedData",
  "eth_signTypedData_v3",
  "eth_signTypedData_v4",
  "eth_sendTransaction",
  "personal_sign",
  "wallet_switchEthereumChain",
  "wallet_addEthereumChain",
  "wallet_getPermissions",
  "wallet_requestPermissions",
  "wallet_registerOnboarding",
  "wallet_watchAsset",
  "wallet_scanQRCode",
  // EIP-5792
  "wallet_getCallsStatus",
  "wallet_sendCalls",
  "wallet_getCapabilities",
  // EIP-7715
  "wallet_grantPermissions",
  "wallet_revokePermissions",
  //EIP-7811
  "wallet_getAssets"
];

// node_modules/@reown/appkit/dist/esm/src/adapters/ChainAdapterBlueprint.js
var AdapterBlueprint = class {
  /**
   * Creates an instance of AdapterBlueprint.
   * @param {AdapterBlueprint.Params} params - The parameters for initializing the adapter
   */
  constructor(params) {
    this.availableConnectors = [];
    this.eventListeners = /* @__PURE__ */ new Map();
    this.getCaipNetworks = (namespace) => ChainController.getCaipNetworks(namespace);
    if (params) {
      this.construct(params);
    }
  }
  /**
   * Initializes the adapter with the given parameters.
   * @param {AdapterBlueprint.Params} params - The parameters for initializing the adapter
   */
  construct(params) {
    this.projectId = params.projectId;
    this.namespace = params.namespace;
    this.adapterType = params.adapterType;
  }
  /**
   * Gets the available connectors.
   * @returns {Connector[]} An array of available connectors
   */
  get connectors() {
    return this.availableConnectors;
  }
  /**
   * Gets the supported networks.
   * @returns {CaipNetwork[]} An array of supported networks
   */
  get networks() {
    return this.getCaipNetworks(this.namespace);
  }
  /**
   * Sets the auth provider.
   * @param {W3mFrameProvider} authProvider - The auth provider instance
   */
  setAuthProvider(authProvider) {
    this.addConnector({
      id: ConstantsUtil.CONNECTOR_ID.AUTH,
      type: "AUTH",
      name: ConstantsUtil.CONNECTOR_NAMES.AUTH,
      provider: authProvider,
      imageId: PresetsUtil.ConnectorImageIds[ConstantsUtil.CONNECTOR_ID.AUTH],
      chain: this.namespace,
      chains: []
    });
  }
  /**
   * Adds one or more connectors to the available connectors list.
   * @param {...Connector} connectors - The connectors to add
   */
  addConnector(...connectors) {
    const connectorsAdded = /* @__PURE__ */ new Set();
    this.availableConnectors = [...connectors, ...this.availableConnectors].filter((connector) => {
      if (connectorsAdded.has(connector.id)) {
        return false;
      }
      connectorsAdded.add(connector.id);
      return true;
    });
    this.emit("connectors", this.availableConnectors);
  }
  setStatus(status, chainNamespace) {
    AccountController.setStatus(status, chainNamespace);
  }
  /**
   * Adds an event listener for a specific event.
   * @template T
   * @param {T} eventName - The name of the event
   * @param {EventCallback<T>} callback - The callback function to be called when the event is emitted
   */
  on(eventName, callback) {
    var _a;
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, /* @__PURE__ */ new Set());
    }
    (_a = this.eventListeners.get(eventName)) == null ? void 0 : _a.add(callback);
  }
  /**
   * Removes an event listener for a specific event.
   * @template T
   * @param {T} eventName - The name of the event
   * @param {EventCallback<T>} callback - The callback function to be removed
   */
  off(eventName, callback) {
    const listeners = this.eventListeners.get(eventName);
    if (listeners) {
      listeners.delete(callback);
    }
  }
  /**
   * Removes all event listeners.
   */
  removeAllEventListeners() {
    this.eventListeners.forEach((listeners) => {
      listeners.clear();
    });
  }
  /**
   * Emits an event with the given name and optional data.
   * @template T
   * @param {T} eventName - The name of the event to emit
   * @param {EventData[T]} [data] - The optional data to be passed to the event listeners
   */
  emit(eventName, data) {
    const listeners = this.eventListeners.get(eventName);
    if (listeners) {
      listeners.forEach((callback) => callback(data));
    }
  }
  /**
   * Connects to WalletConnect.
   * @param {number | string} [_chainId] - Optional chain ID to connect to
   */
  async connectWalletConnect(_chainId) {
    const connector = this.getWalletConnectConnector();
    const result = await connector.connectWalletConnect();
    return { clientId: result.clientId };
  }
  /**
   * Switches the network.
   * @param {AdapterBlueprint.SwitchNetworkParams} params - Network switching parameters
   */
  async switchNetwork(params) {
    var _a;
    const { caipNetwork, providerType } = params;
    if (!params.provider) {
      return;
    }
    const provider = "provider" in params.provider ? params.provider.provider : params.provider;
    if (providerType === "WALLET_CONNECT") {
      ;
      provider.setDefaultChain(caipNetwork.caipNetworkId);
      return;
    }
    if (provider && providerType === "AUTH") {
      const authProvider = provider;
      const preferredAccountType = (_a = AccountController.state.preferredAccountTypes) == null ? void 0 : _a[caipNetwork.chainNamespace];
      await authProvider.switchNetwork(caipNetwork.caipNetworkId);
      const user = await authProvider.getUser({
        chainId: caipNetwork.caipNetworkId,
        preferredAccountType
      });
      this.emit("switchNetwork", user);
    }
  }
  getWalletConnectConnector() {
    const connector = this.connectors.find((c3) => c3 instanceof WalletConnectConnector);
    if (!connector) {
      throw new Error("WalletConnectConnector not found");
    }
    return connector;
  }
};

// node_modules/@reown/appkit/dist/esm/src/universal-adapter/client.js
var UniversalAdapter = class extends AdapterBlueprint {
  setUniversalProvider(universalProvider) {
    this.addConnector(new WalletConnectConnector({
      provider: universalProvider,
      caipNetworks: this.getCaipNetworks(),
      namespace: this.namespace
    }));
  }
  async connect(params) {
    return Promise.resolve({
      id: "WALLET_CONNECT",
      type: "WALLET_CONNECT",
      chainId: Number(params.chainId),
      provider: this.provider,
      address: ""
    });
  }
  async disconnect() {
    try {
      const connector = this.getWalletConnectConnector();
      await connector.disconnect();
    } catch (error) {
      console.warn("UniversalAdapter:disconnect - error", error);
    }
  }
  async getAccounts({ namespace }) {
    var _a, _b, _c2, _d;
    const provider = this.provider;
    const addresses = ((_d = (_c2 = (_b = (_a = provider == null ? void 0 : provider.session) == null ? void 0 : _a.namespaces) == null ? void 0 : _b[namespace]) == null ? void 0 : _c2.accounts) == null ? void 0 : _d.map((account) => {
      const [, , address] = account.split(":");
      return address;
    }).filter((address, index, self) => self.indexOf(address) === index)) || [];
    return Promise.resolve({
      accounts: addresses.map((address) => CoreHelperUtil.createAccount(namespace, address, namespace === "bip122" ? "payment" : "eoa"))
    });
  }
  async syncConnectors() {
    return Promise.resolve();
  }
  async getBalance(params) {
    var _a, _b, _c2, _d, _e5;
    const isBalanceSupported = params.caipNetwork && ConstantsUtil2.BALANCE_SUPPORTED_CHAINS.includes((_a = params.caipNetwork) == null ? void 0 : _a.chainNamespace);
    if (!isBalanceSupported || ((_b = params.caipNetwork) == null ? void 0 : _b.testnet)) {
      return {
        balance: "0.00",
        symbol: ((_c2 = params.caipNetwork) == null ? void 0 : _c2.nativeCurrency.symbol) || ""
      };
    }
    if (AccountController.state.balanceLoading && params.chainId === ((_d = ChainController.state.activeCaipNetwork) == null ? void 0 : _d.id)) {
      return {
        balance: AccountController.state.balance || "0.00",
        symbol: AccountController.state.balanceSymbol || ""
      };
    }
    const balances = await AccountController.fetchTokenBalance();
    const balance = balances.find((b4) => {
      var _a2, _b2;
      return b4.chainId === `${(_a2 = params.caipNetwork) == null ? void 0 : _a2.chainNamespace}:${params.chainId}` && b4.symbol === ((_b2 = params.caipNetwork) == null ? void 0 : _b2.nativeCurrency.symbol);
    });
    return {
      balance: (balance == null ? void 0 : balance.quantity.numeric) || "0.00",
      symbol: (balance == null ? void 0 : balance.symbol) || ((_e5 = params.caipNetwork) == null ? void 0 : _e5.nativeCurrency.symbol) || ""
    };
  }
  async signMessage(params) {
    var _a, _b, _c2;
    const { provider, message, address } = params;
    if (!provider) {
      throw new Error("UniversalAdapter:signMessage - provider is undefined");
    }
    let signature = "";
    if (((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.chainNamespace) === ConstantsUtil.CHAIN.SOLANA) {
      const response = await provider.request({
        method: "solana_signMessage",
        params: {
          message: esm_default.encode(new TextEncoder().encode(message)),
          pubkey: address
        }
      }, (_b = ChainController.state.activeCaipNetwork) == null ? void 0 : _b.caipNetworkId);
      signature = response.signature;
    } else {
      signature = await provider.request({
        method: "personal_sign",
        params: [message, address]
      }, (_c2 = ChainController.state.activeCaipNetwork) == null ? void 0 : _c2.caipNetworkId);
    }
    return { signature };
  }
  // -- Transaction methods ---------------------------------------------------
  /**
   *
   * These methods are supported only on `wagmi` and `ethers` since the Solana SDK does not support them in the same way.
   * These function definition is to have a type parity between the clients. Currently not in use.
   */
  async estimateGas() {
    return Promise.resolve({
      gas: BigInt(0)
    });
  }
  async sendTransaction() {
    return Promise.resolve({
      hash: ""
    });
  }
  walletGetAssets(_params) {
    return Promise.resolve({});
  }
  async writeContract() {
    return Promise.resolve({
      hash: ""
    });
  }
  parseUnits() {
    return 0n;
  }
  formatUnits() {
    return "0";
  }
  async getCapabilities() {
    return Promise.resolve({});
  }
  async grantPermissions() {
    return Promise.resolve({});
  }
  async revokePermissions() {
    return Promise.resolve("0x");
  }
  async syncConnection() {
    return Promise.resolve({
      id: "WALLET_CONNECT",
      type: "WALLET_CONNECT",
      chainId: 1,
      provider: this.provider,
      address: ""
    });
  }
  // eslint-disable-next-line @typescript-eslint/require-await
  async switchNetwork(params) {
    var _a, _b, _c2, _d, _e5, _f;
    const { caipNetwork } = params;
    const connector = this.getWalletConnectConnector();
    if (caipNetwork.chainNamespace === ConstantsUtil.CHAIN.EVM) {
      try {
        await ((_a = connector.provider) == null ? void 0 : _a.request({
          method: "wallet_switchEthereumChain",
          params: [{ chainId: toHex(caipNetwork.id) }]
        }));
      } catch (switchError) {
        if (switchError.code === WcConstantsUtil.ERROR_CODE_UNRECOGNIZED_CHAIN_ID || switchError.code === WcConstantsUtil.ERROR_INVALID_CHAIN_ID || switchError.code === WcConstantsUtil.ERROR_CODE_DEFAULT || ((_c2 = (_b = switchError == null ? void 0 : switchError.data) == null ? void 0 : _b.originalError) == null ? void 0 : _c2.code) === WcConstantsUtil.ERROR_CODE_UNRECOGNIZED_CHAIN_ID) {
          try {
            await ((_f = connector.provider) == null ? void 0 : _f.request({
              method: "wallet_addEthereumChain",
              params: [
                {
                  chainId: toHex(caipNetwork.id),
                  rpcUrls: [(_d = caipNetwork == null ? void 0 : caipNetwork.rpcUrls["chainDefault"]) == null ? void 0 : _d.http],
                  chainName: caipNetwork.name,
                  nativeCurrency: caipNetwork.nativeCurrency,
                  blockExplorerUrls: [(_e5 = caipNetwork.blockExplorers) == null ? void 0 : _e5.default.url]
                }
              ]
            }));
          } catch (error) {
            throw new Error("Chain is not supported");
          }
        }
      }
    }
    connector.provider.setDefaultChain(caipNetwork.caipNetworkId);
  }
  getWalletConnectProvider() {
    const connector = this.connectors.find((c3) => c3.type === "WALLET_CONNECT");
    const provider = connector == null ? void 0 : connector.provider;
    return provider;
  }
};

// node_modules/@reown/appkit/dist/esm/src/utils/ConfigUtil.js
var FEATURE_KEYS = [
  "email",
  "socials",
  "swaps",
  "onramp",
  "activity",
  "reownBranding"
];
var featureConfig = {
  email: {
    apiFeatureName: "social_login",
    localFeatureName: "email",
    returnType: false,
    isLegacy: false,
    isAvailableOnBasic: false,
    processApi: (apiConfig) => {
      if (!(apiConfig == null ? void 0 : apiConfig.config)) {
        return false;
      }
      const config = apiConfig.config;
      return Boolean(apiConfig.isEnabled) && config.includes("email");
    },
    processFallback: (localValue) => {
      if (localValue === void 0) {
        return ConstantsUtil2.DEFAULT_REMOTE_FEATURES.email;
      }
      return Boolean(localValue);
    }
  },
  socials: {
    apiFeatureName: "social_login",
    localFeatureName: "socials",
    returnType: false,
    isLegacy: false,
    isAvailableOnBasic: false,
    processApi: (apiConfig) => {
      if (!(apiConfig == null ? void 0 : apiConfig.config)) {
        return false;
      }
      const config = apiConfig.config;
      return Boolean(apiConfig.isEnabled) && config.length > 0 ? config.filter((s) => s !== "email") : false;
    },
    processFallback: (localValue) => {
      if (localValue === void 0) {
        return ConstantsUtil2.DEFAULT_REMOTE_FEATURES.socials;
      }
      if (typeof localValue === "boolean") {
        return localValue ? ConstantsUtil2.DEFAULT_REMOTE_FEATURES.socials : false;
      }
      return localValue;
    }
  },
  swaps: {
    apiFeatureName: "swap",
    localFeatureName: "swaps",
    returnType: false,
    isLegacy: false,
    isAvailableOnBasic: false,
    processApi: (apiConfig) => {
      if (!(apiConfig == null ? void 0 : apiConfig.config)) {
        return false;
      }
      const config = apiConfig.config;
      return Boolean(apiConfig.isEnabled) && config.length > 0 ? config : false;
    },
    processFallback: (localValue) => {
      if (localValue === void 0) {
        return ConstantsUtil2.DEFAULT_REMOTE_FEATURES.swaps;
      }
      if (typeof localValue === "boolean") {
        return localValue ? ConstantsUtil2.DEFAULT_REMOTE_FEATURES.swaps : false;
      }
      return localValue;
    }
  },
  onramp: {
    apiFeatureName: "onramp",
    localFeatureName: "onramp",
    returnType: false,
    isLegacy: false,
    isAvailableOnBasic: false,
    processApi: (apiConfig) => {
      if (!(apiConfig == null ? void 0 : apiConfig.config)) {
        return false;
      }
      const config = apiConfig.config;
      return Boolean(apiConfig.isEnabled) && config.length > 0 ? config : false;
    },
    processFallback: (localValue) => {
      if (localValue === void 0) {
        return ConstantsUtil2.DEFAULT_REMOTE_FEATURES.onramp;
      }
      if (typeof localValue === "boolean") {
        return localValue ? ConstantsUtil2.DEFAULT_REMOTE_FEATURES.onramp : false;
      }
      return localValue;
    }
  },
  activity: {
    apiFeatureName: "activity",
    localFeatureName: "history",
    returnType: false,
    isLegacy: true,
    isAvailableOnBasic: false,
    processApi: (apiConfig) => Boolean(apiConfig.isEnabled),
    processFallback: (localValue) => {
      if (localValue === void 0) {
        return ConstantsUtil2.DEFAULT_REMOTE_FEATURES.activity;
      }
      return Boolean(localValue);
    }
  },
  reownBranding: {
    apiFeatureName: "reown_branding",
    localFeatureName: "reownBranding",
    returnType: false,
    isLegacy: false,
    isAvailableOnBasic: false,
    processApi: (apiConfig) => Boolean(apiConfig.isEnabled),
    processFallback: (localValue) => {
      if (localValue === void 0) {
        return ConstantsUtil2.DEFAULT_REMOTE_FEATURES.reownBranding;
      }
      return Boolean(localValue);
    }
  }
};
var ConfigUtil = {
  localSettingsOverridden: /* @__PURE__ */ new Set(),
  getApiConfig(id, apiProjectConfig) {
    return apiProjectConfig == null ? void 0 : apiProjectConfig.find((f6) => f6.id === id);
  },
  addWarning(localFeatureValue, featureKey) {
    if (localFeatureValue !== void 0) {
      const config = featureConfig[featureKey];
      const warningName = config.isLegacy ? `"features.${config.localFeatureName}" (now "${featureKey}")` : `"features.${featureKey}"`;
      this.localSettingsOverridden.add(warningName);
    }
  },
  processFeature(featureKey, localFeatures, apiProjectConfig, useApi, isBasic) {
    const config = featureConfig[featureKey];
    const localValue = localFeatures[config.localFeatureName];
    if (isBasic && !config.isAvailableOnBasic) {
      return false;
    }
    if (useApi) {
      const apiConfig = this.getApiConfig(config.apiFeatureName, apiProjectConfig);
      if ((apiConfig == null ? void 0 : apiConfig.config) === null) {
        return this.processFallbackFeature(featureKey, localValue);
      }
      if (!(apiConfig == null ? void 0 : apiConfig.config)) {
        return false;
      }
      if (localValue !== void 0) {
        this.addWarning(localValue, featureKey);
      }
      return this.processApiFeature(featureKey, apiConfig);
    }
    return this.processFallbackFeature(featureKey, localValue);
  },
  processApiFeature(featureKey, apiConfig) {
    return featureConfig[featureKey].processApi(apiConfig);
  },
  processFallbackFeature(featureKey, localValue) {
    return featureConfig[featureKey].processFallback(localValue);
  },
  async fetchRemoteFeatures(config) {
    const isBasic = config.basic ?? false;
    const localFeatures = config.features || {};
    this.localSettingsOverridden.clear();
    let apiProjectConfig = null;
    let useApiConfig = false;
    try {
      apiProjectConfig = await ApiController.fetchProjectConfig();
      useApiConfig = apiProjectConfig !== null && apiProjectConfig !== void 0;
    } catch (e) {
      console.warn("[Reown Config] Failed to fetch remote project configuration. Using local/default values.", e);
    }
    const remoteFeaturesConfig = useApiConfig && !isBasic ? ConstantsUtil2.DEFAULT_REMOTE_FEATURES : ConstantsUtil2.DEFAULT_REMOTE_FEATURES_DISABLED;
    try {
      for (const featureKey of FEATURE_KEYS) {
        const result = this.processFeature(featureKey, localFeatures, apiProjectConfig, useApiConfig, isBasic);
        Object.assign(remoteFeaturesConfig, { [featureKey]: result });
      }
    } catch (e) {
      console.warn("[Reown Config] Failed to process the configuration from Cloud. Using default values.", e);
      return ConstantsUtil2.DEFAULT_REMOTE_FEATURES;
    }
    if (useApiConfig && this.localSettingsOverridden.size > 0) {
      const warningMessage = `Your local configuration for ${Array.from(this.localSettingsOverridden).join(", ")} was ignored because a remote configuration was successfully fetched. Please manage these features via your project dashboard on dashboard.reown.com.`;
      AlertController.open({
        shortMessage: "Local configuration ignored",
        longMessage: `[Reown Config Notice] ${warningMessage}`
      }, "warning");
    }
    return remoteFeaturesConfig;
  }
};

// node_modules/@reown/appkit/dist/esm/src/client/appkit-base-client.js
var AppKitBaseClient = class {
  constructor(options) {
    this.chainNamespaces = [];
    this.remoteFeatures = {};
    this.reportedAlertErrors = {};
    this.getCaipNetwork = (chainNamespace, id) => {
      var _a, _b, _c2, _d;
      if (chainNamespace) {
        const caipNetworkWithId = (_b = (_a = ChainController.getNetworkData(chainNamespace)) == null ? void 0 : _a.requestedCaipNetworks) == null ? void 0 : _b.find((c3) => c3.id === id);
        if (caipNetworkWithId) {
          return caipNetworkWithId;
        }
        const namespaceCaipNetwork = (_c2 = ChainController.getNetworkData(chainNamespace)) == null ? void 0 : _c2.caipNetwork;
        if (namespaceCaipNetwork) {
          return namespaceCaipNetwork;
        }
        const requestedCaipNetworks = ChainController.getRequestedCaipNetworks(chainNamespace);
        return (_d = requestedCaipNetworks.filter((c3) => c3.chainNamespace === chainNamespace)) == null ? void 0 : _d[0];
      }
      return ChainController.state.activeCaipNetwork || this.defaultCaipNetwork;
    };
    this.getCaipNetworkId = () => {
      const network = this.getCaipNetwork();
      if (network) {
        return network.id;
      }
      return void 0;
    };
    this.getCaipNetworks = (namespace) => ChainController.getCaipNetworks(namespace);
    this.getActiveChainNamespace = () => ChainController.state.activeChain;
    this.setRequestedCaipNetworks = (requestedCaipNetworks, chain) => {
      ChainController.setRequestedCaipNetworks(requestedCaipNetworks, chain);
    };
    this.getApprovedCaipNetworkIds = () => ChainController.getAllApprovedCaipNetworkIds();
    this.getCaipAddress = (chainNamespace) => {
      if (ChainController.state.activeChain === chainNamespace || !chainNamespace) {
        return ChainController.state.activeCaipAddress;
      }
      return ChainController.getAccountProp("caipAddress", chainNamespace);
    };
    this.setClientId = (clientId) => {
      BlockchainApiController.setClientId(clientId);
    };
    this.getProvider = (namespace) => ProviderUtil.getProvider(namespace);
    this.getProviderType = (namespace) => ProviderUtil.getProviderId(namespace);
    this.getPreferredAccountType = (namespace) => {
      var _a;
      return (_a = AccountController.state.preferredAccountTypes) == null ? void 0 : _a[namespace];
    };
    this.setCaipAddress = (caipAddress, chain) => {
      AccountController.setCaipAddress(caipAddress, chain);
      if (caipAddress && OptionsController.state.enableEmbedded) {
        this.close();
      }
    };
    this.setBalance = (balance, balanceSymbol, chain) => {
      AccountController.setBalance(balance, balanceSymbol, chain);
    };
    this.setProfileName = (profileName, chain) => {
      AccountController.setProfileName(profileName, chain);
    };
    this.setProfileImage = (profileImage, chain) => {
      AccountController.setProfileImage(profileImage, chain);
    };
    this.setUser = (user, chain) => {
      AccountController.setUser(user, chain);
    };
    this.resetAccount = (chain) => {
      AccountController.resetAccount(chain);
    };
    this.setCaipNetwork = (caipNetwork) => {
      ChainController.setActiveCaipNetwork(caipNetwork);
    };
    this.setCaipNetworkOfNamespace = (caipNetwork, chainNamespace) => {
      ChainController.setChainNetworkData(chainNamespace, { caipNetwork });
    };
    this.setAllAccounts = (addresses, chain) => {
      AccountController.setAllAccounts(addresses, chain);
      OptionsController.setHasMultipleAddresses((addresses == null ? void 0 : addresses.length) > 1);
    };
    this.setStatus = (status, chain) => {
      AccountController.setStatus(status, chain);
      if (ConnectorController.isConnected()) {
        StorageUtil.setConnectionStatus("connected");
      } else {
        StorageUtil.setConnectionStatus("disconnected");
      }
    };
    this.getAddressByChainNamespace = (chainNamespace) => ChainController.getAccountProp("address", chainNamespace);
    this.setConnectors = (connectors) => {
      const allConnectors = [...ConnectorController.state.allConnectors, ...connectors];
      ConnectorController.setConnectors(allConnectors);
    };
    this.setConnections = (connections, chainNamespace) => {
      ConnectionController.setConnections(connections, chainNamespace);
    };
    this.fetchIdentity = (request) => BlockchainApiController.fetchIdentity(request);
    this.getReownName = (address) => EnsController.getNamesForAddress(address);
    this.getConnectors = () => ConnectorController.getConnectors();
    this.getConnectorImage = (connector) => AssetUtil.getConnectorImage(connector);
    this.setConnectedWalletInfo = (connectedWalletInfo, chain) => {
      const type = ProviderUtil.getProviderId(chain);
      const walletInfo = connectedWalletInfo ? { ...connectedWalletInfo, type } : void 0;
      AccountController.setConnectedWalletInfo(walletInfo, chain);
    };
    this.getIsConnectedState = () => Boolean(ChainController.state.activeCaipAddress);
    this.addAddressLabel = (address, label, chain) => {
      AccountController.addAddressLabel(address, label, chain);
    };
    this.removeAddressLabel = (address, chain) => {
      AccountController.removeAddressLabel(address, chain);
    };
    this.getAddress = (chainNamespace) => {
      if (ChainController.state.activeChain === chainNamespace || !chainNamespace) {
        return AccountController.state.address;
      }
      return ChainController.getAccountProp("address", chainNamespace);
    };
    this.setApprovedCaipNetworksData = (namespace) => ChainController.setApprovedCaipNetworksData(namespace);
    this.resetNetwork = (namespace) => {
      ChainController.resetNetwork(namespace);
    };
    this.addConnector = (connector) => {
      ConnectorController.addConnector(connector);
    };
    this.resetWcConnection = () => {
      ConnectionController.resetWcConnection();
    };
    this.setAddressExplorerUrl = (addressExplorerUrl, chain) => {
      AccountController.setAddressExplorerUrl(addressExplorerUrl, chain);
    };
    this.setSmartAccountDeployed = (isDeployed, chain) => {
      AccountController.setSmartAccountDeployed(isDeployed, chain);
    };
    this.setSmartAccountEnabledNetworks = (smartAccountEnabledNetworks, chain) => {
      ChainController.setSmartAccountEnabledNetworks(smartAccountEnabledNetworks, chain);
    };
    this.setPreferredAccountType = (preferredAccountType, chain) => {
      AccountController.setPreferredAccountType(preferredAccountType, chain);
    };
    this.setEIP6963Enabled = (enabled) => {
      OptionsController.setEIP6963Enabled(enabled);
    };
    this.handleUnsafeRPCRequest = () => {
      if (this.isOpen()) {
        if (this.isTransactionStackEmpty()) {
          return;
        }
        this.redirect("ApproveTransaction");
      } else {
        this.open({ view: "ApproveTransaction" });
      }
    };
    this.options = options;
    this.version = options.sdkVersion;
    this.caipNetworks = this.extendCaipNetworks(options);
    this.chainNamespaces = this.getChainNamespacesSet(options.adapters, this.caipNetworks);
    this.defaultCaipNetwork = this.extendDefaultCaipNetwork(options);
    this.chainAdapters = this.createAdapters(options.adapters);
    this.readyPromise = this.initialize(options);
  }
  getChainNamespacesSet(adapters, caipNetworks) {
    const adapterNamespaces = adapters == null ? void 0 : adapters.map((adapter) => adapter.namespace).filter((namespace) => Boolean(namespace));
    if (adapterNamespaces == null ? void 0 : adapterNamespaces.length) {
      return [...new Set(adapterNamespaces)];
    }
    const networkNamespaces = caipNetworks == null ? void 0 : caipNetworks.map((network) => network.chainNamespace);
    return [...new Set(networkNamespaces)];
  }
  async initialize(options) {
    var _a, _b, _c2;
    this.initializeProjectSettings(options);
    this.initControllers(options);
    await this.initChainAdapters();
    this.sendInitializeEvent(options);
    await this.syncExistingConnection();
    this.remoteFeatures = await ConfigUtil.fetchRemoteFeatures(options);
    OptionsController.setRemoteFeatures(this.remoteFeatures);
    if (this.remoteFeatures.onramp) {
      OnRampController.setOnrampProviders(this.remoteFeatures.onramp);
    }
    if (((_a = OptionsController.state.remoteFeatures) == null ? void 0 : _a.email) || Array.isArray((_b = OptionsController.state.remoteFeatures) == null ? void 0 : _b.socials) && ((_c2 = OptionsController.state.remoteFeatures) == null ? void 0 : _c2.socials.length) > 0) {
      await this.checkAllowedOrigins();
    }
  }
  async checkAllowedOrigins() {
    const allowedOrigins = await ApiController.fetchAllowedOrigins();
    if (allowedOrigins && CoreHelperUtil.isClient()) {
      const currentOrigin = window.location.origin;
      const isOriginAllowed = WcHelpersUtil.isOriginAllowed(currentOrigin, allowedOrigins, WcConstantsUtil.DEFAULT_ALLOWED_ANCESTORS);
      if (!isOriginAllowed) {
        AlertController.open(ErrorUtil.ALERT_ERRORS.INVALID_APP_CONFIGURATION, "error");
      }
    } else {
      AlertController.open(ErrorUtil.ALERT_ERRORS.PROJECT_ID_NOT_CONFIGURED, "error");
    }
  }
  sendInitializeEvent(options) {
    var _a;
    const { ...optionsCopy } = options;
    delete optionsCopy.adapters;
    delete optionsCopy.universalProvider;
    EventsController.sendEvent({
      type: "track",
      event: "INITIALIZE",
      properties: {
        ...optionsCopy,
        networks: options.networks.map((n2) => n2.id),
        siweConfig: {
          options: ((_a = options.siweConfig) == null ? void 0 : _a.options) || {}
        }
      }
    });
  }
  // -- Controllers initialization ---------------------------------------------------
  initControllers(options) {
    this.initializeOptionsController(options);
    this.initializeChainController(options);
    this.initializeThemeController(options);
    this.initializeConnectionController(options);
    this.initializeConnectorController();
  }
  initializeThemeController(options) {
    if (options.themeMode) {
      ThemeController.setThemeMode(options.themeMode);
    }
    if (options.themeVariables) {
      ThemeController.setThemeVariables(options.themeVariables);
    }
  }
  initializeChainController(options) {
    if (!this.connectionControllerClient || !this.networkControllerClient) {
      throw new Error("ConnectionControllerClient and NetworkControllerClient must be set");
    }
    ChainController.initialize(options.adapters ?? [], this.caipNetworks, {
      connectionControllerClient: this.connectionControllerClient,
      networkControllerClient: this.networkControllerClient
    });
    const network = this.getDefaultNetwork();
    if (network) {
      ChainController.setActiveCaipNetwork(network);
    }
  }
  initializeConnectionController(options) {
    ConnectionController.setWcBasic(options.basic ?? false);
  }
  initializeConnectorController() {
    ConnectorController.initialize(this.chainNamespaces);
  }
  initializeProjectSettings(options) {
    OptionsController.setProjectId(options.projectId);
    OptionsController.setSdkVersion(options.sdkVersion);
  }
  initializeOptionsController(options) {
    var _a;
    OptionsController.setDebug(options.debug !== false);
    OptionsController.setEnableWalletConnect(options.enableWalletConnect !== false);
    OptionsController.setEnableWalletGuide(options.enableWalletGuide !== false);
    OptionsController.setEnableWallets(options.enableWallets !== false);
    OptionsController.setEIP6963Enabled(options.enableEIP6963 !== false);
    OptionsController.setEnableNetworkSwitch(options.enableNetworkSwitch !== false);
    OptionsController.setEnableAuthLogger(options.enableAuthLogger !== false);
    OptionsController.setCustomRpcUrls(options.customRpcUrls);
    OptionsController.setEnableEmbedded(options.enableEmbedded);
    OptionsController.setAllWallets(options.allWallets);
    OptionsController.setIncludeWalletIds(options.includeWalletIds);
    OptionsController.setExcludeWalletIds(options.excludeWalletIds);
    OptionsController.setFeaturedWalletIds(options.featuredWalletIds);
    OptionsController.setTokens(options.tokens);
    OptionsController.setTermsConditionsUrl(options.termsConditionsUrl);
    OptionsController.setPrivacyPolicyUrl(options.privacyPolicyUrl);
    OptionsController.setCustomWallets(options.customWallets);
    OptionsController.setFeatures(options.features);
    OptionsController.setAllowUnsupportedChain(options.allowUnsupportedChain);
    OptionsController.setUniversalProviderConfigOverride(options.universalProviderConfigOverride);
    OptionsController.setPreferUniversalLinks(options.experimental_preferUniversalLinks);
    OptionsController.setDefaultAccountTypes(options.defaultAccountTypes);
    const storedAccountTypes = StorageUtil.getPreferredAccountTypes() || {};
    const defaultTypes = { ...OptionsController.state.defaultAccountTypes, ...storedAccountTypes };
    AccountController.setPreferredAccountTypes(defaultTypes);
    const defaultMetaData = this.getDefaultMetaData();
    if (!options.metadata && defaultMetaData) {
      options.metadata = defaultMetaData;
    }
    OptionsController.setMetadata(options.metadata);
    OptionsController.setDisableAppend(options.disableAppend);
    OptionsController.setEnableEmbedded(options.enableEmbedded);
    OptionsController.setSIWX(options.siwx);
    if (!options.projectId) {
      AlertController.open(ErrorUtil.ALERT_ERRORS.PROJECT_ID_NOT_CONFIGURED, "error");
      return;
    }
    const evmAdapter = (_a = options.adapters) == null ? void 0 : _a.find((adapter) => adapter.namespace === ConstantsUtil.CHAIN.EVM);
    if (evmAdapter) {
      if (options.siweConfig) {
        if (options.siwx) {
          throw new Error("Cannot set both `siweConfig` and `siwx` options");
        }
        OptionsController.setSIWX(options.siweConfig.mapToSIWX());
      }
    }
  }
  getDefaultMetaData() {
    var _a, _b, _c2, _d;
    if (CoreHelperUtil.isClient()) {
      return {
        name: ((_b = (_a = document.getElementsByTagName("title")) == null ? void 0 : _a[0]) == null ? void 0 : _b.textContent) || "",
        description: ((_c2 = document.querySelector('meta[property="og:description"]')) == null ? void 0 : _c2.content) || "",
        url: window.location.origin,
        icons: [((_d = document.querySelector('link[rel~="icon"]')) == null ? void 0 : _d.href) || ""]
      };
    }
    return null;
  }
  // -- Network Initialization ---------------------------------------------------
  setUnsupportedNetwork(chainId) {
    const namespace = this.getActiveChainNamespace();
    if (namespace) {
      const unsupportedNetwork = CaipNetworksUtil.getUnsupportedNetwork(`${namespace}:${chainId}`);
      ChainController.setActiveCaipNetwork(unsupportedNetwork);
    }
  }
  getDefaultNetwork() {
    return CaipNetworksUtil.getCaipNetworkFromStorage(this.defaultCaipNetwork);
  }
  extendCaipNetwork(network, options) {
    const extendedNetwork = CaipNetworksUtil.extendCaipNetwork(network, {
      customNetworkImageUrls: options.chainImages,
      projectId: options.projectId
    });
    return extendedNetwork;
  }
  extendCaipNetworks(options) {
    const extendedNetworks = CaipNetworksUtil.extendCaipNetworks(options.networks, {
      customNetworkImageUrls: options.chainImages,
      customRpcUrls: options.customRpcUrls,
      projectId: options.projectId
    });
    return extendedNetworks;
  }
  extendDefaultCaipNetwork(options) {
    const defaultNetwork = options.networks.find((n2) => {
      var _a;
      return n2.id === ((_a = options.defaultNetwork) == null ? void 0 : _a.id);
    });
    const extendedNetwork = defaultNetwork ? CaipNetworksUtil.extendCaipNetwork(defaultNetwork, {
      customNetworkImageUrls: options.chainImages,
      customRpcUrls: options.customRpcUrls,
      projectId: options.projectId
    }) : void 0;
    return extendedNetwork;
  }
  async disconnectNamespace(namespace) {
    try {
      const adapter = this.getAdapter(namespace);
      const provider = ProviderUtil.getProvider(namespace);
      const providerType = ProviderUtil.getProviderId(namespace);
      const { caipAddress } = ChainController.getAccountData(namespace) || {};
      this.setLoading(true, namespace);
      if (caipAddress && (adapter == null ? void 0 : adapter.disconnect)) {
        await adapter.disconnect({ provider, providerType });
      }
      StorageUtil.removeConnectedNamespace(namespace);
      ProviderUtil.resetChain(namespace);
      this.setUser(void 0, namespace);
      this.setStatus("disconnected", namespace);
      this.setConnectedWalletInfo(void 0, namespace);
      ConnectorController.removeConnectorId(namespace);
      ChainController.resetAccount(namespace);
      ChainController.resetNetwork(namespace);
      this.setLoading(false, namespace);
    } catch (error) {
      this.setLoading(false, namespace);
      throw new Error(`Failed to disconnect chain ${namespace}: ${error.message}`);
    }
  }
  // -- Client Initialization ---------------------------------------------------
  createClients() {
    this.connectionControllerClient = {
      connectWalletConnect: async () => {
        var _a;
        const activeChain = ChainController.state.activeChain;
        const adapter = this.getAdapter(activeChain);
        const chainId = (_a = this.getCaipNetwork(activeChain)) == null ? void 0 : _a.id;
        if (!adapter) {
          throw new Error("Adapter not found");
        }
        const result = await adapter.connectWalletConnect(chainId);
        this.close();
        this.setClientId((result == null ? void 0 : result.clientId) || null);
        StorageUtil.setConnectedNamespaces([...ChainController.state.chains.keys()]);
        this.chainNamespaces.forEach((namespace) => {
          ConnectorController.setConnectorId(ConstantsUtil3.CONNECTOR_TYPE_WALLET_CONNECT, namespace);
        });
        await this.syncWalletConnectAccount();
      },
      connectExternal: async ({ id, info, type, provider, chain, caipNetwork, socialUri }) => {
        var _a, _b, _c2, _d, _e5, _f;
        const activeChain = ChainController.state.activeChain;
        const chainToUse = chain || activeChain;
        const adapter = this.getAdapter(chainToUse);
        if (chain && chain !== activeChain && !caipNetwork) {
          const toConnectNetwork = this.getCaipNetworks().find((network) => network.chainNamespace === chain);
          if (toConnectNetwork) {
            this.setCaipNetwork(toConnectNetwork);
          }
        }
        if (!adapter) {
          throw new Error("Adapter not found");
        }
        const fallbackCaipNetwork = this.getCaipNetwork(chainToUse);
        const res = await adapter.connect({
          id,
          info,
          type,
          provider,
          socialUri,
          chainId: (caipNetwork == null ? void 0 : caipNetwork.id) || (fallbackCaipNetwork == null ? void 0 : fallbackCaipNetwork.id),
          rpcUrl: ((_c2 = (_b = (_a = caipNetwork == null ? void 0 : caipNetwork.rpcUrls) == null ? void 0 : _a.default) == null ? void 0 : _b.http) == null ? void 0 : _c2[0]) || ((_f = (_e5 = (_d = fallbackCaipNetwork == null ? void 0 : fallbackCaipNetwork.rpcUrls) == null ? void 0 : _d.default) == null ? void 0 : _e5.http) == null ? void 0 : _f[0])
        });
        if (!res) {
          return;
        }
        StorageUtil.addConnectedNamespace(chainToUse);
        this.syncProvider({ ...res, chainNamespace: chainToUse });
        const syncedAccounts = AccountController.state.allAccounts;
        const { accounts } = (syncedAccounts == null ? void 0 : syncedAccounts.length) > 0 ? (
          // eslint-disable-next-line line-comment-position
          // Using new array else the accounts will have the same reference and react will not re-render
          { accounts: [...syncedAccounts] }
        ) : await adapter.getAccounts({ namespace: chainToUse, id });
        this.setAllAccounts(accounts, chainToUse);
        this.setStatus("connected", chainToUse);
        this.syncConnectedWalletInfo(chainToUse);
      },
      reconnectExternal: async ({ id, info, type, provider }) => {
        var _a;
        const namespace = ChainController.state.activeChain;
        const adapter = this.getAdapter(namespace);
        if (adapter == null ? void 0 : adapter.reconnect) {
          await (adapter == null ? void 0 : adapter.reconnect({ id, info, type, provider, chainId: (_a = this.getCaipNetwork()) == null ? void 0 : _a.id }));
          StorageUtil.addConnectedNamespace(namespace);
          this.syncConnectedWalletInfo(namespace);
        }
      },
      disconnect: async (chainNamespace) => {
        const chainsToDisconnect = getChainsToDisconnect(chainNamespace);
        try {
          const disconnectResults = await Promise.allSettled(chainsToDisconnect.map(async ([ns2]) => this.disconnectNamespace(ns2)));
          SendController.resetSend();
          ConnectionController.resetWcConnection();
          await SIWXUtil.clearSessions();
          ConnectorController.setFilterByNamespace(void 0);
          const failures = disconnectResults.filter((result) => result.status === "rejected");
          if (failures.length > 0) {
            throw new Error(failures.map((f6) => f6.reason.message).join(", "));
          }
          StorageUtil.deleteConnectedSocialProvider();
          EventsController.sendEvent({
            type: "track",
            event: "DISCONNECT_SUCCESS",
            properties: {
              namespace: chainNamespace || "all"
            }
          });
        } catch (error) {
          throw new Error(`Failed to disconnect chains: ${error.message}`);
        }
      },
      checkInstalled: (ids) => {
        if (!ids) {
          return Boolean(window.ethereum);
        }
        return ids.some((id) => {
          var _a;
          return Boolean((_a = window.ethereum) == null ? void 0 : _a[String(id)]);
        });
      },
      signMessage: async (message) => {
        const adapter = this.getAdapter(ChainController.state.activeChain);
        const result = await (adapter == null ? void 0 : adapter.signMessage({
          message,
          address: AccountController.state.address,
          provider: ProviderUtil.getProvider(ChainController.state.activeChain)
        }));
        return (result == null ? void 0 : result.signature) || "";
      },
      sendTransaction: async (args) => {
        const namespace = args.chainNamespace;
        if (ConstantsUtil2.SEND_SUPPORTED_NAMESPACES.includes(namespace)) {
          const adapter = this.getAdapter(ChainController.state.activeChain);
          const provider = ProviderUtil.getProvider(namespace);
          const result = await (adapter == null ? void 0 : adapter.sendTransaction({
            ...args,
            caipNetwork: this.getCaipNetwork(),
            provider
          }));
          return (result == null ? void 0 : result.hash) || "";
        }
        return "";
      },
      estimateGas: async (args) => {
        if (args.chainNamespace === ConstantsUtil.CHAIN.EVM) {
          const adapter = this.getAdapter(ChainController.state.activeChain);
          const provider = ProviderUtil.getProvider(ChainController.state.activeChain);
          const caipNetwork = this.getCaipNetwork();
          if (!caipNetwork) {
            throw new Error("CaipNetwork is undefined");
          }
          const result = await (adapter == null ? void 0 : adapter.estimateGas({
            ...args,
            provider,
            caipNetwork
          }));
          return (result == null ? void 0 : result.gas) || 0n;
        }
        return 0n;
      },
      getEnsAvatar: async () => {
        var _a;
        await this.syncIdentity({
          address: AccountController.state.address,
          chainId: Number((_a = this.getCaipNetwork()) == null ? void 0 : _a.id),
          chainNamespace: ChainController.state.activeChain
        });
        return AccountController.state.profileImage || false;
      },
      getEnsAddress: async (name) => await WcHelpersUtil.resolveReownName(name),
      writeContract: async (args) => {
        const adapter = this.getAdapter(ChainController.state.activeChain);
        const caipNetwork = this.getCaipNetwork();
        const caipAddress = this.getCaipAddress();
        const provider = ProviderUtil.getProvider(ChainController.state.activeChain);
        if (!caipNetwork || !caipAddress) {
          throw new Error("CaipNetwork or CaipAddress is undefined");
        }
        const result = await (adapter == null ? void 0 : adapter.writeContract({ ...args, caipNetwork, provider, caipAddress }));
        return result == null ? void 0 : result.hash;
      },
      parseUnits: (value, decimals) => {
        const adapter = this.getAdapter(ChainController.state.activeChain);
        return (adapter == null ? void 0 : adapter.parseUnits({ value, decimals })) ?? 0n;
      },
      formatUnits: (value, decimals) => {
        const adapter = this.getAdapter(ChainController.state.activeChain);
        return (adapter == null ? void 0 : adapter.formatUnits({ value, decimals })) ?? "0";
      },
      getCapabilities: async (params) => {
        const adapter = this.getAdapter(ChainController.state.activeChain);
        return await (adapter == null ? void 0 : adapter.getCapabilities(params));
      },
      grantPermissions: async (params) => {
        const adapter = this.getAdapter(ChainController.state.activeChain);
        return await (adapter == null ? void 0 : adapter.grantPermissions(params));
      },
      revokePermissions: async (params) => {
        const adapter = this.getAdapter(ChainController.state.activeChain);
        if (adapter == null ? void 0 : adapter.revokePermissions) {
          return await adapter.revokePermissions(params);
        }
        return "0x";
      },
      walletGetAssets: async (params) => {
        const adapter = this.getAdapter(ChainController.state.activeChain);
        return await (adapter == null ? void 0 : adapter.walletGetAssets(params)) ?? {};
      },
      updateBalance: (namespace) => {
        const caipNetwork = this.getCaipNetwork(namespace);
        if (!caipNetwork || !AccountController.state.address) {
          return;
        }
        this.updateNativeBalance(AccountController.state.address, caipNetwork == null ? void 0 : caipNetwork.id, namespace);
      }
    };
    this.networkControllerClient = {
      switchCaipNetwork: async (caipNetwork) => await this.switchCaipNetwork(caipNetwork),
      // eslint-disable-next-line @typescript-eslint/require-await
      getApprovedCaipNetworksData: async () => this.getApprovedCaipNetworksData()
    };
    ConnectionController.setClient(this.connectionControllerClient);
  }
  getApprovedCaipNetworksData() {
    var _a, _b, _c2, _d, _e5;
    const providerType = ProviderUtil.getProviderId(ChainController.state.activeChain);
    if (providerType === ConstantsUtil3.CONNECTOR_TYPE_WALLET_CONNECT) {
      const namespaces = (_b = (_a = this.universalProvider) == null ? void 0 : _a.session) == null ? void 0 : _b.namespaces;
      return {
        /*
         * MetaMask Wallet only returns 1 namespace in the session object. This makes it imposible
         * to switch to other networks. Setting supportsAllNetworks to true for MetaMask Wallet
         * will make it possible to switch to other networks.
         */
        supportsAllNetworks: ((_e5 = (_d = (_c2 = this.universalProvider) == null ? void 0 : _c2.session) == null ? void 0 : _d.peer) == null ? void 0 : _e5.metadata.name) === "MetaMask Wallet",
        approvedCaipNetworkIds: this.getChainsFromNamespaces(namespaces)
      };
    }
    return { supportsAllNetworks: true, approvedCaipNetworkIds: [] };
  }
  async switchCaipNetwork(caipNetwork) {
    if (!caipNetwork) {
      return;
    }
    const networkNamespace = caipNetwork.chainNamespace;
    const namespaceAddress = this.getAddressByChainNamespace(caipNetwork.chainNamespace);
    if (namespaceAddress) {
      const provider = ProviderUtil.getProvider(networkNamespace);
      const providerType = ProviderUtil.getProviderId(networkNamespace);
      if (caipNetwork.chainNamespace === ChainController.state.activeChain) {
        const adapter = this.getAdapter(networkNamespace);
        await (adapter == null ? void 0 : adapter.switchNetwork({ caipNetwork, provider, providerType }));
      } else {
        this.setCaipNetwork(caipNetwork);
        if (providerType === ConstantsUtil3.CONNECTOR_TYPE_WALLET_CONNECT) {
          this.syncWalletConnectAccount();
        } else {
          const address = this.getAddressByChainNamespace(networkNamespace);
          if (address) {
            this.syncAccount({
              address,
              chainId: caipNetwork.id,
              chainNamespace: networkNamespace
            });
          }
        }
      }
    } else {
      this.setCaipNetwork(caipNetwork);
    }
  }
  getChainsFromNamespaces(namespaces = {}) {
    return Object.values(namespaces).flatMap((namespace) => {
      const chains = namespace.chains || [];
      const accountsChains = namespace.accounts.map((account) => {
        const { chainId, chainNamespace } = ParseUtil.parseCaipAddress(account);
        return `${chainNamespace}:${chainId}`;
      });
      return Array.from(/* @__PURE__ */ new Set([...chains, ...accountsChains]));
    });
  }
  // -- Adapter Initialization ---------------------------------------------------
  createAdapters(blueprints) {
    this.createClients();
    return this.chainNamespaces.reduce((adapters, namespace) => {
      var _a;
      const blueprint = blueprints == null ? void 0 : blueprints.find((b4) => b4.namespace === namespace);
      if (blueprint) {
        blueprint.construct({
          namespace,
          projectId: (_a = this.options) == null ? void 0 : _a.projectId,
          networks: this.getCaipNetworks()
        });
        adapters[namespace] = blueprint;
      } else {
        adapters[namespace] = new UniversalAdapter({
          namespace,
          networks: this.getCaipNetworks()
        });
      }
      return adapters;
    }, {});
  }
  async initChainAdapter(namespace) {
    var _a;
    this.onConnectors(namespace);
    this.listenAdapter(namespace);
    await ((_a = this.chainAdapters) == null ? void 0 : _a[namespace].syncConnectors(this.options, this));
    await this.createUniversalProviderForAdapter(namespace);
  }
  async initChainAdapters() {
    await Promise.all(this.chainNamespaces.map(async (namespace) => {
      await this.initChainAdapter(namespace);
    }));
  }
  onConnectors(chainNamespace) {
    const adapter = this.getAdapter(chainNamespace);
    adapter == null ? void 0 : adapter.on("connectors", this.setConnectors.bind(this));
  }
  listenAdapter(chainNamespace) {
    const adapter = this.getAdapter(chainNamespace);
    if (!adapter) {
      return;
    }
    const connectionStatus = StorageUtil.getConnectionStatus();
    if (connectionStatus === "connected") {
      this.setStatus("connecting", chainNamespace);
    } else if (connectionStatus === "disconnected") {
      StorageUtil.clearAddressCache();
      this.setStatus(connectionStatus, chainNamespace);
    } else {
      this.setStatus(connectionStatus, chainNamespace);
    }
    adapter.on("switchNetwork", ({ address, chainId }) => {
      const caipNetwork = this.getCaipNetworks().find((n2) => n2.id === chainId || n2.caipNetworkId === chainId);
      const isSameNamespace = ChainController.state.activeChain === chainNamespace;
      const accountAddress = ChainController.getAccountProp("address", chainNamespace);
      if (caipNetwork) {
        const account = isSameNamespace && address ? address : accountAddress;
        if (account) {
          this.syncAccount({ address: account, chainId: caipNetwork.id, chainNamespace });
        }
      } else {
        this.setUnsupportedNetwork(chainId);
      }
    });
    adapter.on("disconnect", this.disconnect.bind(this, chainNamespace));
    adapter.on("connections", (connections) => {
      this.setConnections(connections, chainNamespace);
    });
    adapter.on("pendingTransactions", () => {
      const address = AccountController.state.address;
      const activeCaipNetwork = ChainController.state.activeCaipNetwork;
      if (!address || !(activeCaipNetwork == null ? void 0 : activeCaipNetwork.id)) {
        return;
      }
      this.updateNativeBalance(address, activeCaipNetwork.id, activeCaipNetwork.chainNamespace);
    });
    adapter.on("accountChanged", ({ address, chainId }) => {
      var _a, _b;
      const isActiveChain = ChainController.state.activeChain === chainNamespace;
      if (isActiveChain && chainId) {
        this.syncAccount({
          address,
          chainId,
          chainNamespace
        });
      } else if (isActiveChain && ((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.id)) {
        this.syncAccount({
          address,
          chainId: (_b = ChainController.state.activeCaipNetwork) == null ? void 0 : _b.id,
          chainNamespace
        });
      } else {
        this.syncAccountInfo(address, chainId, chainNamespace);
      }
      this.syncAllAccounts(chainNamespace);
    });
  }
  async createUniversalProviderForAdapter(chainNamespace) {
    var _a, _b, _c2;
    await this.getUniversalProvider();
    if (this.universalProvider) {
      (_c2 = (_b = (_a = this.chainAdapters) == null ? void 0 : _a[chainNamespace]) == null ? void 0 : _b.setUniversalProvider) == null ? void 0 : _c2.call(_b, this.universalProvider);
    }
  }
  // -- Connection Sync ---------------------------------------------------
  async syncExistingConnection() {
    await Promise.allSettled(this.chainNamespaces.map((namespace) => this.syncNamespaceConnection(namespace)));
  }
  async syncNamespaceConnection(namespace) {
    try {
      if (namespace === ConstantsUtil.CHAIN.EVM && CoreHelperUtil.isSafeApp()) {
        ConnectorController.setConnectorId(ConstantsUtil.CONNECTOR_ID.SAFE, namespace);
      }
      const connectorId = ConnectorController.getConnectorId(namespace);
      this.setStatus("connecting", namespace);
      switch (connectorId) {
        case ConstantsUtil.CONNECTOR_ID.WALLET_CONNECT:
          await this.syncWalletConnectAccount();
          break;
        case ConstantsUtil.CONNECTOR_ID.AUTH:
          break;
        default:
          await this.syncAdapterConnection(namespace);
      }
    } catch (err) {
      console.warn("AppKit couldn't sync existing connection", err);
      this.setStatus("disconnected", namespace);
    }
  }
  async syncAdapterConnection(namespace) {
    var _a, _b, _c2;
    const adapter = this.getAdapter(namespace);
    const connectorId = ConnectorController.getConnectorId(namespace);
    const caipNetwork = this.getCaipNetwork(namespace);
    const connectors = ConnectorController.getConnectors(namespace);
    const connector = connectors.find((c3) => c3.id === connectorId);
    try {
      if (!adapter || !connector) {
        throw new Error(`Adapter or connector not found for namespace ${namespace}`);
      }
      if (!(caipNetwork == null ? void 0 : caipNetwork.id)) {
        throw new Error("CaipNetwork not found");
      }
      const connection = await (adapter == null ? void 0 : adapter.syncConnection({
        namespace,
        id: connector.id,
        chainId: caipNetwork.id,
        rpcUrl: (_c2 = (_b = (_a = caipNetwork == null ? void 0 : caipNetwork.rpcUrls) == null ? void 0 : _a.default) == null ? void 0 : _b.http) == null ? void 0 : _c2[0]
      }));
      if (connection) {
        const accounts = await (adapter == null ? void 0 : adapter.getAccounts({
          namespace,
          id: connector.id
        }));
        if (accounts && accounts.accounts.length > 0) {
          this.setAllAccounts(accounts.accounts, namespace);
        } else {
          this.setAllAccounts([CoreHelperUtil.createAccount(namespace, connection.address, "eoa")], namespace);
        }
        this.syncProvider({ ...connection, chainNamespace: namespace });
        await this.syncAccount({ ...connection, chainNamespace: namespace });
        this.setStatus("connected", namespace);
      } else {
        this.setStatus("disconnected", namespace);
      }
    } catch (e) {
      this.setStatus("disconnected", namespace);
    }
  }
  async syncWalletConnectAccount() {
    const syncTasks = this.chainNamespaces.map(async (chainNamespace) => {
      var _a, _b, _c2, _d, _e5;
      const adapter = this.getAdapter(chainNamespace);
      const namespaceAccounts = ((_d = (_c2 = (_b = (_a = this.universalProvider) == null ? void 0 : _a.session) == null ? void 0 : _b.namespaces) == null ? void 0 : _c2[chainNamespace]) == null ? void 0 : _d.accounts) || [];
      const activeChainId = (_e5 = ChainController.state.activeCaipNetwork) == null ? void 0 : _e5.id;
      const sessionAddress = namespaceAccounts.find((account) => {
        const { chainId } = ParseUtil.parseCaipAddress(account);
        return chainId === (activeChainId == null ? void 0 : activeChainId.toString());
      }) || namespaceAccounts[0];
      if (sessionAddress) {
        const caipAddress = ParseUtil.validateCaipAddress(sessionAddress);
        const { chainId, address } = ParseUtil.parseCaipAddress(caipAddress);
        ProviderUtil.setProviderId(chainNamespace, ConstantsUtil3.CONNECTOR_TYPE_WALLET_CONNECT);
        if (this.caipNetworks && ChainController.state.activeCaipNetwork && (adapter == null ? void 0 : adapter.namespace) !== ConstantsUtil.CHAIN.EVM) {
          const provider = adapter == null ? void 0 : adapter.getWalletConnectProvider({
            caipNetworks: this.getCaipNetworks(),
            provider: this.universalProvider,
            activeCaipNetwork: ChainController.state.activeCaipNetwork
          });
          ProviderUtil.setProvider(chainNamespace, provider);
        } else {
          ProviderUtil.setProvider(chainNamespace, this.universalProvider);
        }
        ConnectorController.setConnectorId(ConstantsUtil.CONNECTOR_ID.WALLET_CONNECT, chainNamespace);
        StorageUtil.addConnectedNamespace(chainNamespace);
        this.syncWalletConnectAccounts(chainNamespace);
        await this.syncAccount({
          address,
          chainId,
          chainNamespace
        });
      } else {
        this.setStatus("disconnected", chainNamespace);
      }
      this.syncConnectedWalletInfo(chainNamespace);
      await ChainController.setApprovedCaipNetworksData(chainNamespace);
    });
    await Promise.all(syncTasks);
  }
  syncWalletConnectAccounts(chainNamespace) {
    var _a, _b, _c2, _d, _e5;
    const addresses = (_e5 = (_d = (_c2 = (_b = (_a = this.universalProvider) == null ? void 0 : _a.session) == null ? void 0 : _b.namespaces) == null ? void 0 : _c2[chainNamespace]) == null ? void 0 : _d.accounts) == null ? void 0 : _e5.map((account) => {
      const { address } = ParseUtil.parseCaipAddress(account);
      return address;
    }).filter((address, index, self) => self.indexOf(address) === index);
    if (addresses) {
      this.setAllAccounts(addresses.map((address) => CoreHelperUtil.createAccount(chainNamespace, address, chainNamespace === "bip122" ? "payment" : "eoa")), chainNamespace);
    }
  }
  syncProvider({ type, provider, id, chainNamespace }) {
    ProviderUtil.setProviderId(chainNamespace, type);
    ProviderUtil.setProvider(chainNamespace, provider);
    ConnectorController.setConnectorId(id, chainNamespace);
  }
  async syncAllAccounts(namespace) {
    const connectorId = ConnectorController.getConnectorId(namespace);
    if (!connectorId) {
      return;
    }
    const adapter = this.getAdapter(namespace);
    const accounts = await (adapter == null ? void 0 : adapter.getAccounts({ namespace, id: connectorId }));
    if (accounts && accounts.accounts.length > 0) {
      this.setAllAccounts(accounts.accounts, namespace);
    }
  }
  async syncAccount(params) {
    var _a, _b;
    const isActiveNamespace = params.chainNamespace === ChainController.state.activeChain;
    const networkOfChain = ChainController.getCaipNetworkByNamespace(params.chainNamespace, params.chainId);
    const { address, chainId, chainNamespace } = params;
    const { chainId: activeChainId } = StorageUtil.getActiveNetworkProps();
    const chainIdToUse = chainId || activeChainId;
    const isUnsupportedNetwork = ((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.name) === ConstantsUtil.UNSUPPORTED_NETWORK_NAME;
    const shouldSupportAllNetworks = ChainController.getNetworkProp("supportsAllNetworks", chainNamespace);
    this.setStatus("connected", chainNamespace);
    if (isUnsupportedNetwork && !shouldSupportAllNetworks) {
      return;
    }
    if (chainIdToUse) {
      let caipNetwork = this.getCaipNetworks().find((n2) => n2.id.toString() === chainIdToUse.toString());
      let fallbackCaipNetwork = this.getCaipNetworks().find((n2) => n2.chainNamespace === chainNamespace);
      if (!shouldSupportAllNetworks && !caipNetwork && !fallbackCaipNetwork) {
        const caipNetworkIds = this.getApprovedCaipNetworkIds() || [];
        const caipNetworkId = caipNetworkIds.find((id) => {
          var _a2;
          return ((_a2 = ParseUtil.parseCaipNetworkId(id)) == null ? void 0 : _a2.chainId) === chainIdToUse.toString();
        });
        const fallBackCaipNetworkId = caipNetworkIds.find((id) => {
          var _a2;
          return ((_a2 = ParseUtil.parseCaipNetworkId(id)) == null ? void 0 : _a2.chainNamespace) === chainNamespace;
        });
        caipNetwork = this.getCaipNetworks().find((n2) => n2.caipNetworkId === caipNetworkId);
        fallbackCaipNetwork = this.getCaipNetworks().find((n2) => n2.caipNetworkId === fallBackCaipNetworkId || // This is a workaround used in Solana network to support deprecated caipNetworkId
        "deprecatedCaipNetworkId" in n2 && n2.deprecatedCaipNetworkId === fallBackCaipNetworkId);
      }
      const network = caipNetwork || fallbackCaipNetwork;
      if ((network == null ? void 0 : network.chainNamespace) === ChainController.state.activeChain) {
        if (OptionsController.state.enableNetworkSwitch && !OptionsController.state.allowUnsupportedChain && ((_b = ChainController.state.activeCaipNetwork) == null ? void 0 : _b.name) === ConstantsUtil.UNSUPPORTED_NETWORK_NAME) {
          ChainController.showUnsupportedChainUI();
        } else {
          this.setCaipNetwork(network);
        }
      } else if (!isActiveNamespace) {
        if (networkOfChain) {
          this.setCaipNetworkOfNamespace(networkOfChain, chainNamespace);
        }
      }
      this.syncConnectedWalletInfo(chainNamespace);
      if (!HelpersUtil.isLowerCaseMatch(address, AccountController.state.address)) {
        this.syncAccountInfo(address, network == null ? void 0 : network.id, chainNamespace);
      }
      if (isActiveNamespace) {
        await this.syncBalance({ address, chainId: network == null ? void 0 : network.id, chainNamespace });
      } else {
        await this.syncBalance({ address, chainId: networkOfChain == null ? void 0 : networkOfChain.id, chainNamespace });
      }
    }
  }
  async syncAccountInfo(address, chainId, chainNamespace) {
    const caipAddress = this.getCaipAddress(chainNamespace);
    const newChainId = chainId || (caipAddress == null ? void 0 : caipAddress.split(":")[1]);
    if (!newChainId) {
      return;
    }
    const newCaipAddress = `${chainNamespace}:${newChainId}:${address}`;
    this.setCaipAddress(newCaipAddress, chainNamespace);
    await this.syncIdentity({
      address,
      chainId: newChainId,
      chainNamespace
    });
  }
  async syncReownName(address, chainNamespace) {
    try {
      const registeredWcNames = await this.getReownName(address);
      if (registeredWcNames[0]) {
        const wcName = registeredWcNames[0];
        this.setProfileName(wcName.name, chainNamespace);
      } else {
        this.setProfileName(null, chainNamespace);
      }
    } catch {
      this.setProfileName(null, chainNamespace);
    }
  }
  syncConnectedWalletInfo(chainNamespace) {
    var _a;
    const connectorId = ConnectorController.getConnectorId(chainNamespace);
    const providerType = ProviderUtil.getProviderId(chainNamespace);
    if (providerType === ConstantsUtil3.CONNECTOR_TYPE_ANNOUNCED || providerType === ConstantsUtil3.CONNECTOR_TYPE_INJECTED) {
      if (connectorId) {
        const connector = this.getConnectors().find((c3) => c3.id === connectorId);
        if (connector) {
          const { info, name, imageUrl } = connector;
          const icon = imageUrl || this.getConnectorImage(connector);
          this.setConnectedWalletInfo({ name, icon, ...info }, chainNamespace);
        }
      }
    } else if (providerType === ConstantsUtil3.CONNECTOR_TYPE_WALLET_CONNECT) {
      const provider = ProviderUtil.getProvider(chainNamespace);
      if (provider == null ? void 0 : provider.session) {
        this.setConnectedWalletInfo({
          ...provider.session.peer.metadata,
          name: provider.session.peer.metadata.name,
          icon: (_a = provider.session.peer.metadata.icons) == null ? void 0 : _a[0]
        }, chainNamespace);
      }
    } else if (connectorId) {
      if (connectorId === ConstantsUtil.CONNECTOR_ID.COINBASE) {
        const connector = this.getConnectors().find((c3) => c3.id === ConstantsUtil.CONNECTOR_ID.COINBASE);
        this.setConnectedWalletInfo({ name: "Coinbase Wallet", icon: this.getConnectorImage(connector) }, chainNamespace);
      }
    }
  }
  async syncBalance(params) {
    const caipNetwork = NetworkUtil.getNetworksByNamespace(this.getCaipNetworks(), params.chainNamespace).find((n2) => {
      var _a;
      return n2.id.toString() === ((_a = params.chainId) == null ? void 0 : _a.toString());
    });
    if (!caipNetwork || !params.chainId) {
      return;
    }
    await this.updateNativeBalance(params.address, params.chainId, params.chainNamespace);
  }
  async ready() {
    await this.readyPromise;
  }
  async updateNativeBalance(address, chainId, namespace) {
    const adapter = this.getAdapter(namespace);
    const caipNetwork = ChainController.getCaipNetworkByNamespace(namespace, chainId);
    if (adapter) {
      const balance = await adapter.getBalance({
        address,
        chainId,
        caipNetwork,
        tokens: this.options.tokens
      });
      this.setBalance(balance.balance, balance.symbol, namespace);
      return balance;
    }
    return void 0;
  }
  // -- Universal Provider ---------------------------------------------------
  async initializeUniversalAdapter() {
    var _a, _b, _c2, _d, _e5, _f, _g, _h, _i3, _j;
    const logger = LoggerUtil.createLogger((error, ...args) => {
      if (error) {
        this.handleAlertError(error);
      }
      console.error(...args);
    });
    const universalProviderOptions = {
      projectId: (_a = this.options) == null ? void 0 : _a.projectId,
      metadata: {
        name: ((_b = this.options) == null ? void 0 : _b.metadata) ? (_c2 = this.options) == null ? void 0 : _c2.metadata.name : "",
        description: ((_d = this.options) == null ? void 0 : _d.metadata) ? (_e5 = this.options) == null ? void 0 : _e5.metadata.description : "",
        url: ((_f = this.options) == null ? void 0 : _f.metadata) ? (_g = this.options) == null ? void 0 : _g.metadata.url : "",
        icons: ((_h = this.options) == null ? void 0 : _h.metadata) ? (_i3 = this.options) == null ? void 0 : _i3.metadata.icons : [""]
      },
      logger
    };
    OptionsController.setManualWCControl(Boolean((_j = this.options) == null ? void 0 : _j.manualWCControl));
    this.universalProvider = this.options.universalProvider ?? await B2.init(universalProviderOptions);
    this.listenWalletConnect();
  }
  listenWalletConnect() {
    if (this.universalProvider) {
      this.universalProvider.on("display_uri", (uri) => {
        ConnectionController.setUri(uri);
      });
      this.universalProvider.on("connect", ConnectionController.finalizeWcConnection);
      this.universalProvider.on("disconnect", () => {
        this.chainNamespaces.forEach((namespace) => {
          this.resetAccount(namespace);
        });
        ConnectionController.resetWcConnection();
      });
      this.universalProvider.on("chainChanged", (chainId) => {
        const caipNetwork = this.getCaipNetworks().find((c3) => c3.id == chainId);
        const currentCaipNetwork = this.getCaipNetwork();
        if (!caipNetwork) {
          this.setUnsupportedNetwork(chainId);
          return;
        }
        if ((currentCaipNetwork == null ? void 0 : currentCaipNetwork.id) !== (caipNetwork == null ? void 0 : caipNetwork.id)) {
          this.setCaipNetwork(caipNetwork);
        }
      });
      this.universalProvider.on("session_event", (callbackData) => {
        if (WcHelpersUtil.isSessionEventData(callbackData)) {
          const { name, data } = callbackData.params.event;
          if (name === "accountsChanged" && Array.isArray(data) && CoreHelperUtil.isCaipAddress(data[0])) {
            this.syncAccount(ParseUtil.parseCaipAddress(data[0]));
          }
        }
      });
    }
  }
  createUniversalProvider() {
    var _a;
    if (!this.universalProviderInitPromise && CoreHelperUtil.isClient() && ((_a = this.options) == null ? void 0 : _a.projectId)) {
      this.universalProviderInitPromise = this.initializeUniversalAdapter();
    }
    return this.universalProviderInitPromise;
  }
  async getUniversalProvider() {
    if (!this.universalProvider) {
      try {
        await this.createUniversalProvider();
      } catch (err) {
        EventsController.sendEvent({
          type: "error",
          event: "INTERNAL_SDK_ERROR",
          properties: {
            errorType: "UniversalProviderInitError",
            errorMessage: err instanceof Error ? err.message : "Unknown",
            uncaught: false
          }
        });
        console.error("AppKit:getUniversalProvider - Cannot create provider", err);
      }
    }
    return this.universalProvider;
  }
  // - Utils -------------------------------------------------------------------
  handleAlertError(error) {
    const matchedUniversalProviderError = Object.entries(ErrorUtil.UniversalProviderErrors).find(([, { message: message2 }]) => error.message.includes(message2));
    const [errorKey, errorValue] = matchedUniversalProviderError ?? [];
    const { message, alertErrorKey } = errorValue ?? {};
    if (errorKey && message && !this.reportedAlertErrors[errorKey]) {
      const alertError = ErrorUtil.ALERT_ERRORS[alertErrorKey];
      if (alertError) {
        AlertController.open(alertError, "error");
        this.reportedAlertErrors[errorKey] = true;
      }
    }
  }
  getAdapter(namespace) {
    var _a;
    if (!namespace) {
      return void 0;
    }
    return (_a = this.chainAdapters) == null ? void 0 : _a[namespace];
  }
  createAdapter(blueprint) {
    var _a;
    if (!blueprint) {
      return;
    }
    const namespace = blueprint.namespace;
    if (!namespace) {
      return;
    }
    this.createClients();
    const adapterBlueprint = blueprint;
    adapterBlueprint.namespace = namespace;
    adapterBlueprint.construct({
      namespace,
      projectId: (_a = this.options) == null ? void 0 : _a.projectId,
      networks: this.getCaipNetworks()
    });
    if (!this.chainNamespaces.includes(namespace)) {
      this.chainNamespaces.push(namespace);
    }
    if (this.chainAdapters) {
      this.chainAdapters[namespace] = adapterBlueprint;
    }
  }
  // -- Public -------------------------------------------------------------------
  async open(options) {
    await this.injectModalUi();
    if (options == null ? void 0 : options.uri) {
      ConnectionController.setUri(options.uri);
    }
    if (options == null ? void 0 : options.arguments) {
      switch (options == null ? void 0 : options.view) {
        case "Swap":
          return ModalController.open({ ...options, data: { swap: options.arguments } });
        default:
      }
    }
    return ModalController.open(options);
  }
  async close() {
    await this.injectModalUi();
    ModalController.close();
  }
  setLoading(loading, namespace) {
    ModalController.setLoading(loading, namespace);
  }
  async disconnect(chainNamespace) {
    await ConnectionController.disconnect(chainNamespace);
  }
  getSIWX() {
    return OptionsController.state.siwx;
  }
  // -- review these -------------------------------------------------------------------
  getError() {
    return "";
  }
  getChainId() {
    var _a;
    return (_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.id;
  }
  async switchNetwork(appKitNetwork) {
    const network = this.getCaipNetworks().find((n2) => n2.id === appKitNetwork.id);
    if (!network) {
      AlertController.open(ErrorUtil.ALERT_ERRORS.SWITCH_NETWORK_NOT_FOUND, "error");
      return;
    }
    await ChainController.switchActiveNetwork(network);
  }
  getWalletProvider() {
    return ChainController.state.activeChain ? ProviderUtil.state.providers[ChainController.state.activeChain] : null;
  }
  getWalletProviderType() {
    return ProviderUtil.getProviderId(ChainController.state.activeChain);
  }
  subscribeProviders(callback) {
    return ProviderUtil.subscribeProviders(callback);
  }
  getThemeMode() {
    return ThemeController.state.themeMode;
  }
  getThemeVariables() {
    return ThemeController.state.themeVariables;
  }
  setThemeMode(themeMode) {
    ThemeController.setThemeMode(themeMode);
    setColorTheme(ThemeController.state.themeMode);
  }
  setTermsConditionsUrl(termsConditionsUrl) {
    OptionsController.setTermsConditionsUrl(termsConditionsUrl);
  }
  setPrivacyPolicyUrl(privacyPolicyUrl) {
    OptionsController.setPrivacyPolicyUrl(privacyPolicyUrl);
  }
  setThemeVariables(themeVariables) {
    ThemeController.setThemeVariables(themeVariables);
    setThemeVariables(ThemeController.state.themeVariables);
  }
  subscribeTheme(callback) {
    return ThemeController.subscribe(callback);
  }
  getWalletInfo() {
    return AccountController.state.connectedWalletInfo;
  }
  getAccount(namespace) {
    var _a;
    const authConnector = ConnectorController.getAuthConnector(namespace);
    const accountState = ChainController.getAccountData(namespace);
    const activeChain = ChainController.state.activeChain;
    const activeConnectorId = StorageUtil.getConnectedConnectorId(namespace || activeChain);
    if (!accountState) {
      return void 0;
    }
    return {
      allAccounts: accountState.allAccounts,
      caipAddress: accountState.caipAddress,
      address: CoreHelperUtil.getPlainAddress(accountState.caipAddress),
      isConnected: Boolean(accountState.caipAddress),
      status: accountState.status,
      embeddedWalletInfo: authConnector && activeConnectorId === ConstantsUtil.CONNECTOR_ID.AUTH ? {
        user: accountState.user ? {
          ...accountState.user,
          /*
           * Getting the username from the chain controller works well for social logins,
           * but Farcaster uses a different connection flow and doesn't emit the username via events.
           * Since the username is stored in local storage before the chain controller updates,
           * it's safe to use the local storage value here.
           */
          username: StorageUtil.getConnectedSocialUsername()
        } : void 0,
        authProvider: accountState.socialProvider || "email",
        accountType: (_a = accountState.preferredAccountTypes) == null ? void 0 : _a[namespace || activeChain],
        isSmartAccountDeployed: Boolean(accountState.smartAccountDeployed)
      } : void 0
    };
  }
  subscribeAccount(callback, namespace) {
    const updateVal = () => {
      const account = this.getAccount(namespace);
      if (!account) {
        return;
      }
      callback(account);
    };
    if (namespace) {
      ChainController.subscribeChainProp("accountState", updateVal, namespace);
    } else {
      ChainController.subscribe(updateVal);
    }
    ConnectorController.subscribe(updateVal);
  }
  subscribeNetwork(callback) {
    return ChainController.subscribe(({ activeCaipNetwork }) => {
      callback({
        caipNetwork: activeCaipNetwork,
        chainId: activeCaipNetwork == null ? void 0 : activeCaipNetwork.id,
        caipNetworkId: activeCaipNetwork == null ? void 0 : activeCaipNetwork.caipNetworkId
      });
    });
  }
  subscribeWalletInfo(callback) {
    return AccountController.subscribeKey("connectedWalletInfo", callback);
  }
  subscribeShouldUpdateToAddress(callback) {
    AccountController.subscribeKey("shouldUpdateToAddress", callback);
  }
  subscribeCaipNetworkChange(callback) {
    ChainController.subscribeKey("activeCaipNetwork", callback);
  }
  getState() {
    return PublicStateController.state;
  }
  subscribeState(callback) {
    return PublicStateController.subscribe(callback);
  }
  showErrorMessage(message) {
    SnackController.showError(message);
  }
  showSuccessMessage(message) {
    SnackController.showSuccess(message);
  }
  getEvent() {
    return { ...EventsController.state };
  }
  subscribeEvents(callback) {
    return EventsController.subscribe(callback);
  }
  replace(route) {
    RouterController.replace(route);
  }
  redirect(route) {
    RouterController.push(route);
  }
  popTransactionStack(status) {
    RouterController.popTransactionStack(status);
  }
  isOpen() {
    return ModalController.state.open;
  }
  isTransactionStackEmpty() {
    return RouterController.state.transactionStack.length === 0;
  }
  static getInstance() {
    return this.instance;
  }
  updateFeatures(newFeatures) {
    OptionsController.setFeatures(newFeatures);
  }
  updateRemoteFeatures(newRemoteFeatures) {
    OptionsController.setRemoteFeatures(newRemoteFeatures);
  }
  updateOptions(newOptions) {
    const currentOptions = OptionsController.state || {};
    const updatedOptions = { ...currentOptions, ...newOptions };
    OptionsController.setOptions(updatedOptions);
  }
  setConnectMethodsOrder(connectMethodsOrder) {
    OptionsController.setConnectMethodsOrder(connectMethodsOrder);
  }
  setWalletFeaturesOrder(walletFeaturesOrder) {
    OptionsController.setWalletFeaturesOrder(walletFeaturesOrder);
  }
  setCollapseWallets(collapseWallets) {
    OptionsController.setCollapseWallets(collapseWallets);
  }
  setSocialsOrder(socialsOrder) {
    OptionsController.setSocialsOrder(socialsOrder);
  }
  getConnectMethodsOrder() {
    return WalletUtil.getConnectOrderMethod(OptionsController.state.features, ConnectorController.getConnectors());
  }
  /**
   * Adds a network to an existing adapter in AppKit.
   * @param namespace - The chain namespace to add the network to (e.g. 'eip155', 'solana')
   * @param network - The network configuration to add
   * @throws Error if adapter for namespace doesn't exist
   */
  addNetwork(namespace, network) {
    if (this.chainAdapters && !this.chainAdapters[namespace]) {
      throw new Error(`Adapter for namespace ${namespace} doesn't exist`);
    }
    const extendedNetwork = this.extendCaipNetwork(network, this.options);
    if (!this.getCaipNetworks().find((n2) => n2.id === extendedNetwork.id)) {
      ChainController.addNetwork(extendedNetwork);
    }
  }
  /**
   * Removes a network from an existing adapter in AppKit.
   * @param namespace - The chain namespace the network belongs to
   * @param networkId - The network ID to remove
   * @throws Error if adapter for namespace doesn't exist or if removing last network
   */
  removeNetwork(namespace, networkId) {
    if (this.chainAdapters && !this.chainAdapters[namespace]) {
      throw new Error(`Adapter for namespace ${namespace} doesn't exist`);
    }
    const networkToRemove = this.getCaipNetworks().find((n2) => n2.id === networkId);
    if (!networkToRemove) {
      return;
    }
    ChainController.removeNetwork(namespace, networkId);
  }
};

// node_modules/@reown/appkit/dist/esm/src/client/appkit-core.js
var isInitialized = false;
var AppKit = class extends AppKitBaseClient {
  // -- Overrides --------------------------------------------------------------
  async open(options) {
    const isConnected = ConnectorController.isConnected();
    if (!isConnected) {
      await super.open(options);
    }
  }
  async close() {
    await super.close();
    if (this.options.manualWCControl) {
      ConnectionController.finalizeWcConnection();
    }
  }
  async syncIdentity(_request) {
    return Promise.resolve();
  }
  async syncBalance(_params) {
    return Promise.resolve();
  }
  async injectModalUi() {
    if (!isInitialized && CoreHelperUtil.isClient()) {
      await import("./basic-P2VNY32F.js");
      await import("./w3m-modal-OY443EZU.js");
      const isElementCreated = document.querySelector("w3m-modal");
      if (!isElementCreated) {
        const modal = document.createElement("w3m-modal");
        if (!OptionsController.state.disableAppend && !OptionsController.state.enableEmbedded) {
          document.body.insertAdjacentElement("beforeend", modal);
        }
      }
      isInitialized = true;
    }
  }
};

// node_modules/@reown/appkit/dist/esm/exports/constants.js
var PACKAGE_VERSION = "1.7.8";

// node_modules/@reown/appkit/dist/esm/exports/core.js
function createAppKit(options) {
  return new AppKit({
    ...options,
    basic: true,
    sdkVersion: `html-core-${PACKAGE_VERSION}`
  });
}
export {
  AppKit,
  createAppKit
};
/*! Bundled license information:

@walletconnect/utils/dist/index.es.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)
*/
//# sourceMappingURL=core-XJVMZWR4.js.map
