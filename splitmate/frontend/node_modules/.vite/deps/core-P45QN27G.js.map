{"version": 3, "sources": ["../../@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/accounts/utils/publicKeyToAddress.ts", "../../@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/utils/signature/recoverPublicKey.ts", "../../@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/utils/signature/recoverAddress.ts", "../../@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/utils/formatters/transaction.ts", "../../@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/utils/formatters/block.ts", "../../@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/actions/public/getTransactionCount.ts", "../../@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/constants/blob.ts", "../../@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/utils/formatters/log.ts", "../../@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/actions/wallet/sendTransaction.ts", "../../@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/utils/promise/withDedupe.ts", "../../@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/utils/rpc/id.ts", "../../@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/utils/formatters/transactionReceipt.ts", "../../@reown/appkit/node_modules/@noble/hashes/src/ripemd160.ts", "../../@reown/appkit/node_modules/@walletconnect/utils/node_modules/viem/utils/nonceManager.ts", "../../@reown/appkit/node_modules/ox/core/version.ts", "../../@reown/appkit/node_modules/ox/core/internal/errors.ts", "../../@reown/appkit/node_modules/ox/core/Errors.ts", "../../@reown/appkit/node_modules/ox/core/internal/bytes.ts", "../../@reown/appkit/node_modules/ox/core/internal/hex.ts", "../../@reown/appkit/node_modules/ox/core/Bytes.ts", "../../@reown/appkit/node_modules/ox/core/Hex.ts", "../../@reown/appkit/node_modules/ox/core/Hash.ts", "../../@reown/appkit/node_modules/ox/core/internal/lru.ts", "../../@reown/appkit/node_modules/ox/core/Caches.ts", "../../@reown/appkit/node_modules/ox/core/Address.ts", "../../@reown/appkit/node_modules/ox/core/Solidity.ts", "../../@reown/appkit/node_modules/ox/core/internal/cursor.ts", "../../@reown/appkit/node_modules/ox/core/AbiParameters.ts", "../../@reown/appkit-controllers/src/utils/ConnectorControllerUtil.ts", "../../@reown/appkit-controllers/src/utils/ChainControllerUtil.ts", "../../@reown/appkit/src/utils/ConstantsUtil.ts", "../../@reown/appkit/src/networks/utils.ts", "../../@reown/appkit/src/networks/solana/solana.ts", "../../@reown/appkit/src/networks/solana/solanaDevnet.ts", "../../@reown/appkit/src/networks/solana/solanaTestnet.ts", "../../@reown/appkit/src/networks/bitcoin.ts", "../../@reown/appkit/src/utils/HelpersUtil.ts", "../../@reown/appkit/src/connectors/WalletConnectConnector.ts", "../../@reown/appkit/src/adapters/ChainAdapterBlueprint.ts", "../../@reown/appkit/src/universal-adapter/client.ts", "../../@reown/appkit/src/utils/ConfigUtil.ts", "../../@reown/appkit/src/client/appkit-base-client.ts", "../../@reown/appkit/src/client/appkit-core.ts", "../../@reown/appkit/exports/constants.ts", "../../@reown/appkit/exports/core.ts"], "sourcesContent": ["import type { Address } from 'abitype'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Hex } from '../../types/misc.js'\nimport {\n  type ChecksumAddressErrorType,\n  checksumAddress,\n} from '../../utils/address/getAddress.js'\nimport {\n  type Keccak256ErrorType,\n  keccak256,\n} from '../../utils/hash/keccak256.js'\n\nexport type PublicKeyToAddressErrorType =\n  | ChecksumAddressErrorType\n  | Keccak256ErrorType\n  | ErrorType\n\n/**\n * @description Converts an ECDSA public key to an address.\n *\n * @param publicKey The public key to convert.\n *\n * @returns The address.\n */\nexport function publicKeyToAddress(publicKey: Hex): Address {\n  const address = keccak256(`0x${publicKey.substring(4)}`).substring(26)\n  return checksumAddress(`0x${address}`) as Address\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex, Signature } from '../../types/misc.js'\nimport { type IsHexErrorType, isHex } from '../data/isHex.js'\nimport {\n  type HexToNumberErrorType,\n  hexToBigInt,\n  hexToNumber,\n} from '../encoding/fromHex.js'\nimport { toHex } from '../encoding/toHex.js'\n\nexport type RecoverPublicKeyParameters = {\n  hash: Hex | ByteArray\n  signature: Hex | ByteArray | Signature\n}\n\nexport type RecoverPublicKeyReturnType = Hex\n\nexport type RecoverPublicKeyErrorType =\n  | HexToNumberErrorType\n  | IsHexErrorType\n  | ErrorType\n\nexport async function recoverPublicKey({\n  hash,\n  signature,\n}: RecoverPublicKeyParameters): Promise<RecoverPublicKeyReturnType> {\n  const hashHex = isHex(hash) ? hash : toHex(hash)\n\n  const { secp256k1 } = await import('@noble/curves/secp256k1')\n  const signature_ = (() => {\n    // typeof signature: `Signature`\n    if (typeof signature === 'object' && 'r' in signature && 's' in signature) {\n      const { r, s, v, yParity } = signature\n      const yParityOrV = Number(yParity ?? v)!\n      const recoveryBit = toRecoveryBit(yParityOrV)\n      return new secp256k1.Signature(\n        hexToBigInt(r),\n        hexToBigInt(s),\n      ).addRecoveryBit(recoveryBit)\n    }\n\n    // typeof signature: `Hex | ByteArray`\n    const signatureHex = isHex(signature) ? signature : toHex(signature)\n    const yParityOrV = hexToNumber(`0x${signatureHex.slice(130)}`)\n    const recoveryBit = toRecoveryBit(yParityOrV)\n    return secp256k1.Signature.fromCompact(\n      signatureHex.substring(2, 130),\n    ).addRecoveryBit(recoveryBit)\n  })()\n\n  const publicKey = signature_\n    .recoverPublicKey(hashHex.substring(2))\n    .toHex(false)\n  return `0x${publicKey}`\n}\n\nfunction toRecoveryBit(yParityOrV: number) {\n  if (yParityOrV === 0 || yParityOrV === 1) return yParityOrV\n  if (yParityOrV === 27) return 0\n  if (yParityOrV === 28) return 1\n  throw new Error('Invalid yParityOrV value')\n}\n", "import type { Address } from 'abitype'\n\nimport { publicKeyToAddress } from '../../accounts/utils/publicKeyToAddress.js'\nimport type { ByteArray, Hex, Signature } from '../../types/misc.js'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport { recoverPublicKey } from './recoverPublicKey.js'\n\nexport type RecoverAddressParameters = {\n  hash: Hex | ByteArray\n  signature: Hex | ByteArray | Signature\n}\n\nexport type RecoverAddressReturnType = Address\n\nexport type RecoverAddressErrorType = ErrorType\n\nexport async function recoverAddress({\n  hash,\n  signature,\n}: RecoverAddressParameters): Promise<RecoverAddressReturnType> {\n  return publicKeyToAddress(await recoverPublicKey({ hash: hash, signature }))\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { SignedAuthorizationList } from '../../experimental/eip7702/types/authorization.js'\nimport type { RpcAuthorizationList } from '../../experimental/eip7702/types/rpc.js'\nimport type { BlockTag } from '../../types/block.js'\nimport type { Chain } from '../../types/chain.js'\nimport type {\n  ExtractChainFormatterExclude,\n  ExtractChainFormatterReturnType,\n} from '../../types/chain.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { RpcTransaction } from '../../types/rpc.js'\nimport type { Transaction, TransactionType } from '../../types/transaction.js'\nimport type { ExactPartial, UnionLooseOmit } from '../../types/utils.js'\nimport { hexToNumber } from '../encoding/fromHex.js'\nimport { type DefineFormatterErrorType, defineFormatter } from './formatter.js'\n\ntype TransactionPendingDependencies =\n  | 'blockHash'\n  | 'blockNumber'\n  | 'transactionIndex'\n\nexport type FormattedTransaction<\n  chain extends Chain | undefined = undefined,\n  blockTag extends BlockTag = BlockTag,\n  _FormatterReturnType = ExtractChainFormatterReturnType<\n    chain,\n    'transaction',\n    Transaction\n  >,\n  _ExcludedPendingDependencies extends string = TransactionPendingDependencies &\n    ExtractChainFormatterExclude<chain, 'transaction'>,\n> = UnionLooseOmit<_FormatterReturnType, TransactionPendingDependencies> & {\n  [_K in _ExcludedPendingDependencies]: never\n} & Pick<\n    Transaction<bigint, number, blockTag extends 'pending' ? true : false>,\n    TransactionPendingDependencies\n  >\n\nexport const transactionType = {\n  '0x0': 'legacy',\n  '0x1': 'eip2930',\n  '0x2': 'eip1559',\n  '0x3': 'eip4844',\n  '0x4': 'eip7702',\n} as const satisfies Record<Hex, TransactionType>\n\nexport type FormatTransactionErrorType = ErrorType\n\nexport function formatTransaction(transaction: ExactPartial<RpcTransaction>) {\n  const transaction_ = {\n    ...transaction,\n    blockHash: transaction.blockHash ? transaction.blockHash : null,\n    blockNumber: transaction.blockNumber\n      ? BigInt(transaction.blockNumber)\n      : null,\n    chainId: transaction.chainId ? hexToNumber(transaction.chainId) : undefined,\n    gas: transaction.gas ? BigInt(transaction.gas) : undefined,\n    gasPrice: transaction.gasPrice ? BigInt(transaction.gasPrice) : undefined,\n    maxFeePerBlobGas: transaction.maxFeePerBlobGas\n      ? BigInt(transaction.maxFeePerBlobGas)\n      : undefined,\n    maxFeePerGas: transaction.maxFeePerGas\n      ? BigInt(transaction.maxFeePerGas)\n      : undefined,\n    maxPriorityFeePerGas: transaction.maxPriorityFeePerGas\n      ? BigInt(transaction.maxPriorityFeePerGas)\n      : undefined,\n    nonce: transaction.nonce ? hexToNumber(transaction.nonce) : undefined,\n    to: transaction.to ? transaction.to : null,\n    transactionIndex: transaction.transactionIndex\n      ? Number(transaction.transactionIndex)\n      : null,\n    type: transaction.type\n      ? (transactionType as any)[transaction.type]\n      : undefined,\n    typeHex: transaction.type ? transaction.type : undefined,\n    value: transaction.value ? BigInt(transaction.value) : undefined,\n    v: transaction.v ? BigInt(transaction.v) : undefined,\n  } as Transaction\n\n  if (transaction.authorizationList)\n    transaction_.authorizationList = formatAuthorizationList(\n      transaction.authorizationList,\n    )\n\n  transaction_.yParity = (() => {\n    // If `yParity` is provided, we will use it.\n    if (transaction.yParity) return Number(transaction.yParity)\n\n    // If no `yParity` provided, try derive from `v`.\n    if (typeof transaction_.v === 'bigint') {\n      if (transaction_.v === 0n || transaction_.v === 27n) return 0\n      if (transaction_.v === 1n || transaction_.v === 28n) return 1\n      if (transaction_.v >= 35n) return transaction_.v % 2n === 0n ? 1 : 0\n    }\n\n    return undefined\n  })()\n\n  if (transaction_.type === 'legacy') {\n    delete transaction_.accessList\n    delete transaction_.maxFeePerBlobGas\n    delete transaction_.maxFeePerGas\n    delete transaction_.maxPriorityFeePerGas\n    delete transaction_.yParity\n  }\n  if (transaction_.type === 'eip2930') {\n    delete transaction_.maxFeePerBlobGas\n    delete transaction_.maxFeePerGas\n    delete transaction_.maxPriorityFeePerGas\n  }\n  if (transaction_.type === 'eip1559') {\n    delete transaction_.maxFeePerBlobGas\n  }\n  return transaction_\n}\n\nexport type DefineTransactionErrorType = DefineFormatterErrorType | ErrorType\n\nexport const defineTransaction = /*#__PURE__*/ defineFormatter(\n  'transaction',\n  formatTransaction,\n)\n\n//////////////////////////////////////////////////////////////////////////////\n\nfunction formatAuthorizationList(\n  authorizationList: RpcAuthorizationList,\n): SignedAuthorizationList {\n  return authorizationList.map((authorization) => ({\n    contractAddress: (authorization as any).address,\n    chainId: Number(authorization.chainId),\n    nonce: Number(authorization.nonce),\n    r: authorization.r,\n    s: authorization.s,\n    yParity: Number(authorization.yParity),\n  })) as SignedAuthorizationList\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Block, BlockTag } from '../../types/block.js'\nimport type { Chain } from '../../types/chain.js'\nimport type {\n  ExtractChainFormatterExclude,\n  ExtractChainFormatterReturnType,\n} from '../../types/chain.js'\nimport type { Hash } from '../../types/misc.js'\nimport type { RpcBlock } from '../../types/rpc.js'\nimport type { ExactPartial, Prettify } from '../../types/utils.js'\n\nimport { type DefineFormatterErrorType, defineFormatter } from './formatter.js'\nimport { type FormattedTransaction, formatTransaction } from './transaction.js'\n\ntype BlockPendingDependencies = 'hash' | 'logsBloom' | 'nonce' | 'number'\n\nexport type FormattedBlock<\n  chain extends Chain | undefined = undefined,\n  includeTransactions extends boolean = boolean,\n  blockTag extends BlockTag = BlockTag,\n  _FormatterReturnType = ExtractChainFormatterReturnType<\n    chain,\n    'block',\n    Block<bigint, includeTransactions>\n  >,\n  _ExcludedPendingDependencies extends string = BlockPendingDependencies &\n    ExtractChainFormatterExclude<chain, 'block'>,\n  _Formatted = Omit<_FormatterReturnType, BlockPendingDependencies> & {\n    [_key in _ExcludedPendingDependencies]: never\n  } & Pick<\n      Block<bigint, includeTransactions, blockTag>,\n      BlockPendingDependencies\n    >,\n  _Transactions = includeTransactions extends true\n    ? Prettify<FormattedTransaction<chain, blockTag>>[]\n    : Hash[],\n> = Omit<_Formatted, 'transactions'> & {\n  transactions: _Transactions\n}\n\nexport type FormatBlockErrorType = ErrorType\n\nexport function formatBlock(block: ExactPartial<RpcBlock>) {\n  const transactions = (block.transactions ?? []).map((transaction) => {\n    if (typeof transaction === 'string') return transaction\n    return formatTransaction(transaction)\n  })\n  return {\n    ...block,\n    baseFeePerGas: block.baseFeePerGas ? BigInt(block.baseFeePerGas) : null,\n    blobGasUsed: block.blobGasUsed ? BigInt(block.blobGasUsed) : undefined,\n    difficulty: block.difficulty ? BigInt(block.difficulty) : undefined,\n    excessBlobGas: block.excessBlobGas\n      ? BigInt(block.excessBlobGas)\n      : undefined,\n    gasLimit: block.gasLimit ? BigInt(block.gasLimit) : undefined,\n    gasUsed: block.gasUsed ? BigInt(block.gasUsed) : undefined,\n    hash: block.hash ? block.hash : null,\n    logsBloom: block.logsBloom ? block.logsBloom : null,\n    nonce: block.nonce ? block.nonce : null,\n    number: block.number ? BigInt(block.number) : null,\n    size: block.size ? BigInt(block.size) : undefined,\n    timestamp: block.timestamp ? BigInt(block.timestamp) : undefined,\n    transactions,\n    totalDifficulty: block.totalDifficulty\n      ? BigInt(block.totalDifficulty)\n      : null,\n  } as Block\n}\n\nexport type DefineBlockErrorType = DefineFormatterErrorType | ErrorType\n\nexport const defineBlock = /*#__PURE__*/ defineFormatter('block', formatBlock)\n", "import type { Address } from 'abitype'\n\nimport type { Account } from '../../accounts/types.js'\nimport type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { BlockTag } from '../../types/block.js'\nimport type { Chain } from '../../types/chain.js'\nimport type { RequestErrorType } from '../../utils/buildRequest.js'\nimport {\n  type HexToNumberErrorType,\n  hexToNumber,\n} from '../../utils/encoding/fromHex.js'\nimport {\n  type NumberToHexErrorType,\n  numberToHex,\n} from '../../utils/encoding/toHex.js'\n\nexport type GetTransactionCountParameters = {\n  /** The account address. */\n  address: Address\n} & (\n  | {\n      /** The block number. */\n      blockNumber?: bigint | undefined\n      blockTag?: undefined\n    }\n  | {\n      blockNumber?: undefined\n      /** The block tag. Defaults to 'latest'. */\n      blockTag?: BlockTag | undefined\n    }\n)\nexport type GetTransactionCountReturnType = number\n\nexport type GetTransactionCountErrorType =\n  | RequestErrorType\n  | NumberToHexErrorType\n  | HexToNumberErrorType\n  | ErrorType\n\n/**\n * Returns the number of [Transactions](https://viem.sh/docs/glossary/terms#transaction) an Account has sent.\n *\n * - Docs: https://viem.sh/docs/actions/public/getTransactionCount\n * - JSON-RPC Methods: [`eth_getTransactionCount`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_gettransactioncount)\n *\n * @param client - Client to use\n * @param parameters - {@link GetTransactionCountParameters}\n * @returns The number of transactions an account has sent. {@link GetTransactionCountReturnType}\n *\n * @example\n * import { createPublicClient, http } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { getTransactionCount } from 'viem/public'\n *\n * const client = createPublicClient({\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const transactionCount = await getTransactionCount(client, {\n *   address: '******************************************',\n * })\n */\nexport async function getTransactionCount<\n  chain extends Chain | undefined,\n  account extends Account | undefined,\n>(\n  client: Client<Transport, chain, account>,\n  { address, blockTag = 'latest', blockNumber }: GetTransactionCountParameters,\n): Promise<GetTransactionCountReturnType> {\n  const count = await client.request(\n    {\n      method: 'eth_getTransactionCount',\n      params: [address, blockNumber ? numberToHex(blockNumber) : blockTag],\n    },\n    { dedupe: Boolean(blockNumber) },\n  )\n  return hexToNumber(count)\n}\n", "// https://github.com/ethereum/EIPs/blob/master/EIPS/eip-4844.md#parameters\n\n/** Blob limit per transaction. */\nconst blobsPerTransaction = 6\n\n/** The number of bytes in a BLS scalar field element. */\nexport const bytesPerFieldElement = 32\n\n/** The number of field elements in a blob. */\nexport const fieldElementsPerBlob = 4096\n\n/** The number of bytes in a blob. */\nexport const bytesPerBlob = bytesPerFieldElement * fieldElementsPerBlob\n\n/** Blob bytes limit per transaction. */\nexport const maxBytesPerTransaction =\n  bytesPerBlob * blobsPerTransaction -\n  // terminator byte (0x80).\n  1 -\n  // zero byte (0x00) appended to each field element.\n  1 * fieldElementsPerBlob * blobsPerTransaction\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Log } from '../../types/log.js'\nimport type { RpcLog } from '../../types/rpc.js'\nimport type { ExactPartial } from '../../types/utils.js'\n\nexport type FormatLogErrorType = ErrorType\n\nexport function formatLog(\n  log: ExactPartial<RpcLog>,\n  {\n    args,\n    eventName,\n  }: { args?: unknown | undefined; eventName?: string | undefined } = {},\n) {\n  return {\n    ...log,\n    blockHash: log.blockHash ? log.blockHash : null,\n    blockNumber: log.blockNumber ? BigInt(log.blockNumber) : null,\n    logIndex: log.logIndex ? Number(log.logIndex) : null,\n    transactionHash: log.transactionHash ? log.transactionHash : null,\n    transactionIndex: log.transactionIndex\n      ? Number(log.transactionIndex)\n      : null,\n    ...(eventName ? { args, eventName } : {}),\n  } as Log\n}\n", "import type { Address } from 'abitype'\n\nimport type { Account } from '../../accounts/types.js'\nimport {\n  type ParseAccountErrorType,\n  parseAccount,\n} from '../../accounts/utils/parseAccount.js'\nimport type { SignTransactionErrorType } from '../../accounts/utils/signTransaction.js'\nimport type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport {\n  AccountNotFoundError,\n  type AccountNotFoundErrorType,\n  AccountTypeNotSupportedError,\n  type AccountTypeNotSupportedErrorType,\n} from '../../errors/account.js'\nimport { BaseError } from '../../errors/base.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport {\n  type RecoverAuthorizationAddressErrorType,\n  recoverAuthorizationAddress,\n} from '../../experimental/eip7702/utils/recoverAuthorizationAddress.js'\nimport type { GetAccountParameter } from '../../types/account.js'\nimport type { Chain, DeriveChain } from '../../types/chain.js'\nimport type { GetChainParameter } from '../../types/chain.js'\nimport type { GetTransactionRequestKzgParameter } from '../../types/kzg.js'\nimport type { Hash } from '../../types/misc.js'\nimport type { TransactionRequest } from '../../types/transaction.js'\nimport type { UnionOmit } from '../../types/utils.js'\nimport type { RequestErrorType } from '../../utils/buildRequest.js'\nimport {\n  type AssertCurrentChainErrorType,\n  assertCurrentChain,\n} from '../../utils/chain/assertCurrentChain.js'\nimport {\n  type GetTransactionErrorReturnType,\n  getTransactionError,\n} from '../../utils/errors/getTransactionError.js'\nimport { extract } from '../../utils/formatters/extract.js'\nimport {\n  type FormattedTransactionRequest,\n  formatTransactionRequest,\n} from '../../utils/formatters/transactionRequest.js'\nimport { getAction } from '../../utils/getAction.js'\nimport { LruMap } from '../../utils/lru.js'\nimport {\n  type AssertRequestErrorType,\n  type AssertRequestParameters,\n  assertRequest,\n} from '../../utils/transaction/assertRequest.js'\nimport { type GetChainIdErrorType, getChainId } from '../public/getChainId.js'\nimport {\n  type PrepareTransactionRequestErrorType,\n  defaultParameters,\n  prepareTransactionRequest,\n} from './prepareTransactionRequest.js'\nimport {\n  type SendRawTransactionErrorType,\n  sendRawTransaction,\n} from './sendRawTransaction.js'\n\nconst supportsWalletNamespace = new LruMap<boolean>(128)\n\nexport type SendTransactionRequest<\n  chain extends Chain | undefined = Chain | undefined,\n  chainOverride extends Chain | undefined = Chain | undefined,\n  ///\n  _derivedChain extends Chain | undefined = DeriveChain<chain, chainOverride>,\n> = UnionOmit<FormattedTransactionRequest<_derivedChain>, 'from'> &\n  GetTransactionRequestKzgParameter\n\nexport type SendTransactionParameters<\n  chain extends Chain | undefined = Chain | undefined,\n  account extends Account | undefined = Account | undefined,\n  chainOverride extends Chain | undefined = Chain | undefined,\n  request extends SendTransactionRequest<\n    chain,\n    chainOverride\n  > = SendTransactionRequest<chain, chainOverride>,\n> = request &\n  GetAccountParameter<account, Account | Address, true, true> &\n  GetChainParameter<chain, chainOverride> &\n  GetTransactionRequestKzgParameter<request>\n\nexport type SendTransactionReturnType = Hash\n\nexport type SendTransactionErrorType =\n  | ParseAccountErrorType\n  | GetTransactionErrorReturnType<\n      | AccountNotFoundErrorType\n      | AccountTypeNotSupportedErrorType\n      | AssertCurrentChainErrorType\n      | AssertRequestErrorType\n      | GetChainIdErrorType\n      | PrepareTransactionRequestErrorType\n      | SendRawTransactionErrorType\n      | RecoverAuthorizationAddressErrorType\n      | SignTransactionErrorType\n      | RequestErrorType\n    >\n  | ErrorType\n\n/**\n * Creates, signs, and sends a new transaction to the network.\n *\n * - Docs: https://viem.sh/docs/actions/wallet/sendTransaction\n * - Examples: https://stackblitz.com/github/wevm/viem/tree/main/examples/transactions_sending-transactions\n * - JSON-RPC Methods:\n *   - JSON-RPC Accounts: [`eth_sendTransaction`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_sendtransaction)\n *   - Local Accounts: [`eth_sendRawTransaction`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_sendrawtransaction)\n *\n * @param client - Client to use\n * @param parameters - {@link SendTransactionParameters}\n * @returns The [Transaction](https://viem.sh/docs/glossary/terms#transaction) hash. {@link SendTransactionReturnType}\n *\n * @example\n * import { createWalletClient, custom } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { sendTransaction } from 'viem/wallet'\n *\n * const client = createWalletClient({\n *   chain: mainnet,\n *   transport: custom(window.ethereum),\n * })\n * const hash = await sendTransaction(client, {\n *   account: '******************************************',\n *   to: '******************************************',\n *   value: 1000000000000000000n,\n * })\n *\n * @example\n * // Account Hoisting\n * import { createWalletClient, http } from 'viem'\n * import { privateKeyToAccount } from 'viem/accounts'\n * import { mainnet } from 'viem/chains'\n * import { sendTransaction } from 'viem/wallet'\n *\n * const client = createWalletClient({\n *   account: privateKeyToAccount('0x…'),\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const hash = await sendTransaction(client, {\n *   to: '******************************************',\n *   value: 1000000000000000000n,\n * })\n */\nexport async function sendTransaction<\n  chain extends Chain | undefined,\n  account extends Account | undefined,\n  const request extends SendTransactionRequest<chain, chainOverride>,\n  chainOverride extends Chain | undefined = undefined,\n>(\n  client: Client<Transport, chain, account>,\n  parameters: SendTransactionParameters<chain, account, chainOverride, request>,\n): Promise<SendTransactionReturnType> {\n  const {\n    account: account_ = client.account,\n    chain = client.chain,\n    accessList,\n    authorizationList,\n    blobs,\n    data,\n    gas,\n    gasPrice,\n    maxFeePerBlobGas,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    nonce,\n    value,\n    ...rest\n  } = parameters\n\n  if (typeof account_ === 'undefined')\n    throw new AccountNotFoundError({\n      docsPath: '/docs/actions/wallet/sendTransaction',\n    })\n  const account = account_ ? parseAccount(account_) : null\n\n  try {\n    assertRequest(parameters as AssertRequestParameters)\n\n    const to = await (async () => {\n      // If `to` exists on the parameters, use that.\n      if (parameters.to) return parameters.to\n\n      // If no `to` exists, and we are sending a EIP-7702 transaction, use the\n      // address of the first authorization in the list.\n      if (authorizationList && authorizationList.length > 0)\n        return await recoverAuthorizationAddress({\n          authorization: authorizationList[0],\n        }).catch(() => {\n          throw new BaseError(\n            '`to` is required. Could not infer from `authorizationList`.',\n          )\n        })\n\n      // Otherwise, we are sending a deployment transaction.\n      return undefined\n    })()\n\n    if (account?.type === 'json-rpc' || account === null) {\n      let chainId: number | undefined\n      if (chain !== null) {\n        chainId = await getAction(client, getChainId, 'getChainId')({})\n        assertCurrentChain({\n          currentChainId: chainId,\n          chain,\n        })\n      }\n\n      const chainFormat = client.chain?.formatters?.transactionRequest?.format\n      const format = chainFormat || formatTransactionRequest\n\n      const request = format({\n        // Pick out extra data that might exist on the chain's transaction request type.\n        ...extract(rest, { format: chainFormat }),\n        accessList,\n        authorizationList,\n        blobs,\n        chainId,\n        data,\n        from: account?.address,\n        gas,\n        gasPrice,\n        maxFeePerBlobGas,\n        maxFeePerGas,\n        maxPriorityFeePerGas,\n        nonce,\n        to,\n        value,\n      } as TransactionRequest)\n\n      const isWalletNamespaceSupported = supportsWalletNamespace.get(client.uid)\n      const method = isWalletNamespaceSupported\n        ? 'wallet_sendTransaction'\n        : 'eth_sendTransaction'\n\n      try {\n        return await client.request(\n          {\n            method,\n            params: [request],\n          },\n          { retryCount: 0 },\n        )\n      } catch (e) {\n        if (isWalletNamespaceSupported === false) throw e\n\n        const error = e as BaseError\n        // If the transport does not support the method or input, attempt to use the\n        // `wallet_sendTransaction` method.\n        if (\n          error.name === 'InvalidInputRpcError' ||\n          error.name === 'InvalidParamsRpcError' ||\n          error.name === 'MethodNotFoundRpcError' ||\n          error.name === 'MethodNotSupportedRpcError'\n        ) {\n          return await client\n            .request(\n              {\n                method: 'wallet_sendTransaction',\n                params: [request],\n              },\n              { retryCount: 0 },\n            )\n            .then((hash) => {\n              supportsWalletNamespace.set(client.uid, true)\n              return hash\n            })\n            .catch((e) => {\n              const walletNamespaceError = e as BaseError\n              if (\n                walletNamespaceError.name === 'MethodNotFoundRpcError' ||\n                walletNamespaceError.name === 'MethodNotSupportedRpcError'\n              ) {\n                supportsWalletNamespace.set(client.uid, false)\n                throw error\n              }\n\n              throw walletNamespaceError\n            })\n        }\n\n        throw error\n      }\n    }\n\n    if (account?.type === 'local') {\n      // Prepare the request for signing (assign appropriate fees, etc.)\n      const request = await getAction(\n        client,\n        prepareTransactionRequest,\n        'prepareTransactionRequest',\n      )({\n        account,\n        accessList,\n        authorizationList,\n        blobs,\n        chain,\n        data,\n        gas,\n        gasPrice,\n        maxFeePerBlobGas,\n        maxFeePerGas,\n        maxPriorityFeePerGas,\n        nonce,\n        nonceManager: account.nonceManager,\n        parameters: [...defaultParameters, 'sidecars'],\n        value,\n        ...rest,\n        to,\n      } as any)\n\n      const serializer = chain?.serializers?.transaction\n      const serializedTransaction = (await account.signTransaction(request, {\n        serializer,\n      })) as Hash\n      return await getAction(\n        client,\n        sendRawTransaction,\n        'sendRawTransaction',\n      )({\n        serializedTransaction,\n      })\n    }\n\n    if (account?.type === 'smart')\n      throw new AccountTypeNotSupportedError({\n        metaMessages: [\n          'Consider using the `sendUserOperation` Action instead.',\n        ],\n        docsPath: '/docs/actions/bundler/sendUserOperation',\n        type: 'smart',\n      })\n\n    throw new AccountTypeNotSupportedError({\n      docsPath: '/docs/actions/wallet/sendTransaction',\n      type: (account as any)?.type,\n    })\n  } catch (err) {\n    if (err instanceof AccountTypeNotSupportedError) throw err\n    throw getTransactionError(err as BaseError, {\n      ...parameters,\n      account,\n      chain: parameters.chain || undefined,\n    })\n  }\n}\n", "import { LruMap } from '../lru.js'\n\n/** @internal */\nexport const promiseCache = /*#__PURE__*/ new LruMap<Promise<any>>(8192)\n\ntype WithDedupeOptions = {\n  enabled?: boolean | undefined\n  id?: string | undefined\n}\n\n/** Deduplicates in-flight promises. */\nexport function withDedupe<data>(\n  fn: () => Promise<data>,\n  { enabled = true, id }: WithDedupeOptions,\n): Promise<data> {\n  if (!enabled || !id) return fn()\n  if (promiseCache.get(id)) return promiseCache.get(id)!\n  const promise = fn().finally(() => promiseCache.delete(id))\n  promiseCache.set(id, promise)\n  return promise\n}\n", "function createIdStore() {\n  return {\n    current: 0,\n    take() {\n      return this.current++\n    },\n    reset() {\n      this.current = 0\n    },\n  }\n}\n\nexport const idCache = /*#__PURE__*/ createIdStore()\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type {\n  Chain,\n  ExtractChainFormatterReturnType,\n} from '../../types/chain.js'\nimport type { RpcTransactionReceipt } from '../../types/rpc.js'\nimport type { TransactionReceipt } from '../../types/transaction.js'\nimport type { ExactPartial } from '../../types/utils.js'\nimport { hexToNumber } from '../encoding/fromHex.js'\n\nimport { type DefineFormatterErrorType, defineFormatter } from './formatter.js'\nimport { formatLog } from './log.js'\nimport { transactionType } from './transaction.js'\n\nexport type FormattedTransactionReceipt<\n  chain extends Chain | undefined = undefined,\n> = ExtractChainFormatterReturnType<\n  chain,\n  'transactionReceipt',\n  TransactionReceipt\n>\n\nexport const receiptStatuses = {\n  '0x0': 'reverted',\n  '0x1': 'success',\n} as const\n\nexport type FormatTransactionReceiptErrorType = ErrorType\n\nexport function formatTransactionReceipt(\n  transactionReceipt: ExactPartial<RpcTransactionReceipt>,\n) {\n  const receipt = {\n    ...transactionReceipt,\n    blockNumber: transactionReceipt.blockNumber\n      ? BigInt(transactionReceipt.blockNumber)\n      : null,\n    contractAddress: transactionReceipt.contractAddress\n      ? transactionReceipt.contractAddress\n      : null,\n    cumulativeGasUsed: transactionReceipt.cumulativeGasUsed\n      ? BigInt(transactionReceipt.cumulativeGasUsed)\n      : null,\n    effectiveGasPrice: transactionReceipt.effectiveGasPrice\n      ? BigInt(transactionReceipt.effectiveGasPrice)\n      : null,\n    gasUsed: transactionReceipt.gasUsed\n      ? BigInt(transactionReceipt.gasUsed)\n      : null,\n    logs: transactionReceipt.logs\n      ? transactionReceipt.logs.map((log) => formatLog(log))\n      : null,\n    to: transactionReceipt.to ? transactionReceipt.to : null,\n    transactionIndex: transactionReceipt.transactionIndex\n      ? hexToNumber(transactionReceipt.transactionIndex)\n      : null,\n    status: transactionReceipt.status\n      ? receiptStatuses[transactionReceipt.status]\n      : null,\n    type: transactionReceipt.type\n      ? transactionType[\n          transactionReceipt.type as keyof typeof transactionType\n        ] || transactionReceipt.type\n      : null,\n  } as TransactionReceipt\n\n  if (transactionReceipt.blobGasPrice)\n    receipt.blobGasPrice = BigInt(transactionReceipt.blobGasPrice)\n  if (transactionReceipt.blobGasUsed)\n    receipt.blobGasUsed = BigInt(transactionReceipt.blobGasUsed)\n\n  return receipt\n}\n\nexport type DefineTransactionReceiptErrorType =\n  | DefineFormatterErrorType\n  | ErrorType\n\nexport const defineTransactionReceipt = /*#__PURE__*/ defineFormatter(\n  'transactionReceipt',\n  formatTransactionReceipt,\n)\n", "/**\n * RIPEMD-160 legacy hash function.\n * https://homes.esat.kuleuven.be/~bosselae/ripemd160.html\n * https://homes.esat.kuleuven.be/~bosselae/ripemd160/pdf/AB-9601/AB-9601.pdf\n * @module\n */\nimport { HashMD } from './_md.js';\nimport { rotl, wrapConstructor, type CHash } from './utils.js';\n\nconst Rho = /* @__PURE__ */ new Uint8Array([7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8]);\nconst Id = /* @__PURE__ */ new Uint8Array(new Array(16).fill(0).map((_, i) => i));\nconst Pi = /* @__PURE__ */ Id.map((i) => (9 * i + 5) % 16);\nlet idxL = [Id];\nlet idxR = [Pi];\nfor (let i = 0; i < 4; i++) for (let j of [idxL, idxR]) j.push(j[i].map((k) => Rho[k]));\n\nconst shifts = /* @__PURE__ */ [\n  [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8],\n  [12, 13, 11, 15, 6, 9, 9, 7, 12, 15, 11, 13, 7, 8, 7, 7],\n  [13, 15, 14, 11, 7, 7, 6, 8, 13, 14, 13, 12, 5, 5, 6, 9],\n  [14, 11, 12, 14, 8, 6, 5, 5, 15, 12, 15, 14, 9, 9, 8, 6],\n  [15, 12, 13, 13, 9, 5, 8, 6, 14, 11, 12, 11, 8, 6, 5, 5],\n].map((i) => new Uint8Array(i));\nconst shiftsL = /* @__PURE__ */ idxL.map((idx, i) => idx.map((j) => shifts[i][j]));\nconst shiftsR = /* @__PURE__ */ idxR.map((idx, i) => idx.map((j) => shifts[i][j]));\nconst Kl = /* @__PURE__ */ new Uint32Array([\n  0x00000000, 0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xa953fd4e,\n]);\nconst Kr = /* @__PURE__ */ new Uint32Array([\n  0x50a28be6, 0x5c4dd124, 0x6d703ef3, 0x7a6d76e9, 0x00000000,\n]);\n// It's called f() in spec.\nfunction f(group: number, x: number, y: number, z: number): number {\n  if (group === 0) return x ^ y ^ z;\n  else if (group === 1) return (x & y) | (~x & z);\n  else if (group === 2) return (x | ~y) ^ z;\n  else if (group === 3) return (x & z) | (y & ~z);\n  else return x ^ (y | ~z);\n}\n// Temporary buffer, not used to store anything between runs\nconst R_BUF = /* @__PURE__ */ new Uint32Array(16);\nexport class RIPEMD160 extends HashMD<RIPEMD160> {\n  private h0 = 0x67452301 | 0;\n  private h1 = 0xefcdab89 | 0;\n  private h2 = 0x98badcfe | 0;\n  private h3 = 0x10325476 | 0;\n  private h4 = 0xc3d2e1f0 | 0;\n\n  constructor() {\n    super(64, 20, 8, true);\n  }\n  protected get(): [number, number, number, number, number] {\n    const { h0, h1, h2, h3, h4 } = this;\n    return [h0, h1, h2, h3, h4];\n  }\n  protected set(h0: number, h1: number, h2: number, h3: number, h4: number): void {\n    this.h0 = h0 | 0;\n    this.h1 = h1 | 0;\n    this.h2 = h2 | 0;\n    this.h3 = h3 | 0;\n    this.h4 = h4 | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    for (let i = 0; i < 16; i++, offset += 4) R_BUF[i] = view.getUint32(offset, true);\n    // prettier-ignore\n    let al = this.h0 | 0, ar = al,\n        bl = this.h1 | 0, br = bl,\n        cl = this.h2 | 0, cr = cl,\n        dl = this.h3 | 0, dr = dl,\n        el = this.h4 | 0, er = el;\n\n    // Instead of iterating 0 to 80, we split it into 5 groups\n    // And use the groups in constants, functions, etc. Much simpler\n    for (let group = 0; group < 5; group++) {\n      const rGroup = 4 - group;\n      const hbl = Kl[group], hbr = Kr[group]; // prettier-ignore\n      const rl = idxL[group], rr = idxR[group]; // prettier-ignore\n      const sl = shiftsL[group], sr = shiftsR[group]; // prettier-ignore\n      for (let i = 0; i < 16; i++) {\n        const tl = (rotl(al + f(group, bl, cl, dl) + R_BUF[rl[i]] + hbl, sl[i]) + el) | 0;\n        al = el, el = dl, dl = rotl(cl, 10) | 0, cl = bl, bl = tl; // prettier-ignore\n      }\n      // 2 loops are 10% faster\n      for (let i = 0; i < 16; i++) {\n        const tr = (rotl(ar + f(rGroup, br, cr, dr) + R_BUF[rr[i]] + hbr, sr[i]) + er) | 0;\n        ar = er, er = dr, dr = rotl(cr, 10) | 0, cr = br, br = tr; // prettier-ignore\n      }\n    }\n    // Add the compressed chunk to the current hash value\n    this.set(\n      (this.h1 + cl + dr) | 0,\n      (this.h2 + dl + er) | 0,\n      (this.h3 + el + ar) | 0,\n      (this.h4 + al + br) | 0,\n      (this.h0 + bl + cr) | 0\n    );\n  }\n  protected roundClean(): void {\n    R_BUF.fill(0);\n  }\n  destroy(): void {\n    this.destroyed = true;\n    this.buffer.fill(0);\n    this.set(0, 0, 0, 0, 0);\n  }\n}\n\n/** RIPEMD-160 - a legacy hash function from 1990s. */\nexport const ripemd160: CHash = /* @__PURE__ */ wrapConstructor(() => new RIPEMD160());\n", "import type { Address } from 'abitype'\n\nimport { getTransactionCount } from '../actions/public/getTransactionCount.js'\nimport type { Client } from '../clients/createClient.js'\nimport type { MaybePromise } from '../types/utils.js'\nimport { LruMap } from './lru.js'\n\nexport type CreateNonceManagerParameters = {\n  source: NonceManagerSource\n}\n\ntype FunctionParameters = {\n  address: Address\n  chainId: number\n}\n\nexport type NonceManager = {\n  /** Get and increment a nonce. */\n  consume: (\n    parameters: FunctionParameters & { client: Client },\n  ) => Promise<number>\n  /** Increment a nonce. */\n  increment: (chainId: FunctionParameters) => void\n  /** Get a nonce. */\n  get: (chainId: FunctionParameters & { client: Client }) => Promise<number>\n  /** Reset a nonce. */\n  reset: (chainId: FunctionParameters) => void\n}\n\n/**\n * Creates a nonce manager for auto-incrementing transaction nonces.\n *\n * - Docs: https://viem.sh/docs/accounts/createNonceManager\n *\n * @example\n * ```ts\n * const nonceManager = createNonceManager({\n *   source: jsonRpc(),\n * })\n * ```\n */\nexport function createNonceManager(\n  parameters: CreateNonceManagerParameters,\n): NonceManager {\n  const { source } = parameters\n\n  const deltaMap = new Map()\n  const nonceMap = new LruMap<number>(8192)\n  const promiseMap = new Map<string, Promise<number>>()\n\n  const getKey = ({ address, chainId }: FunctionParameters) =>\n    `${address}.${chainId}`\n\n  return {\n    async consume({ address, chainId, client }) {\n      const key = getKey({ address, chainId })\n      const promise = this.get({ address, chainId, client })\n\n      this.increment({ address, chainId })\n      const nonce = await promise\n\n      await source.set({ address, chainId }, nonce)\n      nonceMap.set(key, nonce)\n\n      return nonce\n    },\n    async increment({ address, chainId }) {\n      const key = getKey({ address, chainId })\n      const delta = deltaMap.get(key) ?? 0\n      deltaMap.set(key, delta + 1)\n    },\n    async get({ address, chainId, client }) {\n      const key = getKey({ address, chainId })\n\n      let promise = promiseMap.get(key)\n      if (!promise) {\n        promise = (async () => {\n          try {\n            const nonce = await source.get({ address, chainId, client })\n            const previousNonce = nonceMap.get(key) ?? 0\n            if (previousNonce > 0 && nonce <= previousNonce)\n              return previousNonce + 1\n            nonceMap.delete(key)\n            return nonce\n          } finally {\n            this.reset({ address, chainId })\n          }\n        })()\n        promiseMap.set(key, promise)\n      }\n\n      const delta = deltaMap.get(key) ?? 0\n      return delta + (await promise)\n    },\n    reset({ address, chainId }) {\n      const key = getKey({ address, chainId })\n      deltaMap.delete(key)\n      promiseMap.delete(key)\n    },\n  }\n}\n\n////////////////////////////////////////////////////////////////////////////////////////////\n// Sources\n\nexport type NonceManagerSource = {\n  /** Get a nonce. */\n  get(parameters: FunctionParameters & { client: Client }): MaybePromise<number>\n  /** Set a nonce. */\n  set(parameters: FunctionParameters, nonce: number): MaybePromise<void>\n}\n\n/** JSON-RPC source for a nonce manager. */\nexport function jsonRpc(): NonceManagerSource {\n  return {\n    async get(parameters) {\n      const { address, client } = parameters\n      return getTransactionCount(client, {\n        address,\n        blockTag: 'pending',\n      })\n    },\n    set() {},\n  }\n}\n\n////////////////////////////////////////////////////////////////////////////////////////////\n// Default\n\n/** Default Nonce Manager with a JSON-RPC source. */\nexport const nonceManager = /*#__PURE__*/ createNonceManager({\n  source: jsonRpc(),\n})\n", "/** @internal */\nexport const version = '0.1.1'\n", "import { version } from '../version.js'\n\n/** @internal */\nexport function getUrl(url: string) {\n  return url\n}\n\n/** @internal */\nexport function getVersion() {\n  return version\n}\n\n/** @internal */\nexport function prettyPrint(args: unknown) {\n  if (!args) return ''\n  const entries = Object.entries(args)\n    .map(([key, value]) => {\n      if (value === undefined || value === false) return null\n      return [key, value]\n    })\n    .filter(Boolean) as [string, string][]\n  const maxLength = entries.reduce((acc, [key]) => Math.max(acc, key.length), 0)\n  return entries\n    .map(([key, value]) => `  ${`${key}:`.padEnd(maxLength + 1)}  ${value}`)\n    .join('\\n')\n}\n", "import { getVersion } from './internal/errors.js'\n\nexport type GlobalErrorType<name extends string = 'Error'> = Error & {\n  name: name\n}\n\n/**\n * Base error class inherited by all errors thrown by ox.\n *\n * @example\n * ```ts\n * import { Errors } from 'ox'\n * throw new Errors.BaseError('An error occurred')\n * ```\n */\nexport class BaseError<\n  cause extends Error | undefined = undefined,\n> extends Error {\n  details: string\n  docs?: string | undefined\n  docsPath?: string | undefined\n  shortMessage: string\n\n  override cause: cause\n  override name = 'BaseError'\n\n  version = `ox@${getVersion()}`\n\n  constructor(shortMessage: string, options: BaseError.Options<cause> = {}) {\n    const details = (() => {\n      if (options.cause instanceof BaseError) {\n        if (options.cause.details) return options.cause.details\n        if (options.cause.shortMessage) return options.cause.shortMessage\n      }\n      if (options.cause?.message) return options.cause.message\n      return options.details!\n    })()\n    const docsPath = (() => {\n      if (options.cause instanceof BaseError)\n        return options.cause.docsPath || options.docsPath\n      return options.docsPath\n    })()\n\n    const docsBaseUrl = 'https://oxlib.sh'\n    const docs = `${docsBaseUrl}${docsPath ?? ''}`\n\n    const message = [\n      shortMessage || 'An error occurred.',\n      ...(options.metaMessages ? ['', ...options.metaMessages] : []),\n      ...(details || docsPath\n        ? [\n            '',\n            details ? `Details: ${details}` : undefined,\n            docsPath ? `See: ${docs}` : undefined,\n          ]\n        : []),\n    ]\n      .filter((x) => typeof x === 'string')\n      .join('\\n')\n\n    super(message, options.cause ? { cause: options.cause } : undefined)\n\n    this.cause = options.cause as any\n    this.details = details\n    this.docs = docs\n    this.docsPath = docsPath\n    this.shortMessage = shortMessage\n  }\n\n  walk(): Error\n  walk(fn: (err: unknown) => boolean): Error | null\n  walk(fn?: any): any {\n    return walk(this, fn)\n  }\n}\n\nexport declare namespace BaseError {\n  type Options<cause extends Error | undefined = Error | undefined> = {\n    cause?: cause | undefined\n    details?: string | undefined\n    docsPath?: string | undefined\n    metaMessages?: (string | undefined)[] | undefined\n  }\n}\n\n/** @internal */\nfunction walk(\n  err: unknown,\n  fn?: ((err: unknown) => boolean) | undefined,\n): unknown {\n  if (fn?.(err)) return err\n  if (err && typeof err === 'object' && 'cause' in err && err.cause)\n    return walk(err.cause, fn)\n  return fn ? null : err\n}\n", "import * as Bytes from '../Bytes.js'\nimport type * as Errors from '../Errors.js'\n\n/** @internal */\nexport function assertSize(bytes: Bytes.Bytes, size_: number): void {\n  if (Bytes.size(bytes) > size_)\n    throw new Bytes.SizeOverflowError({\n      givenSize: Bytes.size(bytes),\n      maxSize: size_,\n    })\n}\n\n/** @internal */\nexport declare namespace assertSize {\n  type ErrorType =\n    | Bytes.size.ErrorType\n    | Bytes.SizeOverflowError\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function assertStartOffset(\n  value: Bytes.Bytes,\n  start?: number | undefined,\n) {\n  if (typeof start === 'number' && start > 0 && start > Bytes.size(value) - 1)\n    throw new Bytes.SliceOffsetOutOfBoundsError({\n      offset: start,\n      position: 'start',\n      size: Bytes.size(value),\n    })\n}\n\nexport declare namespace assertStartOffset {\n  export type ErrorType =\n    | Bytes.SliceOffsetOutOfBoundsError\n    | Bytes.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function assertEndOffset(\n  value: Bytes.Bytes,\n  start?: number | undefined,\n  end?: number | undefined,\n) {\n  if (\n    typeof start === 'number' &&\n    typeof end === 'number' &&\n    Bytes.size(value) !== end - start\n  ) {\n    throw new Bytes.SliceOffsetOutOfBoundsError({\n      offset: end,\n      position: 'end',\n      size: Bytes.size(value),\n    })\n  }\n}\n\n/** @internal */\nexport declare namespace assertEndOffset {\n  type ErrorType =\n    | Bytes.SliceOffsetOutOfBoundsError\n    | Bytes.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport const charCodeMap = {\n  zero: 48,\n  nine: 57,\n  A: 65,\n  F: 70,\n  a: 97,\n  f: 102,\n} as const\n\n/** @internal */\nexport function charCodeToBase16(char: number) {\n  if (char >= charCodeMap.zero && char <= charCodeMap.nine)\n    return char - charCodeMap.zero\n  if (char >= charCodeMap.A && char <= charCodeMap.F)\n    return char - (charCodeMap.A - 10)\n  if (char >= charCodeMap.a && char <= charCodeMap.f)\n    return char - (charCodeMap.a - 10)\n  return undefined\n}\n\n/** @internal */\nexport function pad(bytes: Bytes.Bytes, options: pad.Options = {}) {\n  const { dir, size = 32 } = options\n  if (size === 0) return bytes\n  if (bytes.length > size)\n    throw new Bytes.SizeExceedsPaddingSizeError({\n      size: bytes.length,\n      targetSize: size,\n      type: 'Bytes',\n    })\n  const paddedBytes = new Uint8Array(size)\n  for (let i = 0; i < size; i++) {\n    const padEnd = dir === 'right'\n    paddedBytes[padEnd ? i : size - i - 1] =\n      bytes[padEnd ? i : bytes.length - i - 1]!\n  }\n  return paddedBytes\n}\n\n/** @internal */\nexport declare namespace pad {\n  type Options = {\n    dir?: 'left' | 'right' | undefined\n    size?: number | undefined\n  }\n\n  type ReturnType = Bytes.Bytes\n\n  type ErrorType = Bytes.SizeExceedsPaddingSizeError | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function trim(\n  value: Bytes.Bytes,\n  options: trim.Options = {},\n): trim.ReturnType {\n  const { dir = 'left' } = options\n\n  let data = value\n\n  let sliceLength = 0\n  for (let i = 0; i < data.length - 1; i++) {\n    if (data[dir === 'left' ? i : data.length - i - 1]!.toString() === '0')\n      sliceLength++\n    else break\n  }\n  data =\n    dir === 'left'\n      ? data.slice(sliceLength)\n      : data.slice(0, data.length - sliceLength)\n\n  return data as trim.ReturnType\n}\n\n/** @internal */\nexport declare namespace trim {\n  type Options = {\n    dir?: 'left' | 'right' | undefined\n  }\n\n  type ReturnType = Bytes.Bytes\n\n  type ErrorType = Errors.GlobalErrorType\n}\n", "import type * as Errors from '../Errors.js'\nimport * as Hex from '../Hex.js'\n\n/** @internal */\nexport function assertSize(hex: Hex.Hex, size_: number): void {\n  if (Hex.size(hex) > size_)\n    throw new Hex.SizeOverflowError({\n      givenSize: Hex.size(hex),\n      maxSize: size_,\n    })\n}\n\n/** @internal */\nexport declare namespace assertSize {\n  type ErrorType =\n    | Hex.size.ErrorType\n    | Hex.SizeOverflowError\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function assertStartOffset(value: Hex.Hex, start?: number | undefined) {\n  if (typeof start === 'number' && start > 0 && start > Hex.size(value) - 1)\n    throw new Hex.SliceOffsetOutOfBoundsError({\n      offset: start,\n      position: 'start',\n      size: Hex.size(value),\n    })\n}\n\nexport declare namespace assertStartOffset {\n  type ErrorType =\n    | Hex.SliceOffsetOutOfBoundsError\n    | Hex.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function assertEndOffset(\n  value: Hex.Hex,\n  start?: number | undefined,\n  end?: number | undefined,\n) {\n  if (\n    typeof start === 'number' &&\n    typeof end === 'number' &&\n    Hex.size(value) !== end - start\n  ) {\n    throw new Hex.SliceOffsetOutOfBoundsError({\n      offset: end,\n      position: 'end',\n      size: Hex.size(value),\n    })\n  }\n}\n\nexport declare namespace assertEndOffset {\n  type ErrorType =\n    | Hex.SliceOffsetOutOfBoundsError\n    | Hex.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function pad(hex_: Hex.Hex, options: pad.Options = {}) {\n  const { dir, size = 32 } = options\n\n  if (size === 0) return hex_\n\n  const hex = hex_.replace('0x', '')\n  if (hex.length > size * 2)\n    throw new Hex.SizeExceedsPaddingSizeError({\n      size: Math.ceil(hex.length / 2),\n      targetSize: size,\n      type: 'Hex',\n    })\n\n  return `0x${hex[dir === 'right' ? 'padEnd' : 'padStart'](size * 2, '0')}` as Hex.Hex\n}\n\n/** @internal */\nexport declare namespace pad {\n  type Options = {\n    dir?: 'left' | 'right' | undefined\n    size?: number | undefined\n  }\n  type ErrorType = Hex.SizeExceedsPaddingSizeError | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function trim(\n  value: Hex.Hex,\n  options: trim.Options = {},\n): trim.ReturnType {\n  const { dir = 'left' } = options\n\n  let data = value.replace('0x', '')\n\n  let sliceLength = 0\n  for (let i = 0; i < data.length - 1; i++) {\n    if (data[dir === 'left' ? i : data.length - i - 1]!.toString() === '0')\n      sliceLength++\n    else break\n  }\n  data =\n    dir === 'left'\n      ? data.slice(sliceLength)\n      : data.slice(0, data.length - sliceLength)\n\n  if (data === '0') return '0x'\n  if (dir === 'right' && data.length % 2 === 1) return `0x${data}0`\n  return `0x${data}` as trim.ReturnType\n}\n\n/** @internal */\nexport declare namespace trim {\n  type Options = {\n    dir?: 'left' | 'right' | undefined\n  }\n\n  type ReturnType = Hex.Hex\n\n  type ErrorType = Errors.GlobalErrorType\n}\n", "import { equalBytes } from '@noble/curves/abstract/utils'\nimport * as Errors from './Errors.js'\nimport * as Hex from './Hex.js'\nimport * as <PERSON><PERSON> from './Json.js'\nimport * as internal from './internal/bytes.js'\nimport * as internal_hex from './internal/hex.js'\n\nconst decoder = /*#__PURE__*/ new TextDecoder()\nconst encoder = /*#__PURE__*/ new TextEncoder()\n\n/** Root type for a Bytes array. */\nexport type Bytes = Uint8Array\n\n/**\n * Asserts if the given value is {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.assert('abc')\n * // @error: Bytes.InvalidBytesTypeError:\n * // @error: Value `\"abc\"` of type `string` is an invalid Bytes value.\n * // @error: Bytes values must be of type `Uint8Array`.\n * ```\n *\n * @param value - Value to assert.\n */\nexport function assert(value: unknown): asserts value is Bytes {\n  if (value instanceof Uint8Array) return\n  if (!value) throw new InvalidBytesTypeError(value)\n  if (typeof value !== 'object') throw new InvalidBytesTypeError(value)\n  if (!('BYTES_PER_ELEMENT' in value)) throw new InvalidBytesTypeError(value)\n  if (value.BYTES_PER_ELEMENT !== 1 || value.constructor.name !== 'Uint8Array')\n    throw new InvalidBytesTypeError(value)\n}\n\nexport declare namespace assert {\n  type ErrorType = InvalidBytesTypeError | Errors.GlobalErrorType\n}\n\n/**\n * Concatenates two or more {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const bytes = Bytes.concat(\n *   Bytes.from([1]),\n *   Bytes.from([69]),\n *   Bytes.from([420, 69]),\n * )\n * // @log: Uint8Array [ 1, 69, 420, 69 ]\n * ```\n *\n * @param values - Values to concatenate.\n * @returns Concatenated {@link ox#Bytes.Bytes}.\n */\nexport function concat(...values: readonly Bytes[]): Bytes {\n  let length = 0\n  for (const arr of values) {\n    length += arr.length\n  }\n  const result = new Uint8Array(length)\n  for (let i = 0, index = 0; i < values.length; i++) {\n    const arr = values[i]\n    result.set(arr!, index)\n    index += arr!.length\n  }\n  return result\n}\n\nexport declare namespace concat {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Instantiates a {@link ox#Bytes.Bytes} value from a `Uint8Array`, a hex string, or an array of unsigned 8-bit integers.\n *\n * :::tip\n *\n * To instantiate from a **Boolean**, **String**, or **Number**, use one of the following:\n *\n * - `Bytes.fromBoolean`\n *\n * - `Bytes.fromString`\n *\n * - `Bytes.fromNumber`\n *\n * :::\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.from([255, 124, 5, 4])\n * // @log: Uint8Array([255, 124, 5, 4])\n *\n * const data = Bytes.from('0xdeadbeef')\n * // @log: Uint8Array([222, 173, 190, 239])\n * ```\n *\n * @param value - Value to convert.\n * @returns A {@link ox#Bytes.Bytes} instance.\n */\nexport function from(value: Hex.Hex | Bytes | readonly number[]): Bytes {\n  if (value instanceof Uint8Array) return value\n  if (typeof value === 'string') return fromHex(value)\n  return fromArray(value)\n}\n\nexport declare namespace from {\n  type ErrorType =\n    | fromHex.ErrorType\n    | fromArray.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts an array of unsigned 8-bit integers into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromArray([255, 124, 5, 4])\n * // @log: Uint8Array([255, 124, 5, 4])\n * ```\n *\n * @param value - Value to convert.\n * @returns A {@link ox#Bytes.Bytes} instance.\n */\nexport function fromArray(value: readonly number[] | Uint8Array): Bytes {\n  return value instanceof Uint8Array ? value : new Uint8Array(value)\n}\n\nexport declare namespace fromArray {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Encodes a boolean value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromBoolean(true)\n * // @log: Uint8Array([1])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromBoolean(true, { size: 32 })\n * // @log: Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1])\n * ```\n *\n * @param value - Boolean value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nexport function fromBoolean(value: boolean, options: fromBoolean.Options = {}) {\n  const { size } = options\n  const bytes = new Uint8Array(1)\n  bytes[0] = Number(value)\n  if (typeof size === 'number') {\n    internal.assertSize(bytes, size)\n    return padLeft(bytes, size)\n  }\n  return bytes\n}\n\nexport declare namespace fromBoolean {\n  type Options = {\n    /** Size of the output bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | padLeft.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a {@link ox#Hex.Hex} value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromHex('0x48656c6c6f20776f726c6421')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromHex('0x48656c6c6f20776f726c6421', { size: 32 })\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n * ```\n *\n * @param value - {@link ox#Hex.Hex} value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nexport function fromHex(value: Hex.Hex, options: fromHex.Options = {}): Bytes {\n  const { size } = options\n\n  let hex = value\n  if (size) {\n    internal_hex.assertSize(value, size)\n    hex = Hex.padRight(value, size)\n  }\n\n  let hexString = hex.slice(2) as string\n  if (hexString.length % 2) hexString = `0${hexString}`\n\n  const length = hexString.length / 2\n  const bytes = new Uint8Array(length)\n  for (let index = 0, j = 0; index < length; index++) {\n    const nibbleLeft = internal.charCodeToBase16(hexString.charCodeAt(j++))\n    const nibbleRight = internal.charCodeToBase16(hexString.charCodeAt(j++))\n    if (nibbleLeft === undefined || nibbleRight === undefined) {\n      throw new Errors.BaseError(\n        `Invalid byte sequence (\"${hexString[j - 2]}${hexString[j - 1]}\" in \"${hexString}\").`,\n      )\n    }\n    bytes[index] = nibbleLeft * 16 + nibbleRight\n  }\n  return bytes\n}\n\nexport declare namespace fromHex {\n  type Options = {\n    /** Size of the output bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal_hex.assertSize.ErrorType\n    | Hex.padRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a number value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromNumber(420)\n * // @log: Uint8Array([1, 164])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromNumber(420, { size: 4 })\n * // @log: Uint8Array([0, 0, 1, 164])\n * ```\n *\n * @param value - Number value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nexport function fromNumber(\n  value: bigint | number,\n  options?: fromNumber.Options | undefined,\n) {\n  const hex = Hex.fromNumber(value, options)\n  return fromHex(hex)\n}\n\nexport declare namespace fromNumber {\n  export type Options = Hex.fromNumber.Options\n\n  export type ErrorType =\n    | Hex.fromNumber.ErrorType\n    | fromHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a string into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromString('Hello world!')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100, 33])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromString('Hello world!', { size: 32 })\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n * ```\n *\n * @param value - String to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nexport function fromString(\n  value: string,\n  options: fromString.Options = {},\n): Bytes {\n  const { size } = options\n\n  const bytes = encoder.encode(value)\n  if (typeof size === 'number') {\n    internal.assertSize(bytes, size)\n    return padRight(bytes, size)\n  }\n  return bytes\n}\n\nexport declare namespace fromString {\n  type Options = {\n    /** Size of the output bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | padRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Checks if two {@link ox#Bytes.Bytes} values are equal.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.isEqual(Bytes.from([1]), Bytes.from([1]))\n * // @log: true\n *\n * Bytes.isEqual(Bytes.from([1]), Bytes.from([2]))\n * // @log: false\n * ```\n *\n * @param bytesA - First {@link ox#Bytes.Bytes} value.\n * @param bytesB - Second {@link ox#Bytes.Bytes} value.\n * @returns `true` if the two values are equal, otherwise `false`.\n */\nexport function isEqual(bytesA: Bytes, bytesB: Bytes) {\n  return equalBytes(bytesA, bytesB)\n}\n\nexport declare namespace isEqual {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Pads a {@link ox#Bytes.Bytes} value to the left with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padLeft(Bytes.from([1]), 4)\n * // @log: Uint8Array([0, 0, 0, 1])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value to pad.\n * @param size - Size to pad the {@link ox#Bytes.Bytes} value to.\n * @returns Padded {@link ox#Bytes.Bytes} value.\n */\nexport function padLeft(\n  value: Bytes,\n  size?: number | undefined,\n): padLeft.ReturnType {\n  return internal.pad(value, { dir: 'left', size })\n}\n\nexport declare namespace padLeft {\n  type ReturnType = internal.pad.ReturnType\n  type ErrorType = internal.pad.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Pads a {@link ox#Bytes.Bytes} value to the right with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padRight(Bytes.from([1]), 4)\n * // @log: Uint8Array([1, 0, 0, 0])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value to pad.\n * @param size - Size to pad the {@link ox#Bytes.Bytes} value to.\n * @returns Padded {@link ox#Bytes.Bytes} value.\n */\nexport function padRight(\n  value: Bytes,\n  size?: number | undefined,\n): padRight.ReturnType {\n  return internal.pad(value, { dir: 'right', size })\n}\n\nexport declare namespace padRight {\n  type ReturnType = internal.pad.ReturnType\n  type ErrorType = internal.pad.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Generates random {@link ox#Bytes.Bytes} of the specified length.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const bytes = Bytes.random(32)\n * // @log: Uint8Array([... x32])\n * ```\n *\n * @param length - Length of the random {@link ox#Bytes.Bytes} to generate.\n * @returns Random {@link ox#Bytes.Bytes} of the specified length.\n */\nexport function random(length: number): Bytes {\n  return crypto.getRandomValues(new Uint8Array(length))\n}\n\nexport declare namespace random {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Retrieves the size of a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.size(Bytes.from([1, 2, 3, 4]))\n * // @log: 4\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Size of the {@link ox#Bytes.Bytes} value.\n */\nexport function size(value: Bytes): number {\n  return value.length\n}\n\nexport declare namespace size {\n  export type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Returns a section of a {@link ox#Bytes.Bytes} value given a start/end bytes offset.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.slice(\n *   Bytes.from([1, 2, 3, 4, 5, 6, 7, 8, 9]),\n *   1,\n *   4,\n * )\n * // @log: Uint8Array([2, 3, 4])\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value.\n * @param start - Start offset.\n * @param end - End offset.\n * @param options - Slice options.\n * @returns Sliced {@link ox#Bytes.Bytes} value.\n */\nexport function slice(\n  value: Bytes,\n  start?: number | undefined,\n  end?: number | undefined,\n  options: slice.Options = {},\n): Bytes {\n  const { strict } = options\n  internal.assertStartOffset(value, start)\n  const value_ = value.slice(start, end)\n  if (strict) internal.assertEndOffset(value_, start, end)\n  return value_\n}\n\nexport declare namespace slice {\n  type Options = {\n    /** Asserts that the sliced value is the same size as the given start/end offsets. */\n    strict?: boolean | undefined\n  }\n\n  export type ErrorType =\n    | internal.assertStartOffset.ErrorType\n    | internal.assertEndOffset.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a bigint.\n *\n * @example\n * ```ts\n * import { Bytes } from 'ox'\n *\n * Bytes.toBigInt(Bytes.from([1, 164]))\n * // @log: 420n\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Decoding options.\n * @returns Decoded bigint.\n */\nexport function toBigInt(bytes: Bytes, options: toBigInt.Options = {}): bigint {\n  const { size } = options\n  if (typeof size !== 'undefined') internal.assertSize(bytes, size)\n  const hex = Hex.fromBytes(bytes, options)\n  return Hex.toBigInt(hex, options)\n}\n\nexport declare namespace toBigInt {\n  type Options = {\n    /** Whether or not the number of a signed representation. */\n    signed?: boolean | undefined\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | Hex.fromBytes.ErrorType\n    | Hex.toBigInt.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a boolean.\n *\n * @example\n * ```ts\n * import { Bytes } from 'ox'\n *\n * Bytes.toBoolean(Bytes.from([1]))\n * // @log: true\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Decoding options.\n * @returns Decoded boolean.\n */\nexport function toBoolean(\n  bytes: Bytes,\n  options: toBoolean.Options = {},\n): boolean {\n  const { size } = options\n  let bytes_ = bytes\n  if (typeof size !== 'undefined') {\n    internal.assertSize(bytes_, size)\n    bytes_ = trimLeft(bytes_)\n  }\n  if (bytes_.length > 1 || bytes_[0]! > 1)\n    throw new InvalidBytesBooleanError(bytes_)\n  return Boolean(bytes_[0])\n}\n\nexport declare namespace toBoolean {\n  type Options = {\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | trimLeft.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a {@link ox#Bytes.Bytes} value into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toHex(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Options.\n * @returns Decoded {@link ox#Hex.Hex} value.\n */\nexport function toHex(value: Bytes, options: toHex.Options = {}): Hex.Hex {\n  return Hex.fromBytes(value, options)\n}\n\nexport declare namespace toHex {\n  type Options = {\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType = Hex.fromBytes.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a number.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toNumber(Bytes.from([1, 164]))\n * // @log: 420\n * ```\n */\nexport function toNumber(bytes: Bytes, options: toNumber.Options = {}): number {\n  const { size } = options\n  if (typeof size !== 'undefined') internal.assertSize(bytes, size)\n  const hex = Hex.fromBytes(bytes, options)\n  return Hex.toNumber(hex, options)\n}\n\nexport declare namespace toNumber {\n  type Options = {\n    /** Whether or not the number of a signed representation. */\n    signed?: boolean | undefined\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | Hex.fromBytes.ErrorType\n    | Hex.toNumber.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a string.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.toString(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: 'Hello world'\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Options.\n * @returns Decoded string.\n */\nexport function toString(bytes: Bytes, options: toString.Options = {}): string {\n  const { size } = options\n\n  let bytes_ = bytes\n  if (typeof size !== 'undefined') {\n    internal.assertSize(bytes_, size)\n    bytes_ = trimRight(bytes_)\n  }\n  return decoder.decode(bytes_)\n}\n\nexport declare namespace toString {\n  export type Options = {\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  export type ErrorType =\n    | internal.assertSize.ErrorType\n    | trimRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Trims leading zeros from a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.trimLeft(Bytes.from([0, 0, 0, 0, 1, 2, 3]))\n * // @log: Uint8Array([1, 2, 3])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Trimmed {@link ox#Bytes.Bytes} value.\n */\nexport function trimLeft(value: Bytes): Bytes {\n  return internal.trim(value, { dir: 'left' })\n}\n\nexport declare namespace trimLeft {\n  type ErrorType = internal.trim.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Trims trailing zeros from a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.trimRight(Bytes.from([1, 2, 3, 0, 0, 0, 0]))\n * // @log: Uint8Array([1, 2, 3])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Trimmed {@link ox#Bytes.Bytes} value.\n */\nexport function trimRight(value: Bytes): Bytes {\n  return internal.trim(value, { dir: 'right' })\n}\n\nexport declare namespace trimRight {\n  export type ErrorType = internal.trim.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Checks if the given value is {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.validate('0x')\n * // @log: false\n *\n * Bytes.validate(Bytes.from([1, 2, 3]))\n * // @log: true\n * ```\n *\n * @param value - Value to check.\n * @returns `true` if the value is {@link ox#Bytes.Bytes}, otherwise `false`.\n */\nexport function validate(value: unknown): value is Bytes {\n  try {\n    assert(value)\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  export type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Thrown when the bytes value cannot be represented as a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toBoolean(Bytes.from([5]))\n * // @error: Bytes.InvalidBytesBooleanError: Bytes value `[5]` is not a valid boolean.\n * // @error: The bytes array must contain a single byte of either a `0` or `1` value.\n * ```\n */\nexport class InvalidBytesBooleanError extends Errors.BaseError {\n  override readonly name = 'Bytes.InvalidBytesBooleanError'\n\n  constructor(bytes: Bytes) {\n    super(`Bytes value \\`${bytes}\\` is not a valid boolean.`, {\n      metaMessages: [\n        'The bytes array must contain a single byte of either a `0` or `1` value.',\n      ],\n    })\n  }\n}\n\n/**\n * Thrown when a value cannot be converted to bytes.\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Bytes } from 'ox'\n *\n * Bytes.from('foo')\n * // @error: Bytes.InvalidBytesTypeError: Value `foo` of type `string` is an invalid Bytes value.\n * ```\n */\nexport class InvalidBytesTypeError extends Errors.BaseError {\n  override readonly name = 'Bytes.InvalidBytesTypeError'\n\n  constructor(value: unknown) {\n    super(\n      `Value \\`${typeof value === 'object' ? Json.stringify(value) : value}\\` of type \\`${typeof value}\\` is an invalid Bytes value.`,\n      {\n        metaMessages: ['Bytes values must be of type `Bytes`.'],\n      },\n    )\n  }\n}\n\n/**\n * Thrown when a size exceeds the maximum allowed size.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.fromString('Hello World!', { size: 8 })\n * // @error: Bytes.SizeOverflowError: Size cannot exceed `8` bytes. Given size: `12` bytes.\n * ```\n */\nexport class SizeOverflowError extends Errors.BaseError {\n  override readonly name = 'Bytes.SizeOverflowError'\n\n  constructor({ givenSize, maxSize }: { givenSize: number; maxSize: number }) {\n    super(\n      `Size cannot exceed \\`${maxSize}\\` bytes. Given size: \\`${givenSize}\\` bytes.`,\n    )\n  }\n}\n\n/**\n * Thrown when a slice offset is out-of-bounds.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.slice(Bytes.from([1, 2, 3]), 4)\n * // @error: Bytes.SliceOffsetOutOfBoundsError: Slice starting at offset `4` is out-of-bounds (size: `3`).\n * ```\n */\nexport class SliceOffsetOutOfBoundsError extends Errors.BaseError {\n  override readonly name = 'Bytes.SliceOffsetOutOfBoundsError'\n\n  constructor({\n    offset,\n    position,\n    size,\n  }: { offset: number; position: 'start' | 'end'; size: number }) {\n    super(\n      `Slice ${\n        position === 'start' ? 'starting' : 'ending'\n      } at offset \\`${offset}\\` is out-of-bounds (size: \\`${size}\\`).`,\n    )\n  }\n}\n\n/**\n * Thrown when a the padding size exceeds the maximum allowed size.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padLeft(Bytes.fromString('Hello World!'), 8)\n * // @error: [Bytes.SizeExceedsPaddingSizeError: Bytes size (`12`) exceeds padding size (`8`).\n * ```\n */\nexport class SizeExceedsPaddingSizeError extends Errors.BaseError {\n  override readonly name = 'Bytes.SizeExceedsPaddingSizeError'\n\n  constructor({\n    size,\n    targetSize,\n    type,\n  }: {\n    size: number\n    targetSize: number\n    type: 'Hex' | 'Bytes'\n  }) {\n    super(\n      `${type.charAt(0).toUpperCase()}${type\n        .slice(1)\n        .toLowerCase()} size (\\`${size}\\`) exceeds padding size (\\`${targetSize}\\`).`,\n    )\n  }\n}\n", "import { equalBytes } from '@noble/curves/abstract/utils'\nimport * as Bytes from './Bytes.js'\nimport * as Errors from './Errors.js'\nimport * as Json from './Json.js'\nimport * as internal_bytes from './internal/bytes.js'\nimport * as internal from './internal/hex.js'\n\nconst encoder = /*#__PURE__*/ new TextEncoder()\n\nconst hexes = /*#__PURE__*/ Array.from({ length: 256 }, (_v, i) =>\n  i.toString(16).padStart(2, '0'),\n)\n\n/** Root type for a Hex string. */\nexport type Hex = `0x${string}`\n\n/**\n * Asserts if the given value is {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert('abc')\n * // @error: InvalidHexValueTypeError:\n * // @error: Value `\"abc\"` of type `string` is an invalid hex type.\n * // @error: Hex types must be represented as `\"0x\\${string}\"`.\n * ```\n *\n * @param value - The value to assert.\n * @param options - Options.\n */\nexport function assert(\n  value: unknown,\n  options: assert.Options = {},\n): asserts value is Hex {\n  const { strict = false } = options\n  if (!value) throw new InvalidHexTypeError(value)\n  if (typeof value !== 'string') throw new InvalidHexTypeError(value)\n  if (strict) {\n    if (!/^0x[0-9a-fA-F]*$/.test(value)) throw new InvalidHexValueError(value)\n  }\n  if (!value.startsWith('0x')) throw new InvalidHexValueError(value)\n}\n\nexport declare namespace assert {\n  type Options = {\n    /** Checks if the {@link ox#Hex.Hex} value contains invalid hexadecimal characters. @default false */\n    strict?: boolean | undefined\n  }\n\n  type ErrorType =\n    | InvalidHexTypeError\n    | InvalidHexValueError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Concatenates two or more {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.concat('0x123', '0x456')\n * // @log: '0x123456'\n * ```\n *\n * @param values - The {@link ox#Hex.Hex} values to concatenate.\n * @returns The concatenated {@link ox#Hex.Hex} value.\n */\nexport function concat(...values: readonly Hex[]): Hex {\n  return `0x${(values as Hex[]).reduce((acc, x) => acc + x.replace('0x', ''), '')}`\n}\n\nexport declare namespace concat {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Instantiates a {@link ox#Hex.Hex} value from a hex string or {@link ox#Bytes.Bytes} value.\n *\n * :::tip\n *\n * To instantiate from a **Boolean**, **String**, or **Number**, use one of the following:\n *\n * - `Hex.fromBoolean`\n *\n * - `Hex.fromString`\n *\n * - `Hex.fromNumber`\n *\n * :::\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.from('0x48656c6c6f20576f726c6421')\n * // @log: '0x48656c6c6f20576f726c6421'\n *\n * Hex.from(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value to encode.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function from(value: Hex | Bytes.Bytes | readonly number[]): Hex {\n  if (value instanceof Uint8Array) return fromBytes(value)\n  if (Array.isArray(value)) return fromBytes(new Uint8Array(value))\n  return value as never\n}\n\nexport declare namespace from {\n  type Options = {\n    /** The size (in bytes) of the output hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType = fromBytes.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a boolean into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromBoolean(true)\n * // @log: '0x1'\n *\n * Hex.fromBoolean(false)\n * // @log: '0x0'\n *\n * Hex.fromBoolean(true, { size: 32 })\n * // @log: '0x0000000000000000000000000000000000000000000000000000000000000001'\n * ```\n *\n * @param value - The boolean value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function fromBoolean(\n  value: boolean,\n  options: fromBoolean.Options = {},\n): Hex {\n  const hex: Hex = `0x${Number(value)}`\n  if (typeof options.size === 'number') {\n    internal.assertSize(hex, options.size)\n    return padLeft(hex, options.size)\n  }\n  return hex\n}\n\nexport declare namespace fromBoolean {\n  type Options = {\n    /** The size (in bytes) of the output hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | padLeft.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a {@link ox#Bytes.Bytes} value into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.fromBytes(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function fromBytes(\n  value: Bytes.Bytes,\n  options: fromBytes.Options = {},\n): Hex {\n  let string = ''\n  for (let i = 0; i < value.length; i++) string += hexes[value[i]!]\n  const hex = `0x${string}` as const\n\n  if (typeof options.size === 'number') {\n    internal.assertSize(hex, options.size)\n    return padRight(hex, options.size)\n  }\n  return hex\n}\n\nexport declare namespace fromBytes {\n  type Options = {\n    /** The size (in bytes) of the output hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | padRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a number or bigint into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromNumber(420)\n * // @log: '0x1a4'\n *\n * Hex.fromNumber(420, { size: 32 })\n * // @log: '0x00000000000000000000000000000000000000000000000000000000000001a4'\n * ```\n *\n * @param value - The number or bigint value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function fromNumber(\n  value: number | bigint,\n  options: fromNumber.Options = {},\n): Hex {\n  const { signed, size } = options\n\n  const value_ = BigInt(value)\n\n  let maxValue: bigint | number | undefined\n  if (size) {\n    if (signed) maxValue = (1n << (BigInt(size) * 8n - 1n)) - 1n\n    else maxValue = 2n ** (BigInt(size) * 8n) - 1n\n  } else if (typeof value === 'number') {\n    maxValue = BigInt(Number.MAX_SAFE_INTEGER)\n  }\n\n  const minValue = typeof maxValue === 'bigint' && signed ? -maxValue - 1n : 0\n\n  if ((maxValue && value_ > maxValue) || value_ < minValue) {\n    const suffix = typeof value === 'bigint' ? 'n' : ''\n    throw new IntegerOutOfRangeError({\n      max: maxValue ? `${maxValue}${suffix}` : undefined,\n      min: `${minValue}${suffix}`,\n      signed,\n      size,\n      value: `${value}${suffix}`,\n    })\n  }\n\n  const stringValue = (\n    signed && value_ < 0 ? (1n << BigInt(size * 8)) + BigInt(value_) : value_\n  ).toString(16)\n\n  const hex = `0x${stringValue}` as Hex\n  if (size) return padLeft(hex, size) as Hex\n  return hex\n}\n\nexport declare namespace fromNumber {\n  type Options =\n    | {\n        /** Whether or not the number of a signed representation. */\n        signed?: boolean | undefined\n        /** The size (in bytes) of the output hex value. */\n        size: number\n      }\n    | {\n        signed?: undefined\n        /** The size (in bytes) of the output hex value. */\n        size?: number | undefined\n      }\n\n  type ErrorType =\n    | IntegerOutOfRangeError\n    | padLeft.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a string into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n * Hex.fromString('Hello World!')\n * // '0x48656c6c6f20576f726c6421'\n *\n * Hex.fromString('Hello World!', { size: 32 })\n * // '0x48656c6c6f20576f726c64210000000000000000000000000000000000000000'\n * ```\n *\n * @param value - The string value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function fromString(\n  value: string,\n  options: fromString.Options = {},\n): Hex {\n  return fromBytes(encoder.encode(value), options)\n}\n\nexport declare namespace fromString {\n  type Options = {\n    /** The size (in bytes) of the output hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType = fromBytes.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Checks if two {@link ox#Hex.Hex} values are equal.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.isEqual('0xdeadbeef', '0xdeadbeef')\n * // @log: true\n *\n * Hex.isEqual('0xda', '0xba')\n * // @log: false\n * ```\n *\n * @param hexA - The first {@link ox#Hex.Hex} value.\n * @param hexB - The second {@link ox#Hex.Hex} value.\n * @returns `true` if the two {@link ox#Hex.Hex} values are equal, `false` otherwise.\n */\nexport function isEqual(hexA: Hex, hexB: Hex) {\n  return equalBytes(Bytes.fromHex(hexA), Bytes.fromHex(hexB))\n}\n\nexport declare namespace isEqual {\n  type ErrorType = Bytes.fromHex.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Pads a {@link ox#Hex.Hex} value to the left with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.padLeft('0x1234', 4)\n * // @log: '0x00001234'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to pad.\n * @param size - The size (in bytes) of the output hex value.\n * @returns The padded {@link ox#Hex.Hex} value.\n */\nexport function padLeft(\n  value: Hex,\n  size?: number | undefined,\n): padLeft.ReturnType {\n  return internal.pad(value, { dir: 'left', size })\n}\n\nexport declare namespace padLeft {\n  type ReturnType = Hex\n  type ErrorType = internal.pad.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Pads a {@link ox#Hex.Hex} value to the right with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts\n * import { Hex } from 'ox'\n *\n * Hex.padRight('0x1234', 4)\n * // @log: '0x12340000'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to pad.\n * @param size - The size (in bytes) of the output hex value.\n * @returns The padded {@link ox#Hex.Hex} value.\n */\nexport function padRight(\n  value: Hex,\n  size?: number | undefined,\n): padRight.ReturnType {\n  return internal.pad(value, { dir: 'right', size })\n}\n\nexport declare namespace padRight {\n  type ReturnType = Hex\n  type ErrorType = internal.pad.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Generates a random {@link ox#Hex.Hex} value of the specified length.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * const hex = Hex.random(32)\n * // @log: '0x...'\n * ```\n *\n * @returns Random {@link ox#Hex.Hex} value.\n */\nexport function random(length: number): Hex {\n  return fromBytes(Bytes.random(length))\n}\n\nexport declare namespace random {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Returns a section of a {@link ox#Bytes.Bytes} value given a start/end bytes offset.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.slice('0x0123456789', 1, 4)\n * // @log: '0x234567'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to slice.\n * @param start - The start offset (in bytes).\n * @param end - The end offset (in bytes).\n * @param options - Options.\n * @returns The sliced {@link ox#Hex.Hex} value.\n */\nexport function slice(\n  value: Hex,\n  start?: number | undefined,\n  end?: number | undefined,\n  options: slice.Options = {},\n): Hex {\n  const { strict } = options\n  internal.assertStartOffset(value, start)\n  const value_ = `0x${value\n    .replace('0x', '')\n    .slice((start ?? 0) * 2, (end ?? value.length) * 2)}` as const\n  if (strict) internal.assertEndOffset(value_, start, end)\n  return value_\n}\n\nexport declare namespace slice {\n  type Options = {\n    /** Asserts that the sliced value is the same size as the given start/end offsets. */\n    strict?: boolean | undefined\n  }\n\n  type ErrorType =\n    | internal.assertStartOffset.ErrorType\n    | internal.assertEndOffset.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Retrieves the size of a {@link ox#Hex.Hex} value (in bytes).\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.size('0xdeadbeef')\n * // @log: 4\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to get the size of.\n * @returns The size of the {@link ox#Hex.Hex} value (in bytes).\n */\nexport function size(value: Hex): number {\n  return Math.ceil((value.length - 2) / 2)\n}\n\nexport declare namespace size {\n  export type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Trims leading zeros from a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.trimLeft('0x00000000deadbeef')\n * // @log: '0xdeadbeef'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to trim.\n * @returns The trimmed {@link ox#Hex.Hex} value.\n */\nexport function trimLeft(value: Hex): trimLeft.ReturnType {\n  return internal.trim(value, { dir: 'left' })\n}\n\nexport declare namespace trimLeft {\n  type ReturnType = Hex\n\n  type ErrorType = internal.trim.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Trims trailing zeros from a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.trimRight('0xdeadbeef00000000')\n * // @log: '0xdeadbeef'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to trim.\n * @returns The trimmed {@link ox#Hex.Hex} value.\n */\nexport function trimRight(value: Hex): trimRight.ReturnType {\n  return internal.trim(value, { dir: 'right' })\n}\n\nexport declare namespace trimRight {\n  type ReturnType = Hex\n\n  type ErrorType = internal.trim.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a BigInt.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBigInt('0x1a4')\n * // @log: 420n\n *\n * Hex.toBigInt('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // @log: 420n\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded BigInt.\n */\nexport function toBigInt(hex: Hex, options: toBigInt.Options = {}): bigint {\n  const { signed } = options\n\n  if (options.size) internal.assertSize(hex, options.size)\n\n  const value = BigInt(hex)\n  if (!signed) return value\n\n  const size = (hex.length - 2) / 2\n\n  const max_unsigned = (1n << (BigInt(size) * 8n)) - 1n\n  const max_signed = max_unsigned >> 1n\n\n  if (value <= max_signed) return value\n  return value - max_unsigned - 1n\n}\n\nexport declare namespace toBigInt {\n  type Options = {\n    /** Whether or not the number of a signed representation. */\n    signed?: boolean | undefined\n    /** Size (in bytes) of the hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType = internal.assertSize.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBoolean('0x01')\n * // @log: true\n *\n * Hex.toBoolean('0x0000000000000000000000000000000000000000000000000000000000000001', { size: 32 })\n * // @log: true\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded boolean.\n */\nexport function toBoolean(hex: Hex, options: toBoolean.Options = {}): boolean {\n  if (options.size) internal.assertSize(hex, options.size)\n  const hex_ = trimLeft(hex)\n  if (hex_ === '0x') return false\n  if (hex_ === '0x1') return true\n  throw new InvalidHexBooleanError(hex)\n}\n\nexport declare namespace toBoolean {\n  type Options = {\n    /** Size (in bytes) of the hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | trimLeft.ErrorType\n    | InvalidHexBooleanError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * const data = Hex.toBytes('0x48656c6c6f20776f726c6421')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded {@link ox#Bytes.Bytes}.\n */\nexport function toBytes(hex: Hex, options: toBytes.Options = {}): Bytes.Bytes {\n  return Bytes.fromHex(hex, options)\n}\n\nexport declare namespace toBytes {\n  type Options = {\n    /** Size (in bytes) of the hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType = Bytes.fromHex.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a number.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toNumber('0x1a4')\n * // @log: 420\n *\n * Hex.toNumber('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // @log: 420\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded number.\n */\nexport function toNumber(hex: Hex, options: toNumber.Options = {}): number {\n  const { signed, size } = options\n  if (!signed && !size) return Number(hex)\n  return Number(toBigInt(hex, options))\n}\n\nexport declare namespace toNumber {\n  type Options = toBigInt.Options\n\n  type ErrorType = toBigInt.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a string.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toString('0x48656c6c6f20576f726c6421')\n * // @log: 'Hello world!'\n *\n * Hex.toString('0x48656c6c6f20576f726c64210000000000000000000000000000000000000000', {\n *  size: 32,\n * })\n * // @log: 'Hello world'\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded string.\n */\nexport function toString(hex: Hex, options: toString.Options = {}): string {\n  const { size } = options\n\n  let bytes = Bytes.fromHex(hex)\n  if (size) {\n    internal_bytes.assertSize(bytes, size)\n    bytes = Bytes.trimRight(bytes)\n  }\n  return new TextDecoder().decode(bytes)\n}\n\nexport declare namespace toString {\n  type Options = {\n    /** Size (in bytes) of the hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal_bytes.assertSize.ErrorType\n    | Bytes.fromHex.ErrorType\n    | Bytes.trimRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Checks if the given value is {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.validate('0xdeadbeef')\n * // @log: true\n *\n * Hex.validate(Bytes.from([1, 2, 3]))\n * // @log: false\n * ```\n *\n * @param value - The value to check.\n * @param options - Options.\n * @returns `true` if the value is a {@link ox#Hex.Hex}, `false` otherwise.\n */\nexport function validate(\n  value: unknown,\n  options: validate.Options = {},\n): value is Hex {\n  const { strict = false } = options\n  try {\n    assert(value, { strict })\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  type Options = {\n    /** Checks if the {@link ox#Hex.Hex} value contains invalid hexadecimal characters. @default false */\n    strict?: boolean | undefined\n  }\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Thrown when the provided integer is out of range, and cannot be represented as a hex value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromNumber(420182738912731283712937129)\n * // @error: Hex.IntegerOutOfRangeError: Number \\`4.2018273891273126e+26\\` is not in safe unsigned integer range (`0` to `9007199254740991`)\n * ```\n */\nexport class IntegerOutOfRangeError extends Errors.BaseError {\n  override readonly name = 'Hex.IntegerOutOfRangeError'\n\n  constructor({\n    max,\n    min,\n    signed,\n    size,\n    value,\n  }: {\n    max?: string | undefined\n    min: string\n    signed?: boolean | undefined\n    size?: number | undefined\n    value: string\n  }) {\n    super(\n      `Number \\`${value}\\` is not in safe${\n        size ? ` ${size * 8}-bit` : ''\n      }${signed ? ' signed' : ' unsigned'} integer range ${max ? `(\\`${min}\\` to \\`${max}\\`)` : `(above \\`${min}\\`)`}`,\n    )\n  }\n}\n\n/**\n * Thrown when the provided hex value cannot be represented as a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBoolean('0xa')\n * // @error: Hex.InvalidHexBooleanError: Hex value `\"0xa\"` is not a valid boolean.\n * // @error: The hex value must be `\"0x0\"` (false) or `\"0x1\"` (true).\n * ```\n */\nexport class InvalidHexBooleanError extends Errors.BaseError {\n  override readonly name = 'Hex.InvalidHexBooleanError'\n\n  constructor(hex: Hex) {\n    super(`Hex value \\`\"${hex}\"\\` is not a valid boolean.`, {\n      metaMessages: [\n        'The hex value must be `\"0x0\"` (false) or `\"0x1\"` (true).',\n      ],\n    })\n  }\n}\n\n/**\n * Thrown when the provided value is not a valid hex type.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert(1)\n * // @error: Hex.InvalidHexTypeError: Value `1` of type `number` is an invalid hex type.\n * ```\n */\nexport class InvalidHexTypeError extends Errors.BaseError {\n  override readonly name = 'Hex.InvalidHexTypeError'\n\n  constructor(value: unknown) {\n    super(\n      `Value \\`${typeof value === 'object' ? Json.stringify(value) : value}\\` of type \\`${typeof value}\\` is an invalid hex type.`,\n      {\n        metaMessages: ['Hex types must be represented as `\"0x${string}\"`.'],\n      },\n    )\n  }\n}\n\n/**\n * Thrown when the provided hex value is invalid.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert('0x0123456789abcdefg')\n * // @error: Hex.InvalidHexValueError: Value `0x0123456789abcdefg` is an invalid hex value.\n * // @error: Hex values must start with `\"0x\"` and contain only hexadecimal characters (0-9, a-f, A-F).\n * ```\n */\nexport class InvalidHexValueError extends Errors.BaseError {\n  override readonly name = 'Hex.InvalidHexValueError'\n\n  constructor(value: unknown) {\n    super(`Value \\`${value}\\` is an invalid hex value.`, {\n      metaMessages: [\n        'Hex values must start with `\"0x\"` and contain only hexadecimal characters (0-9, a-f, A-F).',\n      ],\n    })\n  }\n}\n\n/**\n * Thrown when the provided hex value is an odd length.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.fromHex('0xabcde')\n * // @error: Hex.InvalidLengthError: Hex value `\"0xabcde\"` is an odd length (5 nibbles).\n * ```\n */\nexport class InvalidLengthError extends Errors.BaseError {\n  override readonly name = 'Hex.InvalidLengthError'\n\n  constructor(value: Hex) {\n    super(\n      `Hex value \\`\"${value}\"\\` is an odd length (${value.length - 2} nibbles).`,\n      {\n        metaMessages: ['It must be an even length.'],\n      },\n    )\n  }\n}\n\n/**\n * Thrown when the size of the value exceeds the expected max size.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromString('Hello World!', { size: 8 })\n * // @error: Hex.SizeOverflowError: Size cannot exceed `8` bytes. Given size: `12` bytes.\n * ```\n */\nexport class SizeOverflowError extends Errors.BaseError {\n  override readonly name = 'Hex.SizeOverflowError'\n\n  constructor({ givenSize, maxSize }: { givenSize: number; maxSize: number }) {\n    super(\n      `Size cannot exceed \\`${maxSize}\\` bytes. Given size: \\`${givenSize}\\` bytes.`,\n    )\n  }\n}\n\n/**\n * Thrown when the slice offset exceeds the bounds of the value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.slice('0x0123456789', 6)\n * // @error: Hex.SliceOffsetOutOfBoundsError: Slice starting at offset `6` is out-of-bounds (size: `5`).\n * ```\n */\nexport class SliceOffsetOutOfBoundsError extends Errors.BaseError {\n  override readonly name = 'Hex.SliceOffsetOutOfBoundsError'\n\n  constructor({\n    offset,\n    position,\n    size,\n  }: { offset: number; position: 'start' | 'end'; size: number }) {\n    super(\n      `Slice ${\n        position === 'start' ? 'starting' : 'ending'\n      } at offset \\`${offset}\\` is out-of-bounds (size: \\`${size}\\`).`,\n    )\n  }\n}\n\n/**\n * Thrown when the size of the value exceeds the pad size.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.padLeft('0x1a4e12a45a21323123aaa87a897a897a898a6567a578a867a98778a667a85a875a87a6a787a65a675a6a9', 32)\n * // @error: Hex.SizeExceedsPaddingSizeError: Hex size (`43`) exceeds padding size (`32`).\n * ```\n */\nexport class SizeExceedsPaddingSizeError extends Errors.BaseError {\n  override readonly name = 'Hex.SizeExceedsPaddingSizeError'\n\n  constructor({\n    size,\n    targetSize,\n    type,\n  }: {\n    size: number\n    targetSize: number\n    type: 'Hex' | 'Bytes'\n  }) {\n    super(\n      `${type.charAt(0).toUpperCase()}${type\n        .slice(1)\n        .toLowerCase()} size (\\`${size}\\`) exceeds padding size (\\`${targetSize}\\`).`,\n    )\n  }\n}\n", "import { ripemd160 as noble_ripemd160 } from '@noble/hashes/ripemd160'\nimport { keccak_256 as noble_keccak256 } from '@noble/hashes/sha3'\nimport { sha256 as noble_sha256 } from '@noble/hashes/sha256'\nimport * as Bytes from './Bytes.js'\nimport type * as Errors from './Errors.js'\nimport * as Hex from './Hex.js'\n\n/**\n * Calculates the [Keccak256](https://en.wikipedia.org/wiki/SHA-3) hash of a {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n *\n * This function is a re-export of `keccak_256` from [`@noble/hashes`](https://github.com/paulmillr/noble-hashes), an audited & minimal JS hashing library.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.keccak256('0xdeadbeef')\n * // @log: '0xd4fd4e189132273036449fc9e11198c739161b4c0116a9a2dccdfa1c492006f1'\n * ```\n *\n * @example\n * ### Calculate Hash of a String\n *\n * ```ts twoslash\n * import { Hash, Hex } from 'ox'\n *\n * Hash.keccak256(Hex.fromString('hello world'))\n * // @log: '0x3ea2f1d0abf3fc66cf29eebb70cbd4e7fe762ef8a09bcc06c8edf641230afec0'\n * ```\n *\n * @example\n * ### Configure Return Type\n *\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.keccak256('0xdeadbeef', { as: 'Bytes' })\n * // @log: Uint8Array [...]\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n * @param options - Options.\n * @returns Keccak256 hash.\n */\nexport function keccak256<\n  value extends Hex.Hex | Bytes.Bytes,\n  as extends 'Hex' | 'Bytes' =\n    | (value extends Hex.Hex ? 'Hex' : never)\n    | (value extends Bytes.Bytes ? 'Bytes' : never),\n>(\n  value: value | Hex.Hex | Bytes.Bytes,\n  options: keccak256.Options<as> = {},\n): keccak256.ReturnType<as> {\n  const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options\n  const bytes = noble_keccak256(Bytes.from(value))\n  if (as === 'Bytes') return bytes as never\n  return Hex.fromBytes(bytes) as never\n}\n\nexport declare namespace keccak256 {\n  type Options<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> = {\n    /** The return type. @default 'Hex' */\n    as?: as | 'Hex' | 'Bytes' | undefined\n  }\n\n  type ReturnType<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> =\n    | (as extends 'Bytes' ? Bytes.Bytes : never)\n    | (as extends 'Hex' ? Hex.Hex : never)\n\n  type ErrorType =\n    | Bytes.from.ErrorType\n    | Hex.fromBytes.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Calculates the [Ripemd160](https://en.wikipedia.org/wiki/RIPEMD) hash of a {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n *\n * This function is a re-export of `ripemd160` from [`@noble/hashes`](https://github.com/paulmillr/noble-hashes), an audited & minimal JS hashing library.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.ripemd160('0xdeadbeef')\n * // '0x226821c2f5423e11fe9af68bd285c249db2e4b5a'\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n * @param options - Options.\n * @returns Ripemd160 hash.\n */\nexport function ripemd160<\n  value extends Hex.Hex | Bytes.Bytes,\n  as extends 'Hex' | 'Bytes' =\n    | (value extends Hex.Hex ? 'Hex' : never)\n    | (value extends Bytes.Bytes ? 'Bytes' : never),\n>(\n  value: value | Hex.Hex | Bytes.Bytes,\n  options: ripemd160.Options<as> = {},\n): ripemd160.ReturnType<as> {\n  const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options\n  const bytes = noble_ripemd160(Bytes.from(value))\n  if (as === 'Bytes') return bytes as never\n  return Hex.fromBytes(bytes) as never\n}\n\nexport declare namespace ripemd160 {\n  type Options<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> = {\n    /** The return type. @default 'Hex' */\n    as?: as | 'Hex' | 'Bytes' | undefined\n  }\n\n  type ReturnType<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> =\n    | (as extends 'Bytes' ? Bytes.Bytes : never)\n    | (as extends 'Hex' ? Hex.Hex : never)\n\n  type ErrorType =\n    | Bytes.from.ErrorType\n    | Hex.fromBytes.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Calculates the [Sha256](https://en.wikipedia.org/wiki/SHA-256) hash of a {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n *\n * This function is a re-export of `sha256` from [`@noble/hashes`](https://github.com/paulmillr/noble-hashes), an audited & minimal JS hashing library.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.sha256('0xdeadbeef')\n * // '0x5f78c33274e43fa9de5659265c1d917e25c03722dcb0b8d27db8d5feaa813953'\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n * @param options - Options.\n * @returns Sha256 hash.\n */\nexport function sha256<\n  value extends Hex.Hex | Bytes.Bytes,\n  as extends 'Hex' | 'Bytes' =\n    | (value extends Hex.Hex ? 'Hex' : never)\n    | (value extends Bytes.Bytes ? 'Bytes' : never),\n>(\n  value: value | Hex.Hex | Bytes.Bytes,\n  options: sha256.Options<as> = {},\n): sha256.ReturnType<as> {\n  const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options\n  const bytes = noble_sha256(Bytes.from(value))\n  if (as === 'Bytes') return bytes as never\n  return Hex.fromBytes(bytes) as never\n}\n\nexport declare namespace sha256 {\n  type Options<as extends 'Hex' | 'Bytes' = 'Hex'> = {\n    /** The return type. @default 'Hex' */\n    as?: as | 'Hex' | 'Bytes' | undefined\n  }\n\n  type ReturnType<as extends 'Hex' | 'Bytes' = 'Hex'> =\n    | (as extends 'Bytes' ? Bytes.Bytes : never)\n    | (as extends 'Hex' ? Hex.Hex : never)\n\n  type ErrorType =\n    | Bytes.from.ErrorType\n    | Hex.fromBytes.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Checks if a string is a valid hash value.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.validate('0x')\n * // @log: false\n *\n * Hash.validate('0x3ea2f1d0abf3fc66cf29eebb70cbd4e7fe762ef8a09bcc06c8edf641230afec0')\n * // @log: true\n * ```\n *\n * @param value - Value to check.\n * @returns Whether the value is a valid hash.\n */\nexport function validate(value: string): value is Hex.Hex {\n  return Hex.validate(value) && Hex.size(value) === 32\n}\n\nexport declare namespace validate {\n  type ErrorType =\n    | Hex.validate.ErrorType\n    | Hex.size.ErrorType\n    | Errors.GlobalErrorType\n}\n", "/**\n * @internal\n *\n * Map with a LRU (Least recently used) policy.\n * @see https://en.wikipedia.org/wiki/Cache_replacement_policies#LRU\n */\nexport class LruMap<value = unknown> extends Map<string, value> {\n  maxSize: number\n\n  constructor(size: number) {\n    super()\n    this.maxSize = size\n  }\n\n  override get(key: string) {\n    const value = super.get(key)\n\n    if (super.has(key) && value !== undefined) {\n      this.delete(key)\n      super.set(key, value)\n    }\n\n    return value\n  }\n\n  override set(key: string, value: value) {\n    super.set(key, value)\n    if (this.maxSize && this.size > this.maxSize) {\n      const firstKey = this.keys().next().value\n      if (firstKey) this.delete(firstKey)\n    }\n    return this\n  }\n}\n", "import type * as Address from './Address.js'\nimport { LruMap } from './internal/lru.js'\n\nconst caches = {\n  checksum: /*#__PURE__*/ new LruMap<Address.Address>(8192),\n}\n\nexport const checksum = caches.checksum\n\n/**\n * Clears all global caches.\n *\n * @example\n * ```ts\n * import { Caches } from 'ox'\n * Caches.clear()\n * ```\n */\nexport function clear() {\n  for (const cache of Object.values(caches)) cache.clear()\n}\n", "import type { Address as abitype_Address } from 'abitype'\nimport * as Bytes from './Bytes.js'\nimport * as Caches from './Caches.js'\nimport * as Errors from './Errors.js'\nimport * as Hash from './Hash.js'\nimport * as <PERSON>Key from './PublicKey.js'\n\nconst addressRegex = /*#__PURE__*/ /^0x[a-fA-F0-9]{40}$/\n\n/** Root type for Address. */\nexport type Address = abitype_Address\n\n/**\n * Asserts that the given value is a valid {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.assert('******************************************')\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.assert('0xdeadbeef')\n * // @error: InvalidAddressError: Address \"0xdeadbeef\" is invalid.\n * ```\n *\n * @param value - Value to assert if it is a valid address.\n * @param options - Assertion options.\n */\nexport function assert(\n  value: string,\n  options: assert.Options = {},\n): asserts value is Address {\n  const { strict = true } = options\n\n  if (!addressRegex.test(value))\n    throw new InvalidAddressError({\n      address: value,\n      cause: new InvalidInputError(),\n    })\n\n  if (strict) {\n    if (value.toLowerCase() === value) return\n    if (checksum(value as Address) !== value)\n      throw new InvalidAddressError({\n        address: value,\n        cause: new InvalidChecksumError(),\n      })\n  }\n}\n\nexport declare namespace assert {\n  type Options = {\n    /**\n     * Enables strict mode. Whether or not to compare the address against its checksum.\n     *\n     * @default true\n     */\n    strict?: boolean | undefined\n  }\n\n  type ErrorType = InvalidAddressError | Errors.GlobalErrorType\n}\n\n/**\n * Computes the checksum address for the given {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.checksum('******************************************')\n * // @log: '******************************************'\n * ```\n *\n * @param address - The address to compute the checksum for.\n * @returns The checksummed address.\n */\nexport function checksum(address: string): Address {\n  if (Caches.checksum.has(address)) return Caches.checksum.get(address)!\n\n  assert(address, { strict: false })\n\n  const hexAddress = address.substring(2).toLowerCase()\n  const hash = Hash.keccak256(Bytes.fromString(hexAddress), { as: 'Bytes' })\n\n  const characters = hexAddress.split('')\n  for (let i = 0; i < 40; i += 2) {\n    if (hash[i >> 1]! >> 4 >= 8 && characters[i]) {\n      characters[i] = characters[i]!.toUpperCase()\n    }\n    if ((hash[i >> 1]! & 0x0f) >= 8 && characters[i + 1]) {\n      characters[i + 1] = characters[i + 1]!.toUpperCase()\n    }\n  }\n\n  const result = `0x${characters.join('')}` as const\n  Caches.checksum.set(address, result)\n  return result\n}\n\nexport declare namespace checksum {\n  type ErrorType =\n    | assert.ErrorType\n    | Hash.keccak256.ErrorType\n    | Bytes.fromString.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts a stringified address to a typed (checksummed) {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('******************************************')\n * // @log: '******************************************'\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('******************************************', {\n *   checksum: false\n * })\n * // @log: '******************************************'\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('hello')\n * // @error: InvalidAddressError: Address \"0xa\" is invalid.\n * ```\n *\n * @param address - An address string to convert to a typed Address.\n * @param options - Conversion options.\n * @returns The typed Address.\n */\nexport function from(address: string, options: from.Options = {}): Address {\n  const { checksum: checksumVal = false } = options\n  assert(address)\n  if (checksumVal) return checksum(address)\n  return address as Address\n}\n\nexport declare namespace from {\n  type Options = {\n    /**\n     * Whether to checksum the address.\n     *\n     * @default false\n     */\n    checksum?: boolean | undefined\n  }\n\n  type ErrorType =\n    | assert.ErrorType\n    | checksum.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts an ECDSA public key to an {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address, PublicKey } from 'ox'\n *\n * const publicKey = PublicKey.from(\n *   '0x048318535b54105d4a7aae60c08fc45f9687181b4fdfc625bd1a753fa7397fed753547f11ca8696646f2f3acb08e31016afac23e630c5d11f59f61fef57b0d2aa5',\n * )\n * const address = Address.fromPublicKey(publicKey)\n * // @log: '******************************************'\n * ```\n *\n * @param publicKey - The ECDSA public key to convert to an {@link ox#Address.Address}.\n * @param options - Conversion options.\n * @returns The {@link ox#Address.Address} corresponding to the public key.\n */\nexport function fromPublicKey(\n  publicKey: PublicKey.PublicKey,\n  options: fromPublicKey.Options = {},\n): Address {\n  const address = Hash.keccak256(\n    `0x${PublicKey.toHex(publicKey).slice(4)}`,\n  ).substring(26)\n  return from(`0x${address}`, options)\n}\n\nexport declare namespace fromPublicKey {\n  type Options = {\n    /**\n     * Whether to checksum the address.\n     *\n     * @default false\n     */\n    checksum?: boolean | undefined\n  }\n\n  type ErrorType =\n    | Hash.keccak256.ErrorType\n    | PublicKey.toHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Checks if two {@link ox#Address.Address} are equal.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.isEqual(\n *   '******************************************',\n *   '******************************************'\n * )\n * // @log: true\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.isEqual(\n *   '******************************************',\n *   '******************************************'\n * )\n * // @log: false\n * ```\n *\n * @param addressA - The first address to compare.\n * @param addressB - The second address to compare.\n * @returns Whether the addresses are equal.\n */\nexport function isEqual(addressA: Address, addressB: Address): boolean {\n  assert(addressA, { strict: false })\n  assert(addressB, { strict: false })\n  return addressA.toLowerCase() === addressB.toLowerCase()\n}\n\nexport declare namespace isEqual {\n  type ErrorType = assert.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Checks if the given address is a valid {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.validate('******************************************')\n * // @log: true\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.validate('0xdeadbeef')\n * // @log: false\n * ```\n *\n * @param address - Value to check if it is a valid address.\n * @param options - Check options.\n * @returns Whether the address is a valid address.\n */\nexport function validate(\n  address: string,\n  options: validate.Options = {},\n): address is Address {\n  const { strict = true } = options ?? {}\n  try {\n    assert(address, { strict })\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  type Options = {\n    /**\n     * Enables strict mode. Whether or not to compare the address against its checksum.\n     *\n     * @default true\n     */\n    strict?: boolean | undefined\n  }\n}\n\n/**\n * Thrown when an address is invalid.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('0x123')\n * // @error: Address.InvalidAddressError: Address `0x123` is invalid.\n * ```\n */\nexport class InvalidAddressError<\n  cause extends InvalidInputError | InvalidChecksumError =\n    | InvalidInputError\n    | InvalidChecksumError,\n> extends Errors.BaseError<cause> {\n  override readonly name = 'Address.InvalidAddressError'\n\n  constructor({ address, cause }: { address: string; cause: cause }) {\n    super(`Address \"${address}\" is invalid.`, {\n      cause,\n    })\n  }\n}\n\n/** Thrown when an address is not a 20 byte (40 hexadecimal character) value. */\nexport class InvalidInputError extends Errors.BaseError {\n  override readonly name = 'Address.InvalidInputError'\n\n  constructor() {\n    super('Address is not a 20 byte (40 hexadecimal character) value.')\n  }\n}\n\n/** Thrown when an address does not match its checksum counterpart. */\nexport class InvalidChecksumError extends Errors.BaseError {\n  override readonly name = 'Address.InvalidChecksumError'\n\n  constructor() {\n    super('Address does not match its checksum counterpart.')\n  }\n}\n", "export const arrayRegex = /^(.*)\\[([0-9]*)\\]$/\n\n// `bytes<M>`: binary type of `M` bytes, `0 < M <= 32`\n// https://regexr.com/6va55\nexport const bytesRegex = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/\n\n// `(u)int<M>`: (un)signed integer type of `M` bits, `0 < M <= 256`, `M % 8 == 0`\n// https://regexr.com/6v8hp\nexport const integerRegex =\n  /^(u?int)(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/\n\nexport const maxInt8 = 2n ** (8n - 1n) - 1n\nexport const maxInt16 = 2n ** (16n - 1n) - 1n\nexport const maxInt24 = 2n ** (24n - 1n) - 1n\nexport const maxInt32 = 2n ** (32n - 1n) - 1n\nexport const maxInt40 = 2n ** (40n - 1n) - 1n\nexport const maxInt48 = 2n ** (48n - 1n) - 1n\nexport const maxInt56 = 2n ** (56n - 1n) - 1n\nexport const maxInt64 = 2n ** (64n - 1n) - 1n\nexport const maxInt72 = 2n ** (72n - 1n) - 1n\nexport const maxInt80 = 2n ** (80n - 1n) - 1n\nexport const maxInt88 = 2n ** (88n - 1n) - 1n\nexport const maxInt96 = 2n ** (96n - 1n) - 1n\nexport const maxInt104 = 2n ** (104n - 1n) - 1n\nexport const maxInt112 = 2n ** (112n - 1n) - 1n\nexport const maxInt120 = 2n ** (120n - 1n) - 1n\nexport const maxInt128 = 2n ** (128n - 1n) - 1n\nexport const maxInt136 = 2n ** (136n - 1n) - 1n\nexport const maxInt144 = 2n ** (144n - 1n) - 1n\nexport const maxInt152 = 2n ** (152n - 1n) - 1n\nexport const maxInt160 = 2n ** (160n - 1n) - 1n\nexport const maxInt168 = 2n ** (168n - 1n) - 1n\nexport const maxInt176 = 2n ** (176n - 1n) - 1n\nexport const maxInt184 = 2n ** (184n - 1n) - 1n\nexport const maxInt192 = 2n ** (192n - 1n) - 1n\nexport const maxInt200 = 2n ** (200n - 1n) - 1n\nexport const maxInt208 = 2n ** (208n - 1n) - 1n\nexport const maxInt216 = 2n ** (216n - 1n) - 1n\nexport const maxInt224 = 2n ** (224n - 1n) - 1n\nexport const maxInt232 = 2n ** (232n - 1n) - 1n\nexport const maxInt240 = 2n ** (240n - 1n) - 1n\nexport const maxInt248 = 2n ** (248n - 1n) - 1n\nexport const maxInt256 = 2n ** (256n - 1n) - 1n\n\nexport const minInt8 = -(2n ** (8n - 1n))\nexport const minInt16 = -(2n ** (16n - 1n))\nexport const minInt24 = -(2n ** (24n - 1n))\nexport const minInt32 = -(2n ** (32n - 1n))\nexport const minInt40 = -(2n ** (40n - 1n))\nexport const minInt48 = -(2n ** (48n - 1n))\nexport const minInt56 = -(2n ** (56n - 1n))\nexport const minInt64 = -(2n ** (64n - 1n))\nexport const minInt72 = -(2n ** (72n - 1n))\nexport const minInt80 = -(2n ** (80n - 1n))\nexport const minInt88 = -(2n ** (88n - 1n))\nexport const minInt96 = -(2n ** (96n - 1n))\nexport const minInt104 = -(2n ** (104n - 1n))\nexport const minInt112 = -(2n ** (112n - 1n))\nexport const minInt120 = -(2n ** (120n - 1n))\nexport const minInt128 = -(2n ** (128n - 1n))\nexport const minInt136 = -(2n ** (136n - 1n))\nexport const minInt144 = -(2n ** (144n - 1n))\nexport const minInt152 = -(2n ** (152n - 1n))\nexport const minInt160 = -(2n ** (160n - 1n))\nexport const minInt168 = -(2n ** (168n - 1n))\nexport const minInt176 = -(2n ** (176n - 1n))\nexport const minInt184 = -(2n ** (184n - 1n))\nexport const minInt192 = -(2n ** (192n - 1n))\nexport const minInt200 = -(2n ** (200n - 1n))\nexport const minInt208 = -(2n ** (208n - 1n))\nexport const minInt216 = -(2n ** (216n - 1n))\nexport const minInt224 = -(2n ** (224n - 1n))\nexport const minInt232 = -(2n ** (232n - 1n))\nexport const minInt240 = -(2n ** (240n - 1n))\nexport const minInt248 = -(2n ** (248n - 1n))\nexport const minInt256 = -(2n ** (256n - 1n))\n\nexport const maxUint8 = 2n ** 8n - 1n\nexport const maxUint16 = 2n ** 16n - 1n\nexport const maxUint24 = 2n ** 24n - 1n\nexport const maxUint32 = 2n ** 32n - 1n\nexport const maxUint40 = 2n ** 40n - 1n\nexport const maxUint48 = 2n ** 48n - 1n\nexport const maxUint56 = 2n ** 56n - 1n\nexport const maxUint64 = 2n ** 64n - 1n\nexport const maxUint72 = 2n ** 72n - 1n\nexport const maxUint80 = 2n ** 80n - 1n\nexport const maxUint88 = 2n ** 88n - 1n\nexport const maxUint96 = 2n ** 96n - 1n\nexport const maxUint104 = 2n ** 104n - 1n\nexport const maxUint112 = 2n ** 112n - 1n\nexport const maxUint120 = 2n ** 120n - 1n\nexport const maxUint128 = 2n ** 128n - 1n\nexport const maxUint136 = 2n ** 136n - 1n\nexport const maxUint144 = 2n ** 144n - 1n\nexport const maxUint152 = 2n ** 152n - 1n\nexport const maxUint160 = 2n ** 160n - 1n\nexport const maxUint168 = 2n ** 168n - 1n\nexport const maxUint176 = 2n ** 176n - 1n\nexport const maxUint184 = 2n ** 184n - 1n\nexport const maxUint192 = 2n ** 192n - 1n\nexport const maxUint200 = 2n ** 200n - 1n\nexport const maxUint208 = 2n ** 208n - 1n\nexport const maxUint216 = 2n ** 216n - 1n\nexport const maxUint224 = 2n ** 224n - 1n\nexport const maxUint232 = 2n ** 232n - 1n\nexport const maxUint240 = 2n ** 240n - 1n\nexport const maxUint248 = 2n ** 248n - 1n\nexport const maxUint256 = 2n ** 256n - 1n\n", "import type { Bytes } from '../Bytes.js'\nimport * as Errors from '../Errors.js'\n\n/** @internal */\nexport type Cursor = {\n  bytes: Bytes\n  dataView: DataView\n  position: number\n  positionReadCount: Map<number, number>\n  recursiveReadCount: number\n  recursiveReadLimit: number\n  remaining: number\n  assertReadLimit(position?: number): void\n  assertPosition(position: number): void\n  decrementPosition(offset: number): void\n  getReadCount(position?: number): number\n  incrementPosition(offset: number): void\n  inspectByte(position?: number): Bytes[number]\n  inspectBytes(length: number, position?: number): Bytes\n  inspectUint8(position?: number): number\n  inspectUint16(position?: number): number\n  inspectUint24(position?: number): number\n  inspectUint32(position?: number): number\n  pushByte(byte: Bytes[number]): void\n  pushBytes(bytes: Bytes): void\n  pushUint8(value: number): void\n  pushUint16(value: number): void\n  pushUint24(value: number): void\n  pushUint32(value: number): void\n  readByte(): Bytes[number]\n  readBytes(length: number, size?: number): Bytes\n  readUint8(): number\n  readUint16(): number\n  readUint24(): number\n  readUint32(): number\n  setPosition(position: number): () => void\n  _touch(): void\n}\n\nconst staticCursor: Cursor = /*#__PURE__*/ {\n  bytes: new Uint8Array(),\n  dataView: new DataView(new ArrayBuffer(0)),\n  position: 0,\n  positionReadCount: new Map(),\n  recursiveReadCount: 0,\n  recursiveReadLimit: Number.POSITIVE_INFINITY,\n  assertReadLimit() {\n    if (this.recursiveReadCount >= this.recursiveReadLimit)\n      throw new RecursiveReadLimitExceededError({\n        count: this.recursiveReadCount + 1,\n        limit: this.recursiveReadLimit,\n      })\n  },\n  assertPosition(position) {\n    if (position < 0 || position > this.bytes.length - 1)\n      throw new PositionOutOfBoundsError({\n        length: this.bytes.length,\n        position,\n      })\n  },\n  decrementPosition(offset) {\n    if (offset < 0) throw new NegativeOffsetError({ offset })\n    const position = this.position - offset\n    this.assertPosition(position)\n    this.position = position\n  },\n  getReadCount(position) {\n    return this.positionReadCount.get(position || this.position) || 0\n  },\n  incrementPosition(offset) {\n    if (offset < 0) throw new NegativeOffsetError({ offset })\n    const position = this.position + offset\n    this.assertPosition(position)\n    this.position = position\n  },\n  inspectByte(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position)\n    return this.bytes[position]!\n  },\n  inspectBytes(length, position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + length - 1)\n    return this.bytes.subarray(position, position + length)\n  },\n  inspectUint8(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position)\n    return this.bytes[position]!\n  },\n  inspectUint16(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 1)\n    return this.dataView.getUint16(position)\n  },\n  inspectUint24(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 2)\n    return (\n      (this.dataView.getUint16(position) << 8) +\n      this.dataView.getUint8(position + 2)\n    )\n  },\n  inspectUint32(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 3)\n    return this.dataView.getUint32(position)\n  },\n  pushByte(byte: Bytes[number]) {\n    this.assertPosition(this.position)\n    this.bytes[this.position] = byte\n    this.position++\n  },\n  pushBytes(bytes: Bytes) {\n    this.assertPosition(this.position + bytes.length - 1)\n    this.bytes.set(bytes, this.position)\n    this.position += bytes.length\n  },\n  pushUint8(value: number) {\n    this.assertPosition(this.position)\n    this.bytes[this.position] = value\n    this.position++\n  },\n  pushUint16(value: number) {\n    this.assertPosition(this.position + 1)\n    this.dataView.setUint16(this.position, value)\n    this.position += 2\n  },\n  pushUint24(value: number) {\n    this.assertPosition(this.position + 2)\n    this.dataView.setUint16(this.position, value >> 8)\n    this.dataView.setUint8(this.position + 2, value & ~4294967040)\n    this.position += 3\n  },\n  pushUint32(value: number) {\n    this.assertPosition(this.position + 3)\n    this.dataView.setUint32(this.position, value)\n    this.position += 4\n  },\n  readByte() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectByte()\n    this.position++\n    return value\n  },\n  readBytes(length, size) {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectBytes(length)\n    this.position += size ?? length\n    return value\n  },\n  readUint8() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint8()\n    this.position += 1\n    return value\n  },\n  readUint16() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint16()\n    this.position += 2\n    return value\n  },\n  readUint24() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint24()\n    this.position += 3\n    return value\n  },\n  readUint32() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint32()\n    this.position += 4\n    return value\n  },\n  get remaining() {\n    return this.bytes.length - this.position\n  },\n  setPosition(position) {\n    const oldPosition = this.position\n    this.assertPosition(position)\n    this.position = position\n    return () => (this.position = oldPosition)\n  },\n  _touch() {\n    if (this.recursiveReadLimit === Number.POSITIVE_INFINITY) return\n    const count = this.getReadCount()\n    this.positionReadCount.set(this.position, count + 1)\n    if (count > 0) this.recursiveReadCount++\n  },\n}\n\n/** @internal */\nexport function create(\n  bytes: Bytes,\n  { recursiveReadLimit = 8_192 }: create.Config = {},\n): Cursor {\n  const cursor: Cursor = Object.create(staticCursor)\n  cursor.bytes = bytes\n  cursor.dataView = new DataView(\n    bytes.buffer,\n    bytes.byteOffset,\n    bytes.byteLength,\n  )\n  cursor.positionReadCount = new Map()\n  cursor.recursiveReadLimit = recursiveReadLimit\n  return cursor\n}\n\n/** @internal */\nexport declare namespace create {\n  type Config = { recursiveReadLimit?: number | undefined }\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/** @internal */\nexport class NegativeOffsetError extends Errors.BaseError {\n  override readonly name = 'Cursor.NegativeOffsetError'\n\n  constructor({ offset }: { offset: number }) {\n    super(`Offset \\`${offset}\\` cannot be negative.`)\n  }\n}\n\n/** @internal */\nexport class PositionOutOfBoundsError extends Errors.BaseError {\n  override readonly name = 'Cursor.PositionOutOfBoundsError'\n\n  constructor({ length, position }: { length: number; position: number }) {\n    super(\n      `Position \\`${position}\\` is out of bounds (\\`0 < position < ${length}\\`).`,\n    )\n  }\n}\n\n/** @internal */\nexport class RecursiveReadLimitExceededError extends Errors.BaseError {\n  override readonly name = 'Cursor.RecursiveReadLimitExceededError'\n\n  constructor({ count, limit }: { count: number; limit: number }) {\n    super(\n      `Recursive read limit of \\`${limit}\\` exceeded (recursive read count: \\`${count}\\`).`,\n    )\n  }\n}\n", "import * as abitype from 'abitype'\nimport * as Address from './Address.js'\nimport * as Bytes from './Bytes.js'\nimport * as Errors from './Errors.js'\nimport * as Hex from './Hex.js'\nimport * as Solidity from './Solidity.js'\nimport * as internal from './internal/abiParameters.js'\nimport * as Cursor from './internal/cursor.js'\n\n/** Root type for ABI parameters. */\nexport type AbiParameters = readonly abitype.AbiParameter[]\n\n/** A parameter on an {@link ox#AbiParameters.AbiParameters}. */\nexport type Parameter = abitype.AbiParameter\n\n/** A packed ABI type. */\nexport type PackedAbiType =\n  | abitype.SolidityAddress\n  | abitype.SolidityBool\n  | abitype.SolidityBytes\n  | abitype.SolidityInt\n  | abitype.SolidityString\n  | abitype.SolidityArrayWithoutTuple\n\n/**\n * Decodes ABI-encoded data into its respective primitive values based on ABI Parameters.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.decode(\n *   AbiParameters.from(['string', 'uint', 'bool']),\n *   '0x000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000001a4000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000057761676d69000000000000000000000000000000000000000000000000000000',\n * )\n * // @log: ['wagmi', 420n, true]\n * ```\n *\n * @example\n * ### JSON Parameters\n *\n * You can pass **JSON ABI** Parameters:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.decode(\n *   [\n *     { name: 'x', type: 'string' },\n *     { name: 'y', type: 'uint' },\n *     { name: 'z', type: 'bool' },\n *   ],\n *   '0x000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000001a4000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000057761676d69000000000000000000000000000000000000000000000000000000',\n * )\n * // @log: ['wagmi', 420n, true]\n * ```\n *\n * @param parameters - The set of ABI parameters to decode, in the shape of the `inputs` or `outputs` attribute of an ABI Item. These parameters must include valid [ABI types](https://docs.soliditylang.org/en/latest/types.html).\n * @param data - ABI encoded data.\n * @param options - Decoding options.\n * @returns Array of decoded values.\n */\nexport function decode<\n  const parameters extends AbiParameters,\n  as extends 'Object' | 'Array' = 'Array',\n>(\n  parameters: parameters,\n  data: Bytes.Bytes | Hex.Hex,\n  options?: decode.Options<as>,\n): decode.ReturnType<parameters, as>\n\n// eslint-disable-next-line jsdoc/require-jsdoc\nexport function decode(\n  parameters: AbiParameters,\n  data: Bytes.Bytes | Hex.Hex,\n  options: {\n    as?: 'Array' | 'Object' | undefined\n    checksumAddress?: boolean | undefined\n  } = {},\n): readonly unknown[] | Record<string, unknown> {\n  const { as = 'Array', checksumAddress = false } = options\n\n  const bytes = typeof data === 'string' ? Bytes.fromHex(data) : data\n  const cursor = Cursor.create(bytes)\n\n  if (Bytes.size(bytes) === 0 && parameters.length > 0)\n    throw new ZeroDataError()\n  if (Bytes.size(bytes) && Bytes.size(bytes) < 32)\n    throw new DataSizeTooSmallError({\n      data: typeof data === 'string' ? data : Hex.fromBytes(data),\n      parameters: parameters as readonly Parameter[],\n      size: Bytes.size(bytes),\n    })\n\n  let consumed = 0\n  const values: any = as === 'Array' ? [] : {}\n  for (let i = 0; i < parameters.length; ++i) {\n    const param = parameters[i] as Parameter\n    cursor.setPosition(consumed)\n    const [data, consumed_] = internal.decodeParameter(cursor, param, {\n      checksumAddress,\n      staticPosition: 0,\n    })\n    consumed += consumed_\n    if (as === 'Array') values.push(data)\n    else values[param.name ?? i] = data\n  }\n  return values\n}\n\nexport declare namespace decode {\n  type Options<as extends 'Object' | 'Array'> = {\n    /**\n     * Whether the decoded values should be returned as an `Object` or `Array`.\n     *\n     * @default \"Array\"\n     */\n    as?: as | 'Object' | 'Array' | undefined\n    /**\n     * Whether decoded addresses should be checksummed.\n     *\n     * @default false\n     */\n    checksumAddress?: boolean | undefined\n  }\n\n  type ReturnType<\n    parameters extends AbiParameters = AbiParameters,\n    as extends 'Object' | 'Array' = 'Array',\n  > = parameters extends readonly []\n    ? as extends 'Object'\n      ? {}\n      : []\n    : as extends 'Object'\n      ? internal.ToObject<parameters>\n      : internal.ToPrimitiveTypes<parameters>\n\n  type ErrorType =\n    | Bytes.fromHex.ErrorType\n    | internal.decodeParameter.ErrorType\n    | ZeroDataError\n    | DataSizeTooSmallError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes primitive values into ABI encoded data as per the [Application Binary Interface (ABI) Specification](https://docs.soliditylang.org/en/latest/abi-spec).\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.encode(\n *   AbiParameters.from(['string', 'uint', 'bool']),\n *   ['wagmi', 420n, true],\n * )\n * ```\n *\n * @example\n * ### JSON Parameters\n *\n * Specify **JSON ABI** Parameters as schema:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.encode(\n *   [\n *     { type: 'string', name: 'name' },\n *     { type: 'uint', name: 'age' },\n *     { type: 'bool', name: 'isOwner' },\n *   ],\n *   ['wagmi', 420n, true],\n * )\n * ```\n *\n * @param parameters - The set of ABI parameters to encode, in the shape of the `inputs` or `outputs` attribute of an ABI Item. These parameters must include valid [ABI types](https://docs.soliditylang.org/en/latest/types.html).\n * @param values - The set of primitive values that correspond to the ABI types defined in `parameters`.\n * @returns ABI encoded data.\n */\nexport function encode<\n  const parameters extends AbiParameters | readonly unknown[],\n>(\n  parameters: parameters,\n  values: parameters extends AbiParameters\n    ? internal.ToPrimitiveTypes<parameters>\n    : never,\n  options?: encode.Options,\n): Hex.Hex {\n  const { checksumAddress = false } = options ?? {}\n\n  if (parameters.length !== values.length)\n    throw new LengthMismatchError({\n      expectedLength: parameters.length as number,\n      givenLength: values.length as any,\n    })\n  // Prepare the parameters to determine dynamic types to encode.\n  const preparedParameters = internal.prepareParameters({\n    checksumAddress,\n    parameters: parameters as readonly Parameter[],\n    values: values as any,\n  })\n  const data = internal.encode(preparedParameters)\n  if (data.length === 0) return '0x'\n  return data\n}\n\nexport declare namespace encode {\n  type ErrorType =\n    | LengthMismatchError\n    | internal.encode.ErrorType\n    | internal.prepareParameters.ErrorType\n    | Errors.GlobalErrorType\n\n  type Options = {\n    /**\n     * Whether addresses should be checked against their checksum.\n     *\n     * @default false\n     */\n    checksumAddress?: boolean | undefined\n  }\n}\n\n/**\n * Encodes an array of primitive values to a [packed ABI encoding](https://docs.soliditylang.org/en/latest/abi-spec.html#non-standard-packed-mode).\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const encoded = AbiParameters.encodePacked(\n *   ['address', 'string'],\n *   ['******************************************', 'hello world'],\n * )\n * // @log: '******************************************68656c6c6f20776f726c64'\n * ```\n *\n * @param types - Set of ABI types to pack encode.\n * @param values - The set of primitive values that correspond to the ABI types defined in `types`.\n * @returns The encoded packed data.\n */\nexport function encodePacked<\n  const packedAbiTypes extends readonly PackedAbiType[] | readonly unknown[],\n>(types: packedAbiTypes, values: encodePacked.Values<packedAbiTypes>): Hex.Hex {\n  if (types.length !== values.length)\n    throw new LengthMismatchError({\n      expectedLength: types.length as number,\n      givenLength: values.length as number,\n    })\n\n  const data: Hex.Hex[] = []\n  for (let i = 0; i < (types as unknown[]).length; i++) {\n    const type = types[i]\n    const value = values[i]\n    data.push(encodePacked.encode(type, value))\n  }\n  return Hex.concat(...data)\n}\n\nexport namespace encodePacked {\n  export type ErrorType =\n    | Hex.concat.ErrorType\n    | LengthMismatchError\n    | Errors.GlobalErrorType\n\n  export type Values<\n    packedAbiTypes extends readonly PackedAbiType[] | readonly unknown[],\n  > = {\n    [key in keyof packedAbiTypes]: packedAbiTypes[key] extends abitype.AbiType\n      ? abitype.AbiParameterToPrimitiveType<{ type: packedAbiTypes[key] }>\n      : unknown\n  }\n\n  // eslint-disable-next-line jsdoc/require-jsdoc\n  export function encode<const packedAbiType extends PackedAbiType | unknown>(\n    type: packedAbiType,\n    value: Values<[packedAbiType]>[0],\n    isArray = false,\n  ): Hex.Hex {\n    if (type === 'address') {\n      const address = value as Address.Address\n      Address.assert(address)\n      return Hex.padLeft(\n        address.toLowerCase() as Hex.Hex,\n        isArray ? 32 : 0,\n      ) as Address.Address\n    }\n    if (type === 'string') return Hex.fromString(value as string)\n    if (type === 'bytes') return value as Hex.Hex\n    if (type === 'bool')\n      return Hex.padLeft(Hex.fromBoolean(value as boolean), isArray ? 32 : 1)\n\n    const intMatch = (type as string).match(Solidity.integerRegex)\n    if (intMatch) {\n      const [_type, baseType, bits = '256'] = intMatch\n      const size = Number.parseInt(bits) / 8\n      return Hex.fromNumber(value as number, {\n        size: isArray ? 32 : size,\n        signed: baseType === 'int',\n      })\n    }\n\n    const bytesMatch = (type as string).match(Solidity.bytesRegex)\n    if (bytesMatch) {\n      const [_type, size] = bytesMatch\n      if (Number.parseInt(size!) !== ((value as Hex.Hex).length - 2) / 2)\n        throw new BytesSizeMismatchError({\n          expectedSize: Number.parseInt(size!),\n          value: value as Hex.Hex,\n        })\n      return Hex.padRight(value as Hex.Hex, isArray ? 32 : 0) as Hex.Hex\n    }\n\n    const arrayMatch = (type as string).match(Solidity.arrayRegex)\n    if (arrayMatch && Array.isArray(value)) {\n      const [_type, childType] = arrayMatch\n      const data: Hex.Hex[] = []\n      for (let i = 0; i < value.length; i++) {\n        data.push(encode(childType, value[i], true))\n      }\n      if (data.length === 0) return '0x'\n      return Hex.concat(...data)\n    }\n\n    throw new InvalidTypeError(type as string)\n  }\n}\n\n/**\n * Formats {@link ox#AbiParameters.AbiParameters} into **Human Readable ABI Parameters**.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const formatted = AbiParameters.format([\n *   {\n *     name: 'spender',\n *     type: 'address',\n *   },\n *   {\n *     name: 'amount',\n *     type: 'uint256',\n *   },\n * ])\n *\n * formatted\n * //    ^?\n *\n *\n * ```\n *\n * @param parameters - The ABI Parameters to format.\n * @returns The formatted ABI Parameters  .\n */\nexport function format<\n  const parameters extends readonly [\n    Parameter | abitype.AbiEventParameter,\n    ...(readonly (Parameter | abitype.AbiEventParameter)[]),\n  ],\n>(\n  parameters:\n    | parameters\n    | readonly [\n        Parameter | abitype.AbiEventParameter,\n        ...(readonly (Parameter | abitype.AbiEventParameter)[]),\n      ],\n): abitype.FormatAbiParameters<parameters> {\n  return abitype.formatAbiParameters(parameters)\n}\n\nexport declare namespace format {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Parses arbitrary **JSON ABI Parameters** or **Human Readable ABI Parameters** into typed {@link ox#AbiParameters.AbiParameters}.\n *\n * @example\n * ### JSON Parameters\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const parameters = AbiParameters.from([\n *   {\n *     name: 'spender',\n *     type: 'address',\n *   },\n *   {\n *     name: 'amount',\n *     type: 'uint256',\n *   },\n * ])\n *\n * parameters\n * //^?\n *\n *\n *\n *\n *\n *\n *\n * ```\n *\n * @example\n * ### Human Readable Parameters\n *\n * Human Readable ABI Parameters can be parsed into a typed {@link ox#AbiParameters.AbiParameters}:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const parameters = AbiParameters.from('address spender, uint256 amount')\n *\n * parameters\n * //^?\n *\n *\n *\n *\n *\n *\n *\n * ```\n *\n * @example\n * It is possible to specify `struct`s along with your definitions:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const parameters = AbiParameters.from([\n *   'struct Foo { address spender; uint256 amount; }', // [!code hl]\n *   'Foo foo, address bar',\n * ])\n *\n * parameters\n * //^?\n *\n *\n *\n *\n *\n *\n *\n *\n *\n *\n *\n *\n * ```\n *\n *\n *\n * @param parameters - The ABI Parameters to parse.\n * @returns The typed ABI Parameters.\n */\nexport function from<\n  const parameters extends AbiParameters | string | readonly string[],\n>(\n  parameters: parameters | AbiParameters | string | readonly string[],\n): from.ReturnType<parameters> {\n  if (Array.isArray(parameters) && typeof parameters[0] === 'string')\n    return abitype.parseAbiParameters(parameters) as never\n  if (typeof parameters === 'string')\n    return abitype.parseAbiParameters(parameters) as never\n  return parameters as never\n}\n\nexport declare namespace from {\n  type ReturnType<\n    parameters extends AbiParameters | string | readonly string[],\n  > = parameters extends string\n    ? abitype.ParseAbiParameters<parameters>\n    : parameters extends readonly string[]\n      ? abitype.ParseAbiParameters<parameters>\n      : parameters\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Throws when the data size is too small for the given parameters.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '0x010f')\n * //                                             ↑ ❌ 2 bytes\n * // @error: AbiParameters.DataSizeTooSmallError: Data size of 2 bytes is too small for given parameters.\n * // @error: Params: (uint256)\n * // @error: Data:   0x010f (2 bytes)\n * ```\n *\n * ### Solution\n *\n * Pass a valid data size.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '0x00000000000000000000000000000000000000000000000000000000000010f')\n * //                                             ↑ ✅ 32 bytes\n * ```\n */\nexport class DataSizeTooSmallError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.DataSizeTooSmallError'\n  constructor({\n    data,\n    parameters,\n    size,\n  }: { data: Hex.Hex; parameters: readonly Parameter[]; size: number }) {\n    super(`Data size of ${size} bytes is too small for given parameters.`, {\n      metaMessages: [\n        `Params: (${abitype.formatAbiParameters(parameters as readonly [Parameter])})`,\n        `Data:   ${data} (${size} bytes)`,\n      ],\n    })\n  }\n}\n\n/**\n * Throws when zero data is provided, but data is expected.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '0x')\n * //                                           ↑ ❌ zero data\n * // @error: AbiParameters.DataSizeTooSmallError: Data size of 2 bytes is too small for given parameters.\n * // @error: Params: (uint256)\n * // @error: Data:   0x010f (2 bytes)\n * ```\n *\n * ### Solution\n *\n * Pass valid data.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '0x00000000000000000000000000000000000000000000000000000000000010f')\n * //                                             ↑ ✅ 32 bytes\n * ```\n */\nexport class ZeroDataError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.ZeroDataError'\n  constructor() {\n    super('Cannot decode zero data (\"0x\") with ABI parameters.')\n  }\n}\n\n/**\n * The length of the array value does not match the length specified in the corresponding ABI parameter.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from('uint256[3]'), [[69n, 420n]])\n * //                                               ↑ expected: 3  ↑ ❌ length: 2\n * // @error: AbiParameters.ArrayLengthMismatchError: ABI encoding array length mismatch\n * // @error: for type `uint256[3]`. Expected: `3`. Given: `2`.\n * ```\n *\n * ### Solution\n *\n * Pass an array of the correct length.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['uint256[3]']), [[69n, 420n, 69n]])\n * //                                                         ↑ ✅ length: 3\n * ```\n */\nexport class ArrayLengthMismatchError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.ArrayLengthMismatchError'\n  constructor({\n    expectedLength,\n    givenLength,\n    type,\n  }: { expectedLength: number; givenLength: number; type: string }) {\n    super(\n      `Array length mismatch for type \\`${type}\\`. Expected: \\`${expectedLength}\\`. Given: \\`${givenLength}\\`.`,\n    )\n  }\n}\n\n/**\n * The size of the bytes value does not match the size specified in the corresponding ABI parameter.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from('bytes8'), [['0xdeadbeefdeadbeefdeadbeef']])\n * //                                            ↑ expected: 8 bytes  ↑ ❌ size: 12 bytes\n * // @error: BytesSizeMismatchError: Size of bytes \"0xdeadbeefdeadbeefdeadbeef\"\n * // @error: (bytes12) does not match expected size (bytes8).\n * ```\n *\n * ### Solution\n *\n * Pass a bytes value of the correct size.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['bytes8']), ['0xdeadbeefdeadbeef'])\n * //                                                       ↑ ✅ size: 8 bytes\n * ```\n */\nexport class BytesSizeMismatchError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.BytesSizeMismatchError'\n  constructor({\n    expectedSize,\n    value,\n  }: { expectedSize: number; value: Hex.Hex }) {\n    super(\n      `Size of bytes \"${value}\" (bytes${Hex.size(\n        value,\n      )}) does not match expected size (bytes${expectedSize}).`,\n    )\n  }\n}\n\n/**\n * The length of the values to encode does not match the length of the ABI parameters.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['string', 'uint256']), ['hello'])\n * // @error: LengthMismatchError: ABI encoding params/values length mismatch.\n * // @error: Expected length (params): 2\n * // @error: Given length (values): 1\n * ```\n *\n * ### Solution\n *\n * Pass the correct number of values to encode.\n *\n * ### Solution\n *\n * Pass a [valid ABI type](https://docs.soliditylang.org/en/develop/abi-spec.html#types).\n */\nexport class LengthMismatchError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.LengthMismatchError'\n  constructor({\n    expectedLength,\n    givenLength,\n  }: { expectedLength: number; givenLength: number }) {\n    super(\n      [\n        'ABI encoding parameters/values length mismatch.',\n        `Expected length (parameters): ${expectedLength}`,\n        `Given length (values): ${givenLength}`,\n      ].join('\\n'),\n    )\n  }\n}\n\n/**\n * The value provided is not a valid array as specified in the corresponding ABI parameter.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['uint256[3]']), [69])\n * ```\n *\n * ### Solution\n *\n * Pass an array value.\n */\nexport class InvalidArrayError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.InvalidArrayError'\n  constructor(value: unknown) {\n    super(`Value \\`${value}\\` is not a valid array.`)\n  }\n}\n\n/**\n * Throws when the ABI parameter type is invalid.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'lol' }], '0x00000000000000000000000000000000000000000000000000000000000010f')\n * //                             ↑ ❌ invalid type\n * // @error: AbiParameters.InvalidTypeError: Type `lol` is not a valid ABI Type.\n * ```\n */\nexport class InvalidTypeError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.InvalidTypeError'\n  constructor(type: string) {\n    super(`Type \\`${type}\\` is not a valid ABI Type.`)\n  }\n}\n", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBM,SAAU,mBAAmB,WAAc;AAC/C,QAAM,UAAU,UAAU,KAAK,UAAU,UAAU,CAAC,CAAC,EAAE,EAAE,UAAU,EAAE;AACrE,SAAO,gBAAgB,KAAK,OAAO,EAAE;AACvC;;;ACNA,eAAsB,iBAAiB,EACrC,MACA,UAAS,GACkB;AAC3B,QAAM,UAAU,MAAM,IAAI,IAAI,OAAOA,OAAM,IAAI;AAE/C,QAAM,EAAE,WAAAC,WAAS,IAAK,MAAM,OAAO,yBAAyB;AAC5D,QAAM,cAAc,MAAK;AAEvB,QAAI,OAAO,cAAc,YAAY,OAAO,aAAa,OAAO,WAAW;AACzE,YAAM,EAAE,GAAAC,IAAG,GAAG,GAAAC,IAAG,QAAO,IAAK;AAC7B,YAAMC,cAAa,OAAO,WAAWD,EAAC;AACtC,YAAME,eAAc,cAAcD,WAAU;AAC5C,aAAO,IAAIH,WAAU,UACnB,YAAYC,EAAC,GACb,YAAY,CAAC,CAAC,EACd,eAAeG,YAAW;IAC9B;AAGA,UAAM,eAAe,MAAM,SAAS,IAAI,YAAYL,OAAM,SAAS;AACnE,UAAM,aAAa,YAAY,KAAK,aAAa,MAAM,GAAG,CAAC,EAAE;AAC7D,UAAM,cAAc,cAAc,UAAU;AAC5C,WAAOC,WAAU,UAAU,YACzB,aAAa,UAAU,GAAG,GAAG,CAAC,EAC9B,eAAe,WAAW;EAC9B,GAAE;AAEF,QAAM,YAAY,WACf,iBAAiB,QAAQ,UAAU,CAAC,CAAC,EACrC,MAAM,KAAK;AACd,SAAO,KAAK,SAAS;AACvB;AAEA,SAAS,cAAc,YAAkB;AACvC,MAAI,eAAe,KAAK,eAAe;AAAG,WAAO;AACjD,MAAI,eAAe;AAAI,WAAO;AAC9B,MAAI,eAAe;AAAI,WAAO;AAC9B,QAAM,IAAI,MAAM,0BAA0B;AAC5C;;;AC5CA,eAAsB,eAAe,EACnC,MACA,UAAS,GACgB;AACzB,SAAO,mBAAmB,MAAM,iBAAiB,EAAE,MAAY,UAAS,CAAE,CAAC;AAC7E;;;ACgBO,IAAM,kBAAkB;EAC7B,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;;AAKH,SAAU,kBAAkB,aAAyC;AACzE,QAAM,eAAe;IACnB,GAAG;IACH,WAAW,YAAY,YAAY,YAAY,YAAY;IAC3D,aAAa,YAAY,cACrB,OAAO,YAAY,WAAW,IAC9B;IACJ,SAAS,YAAY,UAAU,YAAY,YAAY,OAAO,IAAI;IAClE,KAAK,YAAY,MAAM,OAAO,YAAY,GAAG,IAAI;IACjD,UAAU,YAAY,WAAW,OAAO,YAAY,QAAQ,IAAI;IAChE,kBAAkB,YAAY,mBAC1B,OAAO,YAAY,gBAAgB,IACnC;IACJ,cAAc,YAAY,eACtB,OAAO,YAAY,YAAY,IAC/B;IACJ,sBAAsB,YAAY,uBAC9B,OAAO,YAAY,oBAAoB,IACvC;IACJ,OAAO,YAAY,QAAQ,YAAY,YAAY,KAAK,IAAI;IAC5D,IAAI,YAAY,KAAK,YAAY,KAAK;IACtC,kBAAkB,YAAY,mBAC1B,OAAO,YAAY,gBAAgB,IACnC;IACJ,MAAM,YAAY,OACb,gBAAwB,YAAY,IAAI,IACzC;IACJ,SAAS,YAAY,OAAO,YAAY,OAAO;IAC/C,OAAO,YAAY,QAAQ,OAAO,YAAY,KAAK,IAAI;IACvD,GAAG,YAAY,IAAI,OAAO,YAAY,CAAC,IAAI;;AAG7C,MAAI,YAAY;AACd,iBAAa,oBAAoB,wBAC/B,YAAY,iBAAiB;AAGjC,eAAa,WAAW,MAAK;AAE3B,QAAI,YAAY;AAAS,aAAO,OAAO,YAAY,OAAO;AAG1D,QAAI,OAAO,aAAa,MAAM,UAAU;AACtC,UAAI,aAAa,MAAM,MAAM,aAAa,MAAM;AAAK,eAAO;AAC5D,UAAI,aAAa,MAAM,MAAM,aAAa,MAAM;AAAK,eAAO;AAC5D,UAAI,aAAa,KAAK;AAAK,eAAO,aAAa,IAAI,OAAO,KAAK,IAAI;IACrE;AAEA,WAAO;EACT,GAAE;AAEF,MAAI,aAAa,SAAS,UAAU;AAClC,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;EACtB;AACA,MAAI,aAAa,SAAS,WAAW;AACnC,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;EACtB;AACA,MAAI,aAAa,SAAS,WAAW;AACnC,WAAO,aAAa;EACtB;AACA,SAAO;AACT;AAIO,IAAM,oBAAkC,gBAC7C,eACA,iBAAiB;AAKnB,SAAS,wBACP,mBAAuC;AAEvC,SAAO,kBAAkB,IAAI,CAAC,mBAAmB;IAC/C,iBAAkB,cAAsB;IACxC,SAAS,OAAO,cAAc,OAAO;IACrC,OAAO,OAAO,cAAc,KAAK;IACjC,GAAG,cAAc;IACjB,GAAG,cAAc;IACjB,SAAS,OAAO,cAAc,OAAO;IACrC;AACJ;;;AC/FM,SAAU,YAAY,OAA6B;AACvD,QAAM,gBAAgB,MAAM,gBAAgB,CAAA,GAAI,IAAI,CAAC,gBAAe;AAClE,QAAI,OAAO,gBAAgB;AAAU,aAAO;AAC5C,WAAO,kBAAkB,WAAW;EACtC,CAAC;AACD,SAAO;IACL,GAAG;IACH,eAAe,MAAM,gBAAgB,OAAO,MAAM,aAAa,IAAI;IACnE,aAAa,MAAM,cAAc,OAAO,MAAM,WAAW,IAAI;IAC7D,YAAY,MAAM,aAAa,OAAO,MAAM,UAAU,IAAI;IAC1D,eAAe,MAAM,gBACjB,OAAO,MAAM,aAAa,IAC1B;IACJ,UAAU,MAAM,WAAW,OAAO,MAAM,QAAQ,IAAI;IACpD,SAAS,MAAM,UAAU,OAAO,MAAM,OAAO,IAAI;IACjD,MAAM,MAAM,OAAO,MAAM,OAAO;IAChC,WAAW,MAAM,YAAY,MAAM,YAAY;IAC/C,OAAO,MAAM,QAAQ,MAAM,QAAQ;IACnC,QAAQ,MAAM,SAAS,OAAO,MAAM,MAAM,IAAI;IAC9C,MAAM,MAAM,OAAO,OAAO,MAAM,IAAI,IAAI;IACxC,WAAW,MAAM,YAAY,OAAO,MAAM,SAAS,IAAI;IACvD;IACA,iBAAiB,MAAM,kBACnB,OAAO,MAAM,eAAe,IAC5B;;AAER;AAIO,IAAM,cAA4B,gBAAgB,SAAS,WAAW;;;ACR7E,eAAsB,oBAIpB,QACA,EAAE,SAAS,WAAW,UAAU,YAAW,GAAiC;AAE5E,QAAM,QAAQ,MAAM,OAAO,QACzB;IACE,QAAQ;IACR,QAAQ,CAAC,SAAS,cAAc,YAAY,WAAW,IAAI,QAAQ;KAErE,EAAE,QAAQ,QAAQ,WAAW,EAAC,CAAE;AAElC,SAAO,YAAY,KAAK;AAC1B;;;AC5EA,IAAM,sBAAsB;AAGrB,IAAM,uBAAuB;AAG7B,IAAM,uBAAuB;AAG7B,IAAM,eAAe,uBAAuB;AAG5C,IAAM,yBACX,eAAe;AAEf;AAEA,IAAI,uBAAuB;;;ACbvB,SAAU,UACd,KACA,EACE,MACA,UAAS,IACyD,CAAA,GAAE;AAEtE,SAAO;IACL,GAAG;IACH,WAAW,IAAI,YAAY,IAAI,YAAY;IAC3C,aAAa,IAAI,cAAc,OAAO,IAAI,WAAW,IAAI;IACzD,UAAU,IAAI,WAAW,OAAO,IAAI,QAAQ,IAAI;IAChD,iBAAiB,IAAI,kBAAkB,IAAI,kBAAkB;IAC7D,kBAAkB,IAAI,mBAClB,OAAO,IAAI,gBAAgB,IAC3B;IACJ,GAAI,YAAY,EAAE,MAAM,UAAS,IAAK,CAAA;;AAE1C;;;ACoCA,IAAM,0BAA0B,IAAI,OAAgB,GAAG;;;AC1DhD,IAAM,eAA6B,IAAI,OAAqB,IAAI;;;ACHvE,SAAS,gBAAa;AACpB,SAAO;IACL,SAAS;IACT,OAAI;AACF,aAAO,KAAK;IACd;IACA,QAAK;AACH,WAAK,UAAU;IACjB;;AAEJ;AAEO,IAAM,UAAwB,cAAa;;;ACU3C,IAAM,kBAAkB;EAC7B,OAAO;EACP,OAAO;;AAKH,SAAU,yBACd,oBAAuD;AAEvD,QAAM,UAAU;IACd,GAAG;IACH,aAAa,mBAAmB,cAC5B,OAAO,mBAAmB,WAAW,IACrC;IACJ,iBAAiB,mBAAmB,kBAChC,mBAAmB,kBACnB;IACJ,mBAAmB,mBAAmB,oBAClC,OAAO,mBAAmB,iBAAiB,IAC3C;IACJ,mBAAmB,mBAAmB,oBAClC,OAAO,mBAAmB,iBAAiB,IAC3C;IACJ,SAAS,mBAAmB,UACxB,OAAO,mBAAmB,OAAO,IACjC;IACJ,MAAM,mBAAmB,OACrB,mBAAmB,KAAK,IAAI,CAAC,QAAQ,UAAU,GAAG,CAAC,IACnD;IACJ,IAAI,mBAAmB,KAAK,mBAAmB,KAAK;IACpD,kBAAkB,mBAAmB,mBACjC,YAAY,mBAAmB,gBAAgB,IAC/C;IACJ,QAAQ,mBAAmB,SACvB,gBAAgB,mBAAmB,MAAM,IACzC;IACJ,MAAM,mBAAmB,OACrB,gBACE,mBAAmB,IAAoC,KACpD,mBAAmB,OACxB;;AAGN,MAAI,mBAAmB;AACrB,YAAQ,eAAe,OAAO,mBAAmB,YAAY;AAC/D,MAAI,mBAAmB;AACrB,YAAQ,cAAc,OAAO,mBAAmB,WAAW;AAE7D,SAAO;AACT;AAMO,IAAM,2BAAyC,gBACpD,sBACA,wBAAwB;;;ACvE1B,IAAM,MAAsB,IAAI,WAAW,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC;AACjG,IAAM,KAAqB,IAAI,WAAW,IAAI,MAAM,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAGK,OAAMA,EAAC,CAAC;AAChF,IAAM,KAAqB,GAAG,IAAI,CAACA,QAAO,IAAIA,KAAI,KAAK,EAAE;AACzD,IAAI,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,CAAC,EAAE;AACd,SAASA,KAAI,GAAGA,KAAI,GAAGA;AAAK,WAASC,MAAK,CAAC,MAAM,IAAI;AAAG,IAAAA,GAAE,KAAKA,GAAED,EAAC,EAAE,IAAI,CAACE,OAAM,IAAIA,EAAC,CAAC,CAAC;AAEtF,IAAM,SAAyB;EAC7B,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,IAAI,CAACF,OAAM,IAAI,WAAWA,EAAC,CAAC;AAC9B,IAAM,UAA0B,KAAK,IAAI,CAAC,KAAKA,OAAM,IAAI,IAAI,CAACC,OAAM,OAAOD,EAAC,EAAEC,EAAC,CAAC,CAAC;AACjF,IAAM,UAA0B,KAAK,IAAI,CAAC,KAAKD,OAAM,IAAI,IAAI,CAACC,OAAM,OAAOD,EAAC,EAAEC,EAAC,CAAC,CAAC;AACjF,IAAM,KAAqB,IAAI,YAAY;EACzC;EAAY;EAAY;EAAY;EAAY;CACjD;AACD,IAAM,KAAqB,IAAI,YAAY;EACzC;EAAY;EAAY;EAAY;EAAY;CACjD;AAED,SAASE,GAAE,OAAeC,IAAWC,IAAWC,IAAS;AACvD,MAAI,UAAU;AAAG,WAAOF,KAAIC,KAAIC;WACvB,UAAU;AAAG,WAAQF,KAAIC,KAAM,CAACD,KAAIE;WACpC,UAAU;AAAG,YAAQF,KAAI,CAACC,MAAKC;WAC/B,UAAU;AAAG,WAAQF,KAAIE,KAAMD,KAAI,CAACC;;AACxC,WAAOF,MAAKC,KAAI,CAACC;AACxB;AAEA,IAAM,QAAwB,IAAI,YAAY,EAAE;AAC1C,IAAO,YAAP,cAAyB,OAAiB;EAO9C,cAAA;AACE,UAAM,IAAI,IAAI,GAAG,IAAI;AAPf,SAAA,KAAK,aAAa;AAClB,SAAA,KAAK,aAAa;AAClB,SAAA,KAAK,aAAa;AAClB,SAAA,KAAK,YAAa;AAClB,SAAA,KAAK,aAAa;EAI1B;EACU,MAAG;AACX,UAAM,EAAE,IAAI,IAAI,IAAAC,KAAI,IAAAC,KAAI,GAAE,IAAK;AAC/B,WAAO,CAAC,IAAI,IAAID,KAAIC,KAAI,EAAE;EAC5B;EACU,IAAI,IAAY,IAAYD,KAAYC,KAAY,IAAU;AACtE,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAKD,MAAK;AACf,SAAK,KAAKC,MAAK;AACf,SAAK,KAAK,KAAK;EACjB;EACU,QAAQ,MAAgB,QAAc;AAC9C,aAASR,KAAI,GAAGA,KAAI,IAAIA,MAAK,UAAU;AAAG,YAAMA,EAAC,IAAI,KAAK,UAAU,QAAQ,IAAI;AAEhF,QAAI,KAAK,KAAK,KAAK,GAAGS,MAAK,IACvB,KAAK,KAAK,KAAK,GAAGC,MAAK,IACvB,KAAK,KAAK,KAAK,GAAGC,MAAK,IACvB,KAAK,KAAK,KAAK,GAAGC,MAAK,IACvB,KAAK,KAAK,KAAK,GAAGC,MAAK;AAI3B,aAAS,QAAQ,GAAG,QAAQ,GAAG,SAAS;AACtC,YAAM,SAAS,IAAI;AACnB,YAAM,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK;AACrC,YAAM,KAAK,KAAK,KAAK,GAAGC,MAAK,KAAK,KAAK;AACvC,YAAM,KAAK,QAAQ,KAAK,GAAGC,MAAK,QAAQ,KAAK;AAC7C,eAASf,KAAI,GAAGA,KAAI,IAAIA,MAAK;AAC3B,cAAM,KAAM,KAAK,KAAKG,GAAE,OAAO,IAAI,IAAI,EAAE,IAAI,MAAM,GAAGH,EAAC,CAAC,IAAI,KAAK,GAAGA,EAAC,CAAC,IAAI,KAAM;AAChF,aAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,EAAE,IAAI,GAAG,KAAK,IAAI,KAAK;MACzD;AAEA,eAASA,KAAI,GAAGA,KAAI,IAAIA,MAAK;AAC3B,cAAMgB,MAAM,KAAKP,MAAKN,GAAE,QAAQO,KAAIC,KAAIC,GAAE,IAAI,MAAME,IAAGd,EAAC,CAAC,IAAI,KAAKe,IAAGf,EAAC,CAAC,IAAIa,MAAM;AACjF,QAAAJ,MAAKI,KAAIA,MAAKD,KAAIA,MAAK,KAAKD,KAAI,EAAE,IAAI,GAAGA,MAAKD,KAAIA,MAAKM;MACzD;IACF;AAEA,SAAK,IACF,KAAK,KAAK,KAAKJ,MAAM,GACrB,KAAK,KAAK,KAAKC,MAAM,GACrB,KAAK,KAAK,KAAKJ,MAAM,GACrB,KAAK,KAAK,KAAKC,MAAM,GACrB,KAAK,KAAK,KAAKC,MAAM,CAAC;EAE3B;EACU,aAAU;AAClB,UAAM,KAAK,CAAC;EACd;EACA,UAAO;AACL,SAAK,YAAY;AACjB,SAAK,OAAO,KAAK,CAAC;AAClB,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;EACxB;;AAIK,IAAM,YAAmC,gBAAgB,MAAM,IAAI,UAAS,CAAE;;;ACnE/E,SAAU,mBACd,YAAwC;AAExC,QAAM,EAAE,OAAM,IAAK;AAEnB,QAAM,WAAW,oBAAI,IAAG;AACxB,QAAM,WAAW,IAAI,OAAe,IAAI;AACxC,QAAM,aAAa,oBAAI,IAAG;AAE1B,QAAM,SAAS,CAAC,EAAE,SAAS,QAAO,MAChC,GAAG,OAAO,IAAI,OAAO;AAEvB,SAAO;IACL,MAAM,QAAQ,EAAE,SAAS,SAAS,OAAM,GAAE;AACxC,YAAM,MAAM,OAAO,EAAE,SAAS,QAAO,CAAE;AACvC,YAAM,UAAU,KAAK,IAAI,EAAE,SAAS,SAAS,OAAM,CAAE;AAErD,WAAK,UAAU,EAAE,SAAS,QAAO,CAAE;AACnC,YAAM,QAAQ,MAAM;AAEpB,YAAM,OAAO,IAAI,EAAE,SAAS,QAAO,GAAI,KAAK;AAC5C,eAAS,IAAI,KAAK,KAAK;AAEvB,aAAO;IACT;IACA,MAAM,UAAU,EAAE,SAAS,QAAO,GAAE;AAClC,YAAM,MAAM,OAAO,EAAE,SAAS,QAAO,CAAE;AACvC,YAAM,QAAQ,SAAS,IAAI,GAAG,KAAK;AACnC,eAAS,IAAI,KAAK,QAAQ,CAAC;IAC7B;IACA,MAAM,IAAI,EAAE,SAAS,SAAS,OAAM,GAAE;AACpC,YAAM,MAAM,OAAO,EAAE,SAAS,QAAO,CAAE;AAEvC,UAAI,UAAU,WAAW,IAAI,GAAG;AAChC,UAAI,CAAC,SAAS;AACZ,mBAAW,YAAW;AACpB,cAAI;AACF,kBAAM,QAAQ,MAAM,OAAO,IAAI,EAAE,SAAS,SAAS,OAAM,CAAE;AAC3D,kBAAM,gBAAgB,SAAS,IAAI,GAAG,KAAK;AAC3C,gBAAI,gBAAgB,KAAK,SAAS;AAChC,qBAAO,gBAAgB;AACzB,qBAAS,OAAO,GAAG;AACnB,mBAAO;UACT;AACE,iBAAK,MAAM,EAAE,SAAS,QAAO,CAAE;UACjC;QACF,GAAE;AACF,mBAAW,IAAI,KAAK,OAAO;MAC7B;AAEA,YAAM,QAAQ,SAAS,IAAI,GAAG,KAAK;AACnC,aAAO,QAAS,MAAM;IACxB;IACA,MAAM,EAAE,SAAS,QAAO,GAAE;AACxB,YAAM,MAAM,OAAO,EAAE,SAAS,QAAO,CAAE;AACvC,eAAS,OAAO,GAAG;AACnB,iBAAW,OAAO,GAAG;IACvB;;AAEJ;AAaM,SAAU,UAAO;AACrB,SAAO;IACL,MAAM,IAAI,YAAU;AAClB,YAAM,EAAE,SAAS,OAAM,IAAK;AAC5B,aAAO,oBAAoB,QAAQ;QACjC;QACA,UAAU;OACX;IACH;IACA,MAAG;IAAI;;AAEX;AAMO,IAAM,eAA6B,mBAAmB;EAC3D,QAAQ,QAAO;CAChB;;;ACnIM,IAAM,UAAU;;;ACOjB,SAAU,aAAU;AACxB,SAAO;AACT;;;ACKM,IAAOM,aAAP,MAAO,mBAEH,MAAK;EAWb,YAAY,cAAsB,UAAoC,CAAA,GAAE;AACtE,UAAM,WAAW,MAAK;AA7B1B;AA8BM,UAAI,QAAQ,iBAAiB,YAAW;AACtC,YAAI,QAAQ,MAAM;AAAS,iBAAO,QAAQ,MAAM;AAChD,YAAI,QAAQ,MAAM;AAAc,iBAAO,QAAQ,MAAM;MACvD;AACA,WAAI,aAAQ,UAAR,mBAAe;AAAS,eAAO,QAAQ,MAAM;AACjD,aAAO,QAAQ;IACjB,GAAE;AACF,UAAM,YAAY,MAAK;AACrB,UAAI,QAAQ,iBAAiB;AAC3B,eAAO,QAAQ,MAAM,YAAY,QAAQ;AAC3C,aAAO,QAAQ;IACjB,GAAE;AAEF,UAAM,cAAc;AACpB,UAAM,OAAO,GAAG,WAAW,GAAG,YAAY,EAAE;AAE5C,UAAM,UAAU;MACd,gBAAgB;MAChB,GAAI,QAAQ,eAAe,CAAC,IAAI,GAAG,QAAQ,YAAY,IAAI,CAAA;MAC3D,GAAI,WAAW,WACX;QACE;QACA,UAAU,YAAY,OAAO,KAAK;QAClC,WAAW,QAAQ,IAAI,KAAK;UAE9B,CAAA;MAEH,OAAO,CAACC,OAAM,OAAOA,OAAM,QAAQ,EACnC,KAAK,IAAI;AAEZ,UAAM,SAAS,QAAQ,QAAQ,EAAE,OAAO,QAAQ,MAAK,IAAK,MAAS;AA1CrE,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,QAAA;;;;;;AACA,WAAA,eAAA,MAAA,YAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AAES,WAAA,eAAA,MAAA,SAAA;;;;;;AACA,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,WAAA;;;;aAAU,MAAM,WAAU,CAAE;;AAoC1B,SAAK,QAAQ,QAAQ;AACrB,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,eAAe;EACtB;EAIA,KAAKC,KAAQ;AACX,WAAO,KAAK,MAAMA,GAAE;EACtB;;AAaF,SAAS,KACP,KACAA,KAA4C;AAE5C,MAAIA,OAAA,gBAAAA,IAAK;AAAM,WAAO;AACtB,MAAI,OAAO,OAAO,QAAQ,YAAY,WAAW,OAAO,IAAI;AAC1D,WAAO,KAAK,IAAI,OAAOA,GAAE;AAC3B,SAAOA,MAAK,OAAO;AACrB;;;AC1FM,SAAU,WAAW,OAAoB,OAAa;AAC1D,MAAUC,MAAK,KAAK,IAAI;AACtB,UAAM,IAAU,kBAAkB;MAChC,WAAiBA,MAAK,KAAK;MAC3B,SAAS;KACV;AACL;AA0DO,IAAM,cAAc;EACzB,MAAM;EACN,MAAM;EACN,GAAG;EACH,GAAG;EACH,GAAG;EACH,GAAG;;AAIC,SAAU,iBAAiB,MAAY;AAC3C,MAAI,QAAQ,YAAY,QAAQ,QAAQ,YAAY;AAClD,WAAO,OAAO,YAAY;AAC5B,MAAI,QAAQ,YAAY,KAAK,QAAQ,YAAY;AAC/C,WAAO,QAAQ,YAAY,IAAI;AACjC,MAAI,QAAQ,YAAY,KAAK,QAAQ,YAAY;AAC/C,WAAO,QAAQ,YAAY,IAAI;AACjC,SAAO;AACT;AAGM,SAAUC,KAAI,OAAoB,UAAuB,CAAA,GAAE;AAC/D,QAAM,EAAE,KAAK,MAAAC,QAAO,GAAE,IAAK;AAC3B,MAAIA,UAAS;AAAG,WAAO;AACvB,MAAI,MAAM,SAASA;AACjB,UAAM,IAAU,4BAA4B;MAC1C,MAAM,MAAM;MACZ,YAAYA;MACZ,MAAM;KACP;AACH,QAAM,cAAc,IAAI,WAAWA,KAAI;AACvC,WAASC,KAAI,GAAGA,KAAID,OAAMC,MAAK;AAC7B,UAAM,SAAS,QAAQ;AACvB,gBAAY,SAASA,KAAID,QAAOC,KAAI,CAAC,IACnC,MAAM,SAASA,KAAI,MAAM,SAASA,KAAI,CAAC;EAC3C;AACA,SAAO;AACT;;;ACrGM,SAAUC,YAAW,KAAc,OAAa;AACpD,MAAQC,MAAK,GAAG,IAAI;AAClB,UAAM,IAAQC,mBAAkB;MAC9B,WAAeD,MAAK,GAAG;MACvB,SAAS;KACV;AACL;AAsDM,SAAUE,KAAI,MAAe,UAAuB,CAAA,GAAE;AAC1D,QAAM,EAAE,KAAK,MAAAC,QAAO,GAAE,IAAK;AAE3B,MAAIA,UAAS;AAAG,WAAO;AAEvB,QAAM,MAAM,KAAK,QAAQ,MAAM,EAAE;AACjC,MAAI,IAAI,SAASA,QAAO;AACtB,UAAM,IAAQC,6BAA4B;MACxC,MAAM,KAAK,KAAK,IAAI,SAAS,CAAC;MAC9B,YAAYD;MACZ,MAAM;KACP;AAEH,SAAO,KAAK,IAAI,QAAQ,UAAU,WAAW,UAAU,EAAEA,QAAO,GAAG,GAAG,CAAC;AACzE;;;ACvEA,IAAM,UAAwB,IAAI,YAAW;AAC7C,IAAM,UAAwB,IAAI,YAAW;AAmGvC,SAAU,KAAK,OAA0C;AAC7D,MAAI,iBAAiB;AAAY,WAAO;AACxC,MAAI,OAAO,UAAU;AAAU,WAAOE,SAAQ,KAAK;AACnD,SAAO,UAAU,KAAK;AACxB;AAuBM,SAAU,UAAU,OAAqC;AAC7D,SAAO,iBAAiB,aAAa,QAAQ,IAAI,WAAW,KAAK;AACnE;AA2EM,SAAUC,SAAQ,OAAgB,UAA2B,CAAA,GAAE;AACnE,QAAM,EAAE,MAAAC,MAAI,IAAK;AAEjB,MAAI,MAAM;AACV,MAAIA,OAAM;AACR,IAAaC,YAAW,OAAOD,KAAI;AACnC,UAAU,SAAS,OAAOA,KAAI;EAChC;AAEA,MAAI,YAAY,IAAI,MAAM,CAAC;AAC3B,MAAI,UAAU,SAAS;AAAG,gBAAY,IAAI,SAAS;AAEnD,QAAM,SAAS,UAAU,SAAS;AAClC,QAAM,QAAQ,IAAI,WAAW,MAAM;AACnC,WAAS,QAAQ,GAAGE,KAAI,GAAG,QAAQ,QAAQ,SAAS;AAClD,UAAM,aAAsB,iBAAiB,UAAU,WAAWA,IAAG,CAAC;AACtE,UAAM,cAAuB,iBAAiB,UAAU,WAAWA,IAAG,CAAC;AACvE,QAAI,eAAe,UAAa,gBAAgB,QAAW;AACzD,YAAM,IAAWC,WACf,2BAA2B,UAAUD,KAAI,CAAC,CAAC,GAAG,UAAUA,KAAI,CAAC,CAAC,SAAS,SAAS,KAAK;IAEzF;AACA,UAAM,KAAK,IAAI,aAAa,KAAK;EACnC;AACA,SAAO;AACT;AA6EM,SAAUE,YACd,OACA,UAA8B,CAAA,GAAE;AAEhC,QAAM,EAAE,MAAAC,MAAI,IAAK;AAEjB,QAAM,QAAQ,QAAQ,OAAO,KAAK;AAClC,MAAI,OAAOA,UAAS,UAAU;AAC5B,IAAS,WAAW,OAAOA,KAAI;AAC/B,WAAOC,UAAS,OAAOD,KAAI;EAC7B;AACA,SAAO;AACT;AAkFM,SAAUE,UACd,OACAC,OAAyB;AAEzB,SAAgBC,KAAI,OAAO,EAAE,KAAK,SAAS,MAAAD,MAAI,CAAE;AACnD;AA2CM,SAAUE,MAAK,OAAY;AAC/B,SAAO,MAAM;AACf;AA0WM,IAAO,oBAAP,cAAwCC,WAAS;EAGrD,YAAY,EAAE,WAAW,QAAO,GAA0C;AACxE,UACE,wBAAwB,OAAO,2BAA2B,SAAS,WAAW;AAJhE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAyCI,IAAO,8BAAP,cAAkDC,WAAS;EAG/D,YAAY,EACV,MAAAC,OACA,YACA,KAAI,GAKL;AACC,UACE,GAAG,KAAK,OAAO,CAAC,EAAE,YAAW,CAAE,GAAG,KAC/B,MAAM,CAAC,EACP,YAAW,CAAE,YAAYA,KAAI,+BAA+B,UAAU,MAAM;AAdjE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAgBzB;;;;AC72BF,IAAMC,WAAwB,IAAI,YAAW;AAE7C,IAAM,QAAsB,MAAM,KAAK,EAAE,QAAQ,IAAG,GAAI,CAAC,IAAIC,OAC3DA,GAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AA6D3B,SAAUC,WAAU,QAAsB;AAC9C,SAAO,KAAM,OAAiB,OAAO,CAAC,KAAKC,OAAM,MAAMA,GAAE,QAAQ,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AAuEM,SAAU,YACd,OACA,UAA+B,CAAA,GAAE;AAEjC,QAAM,MAAW,KAAK,OAAO,KAAK,CAAC;AACnC,MAAI,OAAO,QAAQ,SAAS,UAAU;AACpC,IAASC,YAAW,KAAK,QAAQ,IAAI;AACrC,WAAO,QAAQ,KAAK,QAAQ,IAAI;EAClC;AACA,SAAO;AACT;AA6BM,SAAUC,WACd,OACA,UAA6B,CAAA,GAAE;AAE/B,MAAI,SAAS;AACb,WAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA;AAAK,cAAU,MAAM,MAAMA,EAAC,CAAE;AAChE,QAAM,MAAM,KAAK,MAAM;AAEvB,MAAI,OAAO,QAAQ,SAAS,UAAU;AACpC,IAASF,YAAW,KAAK,QAAQ,IAAI;AACrC,WAAO,SAAS,KAAK,QAAQ,IAAI;EACnC;AACA,SAAO;AACT;AAgCM,SAAU,WACd,OACA,UAA8B,CAAA,GAAE;AAEhC,QAAM,EAAE,QAAQ,MAAAG,MAAI,IAAK;AAEzB,QAAM,SAAS,OAAO,KAAK;AAE3B,MAAI;AACJ,MAAIA,OAAM;AACR,QAAI;AAAQ,kBAAY,MAAO,OAAOA,KAAI,IAAI,KAAK,MAAO;;AACrD,iBAAW,OAAO,OAAOA,KAAI,IAAI,MAAM;EAC9C,WAAW,OAAO,UAAU,UAAU;AACpC,eAAW,OAAO,OAAO,gBAAgB;EAC3C;AAEA,QAAM,WAAW,OAAO,aAAa,YAAY,SAAS,CAAC,WAAW,KAAK;AAE3E,MAAK,YAAY,SAAS,YAAa,SAAS,UAAU;AACxD,UAAM,SAAS,OAAO,UAAU,WAAW,MAAM;AACjD,UAAM,IAAI,uBAAuB;MAC/B,KAAK,WAAW,GAAG,QAAQ,GAAG,MAAM,KAAK;MACzC,KAAK,GAAG,QAAQ,GAAG,MAAM;MACzB;MACA,MAAAA;MACA,OAAO,GAAG,KAAK,GAAG,MAAM;KACzB;EACH;AAEA,QAAM,eACJ,UAAU,SAAS,KAAK,MAAM,OAAOA,QAAO,CAAC,KAAK,OAAO,MAAM,IAAI,QACnE,SAAS,EAAE;AAEb,QAAM,MAAM,KAAK,WAAW;AAC5B,MAAIA;AAAM,WAAO,QAAQ,KAAKA,KAAI;AAClC,SAAO;AACT;AAuCM,SAAUC,YACd,OACA,UAA8B,CAAA,GAAE;AAEhC,SAAOH,WAAUI,SAAQ,OAAO,KAAK,GAAG,OAAO;AACjD;AAoDM,SAAU,QACd,OACAC,OAAyB;AAEzB,SAAgBC,KAAI,OAAO,EAAE,KAAK,QAAQ,MAAAD,MAAI,CAAE;AAClD;AAsBM,SAAU,SACd,OACAA,OAAyB;AAEzB,SAAgBC,KAAI,OAAO,EAAE,KAAK,SAAS,MAAAD,MAAI,CAAE;AACnD;AAsFM,SAAUE,MAAK,OAAU;AAC7B,SAAO,KAAK,MAAM,MAAM,SAAS,KAAK,CAAC;AACzC;AAoSM,IAAO,yBAAP,cAA6CC,WAAS;EAG1D,YAAY,EACV,KACA,KACA,QACA,MAAAC,OACA,MAAK,GAON;AACC,UACE,YAAY,KAAK,oBACfA,QAAO,IAAIA,QAAO,CAAC,SAAS,EAC9B,GAAG,SAAS,YAAY,WAAW,kBAAkB,MAAM,MAAM,GAAG,WAAW,GAAG,QAAQ,YAAY,GAAG,KAAK,EAAE;AAlBlG,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAoBzB;;AA8GI,IAAOC,qBAAP,cAAwCC,WAAS;EAGrD,YAAY,EAAE,WAAW,QAAO,GAA0C;AACxE,UACE,wBAAwB,OAAO,2BAA2B,SAAS,WAAW;AAJhE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAyCI,IAAOC,+BAAP,cAAkDC,WAAS;EAG/D,YAAY,EACV,MAAAC,OACA,YACA,KAAI,GAKL;AACC,UACE,GAAG,KAAK,OAAO,CAAC,EAAE,YAAW,CAAE,GAAG,KAC/B,MAAM,CAAC,EACP,YAAW,CAAE,YAAYA,KAAI,+BAA+B,UAAU,MAAM;AAdjE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAgBzB;;;;AC55BI,SAAUC,WAMd,OACA,UAAiC,CAAA,GAAE;AAEnC,QAAM,EAAE,KAAK,OAAO,UAAU,WAAW,QAAQ,QAAO,IAAK;AAC7D,QAAM,QAAQ,WAAsB,KAAK,KAAK,CAAC;AAC/C,MAAI,OAAO;AAAS,WAAO;AAC3B,SAAWC,WAAU,KAAK;AAC5B;;;ACnDM,IAAOC,UAAP,cAAuC,IAAkB;EAG7D,YAAYC,OAAY;AACtB,UAAK;AAHP,WAAA,eAAA,MAAA,WAAA;;;;;;AAIE,SAAK,UAAUA;EACjB;EAES,IAAI,KAAW;AACtB,UAAM,QAAQ,MAAM,IAAI,GAAG;AAE3B,QAAI,MAAM,IAAI,GAAG,KAAK,UAAU,QAAW;AACzC,WAAK,OAAO,GAAG;AACf,YAAM,IAAI,KAAK,KAAK;IACtB;AAEA,WAAO;EACT;EAES,IAAI,KAAa,OAAY;AACpC,UAAM,IAAI,KAAK,KAAK;AACpB,QAAI,KAAK,WAAW,KAAK,OAAO,KAAK,SAAS;AAC5C,YAAM,WAAW,KAAK,KAAI,EAAG,KAAI,EAAG;AACpC,UAAI;AAAU,aAAK,OAAO,QAAQ;IACpC;AACA,WAAO;EACT;;;;AC7BF,IAAM,SAAS;EACb,UAAwB,IAAIC,QAAwB,IAAI;;AAGnD,IAAM,WAAW,OAAO;;;ACA/B,IAAM,eAA6B;AA0B7B,SAAU,OACd,OACA,UAA0B,CAAA,GAAE;AAE5B,QAAM,EAAE,SAAS,KAAI,IAAK;AAE1B,MAAI,CAAC,aAAa,KAAK,KAAK;AAC1B,UAAM,IAAIC,qBAAoB;MAC5B,SAAS;MACT,OAAO,IAAI,kBAAiB;KAC7B;AAEH,MAAI,QAAQ;AACV,QAAI,MAAM,YAAW,MAAO;AAAO;AACnC,QAAIC,UAAS,KAAgB,MAAM;AACjC,YAAM,IAAID,qBAAoB;QAC5B,SAAS;QACT,OAAO,IAAI,qBAAoB;OAChC;EACL;AACF;AA6BM,SAAUC,UAAS,SAAe;AACtC,MAAW,SAAS,IAAI,OAAO;AAAG,WAAc,SAAS,IAAI,OAAO;AAEpE,SAAO,SAAS,EAAE,QAAQ,MAAK,CAAE;AAEjC,QAAM,aAAa,QAAQ,UAAU,CAAC,EAAE,YAAW;AACnD,QAAM,OAAYC,WAAgBC,YAAW,UAAU,GAAG,EAAE,IAAI,QAAO,CAAE;AAEzE,QAAM,aAAa,WAAW,MAAM,EAAE;AACtC,WAASC,KAAI,GAAGA,KAAI,IAAIA,MAAK,GAAG;AAC9B,QAAI,KAAKA,MAAK,CAAC,KAAM,KAAK,KAAK,WAAWA,EAAC,GAAG;AAC5C,iBAAWA,EAAC,IAAI,WAAWA,EAAC,EAAG,YAAW;IAC5C;AACA,SAAK,KAAKA,MAAK,CAAC,IAAK,OAAS,KAAK,WAAWA,KAAI,CAAC,GAAG;AACpD,iBAAWA,KAAI,CAAC,IAAI,WAAWA,KAAI,CAAC,EAAG,YAAW;IACpD;EACF;AAEA,QAAM,SAAS,KAAK,WAAW,KAAK,EAAE,CAAC;AACvC,EAAO,SAAS,IAAI,SAAS,MAAM;AACnC,SAAO;AACT;AA+MM,IAAOC,uBAAP,cAIWC,WAAgB;EAG/B,YAAY,EAAE,SAAS,MAAK,GAAqC;AAC/D,UAAM,YAAY,OAAO,iBAAiB;MACxC;KACD;AALe,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAII,IAAO,oBAAP,cAAwCA,WAAS;EAGrD,cAAA;AACE,UAAM,4DAA4D;AAHlD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;AAII,IAAO,uBAAP,cAA2CA,WAAS;EAGxD,cAAA;AACE,UAAM,kDAAkD;AAHxC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;;;ACnVK,IAAMC,cAAa;AAInB,IAAMC,cAAa;AAInB,IAAMC,gBACX;AAEK,IAAM,UAAU,OAAO,KAAK,MAAM;AAClC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AAEtC,IAAM,UAAU,EAAE,OAAO,KAAK;AAC9B,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAElC,IAAM,WAAW,MAAM,KAAK;AAC5B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAMC,cAAa,MAAM,OAAO;;;ACrEvC,IAAM,eAAqC;EACzC,OAAO,IAAI,WAAU;EACrB,UAAU,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC;EACzC,UAAU;EACV,mBAAmB,oBAAI,IAAG;EAC1B,oBAAoB;EACpB,oBAAoB,OAAO;EAC3B,kBAAe;AACb,QAAI,KAAK,sBAAsB,KAAK;AAClC,YAAM,IAAI,gCAAgC;QACxC,OAAO,KAAK,qBAAqB;QACjC,OAAO,KAAK;OACb;EACL;EACA,eAAe,UAAQ;AACrB,QAAI,WAAW,KAAK,WAAW,KAAK,MAAM,SAAS;AACjD,YAAM,IAAIC,0BAAyB;QACjC,QAAQ,KAAK,MAAM;QACnB;OACD;EACL;EACA,kBAAkB,QAAM;AACtB,QAAI,SAAS;AAAG,YAAM,IAAI,oBAAoB,EAAE,OAAM,CAAE;AACxD,UAAM,WAAW,KAAK,WAAW;AACjC,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;EAClB;EACA,aAAa,UAAQ;AACnB,WAAO,KAAK,kBAAkB,IAAI,YAAY,KAAK,QAAQ,KAAK;EAClE;EACA,kBAAkB,QAAM;AACtB,QAAI,SAAS;AAAG,YAAM,IAAI,oBAAoB,EAAE,OAAM,CAAE;AACxD,UAAM,WAAW,KAAK,WAAW;AACjC,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;EAClB;EACA,YAAY,WAAS;AACnB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,QAAQ;AAC5B,WAAO,KAAK,MAAM,QAAQ;EAC5B;EACA,aAAa,QAAQ,WAAS;AAC5B,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,SAAS,CAAC;AACzC,WAAO,KAAK,MAAM,SAAS,UAAU,WAAW,MAAM;EACxD;EACA,aAAa,WAAS;AACpB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,QAAQ;AAC5B,WAAO,KAAK,MAAM,QAAQ;EAC5B;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,WAAO,KAAK,SAAS,UAAU,QAAQ;EACzC;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,YACG,KAAK,SAAS,UAAU,QAAQ,KAAK,KACtC,KAAK,SAAS,SAAS,WAAW,CAAC;EAEvC;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,WAAO,KAAK,SAAS,UAAU,QAAQ;EACzC;EACA,SAAS,MAAmB;AAC1B,SAAK,eAAe,KAAK,QAAQ;AACjC,SAAK,MAAM,KAAK,QAAQ,IAAI;AAC5B,SAAK;EACP;EACA,UAAU,OAAY;AACpB,SAAK,eAAe,KAAK,WAAW,MAAM,SAAS,CAAC;AACpD,SAAK,MAAM,IAAI,OAAO,KAAK,QAAQ;AACnC,SAAK,YAAY,MAAM;EACzB;EACA,UAAU,OAAa;AACrB,SAAK,eAAe,KAAK,QAAQ;AACjC,SAAK,MAAM,KAAK,QAAQ,IAAI;AAC5B,SAAK;EACP;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,KAAK;AAC5C,SAAK,YAAY;EACnB;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,SAAS,CAAC;AACjD,SAAK,SAAS,SAAS,KAAK,WAAW,GAAG,QAAQ,CAAC,UAAU;AAC7D,SAAK,YAAY;EACnB;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,KAAK;AAC5C,SAAK,YAAY;EACnB;EACA,WAAQ;AACN,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,YAAW;AAC9B,SAAK;AACL,WAAO;EACT;EACA,UAAU,QAAQC,OAAI;AACpB,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,aAAa,MAAM;AACtC,SAAK,YAAYA,SAAQ;AACzB,WAAO;EACT;EACA,YAAS;AACP,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,aAAY;AAC/B,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,IAAI,YAAS;AACX,WAAO,KAAK,MAAM,SAAS,KAAK;EAClC;EACA,YAAY,UAAQ;AAClB,UAAM,cAAc,KAAK;AACzB,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;AAChB,WAAO,MAAO,KAAK,WAAW;EAChC;EACA,SAAM;AACJ,QAAI,KAAK,uBAAuB,OAAO;AAAmB;AAC1D,UAAM,QAAQ,KAAK,aAAY;AAC/B,SAAK,kBAAkB,IAAI,KAAK,UAAU,QAAQ,CAAC;AACnD,QAAI,QAAQ;AAAG,WAAK;EACtB;;AA4BI,IAAO,sBAAP,cAA0CC,WAAS;EAGvD,YAAY,EAAE,OAAM,GAAsB;AACxC,UAAM,YAAY,MAAM,wBAAwB;AAHhC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;AAII,IAAOC,4BAAP,cAA+CD,WAAS;EAG5D,YAAY,EAAE,QAAQ,SAAQ,GAAwC;AACpE,UACE,cAAc,QAAQ,yCAAyC,MAAM,MAAM;AAJ7D,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAII,IAAO,kCAAP,cAAsDA,WAAS;EAGnE,YAAY,EAAE,OAAO,MAAK,GAAoC;AAC5D,UACE,6BAA6B,KAAK,wCAAwC,KAAK,MAAM;AAJvE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;;;ACRI,SAAUE,cAEd,OAAuB,QAA2C;AAClE,MAAI,MAAM,WAAW,OAAO;AAC1B,UAAM,IAAI,oBAAoB;MAC5B,gBAAgB,MAAM;MACtB,aAAa,OAAO;KACrB;AAEH,QAAM,OAAkB,CAAA;AACxB,WAASC,KAAI,GAAGA,KAAK,MAAoB,QAAQA,MAAK;AACpD,UAAM,OAAO,MAAMA,EAAC;AACpB,UAAM,QAAQ,OAAOA,EAAC;AACtB,SAAK,KAAKD,cAAa,OAAO,MAAM,KAAK,CAAC;EAC5C;AACA,SAAWE,QAAO,GAAG,IAAI;AAC3B;CAEA,SAAiBF,eAAY;AAe3B,WAAgBG,QACd,MACA,OACA,UAAU,OAAK;AAEf,QAAI,SAAS,WAAW;AACtB,YAAM,UAAU;AAChB,MAAQ,OAAO,OAAO;AACtB,aAAW,QACT,QAAQ,YAAW,GACnB,UAAU,KAAK,CAAC;IAEpB;AACA,QAAI,SAAS;AAAU,aAAWC,YAAW,KAAe;AAC5D,QAAI,SAAS;AAAS,aAAO;AAC7B,QAAI,SAAS;AACX,aAAW,QAAY,YAAY,KAAgB,GAAG,UAAU,KAAK,CAAC;AAExE,UAAM,WAAY,KAAgB,MAAeC,aAAY;AAC7D,QAAI,UAAU;AACZ,YAAM,CAAC,OAAO,UAAU,OAAO,KAAK,IAAI;AACxC,YAAMC,QAAO,OAAO,SAAS,IAAI,IAAI;AACrC,aAAW,WAAW,OAAiB;QACrC,MAAM,UAAU,KAAKA;QACrB,QAAQ,aAAa;OACtB;IACH;AAEA,UAAM,aAAc,KAAgB,MAAeC,WAAU;AAC7D,QAAI,YAAY;AACd,YAAM,CAAC,OAAOD,KAAI,IAAI;AACtB,UAAI,OAAO,SAASA,KAAK,OAAQ,MAAkB,SAAS,KAAK;AAC/D,cAAM,IAAIE,wBAAuB;UAC/B,cAAc,OAAO,SAASF,KAAK;UACnC;SACD;AACH,aAAW,SAAS,OAAkB,UAAU,KAAK,CAAC;IACxD;AAEA,UAAM,aAAc,KAAgB,MAAeG,WAAU;AAC7D,QAAI,cAAc,MAAM,QAAQ,KAAK,GAAG;AACtC,YAAM,CAAC,OAAO,SAAS,IAAI;AAC3B,YAAM,OAAkB,CAAA;AACxB,eAASR,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,aAAK,KAAKE,QAAO,WAAW,MAAMF,EAAC,GAAG,IAAI,CAAC;MAC7C;AACA,UAAI,KAAK,WAAW;AAAG,eAAO;AAC9B,aAAWC,QAAO,GAAG,IAAI;IAC3B;AAEA,UAAM,IAAI,iBAAiB,IAAc;EAC3C;AAnDgB,EAAAF,cAAA,SAAMG;AAoDxB,GAnEiBH,kBAAAA,gBAAY,CAAA,EAAA;AA0WvB,IAAOU,0BAAP,cAA6CC,WAAS;EAE1D,YAAY,EACV,cACA,MAAK,GACoC;AACzC,UACE,kBAAkB,KAAK,WAAeC,MACpC,KAAK,CACN,wCAAwC,YAAY,IAAI;AAR3C,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAUzB;;AA0BI,IAAO,sBAAP,cAA0CD,WAAS;EAEvD,YAAY,EACV,gBACA,YAAW,GACqC;AAChD,UACE;MACE;MACA,iCAAiC,cAAc;MAC/C,0BAA0B,WAAW;MACrC,KAAK,IAAI,CAAC;AAVE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYzB;;AAsCI,IAAO,mBAAP,cAAuCE,WAAS;EAEpD,YAAY,MAAY;AACtB,UAAM,UAAU,IAAI,6BAA6B;AAFjC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGzB;;A;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChsBI,SAAU,0BAA0B,WAA2B,aAAmB;AACtF,SAAO,oBAAoB,eAAe,SAAS,MAAM;AAC3D;;;ACCM,SAAU,sBAAsB,WAA0B;AAC9D,QAAM,aAAa,MAAM,KAAK,gBAAgB,MAAM,OAAO,KAAI,CAAE;AACjE,MAAI,SAA2C,CAAA;AAE/C,MAAI,WAAW;AACb,WAAO,KAAK,CAAC,WAAW,gBAAgB,MAAM,OAAO,IAAI,SAAS,CAAiB,CAAC;AAEpF,QAAI,0BAA0B,WAAW,cAAoB,aAAa,cAAc,GAAG;AACzF,iBAAW,QAAQ,CAAAC,QAAK;AACtB,YACEA,QAAO,aACP,0BAA0BA,KAAI,cAAoB,aAAa,cAAc,GAC7E;AACA,iBAAO,KAAK,CAACA,KAAI,gBAAgB,MAAM,OAAO,IAAIA,GAAE,CAAiB,CAAC;QACxE;MACF,CAAC;IACH,WAAW,0BAA0B,WAAW,cAAoB,aAAa,IAAI,GAAG;AACtF,iBAAW,QAAQ,CAAAA,QAAK;AACtB,YACEA,QAAO,aACP,0BAA0BA,KAAI,cAAoB,aAAa,IAAI,GACnE;AACA,iBAAO,KAAK,CAACA,KAAI,gBAAgB,MAAM,OAAO,IAAIA,GAAE,CAAiB,CAAC;QACxE;MACF,CAAC;IACH;EACF,OAAO;AACL,aAAS,MAAM,KAAK,gBAAgB,MAAM,OAAO,QAAO,CAAE;EAC5D;AAEA,SAAO;AACT;;;AC5CO,IAAM,kBAAkB;EAC7B,kCAAkC;EAClC,oBAAoB;EACpB,wBAAwB;EACxB,2BAA2B;IACzB;IACA;IACA;IACA;IACA;IACA;;;;;ACNE,SAAUC,aAGd,OAAY;AACZ,SAAO;IACL,YAAY;IACZ,MAAM;IACN,aAAa;IACb,GAAG;;AAEP;;;ACZO,IAAM,SAASC,aAAY;EAChC,IAAI;EACJ,MAAM;EACN,SAAS;EACT,gBAAgB,EAAE,MAAM,UAAU,QAAQ,OAAO,UAAU,EAAC;EAC5D,SAAS;IACP,SAAS,EAAE,MAAM,CAAC,kCAAkC,EAAC;;EAEvD,gBAAgB,EAAE,SAAS,EAAE,MAAM,WAAW,KAAK,qBAAoB,EAAE;EACzE,SAAS;EACT,gBAAgB;EAChB,eAAe;EACf,yBAAyB;CAC1B;;;ACbM,IAAM,eAAeC,aAAY;EACtC,IAAI;EACJ,MAAM;EACN,SAAS;EACT,gBAAgB,EAAE,MAAM,UAAU,QAAQ,OAAO,UAAU,EAAC;EAC5D,SAAS;IACP,SAAS,EAAE,MAAM,CAAC,kCAAkC,EAAC;;EAEvD,gBAAgB,EAAE,SAAS,EAAE,MAAM,WAAW,KAAK,qBAAoB,EAAE;EACzE,SAAS;EACT,gBAAgB;EAChB,eAAe;EACf,yBAAyB;CAC1B;;;ACbM,IAAM,gBAAgBC,aAAY;EACvC,IAAI;EACJ,MAAM;EACN,SAAS;EACT,gBAAgB,EAAE,MAAM,UAAU,QAAQ,OAAO,UAAU,EAAC;EAC5D,SAAS;IACP,SAAS,EAAE,MAAM,CAAC,kCAAkC,EAAC;;EAEvD,gBAAgB,EAAE,SAAS,EAAE,MAAM,WAAW,KAAK,qBAAoB,EAAE;EACzE,SAAS;EACT,gBAAgB;EAChB,eAAe;CAChB;;;ACZM,IAAM,UAAUC,aAAY;EACjC,IAAI;EACJ,eAAe;EACf,gBAAgB;EAChB,MAAM;EACN,gBAAgB;IACd,MAAM;IACN,QAAQ;IACR,UAAU;;EAEZ,SAAS;IACP,SAAS,EAAE,MAAM,CAAC,kCAAkC,EAAC;;CAExD;AAEM,IAAM,iBAAiBA,aAAY;EACxC,IAAI;EACJ,eAAe;EACf,gBAAgB;EAChB,MAAM;EACN,gBAAgB;IACd,MAAM;IACN,QAAQ;IACR,UAAU;;EAEZ,SAAS;IACP,SAAS,EAAE,MAAM,CAAC,kCAAkC,EAAC;;EAEvD,SAAS;CACV;;;ACvBM,IAAM,kBAAkB;EAC7B,QAAQ;IACN;IACA;IACA;IACA;IACA;IACA;;EAEF,QAAQ;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;;IAEA;IACA;;IAEA;;EAEF,QAAQ,CAAC,gBAAgB,eAAe,YAAY,qBAAqB;;AAGpE,IAAM,gBAAgB;EAC3B,2BAA2B,gBAA8B;AACvD,WAAO,gBAAgB,cAA8C,KAAK,CAAA;EAC5E;EACA,uBAAuB,gBAA8B;AACnD,WAAO;MACL,SAAS,KAAK,2BAA2B,cAAc;MACvD,QAAQ,CAAC,mBAAmB,cAAc;MAC1C,QAAQ,CAAA;MACR,QAAQ,CAAA;;EAEZ;EAEA,wBACE,gBACA,WAAqE;AAErE,QAAI,CAAC,WAAW;AACd,aAAO,EAAE,GAAG,eAAc;IAC5B;AAEA,UAAM,SAAS,EAAE,GAAG,eAAc;AAElC,UAAM,uBAAuB,oBAAI,IAAG;AAEpC,QAAI,UAAU,SAAS;AACrB,aAAO,KAAK,UAAU,OAAO,EAAE,QAAQ,CAAAC,QAAM,qBAAqB,IAAIA,GAAE,CAAC;IAC3E;AAEA,QAAI,UAAU,QAAQ;AACpB,aAAO,KAAK,UAAU,MAAM,EAAE,QAAQ,CAAAA,QAAM,qBAAqB,IAAIA,GAAE,CAAC;IAC1E;AAEA,QAAI,UAAU,QAAQ;AACpB,aAAO,KAAK,UAAU,MAAM,EAAE,QAAQ,CAAAA,QAAM,qBAAqB,IAAIA,GAAE,CAAC;IAC1E;AAEA,QAAI,UAAU,QAAQ;AACpB,aAAO,KAAK,UAAU,MAAM,EAAE,QAAQ,aAAU;AAC9C,cAAM,CAACA,GAAE,IAAI,QAAQ,MAAM,GAAG;AAC9B,YAAIA,KAAI;AACN,+BAAqB,IAAIA,GAAE;QAC7B;MACF,CAAC;IACH;AAEA,yBAAqB,QAAQ,CAAAA,QAAK;AAChC,UAAI,CAAC,OAAOA,GAAE,GAAG;AACf,eAAOA,GAAE,IAAI,KAAK,uBAAuBA,GAAoB;MAC/D;IACF,CAAC;AAED,QAAI,UAAU,SAAS;AACrB,aAAO,QAAQ,UAAU,OAAO,EAAE,QAAQ,CAAC,CAACA,KAAI,OAAO,MAAK;AAC1D,YAAI,OAAOA,GAAE,GAAG;AACd,iBAAOA,GAAE,EAAE,UAAU;QACvB;MACF,CAAC;IACH;AAEA,QAAI,UAAU,QAAQ;AACpB,aAAO,QAAQ,UAAU,MAAM,EAAE,QAAQ,CAAC,CAACA,KAAI,MAAM,MAAK;AACxD,YAAI,OAAOA,GAAE,GAAG;AACd,iBAAOA,GAAE,EAAE,SAAS;QACtB;MACF,CAAC;IACH;AAEA,QAAI,UAAU,QAAQ;AACpB,aAAO,QAAQ,UAAU,MAAM,EAAE,QAAQ,CAAC,CAACA,KAAI,MAAM,MAAK;AACxD,YAAI,OAAOA,GAAE,GAAG;AACd,iBAAOA,GAAE,EAAE,SAAS;QACtB;MACF,CAAC;IACH;AAEA,QAAI,UAAU,QAAQ;AACpB,YAAM,sBAAsB,oBAAI,IAAG;AAEnC,aAAO,QAAQ,UAAU,MAAM,EAAE,QAAQ,CAAC,CAAC,SAAS,MAAM,MAAK;AAC7D,cAAM,CAACA,KAAI,EAAE,IAAI,QAAQ,MAAM,GAAG;AAClC,YAAI,CAACA,OAAM,CAAC,MAAM,CAAC,OAAOA,GAAE,GAAG;AAC7B;QACF;AAEA,YAAI,CAAC,OAAOA,GAAE,EAAE,QAAQ;AACtB,iBAAOA,GAAE,EAAE,SAAS,CAAA;QACtB;AAEA,YAAI,CAAC,oBAAoB,IAAIA,GAAE,GAAG;AAChC,iBAAOA,GAAE,EAAE,SAAS,CAAA;AACpB,8BAAoB,IAAIA,GAAE;QAC5B;AAEA,eAAOA,GAAE,EAAE,OAAO,EAAE,IAAI;MAC1B,CAAC;IACH;AAEA,WAAO;EACT;EAEA,iBACE,cACA,gBAA0E;AAE1E,UAAM,oBAAoB,aAAa,OAAwB,CAAC,KAAK,UAAS;AAC5E,YAAM,EAAE,IAAI,gBAAgB,QAAO,IAAK;AACxC,YAAM,SAAS,QAAQ,QAAQ,KAAK,CAAC;AAErC,UAAI,CAAC,IAAI,cAAc,GAAG;AACxB,YAAI,cAAc,IAAI,KAAK,uBAAuB,cAAc;MAClE;AAEA,YAAM,gBAAgB,GAAG,cAAc,IAAI,EAAE;AAG7C,YAAM,YAAY,IAAI,cAAc;AAEpC,gBAAU,OAAO,KAAK,aAAa;AAGnC,cAAQ,eAAe;QACrB,KAAK,OAAO;AACV,oBAAU,OAAO,KAAK,OAAO,uBAAuB;AACpD;QACF,KAAK,aAAa;AAChB,oBAAU,OAAO,KAAK,aAAa,uBAAuB;AAC1D;QACF;MACF;AAEA,WAAI,uCAAW,WAAU,QAAQ;AAC/B,kBAAU,OAAO,EAAE,IAAI;MACzB;AAEA,aAAO;IACT,GAAG,CAAA,CAAE;AAEL,WAAO,KAAK,wBAAwB,mBAAmB,cAAc;EACvE;EAEA,kBAAkB,OAAO,SAAgB;AA1L3C;AA2LI,UAAM,gBAAgB,MAAM,cAAc,YAAY,IAAI;AAC1D,UAAM,uBAAuB,OAAO,OAAO,+CAAe,SAAS,KAAK,CAAA;AAExE,aAAO,0BAAqB,CAAC,MAAtB,mBAAyB,YAAW;EAC7C;EAEA,wBAAwB,aAAsC,CAAA,GAAE;AAC9D,WAAO,OAAO,OAAO,UAAU,EAAE,QAAuB,eAAY;AAClE,YAAM,SAAU,UAAU,UAAU,CAAA;AACpC,YAAM,iBAAiB,UAAU,SAAS,IAAI,aAAU;AACtD,cAAM,CAAC,gBAAgB,OAAO,IAAI,QAAQ,MAAM,GAAG;AAEnD,eAAO,GAAG,cAAc,IAAI,OAAO;MACrC,CAAC;AAED,aAAO,MAAM,KAAK,oBAAI,IAAI,CAAC,GAAG,QAAQ,GAAG,cAAc,CAAC,CAAC;IAC3D,CAAC;EACH;EAEA,mBAAmB,MAAa;AAC9B,WACE,OAAO,SAAS,YAChB,SAAS,QACT,QAAQ,QACR,WAAW,QACX,YAAY,QACZ,OAAO,KAAK,WAAW,YACvB,KAAK,WAAW,QAChB,aAAa,KAAK,UAClB,WAAW,KAAK,UAChB,OAAO,KAAK,OAAO,UAAU,YAC7B,KAAK,OAAO,UAAU;EAE1B;EAEA,gBACE,eACA,iBACA,uBAA+B;AAE/B,eAAW,WAAW,CAAC,GAAG,iBAAiB,GAAG,qBAAqB,GAAG;AACpE,UAAI,QAAQ,SAAS,GAAG,GAAG;AAEzB,cAAM,iBAAiB,QAAQ,QAAQ,wBAAwB,MAAM;AACrE,cAAM,cAAc,IAAI,eAAe,QAAQ,UAAU,IAAI,CAAC;AAC9D,cAAM,QAAQ,IAAI,OAAO,aAAa,GAAG;AAEzC,YAAI,MAAM,KAAK,aAAa,GAAG;AAC7B,iBAAO;QACT;MACF,OAAO;AAKL,YAAI;AACF,cAAI,IAAI,IAAI,OAAO,EAAE,WAAW,eAAe;AAC7C,mBAAO;UACT;QACF,SAAS,GAAG;AACV,cAAI,YAAY,eAAe;AAC7B,mBAAO;UACT;QACF;MACF;IACF;AAGA,WAAO;EACT;;;;AC1PI,IAAO,yBAAP,MAA6B;EAcjC,YAAY,EAAE,UAAU,UAAS,GAA6C;AAX9D,SAAA,KAAK,cAAc,aAAa;AAChC,SAAA,OAAO,YAAY,kBACjC,cAAc,aAAa,cAAc;AAE3B,SAAA,OAAO;AACP,SAAA,UAAU,YAAY,kBAAkB,cAAc,aAAa,cAAc;AAIzF,SAAA,kBAAkB,gBAAgB,gBAAgB,KAAK,eAAe;AAG5E,SAAK,eAAe,KAAK,gBAAe;AACxC,SAAK,WAAW;AAChB,SAAK,QAAQ;EACf;EAEA,IAAI,SAAM;AACR,WAAO,KAAK,gBAAe;EAC7B;EAEA,MAAM,uBAAoB;AACxB,UAAM,kBAAkB,MAAM,KAAK,aAAY;AAE/C,QAAI,CAAC,iBAAiB;AACpB,YAAM,eAAe,KAAK,gBAAe;AACzC,YAAM,kCACJ,kBAAkB,MAAM;AAC1B,YAAM,aAAa,cAAc,iBAC/B,cACA,+BAA+B;AAEjC,YAAM,KAAK,SAAS,QAAQ,EAAE,oBAAoB,WAAU,CAAE;IAChE;AAEA,WAAO;MACL,UAAU,MAAM,KAAK,SAAS,OAAO,KAAK,OAAO,YAAW;MAC5D,SAAS,KAAK,SAAS;;EAE3B;EAEA,MAAM,aAAU;AACd,UAAM,KAAK,SAAS,WAAU;EAChC;EAEA,MAAM,eAAY;AAChB,UAAM,SAAS,KAAK,OAAO,IAAI,aAAW,QAAQ,aAAa;AAE/D,WAAO,SAAS,8BAA8B;MAC5C,mBAAmB,KAAK;MACxB;MACA,SAAS;KACV;EACH;;AAgBF,IAAM,mBAAmB;EACvB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;;EAEA;;;;AC7DI,IAAgB,mBAAhB,MAAgC;;;;;EAiBpC,YAAY,QAAgC;AAVlC,SAAA,sBAAmC,CAAA;AAIrC,SAAA,iBAAiB,oBAAI,IAAG;AAO9B,SAAK,kBAAkB,CAAC,cACtB,gBAAgB,gBAAgB,SAAS;AAE3C,QAAI,QAAQ;AACV,WAAK,UAAU,MAAM;IACvB;EACF;;;;;EAMA,UAAU,QAA+B;AACvC,SAAK,YAAY,OAAO;AACxB,SAAK,YAAY,OAAO;AACxB,SAAK,cAAc,OAAO;EAC5B;;;;;EAMA,IAAW,aAAU;AACnB,WAAO,KAAK;EACd;;;;;EAMA,IAAW,WAAQ;AACjB,WAAO,KAAK,gBAAgB,KAAK,SAAS;EAC5C;;;;;EAYO,gBAAgB,cAA8B;AACnD,SAAK,aAAa;MAChB,IAAI,cAAoB,aAAa;MACrC,MAAM;MACN,MAAM,cAAoB,gBAAgB;MAC1C,UAAU;MACV,SAAS,YAAY,kBAAkB,cAAoB,aAAa,IAAI;MAC5E,OAAO,KAAK;MACZ,QAAQ,CAAA;KACe;EAC3B;;;;;EAMU,gBAAgB,YAAuB;AAC/C,UAAM,kBAAkB,oBAAI,IAAG;AAC/B,SAAK,sBAAsB,CAAC,GAAG,YAAY,GAAG,KAAK,mBAAmB,EAAE,OAAO,eAAY;AACzF,UAAI,gBAAgB,IAAI,UAAU,EAAE,GAAG;AACrC,eAAO;MACT;AAEA,sBAAgB,IAAI,UAAU,EAAE;AAEhC,aAAO;IACT,CAAC;AAED,SAAK,KAAK,cAAc,KAAK,mBAAmB;EAClD;EAEU,UAAU,QAA0C,gBAA+B;AAC3F,sBAAkB,UAAU,QAAQ,cAAc;EACpD;;;;;;;EAQO,GAAwB,WAAc,UAA0B;AAtJzE;AAuJI,QAAI,CAAC,KAAK,eAAe,IAAI,SAAS,GAAG;AACvC,WAAK,eAAe,IAAI,WAAW,oBAAI,IAAG,CAAE;IAC9C;AAEA,eAAK,eAAe,IAAI,SAAS,MAAjC,mBAAoC,IAAI;EAC1C;;;;;;;EAQO,IAAyB,WAAc,UAA0B;AACtE,UAAM,YAAY,KAAK,eAAe,IAAI,SAAS;AACnD,QAAI,WAAW;AACb,gBAAU,OAAO,QAAoC;IACvD;EACF;;;;EAKO,0BAAuB;AAC5B,SAAK,eAAe,QAAQ,eAAY;AACtC,gBAAU,MAAK;IACjB,CAAC;EACH;;;;;;;EAQU,KAA0B,WAAc,MAAmB;AACnE,UAAM,YAAY,KAAK,eAAe,IAAI,SAAS;AACnD,QAAI,WAAW;AACb,gBAAU,QAAQ,cAAY,SAAS,IAAoB,CAAC;IAC9D;EACF;;;;;EAMO,MAAM,qBACX,UAA0B;AAE1B,UAAM,YAAY,KAAK,0BAAyB;AAEhD,UAAM,SAAS,MAAM,UAAU,qBAAoB;AAEnD,WAAO,EAAE,UAAU,OAAO,SAAQ;EACpC;;;;;EAwBO,MAAM,cAAc,QAA4C;AArOzE;AAsOI,UAAM,EAAE,aAAa,aAAY,IAAK;AAEtC,QAAI,CAAC,OAAO,UAAU;AACpB;IACF;AAEA,UAAM,WAAW,cAAc,OAAO,WAAW,OAAO,SAAS,WAAW,OAAO;AAEnF,QAAI,iBAAiB,kBAAkB;AACrC;AAAE,eAA+B,gBAAgB,YAAY,aAAa;AAE1E;IACF;AAEA,QAAI,YAAY,iBAAiB,QAAQ;AACvC,YAAM,eAAe;AACrB,YAAM,wBACJ,uBAAkB,MAAM,0BAAxB,mBAAgD,YAAY;AAC9D,YAAM,aAAa,cAAc,YAAY,aAAa;AAC1D,YAAM,OAAO,MAAM,aAAa,QAAQ;QACtC,SAAS,YAAY;QACrB;OACD;AAED,WAAK,KAAK,iBAAiB,IAAI;IACjC;EACF;EAsHU,4BAAyB;AACjC,UAAM,YAAY,KAAK,WAAW,KAAK,CAAAC,OAAKA,cAAa,sBAAsB;AAI/E,QAAI,CAAC,WAAW;AACd,YAAM,IAAI,MAAM,kCAAkC;IACpD;AAEA,WAAO;EACT;;;;AChXI,IAAO,mBAAP,cAAgC,iBAAgB;EACpC,qBAAqB,mBAAoC;AACvE,SAAK,aACH,IAAI,uBAAuB;MACzB,UAAU;MACV,cAAc,KAAK,gBAAe;MAClC,WAAW,KAAK;KACjB,CAAC;EAEN;EAEO,MAAM,QACX,QAAsC;AAEtC,WAAO,QAAQ,QAAQ;MACrB,IAAI;MACJ,MAAM;MACN,SAAS,OAAO,OAAO,OAAO;MAC9B,UAAU,KAAK;MACf,SAAS;KACV;EACH;EAEO,MAAM,aAAU;AACrB,QAAI;AACF,YAAM,YAAY,KAAK,0BAAyB;AAChD,YAAM,UAAU,WAAU;IAC5B,SAAS,OAAO;AACd,cAAQ,KAAK,uCAAuC,KAAK;IAC3D;EACF;EAEO,MAAM,YAAY,EACvB,UAAS,GAGV;AAnDH,gBAAAC,KAAA;AAoDI,UAAM,WAAW,KAAK;AACtB,UAAM,cAAa,MAAAA,OAAA,gDAAU,YAAV,mBAAmB,eAAnB,mBAAgC,eAAhC,gBAAAA,IAA4C,aAA5C,mBACf,IAAI,aAAU;AACd,YAAM,CAAC,EAAC,EAAG,OAAO,IAAI,QAAQ,MAAM,GAAG;AAEvC,aAAO;IACT,GACC,OAAO,CAAC,SAAS,OAAO,SAAS,KAAK,QAAQ,OAAO,MAAM,WAAU,CAAA;AAExE,WAAO,QAAQ,QAAQ;MACrB,UAAU,UAAU,IAAI,aACtB,eAAe,cAAc,WAAW,SAAS,cAAc,WAAW,YAAY,KAAK,CAAC;KAE/F;EACH;EAES,MAAM,iBAAc;AAC3B,WAAO,QAAQ,QAAO;EACxB;EAEO,MAAM,WACX,QAAyC;AAzE7C,gBAAAA,KAAA,IAAAC;AA2EI,UAAM,qBACJ,OAAO,eACPC,eAAkB,yBAAyB,UAAS,YAAO,gBAAP,mBAAoB,cAAc;AACxF,QAAI,CAAC,wBAAsB,YAAO,gBAAP,mBAAoB,UAAS;AACtD,aAAO;QACL,SAAS;QACT,UAAQF,MAAA,OAAO,gBAAP,gBAAAA,IAAoB,eAAe,WAAU;;IAEzD;AAEA,QACE,kBAAkB,MAAM,kBACxB,OAAO,cAAY,qBAAgB,MAAM,sBAAtB,mBAAyC,KAC5D;AACA,aAAO;QACL,SAAS,kBAAkB,MAAM,WAAW;QAC5C,QAAQ,kBAAkB,MAAM,iBAAiB;;IAErD;AAEA,UAAM,WAAW,MAAM,kBAAkB,kBAAiB;AAC1D,UAAM,UAAU,SAAS,KACvB,CAAAG,OAAE;AAjGR,UAAAC,KAAAC;AAkGQ,aAAAF,GAAE,YAAY,IAAGC,MAAA,OAAO,gBAAP,gBAAAA,IAAoB,cAAc,IAAI,OAAO,OAAO,MACrED,GAAE,aAAWE,MAAA,OAAO,gBAAP,gBAAAA,IAAoB,eAAe;KAAM;AAG1D,WAAO;MACL,UAAS,mCAAS,SAAS,YAAW;MACtC,SAAQ,mCAAS,aAAUJ,MAAA,OAAO,gBAAP,gBAAAA,IAAoB,eAAe,WAAU;;EAE5E;EAEgB,MAAM,YACpB,QAA0C;AA7G9C,gBAAAD;AA+GI,UAAM,EAAE,UAAU,SAAS,QAAO,IAAK;AACvC,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,MAAM,sDAAsD;IACxE;AAEA,QAAI,YAAY;AAEhB,UAAI,qBAAgB,MAAM,sBAAtB,mBAAyC,oBAAmB,cAAc,MAAM,QAAQ;AAC1F,YAAM,WAAW,MAAM,SAAS,QAC9B;QACE,QAAQ;QACR,QAAQ;UACN,SAAS,YAAK,OAAO,IAAI,YAAW,EAAG,OAAO,OAAO,CAAC;UACtD,QAAQ;;UAGZ,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAGxD,kBAAa,SAAmC;IAClD,OAAO;AACL,kBAAY,MAAM,SAAS,QACzB;QACE,QAAQ;QACR,QAAQ,CAAC,SAAS,OAAO;UAE3BA,MAAA,gBAAgB,MAAM,sBAAtB,gBAAAA,IAAyC,aAAa;IAE1D;AAEA,WAAO,EAAE,UAAS;EACpB;;;;;;;EAQgB,MAAM,cAAW;AAC/B,WAAO,QAAQ,QAAQ;MACrB,KAAK,OAAO,CAAC;KACd;EACH;EAEO,MAAM,kBAAe;AAC1B,WAAO,QAAQ,QAAQ;MACrB,MAAM;KACP;EACH;EAEgB,gBACd,SAA+C;AAE/C,WAAO,QAAQ,QAAQ,CAAA,CAAE;EAC3B;EACO,MAAM,gBAAa;AACxB,WAAO,QAAQ,QAAQ;MACrB,MAAM;KACP;EACH;EAEO,aAAU;AACf,WAAO;EACT;EAEO,cAAW;AAChB,WAAO;EACT;EAEO,MAAM,kBAAe;AAC1B,WAAO,QAAQ,QAAQ,CAAA,CAAE;EAC3B;EAEO,MAAM,mBAAgB;AAC3B,WAAO,QAAQ,QAAQ,CAAA,CAAE;EAC3B;EAEO,MAAM,oBAAiB;AAC5B,WAAO,QAAQ,QAAQ,IAAI;EAC7B;EAEO,MAAM,iBAAc;AACzB,WAAO,QAAQ,QAAQ;MACrB,IAAI;MACJ,MAAM;MACN,SAAS;MACT,UAAU,KAAK;MACf,SAAS;KACV;EACH;;EAGgB,MAAM,cAAc,QAA4C;AA5MlF,gBAAAA,KAAA,IAAAC,KAAA;AA6MI,UAAM,EAAE,YAAW,IAAK;AACxB,UAAM,YAAY,KAAK,0BAAyB;AAEhD,QAAI,YAAY,mBAAmB,cAAc,MAAM,KAAK;AAC1D,UAAI;AACF,gBAAM,eAAU,aAAV,mBAAoB,QAAQ;UAChC,QAAQ;UACR,QAAQ,CAAC,EAAE,SAAS,MAAM,YAAY,EAAE,EAAC,CAAE;;MAG/C,SAAS,aAAkB;AACzB,YACE,YAAY,SAAS,gBAAgB,oCACrC,YAAY,SAAS,gBAAgB,0BACrC,YAAY,SAAS,gBAAgB,wBACrCD,OAAA,gDAAa,SAAb,mBAAmB,kBAAnB,gBAAAA,IAAkC,UAChC,gBAAgB,kCAClB;AACA,cAAI;AACF,oBAAM,eAAU,aAAV,mBAAoB,QAAQ;cAChC,QAAQ;cACR,QAAQ;gBACN;kBACE,SAAS,MAAM,YAAY,EAAE;kBAC7B,SAAS,EAAC,gDAAa,QAAQ,oBAArB,mBAAsC,IAAI;kBACpD,WAAW,YAAY;kBACvB,gBAAgB,YAAY;kBAC5B,mBAAmB,EAACC,MAAA,YAAY,mBAAZ,gBAAAA,IAA4B,QAAQ,GAAG;;;;UAInE,SAAS,OAAO;AACd,kBAAM,IAAI,MAAM,wBAAwB;UAC1C;QACF;MACF;IACF;AACA,cAAU,SAAS,gBAAgB,YAAY,aAAa;EAC9D;EAEO,2BAAwB;AAC7B,UAAM,YAAY,KAAK,WAAW,KAAK,CAAAK,OAAKA,GAAE,SAAS,gBAAgB;AAEvE,UAAM,WAAW,uCAAW;AAE5B,WAAO;EACT;;;;AC7OF,IAAM,eAA6B;EACjC;EACA;EACA;EACA;EACA;EACA;;AAGF,IAAM,gBAAgB;EACpB,OAAO;IACL,gBAAgB;IAChB,kBAAkB;IAClB,YAAY;IACZ,UAAU;IACV,oBAAoB;IACpB,YAAY,CAAC,cAA0C;AACrD,UAAI,EAAC,uCAAW,SAAQ;AACtB,eAAO;MACT;AACA,YAAM,SAAS,UAAU;AAEzB,aAAO,QAAQ,UAAU,SAAS,KAAK,OAAO,SAAS,OAAO;IAChE;IACA,iBAAiB,CAAC,eAAgC;AAChD,UAAI,eAAe,QAAW;AAC5B,eAAOC,eAAc,wBAAwB;MAC/C;AAEA,aAAO,QAAQ,UAAU;IAC3B;;EAEF,SAAS;IACP,gBAAgB;IAChB,kBAAkB;IAClB,YAAY;IACZ,UAAU;IACV,oBAAoB;IACpB,YAAY,CAAC,cAA2D;AACtE,UAAI,EAAC,uCAAW,SAAQ;AACtB,eAAO;MACT;AACA,YAAM,SAAS,UAAU;AAEzB,aAAO,QAAQ,UAAU,SAAS,KAAK,OAAO,SAAS,IACnD,OAAO,OAAO,CAAC,MAA2B,MAAM,OAAO,IACvD;IACN;IACA,iBAAiB,CAAC,eAAiD;AACjE,UAAI,eAAe,QAAW;AAC5B,eAAOA,eAAc,wBAAwB;MAC/C;AACA,UAAI,OAAO,eAAe,WAAW;AACnC,eAAO,aAAaA,eAAc,wBAAwB,UAAU;MACtE;AAEA,aAAO;IACT;;EAEF,OAAO;IACL,gBAAgB;IAChB,kBAAkB;IAClB,YAAY;IACZ,UAAU;IACV,oBAAoB;IACpB,YAAY,CAAC,cAAyD;AACpE,UAAI,EAAC,uCAAW,SAAQ;AACtB,eAAO;MACT;AACA,YAAM,SAAS,UAAU;AAEzB,aAAO,QAAQ,UAAU,SAAS,KAAK,OAAO,SAAS,IAAI,SAAS;IACtE;IACA,iBAAiB,CAAC,eAA+C;AAC/D,UAAI,eAAe,QAAW;AAC5B,eAAOA,eAAc,wBAAwB;MAC/C;AACA,UAAI,OAAO,eAAe,WAAW;AACnC,eAAO,aAAaA,eAAc,wBAAwB,QAAQ;MACpE;AAEA,aAAO;IACT;;EAEF,QAAQ;IACN,gBAAgB;IAChB,kBAAkB;IAClB,YAAY;IACZ,UAAU;IACV,oBAAoB;IACpB,YAAY,CAAC,cAA2D;AACtE,UAAI,EAAC,uCAAW,SAAQ;AACtB,eAAO;MACT;AACA,YAAM,SAAS,UAAU;AAEzB,aAAO,QAAQ,UAAU,SAAS,KAAK,OAAO,SAAS,IAAI,SAAS;IACtE;IACA,iBAAiB,CAAC,eAAiD;AACjE,UAAI,eAAe,QAAW;AAC5B,eAAOA,eAAc,wBAAwB;MAC/C;AACA,UAAI,OAAO,eAAe,WAAW;AACnC,eAAO,aAAaA,eAAc,wBAAwB,SAAS;MACrE;AAEA,aAAO;IACT;;EAEF,UAAU;IACR,gBAAgB;IAChB,kBAAkB;IAClB,YAAY;IACZ,UAAU;IACV,oBAAoB;IACpB,YAAY,CAAC,cAA2C,QAAQ,UAAU,SAAS;IACnF,iBAAiB,CAAC,eAAgC;AAChD,UAAI,eAAe,QAAW;AAC5B,eAAOA,eAAc,wBAAwB;MAC/C;AAEA,aAAO,QAAQ,UAAU;IAC3B;;EAEF,eAAe;IACb,gBAAgB;IAChB,kBAAkB;IAClB,YAAY;IACZ,UAAU;IACV,oBAAoB;IACpB,YAAY,CAAC,cAA2C,QAAQ,UAAU,SAAS;IACnF,iBAAiB,CAAC,eAAgC;AAChD,UAAI,eAAe,QAAW;AAC5B,eAAOA,eAAc,wBAAwB;MAC/C;AAEA,aAAO,QAAQ,UAAU;IAC3B;;;AAIG,IAAM,aAAa;EACxB,yBAAyB,oBAAI,IAAG;EAEhC,aAAkC,IAAO,kBAA6C;AACpF,WAAO,qDAAkB,KAAK,CAACC,OAAmDA,GAAE,OAAO;EAC7F;EAEA,WAAW,mBAA4B,YAAsB;AAC3D,QAAI,sBAAsB,QAAW;AACnC,YAAM,SAAS,cAAc,UAAU;AACvC,YAAM,cAAc,OAAO,WACvB,aAAa,OAAO,gBAAgB,WAAW,UAAU,OACzD,aAAa,UAAU;AAC3B,WAAK,wBAAwB,IAAI,WAAW;IAC9C;EACF;EAEA,eACE,YACA,eACA,kBACA,QACA,SAAgB;AAEhB,UAAM,SAAS,cAAc,UAAU;AACvC,UAAM,aAAa,cAAc,OAAO,gBAAgB;AAExD,QAAI,WAAW,CAAC,OAAO,oBAAoB;AACzC,aAAO;IACT;AAEA,QAAI,QAAQ;AACV,YAAM,YAAY,KAAK,aAAa,OAAO,gBAAgB,gBAAgB;AAE3E,WAAI,uCAAW,YAAW,MAAM;AAC9B,eAAO,KAAK,uBAAuB,YAAY,UAAU;MAC3D;AAEA,UAAI,EAAC,uCAAW,SAAQ;AACtB,eAAO;MACT;AAEA,UAAI,eAAe,QAAW;AAC5B,aAAK,WAAW,YAAY,UAAU;MACxC;AAEA,aAAO,KAAK,kBAAkB,YAAY,SAAS;IACrD;AAEA,WAAO,KAAK,uBAAuB,YAAY,UAAU;EAC3D;EAEA,kBACE,YACA,WAA6B;AAE7B,WAAO,cAAc,UAAU,EAAE,WAAW,SAAS;EACvD;EAEA,uBACE,YACA,YAAmB;AAEnB,WAAO,cAAc,UAAU,EAAE,gBAAgB,UAAU;EAC7D;EAEA,MAAM,oBAAoB,QAA4B;AACpD,UAAM,UAAU,OAAO,SAAS;AAChC,UAAM,gBAAgB,OAAO,YAAY,CAAA;AAEzC,SAAK,wBAAwB,MAAK;AAElC,QAAI,mBAAgD;AACpD,QAAI,eAAe;AAEnB,QAAI;AACF,yBAAmB,MAAM,cAAc,mBAAkB;AACzD,qBAAe,qBAAqB,QAAQ,qBAAqB;IACnE,SAAS,GAAG;AACV,cAAQ,KACN,4FACA,CAAC;IAEL;AAEA,UAAM,uBACJ,gBAAgB,CAAC,UACbD,eAAc,0BACdA,eAAc;AAEpB,QAAI;AACF,iBAAW,cAAc,cAAc;AACrC,cAAM,SAAS,KAAK,eAClB,YACA,eACA,kBACA,cACA,OAAO;AAET,eAAO,OAAO,sBAAsB,EAAE,CAAC,UAAU,GAAG,OAAM,CAAE;MAC9D;IACF,SAAS,GAAG;AACV,cAAQ,KACN,wFACA,CAAC;AAGH,aAAOA,eAAc;IACvB;AAEA,QAAI,gBAAgB,KAAK,wBAAwB,OAAO,GAAG;AACzD,YAAM,iBAAiB,gCAAgC,MAAM,KAAK,KAAK,uBAAuB,EAAE,KAAK,IAAI,CAAC;AAC1G,sBAAgB,KACd;QACE,cAAc;QACd,aAAa,yBAAyB,cAAc;SAEtD,SAAS;IAEb;AAEA,WAAO;EACT;;;;ACrKI,IAAgB,mBAAhB,MAAgC;EAkBpC,YAAY,SAA6B;AARlC,SAAA,kBAAoC,CAAA;AAEpC,SAAA,iBAAiC,CAAA;AAEjC,SAAA,sBAA+C,CAAA;AA6yC/C,SAAA,iBAAiB,CAAC,gBAAiC,OAAwB;AA36CpF,kBAAAE,KAAA;AA46CI,UAAI,gBAAgB;AAClB,cAAM,qBAAoB,2BAAgB,eACxC,cAAc,MADU,mBAEvB,0BAFuB,mBAEA,KAAK,CAAAC,OAAKA,GAAE,OAAO;AAE7C,YAAI,mBAAmB;AACrB,iBAAO;QACT;AAEA,cAAM,wBAAuBD,MAAA,gBAAgB,eAAe,cAAc,MAA7C,gBAAAA,IAAgD;AAE7E,YAAI,sBAAsB;AACxB,iBAAO;QACT;AAEA,cAAM,wBAAwB,gBAAgB,yBAAyB,cAAc;AAErF,gBAAO,2BAAsB,OAAO,CAAAC,OAAKA,GAAE,mBAAmB,cAAc,MAArE,mBAAyE;MAClF;AAEA,aAAO,gBAAgB,MAAM,qBAAqB,KAAK;IACzD;AAEO,SAAA,mBAAmB,MAA+C;AACvE,YAAM,UAAU,KAAK,eAAc;AAEnC,UAAI,SAAS;AACX,eAAO,QAAQ;MACjB;AAEA,aAAO;IACT;AAEO,SAAA,kBAAkB,CAAC,cACxB,gBAAgB,gBAAgB,SAAS;AAEpC,SAAA,0BAA0B,MAAM,gBAAgB,MAAM;AAEtD,SAAA,2BAAiF,CACtF,uBACA,UACE;AACF,sBAAgB,yBAAyB,uBAAuB,KAAK;IACvE;AAEO,SAAA,4BAAsF,MAC3F,gBAAgB,6BAA4B;AAEvC,SAAA,iBAAiB,CAAC,mBAAmC;AAC1D,UAAI,gBAAgB,MAAM,gBAAgB,kBAAkB,CAAC,gBAAgB;AAC3E,eAAO,gBAAgB,MAAM;MAC/B;AAEA,aAAO,gBAAgB,eAAe,eAAe,cAAc;IACrE;AAEO,SAAA,cAA+D,cAAW;AAC/E,8BAAwB,YAAY,QAAQ;IAC9C;AAEO,SAAA,cAAc,CAAI,cAA8B,aAAa,YAAe,SAAS;AAErF,SAAA,kBAAkB,CAAC,cAA8B,aAAa,cAAc,SAAS;AAErF,SAAA,0BAA0B,CAAC,cAA2B;AA5+C/D;AA6+CI,qCAAkB,MAAM,0BAAxB,mBAAgD;;AAE3C,SAAA,iBAA+D,CAAC,aAAa,UAAS;AAC3F,wBAAkB,eAAe,aAAa,KAAK;AAInD,UAAI,eAAe,kBAAkB,MAAM,gBAAgB;AACzD,aAAK,MAAK;MACZ;IACF;AAEO,SAAA,aAAuD,CAAC,SAAS,eAAe,UAAS;AAC9F,wBAAkB,WAAW,SAAS,eAAe,KAAK;IAC5D;AAEO,SAAA,iBAA+D,CAAC,aAAa,UAAS;AAC3F,wBAAkB,eAAe,aAAa,KAAK;IACrD;AAEO,SAAA,kBAAiE,CAAC,cAAc,UAAS;AAC9F,wBAAkB,gBAAgB,cAAc,KAAK;IACvD;AAEO,SAAA,UAAiD,CAAC,MAAM,UAAS;AACtE,wBAAkB,QAAQ,MAAM,KAAK;IACvC;AAEO,SAAA,eAA2D,CAAC,UAAyB;AAC1F,wBAAkB,aAAa,KAAK;IACtC;AAEO,SAAA,iBAAmE,iBAAc;AACtF,sBAAgB,qBAAqB,WAAW;IAClD;AAEO,SAAA,4BAA4B,CAAC,aAA0B,mBAAkC;AAC9F,sBAAgB,oBAAoB,gBAAgB,EAAE,YAAW,CAAE;IACrE;AAEO,SAAA,iBAA+D,CAAC,WAAW,UAAS;AACzF,wBAAkB,eAA6B,WAAW,KAAK;AAC/D,wBAAkB,yBAAwB,uCAAW,UAAS,CAAC;IACjE;AAEO,SAAA,YAAqD,CAAC,QAAQ,UAAS;AAC5E,wBAAkB,UAAU,QAAQ,KAAK;AAGzC,UAAI,oBAAoB,YAAW,GAAI;AACrC,oBAAY,oBAAoB,WAAW;MAC7C,OAAO;AACL,oBAAY,oBAAoB,cAAc;MAChD;IACF;AAEO,SAAA,6BAA6B,CAAC,mBACnC,gBAAgB,eAAe,WAAW,cAAc;AAEnD,SAAA,gBAA+D,gBAAa;AACjF,YAAM,gBAAgB,CAAC,GAAG,oBAAoB,MAAM,eAAe,GAAG,UAAU;AAChF,0BAAoB,cAAc,aAAa;IACjD;AAEO,SAAA,iBAAkE,CACvE,aACA,mBACE;AACF,2BAAqB,eAAe,aAAa,cAAc;IACjE;AAEO,SAAA,gBAAmE,aACxE,wBAAwB,cAAc,OAAO;AAExC,SAAA,eAA6D,aAClE,cAAc,mBAAmB,OAAO;AAEnC,SAAA,gBAA+D,MACpE,oBAAoB,cAAa;AAE5B,SAAA,oBAA6D,eAClE,UAAU,kBAAkB,SAAS;AAEhC,SAAA,yBAA+E,CACpF,qBACA,UACE;AACF,YAAM,OAAO,aAAa,cAAc,KAAK;AAC7C,YAAM,aAAa,sBAAsB,EAAE,GAAG,qBAAqB,KAAI,IAAK;AAC5E,wBAAkB,uBAAuB,YAAY,KAAK;IAC5D;AAgPO,SAAA,sBAAsB,MAAM,QAAQ,gBAAgB,MAAM,iBAAiB;AAE3E,SAAA,kBAAiE,CACtE,SACA,OACA,UACE;AACF,wBAAkB,gBAAgB,SAAS,OAAO,KAAK;IACzD;AAEO,SAAA,qBAAuE,CAC5E,SACA,UACE;AACF,wBAAkB,mBAAmB,SAAS,KAAK;IACrD;AAEO,SAAA,aAAa,CAAC,mBAAmC;AACtD,UAAI,gBAAgB,MAAM,gBAAgB,kBAAkB,CAAC,gBAAgB;AAC3E,eAAO,kBAAkB,MAAM;MACjC;AAEA,aAAO,gBAAgB,eAAe,WAAW,cAAc;IACjE;AAEO,SAAA,8BACL,eAAa,gBAAgB,4BAA4B,SAAS;AAE7D,SAAA,eAAyD,CAAC,cAA6B;AAC5F,sBAAgB,aAAa,SAAS;IACxC;AAEO,SAAA,eAA6D,eAAY;AAC9E,0BAAoB,aAAa,SAAS;IAC5C;AAEO,SAAA,oBAAwE,MAAK;AAClF,2BAAqB,kBAAiB;IACxC;AAEO,SAAA,wBAA6E,CAClF,oBACA,UACE;AACF,wBAAkB,sBAAsB,oBAAoB,KAAK;IACnE;AAEO,SAAA,0BAAiF,CACtF,YACA,UACE;AACF,wBAAkB,wBAAwB,YAAY,KAAK;IAC7D;AAEO,SAAA,iCACL,CAAC,6BAA6B,UAAS;AACrC,sBAAgB,+BAA+B,6BAA6B,KAAK;IACnF;AAEK,SAAA,0BAAiF,CACtF,sBACA,UACE;AACF,wBAAkB,wBAAwB,sBAAsB,KAAK;IACvE;AAEO,SAAA,oBAAqE,aAAU;AACpF,wBAAkB,kBAAkB,OAAO;IAC7C;AAEO,SAAA,yBAAyB,MAAK;AACnC,UAAI,KAAK,OAAM,GAAI;AAEjB,YAAI,KAAK,wBAAuB,GAAI;AAClC;QACF;AAGA,aAAK,SAAS,oBAAoB;MACpC,OAAO;AAEL,aAAK,KAAK,EAAE,MAAM,qBAAoB,CAAE;MAC1C;IACF;AAvwDE,SAAK,UAAU;AACf,SAAK,UAAU,QAAQ;AACvB,SAAK,eAAe,KAAK,mBAAmB,OAAO;AACnD,SAAK,kBAAkB,KAAK,sBAC1B,QAAQ,UACR,KAAK,YAAY;AAEnB,SAAK,qBAAqB,KAAK,yBAAyB,OAAO;AAC/D,SAAK,gBAAgB,KAAK,eAAe,QAAQ,QAA8B;AAC/E,SAAK,eAAe,KAAK,WAAW,OAAO;EAC7C;EAEQ,sBAAsB,UAA8B,cAA2B;AACrF,UAAM,oBAAoB,qCACtB,IAAI,aAAW,QAAQ,WACxB,OAAO,CAAC,cAA2C,QAAQ,SAAS;AAEvE,QAAI,uDAAmB,QAAQ;AAC7B,aAAO,CAAC,GAAG,IAAI,IAAI,iBAAiB,CAAC;IACvC;AAEA,UAAM,oBAAoB,6CAAc,IAAI,aAAW,QAAQ;AAE/D,WAAO,CAAC,GAAG,IAAI,IAAI,iBAAiB,CAAC;EACvC;EAEU,MAAM,WAAW,SAA6B;AA7J1D,gBAAAD;AA8JI,SAAK,0BAA0B,OAAO;AACtC,SAAK,gBAAgB,OAAO;AAC5B,UAAM,KAAK,kBAAiB;AAC5B,SAAK,oBAAoB,OAAO;AAChC,UAAM,KAAK,uBAAsB;AACjC,SAAK,iBAAiB,MAAM,WAAW,oBAAoB,OAAO;AAClE,sBAAkB,kBAAkB,KAAK,cAAc;AACvD,QAAI,KAAK,eAAe,QAAQ;AAC9B,uBAAiB,mBAAmB,KAAK,eAAe,MAAM;IAChE;AAEA,UACE,uBAAkB,MAAM,mBAAxB,mBAAwC,UACvC,MAAM,SAAQ,uBAAkB,MAAM,mBAAxB,mBAAwC,OAAO,OAC5DA,MAAA,kBAAkB,MAAM,mBAAxB,gBAAAA,IAAwC,QAAQ,UAAS,GAC3D;AACA,YAAM,KAAK,oBAAmB;IAChC;EACF;EAEQ,MAAM,sBAAmB;AAC/B,UAAM,iBAAiB,MAAM,cAAc,oBAAmB;AAC9D,QAAI,kBAAkB,eAAe,SAAQ,GAAI;AAC/C,YAAM,gBAAgB,OAAO,SAAS;AACtC,YAAM,kBAAkB,cAAc,gBACpC,eACA,gBACA,gBAAgB,yBAAyB;AAE3C,UAAI,CAAC,iBAAiB;AACpB,wBAAgB,KAAK,UAAU,aAAa,2BAA2B,OAAO;MAChF;IACF,OAAO;AACL,sBAAgB,KAAK,UAAU,aAAa,2BAA2B,OAAO;IAChF;EACF;EAEQ,oBAAoB,SAA6B;AAnM3D;AAoMI,UAAM,EAAE,GAAG,YAAW,IAAK;AAC3B,WAAO,YAAY;AACnB,WAAO,YAAY;AAEnB,qBAAiB,UAAU;MACzB,MAAM;MACN,OAAO;MACP,YAAY;QACV,GAAG;QACH,UAAU,QAAQ,SAAS,IAAI,CAAAE,OAAKA,GAAE,EAAE;QACxC,YAAY;UACV,WAAS,aAAQ,eAAR,mBAAoB,YAAW,CAAA;;;KAG7C;EACH;;EAGU,gBAAgB,SAA6B;AACrD,SAAK,4BAA4B,OAAO;AACxC,SAAK,0BAA0B,OAAO;AACtC,SAAK,0BAA0B,OAAO;AACtC,SAAK,+BAA+B,OAAO;AAC3C,SAAK,8BAA6B;EACpC;EAEU,0BAA0B,SAAsB;AACxD,QAAI,QAAQ,WAAW;AACrB,sBAAgB,aAAa,QAAQ,SAAS;IAChD;AACA,QAAI,QAAQ,gBAAgB;AAC1B,sBAAgB,kBAAkB,QAAQ,cAAc;IAC1D;EACF;EAEU,0BAA0B,SAAsB;AACxD,QAAI,CAAC,KAAK,8BAA8B,CAAC,KAAK,yBAAyB;AACrE,YAAM,IAAI,MAAM,oEAAoE;IACtF;AACA,oBAAgB,WAAW,QAAQ,YAAY,CAAA,GAAI,KAAK,cAAc;MACpE,4BAA4B,KAAK;MACjC,yBAAyB,KAAK;KAC/B;AACD,UAAM,UAAU,KAAK,kBAAiB;AACtC,QAAI,SAAS;AACX,sBAAgB,qBAAqB,OAAO;IAC9C;EACF;EAEU,+BAA+B,SAAsB;AAC7D,yBAAqB,WAAW,QAAQ,SAAS,KAAK;EACxD;EAEU,gCAA6B;AACrC,wBAAoB,WAAW,KAAK,eAAe;EACrD;EAEU,0BAA0B,SAA6B;AAC/D,sBAAkB,aAAa,QAAQ,SAAS;AAChD,sBAAkB,cAAc,QAAQ,UAAU;EACpD;EAEU,4BAA4B,SAA6B;AAlQrE;AAmQI,sBAAkB,SAAS,QAAQ,UAAU,KAAK;AAGlD,sBAAkB,uBAAuB,QAAQ,wBAAwB,KAAK;AAC9E,sBAAkB,qBAAqB,QAAQ,sBAAsB,KAAK;AAC1E,sBAAkB,iBAAiB,QAAQ,kBAAkB,KAAK;AAClE,sBAAkB,kBAAkB,QAAQ,kBAAkB,KAAK;AACnE,sBAAkB,uBAAuB,QAAQ,wBAAwB,KAAK;AAE9E,sBAAkB,oBAAoB,QAAQ,qBAAqB,KAAK;AACxE,sBAAkB,iBAAiB,QAAQ,aAAa;AAExD,sBAAkB,kBAAkB,QAAQ,cAAc;AAC1D,sBAAkB,cAAc,QAAQ,UAAU;AAClD,sBAAkB,oBAAoB,QAAQ,gBAAgB;AAC9D,sBAAkB,oBAAoB,QAAQ,gBAAgB;AAC9D,sBAAkB,qBAAqB,QAAQ,iBAAiB;AAChE,sBAAkB,UAAU,QAAQ,MAAM;AAC1C,sBAAkB,sBAAsB,QAAQ,kBAAkB;AAClE,sBAAkB,oBAAoB,QAAQ,gBAAgB;AAC9D,sBAAkB,iBAAiB,QAAQ,aAAa;AACxD,sBAAkB,YAAY,QAAQ,QAAQ;AAC9C,sBAAkB,yBAAyB,QAAQ,qBAAqB;AACxE,sBAAkB,mCAAmC,QAAQ,+BAA+B;AAC5F,sBAAkB,wBAAwB,QAAQ,iCAAiC;AAGnF,sBAAkB,uBAAuB,QAAQ,mBAAmB;AAGpE,UAAM,qBAAqB,YAAY,yBAAwB,KAAM,CAAA;AACrE,UAAM,eAAe,EAAE,GAAG,kBAAkB,MAAM,qBAAqB,GAAG,mBAAkB;AAE5F,sBAAkB,yBAAyB,YAAY;AAEvD,UAAM,kBAAkB,KAAK,mBAAkB;AAC/C,QAAI,CAAC,QAAQ,YAAY,iBAAiB;AACxC,cAAQ,WAAW;IACrB;AACA,sBAAkB,YAAY,QAAQ,QAAQ;AAC9C,sBAAkB,iBAAiB,QAAQ,aAAa;AACxD,sBAAkB,kBAAkB,QAAQ,cAAc;AAC1D,sBAAkB,QAAQ,QAAQ,IAAI;AAEtC,QAAI,CAAC,QAAQ,WAAW;AACtB,sBAAgB,KAAK,UAAU,aAAa,2BAA2B,OAAO;AAE9E;IACF;AAEA,UAAM,cAAa,aAAQ,aAAR,mBAAkB,KACnC,aAAW,QAAQ,cAAc,cAAc,MAAM;AAIvD,QAAI,YAAY;AACd,UAAI,QAAQ,YAAY;AACtB,YAAI,QAAQ,MAAM;AAChB,gBAAM,IAAI,MAAM,iDAAiD;QACnE;AAEA,0BAAkB,QAAQ,QAAQ,WAAW,UAAS,CAAE;MAC1D;IACF;EACF;EAEU,qBAAkB;AArU9B,gBAAAF,KAAA;AAsUI,QAAI,eAAe,SAAQ,GAAI;AAC7B,aAAO;QACL,QAAM,oBAAS,qBAAqB,OAAO,MAArC,mBAAyC,OAAzC,mBAA6C,gBAAe;QAClE,eACEA,MAAA,SAAS,cAA+B,iCAAiC,MAAzE,gBAAAA,IAA4E,YAAW;QACzF,KAAK,OAAO,SAAS;QACrB,OAAO,GAAC,cAAS,cAA+B,mBAAmB,MAA3D,mBAA8D,SAAQ,EAAE;;IAEpF;AAEA,WAAO;EACT;;EAGU,sBAAsB,SAAwB;AACtD,UAAM,YAAY,KAAK,wBAAuB;AAE9C,QAAI,WAAW;AACb,YAAM,qBAAqB,iBAAiB,sBAAsB,GAAG,SAAS,IAAI,OAAO,EAAE;AAC3F,sBAAgB,qBAAqB,kBAAkB;IACzD;EACF;EAEU,oBAAiB;AACzB,WAAO,iBAAiB,0BAA0B,KAAK,kBAAkB;EAC3E;EAEU,kBAAkB,SAAwB,SAAsB;AACxE,UAAM,kBAAkB,iBAAiB,kBAAkB,SAAS;MAClE,wBAAwB,QAAQ;MAChC,WAAW,QAAQ;KACpB;AAED,WAAO;EACT;EAEU,mBAAmB,SAAsB;AACjD,UAAM,mBAAmB,iBAAiB,mBAAmB,QAAQ,UAAU;MAC7E,wBAAwB,QAAQ;MAChC,eAAe,QAAQ;MACvB,WAAW,QAAQ;KACpB;AAED,WAAO;EACT;EAEU,yBAAyB,SAAsB;AACvD,UAAM,iBAAiB,QAAQ,SAAS,KAAK,CAAAE,OAAE;AArXnD;AAqXsD,aAAAA,GAAE,SAAO,aAAQ,mBAAR,mBAAwB;KAAE;AACrF,UAAM,kBAAkB,iBACpB,iBAAiB,kBAAkB,gBAAgB;MACjD,wBAAwB,QAAQ;MAChC,eAAe,QAAQ;MACvB,WAAW,QAAQ;KACpB,IACD;AAEJ,WAAO;EACT;EAEQ,MAAM,oBAAoB,WAAyB;AACzD,QAAI;AACF,YAAM,UAAU,KAAK,WAAW,SAAS;AACzC,YAAM,WAAW,aAAa,YAAY,SAAS;AACnD,YAAM,eAAe,aAAa,cAAc,SAAS;AACzD,YAAM,EAAE,YAAW,IAAK,gBAAgB,eAAe,SAAS,KAAK,CAAA;AAErE,WAAK,WAAW,MAAM,SAAS;AAC/B,UAAI,gBAAe,mCAAS,aAAY;AACtC,cAAM,QAAQ,WAAW,EAAE,UAAU,aAAY,CAAE;MACrD;AAEA,kBAAY,yBAAyB,SAAS;AAC9C,mBAAa,WAAW,SAAS;AACjC,WAAK,QAAQ,QAAW,SAAS;AACjC,WAAK,UAAU,gBAAgB,SAAS;AACxC,WAAK,uBAAuB,QAAW,SAAS;AAEhD,0BAAoB,kBAAkB,SAAS;AAE/C,sBAAgB,aAAa,SAAS;AACtC,sBAAgB,aAAa,SAAS;AACtC,WAAK,WAAW,OAAO,SAAS;IAClC,SAAS,OAAO;AACd,WAAK,WAAW,OAAO,SAAS;AAChC,YAAM,IAAI,MAAM,8BAA8B,SAAS,KAAM,MAAgB,OAAO,EAAE;IACxF;EACF;;EAGU,gBAAa;AACrB,SAAK,6BAA6B;MAChC,sBAAsB,YAAW;AAjavC;AAkaQ,cAAM,cAAc,gBAAgB,MAAM;AAC1C,cAAM,UAAU,KAAK,WAAW,WAAW;AAC3C,cAAM,WAAU,UAAK,eAAe,WAAW,MAA/B,mBAAkC;AAElD,YAAI,CAAC,SAAS;AACZ,gBAAM,IAAI,MAAM,mBAAmB;QACrC;AAEA,cAAM,SAAS,MAAM,QAAQ,qBAAqB,OAAO;AAEzD,aAAK,MAAK;AACV,aAAK,aAAY,iCAAQ,aAAY,IAAI;AACzC,oBAAY,uBAAuB,CAAC,GAAG,gBAAgB,MAAM,OAAO,KAAI,CAAE,CAAC;AAC3E,aAAK,gBAAgB,QAAQ,eAAY;AACvC,8BAAoB,eAClBC,eAAkB,+BAClB,SAAS;QAEb,CAAC;AACD,cAAM,KAAK,yBAAwB;MACrC;MACA,iBAAiB,OAAO,EAAE,IAAI,MAAM,MAAM,UAAU,OAAO,aAAa,UAAS,MAAM;AAvb7F,oBAAAH,KAAA,IAAAI,KAAA;AAwbQ,cAAM,cAAc,gBAAgB,MAAM;AAC1C,cAAM,aAAa,SAAS;AAC5B,cAAM,UAAU,KAAK,WAAW,UAAU;AAE1C,YAAI,SAAS,UAAU,eAAe,CAAC,aAAa;AAClD,gBAAM,mBAAmB,KAAK,gBAAe,EAAG,KAC9C,aAAW,QAAQ,mBAAmB,KAAK;AAE7C,cAAI,kBAAkB;AACpB,iBAAK,eAAe,gBAAgB;UACtC;QACF;AAEA,YAAI,CAAC,SAAS;AACZ,gBAAM,IAAI,MAAM,mBAAmB;QACrC;AAEA,cAAM,sBAAsB,KAAK,eAAe,UAAU;AAE1D,cAAM,MAAM,MAAM,QAAQ,QAAQ;UAChC;UACA;UACA;UACA;UACA;UACA,UAAS,2CAAa,QAAM,2DAAqB;UACjD,UACEJ,OAAA,sDAAa,YAAb,mBAAsB,YAAtB,mBAA+B,SAA/B,gBAAAA,IAAsC,SACtC,MAAAI,OAAA,gEAAqB,YAArB,mBAA8B,YAA9B,gBAAAA,IAAuC,SAAvC,mBAA8C;SACjD;AAED,YAAI,CAAC,KAAK;AACR;QACF;AAEA,oBAAY,sBAAsB,UAAU;AAC5C,aAAK,aAAa,EAAE,GAAG,KAAK,gBAAgB,WAAU,CAAE;AAKxD,cAAM,iBAAiB,kBAAkB,MAAM;AAC/C,cAAM,EAAE,SAAQ,KACd,iDAAgB,UAAS;;;UAGrB,EAAE,UAAU,CAAC,GAAG,cAAc,EAAC;YAC/B,MAAM,QAAQ,YAAY,EAAE,WAAW,YAAY,GAAE,CAAE;AAC7D,aAAK,eAAe,UAAU,UAAU;AACxC,aAAK,UAAU,aAAa,UAAU;AACtC,aAAK,wBAAwB,UAAU;MACzC;MACA,mBAAmB,OAAO,EAAE,IAAI,MAAM,MAAM,SAAQ,MAAM;AA5ehE;AA6eQ,cAAM,YAAY,gBAAgB,MAAM;AACxC,cAAM,UAAU,KAAK,WAAW,SAAS;AACzC,YAAI,mCAAS,WAAW;AACtB,iBAAM,mCAAS,UAAU,EAAE,IAAI,MAAM,MAAM,UAAU,UAAS,UAAK,eAAc,MAAnB,mBAAuB,GAAE;AACvF,sBAAY,sBAAsB,SAAS;AAC3C,eAAK,wBAAwB,SAAS;QACxC;MACF;MACA,YAAY,OAAO,mBAAmC;AACpD,cAAM,qBAAqB,sBAAsB,cAAc;AAC/D,YAAI;AAEF,gBAAM,oBAAoB,MAAM,QAAQ,WACtC,mBAAmB,IAAI,OAAO,CAACC,GAAE,MAAM,KAAK,oBAAoBA,GAAE,CAAC,CAAC;AAEtE,yBAAe,UAAS;AACxB,+BAAqB,kBAAiB;AACtC,gBAAM,SAAS,cAAa;AAC5B,8BAAoB,qBAAqB,MAAS;AAClD,gBAAM,WAAW,kBAAkB,OACjC,CAAC,WAA4C,OAAO,WAAW,UAAU;AAG3E,cAAI,SAAS,SAAS,GAAG;AACvB,kBAAM,IAAI,MAAM,SAAS,IAAI,CAAAC,OAAKA,GAAE,OAAO,OAAO,EAAE,KAAK,IAAI,CAAC;UAChE;AAEA,sBAAY,8BAA6B;AAEzC,2BAAiB,UAAU;YACzB,MAAM;YACN,OAAO;YACP,YAAY;cACV,WAAW,kBAAkB;;WAEhC;QACH,SAAS,OAAO;AACd,gBAAM,IAAI,MAAM,gCAAiC,MAAgB,OAAO,EAAE;QAC5E;MACF;MACA,gBAAgB,CAAC,QAAkB;AACjC,YAAI,CAAC,KAAK;AACR,iBAAO,QAAQ,OAAO,QAAQ;QAChC;AAEA,eAAO,IAAI,KAAK,QAAG;AA1hB3B;AA0hB8B,0BAAQ,YAAO,aAAP,mBAAkB,OAAO,EAAE,EAAE;SAAC;MAC9D;MACA,aAAa,OAAO,YAAmB;AACrC,cAAM,UAAU,KAAK,WAAW,gBAAgB,MAAM,WAA6B;AACnF,cAAM,SAAS,OAAM,mCAAS,YAAY;UACxC;UACA,SAAS,kBAAkB,MAAM;UACjC,UAAU,aAAa,YAAY,gBAAgB,MAAM,WAA6B;;AAGxF,gBAAO,iCAAQ,cAAa;MAC9B;MACA,iBAAiB,OAAO,SAA6B;AACnD,cAAM,YAAY,KAAK;AACvB,YAAIH,eAAkB,0BAA0B,SAAS,SAAS,GAAG;AACnE,gBAAM,UAAU,KAAK,WAAW,gBAAgB,MAAM,WAA6B;AAEnF,gBAAM,WAAW,aAAa,YAAY,SAAS;AACnD,gBAAM,SAAS,OAAM,mCAAS,gBAAgB;YAC5C,GAAG;YACH,aAAa,KAAK,eAAc;YAChC;;AAGF,kBAAO,iCAAQ,SAAQ;QACzB;AAEA,eAAO;MACT;MACA,aAAa,OAAO,SAAoC;AACtD,YAAI,KAAK,mBAAmB,cAAc,MAAM,KAAK;AACnD,gBAAM,UAAU,KAAK,WAAW,gBAAgB,MAAM,WAA6B;AACnF,gBAAM,WAAW,aAAa,YAC5B,gBAAgB,MAAM,WAA6B;AAErD,gBAAM,cAAc,KAAK,eAAc;AACvC,cAAI,CAAC,aAAa;AAChB,kBAAM,IAAI,MAAM,0BAA0B;UAC5C;AAEA,gBAAM,SAAS,OAAM,mCAAS,YAAY;YACxC,GAAG;YACH;YACA;;AAGF,kBAAO,iCAAQ,QAAO;QACxB;AAEA,eAAO;MACT;MACA,cAAc,YAAW;AA7kB/B;AA8kBQ,cAAM,KAAK,aAAa;UACtB,SAAS,kBAAkB,MAAM;UACjC,SAAS,QAAO,UAAK,eAAc,MAAnB,mBAAuB,EAAE;UACzC,gBAAgB,gBAAgB,MAAM;SACvC;AAED,eAAO,kBAAkB,MAAM,gBAAgB;MACjD;MACA,eAAe,OAAO,SAAiB,MAAM,cAAc,iBAAiB,IAAI;MAChF,eAAe,OAAO,SAA2B;AAC/C,cAAM,UAAU,KAAK,WAAW,gBAAgB,MAAM,WAA6B;AACnF,cAAM,cAAc,KAAK,eAAc;AACvC,cAAM,cAAc,KAAK,eAAc;AACvC,cAAM,WAAW,aAAa,YAC5B,gBAAgB,MAAM,WAA6B;AAErD,YAAI,CAAC,eAAe,CAAC,aAAa;AAChC,gBAAM,IAAI,MAAM,yCAAyC;QAC3D;AAEA,cAAM,SAAS,OAAM,mCAAS,cAAc,EAAE,GAAG,MAAM,aAAa,UAAU,YAAW;AAEzF,eAAO,iCAAQ;MACjB;MACA,YAAY,CAAC,OAAe,aAAoB;AAC9C,cAAM,UAAU,KAAK,WAAW,gBAAgB,MAAM,WAA6B;AAEnF,gBAAO,mCAAS,WAAW,EAAE,OAAO,SAAQ,OAAO;MACrD;MACA,aAAa,CAAC,OAAe,aAAoB;AAC/C,cAAM,UAAU,KAAK,WAAW,gBAAgB,MAAM,WAA6B;AAEnF,gBAAO,mCAAS,YAAY,EAAE,OAAO,SAAQ,OAAO;MACtD;MACA,iBAAiB,OAAO,WAAkD;AACxE,cAAM,UAAU,KAAK,WAAW,gBAAgB,MAAM,WAA6B;AAEnF,eAAO,OAAM,mCAAS,gBAAgB;MACxC;MACA,kBAAkB,OAAO,WAAmD;AAC1E,cAAM,UAAU,KAAK,WAAW,gBAAgB,MAAM,WAA6B;AAEnF,eAAO,OAAM,mCAAS,iBAAiB;MACzC;MACA,mBAAmB,OAAO,WAAoD;AAC5E,cAAM,UAAU,KAAK,WAAW,gBAAgB,MAAM,WAA6B;AAEnF,YAAI,mCAAS,mBAAmB;AAC9B,iBAAO,MAAM,QAAQ,kBAAkB,MAAM;QAC/C;AAEA,eAAO;MACT;MACA,iBAAiB,OAAO,WAAkD;AACxE,cAAM,UAAU,KAAK,WAAW,gBAAgB,MAAM,WAA6B;AAEnF,eAAQ,OAAM,mCAAS,gBAAgB,YAAY,CAAA;MACrD;MACA,eAAe,CAAC,cAA6B;AAC3C,cAAM,cAAc,KAAK,eAAe,SAAS;AACjD,YAAI,CAAC,eAAe,CAAC,kBAAkB,MAAM,SAAS;AACpD;QACF;AAEA,aAAK,oBAAoB,kBAAkB,MAAM,SAAS,2CAAa,IAAI,SAAS;MACtF;;AAGF,SAAK,0BAA0B;MAC7B,mBAAmB,OAAM,gBAAe,MAAM,KAAK,kBAAkB,WAAW;;MAEhF,6BAA6B,YAAY,KAAK,4BAA2B;;AAG3E,yBAAqB,UAAU,KAAK,0BAA0B;EAChE;EAEU,8BAA2B;AA3pBvC,gBAAAH,KAAA,IAAAI;AA4pBI,UAAM,eAAe,aAAa,cAAc,gBAAgB,MAAM,WAAW;AAEjF,QAAI,iBAAiBD,eAAkB,+BAA+B;AACpE,YAAM,cAAa,gBAAK,sBAAL,mBAAwB,YAAxB,mBAAiC;AAEpD,aAAO;;;;;;QAML,uBACEC,OAAA,MAAAJ,MAAA,KAAK,sBAAL,gBAAAA,IAAwB,YAAxB,mBAAiC,SAAjC,gBAAAI,IAAuC,SAAS,UAAS;QAC3D,wBAAwB,KAAK,wBAAwB,UAAU;;IAEnE;AAEA,WAAO,EAAE,qBAAqB,MAAM,wBAAwB,CAAA,EAAE;EAChE;EAEU,MAAM,kBAAkB,aAAwB;AACxD,QAAI,CAAC,aAAa;AAChB;IACF;AAEA,UAAM,mBAAmB,YAAY;AACrC,UAAM,mBAAmB,KAAK,2BAA2B,YAAY,cAAc;AAEnF,QAAI,kBAAkB;AACpB,YAAM,WAAW,aAAa,YAAY,gBAAgB;AAC1D,YAAM,eAAe,aAAa,cAAc,gBAAgB;AAEhE,UAAI,YAAY,mBAAmB,gBAAgB,MAAM,aAAa;AACpE,cAAM,UAAU,KAAK,WAAW,gBAAgB;AAEhD,eAAM,mCAAS,cAAc,EAAE,aAAa,UAAU,aAAY;MACpE,OAAO;AACL,aAAK,eAAe,WAAW;AAC/B,YAAI,iBAAiBD,eAAkB,+BAA+B;AACpE,eAAK,yBAAwB;QAC/B,OAAO;AACL,gBAAM,UAAU,KAAK,2BAA2B,gBAAgB;AAChE,cAAI,SAAS;AACX,iBAAK,YAAY;cACf;cACA,SAAS,YAAY;cACrB,gBAAgB;aACjB;UACH;QACF;MACF;IACF,OAAO;AACL,WAAK,eAAe,WAAW;IACjC;EACF;EAEU,wBAAwB,aAAsC,CAAA,GAAE;AACxE,WAAO,OAAO,OAAO,UAAU,EAAE,QAAQ,CAAC,cAAyC;AACjF,YAAM,SAAU,UAAU,UAAU,CAAA;AACpC,YAAM,iBAAiB,UAAU,SAAS,IAAI,aAAU;AACtD,cAAM,EAAE,SAAS,eAAc,IAAK,UAAU,iBAAiB,OAAsB;AAErF,eAAO,GAAG,cAAc,IAAI,OAAO;MACrC,CAAC;AAED,aAAO,MAAM,KAAK,oBAAI,IAAI,CAAC,GAAG,QAAQ,GAAG,cAAc,CAAC,CAAC;IAC3D,CAAC;EACH;;EAGU,eAAe,YAA+B;AACtD,SAAK,cAAa;AAElB,WAAO,KAAK,gBAAgB,OAAiB,CAAC,UAAU,cAAa;AAruBzE;AAsuBM,YAAM,YAAY,yCAAY,KAAK,CAAAI,OAAKA,GAAE,cAAc;AACxD,UAAI,WAAW;AACb,kBAAU,UAAU;UAClB;UACA,YAAW,UAAK,YAAL,mBAAc;UACzB,UAAU,KAAK,gBAAe;SAC/B;AACD,iBAAS,SAAS,IAAI;MACxB,OAAO;AACL,iBAAS,SAA2B,IAAI,IAAI,iBAAiB;UAC3D;UACA,UAAU,KAAK,gBAAe;SAC/B;MACH;AAEA,aAAO;IAET,GAAG,CAAA,CAAc;EACnB;EAEU,MAAM,iBAAiB,WAAyB;AA1vB5D;AA2vBI,SAAK,aAAa,SAAS;AAC3B,SAAK,cAAc,SAAS;AAC5B,YAAM,UAAK,kBAAL,mBAAqB,WAAW,eAAe,KAAK,SAAS;AACnE,UAAM,KAAK,kCAAkC,SAAS;EACxD;EAEU,MAAM,oBAAiB;AAC/B,UAAM,QAAQ,IACZ,KAAK,gBAAgB,IAAI,OAAM,cAAY;AACzC,YAAM,KAAK,iBAAiB,SAAS;IACvC,CAAC,CAAC;EAEN;EAEU,aAAa,gBAA8B;AACnD,UAAM,UAAU,KAAK,WAAW,cAAc;AAE9C,uCAAS,GAAG,cAAc,KAAK,cAAc,KAAK,IAAI;EACxD;EAEU,cAAc,gBAA8B;AACpD,UAAM,UAAU,KAAK,WAAW,cAAc;AAE9C,QAAI,CAAC,SAAS;AACZ;IACF;AAEA,UAAM,mBAAmB,YAAY,oBAAmB;AACxD,QAAI,qBAAqB,aAAa;AACpC,WAAK,UAAU,cAAc,cAAc;IAC7C,WAAW,qBAAqB,gBAAgB;AAK9C,kBAAY,kBAAiB;AAC7B,WAAK,UAAU,kBAAkB,cAAc;IACjD,OAAO;AACL,WAAK,UAAU,kBAAkB,cAAc;IACjD;AAEA,YAAQ,GAAG,iBAAiB,CAAC,EAAE,SAAS,QAAO,MAAM;AACnD,YAAM,cAAc,KAAK,gBAAe,EAAG,KACzC,CAAAL,OAAKA,GAAE,OAAO,WAAWA,GAAE,kBAAkB,OAAO;AAEtD,YAAM,kBAAkB,gBAAgB,MAAM,gBAAgB;AAC9D,YAAM,iBAAiB,gBAAgB,eAAe,WAAW,cAAc;AAE/E,UAAI,aAAa;AACf,cAAM,UAAU,mBAAmB,UAAU,UAAU;AAEvD,YAAI,SAAS;AACX,eAAK,YAAY,EAAE,SAAS,SAAS,SAAS,YAAY,IAAI,eAAc,CAAE;QAChF;MACF,OAAO;AACL,aAAK,sBAAsB,OAAO;MACpC;IACF,CAAC;AAED,YAAQ,GAAG,cAAc,KAAK,WAAW,KAAK,MAAM,cAAc,CAAC;AAEnE,YAAQ,GAAG,eAAe,iBAAc;AACtC,WAAK,eAAe,aAAa,cAAc;IACjD,CAAC;AAED,YAAQ,GAAG,uBAAuB,MAAK;AACrC,YAAM,UAAU,kBAAkB,MAAM;AACxC,YAAM,oBAAoB,gBAAgB,MAAM;AAEhD,UAAI,CAAC,WAAW,EAAC,uDAAmB,KAAI;AACtC;MACF;AAEA,WAAK,oBAAoB,SAAS,kBAAkB,IAAI,kBAAkB,cAAc;IAC1F,CAAC;AAED,YAAQ,GAAG,kBAAkB,CAAC,EAAE,SAAS,QAAO,MAAM;AAv0B1D;AAw0BM,YAAM,gBAAgB,gBAAgB,MAAM,gBAAgB;AAE5D,UAAI,iBAAiB,SAAS;AAC5B,aAAK,YAAY;UACf;UACA;UACA;SACD;MACH,WAAW,mBAAiB,qBAAgB,MAAM,sBAAtB,mBAAyC,KAAI;AACvE,aAAK,YAAY;UACf;UACA,UAAS,qBAAgB,MAAM,sBAAtB,mBAAyC;UAClD;SACD;MACH,OAAO;AACL,aAAK,gBAAgB,SAAS,SAAS,cAAc;MACvD;AACA,WAAK,gBAAgB,cAAc;IACrC,CAAC;EACH;EAEU,MAAM,kCAAkC,gBAA8B;AA71BlF,gBAAAF;AA81BI,UAAM,KAAK,qBAAoB;AAE/B,QAAI,KAAK,mBAAmB;AAC1B,OAAAA,OAAA,gBAAK,kBAAL,mBAAqB,oBAArB,mBAAsC,yBAAtC,gBAAAA,IAAA,SAA6D,KAAK;IACpE;EACF;;EAWU,MAAM,yBAAsB;AACpC,UAAM,QAAQ,WACZ,KAAK,gBAAgB,IAAI,eAAa,KAAK,wBAAwB,SAAS,CAAC,CAAC;EAElF;EAEU,MAAM,wBAAwB,WAAyB;AAC/D,QAAI;AACF,UAAI,cAAc,cAAc,MAAM,OAAO,eAAe,UAAS,GAAI;AACvE,4BAAoB,eAAe,cAAc,aAAa,MAAM,SAAS;MAC/E;AAEA,YAAM,cAAc,oBAAoB,eAAe,SAAS;AAEhE,WAAK,UAAU,cAAc,SAAS;AAEtC,cAAQ,aAAa;QACnB,KAAK,cAAc,aAAa;AAC9B,gBAAM,KAAK,yBAAwB;AACnC;QACF,KAAK,cAAc,aAAa;AAE9B;QACF;AACE,gBAAM,KAAK,sBAAsB,SAAS;MAC9C;IACF,SAAS,KAAK;AACZ,cAAQ,KAAK,4CAA4C,GAAG;AAC5D,WAAK,UAAU,gBAAgB,SAAS;IAC1C;EACF;EAEU,MAAM,sBAAsB,WAAyB;AA94BjE,gBAAAA;AA+4BI,UAAM,UAAU,KAAK,WAAW,SAAS;AACzC,UAAM,cAAc,oBAAoB,eAAe,SAAS;AAChE,UAAM,cAAc,KAAK,eAAe,SAAS;AACjD,UAAM,aAAa,oBAAoB,cAAc,SAAS;AAE9D,UAAM,YAAY,WAAW,KAAK,CAAAC,OAAKA,GAAE,OAAO,WAAW;AAE3D,QAAI;AACF,UAAI,CAAC,WAAW,CAAC,WAAW;AAC1B,cAAM,IAAI,MAAM,gDAAgD,SAAS,EAAE;MAC7E;AAEA,UAAI,EAAC,2CAAa,KAAI;AACpB,cAAM,IAAI,MAAM,uBAAuB;MACzC;AAEA,YAAM,aAAa,OAAM,mCAAS,eAAe;QAC/C;QACA,IAAI,UAAU;QACd,SAAS,YAAY;QACrB,SAAQD,OAAA,sDAAa,YAAb,mBAAsB,YAAtB,mBAA+B,SAA/B,gBAAAA,IAAsC;;AAGhD,UAAI,YAAY;AACd,cAAM,WAAW,OAAM,mCAAS,YAAY;UAC1C;UACA,IAAI,UAAU;;AAGhB,YAAI,YAAY,SAAS,SAAS,SAAS,GAAG;AAC5C,eAAK,eAAe,SAAS,UAAU,SAAS;QAClD,OAAO;AACL,eAAK,eACH,CAAC,eAAe,cAAc,WAAW,WAAW,SAAS,KAAK,CAAC,GACnE,SAAS;QAEb;AAEA,aAAK,aAAa,EAAE,GAAG,YAAY,gBAAgB,UAAS,CAAE;AAC9D,cAAM,KAAK,YAAY,EAAE,GAAG,YAAY,gBAAgB,UAAS,CAAE;AACnE,aAAK,UAAU,aAAa,SAAS;MACvC,OAAO;AACL,aAAK,UAAU,gBAAgB,SAAS;MAC1C;IACF,SAAS,GAAG;AACV,WAAK,UAAU,gBAAgB,SAAS;IAC1C;EACF;EAEU,MAAM,2BAAwB;AACtC,UAAM,YAAY,KAAK,gBAAgB,IAAI,OAAM,mBAAiB;AAj8BtE,kBAAAA,KAAA,IAAAI;AAk8BM,YAAM,UAAU,KAAK,WAAW,cAAgC;AAChE,YAAM,sBACJ,MAAAJ,OAAA,gBAAK,sBAAL,mBAAwB,YAAxB,mBAAiC,eAAjC,gBAAAA,IAA8C,oBAA9C,mBAA+D,aAAY,CAAA;AAG7E,YAAM,iBAAgBI,MAAA,gBAAgB,MAAM,sBAAtB,gBAAAA,IAAyC;AAE/D,YAAM,iBACJ,kBAAkB,KAAK,aAAU;AAC/B,cAAM,EAAE,QAAO,IAAK,UAAU,iBAAiB,OAAsB;AAErE,eAAO,aAAY,+CAAe;MACpC,CAAC,KAAK,kBAAkB,CAAC;AAE3B,UAAI,gBAAgB;AAClB,cAAM,cAAc,UAAU,oBAAoB,cAAc;AAChE,cAAM,EAAE,SAAS,QAAO,IAAK,UAAU,iBAAiB,WAAW;AACnE,qBAAa,cACX,gBACAD,eAAkB,6BAA8C;AAGlE,YACE,KAAK,gBACL,gBAAgB,MAAM,sBACrB,mCAA0B,eAAc,cAAc,MAAM,KAC7D;AACA,gBAAM,WAAW,mCAAS,yBAAyB;YACjD,cAAc,KAAK,gBAAe;YAClC,UAAU,KAAK;YACf,mBAAmB,gBAAgB,MAAM;;AAE3C,uBAAa,YAAY,gBAAgB,QAAQ;QACnD,OAAO;AACL,uBAAa,YAAY,gBAAgB,KAAK,iBAAiB;QACjE;AAEA,4BAAoB,eAClB,cAAc,aAAa,gBAC3B,cAAc;AAEhB,oBAAY,sBAAsB,cAAc;AAEhD,aAAK,0BAA0B,cAAc;AAC7C,cAAM,KAAK,YAAY;UACrB;UACA;UACA;SACD;MACH,OAAO;AACL,aAAK,UAAU,gBAAgB,cAAc;MAC/C;AAEA,WAAK,wBAAwB,cAAc;AAC3C,YAAM,gBAAgB,4BAA4B,cAAc;IAClE,CAAC;AAED,UAAM,QAAQ,IAAI,SAAS;EAC7B;EAEU,0BAA0B,gBAA8B;AA9/BpE,gBAAAH,KAAA,IAAAI;AA+/BI,UAAM,aAAYA,OAAA,MAAAJ,OAAA,gBAAK,sBAAL,mBAAwB,YAAxB,mBAAiC,eAAjC,gBAAAA,IAA8C,oBAA9C,mBAA+D,aAA/D,gBAAAI,IACd,IAAI,aAAU;AACd,YAAM,EAAE,QAAO,IAAK,UAAU,iBAAiB,OAAsB;AAErE,aAAO;IACT,GACC,OAAO,CAAC,SAAS,OAAO,SAAS,KAAK,QAAQ,OAAO,MAAM;AAE9D,QAAI,WAAW;AACb,WAAK,eACH,UAAU,IAAI,aACZ,eAAe,cACb,gBACA,SACA,mBAAmB,WAAW,YAAY,KAAK,CAChD,GAEH,cAAc;IAElB;EACF;EAEU,aAAa,EACrB,MACA,UACA,IACA,eAAc,GAGf;AACC,iBAAa,cAAc,gBAAgB,IAAI;AAC/C,iBAAa,YAAY,gBAAgB,QAAQ;AACjD,wBAAoB,eAAe,IAAI,cAAc;EACvD;EAEU,MAAM,gBAAgB,WAAyB;AACvD,UAAM,cAAc,oBAAoB,eAAe,SAAS;AAEhE,QAAI,CAAC,aAAa;AAChB;IACF;AAEA,UAAM,UAAU,KAAK,WAAW,SAAS;AACzC,UAAM,WAAW,OAAM,mCAAS,YAAY,EAAE,WAAW,IAAI,YAAW;AAExE,QAAI,YAAY,SAAS,SAAS,SAAS,GAAG;AAC5C,WAAK,eAAe,SAAS,UAAU,SAAS;IAClD;EACF;EAEU,MAAM,YACd,QAEC;AApjCL;AAsjCI,UAAM,oBAAoB,OAAO,mBAAmB,gBAAgB,MAAM;AAC1E,UAAM,iBAAiB,gBAAgB,0BACrC,OAAO,gBACP,OAAO,OAAO;AAGhB,UAAM,EAAE,SAAS,SAAS,eAAc,IAAK;AAE7C,UAAM,EAAE,SAAS,cAAa,IAAK,YAAY,sBAAqB;AACpE,UAAM,eAAe,WAAW;AAChC,UAAM,yBACJ,qBAAgB,MAAM,sBAAtB,mBAAyC,UAAS,cAAc;AAClE,UAAM,2BAA2B,gBAAgB,eAC/C,uBACA,cAAc;AAGhB,SAAK,UAAU,aAAa,cAAc;AAC1C,QAAI,wBAAwB,CAAC,0BAA0B;AACrD;IACF;AAEA,QAAI,cAAc;AAChB,UAAI,cAAc,KAAK,gBAAe,EAAG,KACvC,CAAAF,OAAKA,GAAE,GAAG,SAAQ,MAAO,aAAa,SAAQ,CAAE;AAElD,UAAI,sBAAsB,KAAK,gBAAe,EAAG,KAC/C,CAAAA,OAAKA,GAAE,mBAAmB,cAAc;AAI1C,UAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,qBAAqB;AAErE,cAAM,iBAAiB,KAAK,0BAAyB,KAAM,CAAA;AAC3D,cAAM,gBAAgB,eAAe,KACnC,QAAG;AAzlCb,cAAAM;AAylCgB,mBAAAA,MAAA,UAAU,mBAAmB,EAAE,MAA/B,gBAAAA,IAAkC,aAAY,aAAa,SAAQ;SAAE;AAE7E,cAAM,wBAAwB,eAAe,KAC3C,QAAG;AA5lCb,cAAAA;AA4lCgB,mBAAAA,MAAA,UAAU,mBAAmB,EAAE,MAA/B,gBAAAA,IAAkC,oBAAmB;SAAc;AAG3E,sBAAc,KAAK,gBAAe,EAAG,KAAK,CAAAN,OAAKA,GAAE,kBAAkB,aAAa;AAChF,8BAAsB,KAAK,gBAAe,EAAG,KAC3C,CAAAA,OACEA,GAAE,kBAAkB;QAEnB,6BAA6BA,MAAKA,GAAE,4BAA4B,qBAAsB;MAE7F;AAEA,YAAM,UAAU,eAAe;AAE/B,WAAI,mCAAS,oBAAmB,gBAAgB,MAAM,aAAa;AAEjE,YACE,kBAAkB,MAAM,uBACxB,CAAC,kBAAkB,MAAM,2BACzB,qBAAgB,MAAM,sBAAtB,mBAAyC,UAAS,cAAc,0BAChE;AACA,0BAAgB,uBAAsB;QACxC,OAAO;AACL,eAAK,eAAe,OAAO;QAC7B;MACF,WAAW,CAAC,mBAAmB;AAC7B,YAAI,gBAAgB;AAClB,eAAK,0BAA0B,gBAAgB,cAAc;QAC/D;MACF;AAEA,WAAK,wBAAwB,cAAc;AAE3C,UAAI,CAAC,YAAY,iBAAiB,SAAS,kBAAkB,MAAM,OAAO,GAAG;AAC3E,aAAK,gBAAgB,SAAS,mCAAS,IAAI,cAAc;MAC3D;AAEA,UAAI,mBAAmB;AACrB,cAAM,KAAK,YAAY,EAAE,SAAS,SAAS,mCAAS,IAAI,eAAc,CAAE;MAC1E,OAAO;AACL,cAAM,KAAK,YAAY,EAAE,SAAS,SAAS,iDAAgB,IAAI,eAAc,CAAE;MACjF;IACF;EACF;EAEQ,MAAM,gBACZ,SACA,SACA,gBAA8B;AAE9B,UAAM,cAAc,KAAK,eAAe,cAAc;AACtD,UAAM,aAAa,YAAW,2CAAa,MAAM,KAAK;AAEtD,QAAI,CAAC,YAAY;AACf;IACF;AAEA,UAAM,iBAAiB,GAAG,cAAc,IAAI,UAAU,IAAI,OAAO;AAEjE,SAAK,eAAe,gBAA+B,cAAc;AACjE,UAAM,KAAK,aAAa;MACtB;MACA,SAAS;MACT;KACD;EACH;EAEU,MAAM,cAAc,SAAiB,gBAA8B;AAC3E,QAAI;AACF,YAAM,oBAAoB,MAAM,KAAK,aAAa,OAAO;AACzD,UAAI,kBAAkB,CAAC,GAAG;AACxB,cAAM,SAAS,kBAAkB,CAAC;AAClC,aAAK,eAAe,OAAO,MAAM,cAAc;MACjD,OAAO;AACL,aAAK,eAAe,MAAM,cAAc;MAC1C;IACF,QAAQ;AACN,WAAK,eAAe,MAAM,cAAc;IAC1C;EACF;EAEU,wBAAwB,gBAA8B;AA7qClE;AA8qCI,UAAM,cAAc,oBAAoB,eAAe,cAAc;AACrE,UAAM,eAAe,aAAa,cAAc,cAAc;AAE9D,QACE,iBAAiBC,eAAkB,4BACnC,iBAAiBA,eAAkB,yBACnC;AACA,UAAI,aAAa;AACf,cAAM,YAAY,KAAK,cAAa,EAAG,KAAK,CAAAF,OAAKA,GAAE,OAAO,WAAW;AACrE,YAAI,WAAW;AACb,gBAAM,EAAE,MAAM,MAAM,SAAQ,IAAK;AACjC,gBAAM,OAAO,YAAY,KAAK,kBAAkB,SAAS;AACzD,eAAK,uBAAuB,EAAE,MAAM,MAAM,GAAG,KAAI,GAAI,cAAc;QACrE;MACF;IACF,WAAW,iBAAiBE,eAAkB,+BAA+B;AAC3E,YAAM,WAAW,aAAa,YAAY,cAAc;AAExD,UAAI,qCAAU,SAAS;AACrB,aAAK,uBACH;UACE,GAAG,SAAS,QAAQ,KAAK;UACzB,MAAM,SAAS,QAAQ,KAAK,SAAS;UACrC,OAAM,cAAS,QAAQ,KAAK,SAAS,UAA/B,mBAAuC;WAE/C,cAAc;MAElB;IACF,WAAW,aAAa;AACtB,UAAI,gBAAgB,cAAc,aAAa,UAAU;AACvD,cAAM,YAAY,KAAK,cAAa,EAAG,KACrC,CAAAF,OAAKA,GAAE,OAAO,cAAc,aAAa,QAAQ;AAGnD,aAAK,uBACH,EAAE,MAAM,mBAAmB,MAAM,KAAK,kBAAkB,SAAS,EAAC,GAClE,cAAc;MAElB;IACF;EACF;EAEU,MAAM,YAAY,QAI3B;AACC,UAAM,cAAc,YAAY,uBAC9B,KAAK,gBAAe,GACpB,OAAO,cAAc,EACrB,KAAK,CAAAC,OAAE;AAhuCb;AAguCgB,aAAAA,GAAE,GAAG,SAAQ,QAAO,YAAO,YAAP,mBAAgB;KAAU;AAE1D,QAAI,CAAC,eAAe,CAAC,OAAO,SAAS;AACnC;IACF;AAEA,UAAM,KAAK,oBAAoB,OAAO,SAAS,OAAO,SAAS,OAAO,cAAc;EACtF;EAEO,MAAM,QAAK;AAChB,UAAM,KAAK;EACb;EAEO,MAAM,oBACX,SACA,SACA,WAAyB;AAEzB,UAAM,UAAU,KAAK,WAAW,SAAS;AACzC,UAAM,cAAc,gBAAgB,0BAA0B,WAAW,OAAO;AAEhF,QAAI,SAAS;AACX,YAAM,UAAU,MAAM,QAAQ,WAAW;QACvC;QACA;QACA;QACA,QAAQ,KAAK,QAAQ;OACtB;AACD,WAAK,WAAW,QAAQ,SAAS,QAAQ,QAAQ,SAAS;AAE1D,aAAO;IACT;AAEA,WAAO;EACT;;EAGU,MAAM,6BAA0B;AArwC5C,gBAAAF,KAAA,IAAAI,KAAA,YAAAK,KAAA;AAswCI,UAAM,SAAS,WAAW,aAAa,CAAC,UAAU,SAAQ;AACxD,UAAI,OAAO;AACT,aAAK,iBAAiB,KAAK;MAC7B;AAEA,cAAQ,MAAM,GAAG,IAAI;IACvB,CAAC;AAED,UAAM,2BAAkD;MACtD,YAAW,UAAK,YAAL,mBAAc;MACzB,UAAU;QACR,QAAM,UAAK,YAAL,mBAAc,aAAWT,MAAA,KAAK,YAAL,gBAAAA,IAAc,SAAS,OAAO;QAC7D,eAAa,UAAK,YAAL,mBAAc,aAAWI,MAAA,KAAK,YAAL,gBAAAA,IAAc,SAAS,cAAc;QAC3E,OAAK,UAAK,YAAL,mBAAc,aAAW,UAAK,YAAL,mBAAc,SAAS,MAAM;QAC3D,SAAO,UAAK,YAAL,mBAAc,aAAWK,MAAA,KAAK,YAAL,gBAAAA,IAAc,SAAS,QAAQ,CAAC,EAAE;;MAEpE;;AAGF,sBAAkB,mBAAmB,SAAQ,UAAK,YAAL,mBAAc,eAAe,CAAC;AAC3E,SAAK,oBACH,KAAK,QAAQ,qBAAsB,MAAMC,GAAkB,KAAK,wBAAwB;AAC1F,SAAK,oBAAmB;EAC1B;EAEU,sBAAmB;AAC3B,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,GAAG,eAAe,CAAC,QAAe;AACvD,6BAAqB,OAAO,GAAG;MACjC,CAAC;AAED,WAAK,kBAAkB,GAAG,WAAW,qBAAqB,oBAAoB;AAE9E,WAAK,kBAAkB,GAAG,cAAc,MAAK;AAC3C,aAAK,gBAAgB,QAAQ,eAAY;AACvC,eAAK,aAAa,SAAS;QAC7B,CAAC;AACD,6BAAqB,kBAAiB;MACxC,CAAC;AAED,WAAK,kBAAkB,GAAG,gBAAgB,CAAC,YAA4B;AAErE,cAAM,cAAc,KAAK,gBAAe,EAAG,KAAK,CAAAT,OAAKA,GAAE,MAAM,OAAO;AACpE,cAAM,qBAAqB,KAAK,eAAc;AAE9C,YAAI,CAAC,aAAa;AAChB,eAAK,sBAAsB,OAAO;AAElC;QACF;AAEA,aAAI,yDAAoB,SAAO,2CAAa,KAAI;AAC9C,eAAK,eAAe,WAAW;QACjC;MACF,CAAC;AAED,WAAK,kBAAkB,GAAG,iBAAiB,CAAC,iBAAyB;AACnE,YAAI,cAAc,mBAAmB,YAAY,GAAG;AAClD,gBAAM,EAAE,MAAM,KAAI,IAAK,aAAa,OAAO;AAE3C,cACE,SAAS,qBACT,MAAM,QAAQ,IAAI,KAClB,eAAe,cAAc,KAAK,CAAC,CAAC,GACpC;AACA,iBAAK,YAAY,UAAU,iBAAiB,KAAK,CAAC,CAAC,CAAC;UACtD;QACF;MACF,CAAC;IACH;EACF;EAEU,0BAAuB;AA90CnC;AA+0CI,QACE,CAAC,KAAK,gCACN,eAAe,SAAQ,OACvB,UAAK,YAAL,mBAAc,YACd;AACA,WAAK,+BAA+B,KAAK,2BAA0B;IACrE;AAEA,WAAO,KAAK;EACd;EAEO,MAAM,uBAAoB;AAC/B,QAAI,CAAC,KAAK,mBAAmB;AAC3B,UAAI;AACF,cAAM,KAAK,wBAAuB;MACpC,SAAS,KAAK;AACZ,yBAAiB,UAAU;UACzB,MAAM;UACN,OAAO;UACP,YAAY;YACV,WAAW;YACX,cAAc,eAAe,QAAQ,IAAI,UAAU;YACnD,UAAU;;SAEb;AAED,gBAAQ,MAAM,wDAAwD,GAAG;MAC3E;IACF;AAEA,WAAO,KAAK;EACd;;EAGU,iBAAiB,OAAY;AACrC,UAAM,gCAAgC,OAAO,QAAQ,UAAU,uBAAuB,EAAE,KACtF,CAAC,CAAC,EAAE,EAAE,SAAAU,SAAO,CAAE,MAAM,MAAM,QAAQ,SAASA,QAAO,CAAC;AAGtD,UAAM,CAAC,UAAU,UAAU,IAAI,iCAAiC,CAAA;AAEhE,UAAM,EAAE,SAAS,cAAa,IAAK,cAAc,CAAA;AAEjD,QAAI,YAAY,WAAW,CAAC,KAAK,oBAAoB,QAAQ,GAAG;AAC9D,YAAM,aACJ,UAAU,aAAa,aAAoD;AAE7E,UAAI,YAAY;AACd,wBAAgB,KAAK,YAAY,OAAO;AACxC,aAAK,oBAAoB,QAAQ,IAAI;MACvC;IACF;EACF;EAEU,WAAW,WAA0B;AAr4CjD;AAs4CI,QAAI,CAAC,WAAW;AACd,aAAO;IACT;AAEA,YAAO,UAAK,kBAAL,mBAAqB;EAC9B;EAEU,cAAc,WAA2B;AA74CrD;AA84CI,QAAI,CAAC,WAAW;AACd;IACF;AAEA,UAAM,YAAY,UAAU;AAC5B,QAAI,CAAC,WAAW;AACd;IACF;AAEA,SAAK,cAAa;AAElB,UAAM,mBAAqC;AAC3C,qBAAiB,YAAY;AAC7B,qBAAiB,UAAU;MACzB;MACA,YAAW,UAAK,YAAL,mBAAc;MACzB,UAAU,KAAK,gBAAe;KAC/B;AAED,QAAI,CAAC,KAAK,gBAAgB,SAAS,SAAS,GAAG;AAC7C,WAAK,gBAAgB,KAAK,SAAS;IACrC;AAEA,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,SAAS,IAAI;IAClC;EACF;;EAkKO,MAAM,KAAyB,SAA2B;AAC/D,UAAM,KAAK,cAAa;AAExB,QAAI,mCAAS,KAAK;AAChB,2BAAqB,OAAO,QAAQ,GAAG;IACzC;AAEA,QAAI,mCAAS,WAAW;AACtB,cAAQ,mCAAS,MAAM;QACrB,KAAK;AACH,iBAAO,gBAAgB,KAAK,EAAE,GAAG,SAAS,MAAM,EAAE,MAAM,QAAQ,UAAS,EAAE,CAAE;QAC/E;MACF;IACF;AAEA,WAAO,gBAAgB,KAAK,OAAO;EACrC;EAEO,MAAM,QAAK;AAChB,UAAM,KAAK,cAAa;AACxB,oBAAgB,MAAK;EACvB;EAEO,WAAW,SAA0C,WAA0B;AACpF,oBAAgB,WAAW,SAAS,SAAS;EAC/C;EAEO,MAAM,WAAW,gBAA+B;AACrD,UAAM,qBAAqB,WAAW,cAAc;EACtD;EAEO,UAAO;AACZ,WAAO,kBAAkB,MAAM;EACjC;;EAGO,WAAQ;AACb,WAAO;EACT;EAEO,aAAU;AAlnDnB;AAmnDI,YAAO,qBAAgB,MAAM,sBAAtB,mBAAyC;EAClD;EAEO,MAAM,cAAc,eAA4B;AACrD,UAAM,UAAU,KAAK,gBAAe,EAAG,KAAK,CAAAT,OAAKA,GAAE,OAAO,cAAc,EAAE;AAC1E,QAAI,CAAC,SAAS;AACZ,sBAAgB,KAAK,UAAU,aAAa,0BAA0B,OAAO;AAE7E;IACF;AACA,UAAM,gBAAgB,oBAAoB,OAAO;EACnD;EAEO,oBAAiB;AACtB,WAAO,gBAAgB,MAAM,cACzB,aAAa,MAAM,UAAU,gBAAgB,MAAM,WAAW,IAC9D;EACN;EAEO,wBAAqB;AAC1B,WAAO,aAAa,cAAc,gBAAgB,MAAM,WAAW;EACrE;EAEO,mBAAmB,UAAkE;AAC1F,WAAO,aAAa,mBAAmB,QAAQ;EACjD;EAEO,eAAY;AACjB,WAAO,gBAAgB,MAAM;EAC/B;EAEO,oBAAiB;AACtB,WAAO,gBAAgB,MAAM;EAC/B;EAEO,aAAa,WAA4C;AAC9D,oBAAgB,aAAa,SAAS;AACtC,kBAAc,gBAAgB,MAAM,SAAS;EAC/C;EAEO,sBAAsB,oBAA0B;AACrD,sBAAkB,sBAAsB,kBAAkB;EAC5D;EAEO,oBAAoB,kBAAwB;AACjD,sBAAkB,oBAAoB,gBAAgB;EACxD;EAEO,kBAAkB,gBAAsD;AAC7E,oBAAgB,kBAAkB,cAAc;AAChD,sBAAkB,gBAAgB,MAAM,cAAc;EACxD;EAEO,eAAe,UAAkD;AACtE,WAAO,gBAAgB,UAAU,QAAQ;EAC3C;EAEO,gBAAa;AAClB,WAAO,kBAAkB,MAAM;EACjC;EAEO,WAAW,WAA0B;AAhrD9C;AAirDI,UAAM,gBAAgB,oBAAoB,iBAAiB,SAAS;AACpE,UAAM,eAAe,gBAAgB,eAAe,SAAS;AAC7D,UAAM,cAAc,gBAAgB,MAAM;AAC1C,UAAM,oBAAoB,YAAY,wBAAwB,aAAa,WAAW;AAEtF,QAAI,CAAC,cAAc;AACjB,aAAO;IACT;AAEA,WAAO;MACL,aAAa,aAAa;MAC1B,aAAa,aAAa;MAC1B,SAAS,eAAe,gBAAgB,aAAa,WAAW;MAChE,aAAa,QAAQ,aAAa,WAAW;MAC7C,QAAQ,aAAa;MACrB,oBACE,iBAAiB,sBAAsB,cAAc,aAAa,OAC9D;QACE,MAAM,aAAa,OACf;UACE,GAAG,aAAa;;;;;;;UAOhB,UAAU,YAAY,2BAA0B;YAElD;QACJ,cACE,aAAa,kBACZ;QACH,cAAa,kBAAa,0BAAb,mBAAqC,aAAa;QAC/D,wBAAwB,QAAQ,aAAa,oBAAoB;UAEnE;;EAEV;EAEO,iBACL,UACA,WAA0B;AAE1B,UAAM,YAAY,MAAK;AACrB,YAAM,UAAU,KAAK,WAAW,SAAS;AAEzC,UAAI,CAAC,SAAS;AACZ;MACF;AAEA,eAAS,OAAO;IAClB;AAEA,QAAI,WAAW;AACb,sBAAgB,mBAAmB,gBAAgB,WAAW,SAAS;IACzE,OAAO;AACL,sBAAgB,UAAU,SAAS;IACrC;AACA,wBAAoB,UAAU,SAAS;EACzC;EAEO,iBACL,UAA2E;AAE3E,WAAO,gBAAgB,UAAU,CAAC,EAAE,kBAAiB,MAAM;AACzD,eAAS;QACP,aAAa;QACb,SAAS,uDAAmB;QAC5B,eAAe,uDAAmB;OACnC;IACH,CAAC;EACH;EAEO,oBAAoB,UAAkD;AAC3E,WAAO,kBAAkB,aAAa,uBAAuB,QAAQ;EACvE;EAEO,+BAA+B,UAAqC;AACzE,sBAAkB,aAAa,yBAAyB,QAAQ;EAClE;EAEO,2BAA2B,UAA0C;AAC1E,oBAAgB,aAAa,qBAAqB,QAAQ;EAC5D;EAEO,WAAQ;AACb,WAAO,sBAAsB;EAC/B;EAEO,eAAe,UAAwD;AAC5E,WAAO,sBAAsB,UAAU,QAAQ;EACjD;EAEO,iBAAiB,SAAe;AACrC,oBAAgB,UAAU,OAAO;EACnC;EAEO,mBAAmB,SAAe;AACvC,oBAAgB,YAAY,OAAO;EACrC;EAEO,WAAQ;AACb,WAAO,EAAE,GAAG,iBAAiB,MAAK;EACpC;EAEO,gBAAgB,UAAmD;AACxE,WAAO,iBAAiB,UAAU,QAAQ;EAC5C;EAEO,QAAQ,OAAoC;AACjD,qBAAiB,QAAQ,KAAK;EAChC;EAEO,SAAS,OAAoC;AAClD,qBAAiB,KAAK,KAAK;EAC7B;EAEO,oBAAoB,QAAsC;AAC/D,qBAAiB,oBAAoB,MAAM;EAC7C;EAEO,SAAM;AACX,WAAO,gBAAgB,MAAM;EAC/B;EAEO,0BAAuB;AAC5B,WAAO,iBAAiB,MAAM,iBAAiB,WAAW;EAC5D;EAEO,OAAO,cAAW;AACvB,WAAO,KAAK;EACd;EAuFO,eAAe,aAA8B;AAClD,sBAAkB,YAAY,WAAW;EAC3C;EAEO,qBAAqB,mBAA0C;AACpE,sBAAkB,kBAAkB,iBAAiB;EACvD;EAEO,cAAc,YAA2C;AAC9D,UAAM,iBAAiB,kBAAkB,SAAS,CAAA;AAClD,UAAM,iBAAiB,EAAE,GAAG,gBAAgB,GAAG,WAAU;AACzD,sBAAkB,WAAW,cAAc;EAC7C;EAEO,uBAAuB,qBAAoC;AAChE,sBAAkB,uBAAuB,mBAAmB;EAC9D;EAEO,uBAAuB,qBAAoC;AAChE,sBAAkB,uBAAuB,mBAAmB;EAC9D;EAEO,mBAAmB,iBAAwB;AAChD,sBAAkB,mBAAmB,eAAe;EACtD;EAEO,gBAAgB,cAA8B;AACnD,sBAAkB,gBAAgB,YAAY;EAChD;EAEO,yBAAsB;AAC3B,WAAO,WAAW,sBAChB,kBAAkB,MAAM,UACxB,oBAAoB,cAAa,CAAE;EAEvC;;;;;;;EAQO,WAAW,WAA2B,SAAsB;AACjE,QAAI,KAAK,iBAAiB,CAAC,KAAK,cAAc,SAAS,GAAG;AACxD,YAAM,IAAI,MAAM,yBAAyB,SAAS,gBAAgB;IACpE;AAEA,UAAM,kBAAkB,KAAK,kBAAkB,SAAS,KAAK,OAAO;AAEpE,QAAI,CAAC,KAAK,gBAAe,EAAG,KAAK,CAAAA,OAAKA,GAAE,OAAO,gBAAgB,EAAE,GAAG;AAClE,sBAAgB,WAAW,eAAe;IAC5C;EACF;;;;;;;EAQO,cAAc,WAA2B,WAA0B;AACxE,QAAI,KAAK,iBAAiB,CAAC,KAAK,cAAc,SAAS,GAAG;AACxD,YAAM,IAAI,MAAM,yBAAyB,SAAS,gBAAgB;IACpE;AAEA,UAAM,kBAAkB,KAAK,gBAAe,EAAG,KAAK,CAAAA,OAAKA,GAAE,OAAO,SAAS;AAE3E,QAAI,CAAC,iBAAiB;AACpB;IACF;AAEA,oBAAgB,cAAc,WAAW,SAAS;EACpD;;;;ACx7DF,IAAI,gBAAgB;AAGd,IAAO,SAAP,cAAsB,iBAAgB;;EAU1B,MAAM,KAAyB,SAA2B;AAExE,UAAM,cAAc,oBAAoB,YAAW;AAEnD,QAAI,CAAC,aAAa;AAChB,YAAM,MAAM,KAAK,OAAO;IAC1B;EACF;EAEgB,MAAM,QAAK;AACzB,UAAM,MAAM,MAAK;AAEjB,QAAI,KAAK,QAAQ,iBAAiB;AAChC,2BAAqB,qBAAoB;IAC3C;EACF;EAEgB,MAAM,aACpB,UAEC;AAED,WAAO,QAAQ,QAAO;EACxB;EAEgB,MAAM,YAAY,SAIjC;AACC,WAAO,QAAQ,QAAO;EACxB;EAEmB,MAAM,gBAAa;AACpC,QAAI,CAAC,iBAAiB,eAAe,SAAQ,GAAI;AAC/C,YAAM,OAAO,qBAAiC;AAC9C,YAAM,OAAO,yBAAqC;AAElD,YAAM,mBAAmB,SAAS,cAAc,WAAW;AAC3D,UAAI,CAAC,kBAAkB;AACrB,cAAM,QAAQ,SAAS,cAAc,WAAW;AAChD,YAAI,CAAC,kBAAkB,MAAM,iBAAiB,CAAC,kBAAkB,MAAM,gBAAgB;AACrF,mBAAS,KAAK,sBAAsB,aAAa,KAAK;QACxD;MACF;AACA,sBAAgB;IAClB;EACF;;;;AC1FK,IAAM,kBAAkB;;;ACUzB,SAAU,aAAa,SAAqB;AAChD,SAAO,IAAI,OAAO;IAChB,GAAG;IACH,OAAO;IACP,YAAY,aAAa,eAAe;GACzC;AACH;", "names": ["toHex", "secp256k1", "r", "v", "yParityOrV", "recoveryBit", "i", "j", "k", "f", "x", "y", "z", "h2", "h3", "ar", "br", "cr", "dr", "er", "rr", "sr", "tr", "BaseError", "x", "fn", "size", "pad", "size", "i", "assertSize", "size", "SizeOverflowError", "pad", "size", "SizeExceedsPaddingSizeError", "fromHex", "fromHex", "size", "assertSize", "j", "BaseError", "fromString", "size", "padRight", "padRight", "size", "pad", "size", "BaseError", "BaseError", "size", "encoder", "i", "concat", "x", "assertSize", "fromBytes", "i", "size", "fromString", "encoder", "size", "pad", "size", "BaseError", "size", "SizeOverflowError", "BaseError", "SizeExceedsPaddingSizeError", "BaseError", "size", "keccak256", "fromBytes", "LruMap", "size", "LruMap", "InvalidAddressError", "checksum", "keccak256", "fromString", "i", "InvalidAddressError", "BaseError", "arrayRegex", "bytesRegex", "integerRegex", "maxUint256", "PositionOutOfBoundsError", "size", "BaseError", "PositionOutOfBoundsError", "encodePacked", "i", "concat", "encode", "fromString", "integerRegex", "size", "bytesRegex", "BytesSizeMismatchError", "arrayRegex", "BytesSizeMismatchError", "BaseError", "size", "BaseError", "ns", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ns", "c", "_c", "_e", "Constants<PERSON><PERSON>", "b", "_a", "_b", "c", "Constants<PERSON><PERSON>", "f", "_c", "c", "n", "Constants<PERSON><PERSON>", "_e", "ns", "f", "b", "_a", "_i", "B", "message"]}