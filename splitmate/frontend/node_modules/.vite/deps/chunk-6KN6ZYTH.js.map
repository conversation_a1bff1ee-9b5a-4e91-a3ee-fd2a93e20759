{"version": 3, "sources": ["../../@wagmi/core/node_modules/eventemitter3/index.js", "../../@wagmi/core/src/version.ts", "../../@wagmi/core/src/utils/getVersion.ts", "../../@wagmi/core/src/errors/base.ts", "../../@wagmi/core/src/errors/config.ts", "../../@wagmi/core/src/utils/deepEqual.ts", "../../@wagmi/core/src/errors/connector.ts", "../../@wagmi/core/src/connectors/createConnector.ts", "../../@wagmi/core/src/connectors/injected.ts", "../../@wagmi/core/src/connectors/mock.ts", "../../@wagmi/core/src/utils/deserialize.ts", "../../@wagmi/core/src/utils/serialize.ts", "../../@wagmi/core/src/createStorage.ts", "../../mipd/src/utils.ts", "../../mipd/src/store.ts", "../../zustand/esm/middleware.mjs", "../../zustand/esm/vanilla.mjs", "../../@wagmi/core/node_modules/eventemitter3/index.mjs", "../../@wagmi/core/src/createEmitter.ts", "../../@wagmi/core/src/utils/uid.ts", "../../@wagmi/core/src/createConfig.ts", "../../@wagmi/core/src/transports/connector.ts", "../../@wagmi/core/src/transports/fallback.ts", "../../@wagmi/core/src/utils/cookie.ts", "../../@wagmi/core/src/utils/normalizeChainId.ts", "../../@wagmi/core/src/utils/getAction.ts", "../../@wagmi/core/src/actions/call.ts", "../../@wagmi/core/src/actions/connect.ts", "../../@wagmi/core/src/actions/getConnectorClient.ts", "../../@wagmi/core/src/actions/deployContract.ts", "../../@wagmi/core/src/actions/disconnect.ts", "../../@wagmi/core/src/actions/estimateGas.ts", "../../@wagmi/core/src/utils/getUnit.ts", "../../@wagmi/core/src/actions/estimateFeesPerGas.ts", "../../@wagmi/core/src/actions/estimateMaxPriorityFeePerGas.ts", "../../@wagmi/core/src/actions/getAccount.ts", "../../@wagmi/core/src/actions/multicall.ts", "../../@wagmi/core/src/actions/readContract.ts", "../../@wagmi/core/src/actions/readContracts.ts", "../../@wagmi/core/src/actions/getBalance.ts", "../../@wagmi/core/src/actions/getBlock.ts", "../../@wagmi/core/src/actions/getBlockNumber.ts", "../../@wagmi/core/src/actions/getBlockTransactionCount.ts", "../../@wagmi/core/src/actions/getBytecode.ts", "../../@wagmi/core/src/actions/getCallsStatus.ts", "../../@wagmi/core/src/actions/getCapabilities.ts", "../../@wagmi/core/src/actions/getChainId.ts", "../../@wagmi/core/src/actions/getChains.ts", "../../@wagmi/core/src/actions/getClient.ts", "../../@wagmi/core/src/actions/getConnections.ts", "../../@wagmi/core/src/actions/getConnectors.ts", "../../@wagmi/core/src/actions/getEnsAddress.ts", "../../@wagmi/core/src/actions/getEnsAvatar.ts", "../../@wagmi/core/src/actions/getEnsName.ts", "../../@wagmi/core/src/actions/getEnsResolver.ts", "../../@wagmi/core/src/actions/getEnsText.ts", "../../@wagmi/core/src/actions/getFeeHistory.ts", "../../@wagmi/core/src/actions/getGasPrice.ts", "../../@wagmi/core/src/actions/getProof.ts", "../../@wagmi/core/src/actions/getPublicClient.ts", "../../@wagmi/core/src/actions/getStorageAt.ts", "../../@wagmi/core/src/actions/getToken.ts", "../../@wagmi/core/src/actions/getTransaction.ts", "../../@wagmi/core/src/actions/getTransactionConfirmations.ts", "../../@wagmi/core/src/actions/getTransactionCount.ts", "../../@wagmi/core/src/actions/getTransactionReceipt.ts", "../../@wagmi/core/src/actions/getWalletClient.ts", "../../@wagmi/core/src/actions/prepareTransactionRequest.ts", "../../@wagmi/core/src/actions/reconnect.ts", "../../@wagmi/core/src/actions/sendCalls.ts", "../../@wagmi/core/src/actions/sendTransaction.ts", "../../@wagmi/core/src/actions/showCallsStatus.ts", "../../@wagmi/core/src/actions/signMessage.ts", "../../@wagmi/core/src/actions/signTypedData.ts", "../../@wagmi/core/src/actions/simulateContract.ts", "../../@wagmi/core/src/actions/switchAccount.ts", "../../@wagmi/core/src/actions/switchChain.ts", "../../@wagmi/core/src/actions/verifyMessage.ts", "../../@wagmi/core/src/actions/verifyTypedData.ts", "../../@wagmi/core/src/actions/waitForCallsStatus.ts", "../../@wagmi/core/src/actions/watchAccount.ts", "../../@wagmi/core/src/actions/watchAsset.ts", "../../@wagmi/core/src/actions/watchBlocks.ts", "../../@wagmi/core/src/actions/watchBlockNumber.ts", "../../@wagmi/core/src/actions/watchChainId.ts", "../../@wagmi/core/src/actions/watchClient.ts", "../../@wagmi/core/src/actions/watchConnections.ts", "../../@wagmi/core/src/actions/watchConnectors.ts", "../../@wagmi/core/src/actions/watchContractEvent.ts", "../../@wagmi/core/src/actions/watchPendingTransactions.ts", "../../@wagmi/core/src/actions/watchPublicClient.ts", "../../@wagmi/core/src/actions/waitForTransactionReceipt.ts", "../../@wagmi/core/src/actions/writeContract.ts", "../../@wagmi/core/src/hydrate.ts", "../../@wagmi/core/src/utils/extractRpcUrls.ts"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n", "export const version = '2.17.3'\n", "import { version } from '../version.js'\n\nexport const getVersion = () => `@wagmi/core@${version}`\n", "import type { Compute, OneOf } from '../types/utils.js'\nimport { getVersion } from '../utils/getVersion.js'\n\nexport type ErrorType<name extends string = 'Error'> = Error & { name: name }\n\ntype BaseErrorOptions = Compute<\n  OneOf<{ details?: string | undefined } | { cause: BaseError | Error }> & {\n    docsPath?: string | undefined\n    docsSlug?: string | undefined\n    metaMessages?: string[] | undefined\n  }\n>\n\nexport type BaseErrorType = BaseError & { name: 'WagmiCoreError' }\nexport class BaseError extends Error {\n  details: string\n  docsPath?: string | undefined\n  metaMessages?: string[] | undefined\n  shortMessage: string\n\n  override name = 'WagmiCoreError'\n  get docsBaseUrl() {\n    return 'https://wagmi.sh/core'\n  }\n  get version() {\n    return getVersion()\n  }\n\n  constructor(shortMessage: string, options: BaseErrorOptions = {}) {\n    super()\n\n    const details =\n      options.cause instanceof BaseError\n        ? options.cause.details\n        : options.cause?.message\n          ? options.cause.message\n          : options.details!\n    const docsPath =\n      options.cause instanceof BaseError\n        ? options.cause.docsPath || options.docsPath\n        : options.docsPath\n\n    this.message = [\n      shortMessage || 'An error occurred.',\n      '',\n      ...(options.metaMessages ? [...options.metaMessages, ''] : []),\n      ...(docsPath\n        ? [\n            `Docs: ${this.docsBaseUrl}${docsPath}.html${\n              options.docsSlug ? `#${options.docsSlug}` : ''\n            }`,\n          ]\n        : []),\n      ...(details ? [`Details: ${details}`] : []),\n      `Version: ${this.version}`,\n    ].join('\\n')\n\n    if (options.cause) this.cause = options.cause\n    this.details = details\n    this.docsPath = docsPath\n    this.metaMessages = options.metaMessages\n    this.shortMessage = shortMessage\n  }\n\n  walk(fn?: (err: unknown) => boolean) {\n    return this.#walk(this, fn)\n  }\n\n  #walk(err: unknown, fn?: (err: unknown) => boolean): unknown {\n    if (fn?.(err)) return err\n    if ((err as Error).cause) return this.#walk((err as Error).cause, fn)\n    return err\n  }\n}\n", "import type { Address } from 'viem'\n\nimport type { Connector } from '../createConfig.js'\nimport { BaseError } from './base.js'\n\nexport type ChainNotConfiguredErrorType = ChainNotConfiguredError & {\n  name: 'ChainNotConfiguredError'\n}\nexport class ChainNotConfiguredError extends BaseError {\n  override name = 'ChainNotConfiguredError'\n  constructor() {\n    super('Chain not configured.')\n  }\n}\n\nexport type ConnectorAlreadyConnectedErrorType =\n  ConnectorAlreadyConnectedError & {\n    name: 'ConnectorAlreadyConnectedError'\n  }\nexport class ConnectorAlreadyConnectedError extends BaseError {\n  override name = 'ConnectorAlreadyConnectedError'\n  constructor() {\n    super('Connector already connected.')\n  }\n}\n\nexport type ConnectorNotConnectedErrorType = ConnectorNotConnectedError & {\n  name: 'ConnectorNotConnectedError'\n}\nexport class ConnectorNotConnectedError extends BaseError {\n  override name = 'ConnectorNotConnectedError'\n  constructor() {\n    super('Connector not connected.')\n  }\n}\n\nexport type ConnectorNotFoundErrorType = ConnectorNotFoundError & {\n  name: 'ConnectorNotFoundError'\n}\nexport class ConnectorNotFoundError extends BaseError {\n  override name = 'ConnectorNotFoundError'\n  constructor() {\n    super('Connector not found.')\n  }\n}\n\nexport type ConnectorAccountNotFoundErrorType =\n  ConnectorAccountNotFoundError & {\n    name: 'ConnectorAccountNotFoundError'\n  }\nexport class ConnectorAccountNotFoundError extends BaseError {\n  override name = 'ConnectorAccountNotFoundError'\n  constructor({\n    address,\n    connector,\n  }: {\n    address: Address\n    connector: Connector\n  }) {\n    super(`Account \"${address}\" not found for connector \"${connector.name}\".`)\n  }\n}\n\nexport type ConnectorChainMismatchErrorType = ConnectorAccountNotFoundError & {\n  name: 'ConnectorChainMismatchError'\n}\nexport class ConnectorChainMismatchError extends BaseError {\n  override name = 'ConnectorChainMismatchError'\n  constructor({\n    connectionChainId,\n    connectorChainId,\n  }: {\n    connectionChainId: number\n    connectorChainId: number\n  }) {\n    super(\n      `The current chain of the connector (id: ${connectorChainId}) does not match the connection's chain (id: ${connectionChainId}).`,\n      {\n        metaMessages: [\n          `Current Chain ID:  ${connectorChainId}`,\n          `Expected Chain ID: ${connectionChainId}`,\n        ],\n      },\n    )\n  }\n}\n\nexport type ConnectorUnavailableReconnectingErrorType =\n  ConnectorUnavailableReconnectingError & {\n    name: 'ConnectorUnavailableReconnectingError'\n  }\nexport class ConnectorUnavailableReconnectingError extends BaseError {\n  override name = 'ConnectorUnavailableReconnectingError'\n  constructor({ connector }: { connector: { name: string } }) {\n    super(`Connector \"${connector.name}\" unavailable while reconnecting.`, {\n      details: [\n        'During the reconnection step, the only connector methods guaranteed to be available are: `id`, `name`, `type`, `uid`.',\n        'All other methods are not guaranteed to be available until reconnection completes and connectors are fully restored.',\n        'This error commonly occurs for connectors that asynchronously inject after reconnection has already started.',\n      ].join(' '),\n    })\n  }\n}\n", "/** Forked from https://github.com/epoberezkin/fast-deep-equal */\n\nexport function deepEqual(a: any, b: any) {\n  if (a === b) return true\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    if (a.constructor !== b.constructor) return false\n\n    let length: number\n    let i: number\n\n    if (Array.isArray(a) && Array.isArray(b)) {\n      length = a.length\n      if (length !== b.length) return false\n      for (i = length; i-- !== 0; ) if (!deepEqual(a[i], b[i])) return false\n      return true\n    }\n\n    if (a.valueOf !== Object.prototype.valueOf)\n      return a.valueOf() === b.valueOf()\n    if (a.toString !== Object.prototype.toString)\n      return a.toString() === b.toString()\n\n    const keys = Object.keys(a)\n    length = keys.length\n    if (length !== Object.keys(b).length) return false\n\n    for (i = length; i-- !== 0; )\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i]!)) return false\n\n    for (i = length; i-- !== 0; ) {\n      const key = keys[i]\n\n      if (key && !deepEqual(a[key], b[key])) return false\n    }\n\n    return true\n  }\n\n  // true if both NaN, false otherwise\n  // biome-ignore lint/suspicious/noSelfCompare: <explanation>\n  return a !== a && b !== b\n}\n", "import type { Connector } from '../createConfig.js'\nimport { BaseError } from './base.js'\n\nexport type ProviderNotFoundErrorType = ProviderNotFoundError & {\n  name: 'ProviderNotFoundError'\n}\nexport class ProviderNotFoundError extends BaseError {\n  override name = 'ProviderNotFoundError'\n  constructor() {\n    super('Provider not found.')\n  }\n}\n\nexport type SwitchChainNotSupportedErrorType = SwitchChainNotSupportedError & {\n  name: 'SwitchChainNotSupportedError'\n}\nexport class SwitchChainNotSupportedError extends BaseError {\n  override name = 'SwitchChainNotSupportedError'\n\n  constructor({ connector }: { connector: Connector }) {\n    super(`\"${connector.name}\" does not support programmatic chain switching.`)\n  }\n}\n", "import type {\n  AddEthereumChainParameter,\n  Address,\n  Chain,\n  Client,\n  ProviderConnectInfo,\n  ProviderMessage,\n} from 'viem'\n\nimport type { Transport } from '../createConfig.js'\nimport type { Emitter } from '../createEmitter.js'\nimport type { Storage } from '../createStorage.js'\nimport type { Compute, ExactPartial, StrictOmit } from '../types/utils.js'\n\nexport type ConnectorEventMap = {\n  change: {\n    accounts?: readonly Address[] | undefined\n    chainId?: number | undefined\n  }\n  connect: { accounts: readonly Address[]; chainId: number }\n  disconnect: never\n  error: { error: Error }\n  message: { type: string; data?: unknown | undefined }\n}\n\nexport type CreateConnectorFn<\n  provider = unknown,\n  properties extends Record<string, unknown> = Record<string, unknown>,\n  storageItem extends Record<string, unknown> = Record<string, unknown>,\n> = (config: {\n  chains: readonly [Chain, ...Chain[]]\n  emitter: Emitter<ConnectorEventMap>\n  storage?: Compute<Storage<storageItem>> | null | undefined\n  transports?: Record<number, Transport> | undefined\n}) => Compute<\n  {\n    readonly icon?: string | undefined\n    readonly id: string\n    readonly name: string\n    readonly rdns?: string | readonly string[] | undefined\n    /** @deprecated */\n    readonly supportsSimulation?: boolean | undefined\n    readonly type: string\n\n    setup?(): Promise<void>\n    connect(\n      parameters?:\n        | { chainId?: number | undefined; isReconnecting?: boolean | undefined }\n        | undefined,\n    ): Promise<{\n      accounts: readonly Address[]\n      chainId: number\n    }>\n    disconnect(): Promise<void>\n    getAccounts(): Promise<readonly Address[]>\n    getChainId(): Promise<number>\n    getProvider(\n      parameters?: { chainId?: number | undefined } | undefined,\n    ): Promise<provider>\n    getClient?(\n      parameters?: { chainId?: number | undefined } | undefined,\n    ): Promise<Client>\n    isAuthorized(): Promise<boolean>\n    switchChain?(\n      parameters: Compute<{\n        addEthereumChainParameter?:\n          | ExactPartial<StrictOmit<AddEthereumChainParameter, 'chainId'>>\n          | undefined\n        chainId: number\n      }>,\n    ): Promise<Chain>\n\n    onAccountsChanged(accounts: string[]): void\n    onChainChanged(chainId: string): void\n    onConnect?(connectInfo: ProviderConnectInfo): void\n    onDisconnect(error?: Error | undefined): void\n    onMessage?(message: ProviderMessage): void\n  } & properties\n>\n\nexport function createConnector<\n  provider,\n  properties extends Record<string, unknown> = Record<string, unknown>,\n  storageItem extends Record<string, unknown> = Record<string, unknown>,\n  ///\n  createConnectorFn extends CreateConnectorFn<\n    provider,\n    properties,\n    storageItem\n  > = CreateConnectorFn<provider, properties, storageItem>,\n>(createConnectorFn: createConnectorFn) {\n  return createConnectorFn\n}\n", "import {\n  type AddEthereumChainParameter,\n  type Address,\n  type EIP1193<PERSON>rovider,\n  type ProviderConnectInfo,\n  type ProviderRpcError,\n  ResourceUnavailableRpcError,\n  type RpcError,\n  SwitchChainError,\n  UserRejectedRequestError,\n  getAddress,\n  numberToHex,\n  withRetry,\n  withTimeout,\n} from 'viem'\n\nimport type { Connector } from '../createConfig.js'\nimport { ChainNotConfiguredError } from '../errors/config.js'\nimport { ProviderNotFoundError } from '../errors/connector.js'\nimport type { Compute } from '../types/utils.js'\nimport { createConnector } from './createConnector.js'\n\nexport type InjectedParameters = {\n  /**\n   * Some injected providers do not support programmatic disconnect.\n   * This flag simulates the disconnect behavior by keeping track of connection status in storage.\n   * @default true\n   */\n  shimDisconnect?: boolean | undefined\n  /**\n   * [EIP-1193](https://eips.ethereum.org/EIPS/eip-1193) Ethereum Provider to target\n   */\n  target?: TargetId | Target | (() => Target | undefined) | undefined\n  unstable_shimAsyncInject?: boolean | number | undefined\n}\n\ninjected.type = 'injected' as const\nexport function injected(parameters: InjectedParameters = {}) {\n  const { shimDisconnect = true, unstable_shimAsyncInject } = parameters\n\n  function getTarget(): Compute<Target & { id: string }> {\n    const target = parameters.target\n    if (typeof target === 'function') {\n      const result = target()\n      if (result) return result\n    }\n\n    if (typeof target === 'object') return target\n\n    if (typeof target === 'string')\n      return {\n        ...(targetMap[target as keyof typeof targetMap] ?? {\n          id: target,\n          name: `${target[0]!.toUpperCase()}${target.slice(1)}`,\n          provider: `is${target[0]!.toUpperCase()}${target.slice(1)}`,\n        }),\n      }\n\n    return {\n      id: 'injected',\n      name: 'Injected',\n      provider(window) {\n        return window?.ethereum\n      },\n    }\n  }\n\n  type Provider = WalletProvider | undefined\n  type Properties = {\n    onConnect(connectInfo: ProviderConnectInfo): void\n  }\n  type StorageItem = {\n    [_ in 'injected.connected' | `${string}.disconnected`]: true\n  }\n\n  let accountsChanged: Connector['onAccountsChanged'] | undefined\n  let chainChanged: Connector['onChainChanged'] | undefined\n  let connect: Connector['onConnect'] | undefined\n  let disconnect: Connector['onDisconnect'] | undefined\n\n  return createConnector<Provider, Properties, StorageItem>((config) => ({\n    get icon() {\n      return getTarget().icon\n    },\n    get id() {\n      return getTarget().id\n    },\n    get name() {\n      return getTarget().name\n    },\n    /** @deprecated */\n    get supportsSimulation() {\n      return true\n    },\n    type: injected.type,\n    async setup() {\n      const provider = await this.getProvider()\n      // Only start listening for events if `target` is set, otherwise `injected()` will also receive events\n      if (provider?.on && parameters.target) {\n        if (!connect) {\n          connect = this.onConnect.bind(this)\n          provider.on('connect', connect)\n        }\n\n        // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n        // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged)\n        }\n      }\n    },\n    async connect({ chainId, isReconnecting } = {}) {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n\n      let accounts: readonly Address[] = []\n      if (isReconnecting) accounts = await this.getAccounts().catch(() => [])\n      else if (shimDisconnect) {\n        // Attempt to show another prompt for selecting account if `shimDisconnect` flag is enabled\n        try {\n          const permissions = await provider.request({\n            method: 'wallet_requestPermissions',\n            params: [{ eth_accounts: {} }],\n          })\n          accounts = (permissions[0]?.caveats?.[0]?.value as string[])?.map(\n            (x) => getAddress(x),\n          )\n          // `'wallet_requestPermissions'` can return a different order of accounts than `'eth_accounts'`\n          // switch to `'eth_accounts'` ordering if more than one account is connected\n          // https://github.com/wevm/wagmi/issues/4140\n          if (accounts.length > 0) {\n            const sortedAccounts = await this.getAccounts()\n            accounts = sortedAccounts\n          }\n        } catch (err) {\n          const error = err as RpcError\n          // Not all injected providers support `wallet_requestPermissions` (e.g. MetaMask iOS).\n          // Only bubble up error if user rejects request\n          if (error.code === UserRejectedRequestError.code)\n            throw new UserRejectedRequestError(error)\n          // Or prompt is already open\n          if (error.code === ResourceUnavailableRpcError.code) throw error\n        }\n      }\n\n      try {\n        if (!accounts?.length && !isReconnecting) {\n          const requestedAccounts = await provider.request({\n            method: 'eth_requestAccounts',\n          })\n          accounts = requestedAccounts.map((x) => getAddress(x))\n        }\n\n        // Manage EIP-1193 event listeners\n        // https://eips.ethereum.org/EIPS/eip-1193#events\n        if (connect) {\n          provider.removeListener('connect', connect)\n          connect = undefined\n        }\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged)\n        }\n        if (!chainChanged) {\n          chainChanged = this.onChainChanged.bind(this)\n          provider.on('chainChanged', chainChanged)\n        }\n        if (!disconnect) {\n          disconnect = this.onDisconnect.bind(this)\n          provider.on('disconnect', disconnect)\n        }\n\n        // Switch to chain if provided\n        let currentChainId = await this.getChainId()\n        if (chainId && currentChainId !== chainId) {\n          const chain = await this.switchChain!({ chainId }).catch((error) => {\n            if (error.code === UserRejectedRequestError.code) throw error\n            return { id: currentChainId }\n          })\n          currentChainId = chain?.id ?? currentChainId\n        }\n\n        // Remove disconnected shim if it exists\n        if (shimDisconnect)\n          await config.storage?.removeItem(`${this.id}.disconnected`)\n\n        // Add connected shim if no target exists\n        if (!parameters.target)\n          await config.storage?.setItem('injected.connected', true)\n\n        return { accounts, chainId: currentChainId }\n      } catch (err) {\n        const error = err as RpcError\n        if (error.code === UserRejectedRequestError.code)\n          throw new UserRejectedRequestError(error)\n        if (error.code === ResourceUnavailableRpcError.code)\n          throw new ResourceUnavailableRpcError(error)\n        throw error\n      }\n    },\n    async disconnect() {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n\n      // Manage EIP-1193 event listeners\n      if (chainChanged) {\n        provider.removeListener('chainChanged', chainChanged)\n        chainChanged = undefined\n      }\n      if (disconnect) {\n        provider.removeListener('disconnect', disconnect)\n        disconnect = undefined\n      }\n      if (!connect) {\n        connect = this.onConnect.bind(this)\n        provider.on('connect', connect)\n      }\n\n      // Experimental support for MetaMask disconnect\n      // https://github.com/MetaMask/metamask-improvement-proposals/blob/main/MIPs/mip-2.md\n      try {\n        // Adding timeout as not all wallets support this method and can hang\n        // https://github.com/wevm/wagmi/issues/4064\n        await withTimeout(\n          () =>\n            // TODO: Remove explicit type for viem@3\n            provider.request<{\n              Method: 'wallet_revokePermissions'\n              Parameters: [permissions: { eth_accounts: Record<string, any> }]\n              ReturnType: null\n            }>({\n              // `'wallet_revokePermissions'` added in `viem@2.10.3`\n              method: 'wallet_revokePermissions',\n              params: [{ eth_accounts: {} }],\n            }),\n          { timeout: 100 },\n        )\n      } catch {}\n\n      // Add shim signalling connector is disconnected\n      if (shimDisconnect) {\n        await config.storage?.setItem(`${this.id}.disconnected`, true)\n      }\n\n      if (!parameters.target)\n        await config.storage?.removeItem('injected.connected')\n    },\n    async getAccounts() {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n      const accounts = await provider.request({ method: 'eth_accounts' })\n      return accounts.map((x) => getAddress(x))\n    },\n    async getChainId() {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n      const hexChainId = await provider.request({ method: 'eth_chainId' })\n      return Number(hexChainId)\n    },\n    async getProvider() {\n      if (typeof window === 'undefined') return undefined\n\n      let provider: Provider\n      const target = getTarget()\n      if (typeof target.provider === 'function')\n        provider = target.provider(window as Window | undefined)\n      else if (typeof target.provider === 'string')\n        provider = findProvider(window, target.provider)\n      else provider = target.provider\n\n      // Some wallets do not conform to EIP-1193 (e.g. Trust Wallet)\n      // https://github.com/wevm/wagmi/issues/3526#issuecomment-**********\n      if (provider && !provider.removeListener) {\n        // Try using `off` handler if it exists, otherwise noop\n        if ('off' in provider && typeof provider.off === 'function')\n          provider.removeListener =\n            provider.off as typeof provider.removeListener\n        else provider.removeListener = () => {}\n      }\n\n      return provider\n    },\n    async isAuthorized() {\n      try {\n        const isDisconnected =\n          shimDisconnect &&\n          // If shim exists in storage, connector is disconnected\n          (await config.storage?.getItem(`${this.id}.disconnected`))\n        if (isDisconnected) return false\n\n        // Don't allow injected connector to connect if no target is set and it hasn't already connected\n        // (e.g. flag in storage is not set). This prevents a targetless injected connector from connecting\n        // automatically whenever there is a targeted connector configured.\n        if (!parameters.target) {\n          const connected = await config.storage?.getItem('injected.connected')\n          if (!connected) return false\n        }\n\n        const provider = await this.getProvider()\n        if (!provider) {\n          if (\n            unstable_shimAsyncInject !== undefined &&\n            unstable_shimAsyncInject !== false\n          ) {\n            // If no provider is found, check for async injection\n            // https://github.com/wevm/references/issues/167\n            // https://github.com/MetaMask/detect-provider\n            const handleEthereum = async () => {\n              if (typeof window !== 'undefined')\n                window.removeEventListener(\n                  'ethereum#initialized',\n                  handleEthereum,\n                )\n              const provider = await this.getProvider()\n              return !!provider\n            }\n            const timeout =\n              typeof unstable_shimAsyncInject === 'number'\n                ? unstable_shimAsyncInject\n                : 1_000\n            const res = await Promise.race([\n              ...(typeof window !== 'undefined'\n                ? [\n                    new Promise<boolean>((resolve) =>\n                      window.addEventListener(\n                        'ethereum#initialized',\n                        () => resolve(handleEthereum()),\n                        { once: true },\n                      ),\n                    ),\n                  ]\n                : []),\n              new Promise<boolean>((resolve) =>\n                setTimeout(() => resolve(handleEthereum()), timeout),\n              ),\n            ])\n            if (res) return true\n          }\n\n          throw new ProviderNotFoundError()\n        }\n\n        // Use retry strategy as some injected wallets (e.g. MetaMask) fail to\n        // immediately resolve JSON-RPC requests on page load.\n        const accounts = await withRetry(() => this.getAccounts())\n        return !!accounts.length\n      } catch {\n        return false\n      }\n    },\n    async switchChain({ addEthereumChainParameter, chainId }) {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n\n      const chain = config.chains.find((x) => x.id === chainId)\n      if (!chain) throw new SwitchChainError(new ChainNotConfiguredError())\n\n      const promise = new Promise<void>((resolve) => {\n        const listener = ((data) => {\n          if ('chainId' in data && data.chainId === chainId) {\n            config.emitter.off('change', listener)\n            resolve()\n          }\n        }) satisfies Parameters<typeof config.emitter.on>[1]\n        config.emitter.on('change', listener)\n      })\n\n      try {\n        await Promise.all([\n          provider\n            .request({\n              method: 'wallet_switchEthereumChain',\n              params: [{ chainId: numberToHex(chainId) }],\n            })\n            // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n            // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n            // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n            // this callback or an externally emitted `'chainChanged'` event.\n            // https://github.com/MetaMask/metamask-extension/issues/24247\n            .then(async () => {\n              const currentChainId = await this.getChainId()\n              if (currentChainId === chainId)\n                config.emitter.emit('change', { chainId })\n            }),\n          promise,\n        ])\n        return chain\n      } catch (err) {\n        const error = err as RpcError\n\n        // Indicates chain is not added to provider\n        if (\n          error.code === 4902 ||\n          // Unwrapping for MetaMask Mobile\n          // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n          (error as ProviderRpcError<{ originalError?: { code: number } }>)\n            ?.data?.originalError?.code === 4902\n        ) {\n          try {\n            const { default: blockExplorer, ...blockExplorers } =\n              chain.blockExplorers ?? {}\n            let blockExplorerUrls: string[] | undefined\n            if (addEthereumChainParameter?.blockExplorerUrls)\n              blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls\n            else if (blockExplorer)\n              blockExplorerUrls = [\n                blockExplorer.url,\n                ...Object.values(blockExplorers).map((x) => x.url),\n              ]\n\n            let rpcUrls: readonly string[]\n            if (addEthereumChainParameter?.rpcUrls?.length)\n              rpcUrls = addEthereumChainParameter.rpcUrls\n            else rpcUrls = [chain.rpcUrls.default?.http[0] ?? '']\n\n            const addEthereumChain = {\n              blockExplorerUrls,\n              chainId: numberToHex(chainId),\n              chainName: addEthereumChainParameter?.chainName ?? chain.name,\n              iconUrls: addEthereumChainParameter?.iconUrls,\n              nativeCurrency:\n                addEthereumChainParameter?.nativeCurrency ??\n                chain.nativeCurrency,\n              rpcUrls,\n            } satisfies AddEthereumChainParameter\n\n            await Promise.all([\n              provider\n                .request({\n                  method: 'wallet_addEthereumChain',\n                  params: [addEthereumChain],\n                })\n                .then(async () => {\n                  const currentChainId = await this.getChainId()\n                  if (currentChainId === chainId)\n                    config.emitter.emit('change', { chainId })\n                  else\n                    throw new UserRejectedRequestError(\n                      new Error('User rejected switch after adding network.'),\n                    )\n                }),\n              promise,\n            ])\n\n            return chain\n          } catch (error) {\n            throw new UserRejectedRequestError(error as Error)\n          }\n        }\n\n        if (error.code === UserRejectedRequestError.code)\n          throw new UserRejectedRequestError(error)\n        throw new SwitchChainError(error)\n      }\n    },\n    async onAccountsChanged(accounts) {\n      // Disconnect if there are no accounts\n      if (accounts.length === 0) this.onDisconnect()\n      // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n      else if (config.emitter.listenerCount('connect')) {\n        const chainId = (await this.getChainId()).toString()\n        this.onConnect({ chainId })\n        // Remove disconnected shim if it exists\n        if (shimDisconnect)\n          await config.storage?.removeItem(`${this.id}.disconnected`)\n      }\n      // Regular change event\n      else\n        config.emitter.emit('change', {\n          accounts: accounts.map((x) => getAddress(x)),\n        })\n    },\n    onChainChanged(chain) {\n      const chainId = Number(chain)\n      config.emitter.emit('change', { chainId })\n    },\n    async onConnect(connectInfo) {\n      const accounts = await this.getAccounts()\n      if (accounts.length === 0) return\n\n      const chainId = Number(connectInfo.chainId)\n      config.emitter.emit('connect', { accounts, chainId })\n\n      // Manage EIP-1193 event listeners\n      const provider = await this.getProvider()\n      if (provider) {\n        if (connect) {\n          provider.removeListener('connect', connect)\n          connect = undefined\n        }\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged)\n        }\n        if (!chainChanged) {\n          chainChanged = this.onChainChanged.bind(this)\n          provider.on('chainChanged', chainChanged)\n        }\n        if (!disconnect) {\n          disconnect = this.onDisconnect.bind(this)\n          provider.on('disconnect', disconnect)\n        }\n      }\n    },\n    async onDisconnect(error) {\n      const provider = await this.getProvider()\n\n      // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n      // https://github.com/MetaMask/providers/pull/120\n      if (error && (error as RpcError<1013>).code === 1013) {\n        if (provider && !!(await this.getAccounts()).length) return\n      }\n\n      // No need to remove `${this.id}.disconnected` from storage because `onDisconnect` is typically\n      // only called when the wallet is disconnected through the wallet's interface, meaning the wallet\n      // actually disconnected and we don't need to simulate it.\n      config.emitter.emit('disconnect')\n\n      // Manage EIP-1193 event listeners\n      if (provider) {\n        if (chainChanged) {\n          provider.removeListener('chainChanged', chainChanged)\n          chainChanged = undefined\n        }\n        if (disconnect) {\n          provider.removeListener('disconnect', disconnect)\n          disconnect = undefined\n        }\n        if (!connect) {\n          connect = this.onConnect.bind(this)\n          provider.on('connect', connect)\n        }\n      }\n    },\n  }))\n}\n\nconst targetMap = {\n  coinbaseWallet: {\n    id: 'coinbaseWallet',\n    name: 'Coinbase Wallet',\n    provider(window) {\n      if (window?.coinbaseWalletExtension) return window.coinbaseWalletExtension\n      return findProvider(window, 'isCoinbaseWallet')\n    },\n  },\n  metaMask: {\n    id: 'metaMask',\n    name: 'MetaMask',\n    provider(window) {\n      return findProvider(window, (provider) => {\n        if (!provider.isMetaMask) return false\n        // Brave tries to make itself look like MetaMask\n        // Could also try RPC `web3_clientVersion` if following is unreliable\n        if (provider.isBraveWallet && !provider._events && !provider._state)\n          return false\n        // Other wallets that try to look like MetaMask\n        const flags = [\n          'isApexWallet',\n          'isAvalanche',\n          'isBitKeep',\n          'isBlockWallet',\n          'isKuCoinWallet',\n          'isMathWallet',\n          'isOkxWallet',\n          'isOKExWallet',\n          'isOneInchIOSWallet',\n          'isOneInchAndroidWallet',\n          'isOpera',\n          'isPhantom',\n          'isPortal',\n          'isRabby',\n          'isTokenPocket',\n          'isTokenary',\n          'isUniswapWallet',\n          'isZerion',\n        ] satisfies WalletProviderFlags[]\n        for (const flag of flags) if (provider[flag]) return false\n        return true\n      })\n    },\n  },\n  phantom: {\n    id: 'phantom',\n    name: 'Phantom',\n    provider(window) {\n      if (window?.phantom?.ethereum) return window.phantom?.ethereum\n      return findProvider(window, 'isPhantom')\n    },\n  },\n} as const satisfies TargetMap\n\ntype TargetMap = { [_ in TargetId]?: Target | undefined }\n\ntype Target = {\n  icon?: string | undefined\n  id: string\n  name: string\n  provider:\n    | WalletProviderFlags\n    | WalletProvider\n    | ((window?: Window | undefined) => WalletProvider | undefined)\n}\n\n/** @deprecated */\ntype TargetId = Compute<WalletProviderFlags> extends `is${infer name}`\n  ? name extends `${infer char}${infer rest}`\n    ? `${Lowercase<char>}${rest}`\n    : never\n  : never\n\n/**\n * @deprecated As of 2024/10/16, we are no longer accepting new provider flags as EIP-6963 should be used instead.\n */\ntype WalletProviderFlags =\n  | 'isApexWallet'\n  | 'isAvalanche'\n  | 'isBackpack'\n  | 'isBifrost'\n  | 'isBitKeep'\n  | 'isBitski'\n  | 'isBlockWallet'\n  | 'isBraveWallet'\n  | 'isCoinbaseWallet'\n  | 'isDawn'\n  | 'isEnkrypt'\n  | 'isExodus'\n  | 'isFrame'\n  | 'isFrontier'\n  | 'isGamestop'\n  | 'isHyperPay'\n  | 'isImToken'\n  | 'isKuCoinWallet'\n  | 'isMathWallet'\n  | 'isMetaMask'\n  | 'isOkxWallet'\n  | 'isOKExWallet'\n  | 'isOneInchAndroidWallet'\n  | 'isOneInchIOSWallet'\n  | 'isOpera'\n  | 'isPhantom'\n  | 'isPortal'\n  | 'isRabby'\n  | 'isRainbow'\n  | 'isStatus'\n  | 'isTally'\n  | 'isTokenPocket'\n  | 'isTokenary'\n  | 'isTrust'\n  | 'isTrustWallet'\n  | 'isUniswapWallet'\n  | 'isXDEFI'\n  | 'isZerion'\n\ntype WalletProvider = Compute<\n  EIP1193Provider & {\n    [key in WalletProviderFlags]?: true | undefined\n  } & {\n    providers?: WalletProvider[] | undefined\n    /** Only exists in MetaMask as of 2022/04/03 */\n    _events?: { connect?: (() => void) | undefined } | undefined\n    /** Only exists in MetaMask as of 2022/04/03 */\n    _state?:\n      | {\n          accounts?: string[]\n          initialized?: boolean\n          isConnected?: boolean\n          isPermanentlyDisconnected?: boolean\n          isUnlocked?: boolean\n        }\n      | undefined\n  }\n>\n\ntype Window = {\n  coinbaseWalletExtension?: WalletProvider | undefined\n  ethereum?: WalletProvider | undefined\n  phantom?: { ethereum: WalletProvider } | undefined\n}\n\nfunction findProvider(\n  window: globalThis.Window | Window | undefined,\n  select?: WalletProviderFlags | ((provider: WalletProvider) => boolean),\n) {\n  function isProvider(provider: WalletProvider) {\n    if (typeof select === 'function') return select(provider)\n    if (typeof select === 'string') return provider[select]\n    return true\n  }\n\n  const ethereum = (window as Window).ethereum\n  if (ethereum?.providers)\n    return ethereum.providers.find((provider) => isProvider(provider))\n  if (ethereum && isProvider(ethereum)) return ethereum\n  return undefined\n}\n", "import {\n  type Address,\n  type EIP1193RequestFn,\n  type Hex,\n  RpcRequestError,\n  SwitchChainError,\n  type Transport,\n  UserRejectedRequestError,\n  type WalletCallReceipt,\n  type WalletGetCallsStatusReturnType,\n  type WalletRpcSchema,\n  custom,\n  fromHex,\n  getAddress,\n  keccak256,\n  numberToHex,\n  stringToHex,\n} from 'viem'\nimport { rpc } from 'viem/utils'\n\nimport {\n  ChainNotConfiguredError,\n  ConnectorNotConnectedError,\n} from '../errors/config.js'\nimport { createConnector } from './createConnector.js'\n\nexport type MockParameters = {\n  accounts: readonly [Address, ...Address[]]\n  features?:\n    | {\n        defaultConnected?: boolean | undefined\n        connectError?: boolean | Error | undefined\n        switchChainError?: boolean | Error | undefined\n        signMessageError?: boolean | Error | undefined\n        signTypedDataError?: boolean | Error | undefined\n        reconnect?: boolean | undefined\n        watchAssetError?: boolean | Error | undefined\n      }\n    | undefined\n}\n\nmock.type = 'mock' as const\nexport function mock(parameters: MockParameters) {\n  const transactionCache = new Map<Hex, Hex[]>()\n  const features =\n    parameters.features ??\n    ({ defaultConnected: false } satisfies MockParameters['features'])\n\n  type Provider = ReturnType<\n    Transport<'custom', unknown, EIP1193RequestFn<WalletRpcSchema>>\n  >\n  type Properties = {\n    connect(parameters?: {\n      chainId?: number | undefined\n      isReconnecting?: boolean | undefined\n      foo?: string | undefined\n    }): Promise<{\n      accounts: readonly Address[]\n      chainId: number\n    }>\n  }\n  let connected = features.defaultConnected\n  let connectedChainId: number\n\n  return createConnector<Provider, Properties>((config) => ({\n    id: 'mock',\n    name: 'Mock Connector',\n    type: mock.type,\n    async setup() {\n      connectedChainId = config.chains[0].id\n    },\n    async connect({ chainId } = {}) {\n      if (features.connectError) {\n        if (typeof features.connectError === 'boolean')\n          throw new UserRejectedRequestError(new Error('Failed to connect.'))\n        throw features.connectError\n      }\n\n      const provider = await this.getProvider()\n      const accounts = await provider.request({\n        method: 'eth_requestAccounts',\n      })\n\n      let currentChainId = await this.getChainId()\n      if (chainId && currentChainId !== chainId) {\n        const chain = await this.switchChain!({ chainId })\n        currentChainId = chain.id\n      }\n\n      connected = true\n\n      return {\n        accounts: accounts.map((x) => getAddress(x)),\n        chainId: currentChainId,\n      }\n    },\n    async disconnect() {\n      connected = false\n    },\n    async getAccounts() {\n      if (!connected) throw new ConnectorNotConnectedError()\n      const provider = await this.getProvider()\n      const accounts = await provider.request({ method: 'eth_accounts' })\n      return accounts.map((x) => getAddress(x))\n    },\n    async getChainId() {\n      const provider = await this.getProvider()\n      const hexChainId = await provider.request({ method: 'eth_chainId' })\n      return fromHex(hexChainId, 'number')\n    },\n    async isAuthorized() {\n      if (!features.reconnect) return false\n      if (!connected) return false\n      const accounts = await this.getAccounts()\n      return !!accounts.length\n    },\n    async switchChain({ chainId }) {\n      const provider = await this.getProvider()\n      const chain = config.chains.find((x) => x.id === chainId)\n      if (!chain) throw new SwitchChainError(new ChainNotConfiguredError())\n\n      await provider.request({\n        method: 'wallet_switchEthereumChain',\n        params: [{ chainId: numberToHex(chainId) }],\n      })\n      return chain\n    },\n    onAccountsChanged(accounts) {\n      if (accounts.length === 0) this.onDisconnect()\n      else\n        config.emitter.emit('change', {\n          accounts: accounts.map((x) => getAddress(x)),\n        })\n    },\n    onChainChanged(chain) {\n      const chainId = Number(chain)\n      config.emitter.emit('change', { chainId })\n    },\n    async onDisconnect(_error) {\n      config.emitter.emit('disconnect')\n      connected = false\n    },\n    async getProvider({ chainId } = {}) {\n      const chain =\n        config.chains.find((x) => x.id === chainId) ?? config.chains[0]\n      const url = chain.rpcUrls.default.http[0]!\n\n      const request: EIP1193RequestFn = async ({ method, params }) => {\n        // eth methods\n        if (method === 'eth_chainId') return numberToHex(connectedChainId)\n        if (method === 'eth_requestAccounts') return parameters.accounts\n        if (method === 'eth_signTypedData_v4')\n          if (features.signTypedDataError) {\n            if (typeof features.signTypedDataError === 'boolean')\n              throw new UserRejectedRequestError(\n                new Error('Failed to sign typed data.'),\n              )\n            throw features.signTypedDataError\n          }\n\n        // wallet methods\n        if (method === 'wallet_switchEthereumChain') {\n          if (features.switchChainError) {\n            if (typeof features.switchChainError === 'boolean')\n              throw new UserRejectedRequestError(\n                new Error('Failed to switch chain.'),\n              )\n            throw features.switchChainError\n          }\n          type Params = [{ chainId: Hex }]\n          connectedChainId = fromHex((params as Params)[0].chainId, 'number')\n          this.onChainChanged(connectedChainId.toString())\n          return\n        }\n\n        if (method === 'wallet_watchAsset') {\n          if (features.watchAssetError) {\n            if (typeof features.watchAssetError === 'boolean')\n              throw new UserRejectedRequestError(\n                new Error('Failed to switch chain.'),\n              )\n            throw features.watchAssetError\n          }\n          return connected\n        }\n\n        if (method === 'wallet_getCapabilities')\n          return {\n            '0x2105': {\n              paymasterService: {\n                supported:\n                  (params as [Hex])[0] ===\n                  '******************************************',\n              },\n              sessionKeys: {\n                supported: true,\n              },\n            },\n            '0x14A34': {\n              paymasterService: {\n                supported:\n                  (params as [Hex])[0] ===\n                  '******************************************',\n              },\n            },\n          }\n\n        if (method === 'wallet_sendCalls') {\n          const hashes = []\n          const calls = (params as any)[0].calls\n          for (const call of calls) {\n            const { result, error } = await rpc.http(url, {\n              body: {\n                method: 'eth_sendTransaction',\n                params: [call],\n              },\n            })\n            if (error)\n              throw new RpcRequestError({\n                body: { method, params },\n                error,\n                url,\n              })\n            hashes.push(result)\n          }\n          const id = keccak256(stringToHex(JSON.stringify(calls)))\n          transactionCache.set(id, hashes)\n          return { id }\n        }\n\n        if (method === 'wallet_getCallsStatus') {\n          const hashes = transactionCache.get((params as any)[0])\n          if (!hashes)\n            return {\n              atomic: false,\n              chainId: '0x1',\n              id: (params as any)[0],\n              status: 100,\n              receipts: [],\n              version: '2.0.0',\n            } satisfies WalletGetCallsStatusReturnType\n\n          const receipts = await Promise.all(\n            hashes.map(async (hash) => {\n              const { result, error } = await rpc.http(url, {\n                body: {\n                  method: 'eth_getTransactionReceipt',\n                  params: [hash],\n                  id: 0,\n                },\n              })\n              if (error)\n                throw new RpcRequestError({\n                  body: { method, params },\n                  error,\n                  url,\n                })\n              if (!result) return null\n              return {\n                blockHash: result.blockHash,\n                blockNumber: result.blockNumber,\n                gasUsed: result.gasUsed,\n                logs: result.logs,\n                status: result.status,\n                transactionHash: result.transactionHash,\n              } satisfies WalletCallReceipt\n            }),\n          )\n          const receipts_ = receipts.filter((x) => x !== null)\n          if (receipts_.length === 0)\n            return {\n              atomic: false,\n              chainId: '0x1',\n              id: (params as any)[0],\n              status: 100,\n              receipts: [],\n              version: '2.0.0',\n            } satisfies WalletGetCallsStatusReturnType\n          return {\n            atomic: false,\n            chainId: '0x1',\n            id: (params as any)[0],\n            status: 200,\n            receipts: receipts_,\n            version: '2.0.0',\n          } satisfies WalletGetCallsStatusReturnType\n        }\n\n        if (method === 'wallet_showCallsStatus') return\n\n        // other methods\n        if (method === 'personal_sign') {\n          if (features.signMessageError) {\n            if (typeof features.signMessageError === 'boolean')\n              throw new UserRejectedRequestError(\n                new Error('Failed to sign message.'),\n              )\n            throw features.signMessageError\n          }\n          // Change `personal_sign` to `eth_sign` and swap params\n          method = 'eth_sign'\n          type Params = [data: Hex, address: Address]\n          params = [(params as Params)[1], (params as Params)[0]]\n        }\n\n        const body = { method, params }\n        const { error, result } = await rpc.http(url, { body })\n        if (error) throw new RpcRequestError({ body, error, url })\n\n        return result\n      }\n      return custom({ request })({ retryCount: 0 })\n    },\n  }))\n}\n", "type Reviver = (key: string, value: any) => any\n\nexport function deserialize<type>(value: string, reviver?: Reviver): type {\n  return JSON.parse(value, (key, value_) => {\n    let value = value_\n    if (value?.__type === 'bigint') value = BigInt(value.value)\n    if (value?.__type === 'Map') value = new Map(value.value)\n    return reviver?.(key, value) ?? value\n  })\n}\n", "/**\n * Get the reference key for the circular value\n *\n * @param keys the keys to build the reference key from\n * @param cutoff the maximum number of keys to include\n * @returns the reference key\n */\nfunction getReferenceKey(keys: string[], cutoff: number) {\n  return keys.slice(0, cutoff).join('.') || '.'\n}\n\n/**\n * Faster `Array.prototype.indexOf` implementation build for slicing / splicing\n *\n * @param array the array to match the value in\n * @param value the value to match\n * @returns the matching index, or -1\n */\nfunction getCutoff(array: any[], value: any) {\n  const { length } = array\n\n  for (let index = 0; index < length; ++index) {\n    if (array[index] === value) {\n      return index + 1\n    }\n  }\n\n  return 0\n}\n\ntype StandardReplacer = (key: string, value: any) => any\ntype CircularReplacer = (key: string, value: any, referenceKey: string) => any\n\n/**\n * Create a replacer method that handles circular values\n *\n * @param [replacer] a custom replacer to use for non-circular values\n * @param [circularReplacer] a custom replacer to use for circular methods\n * @returns the value to stringify\n */\nfunction createReplacer(\n  replacer?: StandardReplacer | null | undefined,\n  circularReplacer?: CircularReplacer | null | undefined,\n): StandardReplacer {\n  const hasReplacer = typeof replacer === 'function'\n  const hasCircularReplacer = typeof circularReplacer === 'function'\n\n  const cache: any[] = []\n  const keys: string[] = []\n\n  return function replace(this: any, key: string, value: any) {\n    if (typeof value === 'object') {\n      if (cache.length) {\n        const thisCutoff = getCutoff(cache, this)\n\n        if (thisCutoff === 0) {\n          cache[cache.length] = this\n        } else {\n          cache.splice(thisCutoff)\n          keys.splice(thisCutoff)\n        }\n\n        keys[keys.length] = key\n\n        const valueCutoff = getCutoff(cache, value)\n\n        if (valueCutoff !== 0) {\n          return hasCircularReplacer\n            ? circularReplacer.call(\n                this,\n                key,\n                value,\n                getReferenceKey(keys, valueCutoff),\n              )\n            : `[ref=${getReferenceKey(keys, valueCutoff)}]`\n        }\n      } else {\n        cache[0] = value\n        keys[0] = key\n      }\n    }\n\n    return hasReplacer ? replacer.call(this, key, value) : value\n  }\n}\n\n/**\n * Stringifier that handles circular values\n *\n * Forked from https://github.com/planttheidea/fast-stringify\n *\n * @param value to stringify\n * @param [replacer] a custom replacer function for handling standard values\n * @param [indent] the number of spaces to indent the output by\n * @param [circularReplacer] a custom replacer function for handling circular values\n * @returns the stringified output\n */\nexport function serialize(\n  value: any,\n  replacer?: StandardReplacer | null | undefined,\n  indent?: number | null | undefined,\n  circularReplacer?: CircularReplacer | null | undefined,\n) {\n  return JSON.stringify(\n    value,\n    createReplacer((key, value_) => {\n      let value = value_\n      if (typeof value === 'bigint')\n        value = { __type: 'bigint', value: value_.toString() }\n      if (value instanceof Map)\n        value = { __type: 'Map', value: Array.from(value_.entries()) }\n      return replacer?.(key, value) ?? value\n    }, circularReplacer),\n    indent ?? undefined,\n  )\n}\n", "import type { PartializedState } from './createConfig.js'\nimport type { Compute } from './types/utils.js'\nimport { deserialize as deserialize_ } from './utils/deserialize.js'\nimport { serialize as serialize_ } from './utils/serialize.js'\n\n// key-values for loose autocomplete and typing\nexport type StorageItemMap = {\n  recentConnectorId: string\n  state: PartializedState\n}\n\nexport type Storage<\n  itemMap extends Record<string, unknown> = Record<string, unknown>,\n  ///\n  storageItemMap extends StorageItemMap = StorageItemMap & itemMap,\n> = {\n  key: string\n  getItem<\n    key extends keyof storageItemMap,\n    value extends storageItemMap[key],\n    defaultValue extends value | null | undefined,\n  >(\n    key: key,\n    defaultValue?: defaultValue | undefined,\n  ):\n    | (defaultValue extends null ? value | null : value)\n    | Promise<defaultValue extends null ? value | null : value>\n  setItem<\n    key extends keyof storageItemMap,\n    value extends storageItemMap[key] | null,\n  >(key: key, value: value): void | Promise<void>\n  removeItem(key: keyof storageItemMap): void | Promise<void>\n}\n\nexport type BaseStorage = {\n  getItem(\n    key: string,\n  ): string | null | undefined | Promise<string | null | undefined>\n  setItem(key: string, value: string): void | Promise<void>\n  removeItem(key: string): void | Promise<void>\n}\n\nexport type CreateStorageParameters = {\n  deserialize?: (<type>(value: string) => type | unknown) | undefined\n  key?: string | undefined\n  serialize?: (<type>(value: type | any) => string) | undefined\n  storage?: Compute<BaseStorage> | undefined\n}\n\nexport function createStorage<\n  itemMap extends Record<string, unknown> = Record<string, unknown>,\n  storageItemMap extends StorageItemMap = StorageItemMap & itemMap,\n>(parameters: CreateStorageParameters): Compute<Storage<storageItemMap>> {\n  const {\n    deserialize = deserialize_,\n    key: prefix = 'wagmi',\n    serialize = serialize_,\n    storage = noopStorage,\n  } = parameters\n\n  function unwrap<type>(value: type): type | Promise<type> {\n    if (value instanceof Promise) return value.then((x) => x).catch(() => null)\n    return value\n  }\n\n  return {\n    ...storage,\n    key: prefix,\n    async getItem(key, defaultValue) {\n      const value = storage.getItem(`${prefix}.${key as string}`)\n      const unwrapped = await unwrap(value)\n      if (unwrapped) return deserialize(unwrapped) ?? null\n      return (defaultValue ?? null) as any\n    },\n    async setItem(key, value) {\n      const storageKey = `${prefix}.${key as string}`\n      if (value === null) await unwrap(storage.removeItem(storageKey))\n      else await unwrap(storage.setItem(storageKey, serialize(value)))\n    },\n    async removeItem(key) {\n      await unwrap(storage.removeItem(`${prefix}.${key as string}`))\n    },\n  }\n}\n\nexport const noopStorage = {\n  getItem: () => null,\n  setItem: () => {},\n  removeItem: () => {},\n} satisfies BaseStorage\n\nexport function getDefaultStorage() {\n  const storage = (() => {\n    if (typeof window !== 'undefined' && window.localStorage)\n      return window.localStorage\n    return noopStorage\n  })()\n  return {\n    getItem(key) {\n      return storage.getItem(key)\n    },\n    removeItem(key) {\n      storage.removeItem(key)\n    },\n    setItem(key, value) {\n      try {\n        storage.setItem(key, value)\n        // silence errors by default (QuotaExceededError, SecurityError, etc.)\n      } catch {}\n    },\n  } satisfies BaseStorage\n}\n", "import type { EIP1193Provider } from './register.js'\nimport type {\n  EIP6963AnnounceProviderEvent,\n  EIP6963ProviderDetail,\n} from './types.js'\n\n////////////////////////////////////////////////////////////////////////////\n// Announce Provider\n\nexport type AnnounceProviderParameters = EIP6963ProviderDetail<\n  EIP1193Provider,\n  string\n>\nexport type AnnounceProviderReturnType = () => void\n\n/**\n * Announces an EIP-1193 Provider.\n */\nexport function announceProvider(\n  detail: AnnounceProviderParameters,\n): AnnounceProviderReturnType {\n  const event: CustomEvent<EIP6963ProviderDetail> = new CustomEvent(\n    'eip6963:announceProvider',\n    { detail: Object.freeze(detail) },\n  )\n\n  window.dispatchEvent(event)\n\n  const handler = () => window.dispatchEvent(event)\n  window.addEventListener('eip6963:requestProvider', handler)\n  return () => window.removeEventListener('eip6963:requestProvider', handler)\n}\n\n////////////////////////////////////////////////////////////////////////////\n// Request Providers\n\nexport type RequestProvidersParameters = (\n  providerDetail: EIP6963ProviderDetail,\n) => void\nexport type RequestProvidersReturnType = (() => void) | undefined\n\n/**\n * Watches for EIP-1193 Providers to be announced.\n */\nexport function requestProviders(\n  listener: RequestProvidersParameters,\n): RequestProvidersReturnType {\n  if (typeof window === 'undefined') return\n  const handler = (event: EIP6963AnnounceProviderEvent) =>\n    listener(event.detail)\n\n  window.addEventListener('eip6963:announceProvider', handler)\n\n  window.dispatchEvent(new CustomEvent('eip6963:requestProvider'))\n\n  return () => window.removeEventListener('eip6963:announceProvider', handler)\n}\n", "import type { Rdns } from './register.js'\nimport type { EIP6963ProviderDetail } from './types.js'\nimport { requestProviders } from './utils.js'\n\nexport type Listener = (\n  providerDetails: readonly EIP6963ProviderDetail[],\n  meta?:\n    | {\n        added?: readonly EIP6963ProviderDetail[] | undefined\n        removed?: readonly EIP6963ProviderDetail[] | undefined\n      }\n    | undefined,\n) => void\n\nexport type Store = {\n  /**\n   * Clears the store, including all provider details.\n   */\n  clear(): void\n  /**\n   * Destroys the store, including all provider details and listeners.\n   */\n  destroy(): void\n  /**\n   * Finds a provider detail by its RDNS (Reverse Domain Name Identifier).\n   */\n  findProvider(args: { rdns: Rdns }): EIP6963ProviderDetail | undefined\n  /**\n   * Returns all provider details that have been emitted.\n   */\n  getProviders(): readonly EIP6963ProviderDetail[]\n  /**\n   * Resets the store, and emits an event to request provider details.\n   */\n  reset(): void\n  /**\n   * Subscribes to emitted provider details.\n   */\n  subscribe(\n    listener: Listener,\n    args?: { emitImmediately?: boolean | undefined } | undefined,\n  ): () => void\n\n  /**\n   * @internal\n   * Current state of listening listeners.\n   */\n  _listeners(): Set<Listener>\n}\n\nexport function createStore(): Store {\n  const listeners: Set<Listener> = new Set()\n  let providerDetails: readonly EIP6963ProviderDetail[] = []\n\n  const request = () =>\n    requestProviders((providerDetail) => {\n      if (\n        providerDetails.some(\n          ({ info }) => info.uuid === providerDetail.info.uuid,\n        )\n      )\n        return\n\n      providerDetails = [...providerDetails, providerDetail]\n      listeners.forEach((listener) =>\n        listener(providerDetails, { added: [providerDetail] }),\n      )\n    })\n  let unwatch = request()\n\n  return {\n    _listeners() {\n      return listeners\n    },\n    clear() {\n      listeners.forEach((listener) =>\n        listener([], { removed: [...providerDetails] }),\n      )\n      providerDetails = []\n    },\n    destroy() {\n      this.clear()\n      listeners.clear()\n      unwatch?.()\n    },\n    findProvider({ rdns }) {\n      return providerDetails.find(\n        (providerDetail) => providerDetail.info.rdns === rdns,\n      )\n    },\n    getProviders() {\n      return providerDetails\n    },\n    reset() {\n      this.clear()\n      unwatch?.()\n      unwatch = request()\n    },\n    subscribe(listener, { emitImmediately } = {}) {\n      listeners.add(listener)\n      if (emitImmediately) listener(providerDetails, { added: providerDetails })\n      return () => listeners.delete(listener)\n    },\n  }\n}\n", "const reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...a) => api.dispatch(...a), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extensionConnector) {\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const action = nameOrAction === void 0 ? { type: anonymousActionType || \"anonymous\" } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...a) => {\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...a);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format.\n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, f) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) f(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nconst combine = (initialState, create) => (...a) => Object.assign({}, initialState, create(...a));\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(\n      name,\n      JSON.stringify(newValue, options == null ? void 0 : options.replacer)\n    ),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst persistImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            return [\n              true,\n              options.migrate(\n                deserializedStorageValue.state,\n                deserializedStorageValue.version\n              )\n            ];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persist = persistImpl;\n\nexport { combine, createJSONStorage, devtools, persist, redux, subscribeWithSelector };\n", "const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n", "import EventEmitter from './index.js'\n\nexport { EventEmitter }\nexport default EventEmitter\n", "import { EventEmitter } from 'eventemitter3'\n\ntype EventMap = Record<string, object | never>\ntype EventKey<eventMap extends EventMap> = string & keyof eventMap\ntype EventFn<parameters extends unknown[] = any[]> = (\n  ...parameters: parameters\n) => void\nexport type EventData<\n  eventMap extends EventMap,\n  eventName extends keyof eventMap,\n> = (eventMap[eventName] extends [never] ? unknown : eventMap[eventName]) & {\n  uid: string\n}\n\nexport class Emitter<eventMap extends EventMap> {\n  _emitter = new EventEmitter()\n\n  constructor(public uid: string) {}\n\n  on<key extends EventKey<eventMap>>(\n    eventName: key,\n    fn: EventFn<\n      eventMap[key] extends [never]\n        ? [{ uid: string }]\n        : [data: eventMap[key] & { uid: string }]\n    >,\n  ) {\n    this._emitter.on(eventName, fn as EventFn)\n  }\n\n  once<key extends EventKey<eventMap>>(\n    eventName: key,\n    fn: EventFn<\n      eventMap[key] extends [never]\n        ? [{ uid: string }]\n        : [data: eventMap[key] & { uid: string }]\n    >,\n  ) {\n    this._emitter.once(eventName, fn as EventFn)\n  }\n\n  off<key extends EventKey<eventMap>>(\n    eventName: key,\n    fn: EventFn<\n      eventMap[key] extends [never]\n        ? [{ uid: string }]\n        : [data: eventMap[key] & { uid: string }]\n    >,\n  ) {\n    this._emitter.off(eventName, fn as EventFn)\n  }\n\n  emit<key extends EventKey<eventMap>>(\n    eventName: key,\n    ...params: eventMap[key] extends [never] ? [] : [data: eventMap[key]]\n  ) {\n    const data = params[0]\n    this._emitter.emit(eventName, { uid: this.uid, ...data })\n  }\n\n  listenerCount<key extends EventKey<eventMap>>(eventName: key) {\n    return this._emitter.listenerCount(eventName)\n  }\n}\n\nexport function createEmitter<eventMap extends EventMap>(uid: string) {\n  return new Emitter<eventMap>(uid)\n}\n", "const size = 256\nlet index = size\nlet buffer: string\n\nexport function uid(length = 11) {\n  if (!buffer || index + length > size * 2) {\n    buffer = ''\n    index = 0\n    for (let i = 0; i < size; i++) {\n      buffer += ((256 + Math.random() * 256) | 0).toString(16).substring(1)\n    }\n  }\n  return buffer.substring(index, index++ + length)\n}\n", "import {\n  type EIP6963ProviderDetail,\n  type Store as MipdStore,\n  createStore as createMipd,\n} from 'mipd'\nimport {\n  type Address,\n  type Chain,\n  type Client,\n  type EIP1193RequestFn,\n  createClient,\n  type ClientConfig as viem_ClientConfig,\n  type Transport as viem_Transport,\n} from 'viem'\nimport { persist, subscribeWithSelector } from 'zustand/middleware'\nimport { type Mutate, type StoreApi, createStore } from 'zustand/vanilla'\n\nimport type {\n  ConnectorEventMap,\n  CreateConnectorFn,\n} from './connectors/createConnector.js'\nimport { injected } from './connectors/injected.js'\nimport { type Emitter, type EventData, createEmitter } from './createEmitter.js'\nimport {\n  type Storage,\n  createStorage,\n  getDefaultStorage,\n} from './createStorage.js'\nimport { ChainNotConfiguredError } from './errors/config.js'\nimport type {\n  Compute,\n  ExactPartial,\n  LooseOmit,\n  OneOf,\n  RemoveUndefined,\n} from './types/utils.js'\nimport { uid } from './utils/uid.js'\nimport { version } from './version.js'\n\nexport function createConfig<\n  const chains extends readonly [Chain, ...Chain[]],\n  transports extends Record<chains[number]['id'], Transport>,\n  const connectorFns extends readonly CreateConnectorFn[],\n>(\n  parameters: CreateConfigParameters<chains, transports, connectorFns>,\n): Config<chains, transports, connectorFns> {\n  const {\n    multiInjectedProviderDiscovery = true,\n    storage = createStorage({\n      storage: getDefaultStorage(),\n    }),\n    syncConnectedChain = true,\n    ssr = false,\n    ...rest\n  } = parameters\n\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n  // Set up connectors, clients, etc.\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n\n  const mipd =\n    typeof window !== 'undefined' && multiInjectedProviderDiscovery\n      ? createMipd()\n      : undefined\n\n  const chains = createStore(() => rest.chains)\n  const connectors = createStore(() => {\n    const collection = []\n    const rdnsSet = new Set<string>()\n    for (const connectorFns of rest.connectors ?? []) {\n      const connector = setup(connectorFns)\n      collection.push(connector)\n      if (!ssr && connector.rdns) {\n        const rdnsValues =\n          typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns\n        for (const rdns of rdnsValues) {\n          rdnsSet.add(rdns)\n        }\n      }\n    }\n    if (!ssr && mipd) {\n      const providers = mipd.getProviders()\n      for (const provider of providers) {\n        if (rdnsSet.has(provider.info.rdns)) continue\n        collection.push(setup(providerDetailToConnector(provider)))\n      }\n    }\n    return collection\n  })\n  function setup(connectorFn: CreateConnectorFn): Connector {\n    // Set up emitter with uid and add to connector so they are \"linked\" together.\n    const emitter = createEmitter<ConnectorEventMap>(uid())\n    const connector = {\n      ...connectorFn({\n        emitter,\n        chains: chains.getState(),\n        storage,\n        transports: rest.transports,\n      }),\n      emitter,\n      uid: emitter.uid,\n    }\n\n    // Start listening for `connect` events on connector setup\n    // This allows connectors to \"connect\" themselves without user interaction (e.g. MetaMask's \"Manually connect to current site\")\n    emitter.on('connect', connect)\n    connector.setup?.()\n\n    return connector\n  }\n  function providerDetailToConnector(providerDetail: EIP6963ProviderDetail) {\n    const { info } = providerDetail\n    const provider = providerDetail.provider as any\n    return injected({ target: { ...info, id: info.rdns, provider } })\n  }\n\n  const clients = new Map<number, Client<Transport, chains[number]>>()\n  function getClient<chainId extends chains[number]['id']>(\n    config: { chainId?: chainId | chains[number]['id'] | undefined } = {},\n  ): Client<Transport, Extract<chains[number], { id: chainId }>> {\n    const chainId = config.chainId ?? store.getState().chainId\n    const chain = chains.getState().find((x) => x.id === chainId)\n\n    // chainId specified and not configured\n    if (config.chainId && !chain) throw new ChainNotConfiguredError()\n\n    // If the target chain is not configured, use the client of the current chain.\n    type Return = Client<Transport, Extract<chains[number], { id: chainId }>>\n    {\n      const client = clients.get(store.getState().chainId)\n      if (client && !chain) return client as Return\n      if (!chain) throw new ChainNotConfiguredError()\n    }\n\n    // If a memoized client exists for a chain id, use that.\n    {\n      const client = clients.get(chainId)\n      if (client) return client as Return\n    }\n\n    let client: Client<Transport, chains[number]>\n    if (rest.client) client = rest.client({ chain })\n    else {\n      const chainId = chain.id as chains[number]['id']\n      const chainIds = chains.getState().map((x) => x.id)\n      // Grab all properties off `rest` and resolve for use in `createClient`\n      const properties: Partial<viem_ClientConfig> = {}\n      const entries = Object.entries(rest) as [keyof typeof rest, any][]\n\n      for (const [key, value] of entries) {\n        if (\n          key === 'chains' ||\n          key === 'client' ||\n          key === 'connectors' ||\n          key === 'transports'\n        )\n          continue\n\n        if (typeof value === 'object') {\n          // check if value is chainId-specific since some values can be objects\n          // e.g. { batch: { multicall: { batchSize: 1024 } } }\n          if (chainId in value) properties[key] = value[chainId]\n          else {\n            // check if value is chainId-specific, but does not have value for current chainId\n            const hasChainSpecificValue = chainIds.some((x) => x in value)\n            if (hasChainSpecificValue) continue\n            properties[key] = value\n          }\n        } else properties[key] = value\n      }\n\n      client = createClient({\n        ...properties,\n        chain,\n        batch: properties.batch ?? { multicall: true },\n        transport: (parameters) =>\n          rest.transports[chainId]({ ...parameters, connectors }),\n      })\n    }\n\n    clients.set(chainId, client)\n    return client as Return\n  }\n\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n  // Create store\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n\n  function getInitialState(): State {\n    return {\n      chainId: chains.getState()[0].id,\n      connections: new Map<string, Connection>(),\n      current: null,\n      status: 'disconnected',\n    }\n  }\n\n  let currentVersion: number\n  const prefix = '0.0.0-canary-'\n  if (version.startsWith(prefix))\n    currentVersion = Number.parseInt(version.replace(prefix, ''))\n  // use package major version to version store\n  else currentVersion = Number.parseInt(version.split('.')[0] ?? '0')\n\n  const store = createStore(\n    subscribeWithSelector(\n      // only use persist middleware if storage exists\n      storage\n        ? persist(getInitialState, {\n            migrate(persistedState, version) {\n              if (version === currentVersion) return persistedState as State\n\n              const initialState = getInitialState()\n              const chainId = validatePersistedChainId(\n                persistedState,\n                initialState.chainId,\n              )\n              return { ...initialState, chainId }\n            },\n            name: 'store',\n            partialize(state) {\n              // Only persist \"critical\" store properties to preserve storage size.\n              return {\n                connections: {\n                  __type: 'Map',\n                  value: Array.from(state.connections.entries()).map(\n                    ([key, connection]) => {\n                      const { id, name, type, uid } = connection.connector\n                      const connector = { id, name, type, uid }\n                      return [key, { ...connection, connector }]\n                    },\n                  ),\n                } as unknown as PartializedState['connections'],\n                chainId: state.chainId,\n                current: state.current,\n              } satisfies PartializedState\n            },\n            merge(persistedState, currentState) {\n              // `status` should not be persisted as it messes with reconnection\n              if (\n                typeof persistedState === 'object' &&\n                persistedState &&\n                'status' in persistedState\n              )\n                delete persistedState.status\n              // Make sure persisted `chainId` is valid\n              const chainId = validatePersistedChainId(\n                persistedState,\n                currentState.chainId,\n              )\n              return {\n                ...currentState,\n                ...(persistedState as object),\n                chainId,\n              }\n            },\n            skipHydration: ssr,\n            storage: storage as Storage<Record<string, unknown>>,\n            version: currentVersion,\n          })\n        : getInitialState,\n    ),\n  )\n  store.setState(getInitialState())\n\n  function validatePersistedChainId(\n    persistedState: unknown,\n    defaultChainId: number,\n  ) {\n    return persistedState &&\n      typeof persistedState === 'object' &&\n      'chainId' in persistedState &&\n      typeof persistedState.chainId === 'number' &&\n      chains.getState().some((x) => x.id === persistedState.chainId)\n      ? persistedState.chainId\n      : defaultChainId\n  }\n\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n  // Subscribe to changes\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n\n  // Update default chain when connector chain changes\n  if (syncConnectedChain)\n    store.subscribe(\n      ({ connections, current }) =>\n        current ? connections.get(current)?.chainId : undefined,\n      (chainId) => {\n        // If chain is not configured, then don't switch over to it.\n        const isChainConfigured = chains\n          .getState()\n          .some((x) => x.id === chainId)\n        if (!isChainConfigured) return\n\n        return store.setState((x) => ({\n          ...x,\n          chainId: chainId ?? x.chainId,\n        }))\n      },\n    )\n\n  // EIP-6963 subscribe for new wallet providers\n  mipd?.subscribe((providerDetails) => {\n    const connectorIdSet = new Set<string>()\n    const connectorRdnsSet = new Set<string>()\n    for (const connector of connectors.getState()) {\n      connectorIdSet.add(connector.id)\n      if (connector.rdns) {\n        const rdnsValues =\n          typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns\n        for (const rdns of rdnsValues) {\n          connectorRdnsSet.add(rdns)\n        }\n      }\n    }\n\n    const newConnectors: Connector[] = []\n    for (const providerDetail of providerDetails) {\n      if (connectorRdnsSet.has(providerDetail.info.rdns)) continue\n      const connector = setup(providerDetailToConnector(providerDetail))\n      if (connectorIdSet.has(connector.id)) continue\n      newConnectors.push(connector)\n    }\n\n    if (storage && !store.persist.hasHydrated()) return\n    connectors.setState((x) => [...x, ...newConnectors], true)\n  })\n\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n  // Emitter listeners\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n\n  function change(data: EventData<ConnectorEventMap, 'change'>) {\n    store.setState((x) => {\n      const connection = x.connections.get(data.uid)\n      if (!connection) return x\n      return {\n        ...x,\n        connections: new Map(x.connections).set(data.uid, {\n          accounts:\n            (data.accounts as readonly [Address, ...Address[]]) ??\n            connection.accounts,\n          chainId: data.chainId ?? connection.chainId,\n          connector: connection.connector,\n        }),\n      }\n    })\n  }\n  function connect(data: EventData<ConnectorEventMap, 'connect'>) {\n    // Disable handling if reconnecting/connecting\n    if (\n      store.getState().status === 'connecting' ||\n      store.getState().status === 'reconnecting'\n    )\n      return\n\n    store.setState((x) => {\n      const connector = connectors.getState().find((x) => x.uid === data.uid)\n      if (!connector) return x\n\n      if (connector.emitter.listenerCount('connect'))\n        connector.emitter.off('connect', change)\n      if (!connector.emitter.listenerCount('change'))\n        connector.emitter.on('change', change)\n      if (!connector.emitter.listenerCount('disconnect'))\n        connector.emitter.on('disconnect', disconnect)\n\n      return {\n        ...x,\n        connections: new Map(x.connections).set(data.uid, {\n          accounts: data.accounts as readonly [Address, ...Address[]],\n          chainId: data.chainId,\n          connector: connector,\n        }),\n        current: data.uid,\n        status: 'connected',\n      }\n    })\n  }\n  function disconnect(data: EventData<ConnectorEventMap, 'disconnect'>) {\n    store.setState((x) => {\n      const connection = x.connections.get(data.uid)\n      if (connection) {\n        const connector = connection.connector\n        if (connector.emitter.listenerCount('change'))\n          connection.connector.emitter.off('change', change)\n        if (connector.emitter.listenerCount('disconnect'))\n          connection.connector.emitter.off('disconnect', disconnect)\n        if (!connector.emitter.listenerCount('connect'))\n          connection.connector.emitter.on('connect', connect)\n      }\n\n      x.connections.delete(data.uid)\n\n      if (x.connections.size === 0)\n        return {\n          ...x,\n          connections: new Map(),\n          current: null,\n          status: 'disconnected',\n        }\n\n      const nextConnection = x.connections.values().next().value as Connection\n      return {\n        ...x,\n        connections: new Map(x.connections),\n        current: nextConnection.connector.uid,\n      }\n    })\n  }\n\n  return {\n    get chains() {\n      return chains.getState() as chains\n    },\n    get connectors() {\n      return connectors.getState() as Connector<connectorFns[number]>[]\n    },\n    storage,\n\n    getClient,\n    get state() {\n      return store.getState() as unknown as State<chains>\n    },\n    setState(value) {\n      let newState: State\n      if (typeof value === 'function') newState = value(store.getState() as any)\n      else newState = value\n\n      // Reset state if it got set to something not matching the base state\n      const initialState = getInitialState()\n      if (typeof newState !== 'object') newState = initialState\n      const isCorrupt = Object.keys(initialState).some((x) => !(x in newState))\n      if (isCorrupt) newState = initialState\n\n      store.setState(newState, true)\n    },\n    subscribe(selector, listener, options) {\n      return store.subscribe(\n        selector as unknown as (state: State) => any,\n        listener,\n        options\n          ? ({\n              ...options,\n              fireImmediately: options.emitImmediately,\n              // Workaround cast since Zustand does not support `'exactOptionalPropertyTypes'`\n            } as RemoveUndefined<typeof options>)\n          : undefined,\n      )\n    },\n\n    _internal: {\n      mipd,\n      store,\n      ssr: Boolean(ssr),\n      syncConnectedChain,\n      transports: rest.transports as transports,\n      chains: {\n        setState(value) {\n          const nextChains = (\n            typeof value === 'function' ? value(chains.getState()) : value\n          ) as chains\n          if (nextChains.length === 0) return\n          return chains.setState(nextChains, true)\n        },\n        subscribe(listener) {\n          return chains.subscribe(listener)\n        },\n      },\n      connectors: {\n        providerDetailToConnector,\n        setup: setup as <connectorFn extends CreateConnectorFn>(\n          connectorFn: connectorFn,\n        ) => Connector<connectorFn>,\n        setState(value) {\n          return connectors.setState(\n            typeof value === 'function' ? value(connectors.getState()) : value,\n            true,\n          )\n        },\n        subscribe(listener) {\n          return connectors.subscribe(listener)\n        },\n      },\n      events: { change, connect, disconnect },\n    },\n  }\n}\n\n/////////////////////////////////////////////////////////////////////////////////////////////////\n// Types\n/////////////////////////////////////////////////////////////////////////////////////////////////\n\nexport type CreateConfigParameters<\n  chains extends readonly [Chain, ...Chain[]] = readonly [Chain, ...Chain[]],\n  transports extends Record<chains[number]['id'], Transport> = Record<\n    chains[number]['id'],\n    Transport\n  >,\n  connectorFns extends\n    readonly CreateConnectorFn[] = readonly CreateConnectorFn[],\n> = Compute<\n  {\n    chains: chains\n    connectors?: connectorFns | undefined\n    multiInjectedProviderDiscovery?: boolean | undefined\n    storage?: Storage | null | undefined\n    ssr?: boolean | undefined\n    syncConnectedChain?: boolean | undefined\n  } & OneOf<\n    | ({ transports: transports } & {\n        [key in keyof ClientConfig]?:\n          | ClientConfig[key]\n          | { [_ in chains[number]['id']]?: ClientConfig[key] | undefined }\n          | undefined\n      })\n    | {\n        client(parameters: { chain: chains[number] }): Client<\n          transports[chains[number]['id']],\n          chains[number]\n        >\n      }\n  >\n>\n\nexport type Config<\n  chains extends readonly [Chain, ...Chain[]] = readonly [Chain, ...Chain[]],\n  transports extends Record<chains[number]['id'], Transport> = Record<\n    chains[number]['id'],\n    Transport\n  >,\n  connectorFns extends\n    readonly CreateConnectorFn[] = readonly CreateConnectorFn[],\n> = {\n  readonly chains: chains\n  readonly connectors: readonly Connector<connectorFns[number]>[]\n  readonly storage: Storage | null\n\n  readonly state: State<chains>\n  setState<tchains extends readonly [Chain, ...Chain[]] = chains>(\n    value: State<tchains> | ((state: State<tchains>) => State<tchains>),\n  ): void\n  subscribe<state>(\n    selector: (state: State<chains>) => state,\n    listener: (state: state, previousState: state) => void,\n    options?:\n      | {\n          emitImmediately?: boolean | undefined\n          equalityFn?: ((a: state, b: state) => boolean) | undefined\n        }\n      | undefined,\n  ): () => void\n\n  getClient<chainId extends chains[number]['id']>(parameters?: {\n    chainId?: chainId | chains[number]['id'] | undefined\n  }): Client<transports[chainId], Extract<chains[number], { id: chainId }>>\n\n  /**\n   * Not part of versioned API, proceed with caution.\n   * @internal\n   */\n  _internal: Internal<chains, transports>\n}\n\ntype Internal<\n  chains extends readonly [Chain, ...Chain[]] = readonly [Chain, ...Chain[]],\n  transports extends Record<chains[number]['id'], Transport> = Record<\n    chains[number]['id'],\n    Transport\n  >,\n> = {\n  readonly mipd: MipdStore | undefined\n  readonly store: Mutate<StoreApi<any>, [['zustand/persist', any]]>\n  readonly ssr: boolean\n  readonly syncConnectedChain: boolean\n  readonly transports: transports\n\n  chains: {\n    setState(\n      value:\n        | readonly [Chain, ...Chain[]]\n        | ((\n            state: readonly [Chain, ...Chain[]],\n          ) => readonly [Chain, ...Chain[]]),\n    ): void\n    subscribe(\n      listener: (\n        state: readonly [Chain, ...Chain[]],\n        prevState: readonly [Chain, ...Chain[]],\n      ) => void,\n    ): () => void\n  }\n  connectors: {\n    providerDetailToConnector(\n      providerDetail: EIP6963ProviderDetail,\n    ): CreateConnectorFn\n    setup<connectorFn extends CreateConnectorFn>(\n      connectorFn: connectorFn,\n    ): Connector<connectorFn>\n    setState(value: Connector[] | ((state: Connector[]) => Connector[])): void\n    subscribe(\n      listener: (state: Connector[], prevState: Connector[]) => void,\n    ): () => void\n  }\n  events: {\n    change(data: EventData<ConnectorEventMap, 'change'>): void\n    connect(data: EventData<ConnectorEventMap, 'connect'>): void\n    disconnect(data: EventData<ConnectorEventMap, 'disconnect'>): void\n  }\n}\n\nexport type State<\n  chains extends readonly [Chain, ...Chain[]] = readonly [Chain, ...Chain[]],\n> = {\n  chainId: chains[number]['id']\n  connections: Map<string, Connection>\n  current: string | null\n  status: 'connected' | 'connecting' | 'disconnected' | 'reconnecting'\n}\n\nexport type PartializedState = Compute<\n  ExactPartial<Pick<State, 'chainId' | 'connections' | 'current' | 'status'>>\n>\n\nexport type Connection = {\n  accounts: readonly [Address, ...Address[]]\n  chainId: number\n  connector: Connector\n}\n\nexport type Connector<\n  createConnectorFn extends CreateConnectorFn = CreateConnectorFn,\n> = ReturnType<createConnectorFn> & {\n  emitter: Emitter<ConnectorEventMap>\n  uid: string\n}\n\nexport type Transport<\n  type extends string = string,\n  rpcAttributes = Record<string, any>,\n  eip1193RequestFn extends EIP1193RequestFn = EIP1193RequestFn,\n> = (\n  params: Parameters<\n    viem_Transport<type, rpcAttributes, eip1193RequestFn>\n  >[0] & {\n    connectors?: StoreApi<Connector[]> | undefined\n  },\n) => ReturnType<viem_Transport<type, rpcAttributes, eip1193RequestFn>>\n\ntype ClientConfig = LooseOmit<\n  viem_ClientConfig,\n  'account' | 'chain' | 'key' | 'name' | 'transport' | 'type'\n>\n", "import {\n  ChainDisconnectedError,\n  type EIP1193Parameters,\n  type EIP1193<PERSON>rovider,\n  type EIP1193RequestFn,\n  ProviderDisconnectedError,\n  type TransportConfig,\n  type WalletRpcSchema,\n  createTransport,\n  hexToNumber,\n  withRetry,\n  withTimeout,\n} from 'viem'\n\nimport type { Connector, Transport } from '../createConfig.js'\n\nexport type ConnectorTransportConfig = {\n  /** The key of the transport. */\n  key?: TransportConfig['key'] | undefined\n  /** The name of the transport. */\n  name?: TransportConfig['name'] | undefined\n  /** The max number of times to retry. */\n  retryCount?: TransportConfig['retryCount'] | undefined\n  /** The base delay (in ms) between retries. */\n  retryDelay?: TransportConfig['retryDelay'] | undefined\n}\n\nexport type ConnectorTransport = Transport\n\nexport function unstable_connector(\n  connector: Pick<Connector, 'type'>,\n  config: ConnectorTransportConfig = {},\n): Transport<'connector'> {\n  const { type } = connector\n  const { key = 'connector', name = 'Connector', retryDelay } = config\n\n  return (parameters) => {\n    const { chain, connectors } = parameters\n    const retryCount = config.retryCount ?? parameters.retryCount\n\n    const request: EIP1193RequestFn = async ({ method, params }) => {\n      const connector = connectors?.getState().find((c) => c.type === type)\n      if (!connector)\n        throw new ProviderDisconnectedError(\n          new Error(\n            `Could not find connector of type \"${type}\" in \\`connectors\\` passed to \\`createConfig\\`.`,\n          ),\n        )\n\n      const provider = (await connector.getProvider({\n        chainId: chain?.id,\n      })) as EIP1193Provider | undefined\n      if (!provider)\n        throw new ProviderDisconnectedError(\n          new Error('Provider is disconnected.'),\n        )\n\n      // We are applying a retry & timeout strategy here as some injected wallets (e.g. MetaMask) fail to\n      // immediately resolve a JSON-RPC request on page load.\n      const chainId = hexToNumber(\n        await withRetry(() =>\n          withTimeout(() => provider.request({ method: 'eth_chainId' }), {\n            timeout: 100,\n          }),\n        ),\n      )\n      if (chain && chainId !== chain.id)\n        throw new ChainDisconnectedError(\n          new Error(\n            `The current chain of the connector (id: ${chainId}) does not match the target chain for the request (id: ${chain.id} – ${chain.name}).`,\n          ),\n        )\n\n      const body = { method, params } as EIP1193Parameters<WalletRpcSchema>\n      return provider.request(body)\n    }\n\n    return createTransport({\n      key,\n      name,\n      request,\n      retryCount,\n      retryDelay,\n      type: 'connector',\n    })\n  }\n}\n", "import { fallback as viem_fallback } from 'viem'\n\nimport type { Transport } from '../createConfig.js'\n\nexport function fallback(\n  transports: Transport[],\n  config?: Parameters<typeof viem_fallback>[1],\n) {\n  return viem_fallback(transports, config)\n}\n", "import type { Config, State } from '../createConfig.js'\nimport type { BaseStorage } from '../createStorage.js'\nimport { deserialize } from './deserialize.js'\n\nexport const cookieStorage = {\n  getItem(key) {\n    if (typeof window === 'undefined') return null\n    const value = parseCookie(document.cookie, key)\n    return value ?? null\n  },\n  setItem(key, value) {\n    if (typeof window === 'undefined') return\n    document.cookie = `${key}=${value};path=/;samesite=Lax`\n  },\n  removeItem(key) {\n    if (typeof window === 'undefined') return\n    document.cookie = `${key}=;max-age=-1;path=/`\n  },\n} satisfies BaseStorage\n\nexport function cookieToInitialState(config: Config, cookie?: string | null) {\n  if (!cookie) return undefined\n  const key = `${config.storage?.key}.store`\n  const parsed = parseCookie(cookie, key)\n  if (!parsed) return undefined\n  return deserialize<{ state: State }>(parsed).state\n}\n\nexport function parseCookie(cookie: string, key: string) {\n  const keyValue = cookie.split('; ').find((x) => x.startsWith(`${key}=`))\n  if (!keyValue) return undefined\n  return keyValue.substring(key.length + 1)\n}\n", "/** @deprecated use `Number` instead */\nexport function normalizeChainId(chainId: bigint | number | string | unknown) {\n  if (typeof chainId === 'string')\n    return Number.parseInt(\n      chainId,\n      chainId.trim().substring(0, 2) === '0x' ? 16 : 10,\n    )\n  if (typeof chainId === 'bigint') return Number(chainId)\n  if (typeof chainId === 'number') return chainId\n  throw new Error(\n    `Cannot normalize chainId \"${chainId}\" of type \"${typeof chainId}\"`,\n  )\n}\n", "import type {\n  Account,\n  Chain,\n  Client,\n  PublicActions,\n  RpcSchema,\n  Transport,\n  WalletActions,\n} from 'viem'\n\n/**\n * Retrieves and returns an action from the client (if exists), and falls\n * back to the tree-shakable action.\n *\n * Useful for extracting overridden actions from a client (ie. if a consumer\n * wants to override the `sendTransaction` implementation).\n */\nexport function getAction<\n  transport extends Transport,\n  chain extends Chain | undefined,\n  account extends Account | undefined,\n  rpcSchema extends RpcSchema | undefined,\n  extended extends { [key: string]: unknown },\n  client extends Client<transport, chain, account, rpcSchema, extended>,\n  parameters,\n  returnType,\n>(\n  client: client,\n  actionFn: (_: any, parameters: parameters) => returnType,\n  // Some minifiers drop `Function.prototype.name`, or replace it with short letters,\n  // meaning that `actionFn.name` will not always work. For that case, the consumer\n  // needs to pass the name explicitly.\n  name: keyof PublicActions | keyof WalletActions,\n): (parameters: parameters) => returnType {\n  const action_implicit = client[actionFn.name]\n  if (typeof action_implicit === 'function')\n    return action_implicit as (params: parameters) => returnType\n\n  const action_explicit = client[name]\n  if (typeof action_explicit === 'function')\n    return action_explicit as (params: parameters) => returnType\n\n  return (params) => actionFn(client, params)\n}\n", "import type {\n  CallErrorType as viem_CallErrorType,\n  CallParameters as viem_CallParameters,\n  CallReturnType as viem_CallReturnType,\n} from 'viem'\nimport { call as viem_call } from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type CallParameters<config extends Config = Config> =\n  viem_CallParameters & ChainIdParameter<config>\n\nexport type CallReturnType = viem_CallReturnType\n\nexport type CallErrorType = viem_CallErrorType\n\nexport async function call<config extends Config>(\n  config: config,\n  parameters: CallParameters<config>,\n): Promise<CallReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_call, 'call')\n  return action(rest)\n}\n", "import type {\n  Address,\n  ResourceUnavailableRpcErrorType,\n  UserRejectedRequestErrorType,\n} from 'viem'\n\nimport type { CreateConnectorFn } from '../connectors/createConnector.js'\nimport type { Config, Connector } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport {\n  ConnectorAlreadyConnectedError,\n  type ConnectorAlreadyConnectedErrorType,\n} from '../errors/config.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\n\nexport type ConnectParameters<\n  config extends Config = Config,\n  connector extends Connector | CreateConnectorFn =\n    | Connector\n    | CreateConnectorFn,\n  ///\n  parameters extends unknown | undefined =\n    | (connector extends CreateConnectorFn\n        ? Omit<\n            NonNullable<Parameters<ReturnType<connector>['connect']>[0]>,\n            'isReconnecting'\n          >\n        : never)\n    | (connector extends Connector\n        ? Omit<\n            NonNullable<Parameters<connector['connect']>[0]>,\n            'isReconnecting'\n          >\n        : never),\n> = Compute<\n  ChainIdParameter<config> & {\n    connector: connector | CreateConnectorFn\n  }\n> &\n  parameters\n\nexport type ConnectReturnType<config extends Config = Config> = {\n  accounts: readonly [Address, ...Address[]]\n  chainId:\n    | config['chains'][number]['id']\n    | (number extends config['chains'][number]['id'] ? number : number & {})\n}\n\nexport type ConnectErrorType =\n  | ConnectorAlreadyConnectedErrorType\n  // connector.connect()\n  | UserRejectedRequestErrorType\n  | ResourceUnavailableRpcErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n\n/** https://wagmi.sh/core/api/actions/connect */\nexport async function connect<\n  config extends Config,\n  connector extends Connector | CreateConnectorFn,\n>(\n  config: config,\n  parameters: ConnectParameters<config, connector>,\n): Promise<ConnectReturnType<config>> {\n  // \"Register\" connector if not already created\n  let connector: Connector\n  if (typeof parameters.connector === 'function') {\n    connector = config._internal.connectors.setup(parameters.connector)\n  } else connector = parameters.connector\n\n  // Check if connector is already connected\n  if (connector.uid === config.state.current)\n    throw new ConnectorAlreadyConnectedError()\n\n  try {\n    config.setState((x) => ({ ...x, status: 'connecting' }))\n    connector.emitter.emit('message', { type: 'connecting' })\n\n    const { connector: _, ...rest } = parameters\n    const data = await connector.connect(rest)\n    const accounts = data.accounts as readonly [Address, ...Address[]]\n\n    connector.emitter.off('connect', config._internal.events.connect)\n    connector.emitter.on('change', config._internal.events.change)\n    connector.emitter.on('disconnect', config._internal.events.disconnect)\n\n    await config.storage?.setItem('recentConnectorId', connector.id)\n    config.setState((x) => ({\n      ...x,\n      connections: new Map(x.connections).set(connector.uid, {\n        accounts,\n        chainId: data.chainId,\n        connector: connector,\n      }),\n      current: connector.uid,\n      status: 'connected',\n    }))\n\n    return { accounts, chainId: data.chainId }\n  } catch (error) {\n    config.setState((x) => ({\n      ...x,\n      // Keep existing connector connected in case of error\n      status: x.current ? 'connected' : 'disconnected',\n    }))\n    throw error\n  }\n}\n", "import {\n  type Account,\n  type Address,\n  type BaseErrorType,\n  type Client,\n  createClient,\n  custom,\n} from 'viem'\nimport { getAddress, parseAccount } from 'viem/utils'\n\nimport type { Config, Connection } from '../createConfig.js'\nimport type { ErrorType } from '../errors/base.js'\nimport {\n  ConnectorAccountNotFoundError,\n  type ConnectorAccountNotFoundErrorType,\n  ConnectorChainMismatchError,\n  type ConnectorChainMismatchErrorType,\n  ConnectorNotConnectedError,\n  type ConnectorNotConnectedErrorType,\n  ConnectorUnavailableReconnectingError,\n  type ConnectorUnavailableReconnectingErrorType,\n} from '../errors/config.js'\nimport type {\n  ChainIdParameter,\n  ConnectorParameter,\n} from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\n\nexport type GetConnectorClientParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  ChainIdParameter<config, chainId> &\n    ConnectorParameter & {\n      /**\n       * Account to use for the client.\n       *\n       * - `Account | Address`: An Account MUST exist on the connector.\n       * - `null`: Account MAY NOT exist on the connector. This is useful for\n       *   actions that can infer the account from the connector (e.g. sending a\n       *   call without a connected account – the user will be prompted to select\n       *   an account within the wallet).\n       */\n      account?: Address | Account | null | undefined\n    }\n>\n\nexport type GetConnectorClientReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  Client<\n    config['_internal']['transports'][chainId],\n    Extract<config['chains'][number], { id: chainId }>,\n    Account\n  >\n>\n\nexport type GetConnectorClientErrorType =\n  | ConnectorAccountNotFoundErrorType\n  | ConnectorChainMismatchErrorType\n  | ConnectorNotConnectedErrorType\n  | ConnectorUnavailableReconnectingErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n\n/** https://wagmi.sh/core/api/actions/getConnectorClient */\nexport async function getConnectorClient<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetConnectorClientParameters<config, chainId> = {},\n): Promise<GetConnectorClientReturnType<config, chainId>> {\n  // Get connection\n  let connection: Connection | undefined\n  if (parameters.connector) {\n    const { connector } = parameters\n    if (\n      config.state.status === 'reconnecting' &&\n      !connector.getAccounts &&\n      !connector.getChainId\n    )\n      throw new ConnectorUnavailableReconnectingError({ connector })\n\n    const [accounts, chainId] = await Promise.all([\n      connector.getAccounts().catch((e) => {\n        if (parameters.account === null) return []\n        throw e\n      }),\n      connector.getChainId(),\n    ])\n    connection = {\n      accounts: accounts as readonly [Address, ...Address[]],\n      chainId,\n      connector,\n    }\n  } else connection = config.state.connections.get(config.state.current!)\n  if (!connection) throw new ConnectorNotConnectedError()\n\n  const chainId = parameters.chainId ?? connection.chainId\n\n  // Check connector using same chainId as connection\n  const connectorChainId = await connection.connector.getChainId()\n  if (connectorChainId !== connection.chainId)\n    throw new ConnectorChainMismatchError({\n      connectionChainId: connection.chainId,\n      connectorChainId,\n    })\n\n  // If connector has custom `getClient` implementation\n  type Return = GetConnectorClientReturnType<config, chainId>\n  const connector = connection.connector\n  if (connector.getClient)\n    return connector.getClient({ chainId }) as unknown as Return\n\n  // Default using `custom` transport\n  const account = parseAccount(parameters.account ?? connection.accounts[0]!)\n  if (account) account.address = getAddress(account.address) // TODO: Checksum address as part of `parseAccount`?\n\n  // If account was provided, check that it exists on the connector\n  if (\n    parameters.account &&\n    !connection.accounts.some(\n      (x) => x.toLowerCase() === account.address.toLowerCase(),\n    )\n  )\n    throw new ConnectorAccountNotFoundError({\n      address: account.address,\n      connector,\n    })\n\n  const chain = config.chains.find((chain) => chain.id === chainId)\n  const provider = (await connection.connector.getProvider({ chainId })) as {\n    request(...args: any): Promise<any>\n  }\n\n  return createClient({\n    account,\n    chain,\n    name: 'Connector Client',\n    transport: (opts) => custom(provider)({ ...opts, retryCount: 0 }),\n  }) as Return\n}\n", "import type { Abi, Account, Chain, Client, ContractConstructorArgs } from 'viem'\nimport {\n  type DeployContractErrorType as viem_DeployContractErrorType,\n  type DeployContractParameters as viem_DeployContractParameters,\n  type DeployContractReturnType as viem_DeployContractReturnType,\n  deployContract as viem_deployContract,\n} from 'viem/actions'\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  ConnectorParameter,\n} from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type DeployContractParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  allArgs = ContractConstructorArgs<abi>,\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: Compute<\n    Omit<\n      viem_DeployContractParameters<\n        abi,\n        chains[key],\n        Account,\n        chains[key],\n        allArgs\n      >,\n      'chain'\n    > &\n      ChainIdParameter<config, chainId> &\n      ConnectorParameter\n  >\n}[number]\n\nexport type DeployContractReturnType = viem_DeployContractReturnType\n\nexport type DeployContractErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_DeployContractErrorType\n\n/** https://wagmi.sh/core/api/actions/deployContract */\nexport async function deployContract<\n  config extends Config,\n  const abi extends Abi | readonly unknown[],\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: DeployContractParameters<abi, config, chainId>,\n): Promise<DeployContractReturnType> {\n  const { account, chainId, connector, ...rest } = parameters\n\n  let client: Client\n  if (typeof account === 'object' && account?.type === 'local')\n    client = config.getClient({ chainId })\n  else\n    client = await getConnectorClient(config, {\n      account: account ?? undefined,\n      chainId,\n      connector,\n    })\n\n  const action = getAction(client, viem_deployContract, 'deployContract')\n  const hash = await action({\n    ...(rest as any),\n    ...(account ? { account } : {}),\n    chain: chainId ? { id: chainId } : null,\n  })\n\n  return hash\n}\n", "import type { Config, Connection, Connector } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type {\n  ConnectorNotConnectedErrorType,\n  ConnectorNotFoundErrorType,\n} from '../errors/config.js'\nimport type { ConnectorParameter } from '../types/properties.js'\n\nexport type DisconnectParameters = ConnectorParameter\n\nexport type DisconnectReturnType = void\n\nexport type DisconnectErrorType =\n  | ConnectorNotFoundErrorType\n  | ConnectorNotConnectedErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n\n/** https://wagmi.sh/core/api/actions/disconnect */\nexport async function disconnect(\n  config: Config,\n  parameters: DisconnectParameters = {},\n): Promise<DisconnectReturnType> {\n  let connector: Connector | undefined\n  if (parameters.connector) connector = parameters.connector\n  else {\n    const { connections, current } = config.state\n    const connection = connections.get(current!)\n    connector = connection?.connector\n  }\n\n  const connections = config.state.connections\n\n  if (connector) {\n    await connector.disconnect()\n    connector.emitter.off('change', config._internal.events.change)\n    connector.emitter.off('disconnect', config._internal.events.disconnect)\n    connector.emitter.on('connect', config._internal.events.connect)\n\n    connections.delete(connector.uid)\n  }\n\n  config.setState((x) => {\n    // if no connections exist, move to disconnected state\n    if (connections.size === 0)\n      return {\n        ...x,\n        connections: new Map(),\n        current: null,\n        status: 'disconnected',\n      }\n\n    // switch over to another connection\n    const nextConnection = connections.values().next().value as Connection\n    return {\n      ...x,\n      connections: new Map(connections),\n      current: nextConnection.connector.uid,\n    }\n  })\n\n  // Set recent connector if exists\n  {\n    const current = config.state.current\n    if (!current) return\n    const connector = config.state.connections.get(current)?.connector\n    if (!connector) return\n    await config.storage?.setItem('recentConnectorId', connector.id)\n  }\n}\n", "import type { Account, Address, Chain } from 'viem'\nimport {\n  type EstimateGasErrorType as viem_EstimateGasErrorType,\n  type EstimateGasParameters as viem_EstimateGasParameters,\n  type EstimateGasReturnType as viem_EstimateGasReturnType,\n  estimateGas as viem_estimateGas,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  ConnectorParameter,\n} from '../types/properties.js'\nimport type { UnionCompute, UnionLooseOmit } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type EstimateGasParameters<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: UnionCompute<\n    UnionLooseOmit<viem_EstimateGasParameters<chains[key]>, 'chain'> &\n      ChainIdParameter<config, chainId> &\n      ConnectorParameter\n  >\n}[number]\n\nexport type EstimateGasReturnType = viem_EstimateGasReturnType\n\nexport type EstimateGasErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_EstimateGasErrorType\n\n/** https://wagmi.sh/core/api/actions/estimateGas */\nexport async function estimateGas<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n>(\n  config: config,\n  parameters: EstimateGasParameters<config, chainId>,\n): Promise<EstimateGasReturnType> {\n  const { chainId, connector, ...rest } = parameters\n\n  let account: Address | Account\n  if (parameters.account) account = parameters.account\n  else {\n    const connectorClient = await getConnectorClient(config, {\n      account: parameters.account,\n      chainId,\n      connector,\n    })\n    account = connectorClient.account\n  }\n\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_estimateGas, 'estimateGas')\n  return action({ ...(rest as viem_EstimateGasParameters), account })\n}\n", "import { weiUnits } from 'viem'\n\nimport type { Unit } from '../types/unit.js'\n\nexport function getUnit(unit: Unit) {\n  if (typeof unit === 'number') return unit\n  if (unit === 'wei') return 0\n  return Math.abs(weiUnits[unit])\n}\n", "import {\n  type Chain,\n  type FeeValuesEIP1559,\n  type FeeValuesLegacy,\n  type FeeValuesType,\n  formatUnits,\n} from 'viem'\nimport {\n  type EstimateFeesPerGasErrorType as viem_EstimateFeesPerGasErrorType,\n  type EstimateFeesPerGasParameters as viem_EstimateFeesPerGasParameters,\n  type EstimateFeesPerGasReturnType as viem_EstimateFeesPerGasReturnType,\n  estimateFeesPerGas as viem_estimateFeesPerGas,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Unit } from '../types/unit.js'\nimport type { Compute } from '../types/utils.js'\nimport type { UnionCompute, UnionLooseOmit } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport { getUnit } from '../utils/getUnit.js'\n\nexport type EstimateFeesPerGasParameters<\n  type extends FeeValuesType = FeeValuesType,\n  config extends Config = Config,\n> = UnionCompute<\n  UnionLooseOmit<\n    viem_EstimateFeesPerGasParameters<Chain, Chain, type>,\n    'chain'\n  > &\n    ChainIdParameter<config> & {\n      /** @deprecated */\n      formatUnits?: Unit | undefined\n    }\n>\n\nexport type EstimateFeesPerGasReturnType<\n  type extends FeeValuesType = FeeValuesType,\n> = Compute<\n  viem_EstimateFeesPerGasReturnType<type> & {\n    /** @deprecated */\n    formatted: UnionCompute<\n      | (type extends 'legacy' ? FeeValuesLegacy<string> : never)\n      | (type extends 'eip1559' ? FeeValuesEIP1559<string> : never)\n    >\n  }\n>\n\nexport type EstimateFeesPerGasErrorType = viem_EstimateFeesPerGasErrorType\n\nexport async function estimateFeesPerGas<\n  config extends Config,\n  type extends FeeValuesType = 'eip1559',\n>(\n  config: config,\n  parameters: EstimateFeesPerGasParameters<type, config> = {},\n): Promise<EstimateFeesPerGasReturnType<type>> {\n  const { chainId, formatUnits: units = 'gwei', ...rest } = parameters\n\n  const client = config.getClient({ chainId })\n  const action = getAction(\n    client,\n    viem_estimateFeesPerGas,\n    'estimateFeesPerGas',\n  )\n\n  const { gasPrice, maxFeePerGas, maxPriorityFeePerGas } = await action({\n    ...rest,\n    chain: client.chain,\n  })\n\n  const unit = getUnit(units)\n  const formatted = {\n    gasPrice: gasPrice ? formatUnits(gasPrice, unit) : undefined,\n    maxFeePerGas: maxFeePerGas ? formatUnits(maxFeePerGas, unit) : undefined,\n    maxPriorityFeePerGas: maxPriorityFeePerGas\n      ? formatUnits(maxPriorityFeePerGas, unit)\n      : undefined,\n  }\n\n  return {\n    formatted,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n  } as EstimateFeesPerGasReturnType<type>\n}\n", "import type { Chain } from 'viem'\nimport {\n  type EstimateMaxPriorityFeePerGasErrorType as viem_EstimateMaxPriorityFeePerGasErrorType,\n  type EstimateMaxPriorityFeePerGasParameters as viem_EstimateMaxPriorityFeePerGasParameters,\n  type EstimateMaxPriorityFeePerGasReturnType as viem_EstimateMaxPriorityFeePerGasReturnType,\n  estimateMaxPriorityFeePerGas as viem_estimateMaxPriorityFeePerGas,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute, UnionLooseOmit } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type EstimateMaxPriorityFeePerGasParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  UnionLooseOmit<\n    viem_EstimateMaxPriorityFeePerGasParameters<Chain, Chain> &\n      ChainIdParameter<config, chainId>,\n    'chain'\n  >\n>\n\nexport type EstimateMaxPriorityFeePerGasReturnType =\n  viem_EstimateMaxPriorityFeePerGasReturnType\n\nexport type EstimateMaxPriorityFeePerGasErrorType =\n  viem_EstimateMaxPriorityFeePerGasErrorType\n\n/** https://wagmi.sh/core/api/actions/estimateMaxPriorityFeePerGas */\nexport async function estimateMaxPriorityFeePerGas<\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: EstimateMaxPriorityFeePerGasParameters<config, chainId> = {},\n): Promise<EstimateMaxPriorityFeePerGasReturnType> {\n  const { chainId } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(\n    client,\n    viem_estimateMaxPriorityFeePerGas,\n    'estimateMaxPriorityFeePerGas',\n  )\n  return action({ chain: client.chain })\n}\n", "import type { Address, Chain } from 'viem'\n\nimport type { Config, Connector } from '../createConfig.js'\n\nexport type GetAccountReturnType<\n  config extends Config = Config,\n  ///\n  chain = Config extends config ? Chain : config['chains'][number],\n> =\n  | {\n      address: Address\n      addresses: readonly [Address, ...Address[]]\n      chain: chain | undefined\n      chainId: number\n      connector: Connector\n      isConnected: true\n      isConnecting: false\n      isDisconnected: false\n      isReconnecting: false\n      status: 'connected'\n    }\n  | {\n      address: Address | undefined\n      addresses: readonly Address[] | undefined\n      chain: chain | undefined\n      chainId: number | undefined\n      connector: Connector | undefined\n      isConnected: boolean\n      isConnecting: false\n      isDisconnected: false\n      isReconnecting: true\n      status: 'reconnecting'\n    }\n  | {\n      address: Address | undefined\n      addresses: readonly Address[] | undefined\n      chain: chain | undefined\n      chainId: number | undefined\n      connector: Connector | undefined\n      isConnected: false\n      isReconnecting: false\n      isConnecting: true\n      isDisconnected: false\n      status: 'connecting'\n    }\n  | {\n      address: undefined\n      addresses: undefined\n      chain: undefined\n      chainId: undefined\n      connector: undefined\n      isConnected: false\n      isReconnecting: false\n      isConnecting: false\n      isDisconnected: true\n      status: 'disconnected'\n    }\n\n/** https://wagmi.sh/core/api/actions/getAccount */\nexport function getAccount<config extends Config>(\n  config: config,\n): GetAccountReturnType<config> {\n  const uid = config.state.current!\n  const connection = config.state.connections.get(uid)\n  const addresses = connection?.accounts\n  const address = addresses?.[0]\n  const chain = config.chains.find(\n    (chain) => chain.id === connection?.chainId,\n  ) as GetAccountReturnType<config>['chain']\n  const status = config.state.status\n\n  switch (status) {\n    case 'connected':\n      return {\n        address: address!,\n        addresses: addresses!,\n        chain,\n        chainId: connection?.chainId!,\n        connector: connection?.connector!,\n        isConnected: true,\n        isConnecting: false,\n        isDisconnected: false,\n        isReconnecting: false,\n        status,\n      }\n    case 'reconnecting':\n      return {\n        address,\n        addresses,\n        chain,\n        chainId: connection?.chainId,\n        connector: connection?.connector,\n        isConnected: !!address,\n        isConnecting: false,\n        isDisconnected: false,\n        isReconnecting: true,\n        status,\n      }\n    case 'connecting':\n      return {\n        address,\n        addresses,\n        chain,\n        chainId: connection?.chainId,\n        connector: connection?.connector,\n        isConnected: false,\n        isConnecting: true,\n        isDisconnected: false,\n        isReconnecting: false,\n        status,\n      }\n    case 'disconnected':\n      return {\n        address: undefined,\n        addresses: undefined,\n        chain: undefined,\n        chainId: undefined,\n        connector: undefined,\n        isConnected: false,\n        isConnecting: false,\n        isDisconnected: true,\n        isReconnecting: false,\n        status,\n      }\n  }\n}\n", "import type {\n  ContractFunctionParameters,\n  MulticallErrorType as viem_MulticallErrorType,\n  MulticallParameters as viem_MulticallParameters,\n  MulticallReturnType as viem_MulticallReturnType,\n} from 'viem'\nimport { multicall as viem_multicall } from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type MulticallParameters<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n  config extends Config = Config,\n> = viem_MulticallParameters<contracts, allowFailure> & ChainIdParameter<config>\n\nexport type MulticallReturnType<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n> = viem_MulticallReturnType<contracts, allowFailure>\n\nexport type MulticallErrorType = viem_MulticallErrorType\n\nexport async function multicall<\n  config extends Config,\n  const contracts extends readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n>(\n  config: config,\n  parameters: MulticallParameters<contracts, allowFailure, config>,\n): Promise<MulticallReturnType<contracts, allowFailure>> {\n  const { allowFailure = true, chainId, contracts, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_multicall, 'multicall')\n  return action({\n    allowFailure,\n    contracts,\n    ...rest,\n  }) as Promise<MulticallReturnType<contracts, allowFailure>>\n}\n", "import type { Abi } from 'viem'\nimport type { ContractFunctionArgs, ContractFunctionName } from 'viem'\nimport {\n  type ReadContractErrorType as viem_ReadContractErrorType,\n  type ReadContractParameters as viem_ReadContractParameters,\n  type ReadContractReturnType as viem_ReadContractReturnType,\n  readContract as viem_readContract,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type ReadContractParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'pure' | 'view'\n  > = ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'pure' | 'view',\n    functionName\n  > = ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n  config extends Config = Config,\n> = viem_ReadContractParameters<abi, functionName, args> &\n  ChainIdParameter<config>\n\nexport type ReadContractReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'pure' | 'view'\n  > = ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'pure' | 'view',\n    functionName\n  > = ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n> = viem_ReadContractReturnType<abi, functionName, args>\n\nexport type ReadContractErrorType = viem_ReadContractErrorType\n\n/** https://wagmi.sh/core/api/actions/readContract */\nexport function readContract<\n  config extends Config,\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n>(\n  config: config,\n  parameters: ReadContractParameters<abi, functionName, args, config>,\n): Promise<ReadContractReturnType<abi, functionName, args>> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_readContract, 'readContract')\n  return action(rest as any)\n}\n", "import type {\n  ContractFunctionParameters,\n  MulticallParameters as viem_MulticallParameters,\n  MulticallReturnType as viem_MulticallReturnType,\n} from 'viem'\nimport { ContractFunctionExecutionError } from 'viem'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport { type MulticallErrorType, multicall } from './multicall.js'\nimport { type ReadContractErrorType, readContract } from './readContract.js'\n\nexport type ReadContractsParameters<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n  config extends Config = Config,\n> = viem_MulticallParameters<\n  contracts,\n  allowFailure,\n  { properties: ChainIdParameter<config> }\n>\n\nexport type ReadContractsReturnType<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n> = viem_MulticallReturnType<contracts, allowFailure>\n\nexport type ReadContractsErrorType = MulticallErrorType | ReadContractErrorType\n\nexport async function readContracts<\n  config extends Config,\n  const contracts extends readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n>(\n  config: config,\n  parameters: ReadContractsParameters<contracts, allowFailure, config>,\n): Promise<ReadContractsReturnType<contracts, allowFailure>> {\n  const { allowFailure = true, blockNumber, blockTag, ...rest } = parameters\n  const contracts = parameters.contracts as (ContractFunctionParameters & {\n    chainId?: number | undefined\n  })[]\n\n  try {\n    const contractsByChainId: {\n      [chainId: number]: {\n        contract: ContractFunctionParameters\n        index: number\n      }[]\n    } = {}\n    for (const [index, contract] of contracts.entries()) {\n      const chainId = contract.chainId ?? config.state.chainId\n      if (!contractsByChainId[chainId]) contractsByChainId[chainId] = []\n      contractsByChainId[chainId]?.push({ contract, index })\n    }\n    const promises = () =>\n      Object.entries(contractsByChainId).map(([chainId, contracts]) =>\n        multicall(config, {\n          ...rest,\n          allowFailure,\n          blockNumber,\n          blockTag,\n          chainId: Number.parseInt(chainId),\n          contracts: contracts.map(({ contract }) => contract),\n        }),\n      )\n\n    const multicallResults = (await Promise.all(promises())).flat()\n    // Reorder the contract results back to the order they were\n    // provided in.\n    const resultIndexes = Object.values(contractsByChainId).flatMap(\n      (contracts) => contracts.map(({ index }) => index),\n    )\n    return multicallResults.reduce((results, result, index) => {\n      if (results) (results as unknown[])[resultIndexes[index]!] = result\n      return results\n    }, [] as unknown[]) as ReadContractsReturnType<contracts, allowFailure>\n  } catch (error) {\n    if (error instanceof ContractFunctionExecutionError) throw error\n\n    const promises = () =>\n      contracts.map((contract) =>\n        readContract(config, { ...contract, blockNumber, blockTag }),\n      )\n    if (allowFailure)\n      return (await Promise.allSettled(promises())).map((result) => {\n        if (result.status === 'fulfilled')\n          return { result: result.value, status: 'success' }\n        return { error: result.reason, result: undefined, status: 'failure' }\n      }) as ReadContractsReturnType<contracts, allowFailure>\n\n    return (await Promise.all(promises())) as ReadContractsReturnType<\n      contracts,\n      allowFailure\n    >\n  }\n}\n", "import { type Address, type Hex, formatUnits, hexToString, trim } from 'viem'\nimport {\n  type GetBalanceErrorType as viem_GetBalanceErrorType,\n  type GetBalanceParameters as viem_GetBalanceParameters,\n  getBalance as viem_getBalance,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Unit } from '../types/unit.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport { getUnit } from '../utils/getUnit.js'\nimport { type ReadContractsErrorType, readContracts } from './readContracts.js'\n\nexport type GetBalanceParameters<config extends Config = Config> = Compute<\n  ChainIdParameter<config> &\n    viem_GetBalanceParameters & {\n      /** @deprecated */\n      token?: Address | undefined\n      /** @deprecated */\n      unit?: Unit | undefined\n    }\n>\n\nexport type GetBalanceReturnType = {\n  decimals: number\n  /** @deprecated */\n  formatted: string\n  symbol: string\n  value: bigint\n}\n\nexport type GetBalanceErrorType = viem_GetBalanceErrorType\n\n/** https://wagmi.sh/core/api/actions/getBalance */\nexport async function getBalance<config extends Config>(\n  config: config,\n  parameters: GetBalanceParameters<config>,\n): Promise<GetBalanceReturnType> {\n  const {\n    address,\n    blockNumber,\n    blockTag,\n    chainId,\n    token: tokenAddress,\n    unit = 'ether',\n  } = parameters\n\n  if (tokenAddress) {\n    try {\n      return await getTokenBalance(config, {\n        balanceAddress: address,\n        chainId,\n        symbolType: 'string',\n        tokenAddress,\n      })\n    } catch (error) {\n      // In the chance that there is an error upon decoding the contract result,\n      // it could be likely that the contract data is represented as bytes32 instead\n      // of a string.\n      if (\n        (error as ReadContractsErrorType).name ===\n        'ContractFunctionExecutionError'\n      ) {\n        const balance = await getTokenBalance(config, {\n          balanceAddress: address,\n          chainId,\n          symbolType: 'bytes32',\n          tokenAddress,\n        })\n        const symbol = hexToString(\n          trim(balance.symbol as Hex, { dir: 'right' }),\n        )\n        return { ...balance, symbol }\n      }\n      throw error\n    }\n  }\n\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getBalance, 'getBalance')\n  const value = await action(\n    blockNumber ? { address, blockNumber } : { address, blockTag },\n  )\n  const chain = config.chains.find((x) => x.id === chainId) ?? client.chain!\n  return {\n    decimals: chain.nativeCurrency.decimals,\n    formatted: formatUnits(value, getUnit(unit)),\n    symbol: chain.nativeCurrency.symbol,\n    value,\n  }\n}\n\ntype GetTokenBalanceParameters = {\n  balanceAddress: Address\n  chainId?: number | undefined\n  symbolType: 'bytes32' | 'string'\n  tokenAddress: Address\n  unit?: Unit | undefined\n}\n\nasync function getTokenBalance(\n  config: Config,\n  parameters: GetTokenBalanceParameters,\n) {\n  const { balanceAddress, chainId, symbolType, tokenAddress, unit } = parameters\n  const contract = {\n    abi: [\n      {\n        type: 'function',\n        name: 'balanceOf',\n        stateMutability: 'view',\n        inputs: [{ type: 'address' }],\n        outputs: [{ type: 'uint256' }],\n      },\n      {\n        type: 'function',\n        name: 'decimals',\n        stateMutability: 'view',\n        inputs: [],\n        outputs: [{ type: 'uint8' }],\n      },\n      {\n        type: 'function',\n        name: 'symbol',\n        stateMutability: 'view',\n        inputs: [],\n        outputs: [{ type: symbolType }],\n      },\n    ],\n    address: tokenAddress,\n  } as const\n  const [value, decimals, symbol] = await readContracts(config, {\n    allowFailure: false,\n    contracts: [\n      {\n        ...contract,\n        functionName: 'balanceOf',\n        args: [balanceAddress],\n        chainId,\n      },\n      { ...contract, functionName: 'decimals', chainId },\n      { ...contract, functionName: 'symbol', chainId },\n    ] as const,\n  })\n  const formatted = formatUnits(value ?? '0', getUnit(unit ?? decimals))\n  return { decimals, formatted, symbol, value }\n}\n", "import type { BlockTag, Chain } from 'viem'\nimport {\n  type GetBlockErrorType as viem_GetBlockErrorType,\n  type GetBlockParameters as viem_GetBlockParameters,\n  type GetBlockReturnType as viem_GetBlockReturnType,\n  getBlock as viem_getBlock,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute, IsNarrowable } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetBlockParameters<\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  viem_GetBlockParameters<includeTransactions, blockTag> &\n    ChainIdParameter<config, chainId>\n>\n\nexport type GetBlockReturnType<\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = Compute<\n  {\n    [key in keyof chains]: viem_GetBlockReturnType<\n      IsNarrowable<chains[key], Chain> extends true ? chains[key] : undefined,\n      includeTransactions,\n      blockTag\n    > & { chainId: chains[key]['id'] }\n  }[number]\n>\n\nexport type GetBlockErrorType = viem_GetBlockErrorType\n\n/** https://wagmi.sh/core/actions/getBlock */\nexport async function getBlock<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n>(\n  config: config,\n  parameters: GetBlockParameters<\n    includeTransactions,\n    blockTag,\n    config,\n    chainId\n  > = {},\n): Promise<GetBlockReturnType<includeTransactions, blockTag, config, chainId>> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getBlock, 'getBlock')\n  const block = await action(rest)\n  return {\n    ...(block as unknown as GetBlockReturnType<\n      includeTransactions,\n      blockTag,\n      config,\n      chainId\n    >),\n    chainId: client.chain.id,\n  }\n}\n", "import {\n  type GetBlockNumberErrorType as viem_GetBlockNumberErrorType,\n  type GetBlockNumberParameters as viem_GetBlockNumberParameters,\n  type GetBlockNumberReturnType as viem_GetBlockNumberReturnType,\n  getBlockNumber as viem_getBlockNumber,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetBlockNumberParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<viem_GetBlockNumberParameters & ChainIdParameter<config, chainId>>\n\nexport type GetBlockNumberReturnType = viem_GetBlockNumberReturnType\n\nexport type GetBlockNumberErrorType = viem_GetBlockNumberErrorType\n\n/** https://wagmi.sh/core/api/actions/getBlockNumber */\nexport function getBlockNumber<\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetBlockNumberParameters<config, chainId> = {},\n): Promise<GetBlockNumberReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getBlockNumber, 'getBlockNumber')\n  return action(rest)\n}\n", "import {\n  type GetBlockTransactionCountErrorType as viem_GetBlockTransactionCountErrorType,\n  type GetBlockTransactionCountParameters as viem_GetBlockTransactionCountParameters,\n  type GetBlockTransactionCountReturnType as viem_GetBlockTransactionCountReturnType,\n  getBlockTransactionCount as viem_getBlockTransactionCount,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { UnionCompute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetBlockTransactionCountParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = UnionCompute<\n  viem_GetBlockTransactionCountParameters & ChainIdParameter<config, chainId>\n>\n\nexport type GetBlockTransactionCountReturnType =\n  viem_GetBlockTransactionCountReturnType\n\nexport type GetBlockTransactionCountErrorType =\n  viem_GetBlockTransactionCountErrorType\n\n/** https://wagmi.sh/core/api/actions/getBlockTransactionCount */\nexport function getBlockTransactionCount<\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetBlockTransactionCountParameters<config, chainId> = {},\n): Promise<GetBlockTransactionCountReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(\n    client,\n    viem_getBlockTransactionCount,\n    'getBlockTransactionCount',\n  )\n  return action(rest)\n}\n", "import {\n  type GetBytecodeErrorType as viem_GetBytecodeErrorType,\n  type GetBytecodeParameters as viem_GetBytecodeParameters,\n  type GetBytecodeReturnType as viem_GetBytecodeReturnType,\n  getBytecode as viem_getBytecode,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetBytecodeParameters<config extends Config = Config> = Compute<\n  viem_GetBytecodeParameters & ChainIdParameter<config>\n>\n\nexport type GetBytecodeReturnType = viem_GetBytecodeReturnType\n\nexport type GetBytecodeErrorType = viem_GetBytecodeErrorType\n\n/** https://wagmi.sh/core/api/actions/getBytecode */\nexport async function getBytecode<config extends Config>(\n  config: config,\n  parameters: GetBytecodeParameters<config>,\n): Promise<GetBytecodeReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getBytecode, 'getBytecode')\n  return action(rest)\n}\n", "import {\n  type GetCallsStatusErrorType as viem_GetCallsStatusErrorType,\n  type GetCallsStatusParameters as viem_GetCallsStatusParameters,\n  type GetCallsStatusReturnType as viem_GetCallsStatusReturnType,\n  getCallsStatus as viem_getCallsStatus,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport { getConnectorClient } from './getConnectorClient.js'\n\nexport type GetCallsStatusParameters = viem_GetCallsStatusParameters &\n  ConnectorParameter\n\nexport type GetCallsStatusReturnType = viem_GetCallsStatusReturnType\n\nexport type GetCallsStatusErrorType = viem_GetCallsStatusErrorType\n\n/** https://wagmi.sh/core/api/actions/getCallsStatus */\nexport async function getCallsStatus<config extends Config>(\n  config: config,\n  parameters: GetCallsStatusParameters,\n): Promise<GetCallsStatusReturnType> {\n  const { connector, id } = parameters\n  const client = await getConnectorClient(config, { connector })\n  return viem_getCallsStatus(client, { id })\n}\n", "import type { Account } from 'viem'\nimport {\n  type GetCapabilitiesErrorType as viem_GetCapabilitiesErrorType,\n  type GetCapabilitiesParameters as viem_GetCapabilitiesParameters,\n  type GetCapabilitiesReturnType as viem_GetCapabilitiesReturnType,\n  getCapabilities as viem_getCapabilities,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport { getConnectorClient } from './getConnectorClient.js'\n\nexport type GetCapabilitiesParameters<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n> = viem_GetCapabilitiesParameters<chainId> & ConnectorParameter\n\nexport type GetCapabilitiesReturnType<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n> = viem_GetCapabilitiesReturnType<chainId>\n\nexport type GetCapabilitiesErrorType = viem_GetCapabilitiesErrorType\n\n/** https://wagmi.sh/core/api/actions/getCapabilities */\nexport async function getCapabilities<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n>(\n  config: config,\n  parameters: GetCapabilitiesParameters<config, chainId> = {},\n): Promise<GetCapabilitiesReturnType<config, chainId>> {\n  const { account, chainId, connector } = parameters\n  const client = await getConnectorClient(config, { account, connector })\n  return viem_getCapabilities(client as any, {\n    account: account as Account,\n    chainId,\n  })\n}\n", "import type { Config } from '../createConfig.js'\n\nexport type GetChainIdReturnType<config extends Config = Config> =\n  config['chains'][number]['id']\n\n/** https://wagmi.sh/core/api/actions/getChainId */\nexport function getChainId<config extends Config>(\n  config: config,\n): GetChainIdReturnType<config> {\n  return config.state.chainId\n}\n", "import type { Chain } from 'viem'\nimport type { Config } from '../createConfig.js'\nimport { deepEqual } from '../utils/deepEqual.js'\n\nexport type GetChainsReturnType<config extends Config = Config> = readonly [\n  ...config['chains'],\n  ...Chain[],\n]\n\nlet previousChains: readonly Chain[] = []\n\n/** https://wagmi.sh/core/api/actions/getChains */\nexport function getChains<config extends Config>(\n  config: config,\n): GetChainsReturnType<config> {\n  const chains = config.chains\n  if (deepEqual(previousChains, chains))\n    return previousChains as GetChainsReturnType<config>\n  previousChains = chains\n  return chains as unknown as GetChainsReturnType<config>\n}\n", "import type { Client } from 'viem'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute, IsNarrowable } from '../types/utils.js'\n\nexport type GetClientParameters<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | number\n    | undefined = config['chains'][number]['id'],\n> = ChainIdParameter<config, chainId>\n\nexport type GetClientReturnType<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  ///\n  resolvedChainId extends\n    | config['chains'][number]['id']\n    | undefined = IsNarrowable<\n    config['chains'][number]['id'],\n    number\n  > extends true\n    ? IsNarrowable<chainId, number> extends true\n      ? chainId\n      : config['chains'][number]['id']\n    : config['chains'][number]['id'] | undefined,\n> = resolvedChainId extends config['chains'][number]['id']\n  ? Compute<\n      Client<\n        config['_internal']['transports'][resolvedChainId],\n        Extract<config['chains'][number], { id: resolvedChainId }>\n      >\n    >\n  : undefined\n\nexport function getClient<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | number | undefined,\n>(\n  config: config,\n  parameters: GetClientParameters<config, chainId> = {},\n): GetClientReturnType<config, chainId> {\n  let client = undefined\n  try {\n    client = config.getClient(parameters)\n  } catch {}\n  return client as GetClientReturnType<config, chainId>\n}\n", "import type { Config, Connection } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\nimport { deepEqual } from '../utils/deepEqual.js'\n\nexport type GetConnectionsReturnType = Compute<Connection>[]\n\nlet previousConnections: Connection[] = []\n\n/** https://wagmi.sh/core/api/actions/getConnections */\nexport function getConnections(config: Config): GetConnectionsReturnType {\n  const connections = [...config.state.connections.values()]\n  if (config.state.status === 'reconnecting') return previousConnections\n  if (deepEqual(previousConnections, connections)) return previousConnections\n  previousConnections = connections\n  return connections\n}\n", "import type { Config, Connector } from '../createConfig.js'\nimport { deepEqual } from '../utils/deepEqual.js'\n\nexport type GetConnectorsReturnType<config extends Config = Config> =\n  config['connectors']\n\nlet previousConnectors: readonly Connector[] = []\n\n/** https://wagmi.sh/core/api/actions/getConnectors */\nexport function getConnectors<config extends Config>(\n  config: config,\n): GetConnectorsReturnType<config> {\n  const connectors = config.connectors\n  if (deepEqual(previousConnectors, connectors)) return previousConnectors\n  previousConnectors = connectors\n  return connectors\n}\n", "import {\n  type GetEnsAddressErrorType as viem_GetEnsAddressErrorType,\n  type GetEnsAddressParameters as viem_GetEnsAddressParameters,\n  type GetEnsAddressReturnType as viem_GetEnsAddressReturnType,\n  getEnsAddress as viem_getEnsAddress,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetEnsAddressParameters<config extends Config = Config> = Compute<\n  viem_GetEnsAddressParameters & ChainIdParameter<config>\n>\n\nexport type GetEnsAddressReturnType = viem_GetEnsAddressReturnType\n\nexport type GetEnsAddressErrorType = viem_GetEnsAddressErrorType\n\n/** https://wagmi.sh/core/api/actions/getEnsAddress */\nexport function getEnsAddress<config extends Config>(\n  config: config,\n  parameters: GetEnsAddressParameters<config>,\n): Promise<GetEnsAddressReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getEnsAddress, 'getEnsAddress')\n  return action(rest)\n}\n", "import {\n  type GetEnsAvatarErrorType as viem_GetEnsAvatarErrorType,\n  type GetEnsAvatarParameters as viem_GetEnsAvatarParameters,\n  type GetEnsAvatarReturnType as viem_GetEnsAvatarReturnType,\n  getEnsAvatar as viem_getEnsAvatar,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetEnsAvatarParameters<config extends Config = Config> = Compute<\n  viem_GetEnsAvatarParameters & ChainIdParameter<config>\n>\n\nexport type GetEnsAvatarReturnType = viem_GetEnsAvatarReturnType\n\nexport type GetEnsAvatarErrorType = viem_GetEnsAvatarErrorType\n\n/** https://wagmi.sh/core/api/actions/getEnsAvatar */\nexport function getEnsAvatar<config extends Config>(\n  config: config,\n  parameters: GetEnsAvatarParameters<config>,\n): Promise<GetEnsAvatarReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getEnsAvatar, 'getEnsAvatar')\n  return action(rest)\n}\n", "import {\n  type GetEnsNameErrorType as viem_GetEnsNameErrorType,\n  type GetEnsNameParameters as viem_GetEnsNameParameters,\n  type GetEnsNameReturnType as viem_GetEnsNameReturnType,\n  getEnsName as viem_getEnsName,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetEnsNameParameters<config extends Config = Config> = Compute<\n  viem_GetEnsNameParameters & ChainIdParameter<config>\n>\n\nexport type GetEnsNameReturnType = viem_GetEnsNameReturnType\n\nexport type GetEnsNameErrorType = viem_GetEnsNameErrorType\n\n/** https://wagmi.sh/core/api/actions/getEnsName */\nexport function getEnsName<config extends Config>(\n  config: config,\n  parameters: GetEnsNameParameters<config>,\n): Promise<GetEnsNameReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getEnsName, 'getEnsName')\n  return action(rest)\n}\n", "import {\n  type GetEnsResolverErrorType as viem_GetEnsResolverErrorType,\n  type GetEnsResolverParameters as viem_GetEnsResolverParameters,\n  type GetEnsResolverReturnType as viem_GetEnsResolverReturnType,\n  getEnsResolver as viem_getEnsResolver,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetEnsResolverParameters<config extends Config = Config> = Compute<\n  viem_GetEnsResolverParameters & ChainIdParameter<config>\n>\n\nexport type GetEnsResolverReturnType = viem_GetEnsResolverReturnType\n\nexport type GetEnsResolverErrorType = viem_GetEnsResolverErrorType\n\n/** https://wagmi.sh/core/api/actions/getEnsResolver */\nexport function getEnsResolver<config extends Config>(\n  config: config,\n  parameters: GetEnsResolverParameters<config>,\n): Promise<GetEnsResolverReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getEnsResolver, 'getEnsResolver')\n  return action(rest)\n}\n", "import {\n  type GetEnsTextErrorType as viem_GetEnsTextErrorType,\n  type GetEnsTextParameters as viem_GetEnsTextParameters,\n  type GetEnsTextReturnType as viem_GetEnsTextReturnType,\n  getEnsText as viem_getEnsText,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetEnsTextParameters<config extends Config = Config> = Compute<\n  viem_GetEnsTextParameters & ChainIdParameter<config>\n>\n\nexport type GetEnsTextReturnType = viem_GetEnsTextReturnType\n\nexport type GetEnsTextErrorType = viem_GetEnsTextErrorType\n\n/** https://wagmi.sh/core/api/actions/getEnsText */\nexport function getEnsText<config extends Config>(\n  config: config,\n  parameters: GetEnsTextParameters<config>,\n): Promise<GetEnsTextReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getEnsText, 'getEnsText')\n  return action(rest)\n}\n", "import {\n  type GetFeeHistoryErrorType as viem_GetFeeHistoryErrorType,\n  type GetFeeHistoryParameters as viem_GetFeeHistoryParameters,\n  type GetFeeHistoryReturnType as viem_GetFeeHistoryReturnType,\n  getFeeHistory as viem_getFeeHistory,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetFeeHistoryParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<viem_GetFeeHistoryParameters & ChainIdParameter<config, chainId>>\n\nexport type GetFeeHistoryReturnType = viem_GetFeeHistoryReturnType\n\nexport type GetFeeHistoryErrorType = viem_GetFeeHistoryErrorType\n\n/** https://wagmi.sh/core/api/actions/getFeeHistory */\nexport function getFeeHistory<\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetFeeHistoryParameters<config, chainId>,\n): Promise<GetFeeHistoryReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getFeeHistory, 'getFeeHistory')\n  return action(rest)\n}\n", "import {\n  type GetGasPriceErrorType as viem_GetGasPriceErrorType,\n  type GetGasPriceReturnType as viem_GetGasPriceReturnType,\n  getGasPrice as viem_getGasPrice,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetGasPriceParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<ChainIdParameter<config, chainId>>\n\nexport type GetGasPriceReturnType = viem_GetGasPriceReturnType\n\nexport type GetGasPriceErrorType = viem_GetGasPriceErrorType\n\n/** https://wagmi.sh/core/api/actions/getGasPrice */\nexport function getGasPrice<\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetGasPriceParameters<config, chainId> = {},\n): Promise<GetGasPriceReturnType> {\n  const { chainId } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getGasPrice, 'getGasPrice')\n  return action({})\n}\n", "import {\n  type GetProofErrorType as viem_GetProofErrorType,\n  type GetProofParameters as viem_GetProofParameters,\n  type GetProofReturnType as viem_GetProofReturnType,\n  getProof as viem_getProof,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetProofParameters<config extends Config = Config> = Compute<\n  viem_GetProofParameters & ChainIdParameter<config>\n>\n\nexport type GetProofReturnType = viem_GetProofReturnType\n\nexport type GetProofErrorType = viem_GetProofErrorType\n\n/** https://wagmi.sh/core/api/actions/getProof */\nexport async function getProof<config extends Config>(\n  config: config,\n  parameters: GetProofParameters<config>,\n): Promise<GetProofReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getProof, 'getProof')\n  return action(rest)\n}\n", "import { type Client, type PublicClient, publicActions } from 'viem'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute, IsNarrowable } from '../types/utils.js'\nimport { getClient } from './getClient.js'\n\nexport type GetPublicClientParameters<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n> = ChainIdParameter<config, chainId>\n\nexport type GetPublicClientReturnType<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  ///\n  resolvedChainId extends\n    | config['chains'][number]['id']\n    | undefined = IsNarrowable<\n    config['chains'][number]['id'],\n    number\n  > extends true\n    ? IsNarrowable<chainId, number> extends true\n      ? chainId\n      : config['chains'][number]['id']\n    : config['chains'][number]['id'] | undefined,\n> = resolvedChainId extends config['chains'][number]['id']\n  ? Compute<\n      PublicClient<\n        config['_internal']['transports'][resolvedChainId],\n        Extract<config['chains'][number], { id: resolvedChainId }>\n      >\n    >\n  : undefined\n\nexport function getPublicClient<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | number | undefined,\n>(\n  config: config,\n  parameters: GetPublicClientParameters<config, chainId> = {},\n): GetPublicClientReturnType<config, chainId> {\n  const client = getClient(config, parameters)\n  return (client as Client)?.extend(publicActions) as GetPublicClientReturnType<\n    config,\n    chainId\n  >\n}\n", "import {\n  type GetStorageAtErrorType as viem_GetStorageAtErrorType,\n  type GetStorageAtParameters as viem_GetStorageAtParameters,\n  type GetStorageAtReturnType as viem_GetStorageAtReturnType,\n  getStorageAt as viem_getStorageAt,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetStorageAtParameters<config extends Config = Config> = Compute<\n  viem_GetStorageAtParameters & ChainIdParameter<config>\n>\n\nexport type GetStorageAtReturnType = viem_GetStorageAtReturnType\n\nexport type GetStorageAtErrorType = viem_GetStorageAtErrorType\n\n/** https://wagmi.sh/core/api/actions/getStorageAt */\nexport async function getStorageAt<config extends Config>(\n  config: config,\n  parameters: GetStorageAtParameters<config>,\n): Promise<GetStorageAtReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getStorageAt, 'getStorageAt')\n  return action(rest)\n}\n", "import type { Address, Hex } from 'viem'\nimport {\n  ContractFunctionExecutionError,\n  formatUnits,\n  hexToString,\n  trim,\n} from 'viem'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Unit } from '../types/unit.js'\nimport type { Compute } from '../types/utils.js'\nimport { getUnit } from '../utils/getUnit.js'\nimport { type ReadContractsErrorType, readContracts } from './readContracts.js'\n\nexport type GetTokenParameters<config extends Config = Config> = Compute<\n  ChainIdParameter<config> & {\n    address: Address\n    formatUnits?: Unit | undefined\n  }\n>\n\nexport type GetTokenReturnType = {\n  address: Address\n  decimals: number\n  name: string | undefined\n  symbol: string | undefined\n  totalSupply: {\n    formatted: string\n    value: bigint\n  }\n}\n\nexport type GetTokenErrorType = ReadContractsErrorType\n\n/** @deprecated */\nexport async function getToken<config extends Config>(\n  config: config,\n  parameters: GetTokenParameters<config>,\n): Promise<GetTokenReturnType> {\n  const { address, chainId, formatUnits: unit = 18 } = parameters\n\n  function getAbi<type extends 'bytes32' | 'string'>(type: type) {\n    return [\n      {\n        type: 'function',\n        name: 'decimals',\n        stateMutability: 'view',\n        inputs: [],\n        outputs: [{ type: 'uint8' }],\n      },\n      {\n        type: 'function',\n        name: 'name',\n        stateMutability: 'view',\n        inputs: [],\n        outputs: [{ type }],\n      },\n      {\n        type: 'function',\n        name: 'symbol',\n        stateMutability: 'view',\n        inputs: [],\n        outputs: [{ type }],\n      },\n      {\n        type: 'function',\n        name: 'totalSupply',\n        stateMutability: 'view',\n        inputs: [],\n        outputs: [{ type: 'uint256' }],\n      },\n    ] as const\n  }\n\n  try {\n    const abi = getAbi('string')\n    const contractConfig = { address, abi, chainId } as const\n    const [decimals, name, symbol, totalSupply] = await readContracts(config, {\n      allowFailure: true,\n      contracts: [\n        { ...contractConfig, functionName: 'decimals' },\n        { ...contractConfig, functionName: 'name' },\n        { ...contractConfig, functionName: 'symbol' },\n        { ...contractConfig, functionName: 'totalSupply' },\n      ] as const,\n    })\n\n    // throw if `name` or `symbol` failed\n    if (name.error instanceof ContractFunctionExecutionError) throw name.error\n    if (symbol.error instanceof ContractFunctionExecutionError)\n      throw symbol.error\n\n    // `decimals` and `totalSupply` are required\n    if (decimals.error) throw decimals.error\n    if (totalSupply.error) throw totalSupply.error\n\n    return {\n      address,\n      decimals: decimals.result,\n      name: name.result,\n      symbol: symbol.result,\n      totalSupply: {\n        formatted: formatUnits(totalSupply.result!, getUnit(unit)),\n        value: totalSupply.result,\n      },\n    }\n  } catch (error) {\n    // In the chance that there is an error upon decoding the contract result,\n    // it could be likely that the contract data is represented as bytes32 instead\n    // of a string.\n    if (error instanceof ContractFunctionExecutionError) {\n      const abi = getAbi('bytes32')\n      const contractConfig = { address, abi, chainId } as const\n      const [decimals, name, symbol, totalSupply] = await readContracts(\n        config,\n        {\n          allowFailure: false,\n          contracts: [\n            { ...contractConfig, functionName: 'decimals' },\n            { ...contractConfig, functionName: 'name' },\n            { ...contractConfig, functionName: 'symbol' },\n            { ...contractConfig, functionName: 'totalSupply' },\n          ] as const,\n        },\n      )\n      return {\n        address,\n        decimals,\n        name: hexToString(trim(name as Hex, { dir: 'right' })),\n        symbol: hexToString(trim(symbol as Hex, { dir: 'right' })),\n        totalSupply: {\n          formatted: formatUnits(totalSupply, getUnit(unit)),\n          value: totalSupply,\n        },\n      }\n    }\n\n    throw error\n  }\n}\n", "import type { Chain } from 'viem'\nimport {\n  type GetTransactionErrorType as viem_GetTransactionErrorType,\n  type GetTransactionParameters as viem_GetTransactionParameters,\n  type GetTransactionReturnType as viem_GetTransactionReturnType,\n  getTransaction as viem_getTransaction,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute, IsNarrowable } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetTransactionParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<viem_GetTransactionParameters & ChainIdParameter<config, chainId>>\n\nexport type GetTransactionReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = Compute<\n  {\n    [key in keyof chains]: viem_GetTransactionReturnType<\n      IsNarrowable<chains[key], Chain> extends true ? chains[key] : undefined\n    > & { chainId: chains[key]['id'] }\n  }[number]\n>\n\nexport type GetTransactionErrorType = viem_GetTransactionErrorType\n\n/** https://wagmi.sh/core/api/actions/getTransaction */\nexport function getTransaction<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetTransactionParameters<config, chainId>,\n): Promise<GetTransactionReturnType<config, chainId>> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getTransaction, 'getTransaction')\n  return action(rest) as unknown as Promise<\n    GetTransactionReturnType<config, chainId>\n  >\n}\n", "import type { Chain } from 'viem'\nimport {\n  type GetTransactionConfirmationsErrorType as viem_GetTransactionConfirmationsErrorType,\n  type GetTransactionConfirmationsParameters as viem_GetTransactionConfirmationsParameters,\n  type GetTransactionConfirmationsReturnType as viem_GetTransactionConfirmationsReturnType,\n  getTransactionConfirmations as viem_getTransactionConfirmations,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetTransactionConfirmationsParameters<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: viem_GetTransactionConfirmationsParameters<\n    chains[key]\n  > &\n    ChainIdParameter<config, chainId>\n}[number]\n\nexport type GetTransactionConfirmationsReturnType =\n  viem_GetTransactionConfirmationsReturnType\n\nexport type GetTransactionConfirmationsErrorType =\n  viem_GetTransactionConfirmationsErrorType\n\n/** https://wagmi.sh/core/api/actions/getTransactionConfirmations */\nexport function getTransactionConfirmations<\n  config extends Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetTransactionConfirmationsParameters<config, chainId>,\n): Promise<GetTransactionConfirmationsReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(\n    client,\n    viem_getTransactionConfirmations,\n    'getTransactionConfirmations',\n  )\n  return action(rest as viem_GetTransactionConfirmationsParameters)\n}\n", "import {\n  type GetTransactionCountErrorType as viem_GetTransactionCountErrorType,\n  type GetTransactionCountParameters as viem_GetTransactionCountParameters,\n  type GetTransactionCountReturnType as viem_GetTransactionCountReturnType,\n  getTransactionCount as viem_getTransactionCount,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetTransactionCountParameters<config extends Config = Config> =\n  Compute<ChainIdParameter<config> & viem_GetTransactionCountParameters>\n\nexport type GetTransactionCountReturnType = viem_GetTransactionCountReturnType\n\nexport type GetTransactionCountErrorType = viem_GetTransactionCountErrorType\n\n/** https://wagmi.sh/core/api/actions/getTransactionCount */\nexport async function getTransactionCount<config extends Config>(\n  config: config,\n  parameters: GetTransactionCountParameters<config>,\n): Promise<GetTransactionCountReturnType> {\n  const { address, blockNumber, blockTag, chainId } = parameters\n\n  const client = config.getClient({ chainId })\n  const action = getAction(\n    client,\n    viem_getTransactionCount,\n    'getTransactionCount',\n  )\n  return action(blockNumber ? { address, blockNumber } : { address, blockTag })\n}\n", "import type { Chain } from 'viem'\nimport {\n  type GetTransactionReceiptErrorType as viem_GetTransactionReceiptErrorType,\n  type GetTransactionReceiptParameters as viem_GetTransactionReceiptParameters,\n  type GetTransactionReceiptReturnType as viem_GetTransactionReceiptReturnType,\n  getTransactionReceipt as viem_getTransactionReceipt,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute, IsNarrowable } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetTransactionReceiptParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  viem_GetTransactionReceiptParameters & ChainIdParameter<config, chainId>\n>\n\nexport type GetTransactionReceiptReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = Compute<\n  {\n    [key in keyof chains]: viem_GetTransactionReceiptReturnType<\n      IsNarrowable<chains[key], Chain> extends true ? chains[key] : undefined\n    > & { chainId: chains[key]['id'] }\n  }[number]\n>\n\nexport type GetTransactionReceiptErrorType = viem_GetTransactionReceiptErrorType\n\n/** https://wagmi.sh/core/api/actions/getTransactionReceipt */\nexport async function getTransactionReceipt<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetTransactionReceiptParameters<config>,\n): Promise<GetTransactionReceiptReturnType<config, chainId>> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(\n    client,\n    viem_getTransactionReceipt,\n    'getTransactionReceipt',\n  )\n  return action(rest) as unknown as Promise<\n    GetTransactionReceiptReturnType<config, chainId>\n  >\n}\n", "import { type Account, type WalletClient, walletActions } from 'viem'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { Compute } from '../types/utils.js'\nimport {\n  type GetConnectorClientErrorType,\n  type GetConnectorClientParameters,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type GetWalletClientParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = GetConnectorClientParameters<Config, chainId>\n\nexport type GetWalletClientReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  WalletClient<\n    config['_internal']['transports'][chainId],\n    Extract<config['chains'][number], { id: chainId }>,\n    Account\n  >\n>\n\nexport type GetWalletClientErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n\nexport async function getWalletClient<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetWalletClientParameters<config, chainId> = {},\n): Promise<GetWalletClientReturnType<config, chainId>> {\n  const client = await getConnectorClient(config, parameters)\n  // @ts-ignore\n  return client.extend(walletActions) as unknown as GetWalletClientReturnType<\n    config,\n    chainId\n  >\n}\n", "import type {\n  Account,\n  Address,\n  Chain,\n  PrepareTransactionRequestErrorType as viem_PrepareTransactionRequestErrorType,\n  PrepareTransactionRequestParameters as viem_PrepareTransactionRequestParameters,\n  PrepareTransactionRequestRequest as viem_PrepareTransactionRequestRequest,\n  PrepareTransactionRequestReturnType as viem_PrepareTransactionRequestReturnType,\n} from 'viem'\nimport { prepareTransactionRequest as viem_prepareTransactionRequest } from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type {\n  Compute,\n  IsNarrowable,\n  UnionCompute,\n  UnionStrictOmit,\n} from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport { getAccount } from './getAccount.js'\n\nexport type PrepareTransactionRequestParameters<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  > = viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: UnionCompute<\n    UnionStrictOmit<\n      viem_PrepareTransactionRequestParameters<\n        chains[key],\n        Account,\n        chains[key],\n        Account | Address,\n        request extends viem_PrepareTransactionRequestRequest<\n          chains[key],\n          chains[key]\n        >\n          ? request\n          : never\n      >,\n      'chain'\n    > &\n      ChainIdParameter<config, chainId> & {\n        to: Address\n      }\n  >\n}[number]\n\nexport type PrepareTransactionRequestReturnType<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  > = viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: Compute<\n    viem_PrepareTransactionRequestReturnType<\n      IsNarrowable<chains[key], Chain> extends true ? chains[key] : undefined,\n      Account,\n      chains[key],\n      Account,\n      request extends viem_PrepareTransactionRequestRequest<\n        IsNarrowable<chains[key], Chain> extends true ? chains[key] : undefined,\n        chains[key]\n      >\n        ? request\n        : never\n    >\n  > & {\n    chainId: chains[key]['id']\n  }\n}[number]\n\nexport type PrepareTransactionRequestErrorType =\n  viem_PrepareTransactionRequestErrorType\n\n/** https://wagmi.sh/core/api/actions/prepareTransactionRequest */\nexport async function prepareTransactionRequest<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n  const request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>['0'],\n    SelectChains<config, chainId>['0']\n  >,\n>(\n  config: config,\n  parameters: PrepareTransactionRequestParameters<config, chainId, request>,\n): Promise<PrepareTransactionRequestReturnType<config, chainId, request>> {\n  const { account: account_, chainId, ...rest } = parameters\n\n  const account = account_ ?? getAccount(config).address\n  const client = config.getClient({ chainId })\n\n  const action = getAction(\n    client,\n    viem_prepareTransactionRequest,\n    'prepareTransactionRequest',\n  )\n  return action({\n    ...rest,\n    ...(account ? { account } : {}),\n  } as unknown as viem_PrepareTransactionRequestParameters) as unknown as Promise<\n    PrepareTransactionRequestReturnType<config, chainId, request>\n  >\n}\n", "import type { Address } from 'viem'\n\nimport type { CreateConnectorFn } from '../connectors/createConnector.js'\nimport type { Config, Connection, Connector } from '../createConfig.js'\nimport type { ErrorType } from '../errors/base.js'\nimport type { Compute } from '../types/utils.js'\n\nexport type ReconnectParameters = {\n  /** Connectors to attempt reconnect with */\n  connectors?: readonly (CreateConnectorFn | Connector)[] | undefined\n}\n\nexport type ReconnectReturnType = Compute<Connection>[]\n\nexport type ReconnectErrorType = ErrorType\n\nlet isReconnecting = false\n\n/** https://wagmi.sh/core/api/actions/reconnect */\nexport async function reconnect(\n  config: Config,\n  parameters: ReconnectParameters = {},\n): Promise<ReconnectReturnType> {\n  // If already reconnecting, do nothing\n  if (isReconnecting) return []\n  isReconnecting = true\n\n  config.setState((x) => ({\n    ...x,\n    status: x.current ? 'reconnecting' : 'connecting',\n  }))\n\n  const connectors: Connector[] = []\n  if (parameters.connectors?.length) {\n    for (const connector_ of parameters.connectors) {\n      let connector: Connector\n      // \"Register\" connector if not already created\n      if (typeof connector_ === 'function')\n        connector = config._internal.connectors.setup(connector_)\n      else connector = connector_\n      connectors.push(connector)\n    }\n  } else connectors.push(...config.connectors)\n\n  // Try recently-used connectors first\n  let recentConnectorId: string | null | undefined\n  try {\n    recentConnectorId = await config.storage?.getItem('recentConnectorId')\n  } catch {}\n  const scores: Record<string, number> = {}\n  for (const [, connection] of config.state.connections) {\n    scores[connection.connector.id] = 1\n  }\n  if (recentConnectorId) scores[recentConnectorId] = 0\n  const sorted =\n    Object.keys(scores).length > 0\n      ? // .toSorted()\n        [...connectors].sort(\n          (a, b) => (scores[a.id] ?? 10) - (scores[b.id] ?? 10),\n        )\n      : connectors\n\n  // Iterate through each connector and try to connect\n  let connected = false\n  const connections: Connection[] = []\n  const providers: unknown[] = []\n  for (const connector of sorted) {\n    const provider = await connector.getProvider().catch(() => undefined)\n    if (!provider) continue\n\n    // If we already have an instance of this connector's provider,\n    // then we have already checked it (ie. injected connectors can\n    // share the same `window.ethereum` instance, so we don't want to\n    // connect to it again).\n    if (providers.some((x) => x === provider)) continue\n\n    const isAuthorized = await connector.isAuthorized()\n    if (!isAuthorized) continue\n\n    const data = await connector\n      .connect({ isReconnecting: true })\n      .catch(() => null)\n    if (!data) continue\n\n    connector.emitter.off('connect', config._internal.events.connect)\n    connector.emitter.on('change', config._internal.events.change)\n    connector.emitter.on('disconnect', config._internal.events.disconnect)\n\n    config.setState((x) => {\n      const connections = new Map(connected ? x.connections : new Map()).set(\n        connector.uid,\n        { accounts: data.accounts, chainId: data.chainId, connector },\n      )\n      return {\n        ...x,\n        current: connected ? x.current : connector.uid,\n        connections,\n      }\n    })\n    connections.push({\n      accounts: data.accounts as readonly [Address, ...Address[]],\n      chainId: data.chainId,\n      connector,\n    })\n    providers.push(provider)\n    connected = true\n  }\n\n  // Prevent overwriting connected status from race condition\n  if (\n    config.state.status === 'reconnecting' ||\n    config.state.status === 'connecting'\n  ) {\n    // If connecting didn't succeed, set to disconnected\n    if (!connected)\n      config.setState((x) => ({\n        ...x,\n        connections: new Map(),\n        current: null,\n        status: 'disconnected',\n      }))\n    else config.setState((x) => ({ ...x, status: 'connected' }))\n  }\n\n  isReconnecting = false\n  return connections\n}\n", "import type { Account, Chain } from 'viem'\nimport {\n  type SendCallsErrorType as viem_SendCallsErrorType,\n  type SendCallsParameters as viem_SendCallsParameters,\n  type SendCallsReturnType as viem_SendCallsReturnType,\n  sendCalls as viem_sendCalls,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  ConnectorParameter,\n} from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type SendCallsParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  calls extends readonly unknown[] = readonly unknown[],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: Compute<\n    Omit<\n      viem_SendCallsParameters<chains[key], Account, chains[key], calls>,\n      'chain'\n    > &\n      ChainIdParameter<config, chainId> &\n      ConnectorParameter\n  >\n}[number]\n\nexport type SendCallsReturnType = viem_SendCallsReturnType\n\nexport type SendCallsErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_SendCallsErrorType\n\n/** https://wagmi.sh/core/api/actions/sendCalls */\nexport async function sendCalls<\n  const calls extends readonly unknown[],\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: SendCallsParameters<config, chainId, calls>,\n): Promise<SendCallsReturnType> {\n  const { account, chainId, connector, calls, ...rest } = parameters\n\n  const client = await getConnectorClient(config, {\n    account,\n    chainId,\n    connector,\n  })\n\n  return viem_sendCalls(client, {\n    ...(rest as any),\n    ...(typeof account !== 'undefined' ? { account } : {}),\n    calls,\n    chain: chainId ? { id: chainId } : undefined,\n  })\n}\n", "import type {\n  Account,\n  Chain,\n  Client,\n  TransactionRequest,\n  SendTransactionErrorType as viem_SendTransactionErrorType,\n  SendTransactionParameters as viem_SendTransactionParameters,\n  SendTransactionReturnType as viem_SendTransactionReturnType,\n} from 'viem'\nimport { sendTransaction as viem_sendTransaction } from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  ConnectorParameter,\n} from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type SendTransactionParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: Compute<\n    Omit<\n      viem_SendTransactionParameters<chains[key], Account, chains[key]>,\n      'chain' | 'gas'\n    > &\n      ChainIdParameter<config, chainId> &\n      ConnectorParameter\n  >\n}[number] & {\n  /** Gas provided for transaction execution. */\n  gas?: TransactionRequest['gas'] | null\n}\n\nexport type SendTransactionReturnType = viem_SendTransactionReturnType\n\nexport type SendTransactionErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_SendTransactionErrorType\n\n/** https://wagmi.sh/core/api/actions/sendTransaction */\nexport async function sendTransaction<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: SendTransactionParameters<config, chainId>,\n): Promise<SendTransactionReturnType> {\n  const { account, chainId, connector, ...rest } = parameters\n\n  let client: Client\n  if (typeof account === 'object' && account?.type === 'local')\n    client = config.getClient({ chainId })\n  else\n    client = await getConnectorClient(config, {\n      account: account ?? undefined,\n      chainId,\n      connector,\n    })\n\n  const action = getAction(client, viem_sendTransaction, 'sendTransaction')\n  const hash = await action({\n    ...(rest as any),\n    ...(account ? { account } : {}),\n    chain: chainId ? { id: chainId } : null,\n    gas: rest.gas ?? undefined,\n  })\n\n  return hash\n}\n", "import {\n  type ShowCallsStatusErrorType as viem_ShowCallsStatusErrorType,\n  type ShowCallsStatusParameters as viem_ShowCallsStatusParameters,\n  type ShowCallsStatusReturnType as viem_ShowCallsStatusReturnType,\n  showCallsStatus as viem_showCallsStatus,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport { getConnectorClient } from './getConnectorClient.js'\n\nexport type ShowCallsStatusParameters = viem_ShowCallsStatusParameters &\n  ConnectorParameter\n\nexport type ShowCallsStatusReturnType = viem_ShowCallsStatusReturnType\n\nexport type ShowCallsStatusErrorType = viem_ShowCallsStatusErrorType\n\n/** https://wagmi.sh/core/api/actions/showCallsStatus */\nexport async function showCallsStatus<config extends Config>(\n  config: config,\n  parameters: ShowCallsStatusParameters,\n): Promise<ShowCallsStatusReturnType> {\n  const { connector, id } = parameters\n  const client = await getConnectorClient(config, { connector })\n  return viem_showCallsStatus(client, { id })\n}\n", "import type { Account, Client } from 'viem'\nimport {\n  type SignMessageErrorType as viem_SignMessageErrorType,\n  type SignMessageParameters as viem_SignMessageParameters,\n  type SignMessageReturnType as viem_SignMessageReturnType,\n  signMessage as viem_signMessage,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type SignMessageParameters = Compute<\n  viem_SignMessageParameters<Account> & ConnectorParameter\n>\n\nexport type SignMessageReturnType = viem_SignMessageReturnType\n\nexport type SignMessageErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_SignMessageErrorType\n\n/** https://wagmi.sh/core/api/actions/signMessage */\nexport async function signMessage(\n  config: Config,\n  parameters: SignMessageParameters,\n): Promise<SignMessageReturnType> {\n  const { account, connector, ...rest } = parameters\n\n  let client: Client\n  if (typeof account === 'object' && account.type === 'local')\n    client = config.getClient()\n  else client = await getConnectorClient(config, { account, connector })\n\n  const action = getAction(client, viem_signMessage, 'signMessage')\n  return action({\n    ...rest,\n    ...(account ? { account } : {}),\n  } as viem_SignMessageParameters<Account>)\n}\n", "import type { Account, Client, TypedData } from 'viem'\nimport {\n  type SignMessageErrorType as viem_SignMessageErrorType,\n  type SignTypedDataParameters as viem_SignTypedDataParameters,\n  type SignTypedDataReturnType as viem_SignTypedDataReturnType,\n  signTypedData as viem_signTypedData,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport type { UnionCompute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type SignTypedDataParameters<\n  typedData extends TypedData | Record<string, unknown> = TypedData,\n  primaryType extends keyof typedData | 'EIP712Domain' = keyof typedData,\n  ///\n  primaryTypes = typedData extends TypedData ? keyof typedData : string,\n> = UnionCompute<\n  viem_SignTypedDataParameters<typedData, primaryType, Account, primaryTypes> &\n    ConnectorParameter\n>\n\nexport type SignTypedDataReturnType = viem_SignTypedDataReturnType\n\nexport type SignTypedDataErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_SignMessageErrorType\n\n/** https://wagmi.sh/core/api/actions/signTypedData */\nexport async function signTypedData<\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(\n  config: Config,\n  parameters: SignTypedDataParameters<typedData, primaryType>,\n): Promise<SignTypedDataReturnType> {\n  const { account, connector, ...rest } = parameters\n\n  let client: Client\n  if (typeof account === 'object' && account.type === 'local')\n    client = config.getClient()\n  else client = await getConnectorClient(config, { account, connector })\n\n  const action = getAction(client, viem_signTypedData, 'signTypedData')\n  return action({\n    ...rest,\n    ...(account ? { account } : {}),\n  } as unknown as viem_SignTypedDataParameters)\n}\n", "import type {\n  Abi,\n  Account,\n  Address,\n  Chain,\n  ContractFunctionArgs,\n  ContractFunctionName,\n} from 'viem'\nimport {\n  type SimulateContractErrorType as viem_SimulateContractErrorType,\n  type SimulateContractParameters as viem_SimulateContractParameters,\n  type SimulateContractReturnType as viem_SimulateContractReturnType,\n  simulateContract as viem_simulateContract,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  ConnectorParameter,\n} from '../types/properties.js'\nimport type {\n  Compute,\n  PartialBy,\n  UnionCompute,\n  UnionStrictOmit,\n} from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type SimulateContractParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'nonpayable' | 'payable'\n  > = ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  > = ContractFunctionArgs<abi, 'nonpayable' | 'payable', functionName>,\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: UnionCompute<\n    UnionStrictOmit<\n      viem_SimulateContractParameters<\n        abi,\n        functionName,\n        args,\n        chains[key],\n        chains[key],\n        Account | Address\n      >,\n      'chain'\n    >\n  > &\n    ChainIdParameter<config, chainId> &\n    ConnectorParameter\n}[number]\n\nexport type SimulateContractReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'nonpayable' | 'payable'\n  > = ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  > = ContractFunctionArgs<abi, 'nonpayable' | 'payable', functionName>,\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: viem_SimulateContractReturnType<\n    abi,\n    functionName,\n    args,\n    chains[key],\n    Account,\n    chains[key]\n  > & {\n    chainId: chains[key]['id']\n    request: Compute<\n      PartialBy<\n        { chainId: chainId; chain: chains[key] },\n        chainId extends config['chains'][number]['id'] ? never : 'chainId'\n      >\n    >\n  }\n}[number]\n\nexport type SimulateContractErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_SimulateContractErrorType\n\n/** https://wagmi.sh/core/api/actions/simulateContract */\nexport async function simulateContract<\n  config extends Config,\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n>(\n  config: config,\n  parameters: SimulateContractParameters<\n    abi,\n    functionName,\n    args,\n    config,\n    chainId\n  >,\n): Promise<\n  SimulateContractReturnType<abi, functionName, args, config, chainId>\n> {\n  const { abi, chainId, connector, ...rest } =\n    parameters as SimulateContractParameters\n\n  let account: Address | Account\n  if (parameters.account) account = parameters.account\n  else {\n    const connectorClient = await getConnectorClient(config, {\n      chainId,\n      connector,\n    })\n    account = connectorClient.account\n  }\n\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_simulateContract, 'simulateContract')\n  const { result, request } = await action({ ...rest, abi, account })\n\n  return {\n    chainId: client.chain.id,\n    result,\n    request: { ...request, chainId },\n  } as unknown as SimulateContractReturnType<\n    abi,\n    functionName,\n    args,\n    config,\n    chainId\n  >\n}\n", "import type { Address } from 'viem'\n\nimport type { Config, Connector } from '../createConfig.js'\nimport type { BaseError, ErrorType } from '../errors/base.js'\nimport {\n  ConnectorNotConnectedError,\n  type ConnectorNotConnectedErrorType,\n} from '../errors/config.js'\n\nexport type SwitchAccountParameters = {\n  connector: Connector\n}\n\nexport type SwitchAccountReturnType<config extends Config = Config> = {\n  accounts: readonly [Address, ...Address[]]\n  chainId:\n    | config['chains'][number]['id']\n    | (number extends config['chains'][number]['id'] ? number : number & {})\n}\n\nexport type SwitchAccountErrorType =\n  | ConnectorNotConnectedErrorType\n  | BaseError\n  | ErrorType\n\n/** https://wagmi.sh/core/api/actions/switchAccount */\nexport async function switchAccount<config extends Config>(\n  config: config,\n  parameters: SwitchAccountParameters,\n): Promise<SwitchAccountReturnType<config>> {\n  const { connector } = parameters\n\n  const connection = config.state.connections.get(connector.uid)\n  if (!connection) throw new ConnectorNotConnectedError()\n\n  await config.storage?.setItem('recentConnectorId', connector.id)\n  config.setState((x) => ({\n    ...x,\n    current: connector.uid,\n  }))\n  return {\n    accounts: connection.accounts,\n    chainId: connection.chainId,\n  }\n}\n", "import type {\n  AddEthereumChainParameter,\n  UserRejectedRequestErrorType,\n  SwitchChainErrorType as viem_SwitchChainErrorType,\n} from 'viem'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport {\n  ChainNotConfiguredError,\n  type ChainNotConfiguredErrorType,\n} from '../errors/config.js'\nimport {\n  type ProviderNotFoundErrorType,\n  SwitchChainNotSupportedError,\n  type SwitchChainNotSupportedErrorType,\n} from '../errors/connector.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\n\nexport type SwitchChainParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  ConnectorParameter & {\n    chainId: chainId | config['chains'][number]['id']\n    addEthereumChainParameter?:\n      | Compute<ExactPartial<Omit<AddEthereumChainParameter, 'chainId'>>>\n      | undefined\n  }\n>\n\nexport type SwitchChainReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Extract<\n  config['chains'][number],\n  { id: Config extends config ? number : chainId }\n>\n\nexport type SwitchChainErrorType =\n  | SwitchChainNotSupportedErrorType\n  | ChainNotConfiguredErrorType\n  // connector.switchChain()\n  | ProviderNotFoundErrorType\n  | UserRejectedRequestErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_SwitchChainErrorType\n\n/** https://wagmi.sh/core/api/actions/switchChain */\nexport async function switchChain<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: SwitchChainParameters<config, chainId>,\n): Promise<SwitchChainReturnType<config, chainId>> {\n  const { addEthereumChainParameter, chainId } = parameters\n\n  const connection = config.state.connections.get(\n    parameters.connector?.uid ?? config.state.current!,\n  )\n  if (connection) {\n    const connector = connection.connector\n    if (!connector.switchChain)\n      throw new SwitchChainNotSupportedError({ connector })\n    const chain = await connector.switchChain({\n      addEthereumChainParameter,\n      chainId,\n    })\n    return chain as SwitchChainReturnType<config, chainId>\n  }\n\n  const chain = config.chains.find((x) => x.id === chainId)\n  if (!chain) throw new ChainNotConfiguredError()\n  config.setState((x) => ({ ...x, chainId }))\n  return chain as SwitchChainReturnType<config, chainId>\n}\n", "import {\n  type VerifyMessageErrorType as viem_VerifyMessageErrorType,\n  type VerifyMessageParameters as viem_VerifyMessageParameters,\n  type VerifyMessageReturnType as viem_VerifyMessageReturnType,\n  verifyMessage as viem_verifyMessage,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type VerifyMessageParameters<config extends Config = Config> = Compute<\n  viem_VerifyMessageParameters & ChainIdParameter<config>\n>\n\nexport type VerifyMessageReturnType = viem_VerifyMessageReturnType\n\nexport type VerifyMessageErrorType = viem_VerifyMessageErrorType\n\n/** https://wagmi.sh/core/api/actions/verifyMessage */\nexport async function verifyMessage<config extends Config>(\n  config: config,\n  parameters: VerifyMessageParameters<config>,\n): Promise<VerifyMessageReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_verifyMessage, 'verifyMessage')\n  return action(rest)\n}\n", "import type { TypedData } from 'viem'\nimport {\n  type VerifyTypedDataErrorType as viem_VerifyTypedDataErrorType,\n  type VerifyTypedDataParameters as viem_VerifyTypedDataParameters,\n  type VerifyTypedDataReturnType as viem_VerifyTypedDataReturnType,\n  verifyTypedData as viem_verifyTypedData,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type VerifyTypedDataParameters<\n  typedData extends TypedData | Record<string, unknown> = TypedData,\n  primaryType extends keyof typedData | 'EIP712Domain' = keyof typedData,\n  config extends Config = Config,\n> = Compute<\n  viem_VerifyTypedDataParameters<typedData, primaryType> &\n    ChainIdParameter<config>\n>\n\nexport type VerifyTypedDataReturnType = viem_VerifyTypedDataReturnType\n\nexport type VerifyTypedDataErrorType = viem_VerifyTypedDataErrorType\n\n/** https://wagmi.sh/core/api/actions/verifyTypedData */\nexport async function verifyTypedData<\n  config extends Config,\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(\n  config: config,\n  parameters: VerifyTypedDataParameters<typedData, primaryType, config>,\n): Promise<VerifyTypedDataReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_verifyTypedData, 'verifyTypedData')\n  return action(rest as viem_VerifyTypedDataParameters)\n}\n", "import {\n  type WaitForCallsStatusErrorType as viem_WaitForCallsStatusErrorType,\n  type WaitForCallsStatusParameters as viem_WaitForCallsStatusParameters,\n  type WaitForCallsStatusReturnType as viem_WaitForCallsStatusReturnType,\n  waitForCallsStatus as viem_waitForCallsStatus,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport { getConnectorClient } from './getConnectorClient.js'\n\nexport type WaitForCallsStatusParameters = viem_WaitForCallsStatusParameters &\n  ConnectorParameter\n\nexport type WaitForCallsStatusReturnType = viem_WaitForCallsStatusReturnType\n\nexport type WaitForCallsStatusErrorType = viem_WaitForCallsStatusErrorType\n\n/** https://wagmi.sh/core/api/actions/waitForCallsStatus */\nexport async function waitForCallsStatus<config extends Config>(\n  config: config,\n  parameters: WaitForCallsStatusParameters,\n): Promise<WaitForCallsStatusReturnType> {\n  const { connector } = parameters\n  const client = await getConnectorClient(config, { connector })\n  return viem_waitForCallsStatus(client, parameters)\n}\n", "import type { Config } from '../createConfig.js'\nimport { deepEqual } from '../utils/deepEqual.js'\nimport { type GetAccountReturnType, getAccount } from './getAccount.js'\n\nexport type WatchAccountParameters<config extends Config = Config> = {\n  onChange(\n    account: GetAccountReturnType<config>,\n    prevAccount: GetAccountReturnType<config>,\n  ): void\n}\n\nexport type WatchAccountReturnType = () => void\n\n/** https://wagmi.sh/core/api/actions/watchAccount */\nexport function watchAccount<config extends Config>(\n  config: config,\n  parameters: WatchAccountParameters<config>,\n): WatchAccountReturnType {\n  const { onChange } = parameters\n\n  return config.subscribe(() => getAccount(config), onChange, {\n    equalityFn(a, b) {\n      const { connector: aConnector, ...aRest } = a\n      const { connector: bConnector, ...bRest } = b\n      return (\n        deepEqual(aRest, bRest) &&\n        // check connector separately\n        aConnector?.id === bConnector?.id &&\n        aConnector?.uid === bConnector?.uid\n      )\n    },\n  })\n}\n", "import {\n  type WatchAssetErrorType as viem_WatchAssetErrorType,\n  type WatchAssetParameters as viem_WatchAssetParameters,\n  type WatchAssetReturnType as viem_WatchAssetReturnType,\n  watchAsset as viem_watchAsset,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type WatchAssetParameters = Compute<\n  viem_WatchAssetParameters & ConnectorParameter\n>\n\nexport type WatchAssetReturnType = viem_WatchAssetReturnType\n\nexport type WatchAssetErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_WatchAssetErrorType\n\n/** https://wagmi.sh/core/api/actions/watchAsset */\nexport async function watchAsset(\n  config: Config,\n  parameters: WatchAssetParameters,\n): Promise<WatchAssetReturnType> {\n  const { connector, ...rest } = parameters\n\n  const client = await getConnectorClient(config, { connector })\n\n  const action = getAction(client, viem_watchAsset, 'watchAsset')\n  return action(rest as viem_WatchAssetParameters)\n}\n", "import {\n  type WatchBlocksParameters as viem_WatchBlocksParameters,\n  type WatchBlocksReturnType as viem_WatchBlocksReturnType,\n  watchBlocks as viem_watchBlocks,\n} from 'viem/actions'\n\nimport type { BlockTag, Chain, Transport, WebSocketTransport } from 'viem'\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  SyncConnectedChainParameter,\n} from '../types/properties.js'\nimport type { IsNarrowable, UnionCompute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type WatchBlocksParameters<\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: UnionCompute<\n    viem_WatchBlocksParameters<\n      config['_internal']['transports'][chains[key]['id']] extends infer transport extends\n        Transport\n        ? Transport extends transport\n          ? WebSocketTransport\n          : transport\n        : WebSocketTransport,\n      IsNarrowable<chains[key], Chain> extends true ? chains[key] : undefined,\n      includeTransactions,\n      blockTag\n    > &\n      ChainIdParameter<config, chainId> &\n      SyncConnectedChainParameter\n  >\n}[number]\n\nexport type WatchBlocksReturnType = viem_WatchBlocksReturnType\n\n// TODO: wrap in viem's `observe` to avoid duplicate invocations.\n/** https://wagmi.sh/core/actions/watchBlocks */\nexport function watchBlocks<\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n>(\n  config: config,\n  parameters: WatchBlocksParameters<\n    includeTransactions,\n    blockTag,\n    config,\n    chainId\n  >,\n): WatchBlocksReturnType {\n  const { syncConnectedChain = config._internal.syncConnectedChain, ...rest } =\n    parameters as WatchBlocksParameters\n\n  let unwatch: WatchBlocksReturnType | undefined\n  const listener = (chainId: number | undefined) => {\n    if (unwatch) unwatch()\n\n    const client = config.getClient({ chainId })\n    const action = getAction(client, viem_watchBlocks, 'watchBlocks')\n    unwatch = action(rest as viem_WatchBlocksParameters)\n    return unwatch\n  }\n\n  // set up listener for block number changes\n  const unlisten = listener(parameters.chainId)\n\n  // set up subscriber for connected chain changes\n  let unsubscribe: (() => void) | undefined\n  if (syncConnectedChain && !parameters.chainId)\n    unsubscribe = config.subscribe(\n      ({ chainId }) => chainId,\n      async (chainId) => listener(chainId),\n    )\n\n  return () => {\n    unlisten?.()\n    unsubscribe?.()\n  }\n}\n", "import {\n  type WatchBlockNumberParameters as viem_WatchBlockNumberParameters,\n  type WatchBlockNumberReturnType as viem_WatchBlockNumberReturnType,\n  watchBlockNumber as viem_watchBlockNumber,\n} from 'viem/actions'\n\nimport type { Chain, Transport, WebSocketTransport } from 'viem'\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  SyncConnectedChainParameter,\n} from '../types/properties.js'\nimport type { UnionCompute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type WatchBlockNumberParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: UnionCompute<\n    viem_WatchBlockNumberParameters<\n      config['_internal']['transports'][chains[key]['id']] extends infer transport extends\n        Transport\n        ? Transport extends transport\n          ? WebSocketTransport\n          : transport\n        : WebSocketTransport\n    > &\n      ChainIdParameter<config, chainId> &\n      SyncConnectedChainParameter\n  >\n}[number]\n\nexport type WatchBlockNumberReturnType = viem_WatchBlockNumberReturnType\n\n// TODO: wrap in viem's `observe` to avoid duplicate invocations.\n/** https://wagmi.sh/core/api/actions/watchBlockNumber */\nexport function watchBlockNumber<\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: WatchBlockNumberParameters<config, chainId>,\n): WatchBlockNumberReturnType {\n  const { syncConnectedChain = config._internal.syncConnectedChain, ...rest } =\n    parameters as WatchBlockNumberParameters\n\n  let unwatch: WatchBlockNumberReturnType | undefined\n  const listener = (chainId: number | undefined) => {\n    if (unwatch) unwatch()\n\n    const client = config.getClient({ chainId })\n    const action = getAction(client, viem_watchBlockNumber, 'watchBlockNumber')\n    unwatch = action(rest as viem_WatchBlockNumberParameters)\n    return unwatch\n  }\n\n  // set up listener for block number changes\n  const unlisten = listener(parameters.chainId)\n\n  // set up subscriber for connected chain changes\n  let unsubscribe: (() => void) | undefined\n  if (syncConnectedChain && !parameters.chainId)\n    unsubscribe = config.subscribe(\n      ({ chainId }) => chainId,\n      async (chainId) => listener(chainId),\n    )\n\n  return () => {\n    unlisten?.()\n    unsubscribe?.()\n  }\n}\n", "import type { Config } from '../createConfig.js'\nimport type { GetChainIdReturnType } from './getChainId.js'\n\nexport type WatchChainIdParameters<config extends Config = Config> = {\n  onChange(\n    chainId: GetChainIdReturnType<config>,\n    prevChainId: GetChainIdReturnType<config>,\n  ): void\n}\n\nexport type WatchChainIdReturnType = () => void\n\n/** https://wagmi.sh/core/api/actions/watchChainId */\nexport function watchChainId<config extends Config>(\n  config: config,\n  parameters: WatchChainIdParameters<config>,\n): WatchChainIdReturnType {\n  const { onChange } = parameters\n  return config.subscribe((state) => state.chainId, onChange)\n}\n", "import type { Config } from '../createConfig.js'\nimport { type GetClientReturnType, getClient } from './getClient.js'\n\nexport type WatchClientParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = {\n  onChange(\n    publicClient: GetClientReturnType<config, chainId>,\n    prevClient: GetClientReturnType<config, chainId>,\n  ): void\n}\n\nexport type WatchClientReturnType = () => void\n\n/** https://wagmi.sh/core/api/actions/watchClient */\nexport function watchClient<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: WatchClientParameters<config, chainId>,\n): WatchClientReturnType {\n  const { onChange } = parameters\n  return config.subscribe(\n    () => getClient(config) as GetClientReturnType<config, chainId>,\n    onChange,\n    {\n      equalityFn(a, b) {\n        return a?.uid === b?.uid\n      },\n    },\n  )\n}\n", "import type { Config } from '../createConfig.js'\nimport { deepEqual } from '../utils/deepEqual.js'\nimport {\n  type GetConnectionsReturnType,\n  getConnections,\n} from './getConnections.js'\n\nexport type WatchConnectionsParameters = {\n  onChange(\n    connections: GetConnectionsReturnType,\n    prevConnections: GetConnectionsReturnType,\n  ): void\n}\n\nexport type WatchConnectionsReturnType = () => void\n\n/** https://wagmi.sh/core/api/actions/watchConnections */\nexport function watchConnections(\n  config: Config,\n  parameters: WatchConnectionsParameters,\n): WatchConnectionsReturnType {\n  const { onChange } = parameters\n  return config.subscribe(() => getConnections(config), onChange, {\n    equalityFn: deepEqual,\n  })\n}\n", "import type { Config } from '../createConfig.js'\nimport type { GetConnectorsReturnType } from './getConnectors.js'\n\nexport type WatchConnectorsParameters<config extends Config = Config> = {\n  onChange(\n    connections: GetConnectorsReturnType<config>,\n    prevConnectors: GetConnectorsReturnType<config>,\n  ): void\n}\n\nexport type WatchConnectorsReturnType = () => void\n\n/** https://wagmi.sh/core/api/actions/watchConnectors */\nexport function watchConnectors<config extends Config>(\n  config: config,\n  parameters: WatchConnectorsParameters<config>,\n): WatchConnectorsReturnType {\n  const { onChange } = parameters\n  return config._internal.connectors.subscribe((connectors, prevConnectors) => {\n    onChange(Object.values(connectors), prevConnectors)\n  })\n}\n", "import type {\n  Abi,\n  Chain,\n  ContractEventName,\n  Transport,\n  WebSocketTransport,\n} from 'viem'\nimport {\n  type WatchContractEventParameters as viem_WatchContractEventParameters,\n  type WatchContractEventReturnType as viem_WatchContractEventReturnType,\n  watchContractEvent as viem_watchContractEvent,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  SyncConnectedChainParameter,\n} from '../types/properties.js'\nimport type { UnionCompute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type WatchContractEventParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  eventName extends ContractEventName<abi> | undefined = ContractEventName<abi>,\n  strict extends boolean | undefined = undefined,\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: UnionCompute<\n    viem_WatchContractEventParameters<\n      abi,\n      eventName,\n      strict,\n      config['_internal']['transports'][chains[key]['id']] extends infer transport extends\n        Transport\n        ? Transport extends transport\n          ? WebSocketTransport\n          : transport\n        : WebSocketTransport\n    > &\n      ChainIdParameter<config, chainId> &\n      SyncConnectedChainParameter\n  >\n}[number]\n\nexport type WatchContractEventReturnType = viem_WatchContractEventReturnType\n\n// TODO: wrap in viem's `observe` to avoid duplicate invocations.\n/** https://wagmi.sh/core/api/actions/watchContractEvent */\nexport function watchContractEvent<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n  const abi extends Abi | readonly unknown[],\n  eventName extends ContractEventName<abi> | undefined,\n  strict extends boolean | undefined = undefined,\n>(\n  config: config,\n  parameters: WatchContractEventParameters<\n    abi,\n    eventName,\n    strict,\n    config,\n    chainId\n  >,\n) {\n  const { syncConnectedChain = config._internal.syncConnectedChain, ...rest } =\n    parameters\n\n  let unwatch: WatchContractEventReturnType | undefined\n  const listener = (chainId: number | undefined) => {\n    if (unwatch) unwatch()\n\n    const client = config.getClient({ chainId })\n    const action = getAction(\n      client,\n      viem_watchContractEvent,\n      'watchContractEvent',\n    )\n    unwatch = action(rest as unknown as viem_WatchContractEventParameters)\n    return unwatch\n  }\n\n  // set up listener for transaction changes\n  const unlisten = listener(parameters.chainId)\n\n  // set up subscriber for connected chain changes\n  let unsubscribe: (() => void) | undefined\n  if (syncConnectedChain && !parameters.chainId)\n    unsubscribe = config.subscribe(\n      ({ chainId }) => chainId,\n      async (chainId) => listener(chainId),\n    )\n\n  return () => {\n    unlisten?.()\n    unsubscribe?.()\n  }\n}\n", "import type { Chain, Transport, WebSocketTransport } from 'viem'\nimport {\n  type WatchPendingTransactionsParameters as viem_WatchPendingTransactionsParameters,\n  type WatchPendingTransactionsReturnType as viem_WatchPendingTransactionsReturnType,\n  watchPendingTransactions as viem_watchPendingTransactions,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  SyncConnectedChainParameter,\n} from '../types/properties.js'\nimport type { UnionCompute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type WatchPendingTransactionsParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: UnionCompute<\n    viem_WatchPendingTransactionsParameters<\n      config['_internal']['transports'][chains[key]['id']] extends infer transport extends\n        Transport\n        ? Transport extends transport\n          ? WebSocketTransport\n          : transport\n        : WebSocketTransport\n    > &\n      ChainIdParameter<config, chainId> &\n      SyncConnectedChainParameter\n  >\n}[number]\n\nexport type WatchPendingTransactionsReturnType =\n  viem_WatchPendingTransactionsReturnType\n\n// TODO: wrap in viem's `observe` to avoid duplicate invocations.\n/** https://wagmi.sh/core/api/actions/watchPendingTransactions */\nexport function watchPendingTransactions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: WatchPendingTransactionsParameters<config, chainId>,\n) {\n  const { syncConnectedChain = config._internal.syncConnectedChain, ...rest } =\n    parameters\n\n  let unwatch: WatchPendingTransactionsReturnType | undefined\n  const listener = (chainId: number | undefined) => {\n    if (unwatch) unwatch()\n\n    const client = config.getClient({ chainId })\n    const action = getAction(\n      client,\n      viem_watchPendingTransactions,\n      'watchPendingTransactions',\n    )\n    unwatch = action(rest as viem_WatchPendingTransactionsParameters)\n    return unwatch\n  }\n\n  // set up listener for transaction changes\n  const unlisten = listener(parameters.chainId)\n\n  // set up subscriber for connected chain changes\n  let unsubscribe: (() => void) | undefined\n  if (syncConnectedChain && !parameters.chainId)\n    unsubscribe = config.subscribe(\n      ({ chainId }) => chainId,\n      async (chainId) => listener(chainId),\n    )\n\n  return () => {\n    unlisten?.()\n    unsubscribe?.()\n  }\n}\n", "import type { Config } from '../createConfig.js'\nimport {\n  type GetPublicClientReturnType,\n  getPublicClient,\n} from './getPublicClient.js'\n\nexport type WatchPublicClientParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = {\n  onChange(\n    publicClient: GetPublicClientReturnType<config, chainId>,\n    prevPublicClient: GetPublicClientReturnType<config, chainId>,\n  ): void\n}\n\nexport type WatchPublicClientReturnType = () => void\n\n/** https://wagmi.sh/core/api/actions/watchPublicClient */\nexport function watchPublicClient<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: WatchPublicClientParameters<config, chainId>,\n): WatchPublicClientReturnType {\n  const { onChange } = parameters\n  return config.subscribe(\n    () => getPublicClient(config) as GetPublicClientReturnType<config, chainId>,\n    onChange,\n    {\n      equalityFn(a, b) {\n        return a?.uid === b?.uid\n      },\n    },\n  )\n}\n", "import type { Chain } from 'viem'\nimport { hexToString } from 'viem'\nimport {\n  call,\n  getTransaction,\n  type WaitForTransactionReceiptErrorType as viem_WaitForTransactionReceiptErrorType,\n  type WaitForTransactionReceiptParameters as viem_WaitForTransactionReceiptParameters,\n  type WaitForTransactionReceiptReturnType as viem_WaitForTransactionReceiptReturnType,\n  waitForTransactionReceipt as viem_waitForTransactionReceipt,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute, IsNarrowable } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type WaitForTransactionReceiptParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  viem_WaitForTransactionReceiptParameters & ChainIdParameter<config, chainId>\n>\n\nexport type WaitForTransactionReceiptReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = Compute<\n  {\n    [key in keyof chains]: viem_WaitForTransactionReceiptReturnType<\n      IsNarrowable<chains[key], Chain> extends true ? chains[key] : undefined\n    > & { chainId: chains[key]['id'] }\n  }[number]\n>\n\nexport type WaitForTransactionReceiptErrorType =\n  viem_WaitForTransactionReceiptErrorType\n\nexport async function waitForTransactionReceipt<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: WaitForTransactionReceiptParameters<config, chainId>,\n): Promise<WaitForTransactionReceiptReturnType<config, chainId>> {\n  const { chainId, timeout = 0, ...rest } = parameters\n\n  const client = config.getClient({ chainId })\n  const action = getAction(\n    client,\n    viem_waitForTransactionReceipt,\n    'waitForTransactionReceipt',\n  )\n  const receipt = await action({ ...rest, timeout })\n\n  if (receipt.status === 'reverted') {\n    const action_getTransaction = getAction(\n      client,\n      getTransaction,\n      'getTransaction',\n    )\n    const txn = await action_getTransaction({ hash: receipt.transactionHash })\n    const action_call = getAction(client, call, 'call')\n    const code = await action_call({\n      ...(txn as any),\n      data: txn.input,\n      gasPrice: txn.type !== 'eip1559' ? txn.gasPrice : undefined,\n      maxFeePerGas: txn.type === 'eip1559' ? txn.maxFeePerGas : undefined,\n      maxPriorityFeePerGas:\n        txn.type === 'eip1559' ? txn.maxPriorityFeePerGas : undefined,\n    })\n    const reason = code?.data\n      ? hexToString(`0x${code.data.substring(138)}`)\n      : 'unknown reason'\n    throw new Error(reason)\n  }\n\n  return {\n    ...receipt,\n    chainId: client.chain.id,\n  } as WaitForTransactionReceiptReturnType<config, chainId>\n}\n", "import type {\n  Abi,\n  Account,\n  Chain,\n  Client,\n  ContractFunctionArgs,\n  ContractFunctionName,\n} from 'viem'\nimport {\n  type WriteContractErrorType as viem_WriteContractErrorType,\n  type WriteContractParameters as viem_WriteContractParameters,\n  type WriteContractReturnType as viem_WriteContractReturnType,\n  writeContract as viem_writeContract,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  ConnectorParameter,\n} from '../types/properties.js'\nimport type { Compute, UnionCompute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type WriteContractParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'nonpayable' | 'payable'\n  > = ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  > = ContractFunctionArgs<abi, 'nonpayable' | 'payable', functionName>,\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  allFunctionNames = ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = UnionCompute<\n  {\n    // TODO: Should use `UnionStrictOmit<..., 'chain'>` on `viem_WriteContractParameters` result instead\n    // temp workaround that doesn't affect runtime behavior for for https://github.com/wevm/wagmi/issues/3981\n    [key in keyof chains]: viem_WriteContractParameters<\n      abi,\n      functionName,\n      args,\n      chains[key],\n      Account,\n      chains[key],\n      allFunctionNames\n    >\n  }[number] &\n    Compute<ChainIdParameter<config, chainId>> &\n    ConnectorParameter & {\n      /** @deprecated */\n      __mode?: 'prepared'\n    }\n>\n\nexport type WriteContractReturnType = viem_WriteContractReturnType\n\nexport type WriteContractErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_WriteContractErrorType\n\n/** https://wagmi.sh/core/api/actions/writeContract */\nexport async function writeContract<\n  config extends Config,\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: WriteContractParameters<abi, functionName, args, config, chainId>,\n): Promise<WriteContractReturnType> {\n  const { account, chainId, connector, ...request } = parameters\n\n  let client: Client\n  if (typeof account === 'object' && account?.type === 'local')\n    client = config.getClient({ chainId })\n  else\n    client = await getConnectorClient(config, {\n      account: account ?? undefined,\n      chainId,\n      connector,\n    })\n\n  const action = getAction(client, viem_writeContract, 'writeContract')\n  const hash = await action({\n    ...(request as any),\n    ...(account ? { account } : {}),\n    chain: chainId ? { id: chainId } : null,\n  })\n\n  return hash\n}\n", "import { reconnect } from './actions/reconnect.js'\nimport type { Config, State } from './createConfig.js'\n\ntype HydrateParameters = {\n  initialState?: State | undefined\n  reconnectOnMount?: boolean | undefined\n}\n\nexport function hydrate(config: Config, parameters: HydrateParameters) {\n  const { initialState, reconnectOnMount } = parameters\n\n  if (initialState && !config._internal.store.persist.hasHydrated())\n    config.setState({\n      ...initialState,\n      chainId: config.chains.some((x) => x.id === initialState.chainId)\n        ? initialState.chainId\n        : config.chains[0].id,\n      connections: reconnectOnMount ? initialState.connections : new Map(),\n      status: reconnectOnMount ? 'reconnecting' : 'disconnected',\n    })\n\n  return {\n    async onMount() {\n      if (config._internal.ssr) {\n        await config._internal.store.persist.rehydrate()\n        if (config._internal.mipd) {\n          config._internal.connectors.setState((connectors) => {\n            const rdnsSet = new Set<string>()\n            for (const connector of connectors ?? []) {\n              if (connector.rdns) {\n                const rdnsValues = Array.isArray(connector.rdns)\n                  ? connector.rdns\n                  : [connector.rdns]\n                for (const rdns of rdnsValues) {\n                  rdnsSet.add(rdns)\n                }\n              }\n            }\n            const mipdConnectors = []\n            const providers = config._internal.mipd?.getProviders() ?? []\n            for (const provider of providers) {\n              if (rdnsSet.has(provider.info.rdns)) continue\n              const connectorFn =\n                config._internal.connectors.providerDetailToConnector(provider)\n              const connector = config._internal.connectors.setup(connectorFn)\n              mipdConnectors.push(connector)\n            }\n            return [...connectors, ...mipdConnectors]\n          })\n        }\n      }\n\n      if (reconnectOnMount) reconnect(config)\n      else if (config.storage)\n        // Reset connections that may have been hydrated from storage.\n        config.setState((x) => ({\n          ...x,\n          connections: new Map(),\n        }))\n    },\n  }\n}\n", "import type { Chain, Transport } from 'viem'\n\ntype ExtractRpcUrlsParameters = {\n  transports?: Record<string, Transport> | undefined\n  chain: Chain\n}\n\nexport function extractRpcUrls(parameters: ExtractRpcUrlsParameters) {\n  const { chain } = parameters\n  const fallbackUrl = chain.rpcUrls.default.http[0]\n\n  if (!parameters.transports) return [fallbackUrl]\n\n  const transport = parameters.transports?.[chain.id]?.({ chain })\n  const transports = (transport?.value?.transports as NonNullable<\n    typeof transport\n  >[]) || [transport]\n  return transports.map(({ value }) => value?.url || fallbackUrl)\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,MAAM,OAAO,UAAU;AAA3B,QACI,SAAS;AASb,aAAS,SAAS;AAAA,IAAC;AASnB,QAAI,OAAO,QAAQ;AACjB,aAAO,YAAY,uBAAO,OAAO,IAAI;AAMrC,UAAI,CAAC,IAAI,OAAO,EAAE,UAAW,UAAS;AAAA,IACxC;AAWA,aAAS,GAAG,IAAI,SAAS,MAAM;AAC7B,WAAK,KAAK;AACV,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ;AAAA,IACtB;AAaA,aAAS,YAAY,SAAS,OAAO,IAAI,SAAS,MAAM;AACtD,UAAI,OAAO,OAAO,YAAY;AAC5B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,WAAW,IAAI,GAAG,IAAI,WAAW,SAAS,IAAI,GAC9C,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,QAAQ,QAAQ,GAAG,EAAG,SAAQ,QAAQ,GAAG,IAAI,UAAU,QAAQ;AAAA,eAC3D,CAAC,QAAQ,QAAQ,GAAG,EAAE,GAAI,SAAQ,QAAQ,GAAG,EAAE,KAAK,QAAQ;AAAA,UAChE,SAAQ,QAAQ,GAAG,IAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG,QAAQ;AAE3D,aAAO;AAAA,IACT;AASA,aAAS,WAAW,SAAS,KAAK;AAChC,UAAI,EAAE,QAAQ,iBAAiB,EAAG,SAAQ,UAAU,IAAI,OAAO;AAAA,UAC1D,QAAO,QAAQ,QAAQ,GAAG;AAAA,IACjC;AASA,aAASA,gBAAe;AACtB,WAAK,UAAU,IAAI,OAAO;AAC1B,WAAK,eAAe;AAAA,IACtB;AASA,IAAAA,cAAa,UAAU,aAAa,SAAS,aAAa;AACxD,UAAI,QAAQ,CAAC,GACT,QACA;AAEJ,UAAI,KAAK,iBAAiB,EAAG,QAAO;AAEpC,WAAK,QAAS,SAAS,KAAK,SAAU;AACpC,YAAI,IAAI,KAAK,QAAQ,IAAI,EAAG,OAAM,KAAK,SAAS,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MACtE;AAEA,UAAI,OAAO,uBAAuB;AAChC,eAAO,MAAM,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAC1D;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,YAAY,SAAS,UAAU,OAAO;AAC3D,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,WAAW,KAAK,QAAQ,GAAG;AAE/B,UAAI,CAAC,SAAU,QAAO,CAAC;AACvB,UAAI,SAAS,GAAI,QAAO,CAAC,SAAS,EAAE;AAEpC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK;AAClE,WAAG,CAAC,IAAI,SAAS,CAAC,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,gBAAgB,SAAS,cAAc,OAAO;AACnE,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,CAAC,UAAW,QAAO;AACvB,UAAI,UAAU,GAAI,QAAO;AACzB,aAAO,UAAU;AAAA,IACnB;AASA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AACrE,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG,EAAG,QAAO;AAE/B,UAAI,YAAY,KAAK,QAAQ,GAAG,GAC5B,MAAM,UAAU,QAChB,MACA;AAEJ,UAAI,UAAU,IAAI;AAChB,YAAI,UAAU,KAAM,MAAK,eAAe,OAAO,UAAU,IAAI,QAAW,IAAI;AAE5E,gBAAQ,KAAK;AAAA,UACX,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,OAAO,GAAG;AAAA,UACrD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,EAAE,GAAG;AAAA,UACzD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,EAAE,GAAG;AAAA,UAC7D,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,EAAE,GAAG;AAAA,UACjE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,UACrE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,QAC3E;AAEA,aAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAClD,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,QAC3B;AAEA,kBAAU,GAAG,MAAM,UAAU,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,YAAI,SAAS,UAAU,QACnB;AAEJ,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,cAAI,UAAU,CAAC,EAAE,KAAM,MAAK,eAAe,OAAO,UAAU,CAAC,EAAE,IAAI,QAAW,IAAI;AAElF,kBAAQ,KAAK;AAAA,YACX,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,OAAO;AAAG;AAAA,YACpD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,EAAE;AAAG;AAAA,YACxD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,EAAE;AAAG;AAAA,YAC5D,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,IAAI,EAAE;AAAG;AAAA,YAChE;AACE,kBAAI,CAAC,KAAM,MAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAC7D,qBAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,cAC3B;AAEA,wBAAU,CAAC,EAAE,GAAG,MAAM,UAAU,CAAC,EAAE,SAAS,IAAI;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,IAAAA,cAAa,UAAU,KAAK,SAAS,GAAG,OAAO,IAAI,SAAS;AAC1D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,KAAK;AAAA,IACpD;AAWA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,SAAS;AAC9D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,IACnD;AAYA,IAAAA,cAAa,UAAU,iBAAiB,SAAS,eAAe,OAAO,IAAI,SAAS,MAAM;AACxF,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG,EAAG,QAAO;AAC/B,UAAI,CAAC,IAAI;AACP,mBAAW,MAAM,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,UAAU,IAAI;AAChB,YACE,UAAU,OAAO,OAChB,CAAC,QAAQ,UAAU,UACnB,CAAC,WAAW,UAAU,YAAY,UACnC;AACA,qBAAW,MAAM,GAAG;AAAA,QACtB;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,GAAG,SAAS,CAAC,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AACvE,cACE,UAAU,CAAC,EAAE,OAAO,MACnB,QAAQ,CAAC,UAAU,CAAC,EAAE,QACtB,WAAW,UAAU,CAAC,EAAE,YAAY,SACrC;AACA,mBAAO,KAAK,UAAU,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF;AAKA,YAAI,OAAO,OAAQ,MAAK,QAAQ,GAAG,IAAI,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA,YACpE,YAAW,MAAM,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,qBAAqB,SAAS,mBAAmB,OAAO;AAC7E,UAAI;AAEJ,UAAI,OAAO;AACT,cAAM,SAAS,SAAS,QAAQ;AAChC,YAAI,KAAK,QAAQ,GAAG,EAAG,YAAW,MAAM,GAAG;AAAA,MAC7C,OAAO;AACL,aAAK,UAAU,IAAI,OAAO;AAC1B,aAAK,eAAe;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAKA,IAAAA,cAAa,UAAU,MAAMA,cAAa,UAAU;AACpD,IAAAA,cAAa,UAAU,cAAcA,cAAa,UAAU;AAK5D,IAAAA,cAAa,WAAW;AAKxB,IAAAA,cAAa,eAAeA;AAK5B,QAAI,gBAAgB,OAAO,QAAQ;AACjC,aAAO,UAAUA;AAAA,IACnB;AAAA;AAAA;;;AC/UO,IAAM,UAAU;;;ACEhB,IAAM,aAAa,MAAM,eAAe,OAAO;;;;;;;;;;ACYhD,IAAO,YAAP,MAAO,mBAAkB,MAAK;EAOlC,IAAI,cAAW;AACb,WAAO;EACT;EACA,IAAI,UAAO;AACT,WAAO,WAAU;EACnB;EAEA,YAAY,cAAsB,UAA4B,CAAA,GAAE;;AAC9D,UAAK;;AAdP,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,YAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AAES,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAWd,UAAM,UACJ,QAAQ,iBAAiB,aACrB,QAAQ,MAAM,YACd,aAAQ,UAAR,mBAAe,WACb,QAAQ,MAAM,UACd,QAAQ;AAChB,UAAM,WACJ,QAAQ,iBAAiB,aACrB,QAAQ,MAAM,YAAY,QAAQ,WAClC,QAAQ;AAEd,SAAK,UAAU;MACb,gBAAgB;MAChB;MACA,GAAI,QAAQ,eAAe,CAAC,GAAG,QAAQ,cAAc,EAAE,IAAI,CAAA;MAC3D,GAAI,WACA;QACE,SAAS,KAAK,WAAW,GAAG,QAAQ,QAClC,QAAQ,WAAW,IAAI,QAAQ,QAAQ,KAAK,EAC9C;UAEF,CAAA;MACJ,GAAI,UAAU,CAAC,YAAY,OAAO,EAAE,IAAI,CAAA;MACxC,YAAY,KAAK,OAAO;MACxB,KAAK,IAAI;AAEX,QAAI,QAAQ;AAAO,WAAK,QAAQ,QAAQ;AACxC,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,eAAe,QAAQ;AAC5B,SAAK,eAAe;EACtB;EAEA,KAAK,IAA8B;AACjC,WAAO,uBAAA,MAAI,sBAAA,KAAA,eAAA,EAAM,KAAV,MAAW,MAAM,EAAE;EAC5B;;kGAEM,KAAc,IAA8B;AAChD,MAAI,yBAAK;AAAM,WAAO;AACtB,MAAK,IAAc;AAAO,WAAO,uBAAA,MAAI,sBAAA,KAAAC,gBAAA,EAAM,KAAV,MAAY,IAAc,OAAO,EAAE;AACpE,SAAO;AACT;;;AChEI,IAAO,0BAAP,cAAuC,UAAS;EAEpD,cAAA;AACE,UAAM,uBAAuB;AAFtB,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;AAOI,IAAO,iCAAP,cAA8C,UAAS;EAE3D,cAAA;AACE,UAAM,8BAA8B;AAF7B,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;AAMI,IAAO,6BAAP,cAA0C,UAAS;EAEvD,cAAA;AACE,UAAM,0BAA0B;AAFzB,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;AAMI,IAAO,yBAAP,cAAsC,UAAS;EAEnD,cAAA;AACE,UAAM,sBAAsB;AAFrB,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;AAOI,IAAO,gCAAP,cAA6C,UAAS;EAE1D,YAAY,EACV,SACA,UAAS,GAIV;AACC,UAAM,YAAY,OAAO,8BAA8B,UAAU,IAAI,IAAI;AARlE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAShB;;AAMI,IAAO,8BAAP,cAA2C,UAAS;EAExD,YAAY,EACV,mBACA,iBAAgB,GAIjB;AACC,UACE,2CAA2C,gBAAgB,gDAAgD,iBAAiB,MAC5H;MACE,cAAc;QACZ,sBAAsB,gBAAgB;QACtC,sBAAsB,iBAAiB;;KAE1C;AAfI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAiBhB;;AAOI,IAAO,wCAAP,cAAqD,UAAS;EAElE,YAAY,EAAE,UAAS,GAAmC;AACxD,UAAM,cAAc,UAAU,IAAI,qCAAqC;MACrE,SAAS;QACP;QACA;QACA;QACA,KAAK,GAAG;KACX;AARM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAShB;;;;ACnGI,SAAU,UAAU,GAAQ,GAAM;AACtC,MAAI,MAAM;AAAG,WAAO;AAEpB,MAAI,KAAK,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAC5D,QAAI,EAAE,gBAAgB,EAAE;AAAa,aAAO;AAE5C,QAAI;AACJ,QAAI;AAEJ,QAAI,MAAM,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC,GAAG;AACxC,eAAS,EAAE;AACX,UAAI,WAAW,EAAE;AAAQ,eAAO;AAChC,WAAK,IAAI,QAAQ,QAAQ;AAAK,YAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAG,iBAAO;AACjE,aAAO;IACT;AAEA,QAAI,EAAE,YAAY,OAAO,UAAU;AACjC,aAAO,EAAE,QAAO,MAAO,EAAE,QAAO;AAClC,QAAI,EAAE,aAAa,OAAO,UAAU;AAClC,aAAO,EAAE,SAAQ,MAAO,EAAE,SAAQ;AAEpC,UAAM,OAAO,OAAO,KAAK,CAAC;AAC1B,aAAS,KAAK;AACd,QAAI,WAAW,OAAO,KAAK,CAAC,EAAE;AAAQ,aAAO;AAE7C,SAAK,IAAI,QAAQ,QAAQ;AACvB,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAE;AAAG,eAAO;AAEjE,SAAK,IAAI,QAAQ,QAAQ,KAAK;AAC5B,YAAM,MAAM,KAAK,CAAC;AAElB,UAAI,OAAO,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAAG,eAAO;IAChD;AAEA,WAAO;EACT;AAIA,SAAO,MAAM,KAAK,MAAM;AAC1B;;;ACpCM,IAAO,wBAAP,cAAqC,UAAS;EAElD,cAAA;AACE,UAAM,qBAAqB;AAFpB,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;AAMI,IAAO,+BAAP,cAA4C,UAAS;EAGzD,YAAY,EAAE,UAAS,GAA4B;AACjD,UAAM,IAAI,UAAU,IAAI,kDAAkD;AAHnE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIhB;;;;AC2DI,SAAU,gBAUd,mBAAoC;AACpC,SAAO;AACT;;;ACxDA,SAAS,OAAO;AACV,SAAU,SAAS,aAAiC,CAAA,GAAE;AAC1D,QAAM,EAAE,iBAAiB,MAAM,yBAAwB,IAAK;AAE5D,WAAS,YAAS;AAChB,UAAM,SAAS,WAAW;AAC1B,QAAI,OAAO,WAAW,YAAY;AAChC,YAAM,SAAS,OAAM;AACrB,UAAI;AAAQ,eAAO;IACrB;AAEA,QAAI,OAAO,WAAW;AAAU,aAAO;AAEvC,QAAI,OAAO,WAAW;AACpB,aAAO;QACL,GAAI,UAAU,MAAgC,KAAK;UACjD,IAAI;UACJ,MAAM,GAAG,OAAO,CAAC,EAAG,YAAW,CAAE,GAAG,OAAO,MAAM,CAAC,CAAC;UACnD,UAAU,KAAK,OAAO,CAAC,EAAG,YAAW,CAAE,GAAG,OAAO,MAAM,CAAC,CAAC;;;AAI/D,WAAO;MACL,IAAI;MACJ,MAAM;MACN,SAASC,SAAM;AACb,eAAOA,WAAA,gBAAAA,QAAQ;MACjB;;EAEJ;AAUA,MAAI;AACJ,MAAI;AACJ,MAAIC;AACJ,MAAIC;AAEJ,SAAO,gBAAmD,CAAC,YAAY;IACrE,IAAI,OAAI;AACN,aAAO,UAAS,EAAG;IACrB;IACA,IAAI,KAAE;AACJ,aAAO,UAAS,EAAG;IACrB;IACA,IAAI,OAAI;AACN,aAAO,UAAS,EAAG;IACrB;;IAEA,IAAI,qBAAkB;AACpB,aAAO;IACT;IACA,MAAM,SAAS;IACf,MAAM,QAAK;AACT,YAAM,WAAW,MAAM,KAAK,YAAW;AAEvC,WAAI,qCAAU,OAAM,WAAW,QAAQ;AACrC,YAAI,CAACD,UAAS;AACZ,UAAAA,WAAU,KAAK,UAAU,KAAK,IAAI;AAClC,mBAAS,GAAG,WAAWA,QAAO;QAChC;AAIA,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,mBAAS,GAAG,mBAAmB,eAAe;QAChD;MACF;IACF;IACA,MAAM,QAAQ,EAAE,SAAS,gBAAAE,gBAAc,IAAK,CAAA,GAAE;AAhHlD;AAiHM,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAE9C,UAAI,WAA+B,CAAA;AACnC,UAAIA;AAAgB,mBAAW,MAAM,KAAK,YAAW,EAAG,MAAM,MAAM,CAAA,CAAE;eAC7D,gBAAgB;AAEvB,YAAI;AACF,gBAAM,cAAc,MAAM,SAAS,QAAQ;YACzC,QAAQ;YACR,QAAQ,CAAC,EAAE,cAAc,CAAA,EAAE,CAAE;WAC9B;AACD,sBAAY,mCAAY,CAAC,MAAb,mBAAgB,YAAhB,mBAA0B,OAA1B,mBAA8B,UAA9B,mBAAkD,IAC5D,CAAC,MAAM,WAAW,CAAC;AAKrB,cAAI,SAAS,SAAS,GAAG;AACvB,kBAAM,iBAAiB,MAAM,KAAK,YAAW;AAC7C,uBAAW;UACb;QACF,SAAS,KAAK;AACZ,gBAAM,QAAQ;AAGd,cAAI,MAAM,SAAS,yBAAyB;AAC1C,kBAAM,IAAI,yBAAyB,KAAK;AAE1C,cAAI,MAAM,SAAS,4BAA4B;AAAM,kBAAM;QAC7D;MACF;AAEA,UAAI;AACF,YAAI,EAAC,qCAAU,WAAU,CAACA,iBAAgB;AACxC,gBAAM,oBAAoB,MAAM,SAAS,QAAQ;YAC/C,QAAQ;WACT;AACD,qBAAW,kBAAkB,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;QACvD;AAIA,YAAIF,UAAS;AACX,mBAAS,eAAe,WAAWA,QAAO;AAC1C,UAAAA,WAAU;QACZ;AACA,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,mBAAS,GAAG,mBAAmB,eAAe;QAChD;AACA,YAAI,CAAC,cAAc;AACjB,yBAAe,KAAK,eAAe,KAAK,IAAI;AAC5C,mBAAS,GAAG,gBAAgB,YAAY;QAC1C;AACA,YAAI,CAACC,aAAY;AACf,UAAAA,cAAa,KAAK,aAAa,KAAK,IAAI;AACxC,mBAAS,GAAG,cAAcA,WAAU;QACtC;AAGA,YAAI,iBAAiB,MAAM,KAAK,WAAU;AAC1C,YAAI,WAAW,mBAAmB,SAAS;AACzC,gBAAM,QAAQ,MAAM,KAAK,YAAa,EAAE,QAAO,CAAE,EAAE,MAAM,CAAC,UAAS;AACjE,gBAAI,MAAM,SAAS,yBAAyB;AAAM,oBAAM;AACxD,mBAAO,EAAE,IAAI,eAAc;UAC7B,CAAC;AACD,4BAAiB,+BAAO,OAAM;QAChC;AAGA,YAAI;AACF,kBAAM,YAAO,YAAP,mBAAgB,WAAW,GAAG,KAAK,EAAE;AAG7C,YAAI,CAAC,WAAW;AACd,kBAAM,YAAO,YAAP,mBAAgB,QAAQ,sBAAsB;AAEtD,eAAO,EAAE,UAAU,SAAS,eAAc;MAC5C,SAAS,KAAK;AACZ,cAAM,QAAQ;AACd,YAAI,MAAM,SAAS,yBAAyB;AAC1C,gBAAM,IAAI,yBAAyB,KAAK;AAC1C,YAAI,MAAM,SAAS,4BAA4B;AAC7C,gBAAM,IAAI,4BAA4B,KAAK;AAC7C,cAAM;MACR;IACF;IACA,MAAM,aAAU;AAzMpB;AA0MM,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAG9C,UAAI,cAAc;AAChB,iBAAS,eAAe,gBAAgB,YAAY;AACpD,uBAAe;MACjB;AACA,UAAIA,aAAY;AACd,iBAAS,eAAe,cAAcA,WAAU;AAChD,QAAAA,cAAa;MACf;AACA,UAAI,CAACD,UAAS;AACZ,QAAAA,WAAU,KAAK,UAAU,KAAK,IAAI;AAClC,iBAAS,GAAG,WAAWA,QAAO;MAChC;AAIA,UAAI;AAGF,cAAM,YACJ;;UAEE,SAAS,QAIN;;YAED,QAAQ;YACR,QAAQ,CAAC,EAAE,cAAc,CAAA,EAAE,CAAE;WAC9B;WACH,EAAE,SAAS,IAAG,CAAE;MAEpB,QAAQ;MAAC;AAGT,UAAI,gBAAgB;AAClB,gBAAM,YAAO,YAAP,mBAAgB,QAAQ,GAAG,KAAK,EAAE,iBAAiB;MAC3D;AAEA,UAAI,CAAC,WAAW;AACd,gBAAM,YAAO,YAAP,mBAAgB,WAAW;IACrC;IACA,MAAM,cAAW;AACf,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAC9C,YAAM,WAAW,MAAM,SAAS,QAAQ,EAAE,QAAQ,eAAc,CAAE;AAClE,aAAO,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;IAC1C;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAC9C,YAAM,aAAa,MAAM,SAAS,QAAQ,EAAE,QAAQ,cAAa,CAAE;AACnE,aAAO,OAAO,UAAU;IAC1B;IACA,MAAM,cAAW;AACf,UAAI,OAAO,WAAW;AAAa,eAAO;AAE1C,UAAI;AACJ,YAAM,SAAS,UAAS;AACxB,UAAI,OAAO,OAAO,aAAa;AAC7B,mBAAW,OAAO,SAAS,MAA4B;eAChD,OAAO,OAAO,aAAa;AAClC,mBAAW,aAAa,QAAQ,OAAO,QAAQ;;AAC5C,mBAAW,OAAO;AAIvB,UAAI,YAAY,CAAC,SAAS,gBAAgB;AAExC,YAAI,SAAS,YAAY,OAAO,SAAS,QAAQ;AAC/C,mBAAS,iBACP,SAAS;;AACR,mBAAS,iBAAiB,MAAK;UAAE;MACxC;AAEA,aAAO;IACT;IACA,MAAM,eAAY;AA3RtB;AA4RM,UAAI;AACF,cAAM,iBACJ;QAEC,QAAM,YAAO,YAAP,mBAAgB,QAAQ,GAAG,KAAK,EAAE;AAC3C,YAAI;AAAgB,iBAAO;AAK3B,YAAI,CAAC,WAAW,QAAQ;AACtB,gBAAM,YAAY,QAAM,YAAO,YAAP,mBAAgB,QAAQ;AAChD,cAAI,CAAC;AAAW,mBAAO;QACzB;AAEA,cAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAI,CAAC,UAAU;AACb,cACE,6BAA6B,UAC7B,6BAA6B,OAC7B;AAIA,kBAAM,iBAAiB,YAAW;AAChC,kBAAI,OAAO,WAAW;AACpB,uBAAO,oBACL,wBACA,cAAc;AAElB,oBAAMG,YAAW,MAAM,KAAK,YAAW;AACvC,qBAAO,CAAC,CAACA;YACX;AACA,kBAAM,UACJ,OAAO,6BAA6B,WAChC,2BACA;AACN,kBAAM,MAAM,MAAM,QAAQ,KAAK;cAC7B,GAAI,OAAO,WAAW,cAClB;gBACE,IAAI,QAAiB,CAAC,YACpB,OAAO,iBACL,wBACA,MAAM,QAAQ,eAAc,CAAE,GAC9B,EAAE,MAAM,KAAI,CAAE,CACf;kBAGL,CAAA;cACJ,IAAI,QAAiB,CAAC,YACpB,WAAW,MAAM,QAAQ,eAAc,CAAE,GAAG,OAAO,CAAC;aAEvD;AACD,gBAAI;AAAK,qBAAO;UAClB;AAEA,gBAAM,IAAI,sBAAqB;QACjC;AAIA,cAAM,WAAW,MAAM,UAAU,MAAM,KAAK,YAAW,CAAE;AACzD,eAAO,CAAC,CAAC,SAAS;MACpB,QAAQ;AACN,eAAO;MACT;IACF;IACA,MAAM,YAAY,EAAE,2BAA2B,QAAO,GAAE;AA/V5D;AAgWM,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAE9C,YAAM,QAAQ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACxD,UAAI,CAAC;AAAO,cAAM,IAAI,iBAAiB,IAAI,wBAAuB,CAAE;AAEpE,YAAM,UAAU,IAAI,QAAc,CAAC,YAAW;AAC5C,cAAM,WAAY,CAAC,SAAQ;AACzB,cAAI,aAAa,QAAQ,KAAK,YAAY,SAAS;AACjD,mBAAO,QAAQ,IAAI,UAAU,QAAQ;AACrC,oBAAO;UACT;QACF;AACA,eAAO,QAAQ,GAAG,UAAU,QAAQ;MACtC,CAAC;AAED,UAAI;AACF,cAAM,QAAQ,IAAI;UAChB,SACG,QAAQ;YACP,QAAQ;YACR,QAAQ,CAAC,EAAE,SAAS,YAAY,OAAO,EAAC,CAAE;WAC3C,EAMA,KAAK,YAAW;AACf,kBAAM,iBAAiB,MAAM,KAAK,WAAU;AAC5C,gBAAI,mBAAmB;AACrB,qBAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;UAC7C,CAAC;UACH;SACD;AACD,eAAO;MACT,SAAS,KAAK;AACZ,cAAM,QAAQ;AAGd,YACE,MAAM,SAAS;;UAGd,0CACG,SADH,mBACS,kBADT,mBACwB,UAAS,MAClC;AACA,cAAI;AACF,kBAAM,EAAE,SAAS,eAAe,GAAG,eAAc,IAC/C,MAAM,kBAAkB,CAAA;AAC1B,gBAAI;AACJ,gBAAI,uEAA2B;AAC7B,kCAAoB,0BAA0B;qBACvC;AACP,kCAAoB;gBAClB,cAAc;gBACd,GAAG,OAAO,OAAO,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG;;AAGrD,gBAAI;AACJ,iBAAI,4EAA2B,YAA3B,mBAAoC;AACtC,wBAAU,0BAA0B;;AACjC,wBAAU,GAAC,WAAM,QAAQ,YAAd,mBAAuB,KAAK,OAAM,EAAE;AAEpD,kBAAM,mBAAmB;cACvB;cACA,SAAS,YAAY,OAAO;cAC5B,YAAW,uEAA2B,cAAa,MAAM;cACzD,UAAU,uEAA2B;cACrC,iBACE,uEAA2B,mBAC3B,MAAM;cACR;;AAGF,kBAAM,QAAQ,IAAI;cAChB,SACG,QAAQ;gBACP,QAAQ;gBACR,QAAQ,CAAC,gBAAgB;eAC1B,EACA,KAAK,YAAW;AACf,sBAAM,iBAAiB,MAAM,KAAK,WAAU;AAC5C,oBAAI,mBAAmB;AACrB,yBAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;;AAEzC,wBAAM,IAAI,yBACR,IAAI,MAAM,4CAA4C,CAAC;cAE7D,CAAC;cACH;aACD;AAED,mBAAO;UACT,SAASC,QAAO;AACd,kBAAM,IAAI,yBAAyBA,MAAc;UACnD;QACF;AAEA,YAAI,MAAM,SAAS,yBAAyB;AAC1C,gBAAM,IAAI,yBAAyB,KAAK;AAC1C,cAAM,IAAI,iBAAiB,KAAK;MAClC;IACF;IACA,MAAM,kBAAkB,UAAQ;AAxcpC;AA0cM,UAAI,SAAS,WAAW;AAAG,aAAK,aAAY;eAEnC,OAAO,QAAQ,cAAc,SAAS,GAAG;AAChD,cAAM,WAAW,MAAM,KAAK,WAAU,GAAI,SAAQ;AAClD,aAAK,UAAU,EAAE,QAAO,CAAE;AAE1B,YAAI;AACF,kBAAM,YAAO,YAAP,mBAAgB,WAAW,GAAG,KAAK,EAAE;MAC/C;AAGE,eAAO,QAAQ,KAAK,UAAU;UAC5B,UAAU,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;SAC5C;IACL;IACA,eAAe,OAAK;AAClB,YAAM,UAAU,OAAO,KAAK;AAC5B,aAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;IAC3C;IACA,MAAM,UAAU,aAAW;AACzB,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,SAAS,WAAW;AAAG;AAE3B,YAAM,UAAU,OAAO,YAAY,OAAO;AAC1C,aAAO,QAAQ,KAAK,WAAW,EAAE,UAAU,QAAO,CAAE;AAGpD,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,UAAU;AACZ,YAAIJ,UAAS;AACX,mBAAS,eAAe,WAAWA,QAAO;AAC1C,UAAAA,WAAU;QACZ;AACA,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,mBAAS,GAAG,mBAAmB,eAAe;QAChD;AACA,YAAI,CAAC,cAAc;AACjB,yBAAe,KAAK,eAAe,KAAK,IAAI;AAC5C,mBAAS,GAAG,gBAAgB,YAAY;QAC1C;AACA,YAAI,CAACC,aAAY;AACf,UAAAA,cAAa,KAAK,aAAa,KAAK,IAAI;AACxC,mBAAS,GAAG,cAAcA,WAAU;QACtC;MACF;IACF;IACA,MAAM,aAAa,OAAK;AACtB,YAAM,WAAW,MAAM,KAAK,YAAW;AAIvC,UAAI,SAAU,MAAyB,SAAS,MAAM;AACpD,YAAI,YAAY,CAAC,EAAE,MAAM,KAAK,YAAW,GAAI;AAAQ;MACvD;AAKA,aAAO,QAAQ,KAAK,YAAY;AAGhC,UAAI,UAAU;AACZ,YAAI,cAAc;AAChB,mBAAS,eAAe,gBAAgB,YAAY;AACpD,yBAAe;QACjB;AACA,YAAIA,aAAY;AACd,mBAAS,eAAe,cAAcA,WAAU;AAChD,UAAAA,cAAa;QACf;AACA,YAAI,CAACD,UAAS;AACZ,UAAAA,WAAU,KAAK,UAAU,KAAK,IAAI;AAClC,mBAAS,GAAG,WAAWA,QAAO;QAChC;MACF;IACF;IACA;AACJ;AAEA,IAAM,YAAY;EAChB,gBAAgB;IACd,IAAI;IACJ,MAAM;IACN,SAASD,SAAM;AACb,UAAIA,WAAA,gBAAAA,QAAQ;AAAyB,eAAOA,QAAO;AACnD,aAAO,aAAaA,SAAQ,kBAAkB;IAChD;;EAEF,UAAU;IACR,IAAI;IACJ,MAAM;IACN,SAASA,SAAM;AACb,aAAO,aAAaA,SAAQ,CAAC,aAAY;AACvC,YAAI,CAAC,SAAS;AAAY,iBAAO;AAGjC,YAAI,SAAS,iBAAiB,CAAC,SAAS,WAAW,CAAC,SAAS;AAC3D,iBAAO;AAET,cAAM,QAAQ;UACZ;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;AAEF,mBAAW,QAAQ;AAAO,cAAI,SAAS,IAAI;AAAG,mBAAO;AACrD,eAAO;MACT,CAAC;IACH;;EAEF,SAAS;IACP,IAAI;IACJ,MAAM;IACN,SAASA,SAAM;AA1kBnB;AA2kBM,WAAI,KAAAA,WAAA,gBAAAA,QAAQ,YAAR,mBAAiB;AAAU,gBAAO,KAAAA,QAAO,YAAP,mBAAgB;AACtD,aAAO,aAAaA,SAAQ,WAAW;IACzC;;;AA4FJ,SAAS,aACPA,SACA,QAAsE;AAEtE,WAAS,WAAW,UAAwB;AAC1C,QAAI,OAAO,WAAW;AAAY,aAAO,OAAO,QAAQ;AACxD,QAAI,OAAO,WAAW;AAAU,aAAO,SAAS,MAAM;AACtD,WAAO;EACT;AAEA,QAAM,WAAYA,QAAkB;AACpC,MAAI,qCAAU;AACZ,WAAO,SAAS,UAAU,KAAK,CAAC,aAAa,WAAW,QAAQ,CAAC;AACnE,MAAI,YAAY,WAAW,QAAQ;AAAG,WAAO;AAC7C,SAAO;AACT;;;AC/oBA,KAAK,OAAO;AACN,SAAU,KAAK,YAA0B;AAC7C,QAAM,mBAAmB,oBAAI,IAAG;AAChC,QAAM,WACJ,WAAW,YACV,EAAE,kBAAkB,MAAK;AAe5B,MAAI,YAAY,SAAS;AACzB,MAAI;AAEJ,SAAO,gBAAsC,CAAC,YAAY;IACxD,IAAI;IACJ,MAAM;IACN,MAAM,KAAK;IACX,MAAM,QAAK;AACT,yBAAmB,OAAO,OAAO,CAAC,EAAE;IACtC;IACA,MAAM,QAAQ,EAAE,QAAO,IAAK,CAAA,GAAE;AAC5B,UAAI,SAAS,cAAc;AACzB,YAAI,OAAO,SAAS,iBAAiB;AACnC,gBAAM,IAAI,yBAAyB,IAAI,MAAM,oBAAoB,CAAC;AACpE,cAAM,SAAS;MACjB;AAEA,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,WAAW,MAAM,SAAS,QAAQ;QACtC,QAAQ;OACT;AAED,UAAI,iBAAiB,MAAM,KAAK,WAAU;AAC1C,UAAI,WAAW,mBAAmB,SAAS;AACzC,cAAM,QAAQ,MAAM,KAAK,YAAa,EAAE,QAAO,CAAE;AACjD,yBAAiB,MAAM;MACzB;AAEA,kBAAY;AAEZ,aAAO;QACL,UAAU,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;QAC3C,SAAS;;IAEb;IACA,MAAM,aAAU;AACd,kBAAY;IACd;IACA,MAAM,cAAW;AACf,UAAI,CAAC;AAAW,cAAM,IAAI,2BAA0B;AACpD,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,WAAW,MAAM,SAAS,QAAQ,EAAE,QAAQ,eAAc,CAAE;AAClE,aAAO,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;IAC1C;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,aAAa,MAAM,SAAS,QAAQ,EAAE,QAAQ,cAAa,CAAE;AACnE,aAAO,QAAQ,YAAY,QAAQ;IACrC;IACA,MAAM,eAAY;AAChB,UAAI,CAAC,SAAS;AAAW,eAAO;AAChC,UAAI,CAAC;AAAW,eAAO;AACvB,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,aAAO,CAAC,CAAC,SAAS;IACpB;IACA,MAAM,YAAY,EAAE,QAAO,GAAE;AAC3B,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,QAAQ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACxD,UAAI,CAAC;AAAO,cAAM,IAAI,iBAAiB,IAAI,wBAAuB,CAAE;AAEpE,YAAM,SAAS,QAAQ;QACrB,QAAQ;QACR,QAAQ,CAAC,EAAE,SAAS,YAAY,OAAO,EAAC,CAAE;OAC3C;AACD,aAAO;IACT;IACA,kBAAkB,UAAQ;AACxB,UAAI,SAAS,WAAW;AAAG,aAAK,aAAY;;AAE1C,eAAO,QAAQ,KAAK,UAAU;UAC5B,UAAU,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;SAC5C;IACL;IACA,eAAe,OAAK;AAClB,YAAM,UAAU,OAAO,KAAK;AAC5B,aAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;IAC3C;IACA,MAAM,aAAa,QAAM;AACvB,aAAO,QAAQ,KAAK,YAAY;AAChC,kBAAY;IACd;IACA,MAAM,YAAY,EAAE,QAAO,IAAK,CAAA,GAAE;AAChC,YAAM,QACJ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,KAAK,OAAO,OAAO,CAAC;AAChE,YAAM,MAAM,MAAM,QAAQ,QAAQ,KAAK,CAAC;AAExC,YAAM,UAA4B,OAAO,EAAE,QAAQ,OAAM,MAAM;AAE7D,YAAI,WAAW;AAAe,iBAAO,YAAY,gBAAgB;AACjE,YAAI,WAAW;AAAuB,iBAAO,WAAW;AACxD,YAAI,WAAW;AACb,cAAI,SAAS,oBAAoB;AAC/B,gBAAI,OAAO,SAAS,uBAAuB;AACzC,oBAAM,IAAI,yBACR,IAAI,MAAM,4BAA4B,CAAC;AAE3C,kBAAM,SAAS;UACjB;;AAGF,YAAI,WAAW,8BAA8B;AAC3C,cAAI,SAAS,kBAAkB;AAC7B,gBAAI,OAAO,SAAS,qBAAqB;AACvC,oBAAM,IAAI,yBACR,IAAI,MAAM,yBAAyB,CAAC;AAExC,kBAAM,SAAS;UACjB;AAEA,6BAAmB,QAAS,OAAkB,CAAC,EAAE,SAAS,QAAQ;AAClE,eAAK,eAAe,iBAAiB,SAAQ,CAAE;AAC/C;QACF;AAEA,YAAI,WAAW,qBAAqB;AAClC,cAAI,SAAS,iBAAiB;AAC5B,gBAAI,OAAO,SAAS,oBAAoB;AACtC,oBAAM,IAAI,yBACR,IAAI,MAAM,yBAAyB,CAAC;AAExC,kBAAM,SAAS;UACjB;AACA,iBAAO;QACT;AAEA,YAAI,WAAW;AACb,iBAAO;YACL,UAAU;cACR,kBAAkB;gBAChB,WACG,OAAiB,CAAC,MACnB;;cAEJ,aAAa;gBACX,WAAW;;;YAGf,WAAW;cACT,kBAAkB;gBAChB,WACG,OAAiB,CAAC,MACnB;;;;AAKV,YAAI,WAAW,oBAAoB;AACjC,gBAAM,SAAS,CAAA;AACf,gBAAM,QAAS,OAAe,CAAC,EAAE;AACjC,qBAAWM,SAAQ,OAAO;AACxB,kBAAM,EAAE,QAAAC,SAAQ,OAAAC,OAAK,IAAK,MAAM,IAAI,KAAK,KAAK;cAC5C,MAAM;gBACJ,QAAQ;gBACR,QAAQ,CAACF,KAAI;;aAEhB;AACD,gBAAIE;AACF,oBAAM,IAAI,gBAAgB;gBACxB,MAAM,EAAE,QAAQ,OAAM;gBACtB,OAAAA;gBACA;eACD;AACH,mBAAO,KAAKD,OAAM;UACpB;AACA,gBAAM,KAAK,UAAU,YAAY,KAAK,UAAU,KAAK,CAAC,CAAC;AACvD,2BAAiB,IAAI,IAAI,MAAM;AAC/B,iBAAO,EAAE,GAAE;QACb;AAEA,YAAI,WAAW,yBAAyB;AACtC,gBAAM,SAAS,iBAAiB,IAAK,OAAe,CAAC,CAAC;AACtD,cAAI,CAAC;AACH,mBAAO;cACL,QAAQ;cACR,SAAS;cACT,IAAK,OAAe,CAAC;cACrB,QAAQ;cACR,UAAU,CAAA;cACV,SAAS;;AAGb,gBAAM,WAAW,MAAM,QAAQ,IAC7B,OAAO,IAAI,OAAO,SAAQ;AACxB,kBAAM,EAAE,QAAAA,SAAQ,OAAAC,OAAK,IAAK,MAAM,IAAI,KAAK,KAAK;cAC5C,MAAM;gBACJ,QAAQ;gBACR,QAAQ,CAAC,IAAI;gBACb,IAAI;;aAEP;AACD,gBAAIA;AACF,oBAAM,IAAI,gBAAgB;gBACxB,MAAM,EAAE,QAAQ,OAAM;gBACtB,OAAAA;gBACA;eACD;AACH,gBAAI,CAACD;AAAQ,qBAAO;AACpB,mBAAO;cACL,WAAWA,QAAO;cAClB,aAAaA,QAAO;cACpB,SAASA,QAAO;cAChB,MAAMA,QAAO;cACb,QAAQA,QAAO;cACf,iBAAiBA,QAAO;;UAE5B,CAAC,CAAC;AAEJ,gBAAM,YAAY,SAAS,OAAO,CAAC,MAAM,MAAM,IAAI;AACnD,cAAI,UAAU,WAAW;AACvB,mBAAO;cACL,QAAQ;cACR,SAAS;cACT,IAAK,OAAe,CAAC;cACrB,QAAQ;cACR,UAAU,CAAA;cACV,SAAS;;AAEb,iBAAO;YACL,QAAQ;YACR,SAAS;YACT,IAAK,OAAe,CAAC;YACrB,QAAQ;YACR,UAAU;YACV,SAAS;;QAEb;AAEA,YAAI,WAAW;AAA0B;AAGzC,YAAI,WAAW,iBAAiB;AAC9B,cAAI,SAAS,kBAAkB;AAC7B,gBAAI,OAAO,SAAS,qBAAqB;AACvC,oBAAM,IAAI,yBACR,IAAI,MAAM,yBAAyB,CAAC;AAExC,kBAAM,SAAS;UACjB;AAEA,mBAAS;AAET,mBAAS,CAAE,OAAkB,CAAC,GAAI,OAAkB,CAAC,CAAC;QACxD;AAEA,cAAM,OAAO,EAAE,QAAQ,OAAM;AAC7B,cAAM,EAAE,OAAO,OAAM,IAAK,MAAM,IAAI,KAAK,KAAK,EAAE,KAAI,CAAE;AACtD,YAAI;AAAO,gBAAM,IAAI,gBAAgB,EAAE,MAAM,OAAO,IAAG,CAAE;AAEzD,eAAO;MACT;AACA,aAAO,OAAO,EAAE,QAAO,CAAE,EAAE,EAAE,YAAY,EAAC,CAAE;IAC9C;IACA;AACJ;;;ACxTM,SAAU,YAAkB,OAAe,SAAiB;AAChE,SAAO,KAAK,MAAM,OAAO,CAAC,KAAK,WAAU;AACvC,QAAIE,SAAQ;AACZ,SAAIA,UAAA,gBAAAA,OAAO,YAAW;AAAU,MAAAA,SAAQ,OAAOA,OAAM,KAAK;AAC1D,SAAIA,UAAA,gBAAAA,OAAO,YAAW;AAAO,MAAAA,SAAQ,IAAI,IAAIA,OAAM,KAAK;AACxD,YAAO,mCAAU,KAAKA,YAAUA;EAClC,CAAC;AACH;;;ACFA,SAAS,gBAAgB,MAAgB,QAAc;AACrD,SAAO,KAAK,MAAM,GAAG,MAAM,EAAE,KAAK,GAAG,KAAK;AAC5C;AASA,SAAS,UAAU,OAAc,OAAU;AACzC,QAAM,EAAE,OAAM,IAAK;AAEnB,WAASC,SAAQ,GAAGA,SAAQ,QAAQ,EAAEA,QAAO;AAC3C,QAAI,MAAMA,MAAK,MAAM,OAAO;AAC1B,aAAOA,SAAQ;IACjB;EACF;AAEA,SAAO;AACT;AAYA,SAAS,eACP,UACA,kBAAsD;AAEtD,QAAM,cAAc,OAAO,aAAa;AACxC,QAAM,sBAAsB,OAAO,qBAAqB;AAExD,QAAM,QAAe,CAAA;AACrB,QAAM,OAAiB,CAAA;AAEvB,SAAO,SAAS,QAAmB,KAAa,OAAU;AACxD,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,MAAM,QAAQ;AAChB,cAAM,aAAa,UAAU,OAAO,IAAI;AAExC,YAAI,eAAe,GAAG;AACpB,gBAAM,MAAM,MAAM,IAAI;QACxB,OAAO;AACL,gBAAM,OAAO,UAAU;AACvB,eAAK,OAAO,UAAU;QACxB;AAEA,aAAK,KAAK,MAAM,IAAI;AAEpB,cAAM,cAAc,UAAU,OAAO,KAAK;AAE1C,YAAI,gBAAgB,GAAG;AACrB,iBAAO,sBACH,iBAAiB,KACf,MACA,KACA,OACA,gBAAgB,MAAM,WAAW,CAAC,IAEpC,QAAQ,gBAAgB,MAAM,WAAW,CAAC;QAChD;MACF,OAAO;AACL,cAAM,CAAC,IAAI;AACX,aAAK,CAAC,IAAI;MACZ;IACF;AAEA,WAAO,cAAc,SAAS,KAAK,MAAM,KAAK,KAAK,IAAI;EACzD;AACF;AAaM,SAAU,UACd,OACA,UACA,QACA,kBAAsD;AAEtD,SAAO,KAAK,UACV,OACA,eAAe,CAAC,KAAK,WAAU;AAC7B,QAAIC,SAAQ;AACZ,QAAI,OAAOA,WAAU;AACnB,MAAAA,SAAQ,EAAE,QAAQ,UAAU,OAAO,OAAO,SAAQ,EAAE;AACtD,QAAIA,kBAAiB;AACnB,MAAAA,SAAQ,EAAE,QAAQ,OAAO,OAAO,MAAM,KAAK,OAAO,QAAO,CAAE,EAAC;AAC9D,YAAO,qCAAW,KAAKA,YAAUA;EACnC,GAAG,gBAAgB,GACnB,UAAU,MAAS;AAEvB;;;AClEM,SAAU,cAGd,YAAmC;AACnC,QAAM,EACJ,aAAAC,eAAc,aACd,KAAK,SAAS,SACd,WAAAC,aAAY,WACZ,UAAU,YAAW,IACnB;AAEJ,WAAS,OAAa,OAAW;AAC/B,QAAI,iBAAiB;AAAS,aAAO,MAAM,KAAK,CAAC,MAAM,CAAC,EAAE,MAAM,MAAM,IAAI;AAC1E,WAAO;EACT;AAEA,SAAO;IACL,GAAG;IACH,KAAK;IACL,MAAM,QAAQ,KAAK,cAAY;AAC7B,YAAM,QAAQ,QAAQ,QAAQ,GAAG,MAAM,IAAI,GAAa,EAAE;AAC1D,YAAM,YAAY,MAAM,OAAO,KAAK;AACpC,UAAI;AAAW,eAAOD,aAAY,SAAS,KAAK;AAChD,aAAQ,gBAAgB;IAC1B;IACA,MAAM,QAAQ,KAAK,OAAK;AACtB,YAAM,aAAa,GAAG,MAAM,IAAI,GAAa;AAC7C,UAAI,UAAU;AAAM,cAAM,OAAO,QAAQ,WAAW,UAAU,CAAC;;AAC1D,cAAM,OAAO,QAAQ,QAAQ,YAAYC,WAAU,KAAK,CAAC,CAAC;IACjE;IACA,MAAM,WAAW,KAAG;AAClB,YAAM,OAAO,QAAQ,WAAW,GAAG,MAAM,IAAI,GAAa,EAAE,CAAC;IAC/D;;AAEJ;AAEO,IAAM,cAAc;EACzB,SAAS,MAAM;EACf,SAAS,MAAK;EAAE;EAChB,YAAY,MAAK;EAAE;;AAGf,SAAU,oBAAiB;AAC/B,QAAM,WAAW,MAAK;AACpB,QAAI,OAAO,WAAW,eAAe,OAAO;AAC1C,aAAO,OAAO;AAChB,WAAO;EACT,GAAE;AACF,SAAO;IACL,QAAQ,KAAG;AACT,aAAO,QAAQ,QAAQ,GAAG;IAC5B;IACA,WAAW,KAAG;AACZ,cAAQ,WAAW,GAAG;IACxB;IACA,QAAQ,KAAK,OAAK;AAChB,UAAI;AACF,gBAAQ,QAAQ,KAAK,KAAK;MAE5B,QAAQ;MAAC;IACX;;AAEJ;;;ACnEM,SAAU,iBACd,UAAoC;AAEpC,MAAI,OAAO,WAAW;AAAa;AACnC,QAAM,UAAU,CAAC,UACf,SAAS,MAAM,MAAM;AAEvB,SAAO,iBAAiB,4BAA4B,OAAO;AAE3D,SAAO,cAAc,IAAI,YAAY,yBAAyB,CAAC;AAE/D,SAAO,MAAM,OAAO,oBAAoB,4BAA4B,OAAO;AAC7E;;;ACNM,SAAU,cAAW;AACzB,QAAM,YAA2B,oBAAI,IAAG;AACxC,MAAI,kBAAoD,CAAA;AAExD,QAAM,UAAU,MACd,iBAAiB,CAAC,mBAAkB;AAClC,QACE,gBAAgB,KACd,CAAC,EAAE,KAAI,MAAO,KAAK,SAAS,eAAe,KAAK,IAAI;AAGtD;AAEF,sBAAkB,CAAC,GAAG,iBAAiB,cAAc;AACrD,cAAU,QAAQ,CAAC,aACjB,SAAS,iBAAiB,EAAE,OAAO,CAAC,cAAc,EAAC,CAAE,CAAC;EAE1D,CAAC;AACH,MAAI,UAAU,QAAO;AAErB,SAAO;IACL,aAAU;AACR,aAAO;IACT;IACA,QAAK;AACH,gBAAU,QAAQ,CAAC,aACjB,SAAS,CAAA,GAAI,EAAE,SAAS,CAAC,GAAG,eAAe,EAAC,CAAE,CAAC;AAEjD,wBAAkB,CAAA;IACpB;IACA,UAAO;AACL,WAAK,MAAK;AACV,gBAAU,MAAK;AACf;IACF;IACA,aAAa,EAAE,KAAI,GAAE;AACnB,aAAO,gBAAgB,KACrB,CAAC,mBAAmB,eAAe,KAAK,SAAS,IAAI;IAEzD;IACA,eAAY;AACV,aAAO;IACT;IACA,QAAK;AACH,WAAK,MAAK;AACV;AACA,gBAAU,QAAO;IACnB;IACA,UAAU,UAAU,EAAE,gBAAe,IAAK,CAAA,GAAE;AAC1C,gBAAU,IAAI,QAAQ;AACtB,UAAI;AAAiB,iBAAS,iBAAiB,EAAE,OAAO,gBAAe,CAAE;AACzE,aAAO,MAAM,UAAU,OAAO,QAAQ;IACxC;;AAEJ;;;AC+GA,IAAM,4BAA4B,CAAC,OAAO,CAAC,KAAK,KAAK,QAAQ;AAC3D,QAAM,gBAAgB,IAAI;AAC1B,MAAI,YAAY,CAAC,UAAU,aAAa,YAAY;AAClD,QAAI,WAAW;AACf,QAAI,aAAa;AACf,YAAM,cAAc,WAAW,OAAO,SAAS,QAAQ,eAAe,OAAO;AAC7E,UAAI,eAAe,SAAS,IAAI,SAAS,CAAC;AAC1C,iBAAW,CAAC,UAAU;AACpB,cAAM,YAAY,SAAS,KAAK;AAChC,YAAI,CAAC,WAAW,cAAc,SAAS,GAAG;AACxC,gBAAM,gBAAgB;AACtB,sBAAY,eAAe,WAAW,aAAa;AAAA,QACrD;AAAA,MACF;AACA,UAAI,WAAW,OAAO,SAAS,QAAQ,iBAAiB;AACtD,oBAAY,cAAc,YAAY;AAAA,MACxC;AAAA,IACF;AACA,WAAO,cAAc,QAAQ;AAAA,EAC/B;AACA,QAAM,eAAe,GAAG,KAAK,KAAK,GAAG;AACrC,SAAO;AACT;AACA,IAAM,wBAAwB;AAI9B,SAAS,kBAAkB,YAAY,SAAS;AAC9C,MAAI;AACJ,MAAI;AACF,cAAU,WAAW;AAAA,EACvB,SAAS,GAAG;AACV;AAAA,EACF;AACA,QAAM,iBAAiB;AAAA,IACrB,SAAS,CAAC,SAAS;AACjB,UAAI;AACJ,YAAM,QAAQ,CAAC,SAAS;AACtB,YAAI,SAAS,MAAM;AACjB,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,MAAM,MAAM,WAAW,OAAO,SAAS,QAAQ,OAAO;AAAA,MACpE;AACA,YAAM,OAAO,KAAK,QAAQ,QAAQ,IAAI,MAAM,OAAO,KAAK;AACxD,UAAI,eAAe,SAAS;AAC1B,eAAO,IAAI,KAAK,KAAK;AAAA,MACvB;AACA,aAAO,MAAM,GAAG;AAAA,IAClB;AAAA,IACA,SAAS,CAAC,MAAM,aAAa,QAAQ;AAAA,MACnC;AAAA,MACA,KAAK,UAAU,UAAU,WAAW,OAAO,SAAS,QAAQ,QAAQ;AAAA,IACtE;AAAA,IACA,YAAY,CAAC,SAAS,QAAQ,WAAW,IAAI;AAAA,EAC/C;AACA,SAAO;AACT;AACA,IAAM,aAAa,CAAC,OAAO,CAAC,UAAU;AACpC,MAAI;AACF,UAAM,SAAS,GAAG,KAAK;AACvB,QAAI,kBAAkB,SAAS;AAC7B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MACL,KAAK,aAAa;AAChB,eAAO,WAAW,WAAW,EAAE,MAAM;AAAA,MACvC;AAAA,MACA,MAAM,aAAa;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AACV,WAAO;AAAA,MACL,KAAK,cAAc;AACjB,eAAO;AAAA,MACT;AAAA,MACA,MAAM,YAAY;AAChB,eAAO,WAAW,UAAU,EAAE,CAAC;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,cAAc,CAAC,QAAQ,gBAAgB,CAAC,KAAK,KAAK,QAAQ;AAC9D,MAAI,UAAU;AAAA,IACZ,SAAS,kBAAkB,MAAM,YAAY;AAAA,IAC7C,YAAY,CAAC,UAAU;AAAA,IACvB,SAAS;AAAA,IACT,OAAO,CAAC,gBAAgB,kBAAkB;AAAA,MACxC,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,GAAG;AAAA,EACL;AACA,MAAI,cAAc;AAClB,QAAM,qBAAqC,oBAAI,IAAI;AACnD,QAAM,2BAA2C,oBAAI,IAAI;AACzD,MAAI,UAAU,QAAQ;AACtB,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,MACL,IAAI,SAAS;AACX,gBAAQ;AAAA,UACN,uDAAuD,QAAQ,IAAI;AAAA,QACrE;AACA,YAAI,GAAG,IAAI;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,UAAM,QAAQ,QAAQ,WAAW,EAAE,GAAG,IAAI,EAAE,CAAC;AAC7C,WAAO,QAAQ,QAAQ,QAAQ,MAAM;AAAA,MACnC;AAAA,MACA,SAAS,QAAQ;AAAA,IACnB,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,IAAI;AAC1B,MAAI,WAAW,CAAC,OAAO,YAAY;AACjC,kBAAc,OAAO,OAAO;AAC5B,SAAK,QAAQ;AAAA,EACf;AACA,QAAM,eAAe;AAAA,IACnB,IAAI,SAAS;AACX,UAAI,GAAG,IAAI;AACX,WAAK,QAAQ;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,kBAAkB,MAAM;AAC5B,MAAI;AACJ,QAAMC,WAAU,MAAM;AACpB,QAAI,IAAI;AACR,QAAI,CAAC,QAAS;AACd,kBAAc;AACd,uBAAmB,QAAQ,CAAC,OAAO;AACjC,UAAI;AACJ,aAAO,IAAI,MAAM,IAAI,MAAM,OAAO,MAAM,YAAY;AAAA,IACtD,CAAC;AACD,UAAM,4BAA4B,KAAK,QAAQ,uBAAuB,OAAO,SAAS,GAAG,KAAK,UAAU,KAAK,IAAI,MAAM,OAAO,KAAK,YAAY,MAAM;AACrJ,WAAO,WAAW,QAAQ,QAAQ,KAAK,OAAO,CAAC,EAAE,QAAQ,IAAI,EAAE,KAAK,CAAC,6BAA6B;AAChG,UAAI,0BAA0B;AAC5B,YAAI,OAAO,yBAAyB,YAAY,YAAY,yBAAyB,YAAY,QAAQ,SAAS;AAChH,cAAI,QAAQ,SAAS;AACnB,mBAAO;AAAA,cACL;AAAA,cACA,QAAQ;AAAA,gBACN,yBAAyB;AAAA,gBACzB,yBAAyB;AAAA,cAC3B;AAAA,YACF;AAAA,UACF;AACA,kBAAQ;AAAA,YACN;AAAA,UACF;AAAA,QACF,OAAO;AACL,iBAAO,CAAC,OAAO,yBAAyB,KAAK;AAAA,QAC/C;AAAA,MACF;AACA,aAAO,CAAC,OAAO,MAAM;AAAA,IACvB,CAAC,EAAE,KAAK,CAAC,oBAAoB;AAC3B,UAAI;AACJ,YAAM,CAAC,UAAU,aAAa,IAAI;AAClC,yBAAmB,QAAQ;AAAA,QACzB;AAAA,SACC,MAAM,IAAI,MAAM,OAAO,MAAM;AAAA,MAChC;AACA,UAAI,kBAAkB,IAAI;AAC1B,UAAI,UAAU;AACZ,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC,EAAE,KAAK,MAAM;AACZ,iCAA2B,OAAO,SAAS,wBAAwB,kBAAkB,MAAM;AAC3F,yBAAmB,IAAI;AACvB,oBAAc;AACd,+BAAyB,QAAQ,CAAC,OAAO,GAAG,gBAAgB,CAAC;AAAA,IAC/D,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,iCAA2B,OAAO,SAAS,wBAAwB,QAAQ,CAAC;AAAA,IAC9E,CAAC;AAAA,EACH;AACA,MAAI,UAAU;AAAA,IACZ,YAAY,CAAC,eAAe;AAC1B,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,UAAI,WAAW,SAAS;AACtB,kBAAU,WAAW;AAAA,MACvB;AAAA,IACF;AAAA,IACA,cAAc,MAAM;AAClB,iBAAW,OAAO,SAAS,QAAQ,WAAW,QAAQ,IAAI;AAAA,IAC5D;AAAA,IACA,YAAY,MAAM;AAAA,IAClB,WAAW,MAAMA,SAAQ;AAAA,IACzB,aAAa,MAAM;AAAA,IACnB,WAAW,CAAC,OAAO;AACjB,yBAAmB,IAAI,EAAE;AACzB,aAAO,MAAM;AACX,2BAAmB,OAAO,EAAE;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,OAAO;AACzB,+BAAyB,IAAI,EAAE;AAC/B,aAAO,MAAM;AACX,iCAAyB,OAAO,EAAE;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,QAAQ,eAAe;AAC1B,IAAAA,SAAQ;AAAA,EACV;AACA,SAAO,oBAAoB;AAC7B;AACA,IAAM,UAAU;;;AC7ahB,IAAM,kBAAkB,CAAC,gBAAgB;AACvC,MAAI;AACJ,QAAM,YAA4B,oBAAI,IAAI;AAC1C,QAAM,WAAW,CAAC,SAAS,YAAY;AACrC,UAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACnE,QAAI,CAAC,OAAO,GAAG,WAAW,KAAK,GAAG;AAChC,YAAM,gBAAgB;AACtB,eAAS,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,QAAQ,YAAY,OAAO,OAAO,CAAC,GAAG,OAAO,SAAS;AAC1I,gBAAU,QAAQ,CAAC,aAAa,SAAS,OAAO,aAAa,CAAC;AAAA,IAChE;AAAA,EACF;AACA,QAAM,WAAW,MAAM;AACvB,QAAM,kBAAkB,MAAM;AAC9B,QAAM,YAAY,CAAC,aAAa;AAC9B,cAAU,IAAI,QAAQ;AACtB,WAAO,MAAM,UAAU,OAAO,QAAQ;AAAA,EACxC;AACA,QAAM,MAAM,EAAE,UAAU,UAAU,iBAAiB,UAAU;AAC7D,QAAM,eAAe,QAAQ,YAAY,UAAU,UAAU,GAAG;AAChE,SAAO;AACT;AACA,IAAMC,eAAc,CAAC,gBAAgB,cAAc,gBAAgB,WAAW,IAAI;;;ACrBlF,mBAAyB;;;ACcnB,IAAO,UAAP,MAAc;EAGlB,YAAmBC,MAAW;AAAlB,WAAA,eAAA,MAAA,OAAA;;;;aAAOA;;AAFnB,WAAA,eAAA,MAAA,YAAA;;;;aAAW,IAAI,aAAAC,QAAY;;EAEM;EAEjC,GACE,WACA,IAIC;AAED,SAAK,SAAS,GAAG,WAAW,EAAa;EAC3C;EAEA,KACE,WACA,IAIC;AAED,SAAK,SAAS,KAAK,WAAW,EAAa;EAC7C;EAEA,IACE,WACA,IAIC;AAED,SAAK,SAAS,IAAI,WAAW,EAAa;EAC5C;EAEA,KACE,cACG,QAAkE;AAErE,UAAM,OAAO,OAAO,CAAC;AACrB,SAAK,SAAS,KAAK,WAAW,EAAE,KAAK,KAAK,KAAK,GAAG,KAAI,CAAE;EAC1D;EAEA,cAA8C,WAAc;AAC1D,WAAO,KAAK,SAAS,cAAc,SAAS;EAC9C;;AAGI,SAAU,cAAyCD,MAAW;AAClE,SAAO,IAAI,QAAkBA,IAAG;AAClC;;;ACnEA,IAAM,OAAO;AACb,IAAI,QAAQ;AACZ,IAAI;AAEE,SAAU,IAAI,SAAS,IAAE;AAC7B,MAAI,CAAC,UAAU,QAAQ,SAAS,OAAO,GAAG;AACxC,aAAS;AACT,YAAQ;AACR,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,iBAAY,MAAM,KAAK,OAAM,IAAK,MAAO,GAAG,SAAS,EAAE,EAAE,UAAU,CAAC;IACtE;EACF;AACA,SAAO,OAAO,UAAU,OAAO,UAAU,MAAM;AACjD;;;AC0BM,SAAU,aAKd,YAAoE;AAEpE,QAAM,EACJ,iCAAiC,MACjC,UAAU,cAAc;IACtB,SAAS,kBAAiB;GAC3B,GACD,qBAAqB,MACrB,MAAM,OACN,GAAG,KAAI,IACL;AAMJ,QAAM,OACJ,OAAO,WAAW,eAAe,iCAC7B,YAAU,IACV;AAEN,QAAM,SAASE,aAAY,MAAM,KAAK,MAAM;AAC5C,QAAM,aAAaA,aAAY,MAAK;AAClC,UAAM,aAAa,CAAA;AACnB,UAAM,UAAU,oBAAI,IAAG;AACvB,eAAW,gBAAgB,KAAK,cAAc,CAAA,GAAI;AAChD,YAAM,YAAY,MAAM,YAAY;AACpC,iBAAW,KAAK,SAAS;AACzB,UAAI,CAAC,OAAO,UAAU,MAAM;AAC1B,cAAM,aACJ,OAAO,UAAU,SAAS,WAAW,CAAC,UAAU,IAAI,IAAI,UAAU;AACpE,mBAAW,QAAQ,YAAY;AAC7B,kBAAQ,IAAI,IAAI;QAClB;MACF;IACF;AACA,QAAI,CAAC,OAAO,MAAM;AAChB,YAAM,YAAY,KAAK,aAAY;AACnC,iBAAW,YAAY,WAAW;AAChC,YAAI,QAAQ,IAAI,SAAS,KAAK,IAAI;AAAG;AACrC,mBAAW,KAAK,MAAM,0BAA0B,QAAQ,CAAC,CAAC;MAC5D;IACF;AACA,WAAO;EACT,CAAC;AACD,WAAS,MAAM,aAA8B;AAzF/C;AA2FI,UAAM,UAAU,cAAiC,IAAG,CAAE;AACtD,UAAM,YAAY;MAChB,GAAG,YAAY;QACb;QACA,QAAQ,OAAO,SAAQ;QACvB;QACA,YAAY,KAAK;OAClB;MACD;MACA,KAAK,QAAQ;;AAKf,YAAQ,GAAG,WAAWC,QAAO;AAC7B,oBAAU,UAAV;AAEA,WAAO;EACT;AACA,WAAS,0BAA0B,gBAAqC;AACtE,UAAM,EAAE,KAAI,IAAK;AACjB,UAAM,WAAW,eAAe;AAChC,WAAO,SAAS,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,KAAK,MAAM,SAAQ,EAAE,CAAE;EAClE;AAEA,QAAM,UAAU,oBAAI,IAAG;AACvB,WAASC,WACP,SAAmE,CAAA,GAAE;AAErE,UAAM,UAAU,OAAO,WAAW,MAAM,SAAQ,EAAG;AACnD,UAAM,QAAQ,OAAO,SAAQ,EAAG,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AAG5D,QAAI,OAAO,WAAW,CAAC;AAAO,YAAM,IAAI,wBAAuB;AAI/D;AACE,YAAMC,UAAS,QAAQ,IAAI,MAAM,SAAQ,EAAG,OAAO;AACnD,UAAIA,WAAU,CAAC;AAAO,eAAOA;AAC7B,UAAI,CAAC;AAAO,cAAM,IAAI,wBAAuB;IAC/C;AAGA;AACE,YAAMA,UAAS,QAAQ,IAAI,OAAO;AAClC,UAAIA;AAAQ,eAAOA;IACrB;AAEA,QAAI;AACJ,QAAI,KAAK;AAAQ,eAAS,KAAK,OAAO,EAAE,MAAK,CAAE;SAC1C;AACH,YAAMC,WAAU,MAAM;AACtB,YAAM,WAAW,OAAO,SAAQ,EAAG,IAAI,CAAC,MAAM,EAAE,EAAE;AAElD,YAAM,aAAyC,CAAA;AAC/C,YAAM,UAAU,OAAO,QAAQ,IAAI;AAEnC,iBAAW,CAAC,KAAK,KAAK,KAAK,SAAS;AAClC,YACE,QAAQ,YACR,QAAQ,YACR,QAAQ,gBACR,QAAQ;AAER;AAEF,YAAI,OAAO,UAAU,UAAU;AAG7B,cAAIA,YAAW;AAAO,uBAAW,GAAG,IAAI,MAAMA,QAAO;eAChD;AAEH,kBAAM,wBAAwB,SAAS,KAAK,CAAC,MAAM,KAAK,KAAK;AAC7D,gBAAI;AAAuB;AAC3B,uBAAW,GAAG,IAAI;UACpB;QACF;AAAO,qBAAW,GAAG,IAAI;MAC3B;AAEA,eAAS,aAAa;QACpB,GAAG;QACH;QACA,OAAO,WAAW,SAAS,EAAE,WAAW,KAAI;QAC5C,WAAW,CAACC,gBACV,KAAK,WAAWD,QAAO,EAAE,EAAE,GAAGC,aAAY,WAAU,CAAE;OACzD;IACH;AAEA,YAAQ,IAAI,SAAS,MAAM;AAC3B,WAAO;EACT;AAMA,WAAS,kBAAe;AACtB,WAAO;MACL,SAAS,OAAO,SAAQ,EAAG,CAAC,EAAE;MAC9B,aAAa,oBAAI,IAAG;MACpB,SAAS;MACT,QAAQ;;EAEZ;AAEA,MAAI;AACJ,QAAM,SAAS;AACf,MAAI,QAAQ,WAAW,MAAM;AAC3B,qBAAiB,OAAO,SAAS,QAAQ,QAAQ,QAAQ,EAAE,CAAC;;AAEzD,qBAAiB,OAAO,SAAS,QAAQ,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG;AAElE,QAAM,QAAQL,aACZ;;IAEE,UACI,QAAQ,iBAAiB;MACvB,QAAQ,gBAAgBM,UAAO;AAC7B,YAAIA,aAAY;AAAgB,iBAAO;AAEvC,cAAM,eAAe,gBAAe;AACpC,cAAM,UAAU,yBACd,gBACA,aAAa,OAAO;AAEtB,eAAO,EAAE,GAAG,cAAc,QAAO;MACnC;MACA,MAAM;MACN,WAAW,OAAK;AAEd,eAAO;UACL,aAAa;YACX,QAAQ;YACR,OAAO,MAAM,KAAK,MAAM,YAAY,QAAO,CAAE,EAAE,IAC7C,CAAC,CAAC,KAAK,UAAU,MAAK;AACpB,oBAAM,EAAE,IAAI,MAAM,MAAM,KAAAC,KAAG,IAAK,WAAW;AAC3C,oBAAM,YAAY,EAAE,IAAI,MAAM,MAAM,KAAAA,KAAG;AACvC,qBAAO,CAAC,KAAK,EAAE,GAAG,YAAY,UAAS,CAAE;YAC3C,CAAC;;UAGL,SAAS,MAAM;UACf,SAAS,MAAM;;MAEnB;MACA,MAAM,gBAAgB,cAAY;AAEhC,YACE,OAAO,mBAAmB,YAC1B,kBACA,YAAY;AAEZ,iBAAO,eAAe;AAExB,cAAM,UAAU,yBACd,gBACA,aAAa,OAAO;AAEtB,eAAO;UACL,GAAG;UACH,GAAI;UACJ;;MAEJ;MACA,eAAe;MACf;MACA,SAAS;KACV,IACD;EAAe,CACpB;AAEH,QAAM,SAAS,gBAAe,CAAE;AAEhC,WAAS,yBACP,gBACA,gBAAsB;AAEtB,WAAO,kBACL,OAAO,mBAAmB,YAC1B,aAAa,kBACb,OAAO,eAAe,YAAY,YAClC,OAAO,SAAQ,EAAG,KAAK,CAAC,MAAM,EAAE,OAAO,eAAe,OAAO,IAC3D,eAAe,UACf;EACN;AAOA,MAAI;AACF,UAAM,UACJ,CAAC,EAAE,aAAa,QAAO,MAAI;AA7RjC;AA8RQ,wBAAU,iBAAY,IAAI,OAAO,MAAvB,mBAA0B,UAAU;OAChD,CAAC,YAAW;AAEV,YAAM,oBAAoB,OACvB,SAAQ,EACR,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AAC/B,UAAI,CAAC;AAAmB;AAExB,aAAO,MAAM,SAAS,CAAC,OAAO;QAC5B,GAAG;QACH,SAAS,WAAW,EAAE;QACtB;IACJ,CAAC;AAIL,+BAAM,UAAU,CAAC,oBAAmB;AAClC,UAAM,iBAAiB,oBAAI,IAAG;AAC9B,UAAM,mBAAmB,oBAAI,IAAG;AAChC,eAAW,aAAa,WAAW,SAAQ,GAAI;AAC7C,qBAAe,IAAI,UAAU,EAAE;AAC/B,UAAI,UAAU,MAAM;AAClB,cAAM,aACJ,OAAO,UAAU,SAAS,WAAW,CAAC,UAAU,IAAI,IAAI,UAAU;AACpE,mBAAW,QAAQ,YAAY;AAC7B,2BAAiB,IAAI,IAAI;QAC3B;MACF;IACF;AAEA,UAAM,gBAA6B,CAAA;AACnC,eAAW,kBAAkB,iBAAiB;AAC5C,UAAI,iBAAiB,IAAI,eAAe,KAAK,IAAI;AAAG;AACpD,YAAM,YAAY,MAAM,0BAA0B,cAAc,CAAC;AACjE,UAAI,eAAe,IAAI,UAAU,EAAE;AAAG;AACtC,oBAAc,KAAK,SAAS;IAC9B;AAEA,QAAI,WAAW,CAAC,MAAM,QAAQ,YAAW;AAAI;AAC7C,eAAW,SAAS,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,aAAa,GAAG,IAAI;EAC3D;AAMA,WAAS,OAAO,MAA4C;AAC1D,UAAM,SAAS,CAAC,MAAK;AACnB,YAAM,aAAa,EAAE,YAAY,IAAI,KAAK,GAAG;AAC7C,UAAI,CAAC;AAAY,eAAO;AACxB,aAAO;QACL,GAAG;QACH,aAAa,IAAI,IAAI,EAAE,WAAW,EAAE,IAAI,KAAK,KAAK;UAChD,UACG,KAAK,YACN,WAAW;UACb,SAAS,KAAK,WAAW,WAAW;UACpC,WAAW,WAAW;SACvB;;IAEL,CAAC;EACH;AACA,WAASN,SAAQ,MAA6C;AAE5D,QACE,MAAM,SAAQ,EAAG,WAAW,gBAC5B,MAAM,SAAQ,EAAG,WAAW;AAE5B;AAEF,UAAM,SAAS,CAAC,MAAK;AACnB,YAAM,YAAY,WAAW,SAAQ,EAAG,KAAK,CAACO,OAAMA,GAAE,QAAQ,KAAK,GAAG;AACtE,UAAI,CAAC;AAAW,eAAO;AAEvB,UAAI,UAAU,QAAQ,cAAc,SAAS;AAC3C,kBAAU,QAAQ,IAAI,WAAW,MAAM;AACzC,UAAI,CAAC,UAAU,QAAQ,cAAc,QAAQ;AAC3C,kBAAU,QAAQ,GAAG,UAAU,MAAM;AACvC,UAAI,CAAC,UAAU,QAAQ,cAAc,YAAY;AAC/C,kBAAU,QAAQ,GAAG,cAAcC,WAAU;AAE/C,aAAO;QACL,GAAG;QACH,aAAa,IAAI,IAAI,EAAE,WAAW,EAAE,IAAI,KAAK,KAAK;UAChD,UAAU,KAAK;UACf,SAAS,KAAK;UACd;SACD;QACD,SAAS,KAAK;QACd,QAAQ;;IAEZ,CAAC;EACH;AACA,WAASA,YAAW,MAAgD;AAClE,UAAM,SAAS,CAAC,MAAK;AACnB,YAAM,aAAa,EAAE,YAAY,IAAI,KAAK,GAAG;AAC7C,UAAI,YAAY;AACd,cAAM,YAAY,WAAW;AAC7B,YAAI,UAAU,QAAQ,cAAc,QAAQ;AAC1C,qBAAW,UAAU,QAAQ,IAAI,UAAU,MAAM;AACnD,YAAI,UAAU,QAAQ,cAAc,YAAY;AAC9C,qBAAW,UAAU,QAAQ,IAAI,cAAcA,WAAU;AAC3D,YAAI,CAAC,UAAU,QAAQ,cAAc,SAAS;AAC5C,qBAAW,UAAU,QAAQ,GAAG,WAAWR,QAAO;MACtD;AAEA,QAAE,YAAY,OAAO,KAAK,GAAG;AAE7B,UAAI,EAAE,YAAY,SAAS;AACzB,eAAO;UACL,GAAG;UACH,aAAa,oBAAI,IAAG;UACpB,SAAS;UACT,QAAQ;;AAGZ,YAAM,iBAAiB,EAAE,YAAY,OAAM,EAAG,KAAI,EAAG;AACrD,aAAO;QACL,GAAG;QACH,aAAa,IAAI,IAAI,EAAE,WAAW;QAClC,SAAS,eAAe,UAAU;;IAEtC,CAAC;EACH;AAEA,SAAO;IACL,IAAI,SAAM;AACR,aAAO,OAAO,SAAQ;IACxB;IACA,IAAI,aAAU;AACZ,aAAO,WAAW,SAAQ;IAC5B;IACA;IAEA,WAAAC;IACA,IAAI,QAAK;AACP,aAAO,MAAM,SAAQ;IACvB;IACA,SAAS,OAAK;AACZ,UAAI;AACJ,UAAI,OAAO,UAAU;AAAY,mBAAW,MAAM,MAAM,SAAQ,CAAS;;AACpE,mBAAW;AAGhB,YAAM,eAAe,gBAAe;AACpC,UAAI,OAAO,aAAa;AAAU,mBAAW;AAC7C,YAAM,YAAY,OAAO,KAAK,YAAY,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,SAAS;AACxE,UAAI;AAAW,mBAAW;AAE1B,YAAM,SAAS,UAAU,IAAI;IAC/B;IACA,UAAU,UAAU,UAAU,SAAO;AACnC,aAAO,MAAM,UACX,UACA,UACA,UACK;QACC,GAAG;QACH,iBAAiB,QAAQ;;UAG3B,MAAS;IAEjB;IAEA,WAAW;MACT;MACA;MACA,KAAK,QAAQ,GAAG;MAChB;MACA,YAAY,KAAK;MACjB,QAAQ;QACN,SAAS,OAAK;AACZ,gBAAM,aACJ,OAAO,UAAU,aAAa,MAAM,OAAO,SAAQ,CAAE,IAAI;AAE3D,cAAI,WAAW,WAAW;AAAG;AAC7B,iBAAO,OAAO,SAAS,YAAY,IAAI;QACzC;QACA,UAAU,UAAQ;AAChB,iBAAO,OAAO,UAAU,QAAQ;QAClC;;MAEF,YAAY;QACV;QACA;QAGA,SAAS,OAAK;AACZ,iBAAO,WAAW,SAChB,OAAO,UAAU,aAAa,MAAM,WAAW,SAAQ,CAAE,IAAI,OAC7D,IAAI;QAER;QACA,UAAU,UAAQ;AAChB,iBAAO,WAAW,UAAU,QAAQ;QACtC;;MAEF,QAAQ,EAAE,QAAQ,SAAAD,UAAS,YAAAQ,YAAU;;;AAG3C;;;AC1cM,SAAU,mBACd,WACA,SAAmC,CAAA,GAAE;AAErC,QAAM,EAAE,KAAI,IAAK;AACjB,QAAM,EAAE,MAAM,aAAa,OAAO,aAAa,WAAU,IAAK;AAE9D,SAAO,CAAC,eAAc;AACpB,UAAM,EAAE,OAAO,WAAU,IAAK;AAC9B,UAAM,aAAa,OAAO,cAAc,WAAW;AAEnD,UAAM,UAA4B,OAAO,EAAE,QAAQ,OAAM,MAAM;AAC7D,YAAMC,aAAY,yCAAY,WAAW,KAAK,CAAC,MAAM,EAAE,SAAS;AAChE,UAAI,CAACA;AACH,cAAM,IAAI,0BACR,IAAI,MACF,qCAAqC,IAAI,iDAAiD,CAC3F;AAGL,YAAM,WAAY,MAAMA,WAAU,YAAY;QAC5C,SAAS,+BAAO;OACjB;AACD,UAAI,CAAC;AACH,cAAM,IAAI,0BACR,IAAI,MAAM,2BAA2B,CAAC;AAK1C,YAAM,UAAU,YACd,MAAM,UAAU,MACd,YAAY,MAAM,SAAS,QAAQ,EAAE,QAAQ,cAAa,CAAE,GAAG;QAC7D,SAAS;OACV,CAAC,CACH;AAEH,UAAI,SAAS,YAAY,MAAM;AAC7B,cAAM,IAAI,uBACR,IAAI,MACF,2CAA2C,OAAO,0DAA0D,MAAM,EAAE,MAAM,MAAM,IAAI,IAAI,CACzI;AAGL,YAAM,OAAO,EAAE,QAAQ,OAAM;AAC7B,aAAO,SAAS,QAAQ,IAAI;IAC9B;AAEA,WAAO,gBAAgB;MACrB;MACA;MACA;MACA;MACA;MACA,MAAM;KACP;EACH;AACF;;;AClFM,SAAUC,UACd,YACA,QAA4C;AAE5C,SAAO,SAAc,YAAY,MAAM;AACzC;;;ACLO,IAAM,gBAAgB;EAC3B,QAAQ,KAAG;AACT,QAAI,OAAO,WAAW;AAAa,aAAO;AAC1C,UAAM,QAAQ,YAAY,SAAS,QAAQ,GAAG;AAC9C,WAAO,SAAS;EAClB;EACA,QAAQ,KAAK,OAAK;AAChB,QAAI,OAAO,WAAW;AAAa;AACnC,aAAS,SAAS,GAAG,GAAG,IAAI,KAAK;EACnC;EACA,WAAW,KAAG;AACZ,QAAI,OAAO,WAAW;AAAa;AACnC,aAAS,SAAS,GAAG,GAAG;EAC1B;;AAGI,SAAU,qBAAqB,QAAgB,QAAsB;AAlB3E;AAmBE,MAAI,CAAC;AAAQ,WAAO;AACpB,QAAM,MAAM,IAAG,YAAO,YAAP,mBAAgB,GAAG;AAClC,QAAM,SAAS,YAAY,QAAQ,GAAG;AACtC,MAAI,CAAC;AAAQ,WAAO;AACpB,SAAO,YAA8B,MAAM,EAAE;AAC/C;AAEM,SAAU,YAAY,QAAgB,KAAW;AACrD,QAAM,WAAW,OAAO,MAAM,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,WAAW,GAAG,GAAG,GAAG,CAAC;AACvE,MAAI,CAAC;AAAU,WAAO;AACtB,SAAO,SAAS,UAAU,IAAI,SAAS,CAAC;AAC1C;;;AC/BM,SAAU,iBAAiB,SAA2C;AAC1E,MAAI,OAAO,YAAY;AACrB,WAAO,OAAO,SACZ,SACA,QAAQ,KAAI,EAAG,UAAU,GAAG,CAAC,MAAM,OAAO,KAAK,EAAE;AAErD,MAAI,OAAO,YAAY;AAAU,WAAO,OAAO,OAAO;AACtD,MAAI,OAAO,YAAY;AAAU,WAAO;AACxC,QAAM,IAAI,MACR,6BAA6B,OAAO,cAAc,OAAO,OAAO,GAAG;AAEvE;;;ACKM,SAAU,UAUd,QACA,UAIA,MAA+C;AAE/C,QAAM,kBAAkB,OAAO,SAAS,IAAI;AAC5C,MAAI,OAAO,oBAAoB;AAC7B,WAAO;AAET,QAAM,kBAAkB,OAAO,IAAI;AACnC,MAAI,OAAO,oBAAoB;AAC7B,WAAO;AAET,SAAO,CAAC,WAAW,SAAS,QAAQ,MAAM;AAC5C;;;ACzBA,eAAsBC,MACpB,QACA,YAAkC;AAElC,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,MAAW,MAAM;AAClD,SAAO,OAAO,IAAI;AACpB;;;ACiCA,eAAsB,QAIpB,QACA,YAAgD;AAvDlD;AA0DE,MAAI;AACJ,MAAI,OAAO,WAAW,cAAc,YAAY;AAC9C,gBAAY,OAAO,UAAU,WAAW,MAAM,WAAW,SAAS;EACpE;AAAO,gBAAY,WAAW;AAG9B,MAAI,UAAU,QAAQ,OAAO,MAAM;AACjC,UAAM,IAAI,+BAA8B;AAE1C,MAAI;AACF,WAAO,SAAS,CAAC,OAAO,EAAE,GAAG,GAAG,QAAQ,aAAY,EAAG;AACvD,cAAU,QAAQ,KAAK,WAAW,EAAE,MAAM,aAAY,CAAE;AAExD,UAAM,EAAE,WAAW,GAAG,GAAG,KAAI,IAAK;AAClC,UAAM,OAAO,MAAM,UAAU,QAAQ,IAAI;AACzC,UAAM,WAAW,KAAK;AAEtB,cAAU,QAAQ,IAAI,WAAW,OAAO,UAAU,OAAO,OAAO;AAChE,cAAU,QAAQ,GAAG,UAAU,OAAO,UAAU,OAAO,MAAM;AAC7D,cAAU,QAAQ,GAAG,cAAc,OAAO,UAAU,OAAO,UAAU;AAErE,YAAM,YAAO,YAAP,mBAAgB,QAAQ,qBAAqB,UAAU;AAC7D,WAAO,SAAS,CAAC,OAAO;MACtB,GAAG;MACH,aAAa,IAAI,IAAI,EAAE,WAAW,EAAE,IAAI,UAAU,KAAK;QACrD;QACA,SAAS,KAAK;QACd;OACD;MACD,SAAS,UAAU;MACnB,QAAQ;MACR;AAEF,WAAO,EAAE,UAAU,SAAS,KAAK,QAAO;EAC1C,SAAS,OAAO;AACd,WAAO,SAAS,CAAC,OAAO;MACtB,GAAG;;MAEH,QAAQ,EAAE,UAAU,cAAc;MAClC;AACF,UAAM;EACR;AACF;;;ACvCA,eAAsB,mBAIpB,QACA,aAA4D,CAAA,GAAE;AAG9D,MAAI;AACJ,MAAI,WAAW,WAAW;AACxB,UAAM,EAAE,WAAAC,WAAS,IAAK;AACtB,QACE,OAAO,MAAM,WAAW,kBACxB,CAACA,WAAU,eACX,CAACA,WAAU;AAEX,YAAM,IAAI,sCAAsC,EAAE,WAAAA,WAAS,CAAE;AAE/D,UAAM,CAAC,UAAUC,QAAO,IAAI,MAAM,QAAQ,IAAI;MAC5CD,WAAU,YAAW,EAAG,MAAM,CAAC,MAAK;AAClC,YAAI,WAAW,YAAY;AAAM,iBAAO,CAAA;AACxC,cAAM;MACR,CAAC;MACDA,WAAU,WAAU;KACrB;AACD,iBAAa;MACX;MACA,SAAAC;MACA,WAAAD;;EAEJ;AAAO,iBAAa,OAAO,MAAM,YAAY,IAAI,OAAO,MAAM,OAAQ;AACtE,MAAI,CAAC;AAAY,UAAM,IAAI,2BAA0B;AAErD,QAAM,UAAU,WAAW,WAAW,WAAW;AAGjD,QAAM,mBAAmB,MAAM,WAAW,UAAU,WAAU;AAC9D,MAAI,qBAAqB,WAAW;AAClC,UAAM,IAAI,4BAA4B;MACpC,mBAAmB,WAAW;MAC9B;KACD;AAIH,QAAM,YAAY,WAAW;AAC7B,MAAI,UAAU;AACZ,WAAO,UAAU,UAAU,EAAE,QAAO,CAAE;AAGxC,QAAM,UAAU,aAAa,WAAW,WAAW,WAAW,SAAS,CAAC,CAAE;AAC1E,MAAI;AAAS,YAAQ,UAAU,WAAW,QAAQ,OAAO;AAGzD,MACE,WAAW,WACX,CAAC,WAAW,SAAS,KACnB,CAAC,MAAM,EAAE,YAAW,MAAO,QAAQ,QAAQ,YAAW,CAAE;AAG1D,UAAM,IAAI,8BAA8B;MACtC,SAAS,QAAQ;MACjB;KACD;AAEH,QAAM,QAAQ,OAAO,OAAO,KAAK,CAACE,WAAUA,OAAM,OAAO,OAAO;AAChE,QAAM,WAAY,MAAM,WAAW,UAAU,YAAY,EAAE,QAAO,CAAE;AAIpE,SAAO,aAAa;IAClB;IACA;IACA,MAAM;IACN,WAAW,CAAC,SAAS,OAAO,QAAQ,EAAE,EAAE,GAAG,MAAM,YAAY,EAAC,CAAE;GACjE;AACH;;;ACxFA,eAAsBC,gBAKpB,QACA,YAA0D;AAE1D,QAAM,EAAE,SAAS,SAAS,WAAW,GAAG,KAAI,IAAK;AAEjD,MAAI;AACJ,MAAI,OAAO,YAAY,aAAY,mCAAS,UAAS;AACnD,aAAS,OAAO,UAAU,EAAE,QAAO,CAAE;;AAErC,aAAS,MAAM,mBAAmB,QAAQ;MACxC,SAAS,WAAW;MACpB;MACA;KACD;AAEH,QAAM,SAAS,UAAU,QAAQ,gBAAqB,gBAAgB;AACtE,QAAM,OAAO,MAAM,OAAO;IACxB,GAAI;IACJ,GAAI,UAAU,EAAE,QAAO,IAAK,CAAA;IAC5B,OAAO,UAAU,EAAE,IAAI,QAAO,IAAK;GACpC;AAED,SAAO;AACT;;;AClEA,eAAsB,WACpB,QACA,aAAmC,CAAA,GAAE;AAHvC;AAKE,MAAI;AACJ,MAAI,WAAW;AAAW,gBAAY,WAAW;OAC5C;AACH,UAAM,EAAE,aAAAC,cAAa,QAAO,IAAK,OAAO;AACxC,UAAM,aAAaA,aAAY,IAAI,OAAQ;AAC3C,gBAAY,yCAAY;EAC1B;AAEA,QAAM,cAAc,OAAO,MAAM;AAEjC,MAAI,WAAW;AACb,UAAM,UAAU,WAAU;AAC1B,cAAU,QAAQ,IAAI,UAAU,OAAO,UAAU,OAAO,MAAM;AAC9D,cAAU,QAAQ,IAAI,cAAc,OAAO,UAAU,OAAO,UAAU;AACtE,cAAU,QAAQ,GAAG,WAAW,OAAO,UAAU,OAAO,OAAO;AAE/D,gBAAY,OAAO,UAAU,GAAG;EAClC;AAEA,SAAO,SAAS,CAAC,MAAK;AAEpB,QAAI,YAAY,SAAS;AACvB,aAAO;QACL,GAAG;QACH,aAAa,oBAAI,IAAG;QACpB,SAAS;QACT,QAAQ;;AAIZ,UAAM,iBAAiB,YAAY,OAAM,EAAG,KAAI,EAAG;AACnD,WAAO;MACL,GAAG;MACH,aAAa,IAAI,IAAI,WAAW;MAChC,SAAS,eAAe,UAAU;;EAEtC,CAAC;AAGD;AACE,UAAM,UAAU,OAAO,MAAM;AAC7B,QAAI,CAAC;AAAS;AACd,UAAMC,cAAY,YAAO,MAAM,YAAY,IAAI,OAAO,MAApC,mBAAuC;AACzD,QAAI,CAACA;AAAW;AAChB,YAAM,YAAO,YAAP,mBAAgB,QAAQ,qBAAqBA,WAAU;EAC/D;AACF;;;ACrBA,eAAsBC,aAIpB,QACA,YAAkD;AAElD,QAAM,EAAE,SAAS,WAAW,GAAG,KAAI,IAAK;AAExC,MAAI;AACJ,MAAI,WAAW;AAAS,cAAU,WAAW;OACxC;AACH,UAAM,kBAAkB,MAAM,mBAAmB,QAAQ;MACvD,SAAS,WAAW;MACpB;MACA;KACD;AACD,cAAU,gBAAgB;EAC5B;AAEA,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,aAAkB,aAAa;AAChE,SAAO,OAAO,EAAE,GAAI,MAAqC,QAAO,CAAE;AACpE;;;ACpEM,SAAU,QAAQ,MAAU;AAChC,MAAI,OAAO,SAAS;AAAU,WAAO;AACrC,MAAI,SAAS;AAAO,WAAO;AAC3B,SAAO,KAAK,IAAI,SAAS,IAAI,CAAC;AAChC;;;AC0CA,eAAsBC,oBAIpB,QACA,aAAyD,CAAA,GAAE;AAE3D,QAAM,EAAE,SAAS,aAAa,QAAQ,QAAQ,GAAG,KAAI,IAAK;AAE1D,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UACb,QACA,oBACA,oBAAoB;AAGtB,QAAM,EAAE,UAAU,cAAc,qBAAoB,IAAK,MAAM,OAAO;IACpE,GAAG;IACH,OAAO,OAAO;GACf;AAED,QAAM,OAAO,QAAQ,KAAK;AAC1B,QAAM,YAAY;IAChB,UAAU,WAAW,YAAY,UAAU,IAAI,IAAI;IACnD,cAAc,eAAe,YAAY,cAAc,IAAI,IAAI;IAC/D,sBAAsB,uBAClB,YAAY,sBAAsB,IAAI,IACtC;;AAGN,SAAO;IACL;IACA;IACA;IACA;;AAEJ;;;ACtDA,eAAsBC,8BAKpB,QACA,aAAsE,CAAA,GAAE;AAExE,QAAM,EAAE,QAAO,IAAK;AACpB,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UACb,QACA,8BACA,8BAA8B;AAEhC,SAAO,OAAO,EAAE,OAAO,OAAO,MAAK,CAAE;AACvC;;;ACWM,SAAU,WACd,QAAc;AAEd,QAAMC,OAAM,OAAO,MAAM;AACzB,QAAM,aAAa,OAAO,MAAM,YAAY,IAAIA,IAAG;AACnD,QAAM,YAAY,yCAAY;AAC9B,QAAM,UAAU,uCAAY;AAC5B,QAAM,QAAQ,OAAO,OAAO,KAC1B,CAACC,WAAUA,OAAM,QAAO,yCAAY,QAAO;AAE7C,QAAM,SAAS,OAAO,MAAM;AAE5B,UAAQ,QAAQ;IACd,KAAK;AACH,aAAO;QACL;QACA;QACA;QACA,SAAS,yCAAY;QACrB,WAAW,yCAAY;QACvB,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB;;IAEJ,KAAK;AACH,aAAO;QACL;QACA;QACA;QACA,SAAS,yCAAY;QACrB,WAAW,yCAAY;QACvB,aAAa,CAAC,CAAC;QACf,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB;;IAEJ,KAAK;AACH,aAAO;QACL;QACA;QACA;QACA,SAAS,yCAAY;QACrB,WAAW,yCAAY;QACvB,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB;;IAEJ,KAAK;AACH,aAAO;QACL,SAAS;QACT,WAAW;QACX,OAAO;QACP,SAAS;QACT,WAAW;QACX,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB;;EAEN;AACF;;;ACpGA,eAAsBC,WAKpB,QACA,YAAgE;AAEhE,QAAM,EAAE,eAAe,MAAM,SAAS,WAAW,GAAG,KAAI,IAAK;AAC7D,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,WAAgB,WAAW;AAC5D,SAAO,OAAO;IACZ;IACA;IACA,GAAG;GACJ;AACH;;;ACGM,SAAUC,cAMd,QACA,YAAmE;AAEnE,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,cAAmB,cAAc;AAClE,SAAO,OAAO,IAAW;AAC3B;;;AC5BA,eAAsB,cAKpB,QACA,YAAoE;AA9BtE;AAgCE,QAAM,EAAE,eAAe,MAAM,aAAa,UAAU,GAAG,KAAI,IAAK;AAChE,QAAM,YAAY,WAAW;AAI7B,MAAI;AACF,UAAM,qBAKF,CAAA;AACJ,eAAW,CAACC,QAAO,QAAQ,KAAK,UAAU,QAAO,GAAI;AACnD,YAAM,UAAU,SAAS,WAAW,OAAO,MAAM;AACjD,UAAI,CAAC,mBAAmB,OAAO;AAAG,2BAAmB,OAAO,IAAI,CAAA;AAChE,+BAAmB,OAAO,MAA1B,mBAA6B,KAAK,EAAE,UAAU,OAAAA,OAAK;IACrD;AACA,UAAM,WAAW,MACf,OAAO,QAAQ,kBAAkB,EAAE,IAAI,CAAC,CAAC,SAASC,UAAS,MACzDC,WAAU,QAAQ;MAChB,GAAG;MACH;MACA;MACA;MACA,SAAS,OAAO,SAAS,OAAO;MAChC,WAAWD,WAAU,IAAI,CAAC,EAAE,SAAQ,MAAO,QAAQ;KACpD,CAAC;AAGN,UAAM,oBAAoB,MAAM,QAAQ,IAAI,SAAQ,CAAE,GAAG,KAAI;AAG7D,UAAM,gBAAgB,OAAO,OAAO,kBAAkB,EAAE,QACtD,CAACA,eAAcA,WAAU,IAAI,CAAC,EAAE,OAAAD,OAAK,MAAOA,MAAK,CAAC;AAEpD,WAAO,iBAAiB,OAAO,CAAC,SAAS,QAAQA,WAAS;AACxD,UAAI;AAAU,gBAAsB,cAAcA,MAAK,CAAE,IAAI;AAC7D,aAAO;IACT,GAAG,CAAA,CAAe;EACpB,SAAS,OAAO;AACd,QAAI,iBAAiB;AAAgC,YAAM;AAE3D,UAAM,WAAW,MACf,UAAU,IAAI,CAAC,aACbG,cAAa,QAAQ,EAAE,GAAG,UAAU,aAAa,SAAQ,CAAE,CAAC;AAEhE,QAAI;AACF,cAAQ,MAAM,QAAQ,WAAW,SAAQ,CAAE,GAAG,IAAI,CAAC,WAAU;AAC3D,YAAI,OAAO,WAAW;AACpB,iBAAO,EAAE,QAAQ,OAAO,OAAO,QAAQ,UAAS;AAClD,eAAO,EAAE,OAAO,OAAO,QAAQ,QAAQ,QAAW,QAAQ,UAAS;MACrE,CAAC;AAEH,WAAQ,MAAM,QAAQ,IAAI,SAAQ,CAAE;EAItC;AACF;;;AC3DA,eAAsBC,YACpB,QACA,YAAwC;AAExC,QAAM,EACJ,SACA,aACA,UACA,SACA,OAAO,cACP,OAAO,QAAO,IACZ;AAEJ,MAAI,cAAc;AAChB,QAAI;AACF,aAAO,MAAM,gBAAgB,QAAQ;QACnC,gBAAgB;QAChB;QACA,YAAY;QACZ;OACD;IACH,SAAS,OAAO;AAId,UACG,MAAiC,SAClC,kCACA;AACA,cAAM,UAAU,MAAM,gBAAgB,QAAQ;UAC5C,gBAAgB;UAChB;UACA,YAAY;UACZ;SACD;AACD,cAAM,SAAS,YACb,KAAK,QAAQ,QAAe,EAAE,KAAK,QAAO,CAAE,CAAC;AAE/C,eAAO,EAAE,GAAG,SAAS,OAAM;MAC7B;AACA,YAAM;IACR;EACF;AAEA,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,YAAiB,YAAY;AAC9D,QAAM,QAAQ,MAAM,OAClB,cAAc,EAAE,SAAS,YAAW,IAAK,EAAE,SAAS,SAAQ,CAAE;AAEhE,QAAM,QAAQ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,KAAK,OAAO;AACpE,SAAO;IACL,UAAU,MAAM,eAAe;IAC/B,WAAW,YAAY,OAAO,QAAQ,IAAI,CAAC;IAC3C,QAAQ,MAAM,eAAe;IAC7B;;AAEJ;AAUA,eAAe,gBACb,QACA,YAAqC;AAErC,QAAM,EAAE,gBAAgB,SAAS,YAAY,cAAc,KAAI,IAAK;AACpE,QAAM,WAAW;IACf,KAAK;MACH;QACE,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,QAAQ,CAAC,EAAE,MAAM,UAAS,CAAE;QAC5B,SAAS,CAAC,EAAE,MAAM,UAAS,CAAE;;MAE/B;QACE,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,QAAQ,CAAA;QACR,SAAS,CAAC,EAAE,MAAM,QAAO,CAAE;;MAE7B;QACE,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,QAAQ,CAAA;QACR,SAAS,CAAC,EAAE,MAAM,WAAU,CAAE;;;IAGlC,SAAS;;AAEX,QAAM,CAAC,OAAO,UAAU,MAAM,IAAI,MAAM,cAAc,QAAQ;IAC5D,cAAc;IACd,WAAW;MACT;QACE,GAAG;QACH,cAAc;QACd,MAAM,CAAC,cAAc;QACrB;;MAEF,EAAE,GAAG,UAAU,cAAc,YAAY,QAAO;MAChD,EAAE,GAAG,UAAU,cAAc,UAAU,QAAO;;GAEjD;AACD,QAAM,YAAY,YAAY,SAAS,KAAK,QAAQ,QAAQ,QAAQ,CAAC;AACrE,SAAO,EAAE,UAAU,WAAW,QAAQ,MAAK;AAC7C;;;ACtGA,eAAsBC,UAMpB,QACA,aAKI,CAAA,GAAE;AAEN,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,UAAe,UAAU;AAC1D,QAAM,QAAQ,MAAM,OAAO,IAAI;AAC/B,SAAO;IACL,GAAI;IAMJ,SAAS,OAAO,MAAM;;AAE1B;;;AClDM,SAAUC,gBAKd,QACA,aAAwD,CAAA,GAAE;AAE1D,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,gBAAqB,gBAAgB;AACtE,SAAO,OAAO,IAAI;AACpB;;;ACRM,SAAUC,0BAKd,QACA,aAAkE,CAAA,GAAE;AAEpE,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UACb,QACA,0BACA,0BAA0B;AAE5B,SAAO,OAAO,IAAI;AACpB;;;ACtBA,eAAsB,YACpB,QACA,YAAyC;AAEzC,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,SAAkB,aAAa;AAChE,SAAO,OAAO,IAAI;AACpB;;;ACVA,eAAsBC,gBACpB,QACA,YAAoC;AAEpC,QAAM,EAAE,WAAW,GAAE,IAAK;AAC1B,QAAM,SAAS,MAAM,mBAAmB,QAAQ,EAAE,UAAS,CAAE;AAC7D,SAAO,eAAoB,QAAQ,EAAE,GAAE,CAAE;AAC3C;;;ACDA,eAAsBC,iBAIpB,QACA,aAAyD,CAAA,GAAE;AAE3D,QAAM,EAAE,SAAS,SAAS,UAAS,IAAK;AACxC,QAAM,SAAS,MAAM,mBAAmB,QAAQ,EAAE,SAAS,UAAS,CAAE;AACtE,SAAO,gBAAqB,QAAe;IACzC;IACA;GACD;AACH;;;AChCM,SAAUC,YACd,QAAc;AAEd,SAAO,OAAO,MAAM;AACtB;;;ACDA,IAAI,iBAAmC,CAAA;AAGjC,SAAU,UACd,QAAc;AAEd,QAAM,SAAS,OAAO;AACtB,MAAI,UAAU,gBAAgB,MAAM;AAClC,WAAO;AACT,mBAAiB;AACjB,SAAO;AACT;;;ACmBM,SAAU,UAId,QACA,aAAmD,CAAA,GAAE;AAErD,MAAI,SAAS;AACb,MAAI;AACF,aAAS,OAAO,UAAU,UAAU;EACtC,QAAQ;EAAC;AACT,SAAO;AACT;;;AC7CA,IAAI,sBAAoC,CAAA;AAGlC,SAAU,eAAe,QAAc;AAC3C,QAAM,cAAc,CAAC,GAAG,OAAO,MAAM,YAAY,OAAM,CAAE;AACzD,MAAI,OAAO,MAAM,WAAW;AAAgB,WAAO;AACnD,MAAI,UAAU,qBAAqB,WAAW;AAAG,WAAO;AACxD,wBAAsB;AACtB,SAAO;AACT;;;ACTA,IAAI,qBAA2C,CAAA;AAGzC,SAAU,cACd,QAAc;AAEd,QAAM,aAAa,OAAO;AAC1B,MAAI,UAAU,oBAAoB,UAAU;AAAG,WAAO;AACtD,uBAAqB;AACrB,SAAO;AACT;;;ACKM,SAAUC,eACd,QACA,YAA2C;AAE3C,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,eAAoB,eAAe;AACpE,SAAO,OAAO,IAAI;AACpB;;;ACRM,SAAUC,cACd,QACA,YAA0C;AAE1C,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,cAAmB,cAAc;AAClE,SAAO,OAAO,IAAI;AACpB;;;ACRM,SAAUC,YACd,QACA,YAAwC;AAExC,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,YAAiB,YAAY;AAC9D,SAAO,OAAO,IAAI;AACpB;;;ACRM,SAAUC,gBACd,QACA,YAA4C;AAE5C,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,gBAAqB,gBAAgB;AACtE,SAAO,OAAO,IAAI;AACpB;;;ACRM,SAAUC,YACd,QACA,YAAwC;AAExC,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,YAAiB,YAAY;AAC9D,SAAO,OAAO,IAAI;AACpB;;;ACNM,SAAUC,eAKd,QACA,YAAoD;AAEpD,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,eAAoB,eAAe;AACpE,SAAO,OAAO,IAAI;AACpB;;;ACbM,SAAUC,aAKd,QACA,aAAqD,CAAA,GAAE;AAEvD,QAAM,EAAE,QAAO,IAAK;AACpB,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,aAAkB,aAAa;AAChE,SAAO,OAAO,CAAA,CAAE;AAClB;;;ACbA,eAAsBC,UACpB,QACA,YAAsC;AAEtC,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,UAAe,UAAU;AAC1D,SAAO,OAAO,IAAI;AACpB;;;ACUM,SAAU,gBAId,QACA,aAAyD,CAAA,GAAE;AAE3D,QAAM,SAAS,UAAU,QAAQ,UAAU;AAC3C,SAAQ,iCAAmB,OAAO;AAIpC;;;AC9BA,eAAsBC,cACpB,QACA,YAA0C;AAE1C,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,cAAmB,cAAc;AAClE,SAAO,OAAO,IAAI;AACpB;;;ACOA,eAAsB,SACpB,QACA,YAAsC;AAEtC,QAAM,EAAE,SAAS,SAAS,aAAa,OAAO,GAAE,IAAK;AAErD,WAAS,OAA0C,MAAU;AAC3D,WAAO;MACL;QACE,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,QAAQ,CAAA;QACR,SAAS,CAAC,EAAE,MAAM,QAAO,CAAE;;MAE7B;QACE,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,QAAQ,CAAA;QACR,SAAS,CAAC,EAAE,KAAI,CAAE;;MAEpB;QACE,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,QAAQ,CAAA;QACR,SAAS,CAAC,EAAE,KAAI,CAAE;;MAEpB;QACE,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,QAAQ,CAAA;QACR,SAAS,CAAC,EAAE,MAAM,UAAS,CAAE;;;EAGnC;AAEA,MAAI;AACF,UAAM,MAAM,OAAO,QAAQ;AAC3B,UAAM,iBAAiB,EAAE,SAAS,KAAK,QAAO;AAC9C,UAAM,CAAC,UAAU,MAAM,QAAQ,WAAW,IAAI,MAAM,cAAc,QAAQ;MACxE,cAAc;MACd,WAAW;QACT,EAAE,GAAG,gBAAgB,cAAc,WAAU;QAC7C,EAAE,GAAG,gBAAgB,cAAc,OAAM;QACzC,EAAE,GAAG,gBAAgB,cAAc,SAAQ;QAC3C,EAAE,GAAG,gBAAgB,cAAc,cAAa;;KAEnD;AAGD,QAAI,KAAK,iBAAiB;AAAgC,YAAM,KAAK;AACrE,QAAI,OAAO,iBAAiB;AAC1B,YAAM,OAAO;AAGf,QAAI,SAAS;AAAO,YAAM,SAAS;AACnC,QAAI,YAAY;AAAO,YAAM,YAAY;AAEzC,WAAO;MACL;MACA,UAAU,SAAS;MACnB,MAAM,KAAK;MACX,QAAQ,OAAO;MACf,aAAa;QACX,WAAW,YAAY,YAAY,QAAS,QAAQ,IAAI,CAAC;QACzD,OAAO,YAAY;;;EAGzB,SAAS,OAAO;AAId,QAAI,iBAAiB,gCAAgC;AACnD,YAAM,MAAM,OAAO,SAAS;AAC5B,YAAM,iBAAiB,EAAE,SAAS,KAAK,QAAO;AAC9C,YAAM,CAAC,UAAU,MAAM,QAAQ,WAAW,IAAI,MAAM,cAClD,QACA;QACE,cAAc;QACd,WAAW;UACT,EAAE,GAAG,gBAAgB,cAAc,WAAU;UAC7C,EAAE,GAAG,gBAAgB,cAAc,OAAM;UACzC,EAAE,GAAG,gBAAgB,cAAc,SAAQ;UAC3C,EAAE,GAAG,gBAAgB,cAAc,cAAa;;OAEnD;AAEH,aAAO;QACL;QACA;QACA,MAAM,YAAY,KAAK,MAAa,EAAE,KAAK,QAAO,CAAE,CAAC;QACrD,QAAQ,YAAY,KAAK,QAAe,EAAE,KAAK,QAAO,CAAE,CAAC;QACzD,aAAa;UACX,WAAW,YAAY,aAAa,QAAQ,IAAI,CAAC;UACjD,OAAO;;;IAGb;AAEA,UAAM;EACR;AACF;;;ACvGM,SAAUC,gBAId,QACA,YAAqD;AAErD,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,gBAAqB,gBAAgB;AACtE,SAAO,OAAO,IAAI;AAGpB;;;AChBM,SAAUC,6BAMd,QACA,YAAkE;AAElE,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UACb,QACA,6BACA,6BAA6B;AAE/B,SAAO,OAAO,IAAkD;AAClE;;;AC/BA,eAAsBC,qBACpB,QACA,YAAiD;AAEjD,QAAM,EAAE,SAAS,aAAa,UAAU,QAAO,IAAK;AAEpD,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UACb,QACA,qBACA,qBAAqB;AAEvB,SAAO,OAAO,cAAc,EAAE,SAAS,YAAW,IAAK,EAAE,SAAS,SAAQ,CAAE;AAC9E;;;ACMA,eAAsBC,uBAIpB,QACA,YAAmD;AAEnD,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UACb,QACA,uBACA,uBAAuB;AAEzB,SAAO,OAAO,IAAI;AAGpB;;;ACpBA,eAAsB,gBAIpB,QACA,aAAyD,CAAA,GAAE;AAE3D,QAAM,SAAS,MAAM,mBAAmB,QAAQ,UAAU;AAE1D,SAAO,OAAO,OAAO,aAAa;AAIpC;;;ACgDA,eAAsBC,2BAQpB,QACA,YAAyE;AAEzE,QAAM,EAAE,SAAS,UAAU,SAAS,GAAG,KAAI,IAAK;AAEhD,QAAM,UAAU,YAAY,WAAW,MAAM,EAAE;AAC/C,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAE3C,QAAM,SAAS,UACb,QACA,2BACA,2BAA2B;AAE7B,SAAO,OAAO;IACZ,GAAG;IACH,GAAI,UAAU,EAAE,QAAO,IAAK,CAAA;GAC0B;AAG1D;;;AC5GA,IAAI,iBAAiB;AAGrB,eAAsB,UACpB,QACA,aAAkC,CAAA,GAAE;AALtC;AAQE,MAAI;AAAgB,WAAO,CAAA;AAC3B,mBAAiB;AAEjB,SAAO,SAAS,CAAC,OAAO;IACtB,GAAG;IACH,QAAQ,EAAE,UAAU,iBAAiB;IACrC;AAEF,QAAM,aAA0B,CAAA;AAChC,OAAI,gBAAW,eAAX,mBAAuB,QAAQ;AACjC,eAAW,cAAc,WAAW,YAAY;AAC9C,UAAI;AAEJ,UAAI,OAAO,eAAe;AACxB,oBAAY,OAAO,UAAU,WAAW,MAAM,UAAU;;AACrD,oBAAY;AACjB,iBAAW,KAAK,SAAS;IAC3B;EACF;AAAO,eAAW,KAAK,GAAG,OAAO,UAAU;AAG3C,MAAI;AACJ,MAAI;AACF,wBAAoB,QAAM,YAAO,YAAP,mBAAgB,QAAQ;EACpD,QAAQ;EAAC;AACT,QAAM,SAAiC,CAAA;AACvC,aAAW,CAAC,EAAE,UAAU,KAAK,OAAO,MAAM,aAAa;AACrD,WAAO,WAAW,UAAU,EAAE,IAAI;EACpC;AACA,MAAI;AAAmB,WAAO,iBAAiB,IAAI;AACnD,QAAM,SACJ,OAAO,KAAK,MAAM,EAAE,SAAS;;IAEzB,CAAC,GAAG,UAAU,EAAE,KACd,CAAC,GAAG,OAAO,OAAO,EAAE,EAAE,KAAK,OAAO,OAAO,EAAE,EAAE,KAAK,GAAG;MAEvD;AAGN,MAAI,YAAY;AAChB,QAAM,cAA4B,CAAA;AAClC,QAAM,YAAuB,CAAA;AAC7B,aAAW,aAAa,QAAQ;AAC9B,UAAM,WAAW,MAAM,UAAU,YAAW,EAAG,MAAM,MAAM,MAAS;AACpE,QAAI,CAAC;AAAU;AAMf,QAAI,UAAU,KAAK,CAAC,MAAM,MAAM,QAAQ;AAAG;AAE3C,UAAM,eAAe,MAAM,UAAU,aAAY;AACjD,QAAI,CAAC;AAAc;AAEnB,UAAM,OAAO,MAAM,UAChB,QAAQ,EAAE,gBAAgB,KAAI,CAAE,EAChC,MAAM,MAAM,IAAI;AACnB,QAAI,CAAC;AAAM;AAEX,cAAU,QAAQ,IAAI,WAAW,OAAO,UAAU,OAAO,OAAO;AAChE,cAAU,QAAQ,GAAG,UAAU,OAAO,UAAU,OAAO,MAAM;AAC7D,cAAU,QAAQ,GAAG,cAAc,OAAO,UAAU,OAAO,UAAU;AAErE,WAAO,SAAS,CAAC,MAAK;AACpB,YAAMC,eAAc,IAAI,IAAI,YAAY,EAAE,cAAc,oBAAI,IAAG,CAAE,EAAE,IACjE,UAAU,KACV,EAAE,UAAU,KAAK,UAAU,SAAS,KAAK,SAAS,UAAS,CAAE;AAE/D,aAAO;QACL,GAAG;QACH,SAAS,YAAY,EAAE,UAAU,UAAU;QAC3C,aAAAA;;IAEJ,CAAC;AACD,gBAAY,KAAK;MACf,UAAU,KAAK;MACf,SAAS,KAAK;MACd;KACD;AACD,cAAU,KAAK,QAAQ;AACvB,gBAAY;EACd;AAGA,MACE,OAAO,MAAM,WAAW,kBACxB,OAAO,MAAM,WAAW,cACxB;AAEA,QAAI,CAAC;AACH,aAAO,SAAS,CAAC,OAAO;QACtB,GAAG;QACH,aAAa,oBAAI,IAAG;QACpB,SAAS;QACT,QAAQ;QACR;;AACC,aAAO,SAAS,CAAC,OAAO,EAAE,GAAG,GAAG,QAAQ,YAAW,EAAG;EAC7D;AAEA,mBAAiB;AACjB,SAAO;AACT;;;AC3EA,eAAsBC,WAKpB,QACA,YAAuD;AAEvD,QAAM,EAAE,SAAS,SAAS,WAAW,OAAO,GAAG,KAAI,IAAK;AAExD,QAAM,SAAS,MAAM,mBAAmB,QAAQ;IAC9C;IACA;IACA;GACD;AAED,SAAO,UAAe,QAAQ;IAC5B,GAAI;IACJ,GAAI,OAAO,YAAY,cAAc,EAAE,QAAO,IAAK,CAAA;IACnD;IACA,OAAO,UAAU,EAAE,IAAI,QAAO,IAAK;GACpC;AACH;;;AChBA,eAAsBC,iBAIpB,QACA,YAAsD;AAEtD,QAAM,EAAE,SAAS,SAAS,WAAW,GAAG,KAAI,IAAK;AAEjD,MAAI;AACJ,MAAI,OAAO,YAAY,aAAY,mCAAS,UAAS;AACnD,aAAS,OAAO,UAAU,EAAE,QAAO,CAAE;;AAErC,aAAS,MAAM,mBAAmB,QAAQ;MACxC,SAAS,WAAW;MACpB;MACA;KACD;AAEH,QAAM,SAAS,UAAU,QAAQ,iBAAsB,iBAAiB;AACxE,QAAM,OAAO,MAAM,OAAO;IACxB,GAAI;IACJ,GAAI,UAAU,EAAE,QAAO,IAAK,CAAA;IAC5B,OAAO,UAAU,EAAE,IAAI,QAAO,IAAK;IACnC,KAAK,KAAK,OAAO;GAClB;AAED,SAAO;AACT;;;AClEA,eAAsBC,iBACpB,QACA,YAAqC;AAErC,QAAM,EAAE,WAAW,GAAE,IAAK;AAC1B,QAAM,SAAS,MAAM,mBAAmB,QAAQ,EAAE,UAAS,CAAE;AAC7D,SAAO,gBAAqB,QAAQ,EAAE,GAAE,CAAE;AAC5C;;;ACQA,eAAsBC,aACpB,QACA,YAAiC;AAEjC,QAAM,EAAE,SAAS,WAAW,GAAG,KAAI,IAAK;AAExC,MAAI;AACJ,MAAI,OAAO,YAAY,YAAY,QAAQ,SAAS;AAClD,aAAS,OAAO,UAAS;;AACtB,aAAS,MAAM,mBAAmB,QAAQ,EAAE,SAAS,UAAS,CAAE;AAErE,QAAM,SAAS,UAAU,QAAQ,aAAkB,aAAa;AAChE,SAAO,OAAO;IACZ,GAAG;IACH,GAAI,UAAU,EAAE,QAAO,IAAK,CAAA;GACU;AAC1C;;;ACVA,eAAsBC,eAIpB,QACA,YAA2D;AAE3D,QAAM,EAAE,SAAS,WAAW,GAAG,KAAI,IAAK;AAExC,MAAI;AACJ,MAAI,OAAO,YAAY,YAAY,QAAQ,SAAS;AAClD,aAAS,OAAO,UAAS;;AACtB,aAAS,MAAM,mBAAmB,QAAQ,EAAE,SAAS,UAAS,CAAE;AAErE,QAAM,SAAS,UAAU,QAAQ,eAAoB,eAAe;AACpE,SAAO,OAAO;IACZ,GAAG;IACH,GAAI,UAAU,EAAE,QAAO,IAAK,CAAA;GACc;AAC9C;;;ACwDA,eAAsBC,kBAWpB,QACA,YAMC;AAID,QAAM,EAAE,KAAK,SAAS,WAAW,GAAG,KAAI,IACtC;AAEF,MAAI;AACJ,MAAI,WAAW;AAAS,cAAU,WAAW;OACxC;AACH,UAAM,kBAAkB,MAAM,mBAAmB,QAAQ;MACvD;MACA;KACD;AACD,cAAU,gBAAgB;EAC5B;AAEA,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,kBAAuB,kBAAkB;AAC1E,QAAM,EAAE,QAAQ,QAAO,IAAK,MAAM,OAAO,EAAE,GAAG,MAAM,KAAK,QAAO,CAAE;AAElE,SAAO;IACL,SAAS,OAAO,MAAM;IACtB;IACA,SAAS,EAAE,GAAG,SAAS,QAAO;;AAQlC;;;AC3IA,eAAsB,cACpB,QACA,YAAmC;AAxBrC;AA0BE,QAAM,EAAE,UAAS,IAAK;AAEtB,QAAM,aAAa,OAAO,MAAM,YAAY,IAAI,UAAU,GAAG;AAC7D,MAAI,CAAC;AAAY,UAAM,IAAI,2BAA0B;AAErD,UAAM,YAAO,YAAP,mBAAgB,QAAQ,qBAAqB,UAAU;AAC7D,SAAO,SAAS,CAAC,OAAO;IACtB,GAAG;IACH,SAAS,UAAU;IACnB;AACF,SAAO;IACL,UAAU,WAAW;IACrB,SAAS,WAAW;;AAExB;;;ACWA,eAAsBC,aAIpB,QACA,YAAkD;AApDpD;AAsDE,QAAM,EAAE,2BAA2B,QAAO,IAAK;AAE/C,QAAM,aAAa,OAAO,MAAM,YAAY,MAC1C,gBAAW,cAAX,mBAAsB,QAAO,OAAO,MAAM,OAAQ;AAEpD,MAAI,YAAY;AACd,UAAM,YAAY,WAAW;AAC7B,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,6BAA6B,EAAE,UAAS,CAAE;AACtD,UAAMC,SAAQ,MAAM,UAAU,YAAY;MACxC;MACA;KACD;AACD,WAAOA;EACT;AAEA,QAAM,QAAQ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACxD,MAAI,CAAC;AAAO,UAAM,IAAI,wBAAuB;AAC7C,SAAO,SAAS,CAAC,OAAO,EAAE,GAAG,GAAG,QAAO,EAAG;AAC1C,SAAO;AACT;;;AC7DA,eAAsBC,eACpB,QACA,YAA2C;AAE3C,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,eAAoB,eAAe;AACpE,SAAO,OAAO,IAAI;AACpB;;;ACFA,eAAsBC,iBAKpB,QACA,YAAqE;AAErE,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,iBAAsB,iBAAiB;AACxE,SAAO,OAAO,IAAsC;AACtD;;;ACpBA,eAAsBC,oBACpB,QACA,YAAwC;AAExC,QAAM,EAAE,UAAS,IAAK;AACtB,QAAM,SAAS,MAAM,mBAAmB,QAAQ,EAAE,UAAS,CAAE;AAC7D,SAAO,mBAAwB,QAAQ,UAAU;AACnD;;;ACZM,SAAU,aACd,QACA,YAA0C;AAE1C,QAAM,EAAE,SAAQ,IAAK;AAErB,SAAO,OAAO,UAAU,MAAM,WAAW,MAAM,GAAG,UAAU;IAC1D,WAAW,GAAG,GAAC;AACb,YAAM,EAAE,WAAW,YAAY,GAAG,MAAK,IAAK;AAC5C,YAAM,EAAE,WAAW,YAAY,GAAG,MAAK,IAAK;AAC5C,aACE,UAAU,OAAO,KAAK;OAEtB,yCAAY,SAAO,yCAAY,QAC/B,yCAAY,UAAQ,yCAAY;IAEpC;GACD;AACH;;;ACCA,eAAsBC,YACpB,QACA,YAAgC;AAEhC,QAAM,EAAE,WAAW,GAAG,KAAI,IAAK;AAE/B,QAAM,SAAS,MAAM,mBAAmB,QAAQ,EAAE,UAAS,CAAE;AAE7D,QAAM,SAAS,UAAU,QAAQ,YAAiB,YAAY;AAC9D,SAAO,OAAO,IAAiC;AACjD;;;ACGM,SAAUC,aAOd,QACA,YAKC;AAED,QAAM,EAAE,qBAAqB,OAAO,UAAU,oBAAoB,GAAG,KAAI,IACvE;AAEF,MAAI;AACJ,QAAM,WAAW,CAAC,YAA+B;AAC/C,QAAI;AAAS,cAAO;AAEpB,UAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,UAAM,SAAS,UAAU,QAAQ,aAAkB,aAAa;AAChE,cAAU,OAAO,IAAkC;AACnD,WAAO;EACT;AAGA,QAAM,WAAW,SAAS,WAAW,OAAO;AAG5C,MAAI;AACJ,MAAI,sBAAsB,CAAC,WAAW;AACpC,kBAAc,OAAO,UACnB,CAAC,EAAE,QAAO,MAAO,SACjB,OAAO,YAAY,SAAS,OAAO,CAAC;AAGxC,SAAO,MAAK;AACV;AACA;EACF;AACF;;;AChDM,SAAUC,kBAKd,QACA,YAAuD;AAEvD,QAAM,EAAE,qBAAqB,OAAO,UAAU,oBAAoB,GAAG,KAAI,IACvE;AAEF,MAAI;AACJ,QAAM,WAAW,CAAC,YAA+B;AAC/C,QAAI;AAAS,cAAO;AAEpB,UAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,UAAM,SAAS,UAAU,QAAQ,kBAAuB,kBAAkB;AAC1E,cAAU,OAAO,IAAuC;AACxD,WAAO;EACT;AAGA,QAAM,WAAW,SAAS,WAAW,OAAO;AAG5C,MAAI;AACJ,MAAI,sBAAsB,CAAC,WAAW;AACpC,kBAAc,OAAO,UACnB,CAAC,EAAE,QAAO,MAAO,SACjB,OAAO,YAAY,SAAS,OAAO,CAAC;AAGxC,SAAO,MAAK;AACV;AACA;EACF;AACF;;;AChEM,SAAU,aACd,QACA,YAA0C;AAE1C,QAAM,EAAE,SAAQ,IAAK;AACrB,SAAO,OAAO,UAAU,CAAC,UAAU,MAAM,SAAS,QAAQ;AAC5D;;;ACFM,SAAU,YAId,QACA,YAAkD;AAElD,QAAM,EAAE,SAAQ,IAAK;AACrB,SAAO,OAAO,UACZ,MAAM,UAAU,MAAM,GACtB,UACA;IACE,WAAW,GAAG,GAAC;AACb,cAAO,uBAAG,UAAQ,uBAAG;IACvB;GACD;AAEL;;;ACjBM,SAAU,iBACd,QACA,YAAsC;AAEtC,QAAM,EAAE,SAAQ,IAAK;AACrB,SAAO,OAAO,UAAU,MAAM,eAAe,MAAM,GAAG,UAAU;IAC9D,YAAY;GACb;AACH;;;ACZM,SAAU,gBACd,QACA,YAA6C;AAE7C,QAAM,EAAE,SAAQ,IAAK;AACrB,SAAO,OAAO,UAAU,WAAW,UAAU,CAAC,YAAY,mBAAkB;AAC1E,aAAS,OAAO,OAAO,UAAU,GAAG,cAAc;EACpD,CAAC;AACH;;;ACgCM,SAAUC,oBAOd,QACA,YAMC;AAED,QAAM,EAAE,qBAAqB,OAAO,UAAU,oBAAoB,GAAG,KAAI,IACvE;AAEF,MAAI;AACJ,QAAM,WAAW,CAAC,YAA+B;AAC/C,QAAI;AAAS,cAAO;AAEpB,UAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,UAAM,SAAS,UACb,QACA,oBACA,oBAAoB;AAEtB,cAAU,OAAO,IAAoD;AACrE,WAAO;EACT;AAGA,QAAM,WAAW,SAAS,WAAW,OAAO;AAG5C,MAAI;AACJ,MAAI,sBAAsB,CAAC,WAAW;AACpC,kBAAc,OAAO,UACnB,CAAC,EAAE,QAAO,MAAO,SACjB,OAAO,YAAY,SAAS,OAAO,CAAC;AAGxC,SAAO,MAAK;AACV;AACA;EACF;AACF;;;AC3DM,SAAUC,0BAId,QACA,YAA+D;AAE/D,QAAM,EAAE,qBAAqB,OAAO,UAAU,oBAAoB,GAAG,KAAI,IACvE;AAEF,MAAI;AACJ,QAAM,WAAW,CAAC,YAA+B;AAC/C,QAAI;AAAS,cAAO;AAEpB,UAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,UAAM,SAAS,UACb,QACA,0BACA,0BAA0B;AAE5B,cAAU,OAAO,IAA+C;AAChE,WAAO;EACT;AAGA,QAAM,WAAW,SAAS,WAAW,OAAO;AAG5C,MAAI;AACJ,MAAI,sBAAsB,CAAC,WAAW;AACpC,kBAAc,OAAO,UACnB,CAAC,EAAE,QAAO,MAAO,SACjB,OAAO,YAAY,SAAS,OAAO,CAAC;AAGxC,SAAO,MAAK;AACV;AACA;EACF;AACF;;;AC7DM,SAAU,kBAId,QACA,YAAwD;AAExD,QAAM,EAAE,SAAQ,IAAK;AACrB,SAAO,OAAO,UACZ,MAAM,gBAAgB,MAAM,GAC5B,UACA;IACE,WAAW,GAAG,GAAC;AACb,cAAO,uBAAG,UAAQ,uBAAG;IACvB;GACD;AAEL;;;ACKA,eAAsBC,2BAIpB,QACA,YAAgE;AAEhE,QAAM,EAAE,SAAS,UAAU,GAAG,GAAG,KAAI,IAAK;AAE1C,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UACb,QACA,2BACA,2BAA2B;AAE7B,QAAM,UAAU,MAAM,OAAO,EAAE,GAAG,MAAM,QAAO,CAAE;AAEjD,MAAI,QAAQ,WAAW,YAAY;AACjC,UAAM,wBAAwB,UAC5B,QACA,gBACA,gBAAgB;AAElB,UAAM,MAAM,MAAM,sBAAsB,EAAE,MAAM,QAAQ,gBAAe,CAAE;AACzE,UAAM,cAAc,UAAU,QAAQ,MAAM,MAAM;AAClD,UAAM,OAAO,MAAM,YAAY;MAC7B,GAAI;MACJ,MAAM,IAAI;MACV,UAAU,IAAI,SAAS,YAAY,IAAI,WAAW;MAClD,cAAc,IAAI,SAAS,YAAY,IAAI,eAAe;MAC1D,sBACE,IAAI,SAAS,YAAY,IAAI,uBAAuB;KACvD;AACD,UAAM,UAAS,6BAAM,QACjB,YAAY,KAAK,KAAK,KAAK,UAAU,GAAG,CAAC,EAAE,IAC3C;AACJ,UAAM,IAAI,MAAM,MAAM;EACxB;AAEA,SAAO;IACL,GAAG;IACH,SAAS,OAAO,MAAM;;AAE1B;;;ACNA,eAAsBC,eAWpB,QACA,YAA6E;AAE7E,QAAM,EAAE,SAAS,SAAS,WAAW,GAAG,QAAO,IAAK;AAEpD,MAAI;AACJ,MAAI,OAAO,YAAY,aAAY,mCAAS,UAAS;AACnD,aAAS,OAAO,UAAU,EAAE,QAAO,CAAE;;AAErC,aAAS,MAAM,mBAAmB,QAAQ;MACxC,SAAS,WAAW;MACpB;MACA;KACD;AAEH,QAAM,SAAS,UAAU,QAAQ,eAAoB,eAAe;AACpE,QAAM,OAAO,MAAM,OAAO;IACxB,GAAI;IACJ,GAAI,UAAU,EAAE,QAAO,IAAK,CAAA;IAC5B,OAAO,UAAU,EAAE,IAAI,QAAO,IAAK;GACpC;AAED,SAAO;AACT;;;ACzGM,SAAU,QAAQ,QAAgB,YAA6B;AACnE,QAAM,EAAE,cAAc,iBAAgB,IAAK;AAE3C,MAAI,gBAAgB,CAAC,OAAO,UAAU,MAAM,QAAQ,YAAW;AAC7D,WAAO,SAAS;MACd,GAAG;MACH,SAAS,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,aAAa,OAAO,IAC5D,aAAa,UACb,OAAO,OAAO,CAAC,EAAE;MACrB,aAAa,mBAAmB,aAAa,cAAc,oBAAI,IAAG;MAClE,QAAQ,mBAAmB,iBAAiB;KAC7C;AAEH,SAAO;IACL,MAAM,UAAO;AACX,UAAI,OAAO,UAAU,KAAK;AACxB,cAAM,OAAO,UAAU,MAAM,QAAQ,UAAS;AAC9C,YAAI,OAAO,UAAU,MAAM;AACzB,iBAAO,UAAU,WAAW,SAAS,CAAC,eAAc;AA1B9D;AA2BY,kBAAM,UAAU,oBAAI,IAAG;AACvB,uBAAW,aAAa,cAAc,CAAA,GAAI;AACxC,kBAAI,UAAU,MAAM;AAClB,sBAAM,aAAa,MAAM,QAAQ,UAAU,IAAI,IAC3C,UAAU,OACV,CAAC,UAAU,IAAI;AACnB,2BAAW,QAAQ,YAAY;AAC7B,0BAAQ,IAAI,IAAI;gBAClB;cACF;YACF;AACA,kBAAM,iBAAiB,CAAA;AACvB,kBAAM,cAAY,YAAO,UAAU,SAAjB,mBAAuB,mBAAkB,CAAA;AAC3D,uBAAW,YAAY,WAAW;AAChC,kBAAI,QAAQ,IAAI,SAAS,KAAK,IAAI;AAAG;AACrC,oBAAM,cACJ,OAAO,UAAU,WAAW,0BAA0B,QAAQ;AAChE,oBAAM,YAAY,OAAO,UAAU,WAAW,MAAM,WAAW;AAC/D,6BAAe,KAAK,SAAS;YAC/B;AACA,mBAAO,CAAC,GAAG,YAAY,GAAG,cAAc;UAC1C,CAAC;QACH;MACF;AAEA,UAAI;AAAkB,kBAAU,MAAM;eAC7B,OAAO;AAEd,eAAO,SAAS,CAAC,OAAO;UACtB,GAAG;UACH,aAAa,oBAAI,IAAG;UACpB;IACN;;AAEJ;;;ACtDM,SAAU,eAAe,YAAoC;AAAnE;AACE,QAAM,EAAE,MAAK,IAAK;AAClB,QAAM,cAAc,MAAM,QAAQ,QAAQ,KAAK,CAAC;AAEhD,MAAI,CAAC,WAAW;AAAY,WAAO,CAAC,WAAW;AAE/C,QAAM,aAAY,sBAAW,eAAX,mBAAwB,MAAM,QAA9B,4BAAoC,EAAE,MAAK;AAC7D,QAAM,eAAc,4CAAW,UAAX,mBAAkB,eAE9B,CAAC,SAAS;AAClB,SAAO,WAAW,IAAI,CAAC,EAAE,MAAK,OAAO,+BAAO,QAAO,WAAW;AAChE;", "names": ["EventEmitter", "_BaseError_walk", "window", "connect", "disconnect", "isReconnecting", "provider", "error", "call", "result", "error", "value", "index", "value", "deserialize", "serialize", "hydrate", "createStore", "uid", "EventEmitter", "createStore", "connect", "getClient", "client", "chainId", "parameters", "version", "uid", "x", "disconnect", "connector", "fallback", "call", "connector", "chainId", "chain", "deployContract", "connections", "connector", "estimateGas", "estimateFeesPerGas", "estimateMaxPriorityFeePerGas", "uid", "chain", "multicall", "readContract", "index", "contracts", "multicall", "readContract", "getBalance", "getBlock", "getBlockNumber", "getBlockTransactionCount", "getCallsStatus", "getCapabilities", "get<PERSON>hainId", "getEnsAddress", "getEnsAvatar", "getEnsName", "getEnsResolver", "getEnsText", "getFeeHistory", "getGasPrice", "getProof", "getStorageAt", "getTransaction", "getTransactionConfirmations", "getTransactionCount", "getTransactionReceipt", "prepareTransactionRequest", "connections", "sendCalls", "sendTransaction", "showCallsStatus", "signMessage", "signTypedData", "simulateContract", "<PERSON><PERSON><PERSON><PERSON>", "chain", "verifyMessage", "verifyTypedData", "waitForCallsStatus", "watchAsset", "watchBlocks", "watchBlockNumber", "watchContractEvent", "watchPendingTransactions", "waitForTransactionReceipt", "writeContract"]}