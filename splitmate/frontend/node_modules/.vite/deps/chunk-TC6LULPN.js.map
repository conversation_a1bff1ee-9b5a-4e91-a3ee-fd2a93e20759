{"version": 3, "sources": ["../../@noble/hashes/src/hmac.ts", "../../@noble/curves/src/abstract/modular.ts", "../../@noble/curves/src/abstract/curve.ts", "../../@noble/curves/src/abstract/weierstrass.ts", "../../@noble/curves/src/_shortw_utils.ts", "../../@noble/curves/src/abstract/hash-to-curve.ts", "../../@noble/curves/src/secp256k1.ts"], "sourcesContent": ["/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\nimport { abytes, aexists, ahash, clean, Hash, toBytes, type CHash, type Input } from './utils.ts';\n\nexport class HMAC<T extends Hash<T>> extends Hash<HMAC<T>> {\n  oHash: T;\n  iHash: T;\n  blockLen: number;\n  outputLen: number;\n  private finished = false;\n  private destroyed = false;\n\n  constructor(hash: CHash, _key: Input) {\n    super();\n    ahash(hash);\n    const key = toBytes(_key);\n    this.iHash = hash.create() as T;\n    if (typeof this.iHash.update !== 'function')\n      throw new Error('Expected instance of class which extends utils.Hash');\n    this.blockLen = this.iHash.blockLen;\n    this.outputLen = this.iHash.outputLen;\n    const blockLen = this.blockLen;\n    const pad = new Uint8Array(blockLen);\n    // blockLen can be bigger than outputLen\n    pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n    for (let i = 0; i < pad.length; i++) pad[i] ^= 0x36;\n    this.iHash.update(pad);\n    // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n    this.oHash = hash.create() as T;\n    // Undo internal XOR && apply outer XOR\n    for (let i = 0; i < pad.length; i++) pad[i] ^= 0x36 ^ 0x5c;\n    this.oHash.update(pad);\n    clean(pad);\n  }\n  update(buf: Input): this {\n    aexists(this);\n    this.iHash.update(buf);\n    return this;\n  }\n  digestInto(out: Uint8Array): void {\n    aexists(this);\n    abytes(out, this.outputLen);\n    this.finished = true;\n    this.iHash.digestInto(out);\n    this.oHash.update(out);\n    this.oHash.digestInto(out);\n    this.destroy();\n  }\n  digest(): Uint8Array {\n    const out = new Uint8Array(this.oHash.outputLen);\n    this.digestInto(out);\n    return out;\n  }\n  _cloneInto(to?: HMAC<T>): HMAC<T> {\n    // Create new instance without calling constructor since key already in state and we don't know it.\n    to ||= Object.create(Object.getPrototypeOf(this), {});\n    const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n    to = to as this;\n    to.finished = finished;\n    to.destroyed = destroyed;\n    to.blockLen = blockLen;\n    to.outputLen = outputLen;\n    to.oHash = oHash._cloneInto(to.oHash);\n    to.iHash = iHash._cloneInto(to.iHash);\n    return to;\n  }\n  clone(): HMAC<T> {\n    return this._cloneInto();\n  }\n  destroy(): void {\n    this.destroyed = true;\n    this.oHash.destroy();\n    this.iHash.destroy();\n  }\n}\n\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nexport const hmac: {\n  (hash: CHash, key: Input, message: Input): Uint8Array;\n  create(hash: CHash, key: Input): HMAC<any>;\n} = (hash: CHash, key: Input, message: Input): Uint8Array =>\n  new HMAC<any>(hash, key).update(message).digest();\nhmac.create = (hash: CHash, key: Input) => new HMAC<any>(hash, key);\n", "/**\n * Utils for modular division and fields.\n * Field over 11 is a finite (Galois) field is integer number operations `mod 11`.\n * There is no division: it is replaced by modular multiplicative inverse.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport {\n  _validateObject,\n  anumber,\n  bitMask,\n  bytesToNumberBE,\n  bytesToNumberLE,\n  ensureBytes,\n  numberToBytesBE,\n  numberToBytesLE,\n} from '../utils.ts';\n\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = /* @__PURE__ */ BigInt(2), _3n = /* @__PURE__ */ BigInt(3);\n// prettier-ignore\nconst _4n = /* @__PURE__ */ BigInt(4), _5n = /* @__PURE__ */ BigInt(5);\nconst _8n = /* @__PURE__ */ BigInt(8);\n\n// Calculates a modulo b\nexport function mod(a: bigint, b: bigint): bigint {\n  const result = a % b;\n  return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\nexport function pow(num: bigint, power: bigint, modulo: bigint): bigint {\n  return FpPow(Field(modulo), num, power);\n}\n\n/** Does `x^(2^power)` mod p. `pow2(30, 4)` == `30^(2^4)` */\nexport function pow2(x: bigint, power: bigint, modulo: bigint): bigint {\n  let res = x;\n  while (power-- > _0n) {\n    res *= res;\n    res %= modulo;\n  }\n  return res;\n}\n\n/**\n * Inverses number over modulo.\n * Implemented using [Euclidean GCD](https://brilliant.org/wiki/extended-euclidean-algorithm/).\n */\nexport function invert(number: bigint, modulo: bigint): bigint {\n  if (number === _0n) throw new Error('invert: expected non-zero number');\n  if (modulo <= _0n) throw new Error('invert: expected positive modulus, got ' + modulo);\n  // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n  let a = mod(number, modulo);\n  let b = modulo;\n  // prettier-ignore\n  let x = _0n, y = _1n, u = _1n, v = _0n;\n  while (a !== _0n) {\n    // JIT applies optimization if those two lines follow each other\n    const q = b / a;\n    const r = b % a;\n    const m = x - u * q;\n    const n = y - v * q;\n    // prettier-ignore\n    b = a, a = r, x = u, y = v, u = m, v = n;\n  }\n  const gcd = b;\n  if (gcd !== _1n) throw new Error('invert: does not exist');\n  return mod(x, modulo);\n}\n\n// Not all roots are possible! Example which will throw:\n// const NUM =\n// n = 72057594037927816n;\n// Fp = Field(BigInt('0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaab'));\nfunction sqrt3mod4<T>(Fp: IField<T>, n: T) {\n  const p1div4 = (Fp.ORDER + _1n) / _4n;\n  const root = Fp.pow(n, p1div4);\n  // Throw if root^2 != n\n  if (!Fp.eql(Fp.sqr(root), n)) throw new Error('Cannot find square root');\n  return root;\n}\n\nfunction sqrt5mod8<T>(Fp: IField<T>, n: T) {\n  const p5div8 = (Fp.ORDER - _5n) / _8n;\n  const n2 = Fp.mul(n, _2n);\n  const v = Fp.pow(n2, p5div8);\n  const nv = Fp.mul(n, v);\n  const i = Fp.mul(Fp.mul(nv, _2n), v);\n  const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n  if (!Fp.eql(Fp.sqr(root), n)) throw new Error('Cannot find square root');\n  return root;\n}\n\n// TODO: Commented-out for now. Provide test vectors.\n// Tonelli is too slow for extension fields Fp2.\n// That means we can't use sqrt (c1, c2...) even for initialization constants.\n// if (P % _16n === _9n) return sqrt9mod16;\n// // prettier-ignore\n// function sqrt9mod16<T>(Fp: IField<T>, n: T, p7div16?: bigint) {\n//   if (p7div16 === undefined) p7div16 = (Fp.ORDER + BigInt(7)) / _16n;\n//   const c1 = Fp.sqrt(Fp.neg(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n//   const c2 = Fp.sqrt(c1);             //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n//   const c3 = Fp.sqrt(Fp.neg(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n//   const c4 = p7div16;                 //  4. c4 = (q + 7) / 16        # Integer arithmetic\n//   let tv1 = Fp.pow(n, c4);            //  1. tv1 = x^c4\n//   let tv2 = Fp.mul(c1, tv1);          //  2. tv2 = c1 * tv1\n//   const tv3 = Fp.mul(c2, tv1);        //  3. tv3 = c2 * tv1\n//   let tv4 = Fp.mul(c3, tv1);          //  4. tv4 = c3 * tv1\n//   const e1 = Fp.eql(Fp.sqr(tv2), n);  //  5.  e1 = (tv2^2) == x\n//   const e2 = Fp.eql(Fp.sqr(tv3), n);  //  6.  e2 = (tv3^2) == x\n//   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n//   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n//   const e3 = Fp.eql(Fp.sqr(tv2), n);  //  9.  e3 = (tv2^2) == x\n//   return Fp.cmov(tv1, tv2, e3); // 10.  z = CMOV(tv1, tv2, e3) # Select the sqrt from tv1 and tv2\n// }\n\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nexport function tonelliShanks(P: bigint): <T>(Fp: IField<T>, n: T) => T {\n  // Initialization (precomputation).\n  // Caching initialization could boost perf by 7%.\n  if (P < BigInt(3)) throw new Error('sqrt is not defined for small field');\n  // Factor P - 1 = Q * 2^S, where Q is odd\n  let Q = P - _1n;\n  let S = 0;\n  while (Q % _2n === _0n) {\n    Q /= _2n;\n    S++;\n  }\n\n  // Find the first quadratic non-residue Z >= 2\n  let Z = _2n;\n  const _Fp = Field(P);\n  while (FpLegendre(_Fp, Z) === 1) {\n    // Basic primality test for P. After x iterations, chance of\n    // not finding quadratic non-residue is 2^x, so 2^1000.\n    if (Z++ > 1000) throw new Error('Cannot find square root: probably non-prime P');\n  }\n  // Fast-path; usually done before Z, but we do \"primality test\".\n  if (S === 1) return sqrt3mod4;\n\n  // Slow-path\n  // TODO: test on Fp2 and others\n  let cc = _Fp.pow(Z, Q); // c = z^Q\n  const Q1div2 = (Q + _1n) / _2n;\n  return function tonelliSlow<T>(Fp: IField<T>, n: T): T {\n    if (Fp.is0(n)) return n;\n    // Check if n is a quadratic residue using Legendre symbol\n    if (FpLegendre(Fp, n) !== 1) throw new Error('Cannot find square root');\n\n    // Initialize variables for the main loop\n    let M = S;\n    let c = Fp.mul(Fp.ONE, cc); // c = z^Q, move cc from field _Fp into field Fp\n    let t = Fp.pow(n, Q); // t = n^Q, first guess at the fudge factor\n    let R = Fp.pow(n, Q1div2); // R = n^((Q+1)/2), first guess at the square root\n\n    // Main loop\n    // while t != 1\n    while (!Fp.eql(t, Fp.ONE)) {\n      if (Fp.is0(t)) return Fp.ZERO; // if t=0 return R=0\n      let i = 1;\n\n      // Find the smallest i >= 1 such that t^(2^i) ≡ 1 (mod P)\n      let t_tmp = Fp.sqr(t); // t^(2^1)\n      while (!Fp.eql(t_tmp, Fp.ONE)) {\n        i++;\n        t_tmp = Fp.sqr(t_tmp); // t^(2^2)...\n        if (i === M) throw new Error('Cannot find square root');\n      }\n\n      // Calculate the exponent for b: 2^(M - i - 1)\n      const exponent = _1n << BigInt(M - i - 1); // bigint is important\n      const b = Fp.pow(c, exponent); // b = 2^(M - i - 1)\n\n      // Update variables\n      M = i;\n      c = Fp.sqr(b); // c = b^2\n      t = Fp.mul(t, c); // t = (t * b^2)\n      R = Fp.mul(R, b); // R = R*b\n    }\n    return R;\n  };\n}\n\n/**\n * Square root for a finite field. Will try optimized versions first:\n *\n * 1. P ≡ 3 (mod 4)\n * 2. P ≡ 5 (mod 8)\n * 3. Tonelli-Shanks algorithm\n *\n * Different algorithms can give different roots, it is up to user to decide which one they want.\n * For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n */\nexport function FpSqrt(P: bigint): <T>(Fp: IField<T>, n: T) => T {\n  // P ≡ 3 (mod 4) => √n = n^((P+1)/4)\n  if (P % _4n === _3n) return sqrt3mod4;\n  // P ≡ 5 (mod 8) => Atkin algorithm, page 10 of https://eprint.iacr.org/2012/685.pdf\n  if (P % _8n === _5n) return sqrt5mod8;\n  // P ≡ 9 (mod 16) not implemented, see above\n  // Tonelli-Shanks algorithm\n  return tonelliShanks(P);\n}\n\n// Little-endian check for first LE bit (last BE bit);\nexport const isNegativeLE = (num: bigint, modulo: bigint): boolean =>\n  (mod(num, modulo) & _1n) === _1n;\n\n/** Field is not always over prime: for example, Fp2 has ORDER(q)=p^m. */\nexport interface IField<T> {\n  ORDER: bigint;\n  isLE: boolean;\n  BYTES: number;\n  BITS: number;\n  MASK: bigint;\n  ZERO: T;\n  ONE: T;\n  // 1-arg\n  create: (num: T) => T;\n  isValid: (num: T) => boolean;\n  is0: (num: T) => boolean;\n  isValidNot0: (num: T) => boolean;\n  neg(num: T): T;\n  inv(num: T): T;\n  sqrt(num: T): T;\n  sqr(num: T): T;\n  // 2-args\n  eql(lhs: T, rhs: T): boolean;\n  add(lhs: T, rhs: T): T;\n  sub(lhs: T, rhs: T): T;\n  mul(lhs: T, rhs: T | bigint): T;\n  pow(lhs: T, power: bigint): T;\n  div(lhs: T, rhs: T | bigint): T;\n  // N for NonNormalized (for now)\n  addN(lhs: T, rhs: T): T;\n  subN(lhs: T, rhs: T): T;\n  mulN(lhs: T, rhs: T | bigint): T;\n  sqrN(num: T): T;\n\n  // Optional\n  // Should be same as sgn0 function in\n  // [RFC9380](https://www.rfc-editor.org/rfc/rfc9380#section-4.1).\n  // NOTE: sgn0 is 'negative in LE', which is same as odd. And negative in LE is kinda strange definition anyway.\n  isOdd?(num: T): boolean; // Odd instead of even since we have it for Fp2\n  // legendre?(num: T): T;\n  invertBatch: (lst: T[]) => T[];\n  toBytes(num: T): Uint8Array;\n  fromBytes(bytes: Uint8Array): T;\n  // If c is False, CMOV returns a, otherwise it returns b.\n  cmov(a: T, b: T, c: boolean): T;\n}\n// prettier-ignore\nconst FIELD_FIELDS = [\n  'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n  'eql', 'add', 'sub', 'mul', 'pow', 'div',\n  'addN', 'subN', 'mulN', 'sqrN'\n] as const;\nexport function validateField<T>(field: IField<T>): IField<T> {\n  const initial = {\n    ORDER: 'bigint',\n    MASK: 'bigint',\n    BYTES: 'number',\n    BITS: 'number',\n  } as Record<string, string>;\n  const opts = FIELD_FIELDS.reduce((map, val: string) => {\n    map[val] = 'function';\n    return map;\n  }, initial);\n  _validateObject(field, opts);\n  // const max = 16384;\n  // if (field.BYTES < 1 || field.BYTES > max) throw new Error('invalid field');\n  // if (field.BITS < 1 || field.BITS > 8 * max) throw new Error('invalid field');\n  return field;\n}\n\n// Generic field functions\n\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nexport function FpPow<T>(Fp: IField<T>, num: T, power: bigint): T {\n  if (power < _0n) throw new Error('invalid exponent, negatives unsupported');\n  if (power === _0n) return Fp.ONE;\n  if (power === _1n) return num;\n  let p = Fp.ONE;\n  let d = num;\n  while (power > _0n) {\n    if (power & _1n) p = Fp.mul(p, d);\n    d = Fp.sqr(d);\n    power >>= _1n;\n  }\n  return p;\n}\n\n/**\n * Efficiently invert an array of Field elements.\n * Exception-free. Will return `undefined` for 0 elements.\n * @param passZero map 0 to 0 (instead of undefined)\n */\nexport function FpInvertBatch<T>(Fp: IField<T>, nums: T[], passZero = false): T[] {\n  const inverted = new Array(nums.length).fill(passZero ? Fp.ZERO : undefined);\n  // Walk from first to last, multiply them by each other MOD p\n  const multipliedAcc = nums.reduce((acc, num, i) => {\n    if (Fp.is0(num)) return acc;\n    inverted[i] = acc;\n    return Fp.mul(acc, num);\n  }, Fp.ONE);\n  // Invert last element\n  const invertedAcc = Fp.inv(multipliedAcc);\n  // Walk from last to first, multiply them by inverted each other MOD p\n  nums.reduceRight((acc, num, i) => {\n    if (Fp.is0(num)) return acc;\n    inverted[i] = Fp.mul(acc, inverted[i]);\n    return Fp.mul(acc, num);\n  }, invertedAcc);\n  return inverted;\n}\n\n// TODO: remove\nexport function FpDiv<T>(Fp: IField<T>, lhs: T, rhs: T | bigint): T {\n  return Fp.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, Fp.ORDER) : Fp.inv(rhs));\n}\n\n/**\n * Legendre symbol.\n * Legendre constant is used to calculate Legendre symbol (a | p)\n * which denotes the value of a^((p-1)/2) (mod p).\n *\n * * (a | p) ≡ 1    if a is a square (mod p), quadratic residue\n * * (a | p) ≡ -1   if a is not a square (mod p), quadratic non residue\n * * (a | p) ≡ 0    if a ≡ 0 (mod p)\n */\nexport function FpLegendre<T>(Fp: IField<T>, n: T): -1 | 0 | 1 {\n  // We can use 3rd argument as optional cache of this value\n  // but seems unneeded for now. The operation is very fast.\n  const p1mod2 = (Fp.ORDER - _1n) / _2n;\n  const powered = Fp.pow(n, p1mod2);\n  const yes = Fp.eql(powered, Fp.ONE);\n  const zero = Fp.eql(powered, Fp.ZERO);\n  const no = Fp.eql(powered, Fp.neg(Fp.ONE));\n  if (!yes && !zero && !no) throw new Error('invalid Legendre symbol result');\n  return yes ? 1 : zero ? 0 : -1;\n}\n\n// This function returns True whenever the value x is a square in the field F.\nexport function FpIsSquare<T>(Fp: IField<T>, n: T): boolean {\n  const l = FpLegendre(Fp, n);\n  return l === 1;\n}\n\nexport type NLength = { nByteLength: number; nBitLength: number };\n// CURVE.n lengths\nexport function nLength(n: bigint, nBitLength?: number): NLength {\n  // Bit size, byte size of CURVE.n\n  if (nBitLength !== undefined) anumber(nBitLength);\n  const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n  const nByteLength = Math.ceil(_nBitLength / 8);\n  return { nBitLength: _nBitLength, nByteLength };\n}\n\ntype FpField = IField<bigint> & Required<Pick<IField<bigint>, 'isOdd'>>;\ntype SqrtFn = (n: bigint) => bigint;\ntype FieldOpts = Partial<{ sqrt: SqrtFn; isLE: boolean; BITS: number }>;\n/**\n * Creates a finite field. Major performance optimizations:\n * * 1. Denormalized operations like mulN instead of mul.\n * * 2. Identical object shape: never add or remove keys.\n * * 3. `Object.freeze`.\n * Fragile: always run a benchmark on a change.\n * Security note: operations don't check 'isValid' for all elements for performance reasons,\n * it is caller responsibility to check this.\n * This is low-level code, please make sure you know what you're doing.\n *\n * Note about field properties:\n * * CHARACTERISTIC p = prime number, number of elements in main subgroup.\n * * ORDER q = similar to cofactor in curves, may be composite `q = p^m`.\n *\n * @param ORDER field order, probably prime, or could be composite\n * @param bitLen how many bits the field consumes\n * @param isLE (default: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nexport function Field(\n  ORDER: bigint,\n  bitLenOrOpts?: number | FieldOpts,\n  isLE = false,\n  opts: { sqrt?: SqrtFn } = {}\n): Readonly<FpField> {\n  if (ORDER <= _0n) throw new Error('invalid field: expected ORDER > 0, got ' + ORDER);\n  let _nbitLength: number | undefined = undefined;\n  let _sqrt: SqrtFn | undefined = undefined;\n  if (typeof bitLenOrOpts === 'object' && bitLenOrOpts != null) {\n    if (opts.sqrt || isLE) throw new Error('cannot specify opts in two arguments');\n    const _opts = bitLenOrOpts;\n    if (_opts.BITS) _nbitLength = _opts.BITS;\n    if (_opts.sqrt) _sqrt = _opts.sqrt;\n    if (typeof _opts.isLE === 'boolean') isLE = _opts.isLE;\n  } else {\n    if (typeof bitLenOrOpts === 'number') _nbitLength = bitLenOrOpts;\n    if (opts.sqrt) _sqrt = opts.sqrt;\n  }\n  const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, _nbitLength);\n  if (BYTES > 2048) throw new Error('invalid field: expected ORDER of <= 2048 bytes');\n  let sqrtP: ReturnType<typeof FpSqrt>; // cached sqrtP\n  const f: Readonly<FpField> = Object.freeze({\n    ORDER,\n    isLE,\n    BITS,\n    BYTES,\n    MASK: bitMask(BITS),\n    ZERO: _0n,\n    ONE: _1n,\n    create: (num) => mod(num, ORDER),\n    isValid: (num) => {\n      if (typeof num !== 'bigint')\n        throw new Error('invalid field element: expected bigint, got ' + typeof num);\n      return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n    },\n    is0: (num) => num === _0n,\n    // is valid and invertible\n    isValidNot0: (num: bigint) => !f.is0(num) && f.isValid(num),\n    isOdd: (num) => (num & _1n) === _1n,\n    neg: (num) => mod(-num, ORDER),\n    eql: (lhs, rhs) => lhs === rhs,\n\n    sqr: (num) => mod(num * num, ORDER),\n    add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n    sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n    mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n    pow: (num, power) => FpPow(f, num, power),\n    div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n\n    // Same as above, but doesn't normalize\n    sqrN: (num) => num * num,\n    addN: (lhs, rhs) => lhs + rhs,\n    subN: (lhs, rhs) => lhs - rhs,\n    mulN: (lhs, rhs) => lhs * rhs,\n\n    inv: (num) => invert(num, ORDER),\n    sqrt:\n      _sqrt ||\n      ((n) => {\n        if (!sqrtP) sqrtP = FpSqrt(ORDER);\n        return sqrtP(f, n);\n      }),\n    toBytes: (num) => (isLE ? numberToBytesLE(num, BYTES) : numberToBytesBE(num, BYTES)),\n    fromBytes: (bytes) => {\n      if (bytes.length !== BYTES)\n        throw new Error('Field.fromBytes: expected ' + BYTES + ' bytes, got ' + bytes.length);\n      return isLE ? bytesToNumberLE(bytes) : bytesToNumberBE(bytes);\n    },\n    // TODO: we don't need it here, move out to separate fn\n    invertBatch: (lst) => FpInvertBatch(f, lst),\n    // We can't move this out because Fp6, Fp12 implement it\n    // and it's unclear what to return in there.\n    cmov: (a, b, c) => (c ? b : a),\n  } as FpField);\n  return Object.freeze(f);\n}\n\nexport function FpSqrtOdd<T>(Fp: IField<T>, elm: T): T {\n  if (!Fp.isOdd) throw new Error(\"Field doesn't have isOdd\");\n  const root = Fp.sqrt(elm);\n  return Fp.isOdd(root) ? root : Fp.neg(root);\n}\n\nexport function FpSqrtEven<T>(Fp: IField<T>, elm: T): T {\n  if (!Fp.isOdd) throw new Error(\"Field doesn't have isOdd\");\n  const root = Fp.sqrt(elm);\n  return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use `mapKeyToField` instead\n */\nexport function hashToPrivateScalar(\n  hash: string | Uint8Array,\n  groupOrder: bigint,\n  isLE = false\n): bigint {\n  hash = ensureBytes('privateHash', hash);\n  const hashLen = hash.length;\n  const minLen = nLength(groupOrder).nByteLength + 8;\n  if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n    throw new Error(\n      'hashToPrivateScalar: expected ' + minLen + '-1024 bytes of input, got ' + hashLen\n    );\n  const num = isLE ? bytesToNumberLE(hash) : bytesToNumberBE(hash);\n  return mod(num, groupOrder - _1n) + _1n;\n}\n\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nexport function getFieldBytesLength(fieldOrder: bigint): number {\n  if (typeof fieldOrder !== 'bigint') throw new Error('field order must be bigint');\n  const bitLength = fieldOrder.toString(2).length;\n  return Math.ceil(bitLength / 8);\n}\n\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nexport function getMinHashLength(fieldOrder: bigint): number {\n  const length = getFieldBytesLength(fieldOrder);\n  return length + Math.ceil(length / 2);\n}\n\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nexport function mapHashToField(key: Uint8Array, fieldOrder: bigint, isLE = false): Uint8Array {\n  const len = key.length;\n  const fieldLen = getFieldBytesLength(fieldOrder);\n  const minLen = getMinHashLength(fieldOrder);\n  // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n  if (len < 16 || len < minLen || len > 1024)\n    throw new Error('expected ' + minLen + '-1024 bytes of input, got ' + len);\n  const num = isLE ? bytesToNumberLE(key) : bytesToNumberBE(key);\n  // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n  const reduced = mod(num, fieldOrder - _1n) + _1n;\n  return isLE ? numberToBytesLE(reduced, fieldLen) : numberToBytesBE(reduced, fieldLen);\n}\n", "/**\n * Methods for elliptic curve multiplication by scalars.\n * Contains wNAF, pippenger\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { bitLen, bitMask, validateObject } from '../utils.ts';\nimport { Field, FpInvertBatch, type IField, nLength, validateField } from './modular.ts';\n\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\n\nexport type AffinePoint<T> = {\n  x: T;\n  y: T;\n} & { z?: never; t?: never };\n\nexport interface Group<T extends Group<T>> {\n  double(): T;\n  negate(): T;\n  add(other: T): T;\n  subtract(other: T): T;\n  equals(other: T): boolean;\n  multiply(scalar: bigint): T;\n  toAffine?(invertedZ?: any): AffinePoint<any>;\n}\n\nexport type GroupConstructor<T> = {\n  BASE: T;\n  ZERO: T;\n};\nexport type ExtendedGroupConstructor<T> = GroupConstructor<T> & {\n  Fp: <PERSON>ield<any>;\n  Fn: IField<bigint>;\n  fromAffine(ap: AffinePoint<any>): T;\n};\nexport type Mapper<T> = (i: T[]) => T[];\n\nexport function negateCt<T extends Group<T>>(condition: boolean, item: T): T {\n  const neg = item.negate();\n  return condition ? neg : item;\n}\n\n/**\n * Takes a bunch of Projective Points but executes only one\n * inversion on all of them. Inversion is very slow operation,\n * so this improves performance massively.\n * Optimization: converts a list of projective points to a list of identical points with Z=1.\n */\nexport function normalizeZ<T>(\n  c: ExtendedGroupConstructor<T>,\n  property: 'pz' | 'ez',\n  points: T[]\n): T[] {\n  const getz = property === 'pz' ? (p: any) => p.pz : (p: any) => p.ez;\n  const toInv = FpInvertBatch(c.Fp, points.map(getz));\n  // @ts-ignore\n  const affined = points.map((p, i) => p.toAffine(toInv[i]));\n  return affined.map(c.fromAffine);\n}\n\nfunction validateW(W: number, bits: number) {\n  if (!Number.isSafeInteger(W) || W <= 0 || W > bits)\n    throw new Error('invalid window size, expected [1..' + bits + '], got W=' + W);\n}\n\n/** Internal wNAF opts for specific W and scalarBits */\nexport type WOpts = {\n  windows: number;\n  windowSize: number;\n  mask: bigint;\n  maxNumber: number;\n  shiftBy: bigint;\n};\n\nfunction calcWOpts(W: number, scalarBits: number): WOpts {\n  validateW(W, scalarBits);\n  const windows = Math.ceil(scalarBits / W) + 1; // W=8 33. Not 32, because we skip zero\n  const windowSize = 2 ** (W - 1); // W=8 128. Not 256, because we skip zero\n  const maxNumber = 2 ** W; // W=8 256\n  const mask = bitMask(W); // W=8 255 == mask 0b11111111\n  const shiftBy = BigInt(W); // W=8 8\n  return { windows, windowSize, mask, maxNumber, shiftBy };\n}\n\nfunction calcOffsets(n: bigint, window: number, wOpts: WOpts) {\n  const { windowSize, mask, maxNumber, shiftBy } = wOpts;\n  let wbits = Number(n & mask); // extract W bits.\n  let nextN = n >> shiftBy; // shift number by W bits.\n\n  // What actually happens here:\n  // const highestBit = Number(mask ^ (mask >> 1n));\n  // let wbits2 = wbits - 1; // skip zero\n  // if (wbits2 & highestBit) { wbits2 ^= Number(mask); // (~);\n\n  // split if bits > max: +224 => 256-32\n  if (wbits > windowSize) {\n    // we skip zero, which means instead of `>= size-1`, we do `> size`\n    wbits -= maxNumber; // -32, can be maxNumber - wbits, but then we need to set isNeg here.\n    nextN += _1n; // +256 (carry)\n  }\n  const offsetStart = window * windowSize;\n  const offset = offsetStart + Math.abs(wbits) - 1; // -1 because we skip zero\n  const isZero = wbits === 0; // is current window slice a 0?\n  const isNeg = wbits < 0; // is current window slice negative?\n  const isNegF = window % 2 !== 0; // fake random statement for noise\n  const offsetF = offsetStart; // fake offset for noise\n  return { nextN, offset, isZero, isNeg, isNegF, offsetF };\n}\n\nfunction validateMSMPoints(points: any[], c: any) {\n  if (!Array.isArray(points)) throw new Error('array expected');\n  points.forEach((p, i) => {\n    if (!(p instanceof c)) throw new Error('invalid point at index ' + i);\n  });\n}\nfunction validateMSMScalars(scalars: any[], field: any) {\n  if (!Array.isArray(scalars)) throw new Error('array of scalars expected');\n  scalars.forEach((s, i) => {\n    if (!field.isValid(s)) throw new Error('invalid scalar at index ' + i);\n  });\n}\n\n// Since points in different groups cannot be equal (different object constructor),\n// we can have single place to store precomputes.\n// Allows to make points frozen / immutable.\nconst pointPrecomputes = new WeakMap<any, any[]>();\nconst pointWindowSizes = new WeakMap<any, number>();\n\nfunction getW(P: any): number {\n  return pointWindowSizes.get(P) || 1;\n}\n\nfunction assert0(n: bigint): void {\n  if (n !== _0n) throw new Error('invalid wNAF');\n}\n\nexport type IWNAF<T extends Group<T>> = {\n  constTimeNegate: <T extends Group<T>>(condition: boolean, item: T) => T;\n  hasPrecomputes(elm: T): boolean;\n  unsafeLadder(elm: T, n: bigint, p?: T): T;\n  precomputeWindow(elm: T, W: number): Group<T>[];\n  getPrecomputes(W: number, P: T, transform?: Mapper<T>): T[];\n  wNAF(W: number, precomputes: T[], n: bigint): { p: T; f: T };\n  wNAFUnsafe(W: number, precomputes: T[], n: bigint, acc?: T): T;\n  wNAFCached(P: T, n: bigint, transform?: Mapper<T>): { p: T; f: T };\n  wNAFCachedUnsafe(P: T, n: bigint, transform?: Mapper<T>, prev?: T): T;\n  setWindowSize(P: T, W: number): void;\n};\n\n/**\n * Elliptic curve multiplication of Point by scalar. Fragile.\n * Scalars should always be less than curve order: this should be checked inside of a curve itself.\n * Creates precomputation tables for fast multiplication:\n * - private scalar is split by fixed size windows of W bits\n * - every window point is collected from window's table & added to accumulator\n * - since windows are different, same point inside tables won't be accessed more than once per calc\n * - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n * - +1 window is neccessary for wNAF\n * - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n *\n * @todo Research returning 2d JS array of windows, instead of a single window.\n * This would allow windows to be in different memory locations\n */\nexport function wNAF<T extends Group<T>>(c: GroupConstructor<T>, bits: number): IWNAF<T> {\n  return {\n    constTimeNegate: negateCt,\n\n    hasPrecomputes(elm: T) {\n      return getW(elm) !== 1;\n    },\n\n    // non-const time multiplication ladder\n    unsafeLadder(elm: T, n: bigint, p = c.ZERO) {\n      let d: T = elm;\n      while (n > _0n) {\n        if (n & _1n) p = p.add(d);\n        d = d.double();\n        n >>= _1n;\n      }\n      return p;\n    },\n\n    /**\n     * Creates a wNAF precomputation window. Used for caching.\n     * Default window size is set by `utils.precompute()` and is equal to 8.\n     * Number of precomputed points depends on the curve size:\n     * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n     * - 𝑊 is the window size\n     * - 𝑛 is the bitlength of the curve order.\n     * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n     * @param elm Point instance\n     * @param W window size\n     * @returns precomputed point tables flattened to a single array\n     */\n    precomputeWindow(elm: T, W: number): Group<T>[] {\n      const { windows, windowSize } = calcWOpts(W, bits);\n      const points: T[] = [];\n      let p: T = elm;\n      let base = p;\n      for (let window = 0; window < windows; window++) {\n        base = p;\n        points.push(base);\n        // i=1, bc we skip 0\n        for (let i = 1; i < windowSize; i++) {\n          base = base.add(p);\n          points.push(base);\n        }\n        p = base.double();\n      }\n      return points;\n    },\n\n    /**\n     * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n     * @param W window size\n     * @param precomputes precomputed tables\n     * @param n scalar (we don't check here, but should be less than curve order)\n     * @returns real and fake (for const-time) points\n     */\n    wNAF(W: number, precomputes: T[], n: bigint): { p: T; f: T } {\n      // Smaller version:\n      // https://github.com/paulmillr/noble-secp256k1/blob/47cb1669b6e506ad66b35fe7d76132ae97465da2/index.ts#L502-L541\n      // TODO: check the scalar is less than group order?\n      // wNAF behavior is undefined otherwise. But have to carefully remove\n      // other checks before wNAF. ORDER == bits here.\n      // Accumulators\n      let p = c.ZERO;\n      let f = c.BASE;\n      // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n      // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n      // there is negate now: it is possible that negated element from low value\n      // would be the same as high element, which will create carry into next window.\n      // It's not obvious how this can fail, but still worth investigating later.\n      const wo = calcWOpts(W, bits);\n      for (let window = 0; window < wo.windows; window++) {\n        // (n === _0n) is handled and not early-exited. isEven and offsetF are used for noise\n        const { nextN, offset, isZero, isNeg, isNegF, offsetF } = calcOffsets(n, window, wo);\n        n = nextN;\n        if (isZero) {\n          // bits are 0: add garbage to fake point\n          // Important part for const-time getPublicKey: add random \"noise\" point to f.\n          f = f.add(negateCt(isNegF, precomputes[offsetF]));\n        } else {\n          // bits are 1: add to result point\n          p = p.add(negateCt(isNeg, precomputes[offset]));\n        }\n      }\n      assert0(n);\n      // Return both real and fake points: JIT won't eliminate f.\n      // At this point there is a way to F be infinity-point even if p is not,\n      // which makes it less const-time: around 1 bigint multiply.\n      return { p, f };\n    },\n\n    /**\n     * Implements ec unsafe (non const-time) multiplication using precomputed tables and w-ary non-adjacent form.\n     * @param W window size\n     * @param precomputes precomputed tables\n     * @param n scalar (we don't check here, but should be less than curve order)\n     * @param acc accumulator point to add result of multiplication\n     * @returns point\n     */\n    wNAFUnsafe(W: number, precomputes: T[], n: bigint, acc: T = c.ZERO): T {\n      const wo = calcWOpts(W, bits);\n      for (let window = 0; window < wo.windows; window++) {\n        if (n === _0n) break; // Early-exit, skip 0 value\n        const { nextN, offset, isZero, isNeg } = calcOffsets(n, window, wo);\n        n = nextN;\n        if (isZero) {\n          // Window bits are 0: skip processing.\n          // Move to next window.\n          continue;\n        } else {\n          const item = precomputes[offset];\n          acc = acc.add(isNeg ? item.negate() : item); // Re-using acc allows to save adds in MSM\n        }\n      }\n      assert0(n);\n      return acc;\n    },\n\n    getPrecomputes(W: number, P: T, transform?: Mapper<T>): T[] {\n      // Calculate precomputes on a first run, reuse them after\n      let comp = pointPrecomputes.get(P);\n      if (!comp) {\n        comp = this.precomputeWindow(P, W) as T[];\n        if (W !== 1) {\n          // Doing transform outside of if brings 15% perf hit\n          if (typeof transform === 'function') comp = transform(comp);\n          pointPrecomputes.set(P, comp);\n        }\n      }\n      return comp;\n    },\n\n    wNAFCached(P: T, n: bigint, transform?: Mapper<T>): { p: T; f: T } {\n      const W = getW(P);\n      return this.wNAF(W, this.getPrecomputes(W, P, transform), n);\n    },\n\n    wNAFCachedUnsafe(P: T, n: bigint, transform?: Mapper<T>, prev?: T): T {\n      const W = getW(P);\n      if (W === 1) return this.unsafeLadder(P, n, prev); // For W=1 ladder is ~x2 faster\n      return this.wNAFUnsafe(W, this.getPrecomputes(W, P, transform), n, prev);\n    },\n\n    // We calculate precomputes for elliptic curve point multiplication\n    // using windowed method. This specifies window size and\n    // stores precomputed values. Usually only base point would be precomputed.\n\n    setWindowSize(P: T, W: number) {\n      validateW(W, bits);\n      pointWindowSizes.set(P, W);\n      pointPrecomputes.delete(P);\n    },\n  };\n}\n\n/**\n * Endomorphism-specific multiplication for Koblitz curves.\n * Cost: 128 dbl, 0-256 adds.\n */\nexport function mulEndoUnsafe<T extends Group<T>>(\n  c: GroupConstructor<T>,\n  point: T,\n  k1: bigint,\n  k2: bigint\n): { p1: T; p2: T } {\n  let acc = point;\n  let p1 = c.ZERO;\n  let p2 = c.ZERO;\n  while (k1 > _0n || k2 > _0n) {\n    if (k1 & _1n) p1 = p1.add(acc);\n    if (k2 & _1n) p2 = p2.add(acc);\n    acc = acc.double();\n    k1 >>= _1n;\n    k2 >>= _1n;\n  }\n  return { p1, p2 };\n}\n\n/**\n * Pippenger algorithm for multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * 30x faster vs naive addition on L=4096, 10x faster than precomputes.\n * For N=254bit, L=1, it does: 1024 ADD + 254 DBL. For L=5: 1536 ADD + 254 DBL.\n * Algorithmically constant-time (for same L), even when 1 point + scalar, or when scalar = 0.\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @param scalars array of L scalars (aka private keys / bigints)\n */\nexport function pippenger<T extends Group<T>>(\n  c: GroupConstructor<T>,\n  fieldN: IField<bigint>,\n  points: T[],\n  scalars: bigint[]\n): T {\n  // If we split scalars by some window (let's say 8 bits), every chunk will only\n  // take 256 buckets even if there are 4096 scalars, also re-uses double.\n  // TODO:\n  // - https://eprint.iacr.org/2024/750.pdf\n  // - https://tches.iacr.org/index.php/TCHES/article/view/10287\n  // 0 is accepted in scalars\n  validateMSMPoints(points, c);\n  validateMSMScalars(scalars, fieldN);\n  const plength = points.length;\n  const slength = scalars.length;\n  if (plength !== slength) throw new Error('arrays of points and scalars must have equal length');\n  // if (plength === 0) throw new Error('array must be of length >= 2');\n  const zero = c.ZERO;\n  const wbits = bitLen(BigInt(plength));\n  let windowSize = 1; // bits\n  if (wbits > 12) windowSize = wbits - 3;\n  else if (wbits > 4) windowSize = wbits - 2;\n  else if (wbits > 0) windowSize = 2;\n  const MASK = bitMask(windowSize);\n  const buckets = new Array(Number(MASK) + 1).fill(zero); // +1 for zero array\n  const lastBits = Math.floor((fieldN.BITS - 1) / windowSize) * windowSize;\n  let sum = zero;\n  for (let i = lastBits; i >= 0; i -= windowSize) {\n    buckets.fill(zero);\n    for (let j = 0; j < slength; j++) {\n      const scalar = scalars[j];\n      const wbits = Number((scalar >> BigInt(i)) & MASK);\n      buckets[wbits] = buckets[wbits].add(points[j]);\n    }\n    let resI = zero; // not using this will do small speed-up, but will lose ct\n    // Skip first bucket, because it is zero\n    for (let j = buckets.length - 1, sumI = zero; j > 0; j--) {\n      sumI = sumI.add(buckets[j]);\n      resI = resI.add(sumI);\n    }\n    sum = sum.add(resI);\n    if (i !== 0) for (let j = 0; j < windowSize; j++) sum = sum.double();\n  }\n  return sum as T;\n}\n/**\n * Precomputed multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @returns function which multiplies points with scaars\n */\nexport function precomputeMSMUnsafe<T extends Group<T>>(\n  c: GroupConstructor<T>,\n  fieldN: IField<bigint>,\n  points: T[],\n  windowSize: number\n): (scalars: bigint[]) => T {\n  /**\n   * Performance Analysis of Window-based Precomputation\n   *\n   * Base Case (256-bit scalar, 8-bit window):\n   * - Standard precomputation requires:\n   *   - 31 additions per scalar × 256 scalars = 7,936 ops\n   *   - Plus 255 summary additions = 8,191 total ops\n   *   Note: Summary additions can be optimized via accumulator\n   *\n   * Chunked Precomputation Analysis:\n   * - Using 32 chunks requires:\n   *   - 255 additions per chunk\n   *   - 256 doublings\n   *   - Total: (255 × 32) + 256 = 8,416 ops\n   *\n   * Memory Usage Comparison:\n   * Window Size | Standard Points | Chunked Points\n   * ------------|-----------------|---------------\n   *     4-bit   |     520         |      15\n   *     8-bit   |    4,224        |     255\n   *    10-bit   |   13,824        |   1,023\n   *    16-bit   |  557,056        |  65,535\n   *\n   * Key Advantages:\n   * 1. Enables larger window sizes due to reduced memory overhead\n   * 2. More efficient for smaller scalar counts:\n   *    - 16 chunks: (16 × 255) + 256 = 4,336 ops\n   *    - ~2x faster than standard 8,191 ops\n   *\n   * Limitations:\n   * - Not suitable for plain precomputes (requires 256 constant doublings)\n   * - Performance degrades with larger scalar counts:\n   *   - Optimal for ~256 scalars\n   *   - Less efficient for 4096+ scalars (Pippenger preferred)\n   */\n  validateW(windowSize, fieldN.BITS);\n  validateMSMPoints(points, c);\n  const zero = c.ZERO;\n  const tableSize = 2 ** windowSize - 1; // table size (without zero)\n  const chunks = Math.ceil(fieldN.BITS / windowSize); // chunks of item\n  const MASK = bitMask(windowSize);\n  const tables = points.map((p: T) => {\n    const res = [];\n    for (let i = 0, acc = p; i < tableSize; i++) {\n      res.push(acc);\n      acc = acc.add(p);\n    }\n    return res;\n  });\n  return (scalars: bigint[]): T => {\n    validateMSMScalars(scalars, fieldN);\n    if (scalars.length > points.length)\n      throw new Error('array of scalars must be smaller than array of points');\n    let res = zero;\n    for (let i = 0; i < chunks; i++) {\n      // No need to double if accumulator is still zero.\n      if (res !== zero) for (let j = 0; j < windowSize; j++) res = res.double();\n      const shiftBy = BigInt(chunks * windowSize - (i + 1) * windowSize);\n      for (let j = 0; j < scalars.length; j++) {\n        const n = scalars[j];\n        const curr = Number((n >> shiftBy) & MASK);\n        if (!curr) continue; // skip zero scalars chunks\n        res = res.add(tables[j][curr - 1]);\n      }\n    }\n    return res;\n  };\n}\n\n/**\n * Generic BasicCurve interface: works even for polynomial fields (BLS): P, n, h would be ok.\n * Though generator can be different (Fp2 / Fp6 for BLS).\n */\nexport type BasicCurve<T> = {\n  Fp: IField<T>; // Field over which we'll do calculations (Fp)\n  n: bigint; // Curve order, total count of valid points in the field\n  nBitLength?: number; // bit length of curve order\n  nByteLength?: number; // byte length of curve order\n  h: bigint; // cofactor. we can assign default=1, but users will just ignore it w/o validation\n  hEff?: bigint; // Number to multiply to clear cofactor\n  Gx: T; // base point X coordinate\n  Gy: T; // base point Y coordinate\n  allowInfinityPoint?: boolean; // bls12-381 requires it. ZERO point is valid, but invalid pubkey\n};\n\n// TODO: remove\n/** @deprecated */\nexport function validateBasic<FP, T>(\n  curve: BasicCurve<FP> & T\n): Readonly<\n  {\n    readonly nBitLength: number;\n    readonly nByteLength: number;\n  } & BasicCurve<FP> &\n    T & {\n      p: bigint;\n    }\n> {\n  validateField(curve.Fp);\n  validateObject(\n    curve,\n    {\n      n: 'bigint',\n      h: 'bigint',\n      Gx: 'field',\n      Gy: 'field',\n    },\n    {\n      nBitLength: 'isSafeInteger',\n      nByteLength: 'isSafeInteger',\n    }\n  );\n  // Set defaults\n  return Object.freeze({\n    ...nLength(curve.n, curve.nBitLength),\n    ...curve,\n    ...{ p: curve.Fp.ORDER },\n  } as const);\n}\n\nexport type ValidCurveParams<T> = {\n  a: T;\n  p: bigint;\n  n: bigint;\n  h: bigint;\n  Gx: T;\n  Gy: T;\n} & ({ b: T } | { d: T });\n\nfunction createField<T>(order: bigint, field?: IField<T>): IField<T> {\n  if (field) {\n    if (field.ORDER !== order) throw new Error('Field.ORDER must match order: Fp == p, Fn == n');\n    validateField(field);\n    return field;\n  } else {\n    return Field(order) as unknown as IField<T>;\n  }\n}\nexport type FpFn<T> = { Fp: IField<T>; Fn: IField<bigint> };\n/** Validates CURVE opts and creates fields */\nexport function _createCurveFields<T>(\n  type: 'weierstrass' | 'edwards',\n  CURVE: ValidCurveParams<T>,\n  curveOpts: Partial<FpFn<T>> = {}\n): FpFn<T> {\n  if (!CURVE || typeof CURVE !== 'object') throw new Error(`expected valid ${type} CURVE object`);\n  for (const p of ['p', 'n', 'h'] as const) {\n    const val = CURVE[p];\n    if (!(typeof val === 'bigint' && val > _0n))\n      throw new Error(`CURVE.${p} must be positive bigint`);\n  }\n  const Fp = createField(CURVE.p, curveOpts.Fp);\n  const Fn = createField(CURVE.n, curveOpts.Fn);\n  const _b: 'b' | 'd' = type === 'weierstrass' ? 'b' : 'd';\n  const params = ['Gx', 'Gy', 'a', _b] as const;\n  for (const p of params) {\n    // @ts-ignore\n    if (!Fp.isValid(CURVE[p]))\n      throw new Error(`CURVE.${p} must be valid field element of CURVE.Fp`);\n  }\n  return { Fp, Fn };\n}\n", "/**\n * Short <PERSON> curve methods. The formula is: y² = x³ + ax + b.\n *\n * ### Design rationale for types\n *\n * * Interaction between classes from different curves should fail:\n *   `k256.Point.BASE.add(p256.Point.BASE)`\n * * For this purpose we want to use `instanceof` operator, which is fast and works during runtime\n * * Different calls of `curve()` would return different classes -\n *   `curve(params) !== curve(params)`: if somebody decided to monkey-patch their curve,\n *   it won't affect others\n *\n * TypeScript can't infer types for classes created inside a function. Classes is one instance\n * of nominative types in TypeScript and interfaces only check for shape, so it's hard to create\n * unique type for every function call.\n *\n * We can use generic types via some param, like curve opts, but that would:\n *     1. Enable interaction between `curve(params)` and `curve(params)` (curves of same params)\n *     which is hard to debug.\n *     2. Params can be generic and we can't enforce them to be constant value:\n *     if somebody creates curve from non-constant params,\n *     it would be allowed to interact with other curves with non-constant params\n *\n * @todo https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-7.html#unique-symbol\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { hmac } from '@noble/hashes/hmac.js';\nimport {\n  _validateObject,\n  abool,\n  abytes,\n  aInRange,\n  bitMask,\n  bytesToHex,\n  bytesToNumberBE,\n  concatBytes,\n  createHmacDrbg,\n  ensureBytes,\n  hexToBytes,\n  inRange,\n  isBytes,\n  memoized,\n  numberToHexUnpadded,\n  randomBytes,\n  type CHash,\n  type Hex,\n  type PrivKey,\n} from '../utils.ts';\nimport {\n  _createCurveFields,\n  mulEndoUnsafe,\n  negateCt,\n  normalizeZ,\n  pippenger,\n  wNAF,\n  type AffinePoint,\n  type BasicCurve,\n  type Group,\n  type GroupConstructor,\n} from './curve.ts';\nimport {\n  Field,\n  FpInvertBatch,\n  getMinHashLength,\n  mapHashToField,\n  validateField,\n  type IField,\n  type NLength,\n} from './modular.ts';\n\nexport type { AffinePoint };\nexport type HmacFnSync = (key: Uint8Array, ...messages: Uint8Array[]) => Uint8Array;\n/**\n * When Weierstrass curve has `a=0`, it becomes Koblitz curve.\n * Koblitz curves allow using **efficiently-computable GLV endomorphism ψ**.\n * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n *\n * Endomorphism consists of beta, lambda and splitScalar:\n *\n * 1. GLV endomorphism ψ transforms a point: `P = (x, y) ↦ ψ(P) = (β·x mod p, y)`\n * 2. GLV scalar decomposition transforms a scalar: `k ≡ k₁ + k₂·λ (mod n)`\n * 3. Then these are combined: `k·P = k₁·P + k₂·ψ(P)`\n * 4. Two 128-bit point-by-scalar multiplications + one point addition is faster than\n *    one 256-bit multiplication.\n *\n * where\n * * beta: β ∈ Fₚ with β³ = 1, β ≠ 1\n * * lambda: λ ∈ Fₙ with λ³ = 1, λ ≠ 1\n * * splitScalar decomposes k ↦ k₁, k₂, by using reduced basis vectors.\n *   Gauss lattice reduction calculates them from initial basis vectors `(n, 0), (-λ, 0)`\n *\n * Check out `test/misc/endomorphism.js` and\n * [gist](https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066).\n */\nexport type EndomorphismOpts = {\n  beta: bigint;\n  splitScalar: (k: bigint) => { k1neg: boolean; k1: bigint; k2neg: boolean; k2: bigint };\n};\nexport type BasicWCurve<T> = BasicCurve<T> & {\n  // Params: a, b\n  a: T;\n  b: T;\n\n  // Optional params\n  allowedPrivateKeyLengths?: readonly number[]; // for P521\n  wrapPrivateKey?: boolean; // bls12-381 requires mod(n) instead of rejecting keys >= n\n  endo?: EndomorphismOpts;\n  // When a cofactor != 1, there can be an effective methods to:\n  // 1. Determine whether a point is torsion-free\n  isTorsionFree?: (c: ProjConstructor<T>, point: ProjPointType<T>) => boolean;\n  // 2. Clear torsion component\n  clearCofactor?: (c: ProjConstructor<T>, point: ProjPointType<T>) => ProjPointType<T>;\n};\n\nexport type Entropy = Hex | boolean;\nexport type SignOpts = { lowS?: boolean; extraEntropy?: Entropy; prehash?: boolean };\nexport type VerOpts = {\n  lowS?: boolean;\n  prehash?: boolean;\n  format?: 'compact' | 'der' | 'js' | undefined;\n};\n\nfunction validateSigVerOpts(opts: SignOpts | VerOpts) {\n  if (opts.lowS !== undefined) abool('lowS', opts.lowS);\n  if (opts.prehash !== undefined) abool('prehash', opts.prehash);\n}\n\n/** Instance methods for 3D XYZ points. */\nexport interface ProjPointType<T> extends Group<ProjPointType<T>> {\n  /** projective x coordinate. Note: different from .x */\n  readonly px: T;\n  /** projective y coordinate. Note: different from .y */\n  readonly py: T;\n  /** projective z coordinate */\n  readonly pz: T;\n  /** affine x coordinate */\n  get x(): T;\n  /** affine y coordinate */\n  get y(): T;\n  assertValidity(): void;\n  clearCofactor(): ProjPointType<T>;\n  is0(): boolean;\n  isTorsionFree(): boolean;\n  multiplyUnsafe(scalar: bigint): ProjPointType<T>;\n  /**\n   * Massively speeds up `p.multiply(n)` by using wnaf precompute tables (caching).\n   * Table generation takes 30MB of ram and 10ms on high-end CPU, but may take\n   * much longer on slow devices.\n   * Actual generation will happen on first call of `.multiply()`.\n   * By default, BASE point is precomputed.\n   * @param windowSize - table window size\n   * @param isLazy - (default true) allows to defer generation\n   */\n  precompute(windowSize?: number, isLazy?: boolean): ProjPointType<T>;\n\n  /** Converts 3D XYZ projective point to 2D xy affine coordinates */\n  toAffine(invertedZ?: T): AffinePoint<T>;\n  /** Encodes point using IEEE P1363 (DER) encoding. First byte is 2/3/4. Default = isCompressed. */\n  toBytes(isCompressed?: boolean): Uint8Array;\n  toHex(isCompressed?: boolean): string;\n\n  /** @deprecated use `toBytes` */\n  toRawBytes(isCompressed?: boolean): Uint8Array;\n  /** @deprecated use `multiplyUnsafe` */\n  multiplyAndAddUnsafe(Q: ProjPointType<T>, a: bigint, b: bigint): ProjPointType<T> | undefined;\n  /** @deprecated use `p.y % 2n === 0n` */\n  hasEvenY(): boolean;\n  /** @deprecated use `p.precompute(windowSize)` */\n  _setWindowSize(windowSize: number): void;\n}\n\n/** Static methods for 3D XYZ points. */\nexport interface ProjConstructor<T> extends GroupConstructor<ProjPointType<T>> {\n  Fp: IField<T>;\n  Fn: IField<bigint>;\n  /** Does NOT validate if the point is valid. Use `.assertValidity()`. */\n  new (x: T, y: T, z: T): ProjPointType<T>;\n  /** Does NOT validate if the point is valid. Use `.assertValidity()`. */\n  fromAffine(p: AffinePoint<T>): ProjPointType<T>;\n  fromBytes(encodedPoint: Uint8Array): ProjPointType<T>;\n  fromHex(hex: Hex): ProjPointType<T>;\n  fromPrivateKey(privateKey: PrivKey): ProjPointType<T>;\n  normalizeZ(points: ProjPointType<T>[]): ProjPointType<T>[];\n  msm(points: ProjPointType<T>[], scalars: bigint[]): ProjPointType<T>;\n}\n\nexport type CurvePointsType<T> = BasicWCurve<T> & {\n  fromBytes?: (bytes: Uint8Array) => AffinePoint<T>;\n  toBytes?: (c: ProjConstructor<T>, point: ProjPointType<T>, isCompressed: boolean) => Uint8Array;\n};\n\n// LegacyWeierstrassOpts\nexport type CurvePointsTypeWithLength<T> = Readonly<CurvePointsType<T> & Partial<NLength>>;\n\n// LegacyWeierstrass\nexport type CurvePointsRes<T> = {\n  /** @deprecated import individual CURVE params */\n  CURVE: CurvePointsType<T>;\n  Point: ProjConstructor<T>;\n  /** @deprecated use `Point` */\n  ProjectivePoint: ProjConstructor<T>;\n  /** @deprecated */\n  normPrivateKeyToScalar: (key: PrivKey) => bigint;\n  /** @deprecated */\n  weierstrassEquation: (x: T) => T;\n  /** @deprecated use `Point.Fn.isValidNot0(num)` */\n  isWithinCurveOrder: (num: bigint) => boolean;\n};\n\n// Aliases to legacy types\n// export type CurveType = LegacyECDSAOpts;\n// export type CurveFn = LegacyECDSA;\n// export type CurvePointsRes<T> = LegacyWeierstrass<T>;\n// export type CurvePointsType<T> = LegacyWeierstrassOpts<T>;\n// export type CurvePointsTypeWithLength<T> = LegacyWeierstrassOpts<T>;\n// export type BasicWCurve<T> = LegacyWeierstrassOpts<T>;\n\n/**\n * Weierstrass curve options.\n *\n * * p: prime characteristic (order) of finite field, in which arithmetics is done\n * * n: order of prime subgroup a.k.a total amount of valid curve points\n * * h: cofactor, usually 1. h*n is group order; n is subgroup order\n * * a: formula param, must be in field of p\n * * b: formula param, must be in field of p\n * * Gx: x coordinate of generator point a.k.a. base point\n * * Gy: y coordinate of generator point\n */\nexport type WeierstrassOpts<T> = Readonly<{\n  p: bigint;\n  n: bigint;\n  h: bigint;\n  a: T;\n  b: T;\n  Gx: T;\n  Gy: T;\n}>;\n\n// When a cofactor != 1, there can be an effective methods to:\n// 1. Determine whether a point is torsion-free\n// 2. Clear torsion component\n// wrapPrivateKey: bls12-381 requires mod(n) instead of rejecting keys >= n\nexport type WeierstrassExtraOpts<T> = Partial<{\n  Fp: IField<T>;\n  Fn: IField<bigint>;\n  // TODO: remove\n  allowedPrivateKeyLengths: readonly number[]; // for P521\n  allowInfinityPoint: boolean;\n  endo: EndomorphismOpts;\n  wrapPrivateKey: boolean;\n  isTorsionFree: (c: ProjConstructor<T>, point: ProjPointType<T>) => boolean;\n  clearCofactor: (c: ProjConstructor<T>, point: ProjPointType<T>) => ProjPointType<T>;\n  fromBytes: (bytes: Uint8Array) => AffinePoint<T>;\n  toBytes: (c: ProjConstructor<T>, point: ProjPointType<T>, isCompressed: boolean) => Uint8Array;\n}>;\n\n/**\n * Options for ECDSA signatures over a Weierstrass curve.\n */\nexport type ECDSAOpts = {\n  hash: CHash;\n  hmac?: HmacFnSync;\n  randomBytes?: (bytesLength?: number) => Uint8Array;\n  lowS?: boolean;\n  bits2int?: (bytes: Uint8Array) => bigint;\n  bits2int_modN?: (bytes: Uint8Array) => bigint;\n};\n\n/** ECDSA is only supported for prime fields, not Fp2 (extension fields). */\nexport interface ECDSA {\n  getPublicKey: (privateKey: PrivKey, isCompressed?: boolean) => Uint8Array;\n  getSharedSecret: (privateA: PrivKey, publicB: Hex, isCompressed?: boolean) => Uint8Array;\n  sign: (msgHash: Hex, privKey: PrivKey, opts?: SignOpts) => RecoveredSignatureType;\n  verify: (signature: Hex | SignatureLike, msgHash: Hex, publicKey: Hex, opts?: VerOpts) => boolean;\n  Point: ProjConstructor<bigint>;\n  Signature: SignatureConstructor;\n  utils: {\n    isValidPrivateKey(privateKey: PrivKey): boolean;\n    randomPrivateKey: () => Uint8Array;\n    // TODO: deprecate those two\n    normPrivateKeyToScalar: (key: PrivKey) => bigint;\n    /** @deprecated */\n    precompute: (windowSize?: number, point?: ProjPointType<bigint>) => ProjPointType<bigint>;\n  };\n}\nexport class DERErr extends Error {\n  constructor(m = '') {\n    super(m);\n  }\n}\nexport type IDER = {\n  // asn.1 DER encoding utils\n  Err: typeof DERErr;\n  // Basic building block is TLV (Tag-Length-Value)\n  _tlv: {\n    encode: (tag: number, data: string) => string;\n    // v - value, l - left bytes (unparsed)\n    decode(tag: number, data: Uint8Array): { v: Uint8Array; l: Uint8Array };\n  };\n  // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n  // since we always use positive integers here. It must always be empty:\n  // - add zero byte if exists\n  // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n  _int: {\n    encode(num: bigint): string;\n    decode(data: Uint8Array): bigint;\n  };\n  toSig(hex: string | Uint8Array): { r: bigint; s: bigint };\n  hexFromSig(sig: { r: bigint; s: bigint }): string;\n};\n/**\n * ASN.1 DER encoding utilities. ASN is very complex & fragile. Format:\n *\n *     [0x30 (SEQUENCE), bytelength, 0x02 (INTEGER), intLength, R, 0x02 (INTEGER), intLength, S]\n *\n * Docs: https://letsencrypt.org/docs/a-warm-welcome-to-asn1-and-der/, https://luca.ntop.org/Teaching/Appunti/asn1.html\n */\nexport const DER: IDER = {\n  // asn.1 DER encoding utils\n  Err: DERErr,\n  // Basic building block is TLV (Tag-Length-Value)\n  _tlv: {\n    encode: (tag: number, data: string): string => {\n      const { Err: E } = DER;\n      if (tag < 0 || tag > 256) throw new E('tlv.encode: wrong tag');\n      if (data.length & 1) throw new E('tlv.encode: unpadded data');\n      const dataLen = data.length / 2;\n      const len = numberToHexUnpadded(dataLen);\n      if ((len.length / 2) & 0b1000_0000) throw new E('tlv.encode: long form length too big');\n      // length of length with long form flag\n      const lenLen = dataLen > 127 ? numberToHexUnpadded((len.length / 2) | 0b1000_0000) : '';\n      const t = numberToHexUnpadded(tag);\n      return t + lenLen + len + data;\n    },\n    // v - value, l - left bytes (unparsed)\n    decode(tag: number, data: Uint8Array): { v: Uint8Array; l: Uint8Array } {\n      const { Err: E } = DER;\n      let pos = 0;\n      if (tag < 0 || tag > 256) throw new E('tlv.encode: wrong tag');\n      if (data.length < 2 || data[pos++] !== tag) throw new E('tlv.decode: wrong tlv');\n      const first = data[pos++];\n      const isLong = !!(first & 0b1000_0000); // First bit of first length byte is flag for short/long form\n      let length = 0;\n      if (!isLong) length = first;\n      else {\n        // Long form: [longFlag(1bit), lengthLength(7bit), length (BE)]\n        const lenLen = first & 0b0111_1111;\n        if (!lenLen) throw new E('tlv.decode(long): indefinite length not supported');\n        if (lenLen > 4) throw new E('tlv.decode(long): byte length is too big'); // this will overflow u32 in js\n        const lengthBytes = data.subarray(pos, pos + lenLen);\n        if (lengthBytes.length !== lenLen) throw new E('tlv.decode: length bytes not complete');\n        if (lengthBytes[0] === 0) throw new E('tlv.decode(long): zero leftmost byte');\n        for (const b of lengthBytes) length = (length << 8) | b;\n        pos += lenLen;\n        if (length < 128) throw new E('tlv.decode(long): not minimal encoding');\n      }\n      const v = data.subarray(pos, pos + length);\n      if (v.length !== length) throw new E('tlv.decode: wrong value length');\n      return { v, l: data.subarray(pos + length) };\n    },\n  },\n  // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n  // since we always use positive integers here. It must always be empty:\n  // - add zero byte if exists\n  // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n  _int: {\n    encode(num: bigint): string {\n      const { Err: E } = DER;\n      if (num < _0n) throw new E('integer: negative integers are not allowed');\n      let hex = numberToHexUnpadded(num);\n      // Pad with zero byte if negative flag is present\n      if (Number.parseInt(hex[0], 16) & 0b1000) hex = '00' + hex;\n      if (hex.length & 1) throw new E('unexpected DER parsing assertion: unpadded hex');\n      return hex;\n    },\n    decode(data: Uint8Array): bigint {\n      const { Err: E } = DER;\n      if (data[0] & 0b1000_0000) throw new E('invalid signature integer: negative');\n      if (data[0] === 0x00 && !(data[1] & 0b1000_0000))\n        throw new E('invalid signature integer: unnecessary leading zero');\n      return bytesToNumberBE(data);\n    },\n  },\n  toSig(hex: string | Uint8Array): { r: bigint; s: bigint } {\n    // parse DER signature\n    const { Err: E, _int: int, _tlv: tlv } = DER;\n    const data = ensureBytes('signature', hex);\n    const { v: seqBytes, l: seqLeftBytes } = tlv.decode(0x30, data);\n    if (seqLeftBytes.length) throw new E('invalid signature: left bytes after parsing');\n    const { v: rBytes, l: rLeftBytes } = tlv.decode(0x02, seqBytes);\n    const { v: sBytes, l: sLeftBytes } = tlv.decode(0x02, rLeftBytes);\n    if (sLeftBytes.length) throw new E('invalid signature: left bytes after parsing');\n    return { r: int.decode(rBytes), s: int.decode(sBytes) };\n  },\n  hexFromSig(sig: { r: bigint; s: bigint }): string {\n    const { _tlv: tlv, _int: int } = DER;\n    const rs = tlv.encode(0x02, int.encode(sig.r));\n    const ss = tlv.encode(0x02, int.encode(sig.s));\n    const seq = rs + ss;\n    return tlv.encode(0x30, seq);\n  },\n};\n\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\n\n// TODO: remove\nexport function _legacyHelperEquat<T>(Fp: IField<T>, a: T, b: T): (x: T) => T {\n  /**\n   * y² = x³ + ax + b: Short weierstrass curve formula. Takes x, returns y².\n   * @returns y²\n   */\n  function weierstrassEquation(x: T): T {\n    const x2 = Fp.sqr(x); // x * x\n    const x3 = Fp.mul(x2, x); // x² * x\n    return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x³ + a * x + b\n  }\n  return weierstrassEquation;\n}\nexport function _legacyHelperNormPriv(\n  Fn: IField<bigint>,\n  allowedPrivateKeyLengths?: readonly number[],\n  wrapPrivateKey?: boolean\n): (key: PrivKey) => bigint {\n  const { BYTES: expected } = Fn;\n  // Validates if priv key is valid and converts it to bigint.\n  function normPrivateKeyToScalar(key: PrivKey): bigint {\n    let num: bigint;\n    if (typeof key === 'bigint') {\n      num = key;\n    } else {\n      let bytes = ensureBytes('private key', key);\n      if (allowedPrivateKeyLengths) {\n        if (!allowedPrivateKeyLengths.includes(bytes.length * 2))\n          throw new Error('invalid private key');\n        const padded = new Uint8Array(expected);\n        padded.set(bytes, padded.length - bytes.length);\n        bytes = padded;\n      }\n      try {\n        num = Fn.fromBytes(bytes);\n      } catch (error) {\n        throw new Error(\n          `invalid private key: expected ui8a of size ${expected}, got ${typeof key}`\n        );\n      }\n    }\n    if (wrapPrivateKey) num = Fn.create(num); // disabled by default, enabled for BLS\n    if (!Fn.isValidNot0(num)) throw new Error('invalid private key: out of range [1..N-1]');\n    return num;\n  }\n  return normPrivateKeyToScalar;\n}\n\nexport function weierstrassN<T>(\n  CURVE: WeierstrassOpts<T>,\n  curveOpts: WeierstrassExtraOpts<T> = {}\n): ProjConstructor<T> {\n  const { Fp, Fn } = _createCurveFields('weierstrass', CURVE, curveOpts);\n  const { h: cofactor, n: CURVE_ORDER } = CURVE;\n  _validateObject(\n    curveOpts,\n    {},\n    {\n      allowInfinityPoint: 'boolean',\n      clearCofactor: 'function',\n      isTorsionFree: 'function',\n      fromBytes: 'function',\n      toBytes: 'function',\n      endo: 'object',\n      wrapPrivateKey: 'boolean',\n    }\n  );\n\n  const { endo } = curveOpts;\n  if (endo) {\n    // validateObject(endo, { beta: 'bigint', splitScalar: 'function' });\n    if (\n      !Fp.is0(CURVE.a) ||\n      typeof endo.beta !== 'bigint' ||\n      typeof endo.splitScalar !== 'function'\n    ) {\n      throw new Error('invalid endo: expected \"beta\": bigint and \"splitScalar\": function');\n    }\n  }\n\n  function assertCompressionIsSupported() {\n    if (!Fp.isOdd) throw new Error('compression is not supported: Field does not have .isOdd()');\n  }\n\n  // Implements IEEE P1363 point encoding\n  function pointToBytes(\n    _c: ProjConstructor<T>,\n    point: ProjPointType<T>,\n    isCompressed: boolean\n  ): Uint8Array {\n    const { x, y } = point.toAffine();\n    const bx = Fp.toBytes(x);\n    abool('isCompressed', isCompressed);\n    if (isCompressed) {\n      assertCompressionIsSupported();\n      const hasEvenY = !Fp.isOdd!(y);\n      return concatBytes(pprefix(hasEvenY), bx);\n    } else {\n      return concatBytes(Uint8Array.of(0x04), bx, Fp.toBytes(y));\n    }\n  }\n  function pointFromBytes(bytes: Uint8Array) {\n    abytes(bytes);\n    const L = Fp.BYTES;\n    const LC = L + 1; // length compressed, e.g. 33 for 32-byte field\n    const LU = 2 * L + 1; // length uncompressed, e.g. 65 for 32-byte field\n    const length = bytes.length;\n    const head = bytes[0];\n    const tail = bytes.subarray(1);\n    // No actual validation is done here: use .assertValidity()\n    if (length === LC && (head === 0x02 || head === 0x03)) {\n      const x = Fp.fromBytes(tail);\n      if (!Fp.isValid(x)) throw new Error('bad point: is not on curve, wrong x');\n      const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n      let y: T;\n      try {\n        y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n      } catch (sqrtError) {\n        const err = sqrtError instanceof Error ? ': ' + sqrtError.message : '';\n        throw new Error('bad point: is not on curve, sqrt error' + err);\n      }\n      assertCompressionIsSupported();\n      const isYOdd = Fp.isOdd!(y); // (y & _1n) === _1n;\n      const isHeadOdd = (head & 1) === 1; // ECDSA-specific\n      if (isHeadOdd !== isYOdd) y = Fp.neg(y);\n      return { x, y };\n    } else if (length === LU && head === 0x04) {\n      // TODO: more checks\n      const x = Fp.fromBytes(tail.subarray(L * 0, L * 1));\n      const y = Fp.fromBytes(tail.subarray(L * 1, L * 2));\n      if (!isValidXY(x, y)) throw new Error('bad point: is not on curve');\n      return { x, y };\n    } else {\n      throw new Error(\n        `bad point: got length ${length}, expected compressed=${LC} or uncompressed=${LU}`\n      );\n    }\n  }\n\n  const toBytes = curveOpts.toBytes || pointToBytes;\n  const fromBytes = curveOpts.fromBytes || pointFromBytes;\n  const weierstrassEquation = _legacyHelperEquat(Fp, CURVE.a, CURVE.b);\n\n  // TODO: move top-level\n  /** Checks whether equation holds for given x, y: y² == x³ + ax + b */\n  function isValidXY(x: T, y: T): boolean {\n    const left = Fp.sqr(y); // y²\n    const right = weierstrassEquation(x); // x³ + ax + b\n    return Fp.eql(left, right);\n  }\n\n  // Validate whether the passed curve params are valid.\n  // Test 1: equation y² = x³ + ax + b should work for generator point.\n  if (!isValidXY(CURVE.Gx, CURVE.Gy)) throw new Error('bad curve params: generator point');\n\n  // Test 2: discriminant Δ part should be non-zero: 4a³ + 27b² != 0.\n  // Guarantees curve is genus-1, smooth (non-singular).\n  const _4a3 = Fp.mul(Fp.pow(CURVE.a, _3n), _4n);\n  const _27b2 = Fp.mul(Fp.sqr(CURVE.b), BigInt(27));\n  if (Fp.is0(Fp.add(_4a3, _27b2))) throw new Error('bad curve params: a or b');\n\n  /** Asserts coordinate is valid: 0 <= n < Fp.ORDER. */\n  function acoord(title: string, n: T, banZero = false) {\n    if (!Fp.isValid(n) || (banZero && Fp.is0(n))) throw new Error(`bad point coordinate ${title}`);\n    return n;\n  }\n\n  function aprjpoint(other: unknown) {\n    if (!(other instanceof Point)) throw new Error('ProjectivePoint expected');\n  }\n\n  // Memoized toAffine / validity check. They are heavy. Points are immutable.\n\n  // Converts Projective point to affine (x, y) coordinates.\n  // Can accept precomputed Z^-1 - for example, from invertBatch.\n  // (X, Y, Z) ∋ (x=X/Z, y=Y/Z)\n  const toAffineMemo = memoized((p: Point, iz?: T): AffinePoint<T> => {\n    const { px: x, py: y, pz: z } = p;\n    // Fast-path for normalized points\n    if (Fp.eql(z, Fp.ONE)) return { x, y };\n    const is0 = p.is0();\n    // If invZ was 0, we return zero point. However we still want to execute\n    // all operations, so we replace invZ with a random number, 1.\n    if (iz == null) iz = is0 ? Fp.ONE : Fp.inv(z);\n    const ax = Fp.mul(x, iz);\n    const ay = Fp.mul(y, iz);\n    const zz = Fp.mul(z, iz);\n    if (is0) return { x: Fp.ZERO, y: Fp.ZERO };\n    if (!Fp.eql(zz, Fp.ONE)) throw new Error('invZ was invalid');\n    return { x: ax, y: ay };\n  });\n  // NOTE: on exception this will crash 'cached' and no value will be set.\n  // Otherwise true will be return\n  const assertValidMemo = memoized((p: Point) => {\n    if (p.is0()) {\n      // (0, 1, 0) aka ZERO is invalid in most contexts.\n      // In BLS, ZERO can be serialized, so we allow it.\n      // (0, 0, 0) is invalid representation of ZERO.\n      if (curveOpts.allowInfinityPoint && !Fp.is0(p.py)) return;\n      throw new Error('bad point: ZERO');\n    }\n    // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n    const { x, y } = p.toAffine();\n    if (!Fp.isValid(x) || !Fp.isValid(y)) throw new Error('bad point: x or y not field elements');\n    if (!isValidXY(x, y)) throw new Error('bad point: equation left != right');\n    if (!p.isTorsionFree()) throw new Error('bad point: not in prime-order subgroup');\n    return true;\n  });\n\n  function finishEndo(\n    endoBeta: EndomorphismOpts['beta'],\n    k1p: Point,\n    k2p: Point,\n    k1neg: boolean,\n    k2neg: boolean\n  ) {\n    k2p = new Point(Fp.mul(k2p.px, endoBeta), k2p.py, k2p.pz);\n    k1p = negateCt(k1neg, k1p);\n    k2p = negateCt(k2neg, k2p);\n    return k1p.add(k2p);\n  }\n\n  /**\n   * Projective Point works in 3d / projective (homogeneous) coordinates:(X, Y, Z) ∋ (x=X/Z, y=Y/Z).\n   * Default Point works in 2d / affine coordinates: (x, y).\n   * We're doing calculations in projective, because its operations don't require costly inversion.\n   */\n  class Point implements ProjPointType<T> {\n    // base / generator point\n    static readonly BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    // zero / infinity / identity point\n    static readonly ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO); // 0, 1, 0\n    // fields\n    static readonly Fp = Fp;\n    static readonly Fn = Fn;\n\n    readonly px: T;\n    readonly py: T;\n    readonly pz: T;\n\n    /** Does NOT validate if the point is valid. Use `.assertValidity()`. */\n    constructor(px: T, py: T, pz: T) {\n      this.px = acoord('x', px);\n      this.py = acoord('y', py, true);\n      this.pz = acoord('z', pz);\n      Object.freeze(this);\n    }\n\n    /** Does NOT validate if the point is valid. Use `.assertValidity()`. */\n    static fromAffine(p: AffinePoint<T>): Point {\n      const { x, y } = p || {};\n      if (!p || !Fp.isValid(x) || !Fp.isValid(y)) throw new Error('invalid affine point');\n      if (p instanceof Point) throw new Error('projective point not allowed');\n      // (0, 0) would've produced (0, 0, 1) - instead, we need (0, 1, 0)\n      if (Fp.is0(x) && Fp.is0(y)) return Point.ZERO;\n      return new Point(x, y, Fp.ONE);\n    }\n\n    get x(): T {\n      return this.toAffine().x;\n    }\n    get y(): T {\n      return this.toAffine().y;\n    }\n\n    static normalizeZ(points: Point[]): Point[] {\n      return normalizeZ(Point, 'pz', points);\n    }\n\n    static fromBytes(bytes: Uint8Array): Point {\n      abytes(bytes);\n      return Point.fromHex(bytes);\n    }\n\n    /** Converts hash string or Uint8Array to Point. */\n    static fromHex(hex: Hex): Point {\n      const P = Point.fromAffine(fromBytes(ensureBytes('pointHex', hex)));\n      P.assertValidity();\n      return P;\n    }\n\n    /** Multiplies generator point by privateKey. */\n    static fromPrivateKey(privateKey: PrivKey) {\n      const normPrivateKeyToScalar = _legacyHelperNormPriv(\n        Fn,\n        curveOpts.allowedPrivateKeyLengths,\n        curveOpts.wrapPrivateKey\n      );\n      return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n    }\n\n    /** Multiscalar Multiplication */\n    static msm(points: Point[], scalars: bigint[]): Point {\n      return pippenger(Point, Fn, points, scalars);\n    }\n\n    /**\n     *\n     * @param windowSize\n     * @param isLazy true will defer table computation until the first multiplication\n     * @returns\n     */\n    precompute(windowSize: number = 8, isLazy = true): Point {\n      wnaf.setWindowSize(this, windowSize);\n      if (!isLazy) this.multiply(_3n); // random number\n      return this;\n    }\n\n    /** \"Private method\", don't use it directly */\n    _setWindowSize(windowSize: number) {\n      this.precompute(windowSize);\n    }\n\n    // TODO: return `this`\n    /** A point on curve is valid if it conforms to equation. */\n    assertValidity(): void {\n      assertValidMemo(this);\n    }\n\n    hasEvenY(): boolean {\n      const { y } = this.toAffine();\n      if (!Fp.isOdd) throw new Error(\"Field doesn't support isOdd\");\n      return !Fp.isOdd(y);\n    }\n\n    /** Compare one point to another. */\n    equals(other: Point): boolean {\n      aprjpoint(other);\n      const { px: X1, py: Y1, pz: Z1 } = this;\n      const { px: X2, py: Y2, pz: Z2 } = other;\n      const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n      const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n      return U1 && U2;\n    }\n\n    /** Flips point to one corresponding to (x, -y) in Affine coordinates. */\n    negate(): Point {\n      return new Point(this.px, Fp.neg(this.py), this.pz);\n    }\n\n    // Renes-Costello-Batina exception-free doubling formula.\n    // There is 30% faster Jacobian formula, but it is not complete.\n    // https://eprint.iacr.org/2015/1060, algorithm 3\n    // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n    double() {\n      const { a, b } = CURVE;\n      const b3 = Fp.mul(b, _3n);\n      const { px: X1, py: Y1, pz: Z1 } = this;\n      let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n      let t0 = Fp.mul(X1, X1); // step 1\n      let t1 = Fp.mul(Y1, Y1);\n      let t2 = Fp.mul(Z1, Z1);\n      let t3 = Fp.mul(X1, Y1);\n      t3 = Fp.add(t3, t3); // step 5\n      Z3 = Fp.mul(X1, Z1);\n      Z3 = Fp.add(Z3, Z3);\n      X3 = Fp.mul(a, Z3);\n      Y3 = Fp.mul(b3, t2);\n      Y3 = Fp.add(X3, Y3); // step 10\n      X3 = Fp.sub(t1, Y3);\n      Y3 = Fp.add(t1, Y3);\n      Y3 = Fp.mul(X3, Y3);\n      X3 = Fp.mul(t3, X3);\n      Z3 = Fp.mul(b3, Z3); // step 15\n      t2 = Fp.mul(a, t2);\n      t3 = Fp.sub(t0, t2);\n      t3 = Fp.mul(a, t3);\n      t3 = Fp.add(t3, Z3);\n      Z3 = Fp.add(t0, t0); // step 20\n      t0 = Fp.add(Z3, t0);\n      t0 = Fp.add(t0, t2);\n      t0 = Fp.mul(t0, t3);\n      Y3 = Fp.add(Y3, t0);\n      t2 = Fp.mul(Y1, Z1); // step 25\n      t2 = Fp.add(t2, t2);\n      t0 = Fp.mul(t2, t3);\n      X3 = Fp.sub(X3, t0);\n      Z3 = Fp.mul(t2, t1);\n      Z3 = Fp.add(Z3, Z3); // step 30\n      Z3 = Fp.add(Z3, Z3);\n      return new Point(X3, Y3, Z3);\n    }\n\n    // Renes-Costello-Batina exception-free addition formula.\n    // There is 30% faster Jacobian formula, but it is not complete.\n    // https://eprint.iacr.org/2015/1060, algorithm 1\n    // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n    add(other: Point): Point {\n      aprjpoint(other);\n      const { px: X1, py: Y1, pz: Z1 } = this;\n      const { px: X2, py: Y2, pz: Z2 } = other;\n      let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n      const a = CURVE.a;\n      const b3 = Fp.mul(CURVE.b, _3n);\n      let t0 = Fp.mul(X1, X2); // step 1\n      let t1 = Fp.mul(Y1, Y2);\n      let t2 = Fp.mul(Z1, Z2);\n      let t3 = Fp.add(X1, Y1);\n      let t4 = Fp.add(X2, Y2); // step 5\n      t3 = Fp.mul(t3, t4);\n      t4 = Fp.add(t0, t1);\n      t3 = Fp.sub(t3, t4);\n      t4 = Fp.add(X1, Z1);\n      let t5 = Fp.add(X2, Z2); // step 10\n      t4 = Fp.mul(t4, t5);\n      t5 = Fp.add(t0, t2);\n      t4 = Fp.sub(t4, t5);\n      t5 = Fp.add(Y1, Z1);\n      X3 = Fp.add(Y2, Z2); // step 15\n      t5 = Fp.mul(t5, X3);\n      X3 = Fp.add(t1, t2);\n      t5 = Fp.sub(t5, X3);\n      Z3 = Fp.mul(a, t4);\n      X3 = Fp.mul(b3, t2); // step 20\n      Z3 = Fp.add(X3, Z3);\n      X3 = Fp.sub(t1, Z3);\n      Z3 = Fp.add(t1, Z3);\n      Y3 = Fp.mul(X3, Z3);\n      t1 = Fp.add(t0, t0); // step 25\n      t1 = Fp.add(t1, t0);\n      t2 = Fp.mul(a, t2);\n      t4 = Fp.mul(b3, t4);\n      t1 = Fp.add(t1, t2);\n      t2 = Fp.sub(t0, t2); // step 30\n      t2 = Fp.mul(a, t2);\n      t4 = Fp.add(t4, t2);\n      t0 = Fp.mul(t1, t4);\n      Y3 = Fp.add(Y3, t0);\n      t0 = Fp.mul(t5, t4); // step 35\n      X3 = Fp.mul(t3, X3);\n      X3 = Fp.sub(X3, t0);\n      t0 = Fp.mul(t3, t1);\n      Z3 = Fp.mul(t5, Z3);\n      Z3 = Fp.add(Z3, t0); // step 40\n      return new Point(X3, Y3, Z3);\n    }\n\n    subtract(other: Point) {\n      return this.add(other.negate());\n    }\n\n    is0(): boolean {\n      return this.equals(Point.ZERO);\n    }\n\n    /**\n     * Constant time multiplication.\n     * Uses wNAF method. Windowed method may be 10% faster,\n     * but takes 2x longer to generate and consumes 2x memory.\n     * Uses precomputes when available.\n     * Uses endomorphism for Koblitz curves.\n     * @param scalar by which the point would be multiplied\n     * @returns New point\n     */\n    multiply(scalar: bigint): Point {\n      const { endo } = curveOpts;\n      if (!Fn.isValidNot0(scalar)) throw new Error('invalid scalar: out of range'); // 0 is invalid\n      let point: Point, fake: Point; // Fake point is used to const-time mult\n      const mul = (n: bigint) => wnaf.wNAFCached(this, n, Point.normalizeZ);\n      /** See docs for {@link EndomorphismOpts} */\n      if (endo) {\n        const { k1neg, k1, k2neg, k2 } = endo.splitScalar(scalar);\n        const { p: k1p, f: k1f } = mul(k1);\n        const { p: k2p, f: k2f } = mul(k2);\n        fake = k1f.add(k2f);\n        point = finishEndo(endo.beta, k1p, k2p, k1neg, k2neg);\n      } else {\n        const { p, f } = mul(scalar);\n        point = p;\n        fake = f;\n      }\n      // Normalize `z` for both points, but return only real one\n      return Point.normalizeZ([point, fake])[0];\n    }\n\n    /**\n     * Non-constant-time multiplication. Uses double-and-add algorithm.\n     * It's faster, but should only be used when you don't care about\n     * an exposed private key e.g. sig verification, which works over *public* keys.\n     */\n    multiplyUnsafe(sc: bigint): Point {\n      const { endo } = curveOpts;\n      const p = this;\n      if (!Fn.isValid(sc)) throw new Error('invalid scalar: out of range'); // 0 is valid\n      if (sc === _0n || p.is0()) return Point.ZERO;\n      if (sc === _1n) return p; // fast-path\n      if (wnaf.hasPrecomputes(this)) return this.multiply(sc);\n      if (endo) {\n        const { k1neg, k1, k2neg, k2 } = endo.splitScalar(sc);\n        // `wNAFCachedUnsafe` is 30% slower\n        const { p1, p2 } = mulEndoUnsafe(Point, p, k1, k2);\n        return finishEndo(endo.beta, p1, p2, k1neg, k2neg);\n      } else {\n        return wnaf.wNAFCachedUnsafe(p, sc);\n      }\n    }\n\n    multiplyAndAddUnsafe(Q: Point, a: bigint, b: bigint): Point | undefined {\n      const sum = this.multiplyUnsafe(a).add(Q.multiplyUnsafe(b));\n      return sum.is0() ? undefined : sum;\n    }\n\n    /**\n     * Converts Projective point to affine (x, y) coordinates.\n     * @param invertedZ Z^-1 (inverted zero) - optional, precomputation is useful for invertBatch\n     */\n    toAffine(invertedZ?: T): AffinePoint<T> {\n      return toAffineMemo(this, invertedZ);\n    }\n\n    /**\n     * Checks whether Point is free of torsion elements (is in prime subgroup).\n     * Always torsion-free for cofactor=1 curves.\n     */\n    isTorsionFree(): boolean {\n      const { isTorsionFree } = curveOpts;\n      if (cofactor === _1n) return true;\n      if (isTorsionFree) return isTorsionFree(Point, this);\n      return wnaf.wNAFCachedUnsafe(this, CURVE_ORDER).is0();\n    }\n\n    clearCofactor(): Point {\n      const { clearCofactor } = curveOpts;\n      if (cofactor === _1n) return this; // Fast-path\n      if (clearCofactor) return clearCofactor(Point, this) as Point;\n      return this.multiplyUnsafe(cofactor);\n    }\n\n    toBytes(isCompressed = true): Uint8Array {\n      abool('isCompressed', isCompressed);\n      this.assertValidity();\n      return toBytes(Point, this, isCompressed);\n    }\n\n    /** @deprecated use `toBytes` */\n    toRawBytes(isCompressed = true): Uint8Array {\n      return this.toBytes(isCompressed);\n    }\n\n    toHex(isCompressed = true): string {\n      return bytesToHex(this.toBytes(isCompressed));\n    }\n\n    toString() {\n      return `<Point ${this.is0() ? 'ZERO' : this.toHex()}>`;\n    }\n  }\n  const bits = Fn.BITS;\n  const wnaf = wNAF(Point, curveOpts.endo ? Math.ceil(bits / 2) : bits);\n  return Point;\n}\n\n// _legacyWeierstrass\n/** @deprecated use `weierstrassN` */\nexport function weierstrassPoints<T>(c: CurvePointsTypeWithLength<T>): CurvePointsRes<T> {\n  const { CURVE, curveOpts } = _weierstrass_legacy_opts_to_new(c);\n  const Point = weierstrassN(CURVE, curveOpts);\n  return _weierstrass_new_output_to_legacy(c, Point);\n}\n\n// Instance\nexport interface SignatureType {\n  readonly r: bigint;\n  readonly s: bigint;\n  readonly recovery?: number;\n  assertValidity(): void;\n  addRecoveryBit(recovery: number): RecoveredSignatureType;\n  hasHighS(): boolean;\n  normalizeS(): SignatureType;\n  recoverPublicKey(msgHash: Hex): ProjPointType<bigint>;\n  toCompactRawBytes(): Uint8Array;\n  toCompactHex(): string;\n  toDERRawBytes(): Uint8Array;\n  toDERHex(): string;\n  // toBytes(format?: string): Uint8Array;\n}\nexport type RecoveredSignatureType = SignatureType & {\n  readonly recovery: number;\n};\n// Static methods\nexport type SignatureConstructor = {\n  new (r: bigint, s: bigint, recovery?: number): SignatureType;\n  fromCompact(hex: Hex): SignatureType;\n  fromDER(hex: Hex): SignatureType;\n};\nexport type SignatureLike = { r: bigint; s: bigint };\nexport type PubKey = Hex | ProjPointType<bigint>;\n\nexport type CurveType = BasicWCurve<bigint> & {\n  hash: CHash; // CHash not FHash because we need outputLen for DRBG\n  hmac?: HmacFnSync;\n  randomBytes?: (bytesLength?: number) => Uint8Array;\n  lowS?: boolean;\n  bits2int?: (bytes: Uint8Array) => bigint;\n  bits2int_modN?: (bytes: Uint8Array) => bigint;\n};\n\n// Points start with byte 0x02 when y is even; otherwise 0x03\nfunction pprefix(hasEvenY: boolean): Uint8Array {\n  return Uint8Array.of(hasEvenY ? 0x02 : 0x03);\n}\n\nexport type CurveFn = {\n  CURVE: CurvePointsType<bigint>;\n  getPublicKey: (privateKey: PrivKey, isCompressed?: boolean) => Uint8Array;\n  getSharedSecret: (privateA: PrivKey, publicB: Hex, isCompressed?: boolean) => Uint8Array;\n  sign: (msgHash: Hex, privKey: PrivKey, opts?: SignOpts) => RecoveredSignatureType;\n  verify: (signature: Hex | SignatureLike, msgHash: Hex, publicKey: Hex, opts?: VerOpts) => boolean;\n  Point: ProjConstructor<bigint>;\n  /** @deprecated use `Point` */\n  ProjectivePoint: ProjConstructor<bigint>;\n  Signature: SignatureConstructor;\n  utils: {\n    normPrivateKeyToScalar: (key: PrivKey) => bigint;\n    isValidPrivateKey(privateKey: PrivKey): boolean;\n    randomPrivateKey: () => Uint8Array;\n    precompute: (windowSize?: number, point?: ProjPointType<bigint>) => ProjPointType<bigint>;\n  };\n};\n\nexport function ecdsa(\n  Point: ProjConstructor<bigint>,\n  ecdsaOpts: ECDSAOpts,\n  curveOpts: WeierstrassExtraOpts<bigint> = {}\n): ECDSA {\n  _validateObject(\n    ecdsaOpts,\n    { hash: 'function' },\n    {\n      hmac: 'function',\n      lowS: 'boolean',\n      randomBytes: 'function',\n      bits2int: 'function',\n      bits2int_modN: 'function',\n    }\n  );\n\n  const randomBytes_ = ecdsaOpts.randomBytes || randomBytes;\n  const hmac_: HmacFnSync =\n    ecdsaOpts.hmac ||\n    (((key, ...msgs) => hmac(ecdsaOpts.hash, key, concatBytes(...msgs))) satisfies HmacFnSync);\n\n  const { Fp, Fn } = Point;\n  const { ORDER: CURVE_ORDER, BITS: fnBits } = Fn;\n\n  function isBiggerThanHalfOrder(number: bigint) {\n    const HALF = CURVE_ORDER >> _1n;\n    return number > HALF;\n  }\n\n  function normalizeS(s: bigint) {\n    return isBiggerThanHalfOrder(s) ? Fn.neg(s) : s;\n  }\n  function aValidRS(title: string, num: bigint) {\n    if (!Fn.isValidNot0(num))\n      throw new Error(`invalid signature ${title}: out of range 1..CURVE.n`);\n  }\n\n  /**\n   * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n   */\n  class Signature implements SignatureType {\n    readonly r: bigint;\n    readonly s: bigint;\n    readonly recovery?: number;\n    constructor(r: bigint, s: bigint, recovery?: number) {\n      aValidRS('r', r); // r in [1..N-1]\n      aValidRS('s', s); // s in [1..N-1]\n      this.r = r;\n      this.s = s;\n      if (recovery != null) this.recovery = recovery;\n      Object.freeze(this);\n    }\n\n    // pair (bytes of r, bytes of s)\n    static fromCompact(hex: Hex) {\n      const L = Fn.BYTES;\n      const b = ensureBytes('compactSignature', hex, L * 2);\n      return new Signature(Fn.fromBytes(b.subarray(0, L)), Fn.fromBytes(b.subarray(L, L * 2)));\n    }\n\n    // DER encoded ECDSA signature\n    // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n    static fromDER(hex: Hex) {\n      const { r, s } = DER.toSig(ensureBytes('DER', hex));\n      return new Signature(r, s);\n    }\n\n    /**\n     * @todo remove\n     * @deprecated\n     */\n    assertValidity(): void {}\n\n    addRecoveryBit(recovery: number): RecoveredSignature {\n      return new Signature(this.r, this.s, recovery) as RecoveredSignature;\n    }\n\n    // ProjPointType<bigint>\n    recoverPublicKey(msgHash: Hex): typeof Point.BASE {\n      const FIELD_ORDER = Fp.ORDER;\n      const { r, s, recovery: rec } = this;\n      if (rec == null || ![0, 1, 2, 3].includes(rec)) throw new Error('recovery id invalid');\n\n      // ECDSA recovery is hard for cofactor > 1 curves.\n      // In sign, `r = q.x mod n`, and here we recover q.x from r.\n      // While recovering q.x >= n, we need to add r+n for cofactor=1 curves.\n      // However, for cofactor>1, r+n may not get q.x:\n      // r+n*i would need to be done instead where i is unknown.\n      // To easily get i, we either need to:\n      // a. increase amount of valid recid values (4, 5...); OR\n      // b. prohibit non-prime-order signatures (recid > 1).\n      const hasCofactor = CURVE_ORDER * _2n < FIELD_ORDER;\n      if (hasCofactor && rec > 1) throw new Error('recovery id is ambiguous for h>1 curve');\n\n      const radj = rec === 2 || rec === 3 ? r + CURVE_ORDER : r;\n      if (!Fp.isValid(radj)) throw new Error('recovery id 2 or 3 invalid');\n      const x = Fp.toBytes(radj);\n      const R = Point.fromHex(concatBytes(pprefix((rec & 1) === 0), x));\n      const ir = Fn.inv(radj); // r^-1\n      const h = bits2int_modN(ensureBytes('msgHash', msgHash)); // Truncate hash\n      const u1 = Fn.create(-h * ir); // -hr^-1\n      const u2 = Fn.create(s * ir); // sr^-1\n      // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1). unsafe is fine: there is no private data.\n      const Q = Point.BASE.multiplyUnsafe(u1).add(R.multiplyUnsafe(u2));\n      if (Q.is0()) throw new Error('point at infinify');\n      Q.assertValidity();\n      return Q;\n    }\n\n    // Signatures should be low-s, to prevent malleability.\n    hasHighS(): boolean {\n      return isBiggerThanHalfOrder(this.s);\n    }\n\n    normalizeS() {\n      return this.hasHighS() ? new Signature(this.r, Fn.neg(this.s), this.recovery) : this;\n    }\n\n    toBytes(format: 'compact' | 'der') {\n      if (format === 'compact') return concatBytes(Fn.toBytes(this.r), Fn.toBytes(this.s));\n      if (format === 'der') return hexToBytes(DER.hexFromSig(this));\n      throw new Error('invalid format');\n    }\n\n    // DER-encoded\n    toDERRawBytes() {\n      return this.toBytes('der');\n    }\n    toDERHex() {\n      return bytesToHex(this.toBytes('der'));\n    }\n\n    // padded bytes of r, then padded bytes of s\n    toCompactRawBytes() {\n      return this.toBytes('compact');\n    }\n    toCompactHex() {\n      return bytesToHex(this.toBytes('compact'));\n    }\n  }\n  type RecoveredSignature = Signature & { recovery: number };\n\n  const normPrivateKeyToScalar = _legacyHelperNormPriv(\n    Fn,\n    curveOpts.allowedPrivateKeyLengths,\n    curveOpts.wrapPrivateKey\n  );\n\n  const utils = {\n    isValidPrivateKey(privateKey: PrivKey) {\n      try {\n        normPrivateKeyToScalar(privateKey);\n        return true;\n      } catch (error) {\n        return false;\n      }\n    },\n    normPrivateKeyToScalar: normPrivateKeyToScalar,\n\n    /**\n     * Produces cryptographically secure private key from random of size\n     * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n     */\n    randomPrivateKey: (): Uint8Array => {\n      const n = CURVE_ORDER;\n      return mapHashToField(randomBytes_(getMinHashLength(n)), n);\n    },\n\n    precompute(windowSize = 8, point = Point.BASE): typeof Point.BASE {\n      return point.precompute(windowSize, false);\n    },\n  };\n\n  /**\n   * Computes public key for a private key. Checks for validity of the private key.\n   * @param privateKey private key\n   * @param isCompressed whether to return compact (default), or full key\n   * @returns Public key, full when isCompressed=false; short when isCompressed=true\n   */\n  function getPublicKey(privateKey: PrivKey, isCompressed = true): Uint8Array {\n    return Point.fromPrivateKey(privateKey).toBytes(isCompressed);\n  }\n\n  /**\n   * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n   */\n  function isProbPub(item: PrivKey | PubKey): boolean | undefined {\n    if (typeof item === 'bigint') return false;\n    if (item instanceof Point) return true;\n    const arr = ensureBytes('key', item);\n    const length = arr.length;\n    const L = Fp.BYTES;\n    const LC = L + 1; // e.g. 33 for 32\n    const LU = 2 * L + 1; // e.g. 65 for 32\n    if (curveOpts.allowedPrivateKeyLengths || Fn.BYTES === LC) {\n      return undefined;\n    } else {\n      return length === LC || length === LU;\n    }\n  }\n\n  /**\n   * ECDH (Elliptic Curve Diffie Hellman).\n   * Computes shared public key from private key and public key.\n   * Checks: 1) private key validity 2) shared key is on-curve.\n   * Does NOT hash the result.\n   * @param privateA private key\n   * @param publicB different public key\n   * @param isCompressed whether to return compact (default), or full key\n   * @returns shared public key\n   */\n  function getSharedSecret(privateA: PrivKey, publicB: Hex, isCompressed = true): Uint8Array {\n    if (isProbPub(privateA) === true) throw new Error('first arg must be private key');\n    if (isProbPub(publicB) === false) throw new Error('second arg must be public key');\n    const b = Point.fromHex(publicB); // check for being on-curve\n    return b.multiply(normPrivateKeyToScalar(privateA)).toBytes(isCompressed);\n  }\n\n  // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n  // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n  // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n  // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n  const bits2int =\n    ecdsaOpts.bits2int ||\n    function (bytes: Uint8Array): bigint {\n      // Our custom check \"just in case\", for protection against DoS\n      if (bytes.length > 8192) throw new Error('input is too large');\n      // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n      // for some cases, since bytes.length * 8 is not actual bitLength.\n      const num = bytesToNumberBE(bytes); // check for == u8 done here\n      const delta = bytes.length * 8 - fnBits; // truncate to nBitLength leftmost bits\n      return delta > 0 ? num >> BigInt(delta) : num;\n    };\n  const bits2int_modN =\n    ecdsaOpts.bits2int_modN ||\n    function (bytes: Uint8Array): bigint {\n      return Fn.create(bits2int(bytes)); // can't use bytesToNumberBE here\n    };\n  // NOTE: pads output with zero as per spec\n  const ORDER_MASK = bitMask(fnBits);\n  /**\n   * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n   */\n  function int2octets(num: bigint): Uint8Array {\n    // IMPORTANT: the check ensures working for case `Fn.BYTES != Fn.BITS * 8`\n    aInRange('num < 2^' + fnBits, num, _0n, ORDER_MASK);\n    return Fn.toBytes(num);\n  }\n\n  // Steps A, D of RFC6979 3.2\n  // Creates RFC6979 seed; converts msg/privKey to numbers.\n  // Used only in sign, not in verify.\n  // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order,\n  // this will be invalid at least for P521. Also it can be bigger for P224 + SHA256\n  function prepSig(msgHash: Hex, privateKey: PrivKey, opts = defaultSigOpts) {\n    if (['recovered', 'canonical'].some((k) => k in opts))\n      throw new Error('sign() legacy options not supported');\n    const { hash } = ecdsaOpts;\n    let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n    if (lowS == null) lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n    msgHash = ensureBytes('msgHash', msgHash);\n    validateSigVerOpts(opts);\n    if (prehash) msgHash = ensureBytes('prehashed msgHash', hash(msgHash));\n\n    // We can't later call bits2octets, since nested bits2int is broken for curves\n    // with fnBits % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n    // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n    const h1int = bits2int_modN(msgHash);\n    const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n    const seedArgs = [int2octets(d), int2octets(h1int)];\n    // extraEntropy. RFC6979 3.6: additional k' (optional).\n    if (ent != null && ent !== false) {\n      // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n      const e = ent === true ? randomBytes_(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n      seedArgs.push(ensureBytes('extraEntropy', e)); // check for being bytes\n    }\n    const seed = concatBytes(...seedArgs); // Step D of RFC6979 3.2\n    const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n    // Converts signature params into point w r/s, checks result for validity.\n    // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n    // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n    // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n    function k2sig(kBytes: Uint8Array): RecoveredSignature | undefined {\n      // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n      // Important: all mod() calls here must be done over N\n      const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n      if (!Fn.isValidNot0(k)) return; // Valid scalars (including k) must be in 1..N-1\n      const ik = Fn.inv(k); // k^-1 mod n\n      const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n      const r = Fn.create(q.x); // r = q.x mod n\n      if (r === _0n) return;\n      const s = Fn.create(ik * Fn.create(m + r * d)); // Not using blinding here, see comment above\n      if (s === _0n) return;\n      let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n      let normS = s;\n      if (lowS && isBiggerThanHalfOrder(s)) {\n        normS = normalizeS(s); // if lowS was passed, ensure s is always\n        recovery ^= 1; // // in the bottom half of N\n      }\n      return new Signature(r, normS, recovery) as RecoveredSignature; // use normS, not s\n    }\n    return { seed, k2sig };\n  }\n  const defaultSigOpts: SignOpts = { lowS: ecdsaOpts.lowS, prehash: false };\n  const defaultVerOpts: VerOpts = { lowS: ecdsaOpts.lowS, prehash: false };\n\n  /**\n   * Signs message hash with a private key.\n   * ```\n   * sign(m, d, k) where\n   *   (x, y) = G × k\n   *   r = x mod n\n   *   s = (m + dr)/k mod n\n   * ```\n   * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n   * @param privKey private key\n   * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n   * @returns signature with recovery param\n   */\n  function sign(msgHash: Hex, privKey: PrivKey, opts = defaultSigOpts): RecoveredSignature {\n    const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n    const drbg = createHmacDrbg<RecoveredSignature>(ecdsaOpts.hash.outputLen, Fn.BYTES, hmac_);\n    return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n  }\n\n  // Enable precomputes. Slows down first publicKey computation by 20ms.\n  Point.BASE.precompute(8);\n\n  /**\n   * Verifies a signature against message hash and public key.\n   * Rejects lowS signatures by default: to override,\n   * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n   *\n   * ```\n   * verify(r, s, h, P) where\n   *   U1 = hs^-1 mod n\n   *   U2 = rs^-1 mod n\n   *   R = U1⋅G - U2⋅P\n   *   mod(R.x, n) == r\n   * ```\n   */\n  function verify(\n    signature: Hex | SignatureLike,\n    msgHash: Hex,\n    publicKey: Hex,\n    opts = defaultVerOpts\n  ): boolean {\n    const sg = signature;\n    msgHash = ensureBytes('msgHash', msgHash);\n    publicKey = ensureBytes('publicKey', publicKey);\n\n    // Verify opts\n    validateSigVerOpts(opts);\n    const { lowS, prehash, format } = opts;\n\n    // TODO: remove\n    if ('strict' in opts) throw new Error('options.strict was renamed to lowS');\n\n    if (format !== undefined && !['compact', 'der', 'js'].includes(format))\n      throw new Error('format must be \"compact\", \"der\" or \"js\"');\n    const isHex = typeof sg === 'string' || isBytes(sg);\n    const isObj =\n      !isHex &&\n      !format &&\n      typeof sg === 'object' &&\n      sg !== null &&\n      typeof sg.r === 'bigint' &&\n      typeof sg.s === 'bigint';\n    if (!isHex && !isObj)\n      throw new Error('invalid signature, expected Uint8Array, hex string or Signature instance');\n    let _sig: Signature | undefined = undefined;\n    let P: ProjPointType<bigint>;\n\n    // deduce signature format\n    try {\n      // if (format === 'js') {\n      //   if (sg != null && !isBytes(sg)) _sig = new Signature(sg.r, sg.s);\n      // } else if (format === 'compact') {\n      //   _sig = Signature.fromCompact(sg);\n      // } else if (format === 'der') {\n      //   _sig = Signature.fromDER(sg);\n      // } else {\n      //   throw new Error('invalid format');\n      // }\n      if (isObj) {\n        if (format === undefined || format === 'js') {\n          _sig = new Signature(sg.r, sg.s);\n        } else {\n          throw new Error('invalid format');\n        }\n      }\n      if (isHex) {\n        // TODO: remove this malleable check\n        // Signature can be represented in 2 ways: compact (2*Fn.BYTES) & DER (variable-length).\n        // Since DER can also be 2*Fn.BYTES bytes, we check for it first.\n        try {\n          if (format !== 'compact') _sig = Signature.fromDER(sg);\n        } catch (derError) {\n          if (!(derError instanceof DER.Err)) throw derError;\n        }\n        if (!_sig && format !== 'der') _sig = Signature.fromCompact(sg);\n      }\n      P = Point.fromHex(publicKey);\n    } catch (error) {\n      return false;\n    }\n    if (!_sig) return false;\n    if (lowS && _sig.hasHighS()) return false;\n    // todo: optional.hash => hash\n    if (prehash) msgHash = ecdsaOpts.hash(msgHash);\n    const { r, s } = _sig;\n    const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n    const is = Fn.inv(s); // s^-1\n    const u1 = Fn.create(h * is); // u1 = hs^-1 mod n\n    const u2 = Fn.create(r * is); // u2 = rs^-1 mod n\n    const R = Point.BASE.multiplyUnsafe(u1).add(P.multiplyUnsafe(u2));\n    if (R.is0()) return false;\n    const v = Fn.create(R.x); // v = r.x mod n\n    return v === r;\n  }\n  // TODO: clarify API for cloning .clone({hash: sha512}) ? .createWith({hash: sha512})?\n  // const clone = (hash: CHash): ECDSA => ecdsa(Point, { ...ecdsaOpts, ...getHash(hash) }, curveOpts);\n  return Object.freeze({\n    getPublicKey,\n    getSharedSecret,\n    sign,\n    verify,\n    utils,\n    Point,\n    Signature,\n  });\n}\n\nexport type WsPointComposed<T> = {\n  CURVE: WeierstrassOpts<T>;\n  curveOpts: WeierstrassExtraOpts<T>;\n};\nexport type WsComposed = {\n  CURVE: WeierstrassOpts<bigint>;\n  curveOpts: WeierstrassExtraOpts<bigint>;\n  ecdsaOpts: ECDSAOpts;\n};\nfunction _weierstrass_legacy_opts_to_new<T>(c: CurvePointsType<T>): WsPointComposed<T> {\n  const CURVE: WeierstrassOpts<T> = {\n    a: c.a,\n    b: c.b,\n    p: c.Fp.ORDER,\n    n: c.n,\n    h: c.h,\n    Gx: c.Gx,\n    Gy: c.Gy,\n  };\n  const Fp = c.Fp;\n  const Fn = Field(CURVE.n, c.nBitLength);\n  const curveOpts: WeierstrassExtraOpts<T> = {\n    Fp,\n    Fn,\n    allowedPrivateKeyLengths: c.allowedPrivateKeyLengths,\n    allowInfinityPoint: c.allowInfinityPoint,\n    endo: c.endo,\n    wrapPrivateKey: c.wrapPrivateKey,\n    isTorsionFree: c.isTorsionFree,\n    clearCofactor: c.clearCofactor,\n    fromBytes: c.fromBytes,\n    toBytes: c.toBytes,\n  };\n  return { CURVE, curveOpts };\n}\nfunction _ecdsa_legacy_opts_to_new(c: CurveType): WsComposed {\n  const { CURVE, curveOpts } = _weierstrass_legacy_opts_to_new(c);\n  const ecdsaOpts: ECDSAOpts = {\n    hash: c.hash,\n    hmac: c.hmac,\n    randomBytes: c.randomBytes,\n    lowS: c.lowS,\n    bits2int: c.bits2int,\n    bits2int_modN: c.bits2int_modN,\n  };\n  return { CURVE, curveOpts, ecdsaOpts };\n}\nfunction _weierstrass_new_output_to_legacy<T>(\n  c: CurvePointsType<T>,\n  Point: ProjConstructor<T>\n): CurvePointsRes<T> {\n  const { Fp, Fn } = Point;\n  // TODO: remove\n  function isWithinCurveOrder(num: bigint): boolean {\n    return inRange(num, _1n, Fn.ORDER);\n  }\n  const weierstrassEquation = _legacyHelperEquat(Fp, c.a, c.b);\n  const normPrivateKeyToScalar = _legacyHelperNormPriv(\n    Fn,\n    c.allowedPrivateKeyLengths,\n    c.wrapPrivateKey\n  );\n  return Object.assign(\n    {},\n    {\n      CURVE: c,\n      Point: Point,\n      ProjectivePoint: Point,\n      normPrivateKeyToScalar,\n      weierstrassEquation,\n      isWithinCurveOrder,\n    }\n  );\n}\nfunction _ecdsa_new_output_to_legacy(c: CurveType, ecdsa: ECDSA): CurveFn {\n  return Object.assign({}, ecdsa, {\n    ProjectivePoint: ecdsa.Point,\n    CURVE: c,\n  });\n}\n\n// _ecdsa_legacy\nexport function weierstrass(c: CurveType): CurveFn {\n  const { CURVE, curveOpts, ecdsaOpts } = _ecdsa_legacy_opts_to_new(c);\n  const Point = weierstrassN(CURVE, curveOpts);\n  const signs = ecdsa(Point, ecdsaOpts, curveOpts);\n  return _ecdsa_new_output_to_legacy(c, signs);\n}\n\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nexport function SWUFpSqrtRatio<T>(\n  Fp: IField<T>,\n  Z: T\n): (u: T, v: T) => { isValid: boolean; value: T } {\n  // Generic implementation\n  const q = Fp.ORDER;\n  let l = _0n;\n  for (let o = q - _1n; o % _2n === _0n; o /= _2n) l += _1n;\n  const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n  // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n  // 2n ** c1 == 2n << (c1-1)\n  const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n  const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n  const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n  const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n  const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n  const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n  const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n  const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n  let sqrtRatio = (u: T, v: T): { isValid: boolean; value: T } => {\n    let tv1 = c6; // 1. tv1 = c6\n    let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n    let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n    tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n    let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n    tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n    tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n    tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n    tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n    let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n    tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n    let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n    tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n    tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n    tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n    tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n    // 17. for i in (c1, c1 - 1, ..., 2):\n    for (let i = c1; i > _1n; i--) {\n      let tv5 = i - _2n; // 18.    tv5 = i - 2\n      tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n      let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n      const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n      tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n      tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n      tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n      tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n      tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n    }\n    return { isValid: isQR, value: tv3 };\n  };\n  if (Fp.ORDER % _4n === _3n) {\n    // sqrt_ratio_3mod4(u, v)\n    const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n    const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n    sqrtRatio = (u: T, v: T) => {\n      let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n      const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n      tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n      let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n      y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n      const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n      const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n      const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n      let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n      return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n    };\n  }\n  // No curves uses that\n  // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n  return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nexport function mapToCurveSimpleSWU<T>(\n  Fp: IField<T>,\n  opts: {\n    A: T;\n    B: T;\n    Z: T;\n  }\n): (u: T) => { x: T; y: T } {\n  validateField(Fp);\n  const { A, B, Z } = opts;\n  if (!Fp.isValid(A) || !Fp.isValid(B) || !Fp.isValid(Z))\n    throw new Error('mapToCurveSimpleSWU: invalid opts');\n  const sqrtRatio = SWUFpSqrtRatio(Fp, Z);\n  if (!Fp.isOdd) throw new Error('Field does not have .isOdd()');\n  // Input: u, an element of F.\n  // Output: (x, y), a point on E.\n  return (u: T): { x: T; y: T } => {\n    // prettier-ignore\n    let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n    tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n    tv1 = Fp.mul(tv1, Z); // 2.  tv1 = Z * tv1\n    tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n    tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n    tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n    tv3 = Fp.mul(tv3, B); // 6.  tv3 = B * tv3\n    tv4 = Fp.cmov(Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n    tv4 = Fp.mul(tv4, A); // 8.  tv4 = A * tv4\n    tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n    tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n    tv5 = Fp.mul(tv6, A); // 11. tv5 = A * tv6\n    tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n    tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n    tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n    tv5 = Fp.mul(tv6, B); // 15. tv5 = B * tv6\n    tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n    x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n    const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n    y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n    y = Fp.mul(y, value); // 20.   y = y * y1\n    x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n    y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n    const e1 = Fp.isOdd!(u) === Fp.isOdd!(y); // 23.  e1 = sgn0(u) == sgn0(y)\n    y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n    const tv4_inv = FpInvertBatch(Fp, [tv4], true)[0];\n    x = Fp.mul(x, tv4_inv); // 25.   x = x / tv4\n    return { x, y };\n  };\n}\n", "/**\n * Utilities for short weierstrass curves, combined with noble-hashes.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { type CurveFn, type CurveType, weierstrass } from './abstract/weierstrass.ts';\nimport type { CHash } from './utils.ts';\n\n/** connects noble-curves to noble-hashes */\nexport function getHash(hash: CHash): { hash: CHash } {\n  return { hash };\n}\n/** Same API as @noble/hashes, with ability to create curve with custom hash */\nexport type CurveDef = Readonly<Omit<CurveType, 'hash'>>;\nexport type CurveFnWithCreate = CurveFn & { create: (hash: CHash) => CurveFn };\n\nexport function createCurve(curveDef: CurveDef, defHash: CHash): CurveFnWithCreate {\n  const create = (hash: CHash): CurveFn => weierstrass({ ...curveDef, hash: hash });\n  return { ...create(defHash), create };\n}\n", "/**\n * hash-to-curve from RFC 9380.\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F.\n * https://www.rfc-editor.org/rfc/rfc9380\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport type { CHash } from '../utils.ts';\nimport {\n  _validateObject,\n  abytes,\n  bytesToNumberBE,\n  concatBytes,\n  isBytes,\n  isHash,\n  utf8ToBytes,\n} from '../utils.ts';\nimport type { AffinePoint, Group, GroupConstructor } from './curve.ts';\nimport { FpInvertBatch, type IField, mod } from './modular.ts';\n\nexport type UnicodeOrBytes = string | Uint8Array;\n\n/**\n * * `DST` is a domain separation tag, defined in section 2.2.5\n * * `p` characteristic of F, where F is a finite field of characteristic p and order q = p^m\n * * `m` is extension degree (1 for prime fields)\n * * `k` is the target security target in bits (e.g. 128), from section 5.1\n * * `expand` is `xmd` (SHA2, SHA3, BLAKE) or `xof` (SHAKE, BLAKE-XOF)\n * * `hash` conforming to `utils.CHash` interface, with `outputLen` / `blockLen` props\n */\nexport type H2COpts = {\n  DST: UnicodeOrBytes;\n  expand: 'xmd' | 'xof';\n  hash: CHash;\n  p: bigint;\n  m: number;\n  k: number;\n};\nexport type H2CHashOpts = {\n  expand: 'xmd' | 'xof';\n  hash: CHash;\n};\n// todo: remove\nexport type Opts = H2COpts;\n\n// Octet Stream to Integer. \"spec\" implementation of os2ip is 2.5x slower vs bytesToNumberBE.\nconst os2ip = bytesToNumberBE;\n\n// Integer to Octet Stream (numberToBytesBE)\nfunction i2osp(value: number, length: number): Uint8Array {\n  anum(value);\n  anum(length);\n  if (value < 0 || value >= 1 << (8 * length)) throw new Error('invalid I2OSP input: ' + value);\n  const res = Array.from({ length }).fill(0) as number[];\n  for (let i = length - 1; i >= 0; i--) {\n    res[i] = value & 0xff;\n    value >>>= 8;\n  }\n  return new Uint8Array(res);\n}\n\nfunction strxor(a: Uint8Array, b: Uint8Array): Uint8Array {\n  const arr = new Uint8Array(a.length);\n  for (let i = 0; i < a.length; i++) {\n    arr[i] = a[i] ^ b[i];\n  }\n  return arr;\n}\n\nfunction anum(item: unknown): void {\n  if (!Number.isSafeInteger(item)) throw new Error('number expected');\n}\n\n/**\n * Produces a uniformly random byte string using a cryptographic hash function H that outputs b bits.\n * [RFC 9380 5.3.1](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.1).\n */\nexport function expand_message_xmd(\n  msg: Uint8Array,\n  DST: Uint8Array,\n  lenInBytes: number,\n  H: CHash\n): Uint8Array {\n  abytes(msg);\n  abytes(DST);\n  anum(lenInBytes);\n  // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n  if (DST.length > 255) DST = H(concatBytes(utf8ToBytes('H2C-OVERSIZE-DST-'), DST));\n  const { outputLen: b_in_bytes, blockLen: r_in_bytes } = H;\n  const ell = Math.ceil(lenInBytes / b_in_bytes);\n  if (lenInBytes > 65535 || ell > 255) throw new Error('expand_message_xmd: invalid lenInBytes');\n  const DST_prime = concatBytes(DST, i2osp(DST.length, 1));\n  const Z_pad = i2osp(0, r_in_bytes);\n  const l_i_b_str = i2osp(lenInBytes, 2); // len_in_bytes_str\n  const b = new Array<Uint8Array>(ell);\n  const b_0 = H(concatBytes(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));\n  b[0] = H(concatBytes(b_0, i2osp(1, 1), DST_prime));\n  for (let i = 1; i <= ell; i++) {\n    const args = [strxor(b_0, b[i - 1]), i2osp(i + 1, 1), DST_prime];\n    b[i] = H(concatBytes(...args));\n  }\n  const pseudo_random_bytes = concatBytes(...b);\n  return pseudo_random_bytes.slice(0, lenInBytes);\n}\n\n/**\n * Produces a uniformly random byte string using an extendable-output function (XOF) H.\n * 1. The collision resistance of H MUST be at least k bits.\n * 2. H MUST be an XOF that has been proved indifferentiable from\n *    a random oracle under a reasonable cryptographic assumption.\n * [RFC 9380 5.3.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.2).\n */\nexport function expand_message_xof(\n  msg: Uint8Array,\n  DST: Uint8Array,\n  lenInBytes: number,\n  k: number,\n  H: CHash\n): Uint8Array {\n  abytes(msg);\n  abytes(DST);\n  anum(lenInBytes);\n  // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n  // DST = H('H2C-OVERSIZE-DST-' || a_very_long_DST, Math.ceil((lenInBytes * k) / 8));\n  if (DST.length > 255) {\n    const dkLen = Math.ceil((2 * k) / 8);\n    DST = H.create({ dkLen }).update(utf8ToBytes('H2C-OVERSIZE-DST-')).update(DST).digest();\n  }\n  if (lenInBytes > 65535 || DST.length > 255)\n    throw new Error('expand_message_xof: invalid lenInBytes');\n  return (\n    H.create({ dkLen: lenInBytes })\n      .update(msg)\n      .update(i2osp(lenInBytes, 2))\n      // 2. DST_prime = DST || I2OSP(len(DST), 1)\n      .update(DST)\n      .update(i2osp(DST.length, 1))\n      .digest()\n  );\n}\n\n/**\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F.\n * [RFC 9380 5.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.2).\n * @param msg a byte string containing the message to hash\n * @param count the number of elements of F to output\n * @param options `{DST: string, p: bigint, m: number, k: number, expand: 'xmd' | 'xof', hash: H}`, see above\n * @returns [u_0, ..., u_(count - 1)], a list of field elements.\n */\nexport function hash_to_field(msg: Uint8Array, count: number, options: H2COpts): bigint[][] {\n  _validateObject(options, {\n    p: 'bigint',\n    m: 'number',\n    k: 'number',\n    hash: 'function',\n  });\n  const { p, k, m, hash, expand, DST: _DST } = options;\n  if (!isBytes(_DST) && typeof _DST !== 'string')\n    throw new Error('DST must be string or uint8array');\n  if (!isHash(options.hash)) throw new Error('expected valid hash');\n  abytes(msg);\n  anum(count);\n  const DST = typeof _DST === 'string' ? utf8ToBytes(_DST) : _DST;\n  const log2p = p.toString(2).length;\n  const L = Math.ceil((log2p + k) / 8); // section 5.1 of ietf draft link above\n  const len_in_bytes = count * m * L;\n  let prb; // pseudo_random_bytes\n  if (expand === 'xmd') {\n    prb = expand_message_xmd(msg, DST, len_in_bytes, hash);\n  } else if (expand === 'xof') {\n    prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);\n  } else if (expand === '_internal_pass') {\n    // for internal tests only\n    prb = msg;\n  } else {\n    throw new Error('expand must be \"xmd\" or \"xof\"');\n  }\n  const u = new Array(count);\n  for (let i = 0; i < count; i++) {\n    const e = new Array(m);\n    for (let j = 0; j < m; j++) {\n      const elm_offset = L * (j + i * m);\n      const tv = prb.subarray(elm_offset, elm_offset + L);\n      e[j] = mod(os2ip(tv), p);\n    }\n    u[i] = e;\n  }\n  return u;\n}\n\nexport type XY<T> = (x: T, y: T) => { x: T; y: T };\nexport type XYRatio<T> = [T[], T[], T[], T[]]; // xn/xd, yn/yd\nexport function isogenyMap<T, F extends IField<T>>(field: F, map: XYRatio<T>): XY<T> {\n  // Make same order as in spec\n  const coeff = map.map((i) => Array.from(i).reverse());\n  return (x: T, y: T) => {\n    const [xn, xd, yn, yd] = coeff.map((val) =>\n      val.reduce((acc, i) => field.add(field.mul(acc, x), i))\n    );\n    // 6.6.3\n    // Exceptional cases of iso_map are inputs that cause the denominator of\n    // either rational function to evaluate to zero; such cases MUST return\n    // the identity point on E.\n    const [xd_inv, yd_inv] = FpInvertBatch(field, [xd, yd], true);\n    x = field.mul(xn, xd_inv); // xNum / xDen\n    y = field.mul(y, field.mul(yn, yd_inv)); // y * (yNum / yDev)\n    return { x, y };\n  };\n}\n\n/** Point interface, which curves must implement to work correctly with the module. */\nexport interface H2CPoint<T> extends Group<H2CPoint<T>> {\n  add(rhs: H2CPoint<T>): H2CPoint<T>;\n  toAffine(iz?: bigint): AffinePoint<T>;\n  clearCofactor(): H2CPoint<T>;\n  assertValidity(): void;\n}\n\nexport interface H2CPointConstructor<T> extends GroupConstructor<H2CPoint<T>> {\n  fromAffine(ap: AffinePoint<T>): H2CPoint<T>;\n}\n\nexport type MapToCurve<T> = (scalar: bigint[]) => AffinePoint<T>;\n\n// Separated from initialization opts, so users won't accidentally change per-curve parameters\n// (changing DST is ok!)\nexport type htfBasicOpts = { DST: UnicodeOrBytes };\nexport type H2CMethod<T> = (msg: Uint8Array, options?: htfBasicOpts) => H2CPoint<T>;\n// TODO: remove\nexport type HTFMethod<T> = H2CMethod<T>;\nexport type MapMethod<T> = (scalars: bigint[]) => H2CPoint<T>;\n/**\n * RFC 9380 methods, with cofactor clearing. See https://www.rfc-editor.org/rfc/rfc9380#section-3.\n *\n * * hashToCurve: `map(hash(input))`, encodes RANDOM bytes to curve (WITH hashing)\n * * encodeToCurve: `map(hash(input))`, encodes NON-UNIFORM bytes to curve (WITH hashing)\n * * mapToCurve: `map(scalars)`, encodes NON-UNIFORM scalars to curve (NO hashing)\n */\nexport type H2CHasher<T> = {\n  hashToCurve: H2CMethod<T>;\n  encodeToCurve: H2CMethod<T>;\n  mapToCurve: MapMethod<T>;\n  defaults: H2COpts & { encodeDST?: UnicodeOrBytes };\n};\n// TODO: remove\nexport type Hasher<T> = H2CHasher<T>;\n\n/** Creates hash-to-curve methods from EC Point and mapToCurve function. See {@link H2CHasher}. */\nexport function createHasher<T>(\n  Point: H2CPointConstructor<T>,\n  mapToCurve: MapToCurve<T>,\n  defaults: H2COpts & { encodeDST?: UnicodeOrBytes }\n): H2CHasher<T> {\n  if (typeof mapToCurve !== 'function') throw new Error('mapToCurve() must be defined');\n  function map(num: bigint[]) {\n    return Point.fromAffine(mapToCurve(num));\n  }\n  function clear(initial: H2CPoint<T>) {\n    const P = initial.clearCofactor();\n    if (P.equals(Point.ZERO)) return Point.ZERO; // zero will throw in assert\n    P.assertValidity();\n    return P;\n  }\n\n  return {\n    defaults,\n    hashToCurve(msg: Uint8Array, options?: htfBasicOpts): H2CPoint<T> {\n      const dst = defaults.DST ? defaults.DST : {};\n      const opts = Object.assign({}, defaults, dst, options);\n      const u = hash_to_field(msg, 2, opts);\n      const u0 = map(u[0]);\n      const u1 = map(u[1]);\n      return clear(u0.add(u1));\n    },\n    encodeToCurve(msg: Uint8Array, options?: htfBasicOpts): H2CPoint<T> {\n      const dst = defaults.encodeDST ? defaults.encodeDST : {};\n      const opts = Object.assign({}, defaults, dst, options);\n      const u = hash_to_field(msg, 1, opts);\n      return clear(map(u[0]));\n    },\n    /** See {@link H2CHasher} */\n    mapToCurve(scalars: bigint[]): H2CPoint<T> {\n      if (!Array.isArray(scalars)) throw new Error('expected array of bigints');\n      for (const i of scalars)\n        if (typeof i !== 'bigint') throw new Error('expected array of bigints');\n      return clear(map(scalars));\n    },\n  };\n}\n", "/**\n * SECG secp256k1. See [pdf](https://www.secg.org/sec2-v2.pdf).\n *\n * Belongs to Koblitz curves: it has efficiently-computable GLV endomorphism ψ,\n * check out {@link EndomorphismOpts}. Seems to be rigid (not backdoored).\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { sha256 } from '@noble/hashes/sha2.js';\nimport { randomBytes } from '@noble/hashes/utils.js';\nimport { createCurve, type CurveFnWithCreate } from './_shortw_utils.ts';\nimport {\n  createHasher,\n  type H2CHasher,\n  type H2CMethod,\n  isogenyMap,\n} from './abstract/hash-to-curve.ts';\nimport { Field, mod, pow2 } from './abstract/modular.ts';\nimport {\n  type EndomorphismOpts,\n  mapToCurveSimpleSWU,\n  type ProjPointType as PointType,\n  type WeierstrassOpts,\n} from './abstract/weierstrass.ts';\nimport type { Hex, Priv<PERSON><PERSON> } from './utils.ts';\nimport {\n  aInRange,\n  bytesToNumberBE,\n  concatBytes,\n  ensureBytes,\n  inRange,\n  numberToBytesBE,\n} from './utils.ts';\n\n// Seems like generator was produced from some seed:\n// `Point.BASE.multiply(Point.Fn.inv(2n, N)).toAffine().x`\n// // gives short x 0x3b78ce563f89a0ed9414f5aa28ad0d96d6795f9c63n\nconst secp256k1_CURVE: WeierstrassOpts<bigint> = {\n  p: BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f'),\n  n: BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141'),\n  h: BigInt(1),\n  a: BigInt(0),\n  b: BigInt(7),\n  Gx: BigInt('0x79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798'),\n  Gy: BigInt('0x483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8'),\n};\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a: bigint, b: bigint) => (a + b / _2n) / b;\n\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y: bigint): bigint {\n  const P = secp256k1_CURVE.p;\n  // prettier-ignore\n  const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n  // prettier-ignore\n  const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n  const b2 = (y * y * y) % P; // x^3, 11\n  const b3 = (b2 * b2 * y) % P; // x^7\n  const b6 = (pow2(b3, _3n, P) * b3) % P;\n  const b9 = (pow2(b6, _3n, P) * b3) % P;\n  const b11 = (pow2(b9, _2n, P) * b2) % P;\n  const b22 = (pow2(b11, _11n, P) * b11) % P;\n  const b44 = (pow2(b22, _22n, P) * b22) % P;\n  const b88 = (pow2(b44, _44n, P) * b44) % P;\n  const b176 = (pow2(b88, _88n, P) * b88) % P;\n  const b220 = (pow2(b176, _44n, P) * b44) % P;\n  const b223 = (pow2(b220, _3n, P) * b3) % P;\n  const t1 = (pow2(b223, _23n, P) * b22) % P;\n  const t2 = (pow2(t1, _6n, P) * b2) % P;\n  const root = pow2(t2, _2n, P);\n  if (!Fpk1.eql(Fpk1.sqr(root), y)) throw new Error('Cannot find square root');\n  return root;\n}\n\nconst Fpk1 = Field(secp256k1_CURVE.p, undefined, undefined, { sqrt: sqrtMod });\n\n/**\n * secp256k1 curve, ECDSA and ECDH methods.\n *\n * Field: `2n**256n - 2n**32n - 2n**9n - 2n**8n - 2n**7n - 2n**6n - 2n**4n - 1n`\n *\n * @example\n * ```js\n * import { secp256k1 } from '@noble/curves/secp256k1';\n * const priv = secp256k1.utils.randomPrivateKey();\n * const pub = secp256k1.getPublicKey(priv);\n * const msg = new Uint8Array(32).fill(1); // message hash (not message) in ecdsa\n * const sig = secp256k1.sign(msg, priv); // `{prehash: true}` option is available\n * const isValid = secp256k1.verify(sig, msg, pub) === true;\n * ```\n */\nexport const secp256k1: CurveFnWithCreate = createCurve(\n  {\n    ...secp256k1_CURVE,\n    Fp: Fpk1,\n    lowS: true, // Allow only low-S signatures by default in sign() and verify()\n    endo: {\n      // Endomorphism, see above\n      beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n      splitScalar: (k: bigint) => {\n        const n = secp256k1_CURVE.n;\n        const a1 = BigInt('0x3086d221a7d46bcde86c90e49284eb15');\n        const b1 = -_1n * BigInt('0xe4437ed6010e88286f547fa90abfe4c3');\n        const a2 = BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8');\n        const b2 = a1;\n        const POW_2_128 = BigInt('0x100000000000000000000000000000000'); // (2n**128n).toString(16)\n\n        const c1 = divNearest(b2 * k, n);\n        const c2 = divNearest(-b1 * k, n);\n        let k1 = mod(k - c1 * a1 - c2 * a2, n);\n        let k2 = mod(-c1 * b1 - c2 * b2, n);\n        const k1neg = k1 > POW_2_128;\n        const k2neg = k2 > POW_2_128;\n        if (k1neg) k1 = n - k1;\n        if (k2neg) k2 = n - k2;\n        if (k1 > POW_2_128 || k2 > POW_2_128) {\n          throw new Error('splitScalar: Endomorphism failed, k=' + k);\n        }\n        return { k1neg, k1, k2neg, k2 };\n      },\n    } satisfies EndomorphismOpts,\n  },\n  sha256\n);\n\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES: { [tag: string]: Uint8Array } = {};\nfunction taggedHash(tag: string, ...messages: Uint8Array[]): Uint8Array {\n  let tagP = TAGGED_HASH_PREFIXES[tag];\n  if (tagP === undefined) {\n    const tagH = sha256(Uint8Array.from(tag, (c) => c.charCodeAt(0)));\n    tagP = concatBytes(tagH, tagH);\n    TAGGED_HASH_PREFIXES[tag] = tagP;\n  }\n  return sha256(concatBytes(tagP, ...messages));\n}\n\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point: PointType<bigint>) => point.toBytes(true).slice(1);\nconst numTo32b = (n: bigint) => numberToBytesBE(n, 32);\nconst modP = (x: bigint) => mod(x, secp256k1_CURVE.p);\nconst modN = (x: bigint) => mod(x, secp256k1_CURVE.n);\nconst Point = /* @__PURE__ */ (() => secp256k1.Point)();\nconst hasEven = (y: bigint) => y % _2n === _0n;\n\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv: PrivKey) {\n  let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n  let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n  const scalar = hasEven(p.y) ? d_ : modN(-d_);\n  return { scalar: scalar, bytes: pointToBytes(p) };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x: bigint): PointType<bigint> {\n  aInRange('x', x, _1n, secp256k1_CURVE.p); // Fail if x ≥ p.\n  const xx = modP(x * x);\n  const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n  let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n  if (!hasEven(y)) y = modP(-y); // Return the unique point P such that x(P) = x and\n  const p = Point.fromAffine({ x, y }); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n  p.assertValidity();\n  return p;\n}\nconst num = bytesToNumberBE;\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args: Uint8Array[]): bigint {\n  return modN(num(taggedHash('BIP0340/challenge', ...args)));\n}\n\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(privateKey: Hex): Uint8Array {\n  return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(\n  message: Hex,\n  privateKey: PrivKey,\n  auxRand: Hex = randomBytes(32)\n): Uint8Array {\n  const m = ensureBytes('message', message);\n  const { bytes: px, scalar: d } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n  const a = ensureBytes('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n  const t = numTo32b(d ^ num(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n  const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n  const k_ = modN(num(rand)); // Let k' = int(rand) mod n\n  if (k_ === _0n) throw new Error('sign failed: k is zero'); // Fail if k' = 0.\n  const { bytes: rx, scalar: k } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n  const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n  const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n  sig.set(rx, 0);\n  sig.set(numTo32b(modN(k + e * d)), 32);\n  // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n  if (!schnorrVerify(sig, m, px)) throw new Error('sign: Invalid signature produced');\n  return sig;\n}\n\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature: Hex, message: Hex, publicKey: Hex): boolean {\n  const sig = ensureBytes('signature', signature, 64);\n  const m = ensureBytes('message', message);\n  const pub = ensureBytes('publicKey', publicKey, 32);\n  try {\n    const P = lift_x(num(pub)); // P = lift_x(int(pk)); fail if that fails\n    const r = num(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n    if (!inRange(r, _1n, secp256k1_CURVE.p)) return false;\n    const s = num(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n    if (!inRange(s, _1n, secp256k1_CURVE.n)) return false;\n    const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n    // R = s⋅G - e⋅P, where -eP == (n-e)P\n    const R = Point.BASE.multiplyUnsafe(s).add(P.multiplyUnsafe(modN(-e)));\n    const { x, y } = R.toAffine();\n    // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n    if (R.is0() || !hasEven(y) || x !== r) return false;\n    return true;\n  } catch (error) {\n    return false;\n  }\n}\n\nexport type SecpSchnorr = {\n  getPublicKey: typeof schnorrGetPublicKey;\n  sign: typeof schnorrSign;\n  verify: typeof schnorrVerify;\n  utils: {\n    randomPrivateKey: () => Uint8Array;\n    lift_x: typeof lift_x;\n    pointToBytes: (point: PointType<bigint>) => Uint8Array;\n    numberToBytesBE: typeof numberToBytesBE;\n    bytesToNumberBE: typeof bytesToNumberBE;\n    taggedHash: typeof taggedHash;\n    mod: typeof mod;\n  };\n};\n/**\n * Schnorr signatures over secp256k1.\n * https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n * @example\n * ```js\n * import { schnorr } from '@noble/curves/secp256k1';\n * const priv = schnorr.utils.randomPrivateKey();\n * const pub = schnorr.getPublicKey(priv);\n * const msg = new TextEncoder().encode('hello');\n * const sig = schnorr.sign(msg, priv);\n * const isValid = schnorr.verify(sig, msg, pub);\n * ```\n */\nexport const schnorr: SecpSchnorr = /* @__PURE__ */ (() => ({\n  getPublicKey: schnorrGetPublicKey,\n  sign: schnorrSign,\n  verify: schnorrVerify,\n  utils: {\n    randomPrivateKey: secp256k1.utils.randomPrivateKey,\n    lift_x,\n    pointToBytes,\n    numberToBytesBE,\n    bytesToNumberBE,\n    taggedHash,\n    mod,\n  },\n}))();\n\nconst isoMap = /* @__PURE__ */ (() =>\n  isogenyMap(\n    Fpk1,\n    [\n      // xNum\n      [\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7',\n        '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581',\n        '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262',\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c',\n      ],\n      // xDen\n      [\n        '0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b',\n        '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n      ],\n      // yNum\n      [\n        '0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c',\n        '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3',\n        '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931',\n        '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84',\n      ],\n      // yDen\n      [\n        '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b',\n        '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573',\n        '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n      ],\n    ].map((i) => i.map((j) => BigInt(j))) as [bigint[], bigint[], bigint[], bigint[]]\n  ))();\nconst mapSWU = /* @__PURE__ */ (() =>\n  mapToCurveSimpleSWU(Fpk1, {\n    A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n    B: BigInt('1771'),\n    Z: Fpk1.create(BigInt('-11')),\n  }))();\n/** Hashing / encoding to secp256k1 points / field. RFC 9380 methods. */\nexport const secp256k1_hasher: H2CHasher<bigint> = /* @__PURE__ */ (() =>\n  createHasher(\n    secp256k1.Point,\n    (scalars: bigint[]) => {\n      const { x, y } = mapSWU(Fpk1.create(scalars[0]));\n      return isoMap(x, y);\n    },\n    {\n      DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n      encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n      p: Fpk1.ORDER,\n      m: 1,\n      k: 128,\n      expand: 'xmd',\n      hash: sha256,\n    }\n  ))();\n\nexport const hashToCurve: H2CMethod<bigint> = /* @__PURE__ */ (() =>\n  secp256k1_hasher.hashToCurve)();\n\nexport const encodeToCurve: H2CMethod<bigint> = /* @__PURE__ */ (() =>\n  secp256k1_hasher.encodeToCurve)();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMM,IAAO,OAAP,cAAuC,KAAa;EAQxD,YAAY,MAAa,MAAW;AAClC,UAAK;AAJC,SAAA,WAAW;AACX,SAAA,YAAY;AAIlB,UAAM,IAAI;AACV,UAAM,MAAM,QAAQ,IAAI;AACxB,SAAK,QAAQ,KAAK,OAAM;AACxB,QAAI,OAAO,KAAK,MAAM,WAAW;AAC/B,YAAM,IAAI,MAAM,qDAAqD;AACvE,SAAK,WAAW,KAAK,MAAM;AAC3B,SAAK,YAAY,KAAK,MAAM;AAC5B,UAAM,WAAW,KAAK;AACtB,UAAM,MAAM,IAAI,WAAW,QAAQ;AAEnC,QAAI,IAAI,IAAI,SAAS,WAAW,KAAK,OAAM,EAAG,OAAO,GAAG,EAAE,OAAM,IAAK,GAAG;AACxE,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAAK,UAAI,CAAC,KAAK;AAC/C,SAAK,MAAM,OAAO,GAAG;AAErB,SAAK,QAAQ,KAAK,OAAM;AAExB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAAK,UAAI,CAAC,KAAK,KAAO;AACtD,SAAK,MAAM,OAAO,GAAG;AACrB,UAAM,GAAG;EACX;EACA,OAAO,KAAU;AACf,YAAQ,IAAI;AACZ,SAAK,MAAM,OAAO,GAAG;AACrB,WAAO;EACT;EACA,WAAW,KAAe;AACxB,YAAQ,IAAI;AACZ,WAAO,KAAK,KAAK,SAAS;AAC1B,SAAK,WAAW;AAChB,SAAK,MAAM,WAAW,GAAG;AACzB,SAAK,MAAM,OAAO,GAAG;AACrB,SAAK,MAAM,WAAW,GAAG;AACzB,SAAK,QAAO;EACd;EACA,SAAM;AACJ,UAAM,MAAM,IAAI,WAAW,KAAK,MAAM,SAAS;AAC/C,SAAK,WAAW,GAAG;AACnB,WAAO;EACT;EACA,WAAW,IAAY;AAErB,WAAA,KAAO,OAAO,OAAO,OAAO,eAAe,IAAI,GAAG,CAAA,CAAE;AACpD,UAAM,EAAE,OAAO,OAAO,UAAU,WAAW,UAAU,UAAS,IAAK;AACnE,SAAK;AACL,OAAG,WAAW;AACd,OAAG,YAAY;AACf,OAAG,WAAW;AACd,OAAG,YAAY;AACf,OAAG,QAAQ,MAAM,WAAW,GAAG,KAAK;AACpC,OAAG,QAAQ,MAAM,WAAW,GAAG,KAAK;AACpC,WAAO;EACT;EACA,QAAK;AACH,WAAO,KAAK,WAAU;EACxB;EACA,UAAO;AACL,SAAK,YAAY;AACjB,SAAK,MAAM,QAAO;AAClB,SAAK,MAAM,QAAO;EACpB;;AAaK,IAAM,OAGT,CAAC,MAAa,KAAY,YAC5B,IAAI,KAAU,MAAM,GAAG,EAAE,OAAO,OAAO,EAAE,OAAM;AACjD,KAAK,SAAS,CAAC,MAAa,QAAe,IAAI,KAAU,MAAM,GAAG;;;AC1ElE,IAAM,MAAM,OAAO,CAAC;AAApB,IAAuB,MAAM,OAAO,CAAC;AAArC,IAAwC,MAAsB,OAAO,CAAC;AAAtE,IAAyE,MAAsB,OAAO,CAAC;AAEvG,IAAM,MAAsB,OAAO,CAAC;AAApC,IAAuC,MAAsB,OAAO,CAAC;AACrE,IAAM,MAAsB,OAAO,CAAC;AAG9B,SAAU,IAAI,GAAW,GAAS;AACtC,QAAM,SAAS,IAAI;AACnB,SAAO,UAAU,MAAM,SAAS,IAAI;AACtC;AAYM,SAAU,KAAK,GAAW,OAAe,QAAc;AAC3D,MAAI,MAAM;AACV,SAAO,UAAU,KAAK;AACpB,WAAO;AACP,WAAO;EACT;AACA,SAAO;AACT;AAMM,SAAU,OAAO,QAAgB,QAAc;AACnD,MAAI,WAAW;AAAK,UAAM,IAAI,MAAM,kCAAkC;AACtE,MAAI,UAAU;AAAK,UAAM,IAAI,MAAM,4CAA4C,MAAM;AAErF,MAAI,IAAI,IAAI,QAAQ,MAAM;AAC1B,MAAI,IAAI;AAER,MAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AACnC,SAAO,MAAM,KAAK;AAEhB,UAAM,IAAI,IAAI;AACd,UAAM,IAAI,IAAI;AACd,UAAM,IAAI,IAAI,IAAI;AAClB,UAAM,IAAI,IAAI,IAAI;AAElB,QAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;EACzC;AACA,QAAM,MAAM;AACZ,MAAI,QAAQ;AAAK,UAAM,IAAI,MAAM,wBAAwB;AACzD,SAAO,IAAI,GAAG,MAAM;AACtB;AAMA,SAAS,UAAa,IAAe,GAAI;AACvC,QAAM,UAAU,GAAG,QAAQ,OAAO;AAClC,QAAM,OAAO,GAAG,IAAI,GAAG,MAAM;AAE7B,MAAI,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC;AAAG,UAAM,IAAI,MAAM,yBAAyB;AACvE,SAAO;AACT;AAEA,SAAS,UAAa,IAAe,GAAI;AACvC,QAAM,UAAU,GAAG,QAAQ,OAAO;AAClC,QAAM,KAAK,GAAG,IAAI,GAAG,GAAG;AACxB,QAAM,IAAI,GAAG,IAAI,IAAI,MAAM;AAC3B,QAAM,KAAK,GAAG,IAAI,GAAG,CAAC;AACtB,QAAM,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC;AACnC,QAAM,OAAO,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;AACzC,MAAI,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC;AAAG,UAAM,IAAI,MAAM,yBAAyB;AACvE,SAAO;AACT;AAgCM,SAAU,cAAc,GAAS;AAGrC,MAAI,IAAI,OAAO,CAAC;AAAG,UAAM,IAAI,MAAM,qCAAqC;AAExE,MAAI,IAAI,IAAI;AACZ,MAAI,IAAI;AACR,SAAO,IAAI,QAAQ,KAAK;AACtB,SAAK;AACL;EACF;AAGA,MAAI,IAAI;AACR,QAAM,MAAM,MAAM,CAAC;AACnB,SAAO,WAAW,KAAK,CAAC,MAAM,GAAG;AAG/B,QAAI,MAAM;AAAM,YAAM,IAAI,MAAM,+CAA+C;EACjF;AAEA,MAAI,MAAM;AAAG,WAAO;AAIpB,MAAI,KAAK,IAAI,IAAI,GAAG,CAAC;AACrB,QAAM,UAAU,IAAI,OAAO;AAC3B,SAAO,SAAS,YAAe,IAAe,GAAI;AAChD,QAAI,GAAG,IAAI,CAAC;AAAG,aAAO;AAEtB,QAAI,WAAW,IAAI,CAAC,MAAM;AAAG,YAAM,IAAI,MAAM,yBAAyB;AAGtE,QAAI,IAAI;AACR,QAAI,IAAI,GAAG,IAAI,GAAG,KAAK,EAAE;AACzB,QAAI,IAAI,GAAG,IAAI,GAAG,CAAC;AACnB,QAAI,IAAI,GAAG,IAAI,GAAG,MAAM;AAIxB,WAAO,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG;AACzB,UAAI,GAAG,IAAI,CAAC;AAAG,eAAO,GAAG;AACzB,UAAI,IAAI;AAGR,UAAI,QAAQ,GAAG,IAAI,CAAC;AACpB,aAAO,CAAC,GAAG,IAAI,OAAO,GAAG,GAAG,GAAG;AAC7B;AACA,gBAAQ,GAAG,IAAI,KAAK;AACpB,YAAI,MAAM;AAAG,gBAAM,IAAI,MAAM,yBAAyB;MACxD;AAGA,YAAM,WAAW,OAAO,OAAO,IAAI,IAAI,CAAC;AACxC,YAAM,IAAI,GAAG,IAAI,GAAG,QAAQ;AAG5B,UAAI;AACJ,UAAI,GAAG,IAAI,CAAC;AACZ,UAAI,GAAG,IAAI,GAAG,CAAC;AACf,UAAI,GAAG,IAAI,GAAG,CAAC;IACjB;AACA,WAAO;EACT;AACF;AAYM,SAAU,OAAO,GAAS;AAE9B,MAAI,IAAI,QAAQ;AAAK,WAAO;AAE5B,MAAI,IAAI,QAAQ;AAAK,WAAO;AAG5B,SAAO,cAAc,CAAC;AACxB;AAkDA,IAAM,eAAe;EACnB;EAAU;EAAW;EAAO;EAAO;EAAO;EAAQ;EAClD;EAAO;EAAO;EAAO;EAAO;EAAO;EACnC;EAAQ;EAAQ;EAAQ;;AAEpB,SAAU,cAAiB,OAAgB;AAC/C,QAAM,UAAU;IACd,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;;AAER,QAAM,OAAO,aAAa,OAAO,CAAC,KAAK,QAAe;AACpD,QAAI,GAAG,IAAI;AACX,WAAO;EACT,GAAG,OAAO;AACV,kBAAgB,OAAO,IAAI;AAI3B,SAAO;AACT;AAQM,SAAU,MAAS,IAAeA,MAAQ,OAAa;AAC3D,MAAI,QAAQ;AAAK,UAAM,IAAI,MAAM,yCAAyC;AAC1E,MAAI,UAAU;AAAK,WAAO,GAAG;AAC7B,MAAI,UAAU;AAAK,WAAOA;AAC1B,MAAI,IAAI,GAAG;AACX,MAAI,IAAIA;AACR,SAAO,QAAQ,KAAK;AAClB,QAAI,QAAQ;AAAK,UAAI,GAAG,IAAI,GAAG,CAAC;AAChC,QAAI,GAAG,IAAI,CAAC;AACZ,cAAU;EACZ;AACA,SAAO;AACT;AAOM,SAAU,cAAiB,IAAe,MAAW,WAAW,OAAK;AACzE,QAAM,WAAW,IAAI,MAAM,KAAK,MAAM,EAAE,KAAK,WAAW,GAAG,OAAO,MAAS;AAE3E,QAAM,gBAAgB,KAAK,OAAO,CAAC,KAAKA,MAAK,MAAK;AAChD,QAAI,GAAG,IAAIA,IAAG;AAAG,aAAO;AACxB,aAAS,CAAC,IAAI;AACd,WAAO,GAAG,IAAI,KAAKA,IAAG;EACxB,GAAG,GAAG,GAAG;AAET,QAAM,cAAc,GAAG,IAAI,aAAa;AAExC,OAAK,YAAY,CAAC,KAAKA,MAAK,MAAK;AAC/B,QAAI,GAAG,IAAIA,IAAG;AAAG,aAAO;AACxB,aAAS,CAAC,IAAI,GAAG,IAAI,KAAK,SAAS,CAAC,CAAC;AACrC,WAAO,GAAG,IAAI,KAAKA,IAAG;EACxB,GAAG,WAAW;AACd,SAAO;AACT;AAgBM,SAAU,WAAc,IAAe,GAAI;AAG/C,QAAM,UAAU,GAAG,QAAQ,OAAO;AAClC,QAAM,UAAU,GAAG,IAAI,GAAG,MAAM;AAChC,QAAM,MAAM,GAAG,IAAI,SAAS,GAAG,GAAG;AAClC,QAAM,OAAO,GAAG,IAAI,SAAS,GAAG,IAAI;AACpC,QAAM,KAAK,GAAG,IAAI,SAAS,GAAG,IAAI,GAAG,GAAG,CAAC;AACzC,MAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;AAAI,UAAM,IAAI,MAAM,gCAAgC;AAC1E,SAAO,MAAM,IAAI,OAAO,IAAI;AAC9B;AAUM,SAAU,QAAQ,GAAW,YAAmB;AAEpD,MAAI,eAAe;AAAW,YAAQ,UAAU;AAChD,QAAM,cAAc,eAAe,SAAY,aAAa,EAAE,SAAS,CAAC,EAAE;AAC1E,QAAM,cAAc,KAAK,KAAK,cAAc,CAAC;AAC7C,SAAO,EAAE,YAAY,aAAa,YAAW;AAC/C;AAwBM,SAAU,MACd,OACA,cACA,OAAO,OACP,OAA0B,CAAA,GAAE;AAE5B,MAAI,SAAS;AAAK,UAAM,IAAI,MAAM,4CAA4C,KAAK;AACnF,MAAI,cAAkC;AACtC,MAAI,QAA4B;AAChC,MAAI,OAAO,iBAAiB,YAAY,gBAAgB,MAAM;AAC5D,QAAI,KAAK,QAAQ;AAAM,YAAM,IAAI,MAAM,sCAAsC;AAC7E,UAAM,QAAQ;AACd,QAAI,MAAM;AAAM,oBAAc,MAAM;AACpC,QAAI,MAAM;AAAM,cAAQ,MAAM;AAC9B,QAAI,OAAO,MAAM,SAAS;AAAW,aAAO,MAAM;EACpD,OAAO;AACL,QAAI,OAAO,iBAAiB;AAAU,oBAAc;AACpD,QAAI,KAAK;AAAM,cAAQ,KAAK;EAC9B;AACA,QAAM,EAAE,YAAY,MAAM,aAAa,MAAK,IAAK,QAAQ,OAAO,WAAW;AAC3E,MAAI,QAAQ;AAAM,UAAM,IAAI,MAAM,gDAAgD;AAClF,MAAI;AACJ,QAAM,IAAuB,OAAO,OAAO;IACzC;IACA;IACA;IACA;IACA,MAAM,QAAQ,IAAI;IAClB,MAAM;IACN,KAAK;IACL,QAAQ,CAACC,SAAQ,IAAIA,MAAK,KAAK;IAC/B,SAAS,CAACA,SAAO;AACf,UAAI,OAAOA,SAAQ;AACjB,cAAM,IAAI,MAAM,iDAAiD,OAAOA,IAAG;AAC7E,aAAO,OAAOA,QAAOA,OAAM;IAC7B;IACA,KAAK,CAACA,SAAQA,SAAQ;;IAEtB,aAAa,CAACA,SAAgB,CAAC,EAAE,IAAIA,IAAG,KAAK,EAAE,QAAQA,IAAG;IAC1D,OAAO,CAACA,UAASA,OAAM,SAAS;IAChC,KAAK,CAACA,SAAQ,IAAI,CAACA,MAAK,KAAK;IAC7B,KAAK,CAAC,KAAK,QAAQ,QAAQ;IAE3B,KAAK,CAACA,SAAQ,IAAIA,OAAMA,MAAK,KAAK;IAClC,KAAK,CAAC,KAAK,QAAQ,IAAI,MAAM,KAAK,KAAK;IACvC,KAAK,CAAC,KAAK,QAAQ,IAAI,MAAM,KAAK,KAAK;IACvC,KAAK,CAAC,KAAK,QAAQ,IAAI,MAAM,KAAK,KAAK;IACvC,KAAK,CAACA,MAAK,UAAU,MAAM,GAAGA,MAAK,KAAK;IACxC,KAAK,CAAC,KAAK,QAAQ,IAAI,MAAM,OAAO,KAAK,KAAK,GAAG,KAAK;;IAGtD,MAAM,CAACA,SAAQA,OAAMA;IACrB,MAAM,CAAC,KAAK,QAAQ,MAAM;IAC1B,MAAM,CAAC,KAAK,QAAQ,MAAM;IAC1B,MAAM,CAAC,KAAK,QAAQ,MAAM;IAE1B,KAAK,CAACA,SAAQ,OAAOA,MAAK,KAAK;IAC/B,MACE,UACC,CAAC,MAAK;AACL,UAAI,CAAC;AAAO,gBAAQ,OAAO,KAAK;AAChC,aAAO,MAAM,GAAG,CAAC;IACnB;IACF,SAAS,CAACA,SAAS,OAAO,gBAAgBA,MAAK,KAAK,IAAI,gBAAgBA,MAAK,KAAK;IAClF,WAAW,CAAC,UAAS;AACnB,UAAI,MAAM,WAAW;AACnB,cAAM,IAAI,MAAM,+BAA+B,QAAQ,iBAAiB,MAAM,MAAM;AACtF,aAAO,OAAO,gBAAgB,KAAK,IAAI,gBAAgB,KAAK;IAC9D;;IAEA,aAAa,CAAC,QAAQ,cAAc,GAAG,GAAG;;;IAG1C,MAAM,CAAC,GAAG,GAAG,MAAO,IAAI,IAAI;GAClB;AACZ,SAAO,OAAO,OAAO,CAAC;AACxB;AA0CM,SAAU,oBAAoB,YAAkB;AACpD,MAAI,OAAO,eAAe;AAAU,UAAM,IAAI,MAAM,4BAA4B;AAChF,QAAM,YAAY,WAAW,SAAS,CAAC,EAAE;AACzC,SAAO,KAAK,KAAK,YAAY,CAAC;AAChC;AASM,SAAU,iBAAiB,YAAkB;AACjD,QAAM,SAAS,oBAAoB,UAAU;AAC7C,SAAO,SAAS,KAAK,KAAK,SAAS,CAAC;AACtC;AAeM,SAAU,eAAe,KAAiB,YAAoB,OAAO,OAAK;AAC9E,QAAM,MAAM,IAAI;AAChB,QAAM,WAAW,oBAAoB,UAAU;AAC/C,QAAM,SAAS,iBAAiB,UAAU;AAE1C,MAAI,MAAM,MAAM,MAAM,UAAU,MAAM;AACpC,UAAM,IAAI,MAAM,cAAc,SAAS,+BAA+B,GAAG;AAC3E,QAAMC,OAAM,OAAO,gBAAgB,GAAG,IAAI,gBAAgB,GAAG;AAE7D,QAAM,UAAU,IAAIA,MAAK,aAAa,GAAG,IAAI;AAC7C,SAAO,OAAO,gBAAgB,SAAS,QAAQ,IAAI,gBAAgB,SAAS,QAAQ;AACtF;;;AChiBA,IAAMC,OAAM,OAAO,CAAC;AACpB,IAAMC,OAAM,OAAO,CAAC;AA4Bd,SAAU,SAA6B,WAAoB,MAAO;AACtE,QAAM,MAAM,KAAK,OAAM;AACvB,SAAO,YAAY,MAAM;AAC3B;AAQM,SAAU,WACd,GACA,UACA,QAAW;AAEX,QAAM,OAAO,aAAa,OAAO,CAAC,MAAW,EAAE,KAAK,CAAC,MAAW,EAAE;AAClE,QAAM,QAAQ,cAAc,EAAE,IAAI,OAAO,IAAI,IAAI,CAAC;AAElD,QAAM,UAAU,OAAO,IAAI,CAAC,GAAG,MAAM,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;AACzD,SAAO,QAAQ,IAAI,EAAE,UAAU;AACjC;AAEA,SAAS,UAAU,GAAW,MAAY;AACxC,MAAI,CAAC,OAAO,cAAc,CAAC,KAAK,KAAK,KAAK,IAAI;AAC5C,UAAM,IAAI,MAAM,uCAAuC,OAAO,cAAc,CAAC;AACjF;AAWA,SAAS,UAAU,GAAW,YAAkB;AAC9C,YAAU,GAAG,UAAU;AACvB,QAAM,UAAU,KAAK,KAAK,aAAa,CAAC,IAAI;AAC5C,QAAM,aAAa,MAAM,IAAI;AAC7B,QAAM,YAAY,KAAK;AACvB,QAAM,OAAO,QAAQ,CAAC;AACtB,QAAM,UAAU,OAAO,CAAC;AACxB,SAAO,EAAE,SAAS,YAAY,MAAM,WAAW,QAAO;AACxD;AAEA,SAAS,YAAY,GAAW,QAAgB,OAAY;AAC1D,QAAM,EAAE,YAAY,MAAM,WAAW,QAAO,IAAK;AACjD,MAAI,QAAQ,OAAO,IAAI,IAAI;AAC3B,MAAI,QAAQ,KAAK;AAQjB,MAAI,QAAQ,YAAY;AAEtB,aAAS;AACT,aAASA;EACX;AACA,QAAM,cAAc,SAAS;AAC7B,QAAM,SAAS,cAAc,KAAK,IAAI,KAAK,IAAI;AAC/C,QAAM,SAAS,UAAU;AACzB,QAAM,QAAQ,QAAQ;AACtB,QAAM,SAAS,SAAS,MAAM;AAC9B,QAAM,UAAU;AAChB,SAAO,EAAE,OAAO,QAAQ,QAAQ,OAAO,QAAQ,QAAO;AACxD;AAEA,SAAS,kBAAkB,QAAe,GAAM;AAC9C,MAAI,CAAC,MAAM,QAAQ,MAAM;AAAG,UAAM,IAAI,MAAM,gBAAgB;AAC5D,SAAO,QAAQ,CAAC,GAAG,MAAK;AACtB,QAAI,EAAE,aAAa;AAAI,YAAM,IAAI,MAAM,4BAA4B,CAAC;EACtE,CAAC;AACH;AACA,SAAS,mBAAmB,SAAgB,OAAU;AACpD,MAAI,CAAC,MAAM,QAAQ,OAAO;AAAG,UAAM,IAAI,MAAM,2BAA2B;AACxE,UAAQ,QAAQ,CAAC,GAAG,MAAK;AACvB,QAAI,CAAC,MAAM,QAAQ,CAAC;AAAG,YAAM,IAAI,MAAM,6BAA6B,CAAC;EACvE,CAAC;AACH;AAKA,IAAM,mBAAmB,oBAAI,QAAO;AACpC,IAAM,mBAAmB,oBAAI,QAAO;AAEpC,SAAS,KAAK,GAAM;AAClB,SAAO,iBAAiB,IAAI,CAAC,KAAK;AACpC;AAEA,SAAS,QAAQ,GAAS;AACxB,MAAI,MAAMD;AAAK,UAAM,IAAI,MAAM,cAAc;AAC/C;AA6BM,SAAU,KAAyB,GAAwB,MAAY;AAC3E,SAAO;IACL,iBAAiB;IAEjB,eAAe,KAAM;AACnB,aAAO,KAAK,GAAG,MAAM;IACvB;;IAGA,aAAa,KAAQ,GAAW,IAAI,EAAE,MAAI;AACxC,UAAI,IAAO;AACX,aAAO,IAAIA,MAAK;AACd,YAAI,IAAIC;AAAK,cAAI,EAAE,IAAI,CAAC;AACxB,YAAI,EAAE,OAAM;AACZ,cAAMA;MACR;AACA,aAAO;IACT;;;;;;;;;;;;;IAcA,iBAAiB,KAAQ,GAAS;AAChC,YAAM,EAAE,SAAS,WAAU,IAAK,UAAU,GAAG,IAAI;AACjD,YAAM,SAAc,CAAA;AACpB,UAAI,IAAO;AACX,UAAI,OAAO;AACX,eAAS,SAAS,GAAG,SAAS,SAAS,UAAU;AAC/C,eAAO;AACP,eAAO,KAAK,IAAI;AAEhB,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,iBAAO,KAAK,IAAI,CAAC;AACjB,iBAAO,KAAK,IAAI;QAClB;AACA,YAAI,KAAK,OAAM;MACjB;AACA,aAAO;IACT;;;;;;;;IASA,KAAK,GAAW,aAAkB,GAAS;AAOzC,UAAI,IAAI,EAAE;AACV,UAAI,IAAI,EAAE;AAMV,YAAM,KAAK,UAAU,GAAG,IAAI;AAC5B,eAAS,SAAS,GAAG,SAAS,GAAG,SAAS,UAAU;AAElD,cAAM,EAAE,OAAO,QAAQ,QAAQ,OAAO,QAAQ,QAAO,IAAK,YAAY,GAAG,QAAQ,EAAE;AACnF,YAAI;AACJ,YAAI,QAAQ;AAGV,cAAI,EAAE,IAAI,SAAS,QAAQ,YAAY,OAAO,CAAC,CAAC;QAClD,OAAO;AAEL,cAAI,EAAE,IAAI,SAAS,OAAO,YAAY,MAAM,CAAC,CAAC;QAChD;MACF;AACA,cAAQ,CAAC;AAIT,aAAO,EAAE,GAAG,EAAC;IACf;;;;;;;;;IAUA,WAAW,GAAW,aAAkB,GAAW,MAAS,EAAE,MAAI;AAChE,YAAM,KAAK,UAAU,GAAG,IAAI;AAC5B,eAAS,SAAS,GAAG,SAAS,GAAG,SAAS,UAAU;AAClD,YAAI,MAAMD;AAAK;AACf,cAAM,EAAE,OAAO,QAAQ,QAAQ,MAAK,IAAK,YAAY,GAAG,QAAQ,EAAE;AAClE,YAAI;AACJ,YAAI,QAAQ;AAGV;QACF,OAAO;AACL,gBAAM,OAAO,YAAY,MAAM;AAC/B,gBAAM,IAAI,IAAI,QAAQ,KAAK,OAAM,IAAK,IAAI;QAC5C;MACF;AACA,cAAQ,CAAC;AACT,aAAO;IACT;IAEA,eAAe,GAAW,GAAM,WAAqB;AAEnD,UAAI,OAAO,iBAAiB,IAAI,CAAC;AACjC,UAAI,CAAC,MAAM;AACT,eAAO,KAAK,iBAAiB,GAAG,CAAC;AACjC,YAAI,MAAM,GAAG;AAEX,cAAI,OAAO,cAAc;AAAY,mBAAO,UAAU,IAAI;AAC1D,2BAAiB,IAAI,GAAG,IAAI;QAC9B;MACF;AACA,aAAO;IACT;IAEA,WAAW,GAAM,GAAW,WAAqB;AAC/C,YAAM,IAAI,KAAK,CAAC;AAChB,aAAO,KAAK,KAAK,GAAG,KAAK,eAAe,GAAG,GAAG,SAAS,GAAG,CAAC;IAC7D;IAEA,iBAAiB,GAAM,GAAW,WAAuB,MAAQ;AAC/D,YAAM,IAAI,KAAK,CAAC;AAChB,UAAI,MAAM;AAAG,eAAO,KAAK,aAAa,GAAG,GAAG,IAAI;AAChD,aAAO,KAAK,WAAW,GAAG,KAAK,eAAe,GAAG,GAAG,SAAS,GAAG,GAAG,IAAI;IACzE;;;;IAMA,cAAc,GAAM,GAAS;AAC3B,gBAAU,GAAG,IAAI;AACjB,uBAAiB,IAAI,GAAG,CAAC;AACzB,uBAAiB,OAAO,CAAC;IAC3B;;AAEJ;AAMM,SAAU,cACd,GACA,OACA,IACA,IAAU;AAEV,MAAI,MAAM;AACV,MAAI,KAAK,EAAE;AACX,MAAI,KAAK,EAAE;AACX,SAAO,KAAKA,QAAO,KAAKA,MAAK;AAC3B,QAAI,KAAKC;AAAK,WAAK,GAAG,IAAI,GAAG;AAC7B,QAAI,KAAKA;AAAK,WAAK,GAAG,IAAI,GAAG;AAC7B,UAAM,IAAI,OAAM;AAChB,WAAOA;AACP,WAAOA;EACT;AACA,SAAO,EAAE,IAAI,GAAE;AACjB;AAYM,SAAU,UACd,GACA,QACA,QACA,SAAiB;AAQjB,oBAAkB,QAAQ,CAAC;AAC3B,qBAAmB,SAAS,MAAM;AAClC,QAAM,UAAU,OAAO;AACvB,QAAM,UAAU,QAAQ;AACxB,MAAI,YAAY;AAAS,UAAM,IAAI,MAAM,qDAAqD;AAE9F,QAAM,OAAO,EAAE;AACf,QAAM,QAAQ,OAAO,OAAO,OAAO,CAAC;AACpC,MAAI,aAAa;AACjB,MAAI,QAAQ;AAAI,iBAAa,QAAQ;WAC5B,QAAQ;AAAG,iBAAa,QAAQ;WAChC,QAAQ;AAAG,iBAAa;AACjC,QAAM,OAAO,QAAQ,UAAU;AAC/B,QAAM,UAAU,IAAI,MAAM,OAAO,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI;AACrD,QAAM,WAAW,KAAK,OAAO,OAAO,OAAO,KAAK,UAAU,IAAI;AAC9D,MAAI,MAAM;AACV,WAAS,IAAI,UAAU,KAAK,GAAG,KAAK,YAAY;AAC9C,YAAQ,KAAK,IAAI;AACjB,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,YAAM,SAAS,QAAQ,CAAC;AACxB,YAAMC,SAAQ,OAAQ,UAAU,OAAO,CAAC,IAAK,IAAI;AACjD,cAAQA,MAAK,IAAI,QAAQA,MAAK,EAAE,IAAI,OAAO,CAAC,CAAC;IAC/C;AACA,QAAI,OAAO;AAEX,aAAS,IAAI,QAAQ,SAAS,GAAG,OAAO,MAAM,IAAI,GAAG,KAAK;AACxD,aAAO,KAAK,IAAI,QAAQ,CAAC,CAAC;AAC1B,aAAO,KAAK,IAAI,IAAI;IACtB;AACA,UAAM,IAAI,IAAI,IAAI;AAClB,QAAI,MAAM;AAAG,eAAS,IAAI,GAAG,IAAI,YAAY;AAAK,cAAM,IAAI,OAAM;EACpE;AACA,SAAO;AACT;AA+IA,SAAS,YAAe,OAAe,OAAiB;AACtD,MAAI,OAAO;AACT,QAAI,MAAM,UAAU;AAAO,YAAM,IAAI,MAAM,gDAAgD;AAC3F,kBAAc,KAAK;AACnB,WAAO;EACT,OAAO;AACL,WAAO,MAAM,KAAK;EACpB;AACF;AAGM,SAAU,mBACd,MACA,OACA,YAA8B,CAAA,GAAE;AAEhC,MAAI,CAAC,SAAS,OAAO,UAAU;AAAU,UAAM,IAAI,MAAM,kBAAkB,IAAI,eAAe;AAC9F,aAAW,KAAK,CAAC,KAAK,KAAK,GAAG,GAAY;AACxC,UAAM,MAAM,MAAM,CAAC;AACnB,QAAI,EAAE,OAAO,QAAQ,YAAY,MAAMC;AACrC,YAAM,IAAI,MAAM,SAAS,CAAC,0BAA0B;EACxD;AACA,QAAM,KAAK,YAAY,MAAM,GAAG,UAAU,EAAE;AAC5C,QAAM,KAAK,YAAY,MAAM,GAAG,UAAU,EAAE;AAC5C,QAAM,KAAgB,SAAS,gBAAgB,MAAM;AACrD,QAAM,SAAS,CAAC,MAAM,MAAM,KAAK,EAAE;AACnC,aAAW,KAAK,QAAQ;AAEtB,QAAI,CAAC,GAAG,QAAQ,MAAM,CAAC,CAAC;AACtB,YAAM,IAAI,MAAM,SAAS,CAAC,0CAA0C;EACxE;AACA,SAAO,EAAE,IAAI,GAAE;AACjB;;;AChcA,SAAS,mBAAmB,MAAwB;AAClD,MAAI,KAAK,SAAS;AAAW,UAAM,QAAQ,KAAK,IAAI;AACpD,MAAI,KAAK,YAAY;AAAW,UAAM,WAAW,KAAK,OAAO;AAC/D;AAgKM,IAAO,SAAP,cAAsB,MAAK;EAC/B,YAAY,IAAI,IAAE;AAChB,UAAM,CAAC;EACT;;AA6BK,IAAM,MAAY;;EAEvB,KAAK;;EAEL,MAAM;IACJ,QAAQ,CAAC,KAAa,SAAwB;AAC5C,YAAM,EAAE,KAAK,EAAC,IAAK;AACnB,UAAI,MAAM,KAAK,MAAM;AAAK,cAAM,IAAI,EAAE,uBAAuB;AAC7D,UAAI,KAAK,SAAS;AAAG,cAAM,IAAI,EAAE,2BAA2B;AAC5D,YAAM,UAAU,KAAK,SAAS;AAC9B,YAAM,MAAM,oBAAoB,OAAO;AACvC,UAAK,IAAI,SAAS,IAAK;AAAa,cAAM,IAAI,EAAE,sCAAsC;AAEtF,YAAM,SAAS,UAAU,MAAM,oBAAqB,IAAI,SAAS,IAAK,GAAW,IAAI;AACrF,YAAM,IAAI,oBAAoB,GAAG;AACjC,aAAO,IAAI,SAAS,MAAM;IAC5B;;IAEA,OAAO,KAAa,MAAgB;AAClC,YAAM,EAAE,KAAK,EAAC,IAAK;AACnB,UAAI,MAAM;AACV,UAAI,MAAM,KAAK,MAAM;AAAK,cAAM,IAAI,EAAE,uBAAuB;AAC7D,UAAI,KAAK,SAAS,KAAK,KAAK,KAAK,MAAM;AAAK,cAAM,IAAI,EAAE,uBAAuB;AAC/E,YAAM,QAAQ,KAAK,KAAK;AACxB,YAAM,SAAS,CAAC,EAAE,QAAQ;AAC1B,UAAI,SAAS;AACb,UAAI,CAAC;AAAQ,iBAAS;WACjB;AAEH,cAAM,SAAS,QAAQ;AACvB,YAAI,CAAC;AAAQ,gBAAM,IAAI,EAAE,mDAAmD;AAC5E,YAAI,SAAS;AAAG,gBAAM,IAAI,EAAE,0CAA0C;AACtE,cAAM,cAAc,KAAK,SAAS,KAAK,MAAM,MAAM;AACnD,YAAI,YAAY,WAAW;AAAQ,gBAAM,IAAI,EAAE,uCAAuC;AACtF,YAAI,YAAY,CAAC,MAAM;AAAG,gBAAM,IAAI,EAAE,sCAAsC;AAC5E,mBAAW,KAAK;AAAa,mBAAU,UAAU,IAAK;AACtD,eAAO;AACP,YAAI,SAAS;AAAK,gBAAM,IAAI,EAAE,wCAAwC;MACxE;AACA,YAAM,IAAI,KAAK,SAAS,KAAK,MAAM,MAAM;AACzC,UAAI,EAAE,WAAW;AAAQ,cAAM,IAAI,EAAE,gCAAgC;AACrE,aAAO,EAAE,GAAG,GAAG,KAAK,SAAS,MAAM,MAAM,EAAC;IAC5C;;;;;;EAMF,MAAM;IACJ,OAAOC,MAAW;AAChB,YAAM,EAAE,KAAK,EAAC,IAAK;AACnB,UAAIA,OAAMC;AAAK,cAAM,IAAI,EAAE,4CAA4C;AACvE,UAAI,MAAM,oBAAoBD,IAAG;AAEjC,UAAI,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI;AAAQ,cAAM,OAAO;AACvD,UAAI,IAAI,SAAS;AAAG,cAAM,IAAI,EAAE,gDAAgD;AAChF,aAAO;IACT;IACA,OAAO,MAAgB;AACrB,YAAM,EAAE,KAAK,EAAC,IAAK;AACnB,UAAI,KAAK,CAAC,IAAI;AAAa,cAAM,IAAI,EAAE,qCAAqC;AAC5E,UAAI,KAAK,CAAC,MAAM,KAAQ,EAAE,KAAK,CAAC,IAAI;AAClC,cAAM,IAAI,EAAE,qDAAqD;AACnE,aAAO,gBAAgB,IAAI;IAC7B;;EAEF,MAAM,KAAwB;AAE5B,UAAM,EAAE,KAAK,GAAG,MAAM,KAAK,MAAM,IAAG,IAAK;AACzC,UAAM,OAAO,YAAY,aAAa,GAAG;AACzC,UAAM,EAAE,GAAG,UAAU,GAAG,aAAY,IAAK,IAAI,OAAO,IAAM,IAAI;AAC9D,QAAI,aAAa;AAAQ,YAAM,IAAI,EAAE,6CAA6C;AAClF,UAAM,EAAE,GAAG,QAAQ,GAAG,WAAU,IAAK,IAAI,OAAO,GAAM,QAAQ;AAC9D,UAAM,EAAE,GAAG,QAAQ,GAAG,WAAU,IAAK,IAAI,OAAO,GAAM,UAAU;AAChE,QAAI,WAAW;AAAQ,YAAM,IAAI,EAAE,6CAA6C;AAChF,WAAO,EAAE,GAAG,IAAI,OAAO,MAAM,GAAG,GAAG,IAAI,OAAO,MAAM,EAAC;EACvD;EACA,WAAW,KAA6B;AACtC,UAAM,EAAE,MAAM,KAAK,MAAM,IAAG,IAAK;AACjC,UAAM,KAAK,IAAI,OAAO,GAAM,IAAI,OAAO,IAAI,CAAC,CAAC;AAC7C,UAAM,KAAK,IAAI,OAAO,GAAM,IAAI,OAAO,IAAI,CAAC,CAAC;AAC7C,UAAM,MAAM,KAAK;AACjB,WAAO,IAAI,OAAO,IAAM,GAAG;EAC7B;;AAKF,IAAMC,OAAM,OAAO,CAAC;AAApB,IAAuBC,OAAM,OAAO,CAAC;AAArC,IAAwCC,OAAM,OAAO,CAAC;AAAtD,IAAyDC,OAAM,OAAO,CAAC;AAAvE,IAA0EC,OAAM,OAAO,CAAC;AAGlF,SAAU,mBAAsB,IAAe,GAAM,GAAI;AAK7D,WAAS,oBAAoB,GAAI;AAC/B,UAAM,KAAK,GAAG,IAAI,CAAC;AACnB,UAAM,KAAK,GAAG,IAAI,IAAI,CAAC;AACvB,WAAO,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;EAC3C;AACA,SAAO;AACT;AACM,SAAU,sBACd,IACA,0BACA,gBAAwB;AAExB,QAAM,EAAE,OAAO,SAAQ,IAAK;AAE5B,WAAS,uBAAuB,KAAY;AAC1C,QAAIL;AACJ,QAAI,OAAO,QAAQ,UAAU;AAC3B,MAAAA,OAAM;IACR,OAAO;AACL,UAAI,QAAQ,YAAY,eAAe,GAAG;AAC1C,UAAI,0BAA0B;AAC5B,YAAI,CAAC,yBAAyB,SAAS,MAAM,SAAS,CAAC;AACrD,gBAAM,IAAI,MAAM,qBAAqB;AACvC,cAAM,SAAS,IAAI,WAAW,QAAQ;AACtC,eAAO,IAAI,OAAO,OAAO,SAAS,MAAM,MAAM;AAC9C,gBAAQ;MACV;AACA,UAAI;AACF,QAAAA,OAAM,GAAG,UAAU,KAAK;MAC1B,SAAS,OAAO;AACd,cAAM,IAAI,MACR,8CAA8C,QAAQ,SAAS,OAAO,GAAG,EAAE;MAE/E;IACF;AACA,QAAI;AAAgB,MAAAA,OAAM,GAAG,OAAOA,IAAG;AACvC,QAAI,CAAC,GAAG,YAAYA,IAAG;AAAG,YAAM,IAAI,MAAM,4CAA4C;AACtF,WAAOA;EACT;AACA,SAAO;AACT;AAEM,SAAU,aACd,OACA,YAAqC,CAAA,GAAE;AAEvC,QAAM,EAAE,IAAI,GAAE,IAAK,mBAAmB,eAAe,OAAO,SAAS;AACrE,QAAM,EAAE,GAAG,UAAU,GAAG,YAAW,IAAK;AACxC,kBACE,WACA,CAAA,GACA;IACE,oBAAoB;IACpB,eAAe;IACf,eAAe;IACf,WAAW;IACX,SAAS;IACT,MAAM;IACN,gBAAgB;GACjB;AAGH,QAAM,EAAE,KAAI,IAAK;AACjB,MAAI,MAAM;AAER,QACE,CAAC,GAAG,IAAI,MAAM,CAAC,KACf,OAAO,KAAK,SAAS,YACrB,OAAO,KAAK,gBAAgB,YAC5B;AACA,YAAM,IAAI,MAAM,mEAAmE;IACrF;EACF;AAEA,WAAS,+BAA4B;AACnC,QAAI,CAAC,GAAG;AAAO,YAAM,IAAI,MAAM,4DAA4D;EAC7F;AAGA,WAASM,cACP,IACA,OACA,cAAqB;AAErB,UAAM,EAAE,GAAG,EAAC,IAAK,MAAM,SAAQ;AAC/B,UAAM,KAAK,GAAG,QAAQ,CAAC;AACvB,UAAM,gBAAgB,YAAY;AAClC,QAAI,cAAc;AAChB,mCAA4B;AAC5B,YAAM,WAAW,CAAC,GAAG,MAAO,CAAC;AAC7B,aAAO,YAAY,QAAQ,QAAQ,GAAG,EAAE;IAC1C,OAAO;AACL,aAAO,YAAY,WAAW,GAAG,CAAI,GAAG,IAAI,GAAG,QAAQ,CAAC,CAAC;IAC3D;EACF;AACA,WAAS,eAAe,OAAiB;AACvC,WAAO,KAAK;AACZ,UAAM,IAAI,GAAG;AACb,UAAM,KAAK,IAAI;AACf,UAAM,KAAK,IAAI,IAAI;AACnB,UAAM,SAAS,MAAM;AACrB,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,OAAO,MAAM,SAAS,CAAC;AAE7B,QAAI,WAAW,OAAO,SAAS,KAAQ,SAAS,IAAO;AACrD,YAAM,IAAI,GAAG,UAAU,IAAI;AAC3B,UAAI,CAAC,GAAG,QAAQ,CAAC;AAAG,cAAM,IAAI,MAAM,qCAAqC;AACzE,YAAM,KAAK,oBAAoB,CAAC;AAChC,UAAI;AACJ,UAAI;AACF,YAAI,GAAG,KAAK,EAAE;MAChB,SAAS,WAAW;AAClB,cAAM,MAAM,qBAAqB,QAAQ,OAAO,UAAU,UAAU;AACpE,cAAM,IAAI,MAAM,2CAA2C,GAAG;MAChE;AACA,mCAA4B;AAC5B,YAAM,SAAS,GAAG,MAAO,CAAC;AAC1B,YAAM,aAAa,OAAO,OAAO;AACjC,UAAI,cAAc;AAAQ,YAAI,GAAG,IAAI,CAAC;AACtC,aAAO,EAAE,GAAG,EAAC;IACf,WAAW,WAAW,MAAM,SAAS,GAAM;AAEzC,YAAM,IAAI,GAAG,UAAU,KAAK,SAAS,IAAI,GAAG,IAAI,CAAC,CAAC;AAClD,YAAM,IAAI,GAAG,UAAU,KAAK,SAAS,IAAI,GAAG,IAAI,CAAC,CAAC;AAClD,UAAI,CAAC,UAAU,GAAG,CAAC;AAAG,cAAM,IAAI,MAAM,4BAA4B;AAClE,aAAO,EAAE,GAAG,EAAC;IACf,OAAO;AACL,YAAM,IAAI,MACR,yBAAyB,MAAM,yBAAyB,EAAE,oBAAoB,EAAE,EAAE;IAEtF;EACF;AAEA,QAAMC,WAAU,UAAU,WAAWD;AACrC,QAAM,YAAY,UAAU,aAAa;AACzC,QAAM,sBAAsB,mBAAmB,IAAI,MAAM,GAAG,MAAM,CAAC;AAInE,WAAS,UAAU,GAAM,GAAI;AAC3B,UAAM,OAAO,GAAG,IAAI,CAAC;AACrB,UAAM,QAAQ,oBAAoB,CAAC;AACnC,WAAO,GAAG,IAAI,MAAM,KAAK;EAC3B;AAIA,MAAI,CAAC,UAAU,MAAM,IAAI,MAAM,EAAE;AAAG,UAAM,IAAI,MAAM,mCAAmC;AAIvF,QAAM,OAAO,GAAG,IAAI,GAAG,IAAI,MAAM,GAAGF,IAAG,GAAGC,IAAG;AAC7C,QAAM,QAAQ,GAAG,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,OAAO,EAAE,CAAC;AAChD,MAAI,GAAG,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC;AAAG,UAAM,IAAI,MAAM,0BAA0B;AAG3E,WAAS,OAAO,OAAe,GAAM,UAAU,OAAK;AAClD,QAAI,CAAC,GAAG,QAAQ,CAAC,KAAM,WAAW,GAAG,IAAI,CAAC;AAAI,YAAM,IAAI,MAAM,wBAAwB,KAAK,EAAE;AAC7F,WAAO;EACT;AAEA,WAAS,UAAU,OAAc;AAC/B,QAAI,EAAE,iBAAiBG;AAAQ,YAAM,IAAI,MAAM,0BAA0B;EAC3E;AAOA,QAAM,eAAe,SAAS,CAAC,GAAU,OAA0B;AACjE,UAAM,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,EAAC,IAAK;AAEhC,QAAI,GAAG,IAAI,GAAG,GAAG,GAAG;AAAG,aAAO,EAAE,GAAG,EAAC;AACpC,UAAM,MAAM,EAAE,IAAG;AAGjB,QAAI,MAAM;AAAM,WAAK,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;AAC5C,UAAM,KAAK,GAAG,IAAI,GAAG,EAAE;AACvB,UAAM,KAAK,GAAG,IAAI,GAAG,EAAE;AACvB,UAAM,KAAK,GAAG,IAAI,GAAG,EAAE;AACvB,QAAI;AAAK,aAAO,EAAE,GAAG,GAAG,MAAM,GAAG,GAAG,KAAI;AACxC,QAAI,CAAC,GAAG,IAAI,IAAI,GAAG,GAAG;AAAG,YAAM,IAAI,MAAM,kBAAkB;AAC3D,WAAO,EAAE,GAAG,IAAI,GAAG,GAAE;EACvB,CAAC;AAGD,QAAM,kBAAkB,SAAS,CAAC,MAAY;AAC5C,QAAI,EAAE,IAAG,GAAI;AAIX,UAAI,UAAU,sBAAsB,CAAC,GAAG,IAAI,EAAE,EAAE;AAAG;AACnD,YAAM,IAAI,MAAM,iBAAiB;IACnC;AAEA,UAAM,EAAE,GAAG,EAAC,IAAK,EAAE,SAAQ;AAC3B,QAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;AAAG,YAAM,IAAI,MAAM,sCAAsC;AAC5F,QAAI,CAAC,UAAU,GAAG,CAAC;AAAG,YAAM,IAAI,MAAM,mCAAmC;AACzE,QAAI,CAAC,EAAE,cAAa;AAAI,YAAM,IAAI,MAAM,wCAAwC;AAChF,WAAO;EACT,CAAC;AAED,WAAS,WACP,UACA,KACA,KACA,OACA,OAAc;AAEd,UAAM,IAAIA,OAAM,GAAG,IAAI,IAAI,IAAI,QAAQ,GAAG,IAAI,IAAI,IAAI,EAAE;AACxD,UAAM,SAAS,OAAO,GAAG;AACzB,UAAM,SAAS,OAAO,GAAG;AACzB,WAAO,IAAI,IAAI,GAAG;EACpB;EAOA,MAAMA,OAAK;;IAcT,YAAY,IAAO,IAAO,IAAK;AAC7B,WAAK,KAAK,OAAO,KAAK,EAAE;AACxB,WAAK,KAAK,OAAO,KAAK,IAAI,IAAI;AAC9B,WAAK,KAAK,OAAO,KAAK,EAAE;AACxB,aAAO,OAAO,IAAI;IACpB;;IAGA,OAAO,WAAW,GAAiB;AACjC,YAAM,EAAE,GAAG,EAAC,IAAK,KAAK,CAAA;AACtB,UAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;AAAG,cAAM,IAAI,MAAM,sBAAsB;AAClF,UAAI,aAAaA;AAAO,cAAM,IAAI,MAAM,8BAA8B;AAEtE,UAAI,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAAG,eAAOA,OAAM;AACzC,aAAO,IAAIA,OAAM,GAAG,GAAG,GAAG,GAAG;IAC/B;IAEA,IAAI,IAAC;AACH,aAAO,KAAK,SAAQ,EAAG;IACzB;IACA,IAAI,IAAC;AACH,aAAO,KAAK,SAAQ,EAAG;IACzB;IAEA,OAAO,WAAW,QAAe;AAC/B,aAAO,WAAWA,QAAO,MAAM,MAAM;IACvC;IAEA,OAAO,UAAU,OAAiB;AAChC,aAAO,KAAK;AACZ,aAAOA,OAAM,QAAQ,KAAK;IAC5B;;IAGA,OAAO,QAAQ,KAAQ;AACrB,YAAM,IAAIA,OAAM,WAAW,UAAU,YAAY,YAAY,GAAG,CAAC,CAAC;AAClE,QAAE,eAAc;AAChB,aAAO;IACT;;IAGA,OAAO,eAAe,YAAmB;AACvC,YAAM,yBAAyB,sBAC7B,IACA,UAAU,0BACV,UAAU,cAAc;AAE1B,aAAOA,OAAM,KAAK,SAAS,uBAAuB,UAAU,CAAC;IAC/D;;IAGA,OAAO,IAAI,QAAiB,SAAiB;AAC3C,aAAO,UAAUA,QAAO,IAAI,QAAQ,OAAO;IAC7C;;;;;;;IAQA,WAAW,aAAqB,GAAG,SAAS,MAAI;AAC9C,WAAK,cAAc,MAAM,UAAU;AACnC,UAAI,CAAC;AAAQ,aAAK,SAASJ,IAAG;AAC9B,aAAO;IACT;;IAGA,eAAe,YAAkB;AAC/B,WAAK,WAAW,UAAU;IAC5B;;;IAIA,iBAAc;AACZ,sBAAgB,IAAI;IACtB;IAEA,WAAQ;AACN,YAAM,EAAE,EAAC,IAAK,KAAK,SAAQ;AAC3B,UAAI,CAAC,GAAG;AAAO,cAAM,IAAI,MAAM,6BAA6B;AAC5D,aAAO,CAAC,GAAG,MAAM,CAAC;IACpB;;IAGA,OAAO,OAAY;AACjB,gBAAU,KAAK;AACf,YAAM,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AACnC,YAAM,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AACnC,YAAM,KAAK,GAAG,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;AAChD,YAAM,KAAK,GAAG,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;AAChD,aAAO,MAAM;IACf;;IAGA,SAAM;AACJ,aAAO,IAAII,OAAM,KAAK,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG,KAAK,EAAE;IACpD;;;;;IAMA,SAAM;AACJ,YAAM,EAAE,GAAG,EAAC,IAAK;AACjB,YAAM,KAAK,GAAG,IAAI,GAAGJ,IAAG;AACxB,YAAM,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AACnC,UAAI,KAAK,GAAG,MAAM,KAAK,GAAG,MAAM,KAAK,GAAG;AACxC,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,GAAG,EAAE;AACjB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,GAAG,EAAE;AACjB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,GAAG,EAAE;AACjB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,aAAO,IAAII,OAAM,IAAI,IAAI,EAAE;IAC7B;;;;;IAMA,IAAI,OAAY;AACd,gBAAU,KAAK;AACf,YAAM,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AACnC,YAAM,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AACnC,UAAI,KAAK,GAAG,MAAM,KAAK,GAAG,MAAM,KAAK,GAAG;AACxC,YAAM,IAAI,MAAM;AAChB,YAAM,KAAK,GAAG,IAAI,MAAM,GAAGJ,IAAG;AAC9B,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,GAAG,EAAE;AACjB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,GAAG,EAAE;AACjB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,GAAG,EAAE;AACjB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,aAAO,IAAII,OAAM,IAAI,IAAI,EAAE;IAC7B;IAEA,SAAS,OAAY;AACnB,aAAO,KAAK,IAAI,MAAM,OAAM,CAAE;IAChC;IAEA,MAAG;AACD,aAAO,KAAK,OAAOA,OAAM,IAAI;IAC/B;;;;;;;;;;IAWA,SAAS,QAAc;AACrB,YAAM,EAAE,MAAAC,MAAI,IAAK;AACjB,UAAI,CAAC,GAAG,YAAY,MAAM;AAAG,cAAM,IAAI,MAAM,8BAA8B;AAC3E,UAAI,OAAc;AAClB,YAAM,MAAM,CAAC,MAAc,KAAK,WAAW,MAAM,GAAGD,OAAM,UAAU;AAEpE,UAAIC,OAAM;AACR,cAAM,EAAE,OAAO,IAAI,OAAO,GAAE,IAAKA,MAAK,YAAY,MAAM;AACxD,cAAM,EAAE,GAAG,KAAK,GAAG,IAAG,IAAK,IAAI,EAAE;AACjC,cAAM,EAAE,GAAG,KAAK,GAAG,IAAG,IAAK,IAAI,EAAE;AACjC,eAAO,IAAI,IAAI,GAAG;AAClB,gBAAQ,WAAWA,MAAK,MAAM,KAAK,KAAK,OAAO,KAAK;MACtD,OAAO;AACL,cAAM,EAAE,GAAG,EAAC,IAAK,IAAI,MAAM;AAC3B,gBAAQ;AACR,eAAO;MACT;AAEA,aAAOD,OAAM,WAAW,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC;IAC1C;;;;;;IAOA,eAAe,IAAU;AACvB,YAAM,EAAE,MAAAC,MAAI,IAAK;AACjB,YAAM,IAAI;AACV,UAAI,CAAC,GAAG,QAAQ,EAAE;AAAG,cAAM,IAAI,MAAM,8BAA8B;AACnE,UAAI,OAAOR,QAAO,EAAE,IAAG;AAAI,eAAOO,OAAM;AACxC,UAAI,OAAON;AAAK,eAAO;AACvB,UAAI,KAAK,eAAe,IAAI;AAAG,eAAO,KAAK,SAAS,EAAE;AACtD,UAAIO,OAAM;AACR,cAAM,EAAE,OAAO,IAAI,OAAO,GAAE,IAAKA,MAAK,YAAY,EAAE;AAEpD,cAAM,EAAE,IAAI,GAAE,IAAK,cAAcD,QAAO,GAAG,IAAI,EAAE;AACjD,eAAO,WAAWC,MAAK,MAAM,IAAI,IAAI,OAAO,KAAK;MACnD,OAAO;AACL,eAAO,KAAK,iBAAiB,GAAG,EAAE;MACpC;IACF;IAEA,qBAAqB,GAAU,GAAW,GAAS;AACjD,YAAM,MAAM,KAAK,eAAe,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;AAC1D,aAAO,IAAI,IAAG,IAAK,SAAY;IACjC;;;;;IAMA,SAAS,WAAa;AACpB,aAAO,aAAa,MAAM,SAAS;IACrC;;;;;IAMA,gBAAa;AACX,YAAM,EAAE,cAAa,IAAK;AAC1B,UAAI,aAAaP;AAAK,eAAO;AAC7B,UAAI;AAAe,eAAO,cAAcM,QAAO,IAAI;AACnD,aAAO,KAAK,iBAAiB,MAAM,WAAW,EAAE,IAAG;IACrD;IAEA,gBAAa;AACX,YAAM,EAAE,cAAa,IAAK;AAC1B,UAAI,aAAaN;AAAK,eAAO;AAC7B,UAAI;AAAe,eAAO,cAAcM,QAAO,IAAI;AACnD,aAAO,KAAK,eAAe,QAAQ;IACrC;IAEA,QAAQ,eAAe,MAAI;AACzB,YAAM,gBAAgB,YAAY;AAClC,WAAK,eAAc;AACnB,aAAOD,SAAQC,QAAO,MAAM,YAAY;IAC1C;;IAGA,WAAW,eAAe,MAAI;AAC5B,aAAO,KAAK,QAAQ,YAAY;IAClC;IAEA,MAAM,eAAe,MAAI;AACvB,aAAO,WAAW,KAAK,QAAQ,YAAY,CAAC;IAC9C;IAEA,WAAQ;AACN,aAAO,UAAU,KAAK,IAAG,IAAK,SAAS,KAAK,MAAK,CAAE;IACrD;;AA5TgB,EAAAA,OAAA,OAAO,IAAIA,OAAM,MAAM,IAAI,MAAM,IAAI,GAAG,GAAG;AAE3C,EAAAA,OAAA,OAAO,IAAIA,OAAM,GAAG,MAAM,GAAG,KAAK,GAAG,IAAI;AAEzC,EAAAA,OAAA,KAAK;AACL,EAAAA,OAAA,KAAK;AAyTvB,QAAM,OAAO,GAAG;AAChB,QAAM,OAAO,KAAKA,QAAO,UAAU,OAAO,KAAK,KAAK,OAAO,CAAC,IAAI,IAAI;AACpE,SAAOA;AACT;AAgDA,SAAS,QAAQ,UAAiB;AAChC,SAAO,WAAW,GAAG,WAAW,IAAO,CAAI;AAC7C;AAoBM,SAAU,MACdE,QACA,WACA,YAA0C,CAAA,GAAE;AAE5C,kBACE,WACA,EAAE,MAAM,WAAU,GAClB;IACE,MAAM;IACN,MAAM;IACN,aAAa;IACb,UAAU;IACV,eAAe;GAChB;AAGH,QAAM,eAAe,UAAU,eAAe;AAC9C,QAAM,QACJ,UAAU,SACR,CAAC,QAAQ,SAAS,KAAK,UAAU,MAAM,KAAK,YAAY,GAAG,IAAI,CAAC;AAEpE,QAAM,EAAE,IAAI,GAAE,IAAKA;AACnB,QAAM,EAAE,OAAO,aAAa,MAAM,OAAM,IAAK;AAE7C,WAAS,sBAAsB,QAAc;AAC3C,UAAM,OAAO,eAAeC;AAC5B,WAAO,SAAS;EAClB;AAEA,WAAS,WAAW,GAAS;AAC3B,WAAO,sBAAsB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;EAChD;AACA,WAAS,SAAS,OAAeC,MAAW;AAC1C,QAAI,CAAC,GAAG,YAAYA,IAAG;AACrB,YAAM,IAAI,MAAM,qBAAqB,KAAK,2BAA2B;EACzE;EAKA,MAAM,UAAS;IAIb,YAAY,GAAW,GAAW,UAAiB;AACjD,eAAS,KAAK,CAAC;AACf,eAAS,KAAK,CAAC;AACf,WAAK,IAAI;AACT,WAAK,IAAI;AACT,UAAI,YAAY;AAAM,aAAK,WAAW;AACtC,aAAO,OAAO,IAAI;IACpB;;IAGA,OAAO,YAAY,KAAQ;AACzB,YAAM,IAAI,GAAG;AACb,YAAM,IAAI,YAAY,oBAAoB,KAAK,IAAI,CAAC;AACpD,aAAO,IAAI,UAAU,GAAG,UAAU,EAAE,SAAS,GAAG,CAAC,CAAC,GAAG,GAAG,UAAU,EAAE,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC;IACzF;;;IAIA,OAAO,QAAQ,KAAQ;AACrB,YAAM,EAAE,GAAG,EAAC,IAAK,IAAI,MAAM,YAAY,OAAO,GAAG,CAAC;AAClD,aAAO,IAAI,UAAU,GAAG,CAAC;IAC3B;;;;;IAMA,iBAAc;IAAU;IAExB,eAAe,UAAgB;AAC7B,aAAO,IAAI,UAAU,KAAK,GAAG,KAAK,GAAG,QAAQ;IAC/C;;IAGA,iBAAiB,SAAY;AAC3B,YAAM,cAAc,GAAG;AACvB,YAAM,EAAE,GAAG,GAAG,UAAU,IAAG,IAAK;AAChC,UAAI,OAAO,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,SAAS,GAAG;AAAG,cAAM,IAAI,MAAM,qBAAqB;AAUrF,YAAM,cAAc,cAAcC,OAAM;AACxC,UAAI,eAAe,MAAM;AAAG,cAAM,IAAI,MAAM,wCAAwC;AAEpF,YAAM,OAAO,QAAQ,KAAK,QAAQ,IAAI,IAAI,cAAc;AACxD,UAAI,CAAC,GAAG,QAAQ,IAAI;AAAG,cAAM,IAAI,MAAM,4BAA4B;AACnE,YAAM,IAAI,GAAG,QAAQ,IAAI;AACzB,YAAM,IAAIH,OAAM,QAAQ,YAAY,SAAS,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;AAChE,YAAM,KAAK,GAAG,IAAI,IAAI;AACtB,YAAM,IAAI,cAAc,YAAY,WAAW,OAAO,CAAC;AACvD,YAAM,KAAK,GAAG,OAAO,CAAC,IAAI,EAAE;AAC5B,YAAM,KAAK,GAAG,OAAO,IAAI,EAAE;AAE3B,YAAM,IAAIA,OAAM,KAAK,eAAe,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC;AAChE,UAAI,EAAE,IAAG;AAAI,cAAM,IAAI,MAAM,mBAAmB;AAChD,QAAE,eAAc;AAChB,aAAO;IACT;;IAGA,WAAQ;AACN,aAAO,sBAAsB,KAAK,CAAC;IACrC;IAEA,aAAU;AACR,aAAO,KAAK,SAAQ,IAAK,IAAI,UAAU,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,KAAK,QAAQ,IAAI;IAClF;IAEA,QAAQ,QAAyB;AAC/B,UAAI,WAAW;AAAW,eAAO,YAAY,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,QAAQ,KAAK,CAAC,CAAC;AACnF,UAAI,WAAW;AAAO,eAAO,WAAW,IAAI,WAAW,IAAI,CAAC;AAC5D,YAAM,IAAI,MAAM,gBAAgB;IAClC;;IAGA,gBAAa;AACX,aAAO,KAAK,QAAQ,KAAK;IAC3B;IACA,WAAQ;AACN,aAAO,WAAW,KAAK,QAAQ,KAAK,CAAC;IACvC;;IAGA,oBAAiB;AACf,aAAO,KAAK,QAAQ,SAAS;IAC/B;IACA,eAAY;AACV,aAAO,WAAW,KAAK,QAAQ,SAAS,CAAC;IAC3C;;AAIF,QAAM,yBAAyB,sBAC7B,IACA,UAAU,0BACV,UAAU,cAAc;AAG1B,QAAM,QAAQ;IACZ,kBAAkB,YAAmB;AACnC,UAAI;AACF,+BAAuB,UAAU;AACjC,eAAO;MACT,SAAS,OAAO;AACd,eAAO;MACT;IACF;IACA;;;;;IAMA,kBAAkB,MAAiB;AACjC,YAAM,IAAI;AACV,aAAO,eAAe,aAAa,iBAAiB,CAAC,CAAC,GAAG,CAAC;IAC5D;IAEA,WAAW,aAAa,GAAG,QAAQA,OAAM,MAAI;AAC3C,aAAO,MAAM,WAAW,YAAY,KAAK;IAC3C;;AASF,WAAS,aAAa,YAAqB,eAAe,MAAI;AAC5D,WAAOA,OAAM,eAAe,UAAU,EAAE,QAAQ,YAAY;EAC9D;AAKA,WAAS,UAAU,MAAsB;AACvC,QAAI,OAAO,SAAS;AAAU,aAAO;AACrC,QAAI,gBAAgBA;AAAO,aAAO;AAClC,UAAM,MAAM,YAAY,OAAO,IAAI;AACnC,UAAM,SAAS,IAAI;AACnB,UAAM,IAAI,GAAG;AACb,UAAM,KAAK,IAAI;AACf,UAAM,KAAK,IAAI,IAAI;AACnB,QAAI,UAAU,4BAA4B,GAAG,UAAU,IAAI;AACzD,aAAO;IACT,OAAO;AACL,aAAO,WAAW,MAAM,WAAW;IACrC;EACF;AAYA,WAAS,gBAAgB,UAAmB,SAAc,eAAe,MAAI;AAC3E,QAAI,UAAU,QAAQ,MAAM;AAAM,YAAM,IAAI,MAAM,+BAA+B;AACjF,QAAI,UAAU,OAAO,MAAM;AAAO,YAAM,IAAI,MAAM,+BAA+B;AACjF,UAAM,IAAIA,OAAM,QAAQ,OAAO;AAC/B,WAAO,EAAE,SAAS,uBAAuB,QAAQ,CAAC,EAAE,QAAQ,YAAY;EAC1E;AAMA,QAAM,WACJ,UAAU,YACV,SAAU,OAAiB;AAEzB,QAAI,MAAM,SAAS;AAAM,YAAM,IAAI,MAAM,oBAAoB;AAG7D,UAAME,OAAM,gBAAgB,KAAK;AACjC,UAAM,QAAQ,MAAM,SAAS,IAAI;AACjC,WAAO,QAAQ,IAAIA,QAAO,OAAO,KAAK,IAAIA;EAC5C;AACF,QAAM,gBACJ,UAAU,iBACV,SAAU,OAAiB;AACzB,WAAO,GAAG,OAAO,SAAS,KAAK,CAAC;EAClC;AAEF,QAAM,aAAa,QAAQ,MAAM;AAIjC,WAAS,WAAWA,MAAW;AAE7B,aAAS,aAAa,QAAQA,MAAKE,MAAK,UAAU;AAClD,WAAO,GAAG,QAAQF,IAAG;EACvB;AAOA,WAAS,QAAQ,SAAc,YAAqB,OAAO,gBAAc;AACvE,QAAI,CAAC,aAAa,WAAW,EAAE,KAAK,CAAC,MAAM,KAAK,IAAI;AAClD,YAAM,IAAI,MAAM,qCAAqC;AACvD,UAAM,EAAE,KAAI,IAAK;AACjB,QAAI,EAAE,MAAM,SAAS,cAAc,IAAG,IAAK;AAC3C,QAAI,QAAQ;AAAM,aAAO;AACzB,cAAU,YAAY,WAAW,OAAO;AACxC,uBAAmB,IAAI;AACvB,QAAI;AAAS,gBAAU,YAAY,qBAAqB,KAAK,OAAO,CAAC;AAKrE,UAAM,QAAQ,cAAc,OAAO;AACnC,UAAM,IAAI,uBAAuB,UAAU;AAC3C,UAAM,WAAW,CAAC,WAAW,CAAC,GAAG,WAAW,KAAK,CAAC;AAElD,QAAI,OAAO,QAAQ,QAAQ,OAAO;AAEhC,YAAM,IAAI,QAAQ,OAAO,aAAa,GAAG,KAAK,IAAI;AAClD,eAAS,KAAK,YAAY,gBAAgB,CAAC,CAAC;IAC9C;AACA,UAAM,OAAO,YAAY,GAAG,QAAQ;AACpC,UAAM,IAAI;AAKV,aAAS,MAAM,QAAkB;AAG/B,YAAM,IAAI,SAAS,MAAM;AACzB,UAAI,CAAC,GAAG,YAAY,CAAC;AAAG;AACxB,YAAM,KAAK,GAAG,IAAI,CAAC;AACnB,YAAM,IAAIF,OAAM,KAAK,SAAS,CAAC,EAAE,SAAQ;AACzC,YAAM,IAAI,GAAG,OAAO,EAAE,CAAC;AACvB,UAAI,MAAMI;AAAK;AACf,YAAM,IAAI,GAAG,OAAO,KAAK,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC;AAC7C,UAAI,MAAMA;AAAK;AACf,UAAI,YAAY,EAAE,MAAM,IAAI,IAAI,KAAK,OAAO,EAAE,IAAIH,IAAG;AACrD,UAAI,QAAQ;AACZ,UAAI,QAAQ,sBAAsB,CAAC,GAAG;AACpC,gBAAQ,WAAW,CAAC;AACpB,oBAAY;MACd;AACA,aAAO,IAAI,UAAU,GAAG,OAAO,QAAQ;IACzC;AACA,WAAO,EAAE,MAAM,MAAK;EACtB;AACA,QAAM,iBAA2B,EAAE,MAAM,UAAU,MAAM,SAAS,MAAK;AACvE,QAAM,iBAA0B,EAAE,MAAM,UAAU,MAAM,SAAS,MAAK;AAetE,WAAS,KAAK,SAAc,SAAkB,OAAO,gBAAc;AACjE,UAAM,EAAE,MAAM,MAAK,IAAK,QAAQ,SAAS,SAAS,IAAI;AACtD,UAAM,OAAO,eAAmC,UAAU,KAAK,WAAW,GAAG,OAAO,KAAK;AACzF,WAAO,KAAK,MAAM,KAAK;EACzB;AAGA,EAAAD,OAAM,KAAK,WAAW,CAAC;AAevB,WAAS,OACP,WACA,SACA,WACA,OAAO,gBAAc;AAErB,UAAM,KAAK;AACX,cAAU,YAAY,WAAW,OAAO;AACxC,gBAAY,YAAY,aAAa,SAAS;AAG9C,uBAAmB,IAAI;AACvB,UAAM,EAAE,MAAM,SAAS,OAAM,IAAK;AAGlC,QAAI,YAAY;AAAM,YAAM,IAAI,MAAM,oCAAoC;AAE1E,QAAI,WAAW,UAAa,CAAC,CAAC,WAAW,OAAO,IAAI,EAAE,SAAS,MAAM;AACnE,YAAM,IAAI,MAAM,yCAAyC;AAC3D,UAAM,QAAQ,OAAO,OAAO,YAAY,QAAQ,EAAE;AAClD,UAAM,QACJ,CAAC,SACD,CAAC,UACD,OAAO,OAAO,YACd,OAAO,QACP,OAAO,GAAG,MAAM,YAChB,OAAO,GAAG,MAAM;AAClB,QAAI,CAAC,SAAS,CAAC;AACb,YAAM,IAAI,MAAM,0EAA0E;AAC5F,QAAI,OAA8B;AAClC,QAAI;AAGJ,QAAI;AAUF,UAAI,OAAO;AACT,YAAI,WAAW,UAAa,WAAW,MAAM;AAC3C,iBAAO,IAAI,UAAU,GAAG,GAAG,GAAG,CAAC;QACjC,OAAO;AACL,gBAAM,IAAI,MAAM,gBAAgB;QAClC;MACF;AACA,UAAI,OAAO;AAIT,YAAI;AACF,cAAI,WAAW;AAAW,mBAAO,UAAU,QAAQ,EAAE;QACvD,SAAS,UAAU;AACjB,cAAI,EAAE,oBAAoB,IAAI;AAAM,kBAAM;QAC5C;AACA,YAAI,CAAC,QAAQ,WAAW;AAAO,iBAAO,UAAU,YAAY,EAAE;MAChE;AACA,UAAIA,OAAM,QAAQ,SAAS;IAC7B,SAAS,OAAO;AACd,aAAO;IACT;AACA,QAAI,CAAC;AAAM,aAAO;AAClB,QAAI,QAAQ,KAAK,SAAQ;AAAI,aAAO;AAEpC,QAAI;AAAS,gBAAU,UAAU,KAAK,OAAO;AAC7C,UAAM,EAAE,GAAG,EAAC,IAAK;AACjB,UAAM,IAAI,cAAc,OAAO;AAC/B,UAAM,KAAK,GAAG,IAAI,CAAC;AACnB,UAAM,KAAK,GAAG,OAAO,IAAI,EAAE;AAC3B,UAAM,KAAK,GAAG,OAAO,IAAI,EAAE;AAC3B,UAAM,IAAIA,OAAM,KAAK,eAAe,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC;AAChE,QAAI,EAAE,IAAG;AAAI,aAAO;AACpB,UAAM,IAAI,GAAG,OAAO,EAAE,CAAC;AACvB,WAAO,MAAM;EACf;AAGA,SAAO,OAAO,OAAO;IACnB;IACA;IACA;IACA;IACA;IACA,OAAAA;IACA;GACD;AACH;AAWA,SAAS,gCAAmC,GAAqB;AAC/D,QAAM,QAA4B;IAChC,GAAG,EAAE;IACL,GAAG,EAAE;IACL,GAAG,EAAE,GAAG;IACR,GAAG,EAAE;IACL,GAAG,EAAE;IACL,IAAI,EAAE;IACN,IAAI,EAAE;;AAER,QAAM,KAAK,EAAE;AACb,QAAM,KAAK,MAAM,MAAM,GAAG,EAAE,UAAU;AACtC,QAAM,YAAqC;IACzC;IACA;IACA,0BAA0B,EAAE;IAC5B,oBAAoB,EAAE;IACtB,MAAM,EAAE;IACR,gBAAgB,EAAE;IAClB,eAAe,EAAE;IACjB,eAAe,EAAE;IACjB,WAAW,EAAE;IACb,SAAS,EAAE;;AAEb,SAAO,EAAE,OAAO,UAAS;AAC3B;AACA,SAAS,0BAA0B,GAAY;AAC7C,QAAM,EAAE,OAAO,UAAS,IAAK,gCAAgC,CAAC;AAC9D,QAAM,YAAuB;IAC3B,MAAM,EAAE;IACR,MAAM,EAAE;IACR,aAAa,EAAE;IACf,MAAM,EAAE;IACR,UAAU,EAAE;IACZ,eAAe,EAAE;;AAEnB,SAAO,EAAE,OAAO,WAAW,UAAS;AACtC;AA4BA,SAAS,4BAA4B,GAAcK,QAAY;AAC7D,SAAO,OAAO,OAAO,CAAA,GAAIA,QAAO;IAC9B,iBAAiBA,OAAM;IACvB,OAAO;GACR;AACH;AAGM,SAAU,YAAY,GAAY;AACtC,QAAM,EAAE,OAAO,WAAW,UAAS,IAAK,0BAA0B,CAAC;AACnE,QAAMC,SAAQ,aAAa,OAAO,SAAS;AAC3C,QAAM,QAAQ,MAAMA,QAAO,WAAW,SAAS;AAC/C,SAAO,4BAA4B,GAAG,KAAK;AAC7C;AAWM,SAAU,eACd,IACA,GAAI;AAGJ,QAAM,IAAI,GAAG;AACb,MAAI,IAAIC;AACR,WAAS,IAAI,IAAIC,MAAK,IAAIC,SAAQF,MAAK,KAAKE;AAAK,SAAKD;AACtD,QAAM,KAAK;AAGX,QAAM,eAAeC,QAAQ,KAAKD,OAAMA;AACxC,QAAM,aAAa,eAAeC;AAClC,QAAM,MAAM,IAAID,QAAO;AACvB,QAAM,MAAM,KAAKA,QAAOC;AACxB,QAAM,KAAK,aAAaD;AACxB,QAAM,KAAK;AACX,QAAM,KAAK,GAAG,IAAI,GAAG,EAAE;AACvB,QAAM,KAAK,GAAG,IAAI,IAAI,KAAKA,QAAOC,IAAG;AACrC,MAAI,YAAY,CAAC,GAAM,MAAwC;AAC7D,QAAI,MAAM;AACV,QAAI,MAAM,GAAG,IAAI,GAAG,EAAE;AACtB,QAAI,MAAM,GAAG,IAAI,GAAG;AACpB,UAAM,GAAG,IAAI,KAAK,CAAC;AACnB,QAAI,MAAM,GAAG,IAAI,GAAG,GAAG;AACvB,UAAM,GAAG,IAAI,KAAK,EAAE;AACpB,UAAM,GAAG,IAAI,KAAK,GAAG;AACrB,UAAM,GAAG,IAAI,KAAK,CAAC;AACnB,UAAM,GAAG,IAAI,KAAK,CAAC;AACnB,QAAI,MAAM,GAAG,IAAI,KAAK,GAAG;AACzB,UAAM,GAAG,IAAI,KAAK,EAAE;AACpB,QAAI,OAAO,GAAG,IAAI,KAAK,GAAG,GAAG;AAC7B,UAAM,GAAG,IAAI,KAAK,EAAE;AACpB,UAAM,GAAG,IAAI,KAAK,GAAG;AACrB,UAAM,GAAG,KAAK,KAAK,KAAK,IAAI;AAC5B,UAAM,GAAG,KAAK,KAAK,KAAK,IAAI;AAE5B,aAAS,IAAI,IAAI,IAAID,MAAK,KAAK;AAC7B,UAAIE,OAAM,IAAID;AACd,MAAAC,OAAMD,QAAQC,OAAMF;AACpB,UAAI,OAAO,GAAG,IAAI,KAAKE,IAAG;AAC1B,YAAM,KAAK,GAAG,IAAI,MAAM,GAAG,GAAG;AAC9B,YAAM,GAAG,IAAI,KAAK,GAAG;AACrB,YAAM,GAAG,IAAI,KAAK,GAAG;AACrB,aAAO,GAAG,IAAI,KAAK,GAAG;AACtB,YAAM,GAAG,KAAK,KAAK,KAAK,EAAE;AAC1B,YAAM,GAAG,KAAK,MAAM,KAAK,EAAE;IAC7B;AACA,WAAO,EAAE,SAAS,MAAM,OAAO,IAAG;EACpC;AACA,MAAI,GAAG,QAAQC,SAAQC,MAAK;AAE1B,UAAMC,OAAM,GAAG,QAAQD,QAAOD;AAC9B,UAAMG,MAAK,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;AAC5B,gBAAY,CAAC,GAAM,MAAQ;AACzB,UAAI,MAAM,GAAG,IAAI,CAAC;AAClB,YAAM,MAAM,GAAG,IAAI,GAAG,CAAC;AACvB,YAAM,GAAG,IAAI,KAAK,GAAG;AACrB,UAAI,KAAK,GAAG,IAAI,KAAKD,GAAE;AACvB,WAAK,GAAG,IAAI,IAAI,GAAG;AACnB,YAAM,KAAK,GAAG,IAAI,IAAIC,GAAE;AACxB,YAAM,MAAM,GAAG,IAAI,GAAG,IAAI,EAAE,GAAG,CAAC;AAChC,YAAM,OAAO,GAAG,IAAI,KAAK,CAAC;AAC1B,UAAI,IAAI,GAAG,KAAK,IAAI,IAAI,IAAI;AAC5B,aAAO,EAAE,SAAS,MAAM,OAAO,EAAC;IAClC;EACF;AAGA,SAAO;AACT;AAKM,SAAU,oBACd,IACA,MAIC;AAED,gBAAc,EAAE;AAChB,QAAM,EAAE,GAAG,GAAG,EAAC,IAAK;AACpB,MAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;AACnD,UAAM,IAAI,MAAM,mCAAmC;AACrD,QAAM,YAAY,eAAe,IAAI,CAAC;AACtC,MAAI,CAAC,GAAG;AAAO,UAAM,IAAI,MAAM,8BAA8B;AAG7D,SAAO,CAAC,MAAwB;AAE9B,QAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACrC,UAAM,GAAG,IAAI,CAAC;AACd,UAAM,GAAG,IAAI,KAAK,CAAC;AACnB,UAAM,GAAG,IAAI,GAAG;AAChB,UAAM,GAAG,IAAI,KAAK,GAAG;AACrB,UAAM,GAAG,IAAI,KAAK,GAAG,GAAG;AACxB,UAAM,GAAG,IAAI,KAAK,CAAC;AACnB,UAAM,GAAG,KAAK,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI,CAAC;AACnD,UAAM,GAAG,IAAI,KAAK,CAAC;AACnB,UAAM,GAAG,IAAI,GAAG;AAChB,UAAM,GAAG,IAAI,GAAG;AAChB,UAAM,GAAG,IAAI,KAAK,CAAC;AACnB,UAAM,GAAG,IAAI,KAAK,GAAG;AACrB,UAAM,GAAG,IAAI,KAAK,GAAG;AACrB,UAAM,GAAG,IAAI,KAAK,GAAG;AACrB,UAAM,GAAG,IAAI,KAAK,CAAC;AACnB,UAAM,GAAG,IAAI,KAAK,GAAG;AACrB,QAAI,GAAG,IAAI,KAAK,GAAG;AACnB,UAAM,EAAE,SAAS,MAAK,IAAK,UAAU,KAAK,GAAG;AAC7C,QAAI,GAAG,IAAI,KAAK,CAAC;AACjB,QAAI,GAAG,IAAI,GAAG,KAAK;AACnB,QAAI,GAAG,KAAK,GAAG,KAAK,OAAO;AAC3B,QAAI,GAAG,KAAK,GAAG,OAAO,OAAO;AAC7B,UAAM,KAAK,GAAG,MAAO,CAAC,MAAM,GAAG,MAAO,CAAC;AACvC,QAAI,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE;AAC5B,UAAM,UAAU,cAAc,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;AAChD,QAAI,GAAG,IAAI,GAAG,OAAO;AACrB,WAAO,EAAE,GAAG,EAAC;EACf;AACF;;;ACnoDM,SAAU,YAAY,UAAoB,SAAc;AAC5D,QAAM,SAAS,CAAC,SAAyB,YAAY,EAAE,GAAG,UAAU,KAAU,CAAE;AAChF,SAAO,EAAE,GAAG,OAAO,OAAO,GAAG,OAAM;AACrC;;;AC2BA,IAAM,QAAQ;AAGd,SAAS,MAAM,OAAe,QAAc;AAC1C,OAAK,KAAK;AACV,OAAK,MAAM;AACX,MAAI,QAAQ,KAAK,SAAS,KAAM,IAAI;AAAS,UAAM,IAAI,MAAM,0BAA0B,KAAK;AAC5F,QAAM,MAAM,MAAM,KAAK,EAAE,OAAM,CAAE,EAAE,KAAK,CAAC;AACzC,WAAS,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK;AACpC,QAAI,CAAC,IAAI,QAAQ;AACjB,eAAW;EACb;AACA,SAAO,IAAI,WAAW,GAAG;AAC3B;AAEA,SAAS,OAAO,GAAe,GAAa;AAC1C,QAAM,MAAM,IAAI,WAAW,EAAE,MAAM;AACnC,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;EACrB;AACA,SAAO;AACT;AAEA,SAAS,KAAK,MAAa;AACzB,MAAI,CAAC,OAAO,cAAc,IAAI;AAAG,UAAM,IAAI,MAAM,iBAAiB;AACpE;AAMM,SAAU,mBACd,KACA,KACA,YACA,GAAQ;AAER,SAAO,GAAG;AACV,SAAO,GAAG;AACV,OAAK,UAAU;AAEf,MAAI,IAAI,SAAS;AAAK,UAAM,EAAE,YAAY,YAAY,mBAAmB,GAAG,GAAG,CAAC;AAChF,QAAM,EAAE,WAAW,YAAY,UAAU,WAAU,IAAK;AACxD,QAAM,MAAM,KAAK,KAAK,aAAa,UAAU;AAC7C,MAAI,aAAa,SAAS,MAAM;AAAK,UAAM,IAAI,MAAM,wCAAwC;AAC7F,QAAM,YAAY,YAAY,KAAK,MAAM,IAAI,QAAQ,CAAC,CAAC;AACvD,QAAM,QAAQ,MAAM,GAAG,UAAU;AACjC,QAAM,YAAY,MAAM,YAAY,CAAC;AACrC,QAAM,IAAI,IAAI,MAAkB,GAAG;AACnC,QAAM,MAAM,EAAE,YAAY,OAAO,KAAK,WAAW,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC;AACxE,IAAE,CAAC,IAAI,EAAE,YAAY,KAAK,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC;AACjD,WAAS,IAAI,GAAG,KAAK,KAAK,KAAK;AAC7B,UAAM,OAAO,CAAC,OAAO,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,GAAG,SAAS;AAC/D,MAAE,CAAC,IAAI,EAAE,YAAY,GAAG,IAAI,CAAC;EAC/B;AACA,QAAM,sBAAsB,YAAY,GAAG,CAAC;AAC5C,SAAO,oBAAoB,MAAM,GAAG,UAAU;AAChD;AASM,SAAU,mBACd,KACA,KACA,YACA,GACA,GAAQ;AAER,SAAO,GAAG;AACV,SAAO,GAAG;AACV,OAAK,UAAU;AAGf,MAAI,IAAI,SAAS,KAAK;AACpB,UAAM,QAAQ,KAAK,KAAM,IAAI,IAAK,CAAC;AACnC,UAAM,EAAE,OAAO,EAAE,MAAK,CAAE,EAAE,OAAO,YAAY,mBAAmB,CAAC,EAAE,OAAO,GAAG,EAAE,OAAM;EACvF;AACA,MAAI,aAAa,SAAS,IAAI,SAAS;AACrC,UAAM,IAAI,MAAM,wCAAwC;AAC1D,SACE,EAAE,OAAO,EAAE,OAAO,WAAU,CAAE,EAC3B,OAAO,GAAG,EACV,OAAO,MAAM,YAAY,CAAC,CAAC,EAE3B,OAAO,GAAG,EACV,OAAO,MAAM,IAAI,QAAQ,CAAC,CAAC,EAC3B,OAAM;AAEb;AAUM,SAAU,cAAc,KAAiB,OAAe,SAAgB;AAC5E,kBAAgB,SAAS;IACvB,GAAG;IACH,GAAG;IACH,GAAG;IACH,MAAM;GACP;AACD,QAAM,EAAE,GAAG,GAAG,GAAG,MAAM,QAAQ,KAAK,KAAI,IAAK;AAC7C,MAAI,CAAC,QAAQ,IAAI,KAAK,OAAO,SAAS;AACpC,UAAM,IAAI,MAAM,kCAAkC;AACpD,MAAI,CAAC,OAAO,QAAQ,IAAI;AAAG,UAAM,IAAI,MAAM,qBAAqB;AAChE,SAAO,GAAG;AACV,OAAK,KAAK;AACV,QAAM,MAAM,OAAO,SAAS,WAAW,YAAY,IAAI,IAAI;AAC3D,QAAM,QAAQ,EAAE,SAAS,CAAC,EAAE;AAC5B,QAAM,IAAI,KAAK,MAAM,QAAQ,KAAK,CAAC;AACnC,QAAM,eAAe,QAAQ,IAAI;AACjC,MAAI;AACJ,MAAI,WAAW,OAAO;AACpB,UAAM,mBAAmB,KAAK,KAAK,cAAc,IAAI;EACvD,WAAW,WAAW,OAAO;AAC3B,UAAM,mBAAmB,KAAK,KAAK,cAAc,GAAG,IAAI;EAC1D,WAAW,WAAW,kBAAkB;AAEtC,UAAM;EACR,OAAO;AACL,UAAM,IAAI,MAAM,+BAA+B;EACjD;AACA,QAAM,IAAI,IAAI,MAAM,KAAK;AACzB,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,UAAM,IAAI,IAAI,MAAM,CAAC;AACrB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,aAAa,KAAK,IAAI,IAAI;AAChC,YAAM,KAAK,IAAI,SAAS,YAAY,aAAa,CAAC;AAClD,QAAE,CAAC,IAAI,IAAI,MAAM,EAAE,GAAG,CAAC;IACzB;AACA,MAAE,CAAC,IAAI;EACT;AACA,SAAO;AACT;AAIM,SAAU,WAAmC,OAAU,KAAe;AAE1E,QAAM,QAAQ,IAAI,IAAI,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE,QAAO,CAAE;AACpD,SAAO,CAAC,GAAM,MAAQ;AACpB,UAAM,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI,MAAM,IAAI,CAAC,QAClC,IAAI,OAAO,CAAC,KAAK,MAAM,MAAM,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAMzD,UAAM,CAAC,QAAQ,MAAM,IAAI,cAAc,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI;AAC5D,QAAI,MAAM,IAAI,IAAI,MAAM;AACxB,QAAI,MAAM,IAAI,GAAG,MAAM,IAAI,IAAI,MAAM,CAAC;AACtC,WAAO,EAAE,GAAG,EAAC;EACf;AACF;AAwCM,SAAU,aACdC,QACA,YACA,UAAkD;AAElD,MAAI,OAAO,eAAe;AAAY,UAAM,IAAI,MAAM,8BAA8B;AACpF,WAAS,IAAIC,MAAa;AACxB,WAAOD,OAAM,WAAW,WAAWC,IAAG,CAAC;EACzC;AACA,WAAS,MAAM,SAAoB;AACjC,UAAM,IAAI,QAAQ,cAAa;AAC/B,QAAI,EAAE,OAAOD,OAAM,IAAI;AAAG,aAAOA,OAAM;AACvC,MAAE,eAAc;AAChB,WAAO;EACT;AAEA,SAAO;IACL;IACA,YAAY,KAAiB,SAAsB;AACjD,YAAM,MAAM,SAAS,MAAM,SAAS,MAAM,CAAA;AAC1C,YAAM,OAAO,OAAO,OAAO,CAAA,GAAI,UAAU,KAAK,OAAO;AACrD,YAAM,IAAI,cAAc,KAAK,GAAG,IAAI;AACpC,YAAM,KAAK,IAAI,EAAE,CAAC,CAAC;AACnB,YAAM,KAAK,IAAI,EAAE,CAAC,CAAC;AACnB,aAAO,MAAM,GAAG,IAAI,EAAE,CAAC;IACzB;IACA,cAAc,KAAiB,SAAsB;AACnD,YAAM,MAAM,SAAS,YAAY,SAAS,YAAY,CAAA;AACtD,YAAM,OAAO,OAAO,OAAO,CAAA,GAAI,UAAU,KAAK,OAAO;AACrD,YAAM,IAAI,cAAc,KAAK,GAAG,IAAI;AACpC,aAAO,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC;IACxB;;IAEA,WAAW,SAAiB;AAC1B,UAAI,CAAC,MAAM,QAAQ,OAAO;AAAG,cAAM,IAAI,MAAM,2BAA2B;AACxE,iBAAW,KAAK;AACd,YAAI,OAAO,MAAM;AAAU,gBAAM,IAAI,MAAM,2BAA2B;AACxE,aAAO,MAAM,IAAI,OAAO,CAAC;IAC3B;;AAEJ;;;AC3PA,IAAM,kBAA2C;EAC/C,GAAG,OAAO,oEAAoE;EAC9E,GAAG,OAAO,oEAAoE;EAC9E,GAAG,OAAO,CAAC;EACX,GAAG,OAAO,CAAC;EACX,GAAG,OAAO,CAAC;EACX,IAAI,OAAO,oEAAoE;EAC/E,IAAI,OAAO,oEAAoE;;AAEjF,IAAME,OAAM,OAAO,CAAC;AACpB,IAAMC,OAAM,OAAO,CAAC;AACpB,IAAMC,OAAM,OAAO,CAAC;AACpB,IAAM,aAAa,CAAC,GAAW,OAAe,IAAI,IAAIA,QAAO;AAM7D,SAAS,QAAQ,GAAS;AACxB,QAAM,IAAI,gBAAgB;AAE1B,QAAMC,OAAM,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,OAAO,OAAO,EAAE,GAAG,OAAO,OAAO,EAAE;AAE3E,QAAM,OAAO,OAAO,EAAE,GAAG,OAAO,OAAO,EAAE,GAAG,OAAO,OAAO,EAAE;AAC5D,QAAM,KAAM,IAAI,IAAI,IAAK;AACzB,QAAM,KAAM,KAAK,KAAK,IAAK;AAC3B,QAAM,KAAM,KAAK,IAAIA,MAAK,CAAC,IAAI,KAAM;AACrC,QAAM,KAAM,KAAK,IAAIA,MAAK,CAAC,IAAI,KAAM;AACrC,QAAM,MAAO,KAAK,IAAID,MAAK,CAAC,IAAI,KAAM;AACtC,QAAM,MAAO,KAAK,KAAK,MAAM,CAAC,IAAI,MAAO;AACzC,QAAM,MAAO,KAAK,KAAK,MAAM,CAAC,IAAI,MAAO;AACzC,QAAM,MAAO,KAAK,KAAK,MAAM,CAAC,IAAI,MAAO;AACzC,QAAM,OAAQ,KAAK,KAAK,MAAM,CAAC,IAAI,MAAO;AAC1C,QAAM,OAAQ,KAAK,MAAM,MAAM,CAAC,IAAI,MAAO;AAC3C,QAAM,OAAQ,KAAK,MAAMC,MAAK,CAAC,IAAI,KAAM;AACzC,QAAM,KAAM,KAAK,MAAM,MAAM,CAAC,IAAI,MAAO;AACzC,QAAM,KAAM,KAAK,IAAI,KAAK,CAAC,IAAI,KAAM;AACrC,QAAM,OAAO,KAAK,IAAID,MAAK,CAAC;AAC5B,MAAI,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC;AAAG,UAAM,IAAI,MAAM,yBAAyB;AAC3E,SAAO;AACT;AAEA,IAAM,OAAO,MAAM,gBAAgB,GAAG,QAAW,QAAW,EAAE,MAAM,QAAO,CAAE;AAiBtE,IAAM,YAA+B,YAC1C;EACE,GAAG;EACH,IAAI;EACJ,MAAM;;EACN,MAAM;;IAEJ,MAAM,OAAO,oEAAoE;IACjF,aAAa,CAAC,MAAa;AACzB,YAAM,IAAI,gBAAgB;AAC1B,YAAM,KAAK,OAAO,oCAAoC;AACtD,YAAM,KAAK,CAACD,OAAM,OAAO,oCAAoC;AAC7D,YAAM,KAAK,OAAO,qCAAqC;AACvD,YAAM,KAAK;AACX,YAAM,YAAY,OAAO,qCAAqC;AAE9D,YAAM,KAAK,WAAW,KAAK,GAAG,CAAC;AAC/B,YAAM,KAAK,WAAW,CAAC,KAAK,GAAG,CAAC;AAChC,UAAI,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC;AACrC,UAAI,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC;AAClC,YAAM,QAAQ,KAAK;AACnB,YAAM,QAAQ,KAAK;AACnB,UAAI;AAAO,aAAK,IAAI;AACpB,UAAI;AAAO,aAAK,IAAI;AACpB,UAAI,KAAK,aAAa,KAAK,WAAW;AACpC,cAAM,IAAI,MAAM,yCAAyC,CAAC;MAC5D;AACA,aAAO,EAAE,OAAO,IAAI,OAAO,GAAE;IAC/B;;GAGJ,MAAM;AAMR,IAAM,uBAAsD,CAAA;AAC5D,SAAS,WAAW,QAAgB,UAAsB;AACxD,MAAI,OAAO,qBAAqB,GAAG;AACnC,MAAI,SAAS,QAAW;AACtB,UAAM,OAAO,OAAO,WAAW,KAAK,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC;AAChE,WAAO,YAAY,MAAM,IAAI;AAC7B,yBAAqB,GAAG,IAAI;EAC9B;AACA,SAAO,OAAO,YAAY,MAAM,GAAG,QAAQ,CAAC;AAC9C;AAGA,IAAM,eAAe,CAAC,UAA6B,MAAM,QAAQ,IAAI,EAAE,MAAM,CAAC;AAC9E,IAAM,WAAW,CAAC,MAAc,gBAAgB,GAAG,EAAE;AACrD,IAAM,OAAO,CAAC,MAAc,IAAI,GAAG,gBAAgB,CAAC;AACpD,IAAM,OAAO,CAAC,MAAc,IAAI,GAAG,gBAAgB,CAAC;AACpD,IAAM,SAAyB,MAAM,UAAU,OAAM;AACrD,IAAM,UAAU,CAAC,MAAc,IAAIC,SAAQF;AAG3C,SAAS,oBAAoB,MAAa;AACxC,MAAI,KAAK,UAAU,MAAM,uBAAuB,IAAI;AACpD,MAAI,IAAI,MAAM,eAAe,EAAE;AAC/B,QAAM,SAAS,QAAQ,EAAE,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE;AAC3C,SAAO,EAAE,QAAgB,OAAO,aAAa,CAAC,EAAC;AACjD;AAKA,SAAS,OAAO,GAAS;AACvB,WAAS,KAAK,GAAGC,MAAK,gBAAgB,CAAC;AACvC,QAAM,KAAK,KAAK,IAAI,CAAC;AACrB,QAAM,IAAI,KAAK,KAAK,IAAI,OAAO,CAAC,CAAC;AACjC,MAAI,IAAI,QAAQ,CAAC;AACjB,MAAI,CAAC,QAAQ,CAAC;AAAG,QAAI,KAAK,CAAC,CAAC;AAC5B,QAAM,IAAI,MAAM,WAAW,EAAE,GAAG,EAAC,CAAE;AACnC,IAAE,eAAc;AAChB,SAAO;AACT;AACA,IAAM,MAAM;AAIZ,SAAS,aAAa,MAAkB;AACtC,SAAO,KAAK,IAAI,WAAW,qBAAqB,GAAG,IAAI,CAAC,CAAC;AAC3D;AAKA,SAAS,oBAAoB,YAAe;AAC1C,SAAO,oBAAoB,UAAU,EAAE;AACzC;AAMA,SAAS,YACP,SACA,YACA,UAAe,YAAY,EAAE,GAAC;AAE9B,QAAM,IAAI,YAAY,WAAW,OAAO;AACxC,QAAM,EAAE,OAAO,IAAI,QAAQ,EAAC,IAAK,oBAAoB,UAAU;AAC/D,QAAM,IAAI,YAAY,WAAW,SAAS,EAAE;AAC5C,QAAM,IAAI,SAAS,IAAI,IAAI,WAAW,eAAe,CAAC,CAAC,CAAC;AACxD,QAAM,OAAO,WAAW,iBAAiB,GAAG,IAAI,CAAC;AACjD,QAAM,KAAK,KAAK,IAAI,IAAI,CAAC;AACzB,MAAI,OAAOD;AAAK,UAAM,IAAI,MAAM,wBAAwB;AACxD,QAAM,EAAE,OAAO,IAAI,QAAQ,EAAC,IAAK,oBAAoB,EAAE;AACvD,QAAM,IAAI,UAAU,IAAI,IAAI,CAAC;AAC7B,QAAM,MAAM,IAAI,WAAW,EAAE;AAC7B,MAAI,IAAI,IAAI,CAAC;AACb,MAAI,IAAI,SAAS,KAAK,IAAI,IAAI,CAAC,CAAC,GAAG,EAAE;AAErC,MAAI,CAAC,cAAc,KAAK,GAAG,EAAE;AAAG,UAAM,IAAI,MAAM,kCAAkC;AAClF,SAAO;AACT;AAMA,SAAS,cAAc,WAAgB,SAAc,WAAc;AACjE,QAAM,MAAM,YAAY,aAAa,WAAW,EAAE;AAClD,QAAM,IAAI,YAAY,WAAW,OAAO;AACxC,QAAM,MAAM,YAAY,aAAa,WAAW,EAAE;AAClD,MAAI;AACF,UAAM,IAAI,OAAO,IAAI,GAAG,CAAC;AACzB,UAAM,IAAI,IAAI,IAAI,SAAS,GAAG,EAAE,CAAC;AACjC,QAAI,CAAC,QAAQ,GAAGC,MAAK,gBAAgB,CAAC;AAAG,aAAO;AAChD,UAAM,IAAI,IAAI,IAAI,SAAS,IAAI,EAAE,CAAC;AAClC,QAAI,CAAC,QAAQ,GAAGA,MAAK,gBAAgB,CAAC;AAAG,aAAO;AAChD,UAAM,IAAI,UAAU,SAAS,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC;AAEnD,UAAM,IAAI,MAAM,KAAK,eAAe,CAAC,EAAE,IAAI,EAAE,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC;AACrE,UAAM,EAAE,GAAG,EAAC,IAAK,EAAE,SAAQ;AAE3B,QAAI,EAAE,IAAG,KAAM,CAAC,QAAQ,CAAC,KAAK,MAAM;AAAG,aAAO;AAC9C,WAAO;EACT,SAAS,OAAO;AACd,WAAO;EACT;AACF;AA6BO,IAAM,WAAwC,OAAO;EAC1D,cAAc;EACd,MAAM;EACN,QAAQ;EACR,OAAO;IACL,kBAAkB,UAAU,MAAM;IAClC;IACA;IACA;IACA;IACA;IACA;;IAED;AAEH,IAAM,UAA0B,MAC9B,WACE,MACA;;EAEE;IACE;IACA;IACA;IACA;;;EAGF;IACE;IACA;IACA;;;;EAGF;IACE;IACA;IACA;IACA;;;EAGF;IACE;IACA;IACA;IACA;;;EAEF,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,CAA6C,GACjF;AACJ,IAAM,UAA0B,MAC9B,oBAAoB,MAAM;EACxB,GAAG,OAAO,oEAAoE;EAC9E,GAAG,OAAO,MAAM;EAChB,GAAG,KAAK,OAAO,OAAO,KAAK,CAAC;CAC7B,GAAE;AAEE,IAAM,oBAAuD,MAClE,aACE,UAAU,OACV,CAAC,YAAqB;AACpB,QAAM,EAAE,GAAG,EAAC,IAAK,OAAO,KAAK,OAAO,QAAQ,CAAC,CAAC,CAAC;AAC/C,SAAO,OAAO,GAAG,CAAC;AACpB,GACA;EACE,KAAK;EACL,WAAW;EACX,GAAG,KAAK;EACR,GAAG;EACH,GAAG;EACH,QAAQ;EACR,MAAM;CACP,GACD;AAEG,IAAM,eAAkD,MAC7D,iBAAiB,aAAY;AAExB,IAAM,iBAAoD,MAC/D,iBAAiB,eAAc;", "names": ["num", "num", "num", "_0n", "_1n", "wbits", "_0n", "num", "_0n", "_1n", "_2n", "_3n", "_4n", "pointToBytes", "toBytes", "Point", "endo", "Point", "_1n", "num", "_2n", "_0n", "ecdsa", "Point", "_0n", "_1n", "_2n", "tv5", "_4n", "_3n", "c1", "c2", "Point", "num", "_0n", "_1n", "_2n", "_3n"]}