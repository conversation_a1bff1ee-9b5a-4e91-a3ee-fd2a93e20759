{"version": 3, "sources": ["../../dayjs/dayjs.min.js", "../../dayjs/locale/en.js", "../../dayjs/plugin/relativeTime.js", "../../dayjs/plugin/updateLocale.js", "../../@reown/appkit-common/src/utils/ConstantsUtil.ts", "../../@reown/appkit-common/src/utils/DateUtil.ts", "../../@reown/appkit-common/src/utils/NetworkUtil.ts", "../../big.js/big.mjs", "../../@reown/appkit-common/src/utils/NumberUtil.ts", "../../@reown/appkit-common/src/contracts/erc20.ts", "../../@reown/appkit-common/src/contracts/swap.ts", "../../@reown/appkit-common/src/contracts/usdt.ts", "../../@reown/appkit-common/src/utils/ContractUtil.ts", "../../@reown/appkit-common/src/utils/EmitterUtil.ts", "../../@reown/appkit-common/src/utils/ParseUtil.ts", "../../@reown/appkit-common/src/utils/SafeLocalStorage.ts", "../../@reown/appkit-common/src/utils/ThemeUtil.ts", "../../@reown/appkit-controllers/src/utils/StorageUtil.ts", "../../@reown/appkit-controllers/src/utils/ConstantsUtil.ts", "../../@reown/appkit-controllers/src/utils/CoreHelperUtil.ts", "../../proxy-compare/src/index.ts", "../../valtio/esm/vanilla.mjs", "../../valtio/esm/vanilla/utils.mjs", "../../@reown/appkit-controllers/src/utils/OptionsUtil.ts", "../../@reown/appkit-controllers/src/controllers/OptionsController.ts", "../../@reown/appkit-controllers/src/utils/FetchUtil.ts", "../../@reown/appkit-controllers/src/controllers/TelemetryController.ts", "../../@reown/appkit-controllers/src/utils/withErrorBoundary.ts", "../../@reown/appkit-controllers/src/controllers/AssetController.ts", "../../@reown/appkit-controllers/src/utils/AssetUtil.ts", "../../@reown/appkit-controllers/src/utils/MobileWallet.ts", "../../@reown/appkit-controllers/src/controllers/SnackController.ts", "../../@reown/appkit-controllers/src/controllers/BlockchainApiController.ts", "../../@reown/appkit-controllers/src/controllers/AccountController.ts", "../../@reown/appkit-controllers/src/utils/NetworkUtil.ts", "../../@reown/appkit-controllers/src/controllers/AlertController.ts", "../../@reown/appkit-controllers/src/controllers/EventsController.ts", "../../@reown/appkit-controllers/src/controllers/PublicStateController.ts", "../../@reown/appkit-controllers/src/controllers/ModalController.ts", "../../@reown/appkit-controllers/src/controllers/RouterController.ts", "../../@reown/appkit-controllers/src/controllers/ThemeController.ts", "../../@reown/appkit-controllers/src/controllers/ConnectorController.ts", "../../@reown/appkit-wallet/src/W3mFrameConstants.ts", "../../@reown/appkit-controllers/src/controllers/TransactionsController.ts", "../../@reown/appkit-controllers/src/controllers/ConnectionController.ts", "../../@reown/appkit-controllers/src/utils/ERC7811Util.ts", "../../@reown/appkit-controllers/src/utils/SendApiUtil.ts", "../../@reown/appkit-controllers/src/controllers/SendController.ts", "../../@reown/appkit-controllers/src/controllers/ChainController.ts", "../../@reown/appkit-controllers/src/controllers/ApiController.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/formatters/transaction.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/formatters/block.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/actions/public/getTransactionCount.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/constants/blob.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/formatters/log.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/actions/wallet/sendTransaction.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/promise/withDedupe.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/rpc/id.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/formatters/transactionReceipt.ts", "../../@reown/appkit-controllers/node_modules/@noble/hashes/src/ripemd160.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/nonceManager.ts", "../../@reown/appkit-controllers/node_modules/ox/core/version.ts", "../../@reown/appkit-controllers/node_modules/ox/core/internal/errors.ts", "../../@reown/appkit-controllers/node_modules/ox/core/Errors.ts", "../../@reown/appkit-controllers/node_modules/ox/core/internal/bytes.ts", "../../@reown/appkit-controllers/node_modules/ox/core/internal/hex.ts", "../../@reown/appkit-controllers/node_modules/ox/core/Bytes.ts", "../../@reown/appkit-controllers/node_modules/ox/core/Hex.ts", "../../@reown/appkit-controllers/node_modules/ox/core/Hash.ts", "../../@reown/appkit-controllers/node_modules/ox/core/internal/lru.ts", "../../@reown/appkit-controllers/node_modules/ox/core/Caches.ts", "../../@reown/appkit-controllers/node_modules/ox/core/Address.ts", "../../@reown/appkit-controllers/node_modules/ox/core/Solidity.ts", "../../@reown/appkit-controllers/node_modules/ox/core/internal/cursor.ts", "../../@reown/appkit-controllers/node_modules/ox/core/AbiParameters.ts", "../../@reown/appkit-controllers/src/utils/SIWXUtil.ts", "../../@reown/appkit-controllers/src/utils/ModalUtil.ts", "../../@reown/appkit-controllers/src/controllers/OnRampController.ts", "../../@reown/appkit-controllers/src/utils/SwapApiUtil.ts", "../../@reown/appkit-controllers/src/utils/SwapCalculationUtil.ts", "../../@reown/appkit-controllers/src/controllers/SwapController.ts", "../../@reown/appkit-controllers/src/controllers/TooltipController.ts", "../../@reown/appkit-controllers/src/utils/EnsUtil.ts", "../../@reown/appkit-controllers/src/controllers/EnsController.ts", "../../@reown/appkit-controllers/src/controllers/OptionsStateController.ts", "../../@reown/appkit-ui/src/utils/ThemeUtil.ts", "../../@reown/appkit-ui/src/utils/UiHelperUtil.ts", "../../@reown/appkit-ui/src/utils/WebComponentsUtil.ts", "../../@reown/appkit-scaffold-ui/src/utils/ConstantsUtil.ts"], "sourcesContent": ["!function(t,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define(e):(t=\"undefined\"!=typeof globalThis?globalThis:t||self).dayjs=e()}(this,(function(){\"use strict\";var t=1e3,e=6e4,n=36e5,r=\"millisecond\",i=\"second\",s=\"minute\",u=\"hour\",a=\"day\",o=\"week\",c=\"month\",f=\"quarter\",h=\"year\",d=\"date\",l=\"Invalid Date\",$=/^(\\d{4})[-/]?(\\d{1,2})?[-/]?(\\d{0,2})[Tt\\s]*(\\d{1,2})?:?(\\d{1,2})?:?(\\d{1,2})?[.:]?(\\d+)?$/,y=/\\[([^\\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,M={name:\"en\",weekdays:\"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday\".split(\"_\"),months:\"January_February_March_April_May_June_July_August_September_October_November_December\".split(\"_\"),ordinal:function(t){var e=[\"th\",\"st\",\"nd\",\"rd\"],n=t%100;return\"[\"+t+(e[(n-20)%10]||e[n]||e[0])+\"]\"}},m=function(t,e,n){var r=String(t);return!r||r.length>=e?t:\"\"+Array(e+1-r.length).join(n)+t},v={s:m,z:function(t){var e=-t.utcOffset(),n=Math.abs(e),r=Math.floor(n/60),i=n%60;return(e<=0?\"+\":\"-\")+m(r,2,\"0\")+\":\"+m(i,2,\"0\")},m:function t(e,n){if(e.date()<n.date())return-t(n,e);var r=12*(n.year()-e.year())+(n.month()-e.month()),i=e.clone().add(r,c),s=n-i<0,u=e.clone().add(r+(s?-1:1),c);return+(-(r+(n-i)/(s?i-u:u-i))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(t){return{M:c,y:h,w:o,d:a,D:d,h:u,m:s,s:i,ms:r,Q:f}[t]||String(t||\"\").toLowerCase().replace(/s$/,\"\")},u:function(t){return void 0===t}},g=\"en\",D={};D[g]=M;var p=\"$isDayjsObject\",S=function(t){return t instanceof _||!(!t||!t[p])},w=function t(e,n,r){var i;if(!e)return g;if(\"string\"==typeof e){var s=e.toLowerCase();D[s]&&(i=s),n&&(D[s]=n,i=s);var u=e.split(\"-\");if(!i&&u.length>1)return t(u[0])}else{var a=e.name;D[a]=e,i=a}return!r&&i&&(g=i),i||!r&&g},O=function(t,e){if(S(t))return t.clone();var n=\"object\"==typeof e?e:{};return n.date=t,n.args=arguments,new _(n)},b=v;b.l=w,b.i=S,b.w=function(t,e){return O(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var _=function(){function M(t){this.$L=w(t.locale,null,!0),this.parse(t),this.$x=this.$x||t.x||{},this[p]=!0}var m=M.prototype;return m.parse=function(t){this.$d=function(t){var e=t.date,n=t.utc;if(null===e)return new Date(NaN);if(b.u(e))return new Date;if(e instanceof Date)return new Date(e);if(\"string\"==typeof e&&!/Z$/i.test(e)){var r=e.match($);if(r){var i=r[2]-1||0,s=(r[7]||\"0\").substring(0,3);return n?new Date(Date.UTC(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)):new Date(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)}}return new Date(e)}(t),this.init()},m.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},m.$utils=function(){return b},m.isValid=function(){return!(this.$d.toString()===l)},m.isSame=function(t,e){var n=O(t);return this.startOf(e)<=n&&n<=this.endOf(e)},m.isAfter=function(t,e){return O(t)<this.startOf(e)},m.isBefore=function(t,e){return this.endOf(e)<O(t)},m.$g=function(t,e,n){return b.u(t)?this[e]:this.set(n,t)},m.unix=function(){return Math.floor(this.valueOf()/1e3)},m.valueOf=function(){return this.$d.getTime()},m.startOf=function(t,e){var n=this,r=!!b.u(e)||e,f=b.p(t),l=function(t,e){var i=b.w(n.$u?Date.UTC(n.$y,e,t):new Date(n.$y,e,t),n);return r?i:i.endOf(a)},$=function(t,e){return b.w(n.toDate()[t].apply(n.toDate(\"s\"),(r?[0,0,0,0]:[23,59,59,999]).slice(e)),n)},y=this.$W,M=this.$M,m=this.$D,v=\"set\"+(this.$u?\"UTC\":\"\");switch(f){case h:return r?l(1,0):l(31,11);case c:return r?l(1,M):l(0,M+1);case o:var g=this.$locale().weekStart||0,D=(y<g?y+7:y)-g;return l(r?m-D:m+(6-D),M);case a:case d:return $(v+\"Hours\",0);case u:return $(v+\"Minutes\",1);case s:return $(v+\"Seconds\",2);case i:return $(v+\"Milliseconds\",3);default:return this.clone()}},m.endOf=function(t){return this.startOf(t,!1)},m.$set=function(t,e){var n,o=b.p(t),f=\"set\"+(this.$u?\"UTC\":\"\"),l=(n={},n[a]=f+\"Date\",n[d]=f+\"Date\",n[c]=f+\"Month\",n[h]=f+\"FullYear\",n[u]=f+\"Hours\",n[s]=f+\"Minutes\",n[i]=f+\"Seconds\",n[r]=f+\"Milliseconds\",n)[o],$=o===a?this.$D+(e-this.$W):e;if(o===c||o===h){var y=this.clone().set(d,1);y.$d[l]($),y.init(),this.$d=y.set(d,Math.min(this.$D,y.daysInMonth())).$d}else l&&this.$d[l]($);return this.init(),this},m.set=function(t,e){return this.clone().$set(t,e)},m.get=function(t){return this[b.p(t)]()},m.add=function(r,f){var d,l=this;r=Number(r);var $=b.p(f),y=function(t){var e=O(l);return b.w(e.date(e.date()+Math.round(t*r)),l)};if($===c)return this.set(c,this.$M+r);if($===h)return this.set(h,this.$y+r);if($===a)return y(1);if($===o)return y(7);var M=(d={},d[s]=e,d[u]=n,d[i]=t,d)[$]||1,m=this.$d.getTime()+r*M;return b.w(m,this)},m.subtract=function(t,e){return this.add(-1*t,e)},m.format=function(t){var e=this,n=this.$locale();if(!this.isValid())return n.invalidDate||l;var r=t||\"YYYY-MM-DDTHH:mm:ssZ\",i=b.z(this),s=this.$H,u=this.$m,a=this.$M,o=n.weekdays,c=n.months,f=n.meridiem,h=function(t,n,i,s){return t&&(t[n]||t(e,r))||i[n].slice(0,s)},d=function(t){return b.s(s%12||12,t,\"0\")},$=f||function(t,e,n){var r=t<12?\"AM\":\"PM\";return n?r.toLowerCase():r};return r.replace(y,(function(t,r){return r||function(t){switch(t){case\"YY\":return String(e.$y).slice(-2);case\"YYYY\":return b.s(e.$y,4,\"0\");case\"M\":return a+1;case\"MM\":return b.s(a+1,2,\"0\");case\"MMM\":return h(n.monthsShort,a,c,3);case\"MMMM\":return h(c,a);case\"D\":return e.$D;case\"DD\":return b.s(e.$D,2,\"0\");case\"d\":return String(e.$W);case\"dd\":return h(n.weekdaysMin,e.$W,o,2);case\"ddd\":return h(n.weekdaysShort,e.$W,o,3);case\"dddd\":return o[e.$W];case\"H\":return String(s);case\"HH\":return b.s(s,2,\"0\");case\"h\":return d(1);case\"hh\":return d(2);case\"a\":return $(s,u,!0);case\"A\":return $(s,u,!1);case\"m\":return String(u);case\"mm\":return b.s(u,2,\"0\");case\"s\":return String(e.$s);case\"ss\":return b.s(e.$s,2,\"0\");case\"SSS\":return b.s(e.$ms,3,\"0\");case\"Z\":return i}return null}(t)||i.replace(\":\",\"\")}))},m.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},m.diff=function(r,d,l){var $,y=this,M=b.p(d),m=O(r),v=(m.utcOffset()-this.utcOffset())*e,g=this-m,D=function(){return b.m(y,m)};switch(M){case h:$=D()/12;break;case c:$=D();break;case f:$=D()/3;break;case o:$=(g-v)/6048e5;break;case a:$=(g-v)/864e5;break;case u:$=g/n;break;case s:$=g/e;break;case i:$=g/t;break;default:$=g}return l?$:b.a($)},m.daysInMonth=function(){return this.endOf(c).$D},m.$locale=function(){return D[this.$L]},m.locale=function(t,e){if(!t)return this.$L;var n=this.clone(),r=w(t,e,!0);return r&&(n.$L=r),n},m.clone=function(){return b.w(this.$d,this)},m.toDate=function(){return new Date(this.valueOf())},m.toJSON=function(){return this.isValid()?this.toISOString():null},m.toISOString=function(){return this.$d.toISOString()},m.toString=function(){return this.$d.toUTCString()},M}(),k=_.prototype;return O.prototype=k,[[\"$ms\",r],[\"$s\",i],[\"$m\",s],[\"$H\",u],[\"$W\",a],[\"$M\",c],[\"$y\",h],[\"$D\",d]].forEach((function(t){k[t[1]]=function(e){return this.$g(e,t[0],t[1])}})),O.extend=function(t,e){return t.$i||(t(e,_,O),t.$i=!0),O},O.locale=w,O.isDayjs=S,O.unix=function(t){return O(1e3*t)},O.en=D[g],O.Ls=D,O.p={},O}));", "!function(e,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n():\"function\"==typeof define&&define.amd?define(n):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_en=n()}(this,(function(){\"use strict\";return{name:\"en\",weekdays:\"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday\".split(\"_\"),months:\"January_February_March_April_May_June_July_August_September_October_November_December\".split(\"_\"),ordinal:function(e){var n=[\"th\",\"st\",\"nd\",\"rd\"],t=e%100;return\"[\"+e+(n[(t-20)%10]||n[t]||n[0])+\"]\"}}}));", "!function(r,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define(e):(r=\"undefined\"!=typeof globalThis?globalThis:r||self).dayjs_plugin_relativeTime=e()}(this,(function(){\"use strict\";return function(r,e,t){r=r||{};var n=e.prototype,o={future:\"in %s\",past:\"%s ago\",s:\"a few seconds\",m:\"a minute\",mm:\"%d minutes\",h:\"an hour\",hh:\"%d hours\",d:\"a day\",dd:\"%d days\",M:\"a month\",MM:\"%d months\",y:\"a year\",yy:\"%d years\"};function i(r,e,t,o){return n.fromToBase(r,e,t,o)}t.en.relativeTime=o,n.fromToBase=function(e,n,i,d,u){for(var f,a,s,l=i.$locale().relativeTime||o,h=r.thresholds||[{l:\"s\",r:44,d:\"second\"},{l:\"m\",r:89},{l:\"mm\",r:44,d:\"minute\"},{l:\"h\",r:89},{l:\"hh\",r:21,d:\"hour\"},{l:\"d\",r:35},{l:\"dd\",r:25,d:\"day\"},{l:\"M\",r:45},{l:\"MM\",r:10,d:\"month\"},{l:\"y\",r:17},{l:\"yy\",d:\"year\"}],m=h.length,c=0;c<m;c+=1){var y=h[c];y.d&&(f=d?t(e).diff(i,y.d,!0):i.diff(e,y.d,!0));var p=(r.rounding||Math.round)(Math.abs(f));if(s=f>0,p<=y.r||!y.r){p<=1&&c>0&&(y=h[c-1]);var v=l[y.l];u&&(p=u(\"\"+p)),a=\"string\"==typeof v?v.replace(\"%d\",p):v(p,n,y.l,s);break}}if(n)return a;var M=s?l.future:l.past;return\"function\"==typeof M?M(a):M.replace(\"%s\",a)},n.to=function(r,e){return i(r,e,this,!0)},n.from=function(r,e){return i(r,e,this)};var d=function(r){return r.$u?t.utc():t()};n.toNow=function(r){return this.to(d(this),r)},n.fromNow=function(r){return this.from(d(this),r)}}}));", "!function(e,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n():\"function\"==typeof define&&define.amd?define(n):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_updateLocale=n()}(this,(function(){\"use strict\";return function(e,n,t){t.updateLocale=function(e,n){var o=t.Ls[e];if(o)return(n?Object.keys(n):[]).forEach((function(e){o[e]=n[e]})),o}}}));", null, null, null, "/*\r\n *  big.js v6.2.2\r\n *  A small, fast, easy-to-use library for arbitrary-precision decimal arithmetic.\r\n *  Copyright (c) 2024 <PERSON>\r\n *  https://github.com/MikeMcl/big.js/LICENCE.md\r\n */\r\n\r\n\r\n/************************************** EDITABLE DEFAULTS *****************************************/\r\n\r\n\r\n  // The default values below must be integers within the stated ranges.\r\n\r\n  /*\r\n   * The maximum number of decimal places (DP) of the results of operations involving division:\r\n   * div and sqrt, and pow with negative exponents.\r\n   */\r\nvar DP = 20,          // 0 to MAX_DP\r\n\r\n  /*\r\n   * The rounding mode (RM) used when rounding to the above decimal places.\r\n   *\r\n   *  0  Towards zero (i.e. truncate, no rounding).       (ROUND_DOWN)\r\n   *  1  To nearest neighbour. If equidistant, round up.  (ROUND_HALF_UP)\r\n   *  2  To nearest neighbour. If equidistant, to even.   (ROUND_HALF_EVEN)\r\n   *  3  Away from zero.                                  (ROUND_UP)\r\n   */\r\n  RM = 1,             // 0, 1, 2 or 3\r\n\r\n  // The maximum value of DP and Big.DP.\r\n  MAX_DP = 1E6,       // 0 to 1000000\r\n\r\n  // The maximum magnitude of the exponent argument to the pow method.\r\n  MAX_POWER = 1E6,    // 1 to 1000000\r\n\r\n  /*\r\n   * The negative exponent (NE) at and beneath which toString returns exponential notation.\r\n   * (JavaScript numbers: -7)\r\n   * -1000000 is the minimum recommended exponent value of a Big.\r\n   */\r\n  NE = -7,            // 0 to -1000000\r\n\r\n  /*\r\n   * The positive exponent (PE) at and above which toString returns exponential notation.\r\n   * (JavaScript numbers: 21)\r\n   * 1000000 is the maximum recommended exponent value of a Big, but this limit is not enforced.\r\n   */\r\n  PE = 21,            // 0 to 1000000\r\n\r\n  /*\r\n   * When true, an error will be thrown if a primitive number is passed to the Big constructor,\r\n   * or if valueOf is called, or if toNumber is called on a Big which cannot be converted to a\r\n   * primitive number without a loss of precision.\r\n   */\r\n  STRICT = false,     // true or false\r\n\r\n\r\n/**************************************************************************************************/\r\n\r\n\r\n  // Error messages.\r\n  NAME = '[big.js] ',\r\n  INVALID = NAME + 'Invalid ',\r\n  INVALID_DP = INVALID + 'decimal places',\r\n  INVALID_RM = INVALID + 'rounding mode',\r\n  DIV_BY_ZERO = NAME + 'Division by zero',\r\n\r\n  // The shared prototype object.\r\n  P = {},\r\n  UNDEFINED = void 0,\r\n  NUMERIC = /^-?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i;\r\n\r\n\r\n/*\r\n * Create and return a Big constructor.\r\n */\r\nfunction _Big_() {\r\n\r\n  /*\r\n   * The Big constructor and exported function.\r\n   * Create and return a new instance of a Big number object.\r\n   *\r\n   * n {number|string|Big} A numeric value.\r\n   */\r\n  function Big(n) {\r\n    var x = this;\r\n\r\n    // Enable constructor usage without new.\r\n    if (!(x instanceof Big)) return n === UNDEFINED ? _Big_() : new Big(n);\r\n\r\n    // Duplicate.\r\n    if (n instanceof Big) {\r\n      x.s = n.s;\r\n      x.e = n.e;\r\n      x.c = n.c.slice();\r\n    } else {\r\n      if (typeof n !== 'string') {\r\n        if (Big.strict === true && typeof n !== 'bigint') {\r\n          throw TypeError(INVALID + 'value');\r\n        }\r\n\r\n        // Minus zero?\r\n        n = n === 0 && 1 / n < 0 ? '-0' : String(n);\r\n      }\r\n\r\n      parse(x, n);\r\n    }\r\n\r\n    // Retain a reference to this Big constructor.\r\n    // Shadow Big.prototype.constructor which points to Object.\r\n    x.constructor = Big;\r\n  }\r\n\r\n  Big.prototype = P;\r\n  Big.DP = DP;\r\n  Big.RM = RM;\r\n  Big.NE = NE;\r\n  Big.PE = PE;\r\n  Big.strict = STRICT;\r\n  Big.roundDown = 0;\r\n  Big.roundHalfUp = 1;\r\n  Big.roundHalfEven = 2;\r\n  Big.roundUp = 3;\r\n\r\n  return Big;\r\n}\r\n\r\n\r\n/*\r\n * Parse the number or string value passed to a Big constructor.\r\n *\r\n * x {Big} A Big number instance.\r\n * n {number|string} A numeric value.\r\n */\r\nfunction parse(x, n) {\r\n  var e, i, nl;\r\n\r\n  if (!NUMERIC.test(n)) {\r\n    throw Error(INVALID + 'number');\r\n  }\r\n\r\n  // Determine sign.\r\n  x.s = n.charAt(0) == '-' ? (n = n.slice(1), -1) : 1;\r\n\r\n  // Decimal point?\r\n  if ((e = n.indexOf('.')) > -1) n = n.replace('.', '');\r\n\r\n  // Exponential form?\r\n  if ((i = n.search(/e/i)) > 0) {\r\n\r\n    // Determine exponent.\r\n    if (e < 0) e = i;\r\n    e += +n.slice(i + 1);\r\n    n = n.substring(0, i);\r\n  } else if (e < 0) {\r\n\r\n    // Integer.\r\n    e = n.length;\r\n  }\r\n\r\n  nl = n.length;\r\n\r\n  // Determine leading zeros.\r\n  for (i = 0; i < nl && n.charAt(i) == '0';) ++i;\r\n\r\n  if (i == nl) {\r\n\r\n    // Zero.\r\n    x.c = [x.e = 0];\r\n  } else {\r\n\r\n    // Determine trailing zeros.\r\n    for (; nl > 0 && n.charAt(--nl) == '0';);\r\n    x.e = e - i - 1;\r\n    x.c = [];\r\n\r\n    // Convert string to array of digits without leading/trailing zeros.\r\n    for (e = 0; i <= nl;) x.c[e++] = +n.charAt(i++);\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Round Big x to a maximum of sd significant digits using rounding mode rm.\r\n *\r\n * x {Big} The Big to round.\r\n * sd {number} Significant digits: integer, 0 to MAX_DP inclusive.\r\n * rm {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n * [more] {boolean} Whether the result of division was truncated.\r\n */\r\nfunction round(x, sd, rm, more) {\r\n  var xc = x.c;\r\n\r\n  if (rm === UNDEFINED) rm = x.constructor.RM;\r\n  if (rm !== 0 && rm !== 1 && rm !== 2 && rm !== 3) {\r\n    throw Error(INVALID_RM);\r\n  }\r\n\r\n  if (sd < 1) {\r\n    more =\r\n      rm === 3 && (more || !!xc[0]) || sd === 0 && (\r\n      rm === 1 && xc[0] >= 5 ||\r\n      rm === 2 && (xc[0] > 5 || xc[0] === 5 && (more || xc[1] !== UNDEFINED))\r\n    );\r\n\r\n    xc.length = 1;\r\n\r\n    if (more) {\r\n\r\n      // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n      x.e = x.e - sd + 1;\r\n      xc[0] = 1;\r\n    } else {\r\n\r\n      // Zero.\r\n      xc[0] = x.e = 0;\r\n    }\r\n  } else if (sd < xc.length) {\r\n\r\n    // xc[sd] is the digit after the digit that may be rounded up.\r\n    more =\r\n      rm === 1 && xc[sd] >= 5 ||\r\n      rm === 2 && (xc[sd] > 5 || xc[sd] === 5 &&\r\n        (more || xc[sd + 1] !== UNDEFINED || xc[sd - 1] & 1)) ||\r\n      rm === 3 && (more || !!xc[0]);\r\n\r\n    // Remove any digits after the required precision.\r\n    xc.length = sd;\r\n\r\n    // Round up?\r\n    if (more) {\r\n\r\n      // Rounding up may mean the previous digit has to be rounded up.\r\n      for (; ++xc[--sd] > 9;) {\r\n        xc[sd] = 0;\r\n        if (sd === 0) {\r\n          ++x.e;\r\n          xc.unshift(1);\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (sd = xc.length; !xc[--sd];) xc.pop();\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Return a string representing the value of Big x in normal or exponential notation.\r\n * Handles P.toExponential, P.toFixed, P.toJSON, P.toPrecision, P.toString and P.valueOf.\r\n */\r\nfunction stringify(x, doExponential, isNonzero) {\r\n  var e = x.e,\r\n    s = x.c.join(''),\r\n    n = s.length;\r\n\r\n  // Exponential notation?\r\n  if (doExponential) {\r\n    s = s.charAt(0) + (n > 1 ? '.' + s.slice(1) : '') + (e < 0 ? 'e' : 'e+') + e;\r\n\r\n  // Normal notation.\r\n  } else if (e < 0) {\r\n    for (; ++e;) s = '0' + s;\r\n    s = '0.' + s;\r\n  } else if (e > 0) {\r\n    if (++e > n) {\r\n      for (e -= n; e--;) s += '0';\r\n    } else if (e < n) {\r\n      s = s.slice(0, e) + '.' + s.slice(e);\r\n    }\r\n  } else if (n > 1) {\r\n    s = s.charAt(0) + '.' + s.slice(1);\r\n  }\r\n\r\n  return x.s < 0 && isNonzero ? '-' + s : s;\r\n}\r\n\r\n\r\n// Prototype/instance methods\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the absolute value of this Big.\r\n */\r\nP.abs = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = 1;\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return 1 if the value of this Big is greater than the value of Big y,\r\n *       -1 if the value of this Big is less than the value of Big y, or\r\n *        0 if they have the same value.\r\n */\r\nP.cmp = function (y) {\r\n  var isneg,\r\n    x = this,\r\n    xc = x.c,\r\n    yc = (y = new x.constructor(y)).c,\r\n    i = x.s,\r\n    j = y.s,\r\n    k = x.e,\r\n    l = y.e;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) return !xc[0] ? !yc[0] ? 0 : -j : i;\r\n\r\n  // Signs differ?\r\n  if (i != j) return i;\r\n\r\n  isneg = i < 0;\r\n\r\n  // Compare exponents.\r\n  if (k != l) return k > l ^ isneg ? 1 : -1;\r\n\r\n  j = (k = xc.length) < (l = yc.length) ? k : l;\r\n\r\n  // Compare digit by digit.\r\n  for (i = -1; ++i < j;) {\r\n    if (xc[i] != yc[i]) return xc[i] > yc[i] ^ isneg ? 1 : -1;\r\n  }\r\n\r\n  // Compare lengths.\r\n  return k == l ? 0 : k > l ^ isneg ? 1 : -1;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big divided by the value of Big y, rounded,\r\n * if necessary, to a maximum of Big.DP decimal places using rounding mode Big.RM.\r\n */\r\nP.div = function (y) {\r\n  var x = this,\r\n    Big = x.constructor,\r\n    a = x.c,                  // dividend\r\n    b = (y = new Big(y)).c,   // divisor\r\n    k = x.s == y.s ? 1 : -1,\r\n    dp = Big.DP;\r\n\r\n  if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n    throw Error(INVALID_DP);\r\n  }\r\n\r\n  // Divisor is zero?\r\n  if (!b[0]) {\r\n    throw Error(DIV_BY_ZERO);\r\n  }\r\n\r\n  // Dividend is 0? Return +-0.\r\n  if (!a[0]) {\r\n    y.s = k;\r\n    y.c = [y.e = 0];\r\n    return y;\r\n  }\r\n\r\n  var bl, bt, n, cmp, ri,\r\n    bz = b.slice(),\r\n    ai = bl = b.length,\r\n    al = a.length,\r\n    r = a.slice(0, bl),   // remainder\r\n    rl = r.length,\r\n    q = y,                // quotient\r\n    qc = q.c = [],\r\n    qi = 0,\r\n    p = dp + (q.e = x.e - y.e) + 1;    // precision of the result\r\n\r\n  q.s = k;\r\n  k = p < 0 ? 0 : p;\r\n\r\n  // Create version of divisor with leading zero.\r\n  bz.unshift(0);\r\n\r\n  // Add zeros to make remainder as long as divisor.\r\n  for (; rl++ < bl;) r.push(0);\r\n\r\n  do {\r\n\r\n    // n is how many times the divisor goes into current remainder.\r\n    for (n = 0; n < 10; n++) {\r\n\r\n      // Compare divisor and remainder.\r\n      if (bl != (rl = r.length)) {\r\n        cmp = bl > rl ? 1 : -1;\r\n      } else {\r\n        for (ri = -1, cmp = 0; ++ri < bl;) {\r\n          if (b[ri] != r[ri]) {\r\n            cmp = b[ri] > r[ri] ? 1 : -1;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      // If divisor < remainder, subtract divisor from remainder.\r\n      if (cmp < 0) {\r\n\r\n        // Remainder can't be more than 1 digit longer than divisor.\r\n        // Equalise lengths using divisor with extra leading zero?\r\n        for (bt = rl == bl ? b : bz; rl;) {\r\n          if (r[--rl] < bt[rl]) {\r\n            ri = rl;\r\n            for (; ri && !r[--ri];) r[ri] = 9;\r\n            --r[ri];\r\n            r[rl] += 10;\r\n          }\r\n          r[rl] -= bt[rl];\r\n        }\r\n\r\n        for (; !r[0];) r.shift();\r\n      } else {\r\n        break;\r\n      }\r\n    }\r\n\r\n    // Add the digit n to the result array.\r\n    qc[qi++] = cmp ? n : ++n;\r\n\r\n    // Update the remainder.\r\n    if (r[0] && cmp) r[rl] = a[ai] || 0;\r\n    else r = [a[ai]];\r\n\r\n  } while ((ai++ < al || r[0] !== UNDEFINED) && k--);\r\n\r\n  // Leading zero? Do not remove if result is simply zero (qi == 1).\r\n  if (!qc[0] && qi != 1) {\r\n\r\n    // There can't be more than one zero.\r\n    qc.shift();\r\n    q.e--;\r\n    p--;\r\n  }\r\n\r\n  // Round?\r\n  if (qi > p) round(q, p, Big.RM, r[0] !== UNDEFINED);\r\n\r\n  return q;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is equal to the value of Big y, otherwise return false.\r\n */\r\nP.eq = function (y) {\r\n  return this.cmp(y) === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is greater than the value of Big y, otherwise return\r\n * false.\r\n */\r\nP.gt = function (y) {\r\n  return this.cmp(y) > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is greater than or equal to the value of Big y, otherwise\r\n * return false.\r\n */\r\nP.gte = function (y) {\r\n  return this.cmp(y) > -1;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is less than the value of Big y, otherwise return false.\r\n */\r\nP.lt = function (y) {\r\n  return this.cmp(y) < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is less than or equal to the value of Big y, otherwise\r\n * return false.\r\n */\r\nP.lte = function (y) {\r\n  return this.cmp(y) < 1;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big minus the value of Big y.\r\n */\r\nP.minus = P.sub = function (y) {\r\n  var i, j, t, xlty,\r\n    x = this,\r\n    Big = x.constructor,\r\n    a = x.s,\r\n    b = (y = new Big(y)).s;\r\n\r\n  // Signs differ?\r\n  if (a != b) {\r\n    y.s = -b;\r\n    return x.plus(y);\r\n  }\r\n\r\n  var xc = x.c.slice(),\r\n    xe = x.e,\r\n    yc = y.c,\r\n    ye = y.e;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) {\r\n    if (yc[0]) {\r\n      y.s = -b;\r\n    } else if (xc[0]) {\r\n      y = new Big(x);\r\n    } else {\r\n      y.s = 1;\r\n    }\r\n    return y;\r\n  }\r\n\r\n  // Determine which is the bigger number. Prepend zeros to equalise exponents.\r\n  if (a = xe - ye) {\r\n\r\n    if (xlty = a < 0) {\r\n      a = -a;\r\n      t = xc;\r\n    } else {\r\n      ye = xe;\r\n      t = yc;\r\n    }\r\n\r\n    t.reverse();\r\n    for (b = a; b--;) t.push(0);\r\n    t.reverse();\r\n  } else {\r\n\r\n    // Exponents equal. Check digit by digit.\r\n    j = ((xlty = xc.length < yc.length) ? xc : yc).length;\r\n\r\n    for (a = b = 0; b < j; b++) {\r\n      if (xc[b] != yc[b]) {\r\n        xlty = xc[b] < yc[b];\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  // x < y? Point xc to the array of the bigger number.\r\n  if (xlty) {\r\n    t = xc;\r\n    xc = yc;\r\n    yc = t;\r\n    y.s = -y.s;\r\n  }\r\n\r\n  /*\r\n   * Append zeros to xc if shorter. No need to add zeros to yc if shorter as subtraction only\r\n   * needs to start at yc.length.\r\n   */\r\n  if ((b = (j = yc.length) - (i = xc.length)) > 0) for (; b--;) xc[i++] = 0;\r\n\r\n  // Subtract yc from xc.\r\n  for (b = i; j > a;) {\r\n    if (xc[--j] < yc[j]) {\r\n      for (i = j; i && !xc[--i];) xc[i] = 9;\r\n      --xc[i];\r\n      xc[j] += 10;\r\n    }\r\n\r\n    xc[j] -= yc[j];\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; xc[--b] === 0;) xc.pop();\r\n\r\n  // Remove leading zeros and adjust exponent accordingly.\r\n  for (; xc[0] === 0;) {\r\n    xc.shift();\r\n    --ye;\r\n  }\r\n\r\n  if (!xc[0]) {\r\n\r\n    // n - n = +0\r\n    y.s = 1;\r\n\r\n    // Result must be zero.\r\n    xc = [ye = 0];\r\n  }\r\n\r\n  y.c = xc;\r\n  y.e = ye;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big modulo the value of Big y.\r\n */\r\nP.mod = function (y) {\r\n  var ygtx,\r\n    x = this,\r\n    Big = x.constructor,\r\n    a = x.s,\r\n    b = (y = new Big(y)).s;\r\n\r\n  if (!y.c[0]) {\r\n    throw Error(DIV_BY_ZERO);\r\n  }\r\n\r\n  x.s = y.s = 1;\r\n  ygtx = y.cmp(x) == 1;\r\n  x.s = a;\r\n  y.s = b;\r\n\r\n  if (ygtx) return new Big(x);\r\n\r\n  a = Big.DP;\r\n  b = Big.RM;\r\n  Big.DP = Big.RM = 0;\r\n  x = x.div(y);\r\n  Big.DP = a;\r\n  Big.RM = b;\r\n\r\n  return this.minus(x.times(y));\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big negated.\r\n */\r\nP.neg = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = -x.s;\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big plus the value of Big y.\r\n */\r\nP.plus = P.add = function (y) {\r\n  var e, k, t,\r\n    x = this,\r\n    Big = x.constructor;\r\n\r\n  y = new Big(y);\r\n\r\n  // Signs differ?\r\n  if (x.s != y.s) {\r\n    y.s = -y.s;\r\n    return x.minus(y);\r\n  }\r\n\r\n  var xe = x.e,\r\n    xc = x.c,\r\n    ye = y.e,\r\n    yc = y.c;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) {\r\n    if (!yc[0]) {\r\n      if (xc[0]) {\r\n        y = new Big(x);\r\n      } else {\r\n        y.s = x.s;\r\n      }\r\n    }\r\n    return y;\r\n  }\r\n\r\n  xc = xc.slice();\r\n\r\n  // Prepend zeros to equalise exponents.\r\n  // Note: reverse faster than unshifts.\r\n  if (e = xe - ye) {\r\n    if (e > 0) {\r\n      ye = xe;\r\n      t = yc;\r\n    } else {\r\n      e = -e;\r\n      t = xc;\r\n    }\r\n\r\n    t.reverse();\r\n    for (; e--;) t.push(0);\r\n    t.reverse();\r\n  }\r\n\r\n  // Point xc to the longer array.\r\n  if (xc.length - yc.length < 0) {\r\n    t = yc;\r\n    yc = xc;\r\n    xc = t;\r\n  }\r\n\r\n  e = yc.length;\r\n\r\n  // Only start adding at yc.length - 1 as the further digits of xc can be left as they are.\r\n  for (k = 0; e; xc[e] %= 10) k = (xc[--e] = xc[e] + yc[e] + k) / 10 | 0;\r\n\r\n  // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n\r\n  if (k) {\r\n    xc.unshift(k);\r\n    ++ye;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (e = xc.length; xc[--e] === 0;) xc.pop();\r\n\r\n  y.c = xc;\r\n  y.e = ye;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a Big whose value is the value of this Big raised to the power n.\r\n * If n is negative, round to a maximum of Big.DP decimal places using rounding\r\n * mode Big.RM.\r\n *\r\n * n {number} Integer, -MAX_POWER to MAX_POWER inclusive.\r\n */\r\nP.pow = function (n) {\r\n  var x = this,\r\n    one = new x.constructor('1'),\r\n    y = one,\r\n    isneg = n < 0;\r\n\r\n  if (n !== ~~n || n < -MAX_POWER || n > MAX_POWER) {\r\n    throw Error(INVALID + 'exponent');\r\n  }\r\n\r\n  if (isneg) n = -n;\r\n\r\n  for (;;) {\r\n    if (n & 1) y = y.times(x);\r\n    n >>= 1;\r\n    if (!n) break;\r\n    x = x.times(x);\r\n  }\r\n\r\n  return isneg ? one.div(y) : y;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big rounded to a maximum precision of sd\r\n * significant digits using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * sd {number} Significant digits: integer, 1 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.prec = function (sd, rm) {\r\n  if (sd !== ~~sd || sd < 1 || sd > MAX_DP) {\r\n    throw Error(INVALID + 'precision');\r\n  }\r\n  return round(new this.constructor(this), sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big rounded to a maximum of dp decimal places\r\n * using rounding mode rm, or Big.RM if rm is not specified.\r\n * If dp is negative, round to an integer which is a multiple of 10**-dp.\r\n * If dp is not specified, round to 0 decimal places.\r\n *\r\n * dp? {number} Integer, -MAX_DP to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.round = function (dp, rm) {\r\n  if (dp === UNDEFINED) dp = 0;\r\n  else if (dp !== ~~dp || dp < -MAX_DP || dp > MAX_DP) {\r\n    throw Error(INVALID_DP);\r\n  }\r\n  return round(new this.constructor(this), dp + this.e + 1, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the square root of the value of this Big, rounded, if\r\n * necessary, to a maximum of Big.DP decimal places using rounding mode Big.RM.\r\n */\r\nP.sqrt = function () {\r\n  var r, c, t,\r\n    x = this,\r\n    Big = x.constructor,\r\n    s = x.s,\r\n    e = x.e,\r\n    half = new Big('0.5');\r\n\r\n  // Zero?\r\n  if (!x.c[0]) return new Big(x);\r\n\r\n  // Negative?\r\n  if (s < 0) {\r\n    throw Error(NAME + 'No square root');\r\n  }\r\n\r\n  // Estimate.\r\n  s = Math.sqrt(+stringify(x, true, true));\r\n\r\n  // Math.sqrt underflow/overflow?\r\n  // Re-estimate: pass x coefficient to Math.sqrt as integer, then adjust the result exponent.\r\n  if (s === 0 || s === 1 / 0) {\r\n    c = x.c.join('');\r\n    if (!(c.length + e & 1)) c += '0';\r\n    s = Math.sqrt(c);\r\n    e = ((e + 1) / 2 | 0) - (e < 0 || e & 1);\r\n    r = new Big((s == 1 / 0 ? '5e' : (s = s.toExponential()).slice(0, s.indexOf('e') + 1)) + e);\r\n  } else {\r\n    r = new Big(s + '');\r\n  }\r\n\r\n  e = r.e + (Big.DP += 4);\r\n\r\n  // Newton-Raphson iteration.\r\n  do {\r\n    t = r;\r\n    r = half.times(t.plus(x.div(t)));\r\n  } while (t.c.slice(0, e).join('') !== r.c.slice(0, e).join(''));\r\n\r\n  return round(r, (Big.DP -= 4) + r.e + 1, Big.RM);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big times the value of Big y.\r\n */\r\nP.times = P.mul = function (y) {\r\n  var c,\r\n    x = this,\r\n    Big = x.constructor,\r\n    xc = x.c,\r\n    yc = (y = new Big(y)).c,\r\n    a = xc.length,\r\n    b = yc.length,\r\n    i = x.e,\r\n    j = y.e;\r\n\r\n  // Determine sign of result.\r\n  y.s = x.s == y.s ? 1 : -1;\r\n\r\n  // Return signed 0 if either 0.\r\n  if (!xc[0] || !yc[0]) {\r\n    y.c = [y.e = 0];\r\n    return y;\r\n  }\r\n\r\n  // Initialise exponent of result as x.e + y.e.\r\n  y.e = i + j;\r\n\r\n  // If array xc has fewer digits than yc, swap xc and yc, and lengths.\r\n  if (a < b) {\r\n    c = xc;\r\n    xc = yc;\r\n    yc = c;\r\n    j = a;\r\n    a = b;\r\n    b = j;\r\n  }\r\n\r\n  // Initialise coefficient array of result with zeros.\r\n  for (c = new Array(j = a + b); j--;) c[j] = 0;\r\n\r\n  // Multiply.\r\n\r\n  // i is initially xc.length.\r\n  for (i = b; i--;) {\r\n    b = 0;\r\n\r\n    // a is yc.length.\r\n    for (j = a + i; j > i;) {\r\n\r\n      // Current sum of products at this digit position, plus carry.\r\n      b = c[j] + yc[i] * xc[j - i - 1] + b;\r\n      c[j--] = b % 10;\r\n\r\n      // carry\r\n      b = b / 10 | 0;\r\n    }\r\n\r\n    c[j] = b;\r\n  }\r\n\r\n  // Increment result exponent if there is a final carry, otherwise remove leading zero.\r\n  if (b) ++y.e;\r\n  else c.shift();\r\n\r\n  // Remove trailing zeros.\r\n  for (i = c.length; !c[--i];) c.pop();\r\n  y.c = c;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big in exponential notation rounded to dp fixed\r\n * decimal places using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * dp? {number} Decimal places: integer, 0 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.toExponential = function (dp, rm) {\r\n  var x = this,\r\n    n = x.c[0];\r\n\r\n  if (dp !== UNDEFINED) {\r\n    if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n      throw Error(INVALID_DP);\r\n    }\r\n    x = round(new x.constructor(x), ++dp, rm);\r\n    for (; x.c.length < dp;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, true, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big in normal notation rounded to dp fixed\r\n * decimal places using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * dp? {number} Decimal places: integer, 0 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n */\r\nP.toFixed = function (dp, rm) {\r\n  var x = this,\r\n    n = x.c[0];\r\n\r\n  if (dp !== UNDEFINED) {\r\n    if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n      throw Error(INVALID_DP);\r\n    }\r\n    x = round(new x.constructor(x), dp + x.e + 1, rm);\r\n\r\n    // x.e may have changed if the value is rounded up.\r\n    for (dp = dp + x.e + 1; x.c.length < dp;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, false, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big.\r\n * Return exponential notation if this Big has a positive exponent equal to or greater than\r\n * Big.PE, or a negative exponent equal to or less than Big.NE.\r\n * Omit the sign for negative zero.\r\n */\r\nP[Symbol.for('nodejs.util.inspect.custom')] = P.toJSON = P.toString = function () {\r\n  var x = this,\r\n    Big = x.constructor;\r\n  return stringify(x, x.e <= Big.NE || x.e >= Big.PE, !!x.c[0]);\r\n};\r\n\r\n\r\n/*\r\n * Return the value of this Big as a primitve number.\r\n */\r\nP.toNumber = function () {\r\n  var n = +stringify(this, true, true);\r\n  if (this.constructor.strict === true && !this.eq(n.toString())) {\r\n    throw Error(NAME + 'Imprecise conversion');\r\n  }\r\n  return n;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big rounded to sd significant digits using\r\n * rounding mode rm, or Big.RM if rm is not specified.\r\n * Use exponential notation if sd is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * sd {number} Significant digits: integer, 1 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.toPrecision = function (sd, rm) {\r\n  var x = this,\r\n    Big = x.constructor,\r\n    n = x.c[0];\r\n\r\n  if (sd !== UNDEFINED) {\r\n    if (sd !== ~~sd || sd < 1 || sd > MAX_DP) {\r\n      throw Error(INVALID + 'precision');\r\n    }\r\n    x = round(new Big(x), sd, rm);\r\n    for (; x.c.length < sd;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, sd <= x.e || x.e <= Big.NE || x.e >= Big.PE, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big.\r\n * Return exponential notation if this Big has a positive exponent equal to or greater than\r\n * Big.PE, or a negative exponent equal to or less than Big.NE.\r\n * Include the sign for negative zero.\r\n */\r\nP.valueOf = function () {\r\n  var x = this,\r\n    Big = x.constructor;\r\n  if (Big.strict === true) {\r\n    throw Error(NAME + 'valueOf disallowed');\r\n  }\r\n  return stringify(x, x.e <= Big.NE || x.e >= Big.PE, true);\r\n};\r\n\r\n\r\n// Export\r\n\r\n\r\nexport var Big = _Big_();\r\n\r\n/// <reference types=\"https://raw.githubusercontent.com/DefinitelyTyped/DefinitelyTyped/master/types/big.js/index.d.ts\" />\r\nexport default Big;\r\n", null, null, null, null, null, null, null, null, null, null, null, null, "// symbols\nconst TRACK_MEMO_SYMBOL = Symbol();\nconst GET_ORIGINAL_SYMBOL = Symbol();\n\n// properties\nconst AFFECTED_PROPERTY = 'a';\nconst IS_TARGET_COPIED_PROPERTY = 'f';\nconst PROXY_PROPERTY = 'p';\nconst PROXY_CACHE_PROPERTY = 'c';\nconst TARGET_CACHE_PROPERTY = 't';\nconst NEXT_OBJECT_PROPERTY = 'n';\nconst CHANGED_PROPERTY = 'g';\nconst HAS_KEY_PROPERTY = 'h';\nconst ALL_OWN_KEYS_PROPERTY = 'w';\nconst HAS_OWN_KEY_PROPERTY = 'o';\nconst KEYS_PROPERTY = 'k';\n\n// function to create a new bare proxy\nlet newProxy = <T extends object>(\n  target: T,\n  handler: ProxyHandler<T>,\n) => new Proxy(target, handler);\n\n// get object prototype\nconst getProto = Object.getPrototypeOf;\n\nconst objectsToTrack = new WeakMap<object, boolean>();\n\n// check if obj is a plain object or an array\nconst isObjectToTrack = <T>(obj: T): obj is T extends object ? T : never => (\n  obj && (objectsToTrack.has(obj as unknown as object)\n    ? objectsToTrack.get(obj as unknown as object) as boolean\n    : (getProto(obj) === Object.prototype || getProto(obj) === Array.prototype)\n  )\n);\n\n// check if it is object\nconst isObject = (x: unknown): x is object => (\n  typeof x === 'object' && x !== null\n);\n\n// Properties that are both non-configurable and non-writable will break\n// the proxy get trap when we try to return a recursive/child compare proxy\n// from them. We can avoid this by making a copy of the target object with\n// all descriptors marked as configurable, see `copyTargetObject`.\n// See: https://github.com/dai-shi/proxy-compare/pull/8\nconst needsToCopyTargetObject = (obj: object) => (\n  Object.values(Object.getOwnPropertyDescriptors(obj)).some(\n    (descriptor) => !descriptor.configurable && !descriptor.writable,\n  )\n);\n\n// Make a copy with all descriptors marked as configurable.\nconst copyTargetObject = <T extends object>(obj: T): T => {\n  if (Array.isArray(obj)) {\n    // Arrays need a special way to copy\n    return Array.from(obj) as T;\n  }\n  // For non-array objects, we create a new object keeping the prototype\n  // with changing all configurable options (otherwise, proxies will complain)\n  const descriptors = Object.getOwnPropertyDescriptors(obj);\n  Object.values(descriptors).forEach((desc) => { desc.configurable = true; });\n  return Object.create(getProto(obj), descriptors);\n};\n\ntype HasKeySet = Set<string | symbol>\ntype HasOwnKeySet = Set<string | symbol>\ntype KeysSet = Set<string | symbol>\ntype Used = {\n  [HAS_KEY_PROPERTY]?: HasKeySet;\n  [ALL_OWN_KEYS_PROPERTY]?: true;\n  [HAS_OWN_KEY_PROPERTY]?: HasOwnKeySet;\n  [KEYS_PROPERTY]?: KeysSet;\n};\ntype Affected = WeakMap<object, Used>;\ntype ProxyHandlerState<T extends object> = {\n  readonly [IS_TARGET_COPIED_PROPERTY]: boolean;\n  [PROXY_PROPERTY]?: T;\n  [PROXY_CACHE_PROPERTY]?: ProxyCache<object> | undefined;\n  [TARGET_CACHE_PROPERTY]?: TargetCache<object> | undefined;\n  [AFFECTED_PROPERTY]?: Affected;\n}\ntype ProxyCache<T extends object> = WeakMap<\n  object,\n  readonly [ProxyHandler<T>, ProxyHandlerState<T>]\n>;\ntype TargetCache<T extends object> = WeakMap<\n  object,\n  readonly [target: T, copiedTarget?: T]\n>;\n\nconst createProxyHandler = <T extends object>(origObj: T, isTargetCopied: boolean) => {\n  const state: ProxyHandlerState<T> = {\n    [IS_TARGET_COPIED_PROPERTY]: isTargetCopied,\n  };\n  let trackObject = false; // for trackMemo\n  const recordUsage = (\n    type:\n      | typeof HAS_KEY_PROPERTY\n      | typeof ALL_OWN_KEYS_PROPERTY\n      | typeof HAS_OWN_KEY_PROPERTY\n      | typeof KEYS_PROPERTY,\n    key?: string | symbol,\n  ) => {\n    if (!trackObject) {\n      let used = (state[AFFECTED_PROPERTY] as Affected).get(origObj);\n      if (!used) {\n        used = {};\n        (state[AFFECTED_PROPERTY] as Affected).set(origObj, used);\n      }\n      if (type === ALL_OWN_KEYS_PROPERTY) {\n        used[ALL_OWN_KEYS_PROPERTY] = true;\n      } else {\n        let set = used[type];\n        if (!set) {\n          set = new Set();\n          used[type] = set;\n        }\n        set.add(key as string | symbol);\n      }\n    }\n  };\n  const recordObjectAsUsed = () => {\n    trackObject = true;\n    (state[AFFECTED_PROPERTY] as Affected).delete(origObj);\n  };\n  const handler: ProxyHandler<T> = {\n    get(target, key) {\n      if (key === GET_ORIGINAL_SYMBOL) {\n        return origObj;\n      }\n      recordUsage(KEYS_PROPERTY, key);\n      return createProxy(\n        Reflect.get(target, key),\n        (state[AFFECTED_PROPERTY] as Affected),\n        state[PROXY_CACHE_PROPERTY],\n        state[TARGET_CACHE_PROPERTY],\n      );\n    },\n    has(target, key) {\n      if (key === TRACK_MEMO_SYMBOL) {\n        recordObjectAsUsed();\n        return true;\n      }\n      recordUsage(HAS_KEY_PROPERTY, key);\n      return Reflect.has(target, key);\n    },\n    getOwnPropertyDescriptor(target, key) {\n      recordUsage(HAS_OWN_KEY_PROPERTY, key);\n      return Reflect.getOwnPropertyDescriptor(target, key);\n    },\n    ownKeys(target) {\n      recordUsage(ALL_OWN_KEYS_PROPERTY);\n      return Reflect.ownKeys(target);\n    },\n  };\n  if (isTargetCopied) {\n    handler.set = handler.deleteProperty = () => false;\n  }\n  return [handler, state] as const;\n};\n\nconst getOriginalObject = <T extends object>(obj: T) => (\n  // unwrap proxy\n  (obj as { [GET_ORIGINAL_SYMBOL]?: typeof obj })[GET_ORIGINAL_SYMBOL]\n  // otherwise\n  || obj\n);\n\n/**\n * Create a proxy.\n *\n * This function will create a proxy at top level and proxy nested objects as you access them,\n * in order to keep track of which properties were accessed via get/has proxy handlers:\n *\n * NOTE: Printing of WeakMap is hard to inspect and not very readable\n * for this purpose you can use the `affectedToPathList` helper.\n *\n * @param {object} obj - Object that will be wrapped on the proxy.\n * @param {WeakMap<object, unknown>} affected -\n * WeakMap that will hold the tracking of which properties in the proxied object were accessed.\n * @param {WeakMap<object, unknown>} [proxyCache] -\n * WeakMap that will help keep referential identity for proxies.\n * @returns {Proxy<object>} - Object wrapped in a proxy.\n *\n * @example\n * import { createProxy } from 'proxy-compare';\n *\n * const original = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n * const proxy = createProxy(original, affected);\n *\n * proxy.a // Will mark as used and track its value.\n * // This will update the affected WeakMap with original as key\n * // and a Set with \"a\"\n *\n * proxy.d // Will mark \"d\" as accessed to track and proxy itself ({ e: \"3\" }).\n * // This will update the affected WeakMap with original as key\n * // and a Set with \"d\"\n */\nexport const createProxy = <T>(\n  obj: T,\n  affected: WeakMap<object, unknown>,\n  proxyCache?: WeakMap<object, unknown>,\n  targetCache?: WeakMap<object, unknown>,\n): T => {\n  if (!isObjectToTrack(obj)) return obj;\n  let targetAndCopied = (\n    targetCache && (targetCache as TargetCache<typeof obj>).get(obj)\n  );\n  if (!targetAndCopied) {\n    const target = getOriginalObject(obj);\n    if (needsToCopyTargetObject(target)) {\n      targetAndCopied = [target, copyTargetObject(target)];\n    } else {\n      targetAndCopied = [target];\n    }\n    targetCache?.set(obj, targetAndCopied);\n  }\n  const [target, copiedTarget] = targetAndCopied;\n  let handlerAndState = (\n    proxyCache && (proxyCache as ProxyCache<typeof target>).get(target)\n  );\n  if (\n    !handlerAndState\n    || handlerAndState[1][IS_TARGET_COPIED_PROPERTY] !== !!copiedTarget\n  ) {\n    handlerAndState = createProxyHandler<typeof target>(target, !!copiedTarget);\n    handlerAndState[1][PROXY_PROPERTY] = newProxy(\n      copiedTarget || target,\n      handlerAndState[0],\n    );\n    if (proxyCache) {\n      proxyCache.set(target, handlerAndState);\n    }\n  }\n  handlerAndState[1][AFFECTED_PROPERTY] = affected as Affected;\n  handlerAndState[1][PROXY_CACHE_PROPERTY] = proxyCache as ProxyCache<object> | undefined;\n  handlerAndState[1][TARGET_CACHE_PROPERTY] = targetCache as TargetCache<object> | undefined;\n  return handlerAndState[1][PROXY_PROPERTY] as typeof target;\n};\n\nconst isAllOwnKeysChanged = (prevObj: object, nextObj: object) => {\n  const prevKeys = Reflect.ownKeys(prevObj);\n  const nextKeys = Reflect.ownKeys(nextObj);\n  return prevKeys.length !== nextKeys.length\n    || prevKeys.some((k, i) => k !== nextKeys[i]);\n};\n\ntype ChangedCache = WeakMap<object, {\n  [NEXT_OBJECT_PROPERTY]: object;\n  [CHANGED_PROPERTY]: boolean;\n}>;\n\n/**\n * Compare changes on objects.\n *\n * This will compare the affected properties on tracked objects inside the proxy\n * to check if there were any changes made to it,\n * by default if no property was accessed on the proxy it will attempt to do a\n * reference equality check for the objects provided (Object.is(a, b)). If you access a property\n * on the proxy, then isChanged will only compare the affected properties.\n *\n * @param {object} prevObj - The previous object to compare.\n * @param {object} nextObj - Object to compare with the previous one.\n * @param {WeakMap<object, unknown>} affected -\n * WeakMap that holds the tracking of which properties in the proxied object were accessed.\n * @param {WeakMap<object, unknown>} [cache] -\n * WeakMap that holds a cache of the comparisons for better performance with repetitive comparisons,\n * and to avoid infinite loop with circular structures.\n * @returns {boolean} - Boolean indicating if the affected property on the object has changed.\n *\n * @example\n * import { createProxy, isChanged } from 'proxy-compare';\n *\n * const obj = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(obj, affected);\n *\n * proxy.a\n *\n * isChanged(obj, { a: \"1\" }, affected) // false\n *\n * proxy.a = \"2\"\n *\n * isChanged(obj, { a: \"1\" }, affected) // true\n */\n\nexport const isChanged = (\n  prevObj: unknown,\n  nextObj: unknown,\n  affected: WeakMap<object, unknown>,\n  cache?: WeakMap<object, unknown>,\n  isEqual: (a: unknown, b: unknown) => boolean = Object.is,\n): boolean => {\n  if (isEqual(prevObj, nextObj)) {\n    return false;\n  }\n  if (!isObject(prevObj) || !isObject(nextObj)) return true;\n  const used = (affected as Affected).get(getOriginalObject(prevObj));\n  if (!used) return true;\n  if (cache) {\n    const hit = (cache as ChangedCache).get(prevObj);\n    if (hit && hit[NEXT_OBJECT_PROPERTY] === nextObj) {\n      return hit[CHANGED_PROPERTY];\n    }\n    // for object with cycles\n    (cache as ChangedCache).set(prevObj, {\n      [NEXT_OBJECT_PROPERTY]: nextObj,\n      [CHANGED_PROPERTY]: false,\n    });\n  }\n  let changed: boolean | null = null;\n  try {\n    for (const key of used[HAS_KEY_PROPERTY] || []) {\n      changed = Reflect.has(prevObj, key) !== Reflect.has(nextObj, key);\n      if (changed) return changed;\n    }\n    if (used[ALL_OWN_KEYS_PROPERTY] === true) {\n      changed = isAllOwnKeysChanged(prevObj, nextObj);\n      if (changed) return changed;\n    } else {\n      for (const key of used[HAS_OWN_KEY_PROPERTY] || []) {\n        const hasPrev = !!Reflect.getOwnPropertyDescriptor(prevObj, key);\n        const hasNext = !!Reflect.getOwnPropertyDescriptor(nextObj, key);\n        changed = hasPrev !== hasNext;\n        if (changed) return changed;\n      }\n    }\n    for (const key of used[KEYS_PROPERTY] || []) {\n      changed = isChanged(\n        (prevObj as any)[key],\n        (nextObj as any)[key],\n        affected,\n        cache,\n        isEqual,\n      );\n      if (changed) return changed;\n    }\n    if (changed === null) changed = true;\n    return changed;\n  } finally {\n    if (cache) {\n      cache.set(prevObj, {\n        [NEXT_OBJECT_PROPERTY]: nextObj,\n        [CHANGED_PROPERTY]: changed,\n      });\n    }\n  }\n};\n\n// explicitly track object with memo\nexport const trackMemo = (obj: unknown) => {\n  if (isObjectToTrack(obj)) {\n    return TRACK_MEMO_SYMBOL in obj;\n  }\n  return false;\n};\n\n/**\n * Unwrap proxy to get the original object.\n *\n * Used to retrieve the original object used to create the proxy instance with `createProxy`.\n *\n * @param {Proxy<object>} obj -  The proxy wrapper of the originial object.\n * @returns {object | null} - Return either the unwrapped object if exists.\n *\n * @example\n * import { createProxy, getUntracked } from 'proxy-compare';\n *\n * const original = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(original, affected);\n * const originalFromProxy = getUntracked(proxy)\n *\n * Object.is(original, originalFromProxy) // true\n * isChanged(original, originalFromProxy, affected) // false\n */\nexport const getUntracked = <T>(obj: T): T | null => {\n  if (isObjectToTrack(obj)) {\n    return (obj as { [GET_ORIGINAL_SYMBOL]?: T })[GET_ORIGINAL_SYMBOL] || null;\n  }\n  return null;\n};\n\n/**\n * Mark object to be tracked.\n *\n * This function marks an object that will be passed into `createProxy`\n * as marked to track or not. By default only Array and Object are marked to track,\n * so this is useful for example to mark a class instance to track or to mark a object\n * to be untracked when creating your proxy.\n *\n * @param obj - Object to mark as tracked or not.\n * @param mark - Boolean indicating whether you want to track this object or not.\n * @returns - No return.\n *\n * @example\n * import { createProxy, markToTrack, isChanged } from 'proxy-compare';\n *\n * const nested = { e: \"3\" }\n *\n * markToTrack(nested, false)\n *\n * const original = { a: \"1\", c: \"2\", d: nested };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(original, affected);\n *\n * proxy.d.e\n *\n * isChanged(original, { d: { e: \"3\" } }, affected) // true\n */\nexport const markToTrack = (obj: object, mark = true) => {\n  objectsToTrack.set(obj, mark);\n};\n\n/**\n * Convert `affected` to path list\n *\n * `affected` is a weak map which is not printable.\n * This function is can convert it to printable path list.\n * It's for debugging purpose.\n *\n * @param obj - An object that is used with `createProxy`.\n * @param affected - A weak map that is used with `createProxy`.\n * @param onlyWithValues - An optional boolean to exclude object getters.\n * @returns - An array of paths.\n */\nexport const affectedToPathList = (\n  obj: unknown,\n  affected: WeakMap<object, unknown>,\n  onlyWithValues?: boolean,\n) => {\n  const list: (string | symbol)[][] = [];\n  const seen = new WeakSet();\n  const walk = (x: unknown, path?: (string | symbol)[]) => {\n    if (seen.has(x as object)) {\n      // for object with cycles\n      return;\n    }\n    if (isObject(x)) {\n      seen.add(x);\n    }\n    const used = isObject(x) && (affected as Affected).get(getOriginalObject(x));\n    if (used) {\n      used[HAS_KEY_PROPERTY]?.forEach((key) => {\n        const segment = `:has(${String(key)})`;\n        list.push(path ? [...path, segment] : [segment]);\n      });\n      if (used[ALL_OWN_KEYS_PROPERTY] === true) {\n        const segment = ':ownKeys';\n        list.push(path ? [...path, segment] : [segment]);\n      } else {\n        used[HAS_OWN_KEY_PROPERTY]?.forEach((key) => {\n          const segment = `:hasOwn(${String(key)})`;\n          list.push(path ? [...path, segment] : [segment]);\n        });\n      }\n      used[KEYS_PROPERTY]?.forEach((key) => {\n        if (!onlyWithValues || 'value' in (Object.getOwnPropertyDescriptor(x, key) || {})) {\n          walk((x as any)[key], path ? [...path, key] : [key]);\n        }\n      });\n    } else if (path) {\n      list.push(path);\n    }\n  };\n  walk(obj);\n  return list;\n};\n\n/**\n * replace newProxy function.\n *\n * This can be used if you want to use proxy-polyfill.\n * Note that proxy-polyfill can't polyfill everything.\n * Use it at your own risk.\n */\nexport const replaceNewProxy = (fn: typeof newProxy) => {\n  newProxy = fn;\n};\n", "import { markToTrack, getUntracked } from 'proxy-compare';\n\nconst isObject = (x) => typeof x === \"object\" && x !== null;\nconst proxyStateMap = /* @__PURE__ */ new WeakMap();\nconst refSet = /* @__PURE__ */ new WeakSet();\nconst buildProxyFunction = (objectIs = Object.is, newProxy = (target, handler) => new Proxy(target, handler), canProxy = (x) => isObject(x) && !refSet.has(x) && (Array.isArray(x) || !(Symbol.iterator in x)) && !(x instanceof WeakMap) && !(x instanceof WeakSet) && !(x instanceof Error) && !(x instanceof Number) && !(x instanceof Date) && !(x instanceof String) && !(x instanceof RegExp) && !(x instanceof ArrayBuffer), defaultHandlePromise = (promise) => {\n  switch (promise.status) {\n    case \"fulfilled\":\n      return promise.value;\n    case \"rejected\":\n      throw promise.reason;\n    default:\n      throw promise;\n  }\n}, snapCache = /* @__PURE__ */ new WeakMap(), createSnapshot = (target, version, handlePromise = defaultHandlePromise) => {\n  const cache = snapCache.get(target);\n  if ((cache == null ? void 0 : cache[0]) === version) {\n    return cache[1];\n  }\n  const snap = Array.isArray(target) ? [] : Object.create(Object.getPrototypeOf(target));\n  markToTrack(snap, true);\n  snapCache.set(target, [version, snap]);\n  Reflect.ownKeys(target).forEach((key) => {\n    if (Object.getOwnPropertyDescriptor(snap, key)) {\n      return;\n    }\n    const value = Reflect.get(target, key);\n    const { enumerable } = Reflect.getOwnPropertyDescriptor(\n      target,\n      key\n    );\n    const desc = {\n      value,\n      enumerable,\n      // This is intentional to avoid copying with proxy-compare.\n      // It's still non-writable, so it avoids assigning a value.\n      configurable: true\n    };\n    if (refSet.has(value)) {\n      markToTrack(value, false);\n    } else if (value instanceof Promise) {\n      delete desc.value;\n      desc.get = () => handlePromise(value);\n    } else if (proxyStateMap.has(value)) {\n      const [target2, ensureVersion] = proxyStateMap.get(\n        value\n      );\n      desc.value = createSnapshot(\n        target2,\n        ensureVersion(),\n        handlePromise\n      );\n    }\n    Object.defineProperty(snap, key, desc);\n  });\n  return Object.preventExtensions(snap);\n}, proxyCache = /* @__PURE__ */ new WeakMap(), versionHolder = [1, 1], proxyFunction = (initialObject) => {\n  if (!isObject(initialObject)) {\n    throw new Error(\"object required\");\n  }\n  const found = proxyCache.get(initialObject);\n  if (found) {\n    return found;\n  }\n  let version = versionHolder[0];\n  const listeners = /* @__PURE__ */ new Set();\n  const notifyUpdate = (op, nextVersion = ++versionHolder[0]) => {\n    if (version !== nextVersion) {\n      version = nextVersion;\n      listeners.forEach((listener) => listener(op, nextVersion));\n    }\n  };\n  let checkVersion = versionHolder[1];\n  const ensureVersion = (nextCheckVersion = ++versionHolder[1]) => {\n    if (checkVersion !== nextCheckVersion && !listeners.size) {\n      checkVersion = nextCheckVersion;\n      propProxyStates.forEach(([propProxyState]) => {\n        const propVersion = propProxyState[1](nextCheckVersion);\n        if (propVersion > version) {\n          version = propVersion;\n        }\n      });\n    }\n    return version;\n  };\n  const createPropListener = (prop) => (op, nextVersion) => {\n    const newOp = [...op];\n    newOp[1] = [prop, ...newOp[1]];\n    notifyUpdate(newOp, nextVersion);\n  };\n  const propProxyStates = /* @__PURE__ */ new Map();\n  const addPropListener = (prop, propProxyState) => {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && propProxyStates.has(prop)) {\n      throw new Error(\"prop listener already exists\");\n    }\n    if (listeners.size) {\n      const remove = propProxyState[3](createPropListener(prop));\n      propProxyStates.set(prop, [propProxyState, remove]);\n    } else {\n      propProxyStates.set(prop, [propProxyState]);\n    }\n  };\n  const removePropListener = (prop) => {\n    var _a;\n    const entry = propProxyStates.get(prop);\n    if (entry) {\n      propProxyStates.delete(prop);\n      (_a = entry[1]) == null ? void 0 : _a.call(entry);\n    }\n  };\n  const addListener = (listener) => {\n    listeners.add(listener);\n    if (listeners.size === 1) {\n      propProxyStates.forEach(([propProxyState, prevRemove], prop) => {\n        if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && prevRemove) {\n          throw new Error(\"remove already exists\");\n        }\n        const remove = propProxyState[3](createPropListener(prop));\n        propProxyStates.set(prop, [propProxyState, remove]);\n      });\n    }\n    const removeListener = () => {\n      listeners.delete(listener);\n      if (listeners.size === 0) {\n        propProxyStates.forEach(([propProxyState, remove], prop) => {\n          if (remove) {\n            remove();\n            propProxyStates.set(prop, [propProxyState]);\n          }\n        });\n      }\n    };\n    return removeListener;\n  };\n  const baseObject = Array.isArray(initialObject) ? [] : Object.create(Object.getPrototypeOf(initialObject));\n  const handler = {\n    deleteProperty(target, prop) {\n      const prevValue = Reflect.get(target, prop);\n      removePropListener(prop);\n      const deleted = Reflect.deleteProperty(target, prop);\n      if (deleted) {\n        notifyUpdate([\"delete\", [prop], prevValue]);\n      }\n      return deleted;\n    },\n    set(target, prop, value, receiver) {\n      const hasPrevValue = Reflect.has(target, prop);\n      const prevValue = Reflect.get(target, prop, receiver);\n      if (hasPrevValue && (objectIs(prevValue, value) || proxyCache.has(value) && objectIs(prevValue, proxyCache.get(value)))) {\n        return true;\n      }\n      removePropListener(prop);\n      if (isObject(value)) {\n        value = getUntracked(value) || value;\n      }\n      let nextValue = value;\n      if (value instanceof Promise) {\n        value.then((v) => {\n          value.status = \"fulfilled\";\n          value.value = v;\n          notifyUpdate([\"resolve\", [prop], v]);\n        }).catch((e) => {\n          value.status = \"rejected\";\n          value.reason = e;\n          notifyUpdate([\"reject\", [prop], e]);\n        });\n      } else {\n        if (!proxyStateMap.has(value) && canProxy(value)) {\n          nextValue = proxyFunction(value);\n        }\n        const childProxyState = !refSet.has(nextValue) && proxyStateMap.get(nextValue);\n        if (childProxyState) {\n          addPropListener(prop, childProxyState);\n        }\n      }\n      Reflect.set(target, prop, nextValue, receiver);\n      notifyUpdate([\"set\", [prop], value, prevValue]);\n      return true;\n    }\n  };\n  const proxyObject = newProxy(baseObject, handler);\n  proxyCache.set(initialObject, proxyObject);\n  const proxyState = [\n    baseObject,\n    ensureVersion,\n    createSnapshot,\n    addListener\n  ];\n  proxyStateMap.set(proxyObject, proxyState);\n  Reflect.ownKeys(initialObject).forEach((key) => {\n    const desc = Object.getOwnPropertyDescriptor(\n      initialObject,\n      key\n    );\n    if (\"value\" in desc) {\n      proxyObject[key] = initialObject[key];\n      delete desc.value;\n      delete desc.writable;\n    }\n    Object.defineProperty(baseObject, key, desc);\n  });\n  return proxyObject;\n}) => [\n  // public functions\n  proxyFunction,\n  // shared state\n  proxyStateMap,\n  refSet,\n  // internal things\n  objectIs,\n  newProxy,\n  canProxy,\n  defaultHandlePromise,\n  snapCache,\n  createSnapshot,\n  proxyCache,\n  versionHolder\n];\nconst [defaultProxyFunction] = buildProxyFunction();\nfunction proxy(initialObject = {}) {\n  return defaultProxyFunction(initialObject);\n}\nfunction getVersion(proxyObject) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  return proxyState == null ? void 0 : proxyState[1]();\n}\nfunction subscribe(proxyObject, callback, notifyInSync) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && !proxyState) {\n    console.warn(\"Please use proxy object\");\n  }\n  let promise;\n  const ops = [];\n  const addListener = proxyState[3];\n  let isListenerActive = false;\n  const listener = (op) => {\n    ops.push(op);\n    if (notifyInSync) {\n      callback(ops.splice(0));\n      return;\n    }\n    if (!promise) {\n      promise = Promise.resolve().then(() => {\n        promise = void 0;\n        if (isListenerActive) {\n          callback(ops.splice(0));\n        }\n      });\n    }\n  };\n  const removeListener = addListener(listener);\n  isListenerActive = true;\n  return () => {\n    isListenerActive = false;\n    removeListener();\n  };\n}\nfunction snapshot(proxyObject, handlePromise) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && !proxyState) {\n    console.warn(\"Please use proxy object\");\n  }\n  const [target, ensureVersion, createSnapshot] = proxyState;\n  return createSnapshot(target, ensureVersion(), handlePromise);\n}\nfunction ref(obj) {\n  refSet.add(obj);\n  return obj;\n}\nconst unstable_buildProxyFunction = buildProxyFunction;\n\nexport { getVersion, proxy, ref, snapshot, subscribe, unstable_buildProxyFunction };\n", "import { subscribe, snapshot, proxy, ref, unstable_buildProxyFunction } from 'valtio/vanilla';\nimport { derive } from 'derive-valtio';\nexport { derive, underive, unstable_deriveSubscriptions } from 'derive-valtio';\n\nfunction subscribeKey(proxyObject, key, callback, notifyInSync) {\n  let prevValue = proxyObject[key];\n  return subscribe(\n    proxyObject,\n    () => {\n      const nextValue = proxyObject[key];\n      if (!Object.is(prevValue, nextValue)) {\n        callback(prevValue = nextValue);\n      }\n    },\n    notifyInSync\n  );\n}\n\nlet currentCleanups;\nfunction watch(callback, options) {\n  let alive = true;\n  const cleanups = /* @__PURE__ */ new Set();\n  const subscriptions = /* @__PURE__ */ new Map();\n  const cleanup = () => {\n    if (alive) {\n      alive = false;\n      cleanups.forEach((clean) => clean());\n      cleanups.clear();\n      subscriptions.forEach((unsubscribe) => unsubscribe());\n      subscriptions.clear();\n    }\n  };\n  const revalidate = async () => {\n    if (!alive) {\n      return;\n    }\n    cleanups.forEach((clean) => clean());\n    cleanups.clear();\n    const proxiesToSubscribe = /* @__PURE__ */ new Set();\n    const parent = currentCleanups;\n    currentCleanups = cleanups;\n    try {\n      const promiseOrPossibleCleanup = callback((proxyObject) => {\n        proxiesToSubscribe.add(proxyObject);\n        if (alive && !subscriptions.has(proxyObject)) {\n          const unsubscribe = subscribe(proxyObject, revalidate, options == null ? void 0 : options.sync);\n          subscriptions.set(proxyObject, unsubscribe);\n        }\n        return proxyObject;\n      });\n      const couldBeCleanup = promiseOrPossibleCleanup && promiseOrPossibleCleanup instanceof Promise ? await promiseOrPossibleCleanup : promiseOrPossibleCleanup;\n      if (couldBeCleanup) {\n        if (alive) {\n          cleanups.add(couldBeCleanup);\n        } else {\n          cleanup();\n        }\n      }\n    } finally {\n      currentCleanups = parent;\n    }\n    subscriptions.forEach((unsubscribe, proxyObject) => {\n      if (!proxiesToSubscribe.has(proxyObject)) {\n        subscriptions.delete(proxyObject);\n        unsubscribe();\n      }\n    });\n  };\n  if (currentCleanups) {\n    currentCleanups.add(cleanup);\n  }\n  revalidate();\n  return cleanup;\n}\n\nconst DEVTOOLS = Symbol();\nfunction devtools(proxyObject, options) {\n  if (typeof options === \"string\") {\n    console.warn(\n      \"string name option is deprecated, use { name }. https://github.com/pmndrs/valtio/pull/400\"\n    );\n    options = { name: options };\n  }\n  const { enabled, name = \"\", ...rest } = options || {};\n  let extension;\n  try {\n    extension = (enabled != null ? enabled : (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extension) {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && enabled) {\n      console.warn(\"[Warning] Please install/enable Redux devtools extension\");\n    }\n    return;\n  }\n  let isTimeTraveling = false;\n  const devtools2 = extension.connect({ name, ...rest });\n  const unsub1 = subscribe(proxyObject, (ops) => {\n    const action = ops.filter(([_, path]) => path[0] !== DEVTOOLS).map(([op, path]) => `${op}:${path.map(String).join(\".\")}`).join(\", \");\n    if (!action) {\n      return;\n    }\n    if (isTimeTraveling) {\n      isTimeTraveling = false;\n    } else {\n      const snapWithoutDevtools = Object.assign({}, snapshot(proxyObject));\n      delete snapWithoutDevtools[DEVTOOLS];\n      devtools2.send(\n        {\n          type: action,\n          updatedAt: (/* @__PURE__ */ new Date()).toLocaleString()\n        },\n        snapWithoutDevtools\n      );\n    }\n  });\n  const unsub2 = devtools2.subscribe((message) => {\n    var _a, _b, _c, _d, _e, _f;\n    if (message.type === \"ACTION\" && message.payload) {\n      try {\n        Object.assign(proxyObject, JSON.parse(message.payload));\n      } catch (e) {\n        console.error(\n          \"please dispatch a serializable value that JSON.parse() and proxy() support\\n\",\n          e\n        );\n      }\n    }\n    if (message.type === \"DISPATCH\" && message.state) {\n      if (((_a = message.payload) == null ? void 0 : _a.type) === \"JUMP_TO_ACTION\" || ((_b = message.payload) == null ? void 0 : _b.type) === \"JUMP_TO_STATE\") {\n        isTimeTraveling = true;\n        const state = JSON.parse(message.state);\n        Object.assign(proxyObject, state);\n      }\n      proxyObject[DEVTOOLS] = message;\n    } else if (message.type === \"DISPATCH\" && ((_c = message.payload) == null ? void 0 : _c.type) === \"COMMIT\") {\n      devtools2.init(snapshot(proxyObject));\n    } else if (message.type === \"DISPATCH\" && ((_d = message.payload) == null ? void 0 : _d.type) === \"IMPORT_STATE\") {\n      const actions = (_e = message.payload.nextLiftedState) == null ? void 0 : _e.actionsById;\n      const computedStates = ((_f = message.payload.nextLiftedState) == null ? void 0 : _f.computedStates) || [];\n      isTimeTraveling = true;\n      computedStates.forEach(({ state }, index) => {\n        const action = actions[index] || \"No action found\";\n        Object.assign(proxyObject, state);\n        if (index === 0) {\n          devtools2.init(snapshot(proxyObject));\n        } else {\n          devtools2.send(action, snapshot(proxyObject));\n        }\n      });\n    }\n  });\n  devtools2.init(snapshot(proxyObject));\n  return () => {\n    unsub1();\n    unsub2 == null ? void 0 : unsub2();\n  };\n}\n\nfunction addComputed_DEPRECATED(proxyObject, computedFns_FAKE, targetObject = proxyObject) {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"addComputed is deprecated. Please consider using `derive`. Falling back to emulation with derive. https://github.com/pmndrs/valtio/pull/201\"\n    );\n  }\n  const derivedFns = {};\n  Object.keys(computedFns_FAKE).forEach((key) => {\n    derivedFns[key] = (get) => computedFns_FAKE[key](get(proxyObject));\n  });\n  return derive(derivedFns, { proxy: targetObject });\n}\n\nfunction proxyWithComputed_DEPRECATED(initialObject, computedFns) {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      'proxyWithComputed is deprecated. Please follow \"Computed Properties\" guide in docs.'\n    );\n  }\n  Object.keys(computedFns).forEach((key) => {\n    if (Object.getOwnPropertyDescriptor(initialObject, key)) {\n      throw new Error(\"object property already defined\");\n    }\n    const computedFn = computedFns[key];\n    const { get, set } = typeof computedFn === \"function\" ? { get: computedFn } : computedFn;\n    const desc = {};\n    desc.get = () => get(snapshot(proxyObject));\n    if (set) {\n      desc.set = (newValue) => set(proxyObject, newValue);\n    }\n    Object.defineProperty(initialObject, key, desc);\n  });\n  const proxyObject = proxy(initialObject);\n  return proxyObject;\n}\n\nconst isObject = (x) => typeof x === \"object\" && x !== null;\nlet refSet;\nconst deepClone = (obj) => {\n  if (!refSet) {\n    refSet = unstable_buildProxyFunction()[2];\n  }\n  if (!isObject(obj) || refSet.has(obj)) {\n    return obj;\n  }\n  const baseObject = Array.isArray(obj) ? [] : Object.create(Object.getPrototypeOf(obj));\n  Reflect.ownKeys(obj).forEach((key) => {\n    baseObject[key] = deepClone(obj[key]);\n  });\n  return baseObject;\n};\nfunction proxyWithHistory_DEPRECATED(initialValue, skipSubscribe = false) {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      'proxyWithHistory is deprecated. Please use the \"valtio-history\" package; refer to the docs'\n    );\n  }\n  const proxyObject = proxy({\n    value: initialValue,\n    history: ref({\n      wip: void 0,\n      // to avoid infinite loop\n      snapshots: [],\n      index: -1\n    }),\n    clone: deepClone,\n    canUndo: () => proxyObject.history.index > 0,\n    undo: () => {\n      if (proxyObject.canUndo()) {\n        proxyObject.value = proxyObject.history.wip = proxyObject.clone(\n          proxyObject.history.snapshots[--proxyObject.history.index]\n        );\n      }\n    },\n    canRedo: () => proxyObject.history.index < proxyObject.history.snapshots.length - 1,\n    redo: () => {\n      if (proxyObject.canRedo()) {\n        proxyObject.value = proxyObject.history.wip = proxyObject.clone(\n          proxyObject.history.snapshots[++proxyObject.history.index]\n        );\n      }\n    },\n    saveHistory: () => {\n      proxyObject.history.snapshots.splice(proxyObject.history.index + 1);\n      proxyObject.history.snapshots.push(snapshot(proxyObject).value);\n      ++proxyObject.history.index;\n    },\n    subscribe: () => subscribe(proxyObject, (ops) => {\n      if (ops.every(\n        (op) => op[1][0] === \"value\" && (op[0] !== \"set\" || op[2] !== proxyObject.history.wip)\n      )) {\n        proxyObject.saveHistory();\n      }\n    })\n  });\n  proxyObject.saveHistory();\n  if (!skipSubscribe) {\n    proxyObject.subscribe();\n  }\n  return proxyObject;\n}\n\nfunction proxySet(initialValues) {\n  const set = proxy({\n    data: Array.from(new Set(initialValues)),\n    has(value) {\n      return this.data.indexOf(value) !== -1;\n    },\n    add(value) {\n      let hasProxy = false;\n      if (typeof value === \"object\" && value !== null) {\n        hasProxy = this.data.indexOf(proxy(value)) !== -1;\n      }\n      if (this.data.indexOf(value) === -1 && !hasProxy) {\n        this.data.push(value);\n      }\n      return this;\n    },\n    delete(value) {\n      const index = this.data.indexOf(value);\n      if (index === -1) {\n        return false;\n      }\n      this.data.splice(index, 1);\n      return true;\n    },\n    clear() {\n      this.data.splice(0);\n    },\n    get size() {\n      return this.data.length;\n    },\n    forEach(cb) {\n      this.data.forEach((value) => {\n        cb(value, value, this);\n      });\n    },\n    get [Symbol.toStringTag]() {\n      return \"Set\";\n    },\n    toJSON() {\n      return new Set(this.data);\n    },\n    [Symbol.iterator]() {\n      return this.data[Symbol.iterator]();\n    },\n    values() {\n      return this.data.values();\n    },\n    keys() {\n      return this.data.values();\n    },\n    entries() {\n      return new Set(this.data).entries();\n    }\n  });\n  Object.defineProperties(set, {\n    data: {\n      enumerable: false\n    },\n    size: {\n      enumerable: false\n    },\n    toJSON: {\n      enumerable: false\n    }\n  });\n  Object.seal(set);\n  return set;\n}\n\nfunction proxyMap(entries) {\n  const map = proxy({\n    data: Array.from(entries || []),\n    has(key) {\n      return this.data.some((p) => p[0] === key);\n    },\n    set(key, value) {\n      const record = this.data.find((p) => p[0] === key);\n      if (record) {\n        record[1] = value;\n      } else {\n        this.data.push([key, value]);\n      }\n      return this;\n    },\n    get(key) {\n      var _a;\n      return (_a = this.data.find((p) => p[0] === key)) == null ? void 0 : _a[1];\n    },\n    delete(key) {\n      const index = this.data.findIndex((p) => p[0] === key);\n      if (index === -1) {\n        return false;\n      }\n      this.data.splice(index, 1);\n      return true;\n    },\n    clear() {\n      this.data.splice(0);\n    },\n    get size() {\n      return this.data.length;\n    },\n    toJSON() {\n      return new Map(this.data);\n    },\n    forEach(cb) {\n      this.data.forEach((p) => {\n        cb(p[1], p[0], this);\n      });\n    },\n    keys() {\n      return this.data.map((p) => p[0]).values();\n    },\n    values() {\n      return this.data.map((p) => p[1]).values();\n    },\n    entries() {\n      return new Map(this.data).entries();\n    },\n    get [Symbol.toStringTag]() {\n      return \"Map\";\n    },\n    [Symbol.iterator]() {\n      return this.entries();\n    }\n  });\n  Object.defineProperties(map, {\n    data: {\n      enumerable: false\n    },\n    size: {\n      enumerable: false\n    },\n    toJSON: {\n      enumerable: false\n    }\n  });\n  Object.seal(map);\n  return map;\n}\n\nexport { addComputed_DEPRECATED as addComputed, devtools, proxyMap, proxySet, proxyWithComputed_DEPRECATED as proxyWithComputed, proxyWithHistory_DEPRECATED as proxyWithHistory, subscribeKey, watch };\n", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "import type { ErrorType } from '../../errors/utils.js'\nimport type { SignedAuthorizationList } from '../../experimental/eip7702/types/authorization.js'\nimport type { RpcAuthorizationList } from '../../experimental/eip7702/types/rpc.js'\nimport type { BlockTag } from '../../types/block.js'\nimport type { Chain } from '../../types/chain.js'\nimport type {\n  ExtractChainFormatterExclude,\n  ExtractChainFormatterReturnType,\n} from '../../types/chain.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { RpcTransaction } from '../../types/rpc.js'\nimport type { Transaction, TransactionType } from '../../types/transaction.js'\nimport type { ExactPartial, UnionLooseOmit } from '../../types/utils.js'\nimport { hexToNumber } from '../encoding/fromHex.js'\nimport { type DefineFormatterErrorType, defineFormatter } from './formatter.js'\n\ntype TransactionPendingDependencies =\n  | 'blockHash'\n  | 'blockNumber'\n  | 'transactionIndex'\n\nexport type FormattedTransaction<\n  chain extends Chain | undefined = undefined,\n  blockTag extends BlockTag = BlockTag,\n  _FormatterReturnType = ExtractChainFormatterReturnType<\n    chain,\n    'transaction',\n    Transaction\n  >,\n  _ExcludedPendingDependencies extends string = TransactionPendingDependencies &\n    ExtractChainFormatterExclude<chain, 'transaction'>,\n> = UnionLooseOmit<_FormatterReturnType, TransactionPendingDependencies> & {\n  [_K in _ExcludedPendingDependencies]: never\n} & Pick<\n    Transaction<bigint, number, blockTag extends 'pending' ? true : false>,\n    TransactionPendingDependencies\n  >\n\nexport const transactionType = {\n  '0x0': 'legacy',\n  '0x1': 'eip2930',\n  '0x2': 'eip1559',\n  '0x3': 'eip4844',\n  '0x4': 'eip7702',\n} as const satisfies Record<Hex, TransactionType>\n\nexport type FormatTransactionErrorType = ErrorType\n\nexport function formatTransaction(transaction: ExactPartial<RpcTransaction>) {\n  const transaction_ = {\n    ...transaction,\n    blockHash: transaction.blockHash ? transaction.blockHash : null,\n    blockNumber: transaction.blockNumber\n      ? BigInt(transaction.blockNumber)\n      : null,\n    chainId: transaction.chainId ? hexToNumber(transaction.chainId) : undefined,\n    gas: transaction.gas ? BigInt(transaction.gas) : undefined,\n    gasPrice: transaction.gasPrice ? BigInt(transaction.gasPrice) : undefined,\n    maxFeePerBlobGas: transaction.maxFeePerBlobGas\n      ? BigInt(transaction.maxFeePerBlobGas)\n      : undefined,\n    maxFeePerGas: transaction.maxFeePerGas\n      ? BigInt(transaction.maxFeePerGas)\n      : undefined,\n    maxPriorityFeePerGas: transaction.maxPriorityFeePerGas\n      ? BigInt(transaction.maxPriorityFeePerGas)\n      : undefined,\n    nonce: transaction.nonce ? hexToNumber(transaction.nonce) : undefined,\n    to: transaction.to ? transaction.to : null,\n    transactionIndex: transaction.transactionIndex\n      ? Number(transaction.transactionIndex)\n      : null,\n    type: transaction.type\n      ? (transactionType as any)[transaction.type]\n      : undefined,\n    typeHex: transaction.type ? transaction.type : undefined,\n    value: transaction.value ? BigInt(transaction.value) : undefined,\n    v: transaction.v ? BigInt(transaction.v) : undefined,\n  } as Transaction\n\n  if (transaction.authorizationList)\n    transaction_.authorizationList = formatAuthorizationList(\n      transaction.authorizationList,\n    )\n\n  transaction_.yParity = (() => {\n    // If `yParity` is provided, we will use it.\n    if (transaction.yParity) return Number(transaction.yParity)\n\n    // If no `yParity` provided, try derive from `v`.\n    if (typeof transaction_.v === 'bigint') {\n      if (transaction_.v === 0n || transaction_.v === 27n) return 0\n      if (transaction_.v === 1n || transaction_.v === 28n) return 1\n      if (transaction_.v >= 35n) return transaction_.v % 2n === 0n ? 1 : 0\n    }\n\n    return undefined\n  })()\n\n  if (transaction_.type === 'legacy') {\n    delete transaction_.accessList\n    delete transaction_.maxFeePerBlobGas\n    delete transaction_.maxFeePerGas\n    delete transaction_.maxPriorityFeePerGas\n    delete transaction_.yParity\n  }\n  if (transaction_.type === 'eip2930') {\n    delete transaction_.maxFeePerBlobGas\n    delete transaction_.maxFeePerGas\n    delete transaction_.maxPriorityFeePerGas\n  }\n  if (transaction_.type === 'eip1559') {\n    delete transaction_.maxFeePerBlobGas\n  }\n  return transaction_\n}\n\nexport type DefineTransactionErrorType = DefineFormatterErrorType | ErrorType\n\nexport const defineTransaction = /*#__PURE__*/ defineFormatter(\n  'transaction',\n  formatTransaction,\n)\n\n//////////////////////////////////////////////////////////////////////////////\n\nfunction formatAuthorizationList(\n  authorizationList: RpcAuthorizationList,\n): SignedAuthorizationList {\n  return authorizationList.map((authorization) => ({\n    contractAddress: (authorization as any).address,\n    chainId: Number(authorization.chainId),\n    nonce: Number(authorization.nonce),\n    r: authorization.r,\n    s: authorization.s,\n    yParity: Number(authorization.yParity),\n  })) as SignedAuthorizationList\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Block, BlockTag } from '../../types/block.js'\nimport type { Chain } from '../../types/chain.js'\nimport type {\n  ExtractChainFormatterExclude,\n  ExtractChainFormatterReturnType,\n} from '../../types/chain.js'\nimport type { Hash } from '../../types/misc.js'\nimport type { RpcBlock } from '../../types/rpc.js'\nimport type { ExactPartial, Prettify } from '../../types/utils.js'\n\nimport { type DefineFormatterErrorType, defineFormatter } from './formatter.js'\nimport { type FormattedTransaction, formatTransaction } from './transaction.js'\n\ntype BlockPendingDependencies = 'hash' | 'logsBloom' | 'nonce' | 'number'\n\nexport type FormattedBlock<\n  chain extends Chain | undefined = undefined,\n  includeTransactions extends boolean = boolean,\n  blockTag extends BlockTag = BlockTag,\n  _FormatterReturnType = ExtractChainFormatterReturnType<\n    chain,\n    'block',\n    Block<bigint, includeTransactions>\n  >,\n  _ExcludedPendingDependencies extends string = BlockPendingDependencies &\n    ExtractChainFormatterExclude<chain, 'block'>,\n  _Formatted = Omit<_FormatterReturnType, BlockPendingDependencies> & {\n    [_key in _ExcludedPendingDependencies]: never\n  } & Pick<\n      Block<bigint, includeTransactions, blockTag>,\n      BlockPendingDependencies\n    >,\n  _Transactions = includeTransactions extends true\n    ? Prettify<FormattedTransaction<chain, blockTag>>[]\n    : Hash[],\n> = Omit<_Formatted, 'transactions'> & {\n  transactions: _Transactions\n}\n\nexport type FormatBlockErrorType = ErrorType\n\nexport function formatBlock(block: ExactPartial<RpcBlock>) {\n  const transactions = (block.transactions ?? []).map((transaction) => {\n    if (typeof transaction === 'string') return transaction\n    return formatTransaction(transaction)\n  })\n  return {\n    ...block,\n    baseFeePerGas: block.baseFeePerGas ? BigInt(block.baseFeePerGas) : null,\n    blobGasUsed: block.blobGasUsed ? BigInt(block.blobGasUsed) : undefined,\n    difficulty: block.difficulty ? BigInt(block.difficulty) : undefined,\n    excessBlobGas: block.excessBlobGas\n      ? BigInt(block.excessBlobGas)\n      : undefined,\n    gasLimit: block.gasLimit ? BigInt(block.gasLimit) : undefined,\n    gasUsed: block.gasUsed ? BigInt(block.gasUsed) : undefined,\n    hash: block.hash ? block.hash : null,\n    logsBloom: block.logsBloom ? block.logsBloom : null,\n    nonce: block.nonce ? block.nonce : null,\n    number: block.number ? BigInt(block.number) : null,\n    size: block.size ? BigInt(block.size) : undefined,\n    timestamp: block.timestamp ? BigInt(block.timestamp) : undefined,\n    transactions,\n    totalDifficulty: block.totalDifficulty\n      ? BigInt(block.totalDifficulty)\n      : null,\n  } as Block\n}\n\nexport type DefineBlockErrorType = DefineFormatterErrorType | ErrorType\n\nexport const defineBlock = /*#__PURE__*/ defineFormatter('block', formatBlock)\n", "import type { Address } from 'abitype'\n\nimport type { Account } from '../../accounts/types.js'\nimport type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { BlockTag } from '../../types/block.js'\nimport type { Chain } from '../../types/chain.js'\nimport type { RequestErrorType } from '../../utils/buildRequest.js'\nimport {\n  type HexToNumberErrorType,\n  hexToNumber,\n} from '../../utils/encoding/fromHex.js'\nimport {\n  type NumberToHexErrorType,\n  numberToHex,\n} from '../../utils/encoding/toHex.js'\n\nexport type GetTransactionCountParameters = {\n  /** The account address. */\n  address: Address\n} & (\n  | {\n      /** The block number. */\n      blockNumber?: bigint | undefined\n      blockTag?: undefined\n    }\n  | {\n      blockNumber?: undefined\n      /** The block tag. Defaults to 'latest'. */\n      blockTag?: BlockTag | undefined\n    }\n)\nexport type GetTransactionCountReturnType = number\n\nexport type GetTransactionCountErrorType =\n  | RequestErrorType\n  | NumberToHexErrorType\n  | HexToNumberErrorType\n  | ErrorType\n\n/**\n * Returns the number of [Transactions](https://viem.sh/docs/glossary/terms#transaction) an Account has sent.\n *\n * - Docs: https://viem.sh/docs/actions/public/getTransactionCount\n * - JSON-RPC Methods: [`eth_getTransactionCount`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_gettransactioncount)\n *\n * @param client - Client to use\n * @param parameters - {@link GetTransactionCountParameters}\n * @returns The number of transactions an account has sent. {@link GetTransactionCountReturnType}\n *\n * @example\n * import { createPublicClient, http } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { getTransactionCount } from 'viem/public'\n *\n * const client = createPublicClient({\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const transactionCount = await getTransactionCount(client, {\n *   address: '******************************************',\n * })\n */\nexport async function getTransactionCount<\n  chain extends Chain | undefined,\n  account extends Account | undefined,\n>(\n  client: Client<Transport, chain, account>,\n  { address, blockTag = 'latest', blockNumber }: GetTransactionCountParameters,\n): Promise<GetTransactionCountReturnType> {\n  const count = await client.request(\n    {\n      method: 'eth_getTransactionCount',\n      params: [address, blockNumber ? numberToHex(blockNumber) : blockTag],\n    },\n    { dedupe: Boolean(blockNumber) },\n  )\n  return hexToNumber(count)\n}\n", "// https://github.com/ethereum/EIPs/blob/master/EIPS/eip-4844.md#parameters\n\n/** Blob limit per transaction. */\nconst blobsPerTransaction = 6\n\n/** The number of bytes in a BLS scalar field element. */\nexport const bytesPerFieldElement = 32\n\n/** The number of field elements in a blob. */\nexport const fieldElementsPerBlob = 4096\n\n/** The number of bytes in a blob. */\nexport const bytesPerBlob = bytesPerFieldElement * fieldElementsPerBlob\n\n/** Blob bytes limit per transaction. */\nexport const maxBytesPerTransaction =\n  bytesPerBlob * blobsPerTransaction -\n  // terminator byte (0x80).\n  1 -\n  // zero byte (0x00) appended to each field element.\n  1 * fieldElementsPerBlob * blobsPerTransaction\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Log } from '../../types/log.js'\nimport type { RpcLog } from '../../types/rpc.js'\nimport type { ExactPartial } from '../../types/utils.js'\n\nexport type FormatLogErrorType = ErrorType\n\nexport function formatLog(\n  log: ExactPartial<RpcLog>,\n  {\n    args,\n    eventName,\n  }: { args?: unknown | undefined; eventName?: string | undefined } = {},\n) {\n  return {\n    ...log,\n    blockHash: log.blockHash ? log.blockHash : null,\n    blockNumber: log.blockNumber ? BigInt(log.blockNumber) : null,\n    logIndex: log.logIndex ? Number(log.logIndex) : null,\n    transactionHash: log.transactionHash ? log.transactionHash : null,\n    transactionIndex: log.transactionIndex\n      ? Number(log.transactionIndex)\n      : null,\n    ...(eventName ? { args, eventName } : {}),\n  } as Log\n}\n", "import type { Address } from 'abitype'\n\nimport type { Account } from '../../accounts/types.js'\nimport {\n  type ParseAccountErrorType,\n  parseAccount,\n} from '../../accounts/utils/parseAccount.js'\nimport type { SignTransactionErrorType } from '../../accounts/utils/signTransaction.js'\nimport type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport {\n  AccountNotFoundError,\n  type AccountNotFoundErrorType,\n  AccountTypeNotSupportedError,\n  type AccountTypeNotSupportedErrorType,\n} from '../../errors/account.js'\nimport { BaseError } from '../../errors/base.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport {\n  type RecoverAuthorizationAddressErrorType,\n  recoverAuthorizationAddress,\n} from '../../experimental/eip7702/utils/recoverAuthorizationAddress.js'\nimport type { GetAccountParameter } from '../../types/account.js'\nimport type { Chain, DeriveChain } from '../../types/chain.js'\nimport type { GetChainParameter } from '../../types/chain.js'\nimport type { GetTransactionRequestKzgParameter } from '../../types/kzg.js'\nimport type { Hash } from '../../types/misc.js'\nimport type { TransactionRequest } from '../../types/transaction.js'\nimport type { UnionOmit } from '../../types/utils.js'\nimport type { RequestErrorType } from '../../utils/buildRequest.js'\nimport {\n  type AssertCurrentChainErrorType,\n  assertCurrentChain,\n} from '../../utils/chain/assertCurrentChain.js'\nimport {\n  type GetTransactionErrorReturnType,\n  getTransactionError,\n} from '../../utils/errors/getTransactionError.js'\nimport { extract } from '../../utils/formatters/extract.js'\nimport {\n  type FormattedTransactionRequest,\n  formatTransactionRequest,\n} from '../../utils/formatters/transactionRequest.js'\nimport { getAction } from '../../utils/getAction.js'\nimport { LruMap } from '../../utils/lru.js'\nimport {\n  type AssertRequestErrorType,\n  type AssertRequestParameters,\n  assertRequest,\n} from '../../utils/transaction/assertRequest.js'\nimport { type GetChainIdErrorType, getChainId } from '../public/getChainId.js'\nimport {\n  type PrepareTransactionRequestErrorType,\n  defaultParameters,\n  prepareTransactionRequest,\n} from './prepareTransactionRequest.js'\nimport {\n  type SendRawTransactionErrorType,\n  sendRawTransaction,\n} from './sendRawTransaction.js'\n\nconst supportsWalletNamespace = new LruMap<boolean>(128)\n\nexport type SendTransactionRequest<\n  chain extends Chain | undefined = Chain | undefined,\n  chainOverride extends Chain | undefined = Chain | undefined,\n  ///\n  _derivedChain extends Chain | undefined = DeriveChain<chain, chainOverride>,\n> = UnionOmit<FormattedTransactionRequest<_derivedChain>, 'from'> &\n  GetTransactionRequestKzgParameter\n\nexport type SendTransactionParameters<\n  chain extends Chain | undefined = Chain | undefined,\n  account extends Account | undefined = Account | undefined,\n  chainOverride extends Chain | undefined = Chain | undefined,\n  request extends SendTransactionRequest<\n    chain,\n    chainOverride\n  > = SendTransactionRequest<chain, chainOverride>,\n> = request &\n  GetAccountParameter<account, Account | Address, true, true> &\n  GetChainParameter<chain, chainOverride> &\n  GetTransactionRequestKzgParameter<request>\n\nexport type SendTransactionReturnType = Hash\n\nexport type SendTransactionErrorType =\n  | ParseAccountErrorType\n  | GetTransactionErrorReturnType<\n      | AccountNotFoundErrorType\n      | AccountTypeNotSupportedErrorType\n      | AssertCurrentChainErrorType\n      | AssertRequestErrorType\n      | GetChainIdErrorType\n      | PrepareTransactionRequestErrorType\n      | SendRawTransactionErrorType\n      | RecoverAuthorizationAddressErrorType\n      | SignTransactionErrorType\n      | RequestErrorType\n    >\n  | ErrorType\n\n/**\n * Creates, signs, and sends a new transaction to the network.\n *\n * - Docs: https://viem.sh/docs/actions/wallet/sendTransaction\n * - Examples: https://stackblitz.com/github/wevm/viem/tree/main/examples/transactions_sending-transactions\n * - JSON-RPC Methods:\n *   - JSON-RPC Accounts: [`eth_sendTransaction`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_sendtransaction)\n *   - Local Accounts: [`eth_sendRawTransaction`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_sendrawtransaction)\n *\n * @param client - Client to use\n * @param parameters - {@link SendTransactionParameters}\n * @returns The [Transaction](https://viem.sh/docs/glossary/terms#transaction) hash. {@link SendTransactionReturnType}\n *\n * @example\n * import { createWalletClient, custom } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { sendTransaction } from 'viem/wallet'\n *\n * const client = createWalletClient({\n *   chain: mainnet,\n *   transport: custom(window.ethereum),\n * })\n * const hash = await sendTransaction(client, {\n *   account: '******************************************',\n *   to: '******************************************',\n *   value: 1000000000000000000n,\n * })\n *\n * @example\n * // Account Hoisting\n * import { createWalletClient, http } from 'viem'\n * import { privateKeyToAccount } from 'viem/accounts'\n * import { mainnet } from 'viem/chains'\n * import { sendTransaction } from 'viem/wallet'\n *\n * const client = createWalletClient({\n *   account: privateKeyToAccount('0x…'),\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const hash = await sendTransaction(client, {\n *   to: '******************************************',\n *   value: 1000000000000000000n,\n * })\n */\nexport async function sendTransaction<\n  chain extends Chain | undefined,\n  account extends Account | undefined,\n  const request extends SendTransactionRequest<chain, chainOverride>,\n  chainOverride extends Chain | undefined = undefined,\n>(\n  client: Client<Transport, chain, account>,\n  parameters: SendTransactionParameters<chain, account, chainOverride, request>,\n): Promise<SendTransactionReturnType> {\n  const {\n    account: account_ = client.account,\n    chain = client.chain,\n    accessList,\n    authorizationList,\n    blobs,\n    data,\n    gas,\n    gasPrice,\n    maxFeePerBlobGas,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    nonce,\n    value,\n    ...rest\n  } = parameters\n\n  if (typeof account_ === 'undefined')\n    throw new AccountNotFoundError({\n      docsPath: '/docs/actions/wallet/sendTransaction',\n    })\n  const account = account_ ? parseAccount(account_) : null\n\n  try {\n    assertRequest(parameters as AssertRequestParameters)\n\n    const to = await (async () => {\n      // If `to` exists on the parameters, use that.\n      if (parameters.to) return parameters.to\n\n      // If no `to` exists, and we are sending a EIP-7702 transaction, use the\n      // address of the first authorization in the list.\n      if (authorizationList && authorizationList.length > 0)\n        return await recoverAuthorizationAddress({\n          authorization: authorizationList[0],\n        }).catch(() => {\n          throw new BaseError(\n            '`to` is required. Could not infer from `authorizationList`.',\n          )\n        })\n\n      // Otherwise, we are sending a deployment transaction.\n      return undefined\n    })()\n\n    if (account?.type === 'json-rpc' || account === null) {\n      let chainId: number | undefined\n      if (chain !== null) {\n        chainId = await getAction(client, getChainId, 'getChainId')({})\n        assertCurrentChain({\n          currentChainId: chainId,\n          chain,\n        })\n      }\n\n      const chainFormat = client.chain?.formatters?.transactionRequest?.format\n      const format = chainFormat || formatTransactionRequest\n\n      const request = format({\n        // Pick out extra data that might exist on the chain's transaction request type.\n        ...extract(rest, { format: chainFormat }),\n        accessList,\n        authorizationList,\n        blobs,\n        chainId,\n        data,\n        from: account?.address,\n        gas,\n        gasPrice,\n        maxFeePerBlobGas,\n        maxFeePerGas,\n        maxPriorityFeePerGas,\n        nonce,\n        to,\n        value,\n      } as TransactionRequest)\n\n      const isWalletNamespaceSupported = supportsWalletNamespace.get(client.uid)\n      const method = isWalletNamespaceSupported\n        ? 'wallet_sendTransaction'\n        : 'eth_sendTransaction'\n\n      try {\n        return await client.request(\n          {\n            method,\n            params: [request],\n          },\n          { retryCount: 0 },\n        )\n      } catch (e) {\n        if (isWalletNamespaceSupported === false) throw e\n\n        const error = e as BaseError\n        // If the transport does not support the method or input, attempt to use the\n        // `wallet_sendTransaction` method.\n        if (\n          error.name === 'InvalidInputRpcError' ||\n          error.name === 'InvalidParamsRpcError' ||\n          error.name === 'MethodNotFoundRpcError' ||\n          error.name === 'MethodNotSupportedRpcError'\n        ) {\n          return await client\n            .request(\n              {\n                method: 'wallet_sendTransaction',\n                params: [request],\n              },\n              { retryCount: 0 },\n            )\n            .then((hash) => {\n              supportsWalletNamespace.set(client.uid, true)\n              return hash\n            })\n            .catch((e) => {\n              const walletNamespaceError = e as BaseError\n              if (\n                walletNamespaceError.name === 'MethodNotFoundRpcError' ||\n                walletNamespaceError.name === 'MethodNotSupportedRpcError'\n              ) {\n                supportsWalletNamespace.set(client.uid, false)\n                throw error\n              }\n\n              throw walletNamespaceError\n            })\n        }\n\n        throw error\n      }\n    }\n\n    if (account?.type === 'local') {\n      // Prepare the request for signing (assign appropriate fees, etc.)\n      const request = await getAction(\n        client,\n        prepareTransactionRequest,\n        'prepareTransactionRequest',\n      )({\n        account,\n        accessList,\n        authorizationList,\n        blobs,\n        chain,\n        data,\n        gas,\n        gasPrice,\n        maxFeePerBlobGas,\n        maxFeePerGas,\n        maxPriorityFeePerGas,\n        nonce,\n        nonceManager: account.nonceManager,\n        parameters: [...defaultParameters, 'sidecars'],\n        value,\n        ...rest,\n        to,\n      } as any)\n\n      const serializer = chain?.serializers?.transaction\n      const serializedTransaction = (await account.signTransaction(request, {\n        serializer,\n      })) as Hash\n      return await getAction(\n        client,\n        sendRawTransaction,\n        'sendRawTransaction',\n      )({\n        serializedTransaction,\n      })\n    }\n\n    if (account?.type === 'smart')\n      throw new AccountTypeNotSupportedError({\n        metaMessages: [\n          'Consider using the `sendUserOperation` Action instead.',\n        ],\n        docsPath: '/docs/actions/bundler/sendUserOperation',\n        type: 'smart',\n      })\n\n    throw new AccountTypeNotSupportedError({\n      docsPath: '/docs/actions/wallet/sendTransaction',\n      type: (account as any)?.type,\n    })\n  } catch (err) {\n    if (err instanceof AccountTypeNotSupportedError) throw err\n    throw getTransactionError(err as BaseError, {\n      ...parameters,\n      account,\n      chain: parameters.chain || undefined,\n    })\n  }\n}\n", "import { LruMap } from '../lru.js'\n\n/** @internal */\nexport const promiseCache = /*#__PURE__*/ new LruMap<Promise<any>>(8192)\n\ntype WithDedupeOptions = {\n  enabled?: boolean | undefined\n  id?: string | undefined\n}\n\n/** Deduplicates in-flight promises. */\nexport function withDedupe<data>(\n  fn: () => Promise<data>,\n  { enabled = true, id }: WithDedupeOptions,\n): Promise<data> {\n  if (!enabled || !id) return fn()\n  if (promiseCache.get(id)) return promiseCache.get(id)!\n  const promise = fn().finally(() => promiseCache.delete(id))\n  promiseCache.set(id, promise)\n  return promise\n}\n", "function createIdStore() {\n  return {\n    current: 0,\n    take() {\n      return this.current++\n    },\n    reset() {\n      this.current = 0\n    },\n  }\n}\n\nexport const idCache = /*#__PURE__*/ createIdStore()\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type {\n  Chain,\n  ExtractChainFormatterReturnType,\n} from '../../types/chain.js'\nimport type { RpcTransactionReceipt } from '../../types/rpc.js'\nimport type { TransactionReceipt } from '../../types/transaction.js'\nimport type { ExactPartial } from '../../types/utils.js'\nimport { hexToNumber } from '../encoding/fromHex.js'\n\nimport { type DefineFormatterErrorType, defineFormatter } from './formatter.js'\nimport { formatLog } from './log.js'\nimport { transactionType } from './transaction.js'\n\nexport type FormattedTransactionReceipt<\n  chain extends Chain | undefined = undefined,\n> = ExtractChainFormatterReturnType<\n  chain,\n  'transactionReceipt',\n  TransactionReceipt\n>\n\nexport const receiptStatuses = {\n  '0x0': 'reverted',\n  '0x1': 'success',\n} as const\n\nexport type FormatTransactionReceiptErrorType = ErrorType\n\nexport function formatTransactionReceipt(\n  transactionReceipt: ExactPartial<RpcTransactionReceipt>,\n) {\n  const receipt = {\n    ...transactionReceipt,\n    blockNumber: transactionReceipt.blockNumber\n      ? BigInt(transactionReceipt.blockNumber)\n      : null,\n    contractAddress: transactionReceipt.contractAddress\n      ? transactionReceipt.contractAddress\n      : null,\n    cumulativeGasUsed: transactionReceipt.cumulativeGasUsed\n      ? BigInt(transactionReceipt.cumulativeGasUsed)\n      : null,\n    effectiveGasPrice: transactionReceipt.effectiveGasPrice\n      ? BigInt(transactionReceipt.effectiveGasPrice)\n      : null,\n    gasUsed: transactionReceipt.gasUsed\n      ? BigInt(transactionReceipt.gasUsed)\n      : null,\n    logs: transactionReceipt.logs\n      ? transactionReceipt.logs.map((log) => formatLog(log))\n      : null,\n    to: transactionReceipt.to ? transactionReceipt.to : null,\n    transactionIndex: transactionReceipt.transactionIndex\n      ? hexToNumber(transactionReceipt.transactionIndex)\n      : null,\n    status: transactionReceipt.status\n      ? receiptStatuses[transactionReceipt.status]\n      : null,\n    type: transactionReceipt.type\n      ? transactionType[\n          transactionReceipt.type as keyof typeof transactionType\n        ] || transactionReceipt.type\n      : null,\n  } as TransactionReceipt\n\n  if (transactionReceipt.blobGasPrice)\n    receipt.blobGasPrice = BigInt(transactionReceipt.blobGasPrice)\n  if (transactionReceipt.blobGasUsed)\n    receipt.blobGasUsed = BigInt(transactionReceipt.blobGasUsed)\n\n  return receipt\n}\n\nexport type DefineTransactionReceiptErrorType =\n  | DefineFormatterErrorType\n  | ErrorType\n\nexport const defineTransactionReceipt = /*#__PURE__*/ defineFormatter(\n  'transactionReceipt',\n  formatTransactionReceipt,\n)\n", "/**\n * RIPEMD-160 legacy hash function.\n * https://homes.esat.kuleuven.be/~bosselae/ripemd160.html\n * https://homes.esat.kuleuven.be/~bosselae/ripemd160/pdf/AB-9601/AB-9601.pdf\n * @module\n */\nimport { HashMD } from './_md.js';\nimport { rotl, wrapConstructor, type CHash } from './utils.js';\n\nconst Rho = /* @__PURE__ */ new Uint8Array([7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8]);\nconst Id = /* @__PURE__ */ new Uint8Array(new Array(16).fill(0).map((_, i) => i));\nconst Pi = /* @__PURE__ */ Id.map((i) => (9 * i + 5) % 16);\nlet idxL = [Id];\nlet idxR = [Pi];\nfor (let i = 0; i < 4; i++) for (let j of [idxL, idxR]) j.push(j[i].map((k) => Rho[k]));\n\nconst shifts = /* @__PURE__ */ [\n  [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8],\n  [12, 13, 11, 15, 6, 9, 9, 7, 12, 15, 11, 13, 7, 8, 7, 7],\n  [13, 15, 14, 11, 7, 7, 6, 8, 13, 14, 13, 12, 5, 5, 6, 9],\n  [14, 11, 12, 14, 8, 6, 5, 5, 15, 12, 15, 14, 9, 9, 8, 6],\n  [15, 12, 13, 13, 9, 5, 8, 6, 14, 11, 12, 11, 8, 6, 5, 5],\n].map((i) => new Uint8Array(i));\nconst shiftsL = /* @__PURE__ */ idxL.map((idx, i) => idx.map((j) => shifts[i][j]));\nconst shiftsR = /* @__PURE__ */ idxR.map((idx, i) => idx.map((j) => shifts[i][j]));\nconst Kl = /* @__PURE__ */ new Uint32Array([\n  0x00000000, 0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xa953fd4e,\n]);\nconst Kr = /* @__PURE__ */ new Uint32Array([\n  0x50a28be6, 0x5c4dd124, 0x6d703ef3, 0x7a6d76e9, 0x00000000,\n]);\n// It's called f() in spec.\nfunction f(group: number, x: number, y: number, z: number): number {\n  if (group === 0) return x ^ y ^ z;\n  else if (group === 1) return (x & y) | (~x & z);\n  else if (group === 2) return (x | ~y) ^ z;\n  else if (group === 3) return (x & z) | (y & ~z);\n  else return x ^ (y | ~z);\n}\n// Temporary buffer, not used to store anything between runs\nconst R_BUF = /* @__PURE__ */ new Uint32Array(16);\nexport class RIPEMD160 extends HashMD<RIPEMD160> {\n  private h0 = 0x67452301 | 0;\n  private h1 = 0xefcdab89 | 0;\n  private h2 = 0x98badcfe | 0;\n  private h3 = 0x10325476 | 0;\n  private h4 = 0xc3d2e1f0 | 0;\n\n  constructor() {\n    super(64, 20, 8, true);\n  }\n  protected get(): [number, number, number, number, number] {\n    const { h0, h1, h2, h3, h4 } = this;\n    return [h0, h1, h2, h3, h4];\n  }\n  protected set(h0: number, h1: number, h2: number, h3: number, h4: number): void {\n    this.h0 = h0 | 0;\n    this.h1 = h1 | 0;\n    this.h2 = h2 | 0;\n    this.h3 = h3 | 0;\n    this.h4 = h4 | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    for (let i = 0; i < 16; i++, offset += 4) R_BUF[i] = view.getUint32(offset, true);\n    // prettier-ignore\n    let al = this.h0 | 0, ar = al,\n        bl = this.h1 | 0, br = bl,\n        cl = this.h2 | 0, cr = cl,\n        dl = this.h3 | 0, dr = dl,\n        el = this.h4 | 0, er = el;\n\n    // Instead of iterating 0 to 80, we split it into 5 groups\n    // And use the groups in constants, functions, etc. Much simpler\n    for (let group = 0; group < 5; group++) {\n      const rGroup = 4 - group;\n      const hbl = Kl[group], hbr = Kr[group]; // prettier-ignore\n      const rl = idxL[group], rr = idxR[group]; // prettier-ignore\n      const sl = shiftsL[group], sr = shiftsR[group]; // prettier-ignore\n      for (let i = 0; i < 16; i++) {\n        const tl = (rotl(al + f(group, bl, cl, dl) + R_BUF[rl[i]] + hbl, sl[i]) + el) | 0;\n        al = el, el = dl, dl = rotl(cl, 10) | 0, cl = bl, bl = tl; // prettier-ignore\n      }\n      // 2 loops are 10% faster\n      for (let i = 0; i < 16; i++) {\n        const tr = (rotl(ar + f(rGroup, br, cr, dr) + R_BUF[rr[i]] + hbr, sr[i]) + er) | 0;\n        ar = er, er = dr, dr = rotl(cr, 10) | 0, cr = br, br = tr; // prettier-ignore\n      }\n    }\n    // Add the compressed chunk to the current hash value\n    this.set(\n      (this.h1 + cl + dr) | 0,\n      (this.h2 + dl + er) | 0,\n      (this.h3 + el + ar) | 0,\n      (this.h4 + al + br) | 0,\n      (this.h0 + bl + cr) | 0\n    );\n  }\n  protected roundClean(): void {\n    R_BUF.fill(0);\n  }\n  destroy(): void {\n    this.destroyed = true;\n    this.buffer.fill(0);\n    this.set(0, 0, 0, 0, 0);\n  }\n}\n\n/** RIPEMD-160 - a legacy hash function from 1990s. */\nexport const ripemd160: CHash = /* @__PURE__ */ wrapConstructor(() => new RIPEMD160());\n", "import type { Address } from 'abitype'\n\nimport { getTransactionCount } from '../actions/public/getTransactionCount.js'\nimport type { Client } from '../clients/createClient.js'\nimport type { MaybePromise } from '../types/utils.js'\nimport { LruMap } from './lru.js'\n\nexport type CreateNonceManagerParameters = {\n  source: NonceManagerSource\n}\n\ntype FunctionParameters = {\n  address: Address\n  chainId: number\n}\n\nexport type NonceManager = {\n  /** Get and increment a nonce. */\n  consume: (\n    parameters: FunctionParameters & { client: Client },\n  ) => Promise<number>\n  /** Increment a nonce. */\n  increment: (chainId: FunctionParameters) => void\n  /** Get a nonce. */\n  get: (chainId: FunctionParameters & { client: Client }) => Promise<number>\n  /** Reset a nonce. */\n  reset: (chainId: FunctionParameters) => void\n}\n\n/**\n * Creates a nonce manager for auto-incrementing transaction nonces.\n *\n * - Docs: https://viem.sh/docs/accounts/createNonceManager\n *\n * @example\n * ```ts\n * const nonceManager = createNonceManager({\n *   source: jsonRpc(),\n * })\n * ```\n */\nexport function createNonceManager(\n  parameters: CreateNonceManagerParameters,\n): NonceManager {\n  const { source } = parameters\n\n  const deltaMap = new Map()\n  const nonceMap = new LruMap<number>(8192)\n  const promiseMap = new Map<string, Promise<number>>()\n\n  const getKey = ({ address, chainId }: FunctionParameters) =>\n    `${address}.${chainId}`\n\n  return {\n    async consume({ address, chainId, client }) {\n      const key = getKey({ address, chainId })\n      const promise = this.get({ address, chainId, client })\n\n      this.increment({ address, chainId })\n      const nonce = await promise\n\n      await source.set({ address, chainId }, nonce)\n      nonceMap.set(key, nonce)\n\n      return nonce\n    },\n    async increment({ address, chainId }) {\n      const key = getKey({ address, chainId })\n      const delta = deltaMap.get(key) ?? 0\n      deltaMap.set(key, delta + 1)\n    },\n    async get({ address, chainId, client }) {\n      const key = getKey({ address, chainId })\n\n      let promise = promiseMap.get(key)\n      if (!promise) {\n        promise = (async () => {\n          try {\n            const nonce = await source.get({ address, chainId, client })\n            const previousNonce = nonceMap.get(key) ?? 0\n            if (previousNonce > 0 && nonce <= previousNonce)\n              return previousNonce + 1\n            nonceMap.delete(key)\n            return nonce\n          } finally {\n            this.reset({ address, chainId })\n          }\n        })()\n        promiseMap.set(key, promise)\n      }\n\n      const delta = deltaMap.get(key) ?? 0\n      return delta + (await promise)\n    },\n    reset({ address, chainId }) {\n      const key = getKey({ address, chainId })\n      deltaMap.delete(key)\n      promiseMap.delete(key)\n    },\n  }\n}\n\n////////////////////////////////////////////////////////////////////////////////////////////\n// Sources\n\nexport type NonceManagerSource = {\n  /** Get a nonce. */\n  get(parameters: FunctionParameters & { client: Client }): MaybePromise<number>\n  /** Set a nonce. */\n  set(parameters: FunctionParameters, nonce: number): MaybePromise<void>\n}\n\n/** JSON-RPC source for a nonce manager. */\nexport function jsonRpc(): NonceManagerSource {\n  return {\n    async get(parameters) {\n      const { address, client } = parameters\n      return getTransactionCount(client, {\n        address,\n        blockTag: 'pending',\n      })\n    },\n    set() {},\n  }\n}\n\n////////////////////////////////////////////////////////////////////////////////////////////\n// Default\n\n/** Default Nonce Manager with a JSON-RPC source. */\nexport const nonceManager = /*#__PURE__*/ createNonceManager({\n  source: jsonRpc(),\n})\n", "/** @internal */\nexport const version = '0.1.1'\n", "import { version } from '../version.js'\n\n/** @internal */\nexport function getUrl(url: string) {\n  return url\n}\n\n/** @internal */\nexport function getVersion() {\n  return version\n}\n\n/** @internal */\nexport function prettyPrint(args: unknown) {\n  if (!args) return ''\n  const entries = Object.entries(args)\n    .map(([key, value]) => {\n      if (value === undefined || value === false) return null\n      return [key, value]\n    })\n    .filter(Boolean) as [string, string][]\n  const maxLength = entries.reduce((acc, [key]) => Math.max(acc, key.length), 0)\n  return entries\n    .map(([key, value]) => `  ${`${key}:`.padEnd(maxLength + 1)}  ${value}`)\n    .join('\\n')\n}\n", "import { getVersion } from './internal/errors.js'\n\nexport type GlobalErrorType<name extends string = 'Error'> = Error & {\n  name: name\n}\n\n/**\n * Base error class inherited by all errors thrown by ox.\n *\n * @example\n * ```ts\n * import { Errors } from 'ox'\n * throw new Errors.BaseError('An error occurred')\n * ```\n */\nexport class BaseError<\n  cause extends Error | undefined = undefined,\n> extends Error {\n  details: string\n  docs?: string | undefined\n  docsPath?: string | undefined\n  shortMessage: string\n\n  override cause: cause\n  override name = 'BaseError'\n\n  version = `ox@${getVersion()}`\n\n  constructor(shortMessage: string, options: BaseError.Options<cause> = {}) {\n    const details = (() => {\n      if (options.cause instanceof BaseError) {\n        if (options.cause.details) return options.cause.details\n        if (options.cause.shortMessage) return options.cause.shortMessage\n      }\n      if (options.cause?.message) return options.cause.message\n      return options.details!\n    })()\n    const docsPath = (() => {\n      if (options.cause instanceof BaseError)\n        return options.cause.docsPath || options.docsPath\n      return options.docsPath\n    })()\n\n    const docsBaseUrl = 'https://oxlib.sh'\n    const docs = `${docsBaseUrl}${docsPath ?? ''}`\n\n    const message = [\n      shortMessage || 'An error occurred.',\n      ...(options.metaMessages ? ['', ...options.metaMessages] : []),\n      ...(details || docsPath\n        ? [\n            '',\n            details ? `Details: ${details}` : undefined,\n            docsPath ? `See: ${docs}` : undefined,\n          ]\n        : []),\n    ]\n      .filter((x) => typeof x === 'string')\n      .join('\\n')\n\n    super(message, options.cause ? { cause: options.cause } : undefined)\n\n    this.cause = options.cause as any\n    this.details = details\n    this.docs = docs\n    this.docsPath = docsPath\n    this.shortMessage = shortMessage\n  }\n\n  walk(): Error\n  walk(fn: (err: unknown) => boolean): Error | null\n  walk(fn?: any): any {\n    return walk(this, fn)\n  }\n}\n\nexport declare namespace BaseError {\n  type Options<cause extends Error | undefined = Error | undefined> = {\n    cause?: cause | undefined\n    details?: string | undefined\n    docsPath?: string | undefined\n    metaMessages?: (string | undefined)[] | undefined\n  }\n}\n\n/** @internal */\nfunction walk(\n  err: unknown,\n  fn?: ((err: unknown) => boolean) | undefined,\n): unknown {\n  if (fn?.(err)) return err\n  if (err && typeof err === 'object' && 'cause' in err && err.cause)\n    return walk(err.cause, fn)\n  return fn ? null : err\n}\n", "import * as Bytes from '../Bytes.js'\nimport type * as Errors from '../Errors.js'\n\n/** @internal */\nexport function assertSize(bytes: Bytes.Bytes, size_: number): void {\n  if (Bytes.size(bytes) > size_)\n    throw new Bytes.SizeOverflowError({\n      givenSize: Bytes.size(bytes),\n      maxSize: size_,\n    })\n}\n\n/** @internal */\nexport declare namespace assertSize {\n  type ErrorType =\n    | Bytes.size.ErrorType\n    | Bytes.SizeOverflowError\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function assertStartOffset(\n  value: Bytes.Bytes,\n  start?: number | undefined,\n) {\n  if (typeof start === 'number' && start > 0 && start > Bytes.size(value) - 1)\n    throw new Bytes.SliceOffsetOutOfBoundsError({\n      offset: start,\n      position: 'start',\n      size: Bytes.size(value),\n    })\n}\n\nexport declare namespace assertStartOffset {\n  export type ErrorType =\n    | Bytes.SliceOffsetOutOfBoundsError\n    | Bytes.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function assertEndOffset(\n  value: Bytes.Bytes,\n  start?: number | undefined,\n  end?: number | undefined,\n) {\n  if (\n    typeof start === 'number' &&\n    typeof end === 'number' &&\n    Bytes.size(value) !== end - start\n  ) {\n    throw new Bytes.SliceOffsetOutOfBoundsError({\n      offset: end,\n      position: 'end',\n      size: Bytes.size(value),\n    })\n  }\n}\n\n/** @internal */\nexport declare namespace assertEndOffset {\n  type ErrorType =\n    | Bytes.SliceOffsetOutOfBoundsError\n    | Bytes.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport const charCodeMap = {\n  zero: 48,\n  nine: 57,\n  A: 65,\n  F: 70,\n  a: 97,\n  f: 102,\n} as const\n\n/** @internal */\nexport function charCodeToBase16(char: number) {\n  if (char >= charCodeMap.zero && char <= charCodeMap.nine)\n    return char - charCodeMap.zero\n  if (char >= charCodeMap.A && char <= charCodeMap.F)\n    return char - (charCodeMap.A - 10)\n  if (char >= charCodeMap.a && char <= charCodeMap.f)\n    return char - (charCodeMap.a - 10)\n  return undefined\n}\n\n/** @internal */\nexport function pad(bytes: Bytes.Bytes, options: pad.Options = {}) {\n  const { dir, size = 32 } = options\n  if (size === 0) return bytes\n  if (bytes.length > size)\n    throw new Bytes.SizeExceedsPaddingSizeError({\n      size: bytes.length,\n      targetSize: size,\n      type: 'Bytes',\n    })\n  const paddedBytes = new Uint8Array(size)\n  for (let i = 0; i < size; i++) {\n    const padEnd = dir === 'right'\n    paddedBytes[padEnd ? i : size - i - 1] =\n      bytes[padEnd ? i : bytes.length - i - 1]!\n  }\n  return paddedBytes\n}\n\n/** @internal */\nexport declare namespace pad {\n  type Options = {\n    dir?: 'left' | 'right' | undefined\n    size?: number | undefined\n  }\n\n  type ReturnType = Bytes.Bytes\n\n  type ErrorType = Bytes.SizeExceedsPaddingSizeError | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function trim(\n  value: Bytes.Bytes,\n  options: trim.Options = {},\n): trim.ReturnType {\n  const { dir = 'left' } = options\n\n  let data = value\n\n  let sliceLength = 0\n  for (let i = 0; i < data.length - 1; i++) {\n    if (data[dir === 'left' ? i : data.length - i - 1]!.toString() === '0')\n      sliceLength++\n    else break\n  }\n  data =\n    dir === 'left'\n      ? data.slice(sliceLength)\n      : data.slice(0, data.length - sliceLength)\n\n  return data as trim.ReturnType\n}\n\n/** @internal */\nexport declare namespace trim {\n  type Options = {\n    dir?: 'left' | 'right' | undefined\n  }\n\n  type ReturnType = Bytes.Bytes\n\n  type ErrorType = Errors.GlobalErrorType\n}\n", "import type * as Errors from '../Errors.js'\nimport * as Hex from '../Hex.js'\n\n/** @internal */\nexport function assertSize(hex: Hex.Hex, size_: number): void {\n  if (Hex.size(hex) > size_)\n    throw new Hex.SizeOverflowError({\n      givenSize: Hex.size(hex),\n      maxSize: size_,\n    })\n}\n\n/** @internal */\nexport declare namespace assertSize {\n  type ErrorType =\n    | Hex.size.ErrorType\n    | Hex.SizeOverflowError\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function assertStartOffset(value: Hex.Hex, start?: number | undefined) {\n  if (typeof start === 'number' && start > 0 && start > Hex.size(value) - 1)\n    throw new Hex.SliceOffsetOutOfBoundsError({\n      offset: start,\n      position: 'start',\n      size: Hex.size(value),\n    })\n}\n\nexport declare namespace assertStartOffset {\n  type ErrorType =\n    | Hex.SliceOffsetOutOfBoundsError\n    | Hex.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function assertEndOffset(\n  value: Hex.Hex,\n  start?: number | undefined,\n  end?: number | undefined,\n) {\n  if (\n    typeof start === 'number' &&\n    typeof end === 'number' &&\n    Hex.size(value) !== end - start\n  ) {\n    throw new Hex.SliceOffsetOutOfBoundsError({\n      offset: end,\n      position: 'end',\n      size: Hex.size(value),\n    })\n  }\n}\n\nexport declare namespace assertEndOffset {\n  type ErrorType =\n    | Hex.SliceOffsetOutOfBoundsError\n    | Hex.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function pad(hex_: Hex.Hex, options: pad.Options = {}) {\n  const { dir, size = 32 } = options\n\n  if (size === 0) return hex_\n\n  const hex = hex_.replace('0x', '')\n  if (hex.length > size * 2)\n    throw new Hex.SizeExceedsPaddingSizeError({\n      size: Math.ceil(hex.length / 2),\n      targetSize: size,\n      type: 'Hex',\n    })\n\n  return `0x${hex[dir === 'right' ? 'padEnd' : 'padStart'](size * 2, '0')}` as Hex.Hex\n}\n\n/** @internal */\nexport declare namespace pad {\n  type Options = {\n    dir?: 'left' | 'right' | undefined\n    size?: number | undefined\n  }\n  type ErrorType = Hex.SizeExceedsPaddingSizeError | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function trim(\n  value: Hex.Hex,\n  options: trim.Options = {},\n): trim.ReturnType {\n  const { dir = 'left' } = options\n\n  let data = value.replace('0x', '')\n\n  let sliceLength = 0\n  for (let i = 0; i < data.length - 1; i++) {\n    if (data[dir === 'left' ? i : data.length - i - 1]!.toString() === '0')\n      sliceLength++\n    else break\n  }\n  data =\n    dir === 'left'\n      ? data.slice(sliceLength)\n      : data.slice(0, data.length - sliceLength)\n\n  if (data === '0') return '0x'\n  if (dir === 'right' && data.length % 2 === 1) return `0x${data}0`\n  return `0x${data}` as trim.ReturnType\n}\n\n/** @internal */\nexport declare namespace trim {\n  type Options = {\n    dir?: 'left' | 'right' | undefined\n  }\n\n  type ReturnType = Hex.Hex\n\n  type ErrorType = Errors.GlobalErrorType\n}\n", "import { equalBytes } from '@noble/curves/abstract/utils'\nimport * as Errors from './Errors.js'\nimport * as Hex from './Hex.js'\nimport * as <PERSON><PERSON> from './Json.js'\nimport * as internal from './internal/bytes.js'\nimport * as internal_hex from './internal/hex.js'\n\nconst decoder = /*#__PURE__*/ new TextDecoder()\nconst encoder = /*#__PURE__*/ new TextEncoder()\n\n/** Root type for a Bytes array. */\nexport type Bytes = Uint8Array\n\n/**\n * Asserts if the given value is {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.assert('abc')\n * // @error: Bytes.InvalidBytesTypeError:\n * // @error: Value `\"abc\"` of type `string` is an invalid Bytes value.\n * // @error: Bytes values must be of type `Uint8Array`.\n * ```\n *\n * @param value - Value to assert.\n */\nexport function assert(value: unknown): asserts value is Bytes {\n  if (value instanceof Uint8Array) return\n  if (!value) throw new InvalidBytesTypeError(value)\n  if (typeof value !== 'object') throw new InvalidBytesTypeError(value)\n  if (!('BYTES_PER_ELEMENT' in value)) throw new InvalidBytesTypeError(value)\n  if (value.BYTES_PER_ELEMENT !== 1 || value.constructor.name !== 'Uint8Array')\n    throw new InvalidBytesTypeError(value)\n}\n\nexport declare namespace assert {\n  type ErrorType = InvalidBytesTypeError | Errors.GlobalErrorType\n}\n\n/**\n * Concatenates two or more {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const bytes = Bytes.concat(\n *   Bytes.from([1]),\n *   Bytes.from([69]),\n *   Bytes.from([420, 69]),\n * )\n * // @log: Uint8Array [ 1, 69, 420, 69 ]\n * ```\n *\n * @param values - Values to concatenate.\n * @returns Concatenated {@link ox#Bytes.Bytes}.\n */\nexport function concat(...values: readonly Bytes[]): Bytes {\n  let length = 0\n  for (const arr of values) {\n    length += arr.length\n  }\n  const result = new Uint8Array(length)\n  for (let i = 0, index = 0; i < values.length; i++) {\n    const arr = values[i]\n    result.set(arr!, index)\n    index += arr!.length\n  }\n  return result\n}\n\nexport declare namespace concat {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Instantiates a {@link ox#Bytes.Bytes} value from a `Uint8Array`, a hex string, or an array of unsigned 8-bit integers.\n *\n * :::tip\n *\n * To instantiate from a **Boolean**, **String**, or **Number**, use one of the following:\n *\n * - `Bytes.fromBoolean`\n *\n * - `Bytes.fromString`\n *\n * - `Bytes.fromNumber`\n *\n * :::\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.from([255, 124, 5, 4])\n * // @log: Uint8Array([255, 124, 5, 4])\n *\n * const data = Bytes.from('0xdeadbeef')\n * // @log: Uint8Array([222, 173, 190, 239])\n * ```\n *\n * @param value - Value to convert.\n * @returns A {@link ox#Bytes.Bytes} instance.\n */\nexport function from(value: Hex.Hex | Bytes | readonly number[]): Bytes {\n  if (value instanceof Uint8Array) return value\n  if (typeof value === 'string') return fromHex(value)\n  return fromArray(value)\n}\n\nexport declare namespace from {\n  type ErrorType =\n    | fromHex.ErrorType\n    | fromArray.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts an array of unsigned 8-bit integers into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromArray([255, 124, 5, 4])\n * // @log: Uint8Array([255, 124, 5, 4])\n * ```\n *\n * @param value - Value to convert.\n * @returns A {@link ox#Bytes.Bytes} instance.\n */\nexport function fromArray(value: readonly number[] | Uint8Array): Bytes {\n  return value instanceof Uint8Array ? value : new Uint8Array(value)\n}\n\nexport declare namespace fromArray {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Encodes a boolean value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromBoolean(true)\n * // @log: Uint8Array([1])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromBoolean(true, { size: 32 })\n * // @log: Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1])\n * ```\n *\n * @param value - Boolean value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nexport function fromBoolean(value: boolean, options: fromBoolean.Options = {}) {\n  const { size } = options\n  const bytes = new Uint8Array(1)\n  bytes[0] = Number(value)\n  if (typeof size === 'number') {\n    internal.assertSize(bytes, size)\n    return padLeft(bytes, size)\n  }\n  return bytes\n}\n\nexport declare namespace fromBoolean {\n  type Options = {\n    /** Size of the output bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | padLeft.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a {@link ox#Hex.Hex} value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromHex('0x48656c6c6f20776f726c6421')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromHex('0x48656c6c6f20776f726c6421', { size: 32 })\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n * ```\n *\n * @param value - {@link ox#Hex.Hex} value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nexport function fromHex(value: Hex.Hex, options: fromHex.Options = {}): Bytes {\n  const { size } = options\n\n  let hex = value\n  if (size) {\n    internal_hex.assertSize(value, size)\n    hex = Hex.padRight(value, size)\n  }\n\n  let hexString = hex.slice(2) as string\n  if (hexString.length % 2) hexString = `0${hexString}`\n\n  const length = hexString.length / 2\n  const bytes = new Uint8Array(length)\n  for (let index = 0, j = 0; index < length; index++) {\n    const nibbleLeft = internal.charCodeToBase16(hexString.charCodeAt(j++))\n    const nibbleRight = internal.charCodeToBase16(hexString.charCodeAt(j++))\n    if (nibbleLeft === undefined || nibbleRight === undefined) {\n      throw new Errors.BaseError(\n        `Invalid byte sequence (\"${hexString[j - 2]}${hexString[j - 1]}\" in \"${hexString}\").`,\n      )\n    }\n    bytes[index] = nibbleLeft * 16 + nibbleRight\n  }\n  return bytes\n}\n\nexport declare namespace fromHex {\n  type Options = {\n    /** Size of the output bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal_hex.assertSize.ErrorType\n    | Hex.padRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a number value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromNumber(420)\n * // @log: Uint8Array([1, 164])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromNumber(420, { size: 4 })\n * // @log: Uint8Array([0, 0, 1, 164])\n * ```\n *\n * @param value - Number value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nexport function fromNumber(\n  value: bigint | number,\n  options?: fromNumber.Options | undefined,\n) {\n  const hex = Hex.fromNumber(value, options)\n  return fromHex(hex)\n}\n\nexport declare namespace fromNumber {\n  export type Options = Hex.fromNumber.Options\n\n  export type ErrorType =\n    | Hex.fromNumber.ErrorType\n    | fromHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a string into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromString('Hello world!')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100, 33])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromString('Hello world!', { size: 32 })\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n * ```\n *\n * @param value - String to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nexport function fromString(\n  value: string,\n  options: fromString.Options = {},\n): Bytes {\n  const { size } = options\n\n  const bytes = encoder.encode(value)\n  if (typeof size === 'number') {\n    internal.assertSize(bytes, size)\n    return padRight(bytes, size)\n  }\n  return bytes\n}\n\nexport declare namespace fromString {\n  type Options = {\n    /** Size of the output bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | padRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Checks if two {@link ox#Bytes.Bytes} values are equal.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.isEqual(Bytes.from([1]), Bytes.from([1]))\n * // @log: true\n *\n * Bytes.isEqual(Bytes.from([1]), Bytes.from([2]))\n * // @log: false\n * ```\n *\n * @param bytesA - First {@link ox#Bytes.Bytes} value.\n * @param bytesB - Second {@link ox#Bytes.Bytes} value.\n * @returns `true` if the two values are equal, otherwise `false`.\n */\nexport function isEqual(bytesA: Bytes, bytesB: Bytes) {\n  return equalBytes(bytesA, bytesB)\n}\n\nexport declare namespace isEqual {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Pads a {@link ox#Bytes.Bytes} value to the left with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padLeft(Bytes.from([1]), 4)\n * // @log: Uint8Array([0, 0, 0, 1])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value to pad.\n * @param size - Size to pad the {@link ox#Bytes.Bytes} value to.\n * @returns Padded {@link ox#Bytes.Bytes} value.\n */\nexport function padLeft(\n  value: Bytes,\n  size?: number | undefined,\n): padLeft.ReturnType {\n  return internal.pad(value, { dir: 'left', size })\n}\n\nexport declare namespace padLeft {\n  type ReturnType = internal.pad.ReturnType\n  type ErrorType = internal.pad.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Pads a {@link ox#Bytes.Bytes} value to the right with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padRight(Bytes.from([1]), 4)\n * // @log: Uint8Array([1, 0, 0, 0])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value to pad.\n * @param size - Size to pad the {@link ox#Bytes.Bytes} value to.\n * @returns Padded {@link ox#Bytes.Bytes} value.\n */\nexport function padRight(\n  value: Bytes,\n  size?: number | undefined,\n): padRight.ReturnType {\n  return internal.pad(value, { dir: 'right', size })\n}\n\nexport declare namespace padRight {\n  type ReturnType = internal.pad.ReturnType\n  type ErrorType = internal.pad.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Generates random {@link ox#Bytes.Bytes} of the specified length.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const bytes = Bytes.random(32)\n * // @log: Uint8Array([... x32])\n * ```\n *\n * @param length - Length of the random {@link ox#Bytes.Bytes} to generate.\n * @returns Random {@link ox#Bytes.Bytes} of the specified length.\n */\nexport function random(length: number): Bytes {\n  return crypto.getRandomValues(new Uint8Array(length))\n}\n\nexport declare namespace random {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Retrieves the size of a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.size(Bytes.from([1, 2, 3, 4]))\n * // @log: 4\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Size of the {@link ox#Bytes.Bytes} value.\n */\nexport function size(value: Bytes): number {\n  return value.length\n}\n\nexport declare namespace size {\n  export type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Returns a section of a {@link ox#Bytes.Bytes} value given a start/end bytes offset.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.slice(\n *   Bytes.from([1, 2, 3, 4, 5, 6, 7, 8, 9]),\n *   1,\n *   4,\n * )\n * // @log: Uint8Array([2, 3, 4])\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value.\n * @param start - Start offset.\n * @param end - End offset.\n * @param options - Slice options.\n * @returns Sliced {@link ox#Bytes.Bytes} value.\n */\nexport function slice(\n  value: Bytes,\n  start?: number | undefined,\n  end?: number | undefined,\n  options: slice.Options = {},\n): Bytes {\n  const { strict } = options\n  internal.assertStartOffset(value, start)\n  const value_ = value.slice(start, end)\n  if (strict) internal.assertEndOffset(value_, start, end)\n  return value_\n}\n\nexport declare namespace slice {\n  type Options = {\n    /** Asserts that the sliced value is the same size as the given start/end offsets. */\n    strict?: boolean | undefined\n  }\n\n  export type ErrorType =\n    | internal.assertStartOffset.ErrorType\n    | internal.assertEndOffset.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a bigint.\n *\n * @example\n * ```ts\n * import { Bytes } from 'ox'\n *\n * Bytes.toBigInt(Bytes.from([1, 164]))\n * // @log: 420n\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Decoding options.\n * @returns Decoded bigint.\n */\nexport function toBigInt(bytes: Bytes, options: toBigInt.Options = {}): bigint {\n  const { size } = options\n  if (typeof size !== 'undefined') internal.assertSize(bytes, size)\n  const hex = Hex.fromBytes(bytes, options)\n  return Hex.toBigInt(hex, options)\n}\n\nexport declare namespace toBigInt {\n  type Options = {\n    /** Whether or not the number of a signed representation. */\n    signed?: boolean | undefined\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | Hex.fromBytes.ErrorType\n    | Hex.toBigInt.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a boolean.\n *\n * @example\n * ```ts\n * import { Bytes } from 'ox'\n *\n * Bytes.toBoolean(Bytes.from([1]))\n * // @log: true\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Decoding options.\n * @returns Decoded boolean.\n */\nexport function toBoolean(\n  bytes: Bytes,\n  options: toBoolean.Options = {},\n): boolean {\n  const { size } = options\n  let bytes_ = bytes\n  if (typeof size !== 'undefined') {\n    internal.assertSize(bytes_, size)\n    bytes_ = trimLeft(bytes_)\n  }\n  if (bytes_.length > 1 || bytes_[0]! > 1)\n    throw new InvalidBytesBooleanError(bytes_)\n  return Boolean(bytes_[0])\n}\n\nexport declare namespace toBoolean {\n  type Options = {\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | trimLeft.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a {@link ox#Bytes.Bytes} value into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toHex(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Options.\n * @returns Decoded {@link ox#Hex.Hex} value.\n */\nexport function toHex(value: Bytes, options: toHex.Options = {}): Hex.Hex {\n  return Hex.fromBytes(value, options)\n}\n\nexport declare namespace toHex {\n  type Options = {\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType = Hex.fromBytes.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a number.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toNumber(Bytes.from([1, 164]))\n * // @log: 420\n * ```\n */\nexport function toNumber(bytes: Bytes, options: toNumber.Options = {}): number {\n  const { size } = options\n  if (typeof size !== 'undefined') internal.assertSize(bytes, size)\n  const hex = Hex.fromBytes(bytes, options)\n  return Hex.toNumber(hex, options)\n}\n\nexport declare namespace toNumber {\n  type Options = {\n    /** Whether or not the number of a signed representation. */\n    signed?: boolean | undefined\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | Hex.fromBytes.ErrorType\n    | Hex.toNumber.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a string.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.toString(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: 'Hello world'\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Options.\n * @returns Decoded string.\n */\nexport function toString(bytes: Bytes, options: toString.Options = {}): string {\n  const { size } = options\n\n  let bytes_ = bytes\n  if (typeof size !== 'undefined') {\n    internal.assertSize(bytes_, size)\n    bytes_ = trimRight(bytes_)\n  }\n  return decoder.decode(bytes_)\n}\n\nexport declare namespace toString {\n  export type Options = {\n    /** Size of the bytes. */\n    size?: number | undefined\n  }\n\n  export type ErrorType =\n    | internal.assertSize.ErrorType\n    | trimRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Trims leading zeros from a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.trimLeft(Bytes.from([0, 0, 0, 0, 1, 2, 3]))\n * // @log: Uint8Array([1, 2, 3])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Trimmed {@link ox#Bytes.Bytes} value.\n */\nexport function trimLeft(value: Bytes): Bytes {\n  return internal.trim(value, { dir: 'left' })\n}\n\nexport declare namespace trimLeft {\n  type ErrorType = internal.trim.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Trims trailing zeros from a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.trimRight(Bytes.from([1, 2, 3, 0, 0, 0, 0]))\n * // @log: Uint8Array([1, 2, 3])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Trimmed {@link ox#Bytes.Bytes} value.\n */\nexport function trimRight(value: Bytes): Bytes {\n  return internal.trim(value, { dir: 'right' })\n}\n\nexport declare namespace trimRight {\n  export type ErrorType = internal.trim.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Checks if the given value is {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.validate('0x')\n * // @log: false\n *\n * Bytes.validate(Bytes.from([1, 2, 3]))\n * // @log: true\n * ```\n *\n * @param value - Value to check.\n * @returns `true` if the value is {@link ox#Bytes.Bytes}, otherwise `false`.\n */\nexport function validate(value: unknown): value is Bytes {\n  try {\n    assert(value)\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  export type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Thrown when the bytes value cannot be represented as a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toBoolean(Bytes.from([5]))\n * // @error: Bytes.InvalidBytesBooleanError: Bytes value `[5]` is not a valid boolean.\n * // @error: The bytes array must contain a single byte of either a `0` or `1` value.\n * ```\n */\nexport class InvalidBytesBooleanError extends Errors.BaseError {\n  override readonly name = 'Bytes.InvalidBytesBooleanError'\n\n  constructor(bytes: Bytes) {\n    super(`Bytes value \\`${bytes}\\` is not a valid boolean.`, {\n      metaMessages: [\n        'The bytes array must contain a single byte of either a `0` or `1` value.',\n      ],\n    })\n  }\n}\n\n/**\n * Thrown when a value cannot be converted to bytes.\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Bytes } from 'ox'\n *\n * Bytes.from('foo')\n * // @error: Bytes.InvalidBytesTypeError: Value `foo` of type `string` is an invalid Bytes value.\n * ```\n */\nexport class InvalidBytesTypeError extends Errors.BaseError {\n  override readonly name = 'Bytes.InvalidBytesTypeError'\n\n  constructor(value: unknown) {\n    super(\n      `Value \\`${typeof value === 'object' ? Json.stringify(value) : value}\\` of type \\`${typeof value}\\` is an invalid Bytes value.`,\n      {\n        metaMessages: ['Bytes values must be of type `Bytes`.'],\n      },\n    )\n  }\n}\n\n/**\n * Thrown when a size exceeds the maximum allowed size.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.fromString('Hello World!', { size: 8 })\n * // @error: Bytes.SizeOverflowError: Size cannot exceed `8` bytes. Given size: `12` bytes.\n * ```\n */\nexport class SizeOverflowError extends Errors.BaseError {\n  override readonly name = 'Bytes.SizeOverflowError'\n\n  constructor({ givenSize, maxSize }: { givenSize: number; maxSize: number }) {\n    super(\n      `Size cannot exceed \\`${maxSize}\\` bytes. Given size: \\`${givenSize}\\` bytes.`,\n    )\n  }\n}\n\n/**\n * Thrown when a slice offset is out-of-bounds.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.slice(Bytes.from([1, 2, 3]), 4)\n * // @error: Bytes.SliceOffsetOutOfBoundsError: Slice starting at offset `4` is out-of-bounds (size: `3`).\n * ```\n */\nexport class SliceOffsetOutOfBoundsError extends Errors.BaseError {\n  override readonly name = 'Bytes.SliceOffsetOutOfBoundsError'\n\n  constructor({\n    offset,\n    position,\n    size,\n  }: { offset: number; position: 'start' | 'end'; size: number }) {\n    super(\n      `Slice ${\n        position === 'start' ? 'starting' : 'ending'\n      } at offset \\`${offset}\\` is out-of-bounds (size: \\`${size}\\`).`,\n    )\n  }\n}\n\n/**\n * Thrown when a the padding size exceeds the maximum allowed size.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padLeft(Bytes.fromString('Hello World!'), 8)\n * // @error: [Bytes.SizeExceedsPaddingSizeError: Bytes size (`12`) exceeds padding size (`8`).\n * ```\n */\nexport class SizeExceedsPaddingSizeError extends Errors.BaseError {\n  override readonly name = 'Bytes.SizeExceedsPaddingSizeError'\n\n  constructor({\n    size,\n    targetSize,\n    type,\n  }: {\n    size: number\n    targetSize: number\n    type: 'Hex' | 'Bytes'\n  }) {\n    super(\n      `${type.charAt(0).toUpperCase()}${type\n        .slice(1)\n        .toLowerCase()} size (\\`${size}\\`) exceeds padding size (\\`${targetSize}\\`).`,\n    )\n  }\n}\n", "import { equalBytes } from '@noble/curves/abstract/utils'\nimport * as Bytes from './Bytes.js'\nimport * as Errors from './Errors.js'\nimport * as Json from './Json.js'\nimport * as internal_bytes from './internal/bytes.js'\nimport * as internal from './internal/hex.js'\n\nconst encoder = /*#__PURE__*/ new TextEncoder()\n\nconst hexes = /*#__PURE__*/ Array.from({ length: 256 }, (_v, i) =>\n  i.toString(16).padStart(2, '0'),\n)\n\n/** Root type for a Hex string. */\nexport type Hex = `0x${string}`\n\n/**\n * Asserts if the given value is {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert('abc')\n * // @error: InvalidHexValueTypeError:\n * // @error: Value `\"abc\"` of type `string` is an invalid hex type.\n * // @error: Hex types must be represented as `\"0x\\${string}\"`.\n * ```\n *\n * @param value - The value to assert.\n * @param options - Options.\n */\nexport function assert(\n  value: unknown,\n  options: assert.Options = {},\n): asserts value is Hex {\n  const { strict = false } = options\n  if (!value) throw new InvalidHexTypeError(value)\n  if (typeof value !== 'string') throw new InvalidHexTypeError(value)\n  if (strict) {\n    if (!/^0x[0-9a-fA-F]*$/.test(value)) throw new InvalidHexValueError(value)\n  }\n  if (!value.startsWith('0x')) throw new InvalidHexValueError(value)\n}\n\nexport declare namespace assert {\n  type Options = {\n    /** Checks if the {@link ox#Hex.Hex} value contains invalid hexadecimal characters. @default false */\n    strict?: boolean | undefined\n  }\n\n  type ErrorType =\n    | InvalidHexTypeError\n    | InvalidHexValueError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Concatenates two or more {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.concat('0x123', '0x456')\n * // @log: '0x123456'\n * ```\n *\n * @param values - The {@link ox#Hex.Hex} values to concatenate.\n * @returns The concatenated {@link ox#Hex.Hex} value.\n */\nexport function concat(...values: readonly Hex[]): Hex {\n  return `0x${(values as Hex[]).reduce((acc, x) => acc + x.replace('0x', ''), '')}`\n}\n\nexport declare namespace concat {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Instantiates a {@link ox#Hex.Hex} value from a hex string or {@link ox#Bytes.Bytes} value.\n *\n * :::tip\n *\n * To instantiate from a **Boolean**, **String**, or **Number**, use one of the following:\n *\n * - `Hex.fromBoolean`\n *\n * - `Hex.fromString`\n *\n * - `Hex.fromNumber`\n *\n * :::\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.from('0x48656c6c6f20576f726c6421')\n * // @log: '0x48656c6c6f20576f726c6421'\n *\n * Hex.from(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value to encode.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function from(value: Hex | Bytes.Bytes | readonly number[]): Hex {\n  if (value instanceof Uint8Array) return fromBytes(value)\n  if (Array.isArray(value)) return fromBytes(new Uint8Array(value))\n  return value as never\n}\n\nexport declare namespace from {\n  type Options = {\n    /** The size (in bytes) of the output hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType = fromBytes.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a boolean into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromBoolean(true)\n * // @log: '0x1'\n *\n * Hex.fromBoolean(false)\n * // @log: '0x0'\n *\n * Hex.fromBoolean(true, { size: 32 })\n * // @log: '0x0000000000000000000000000000000000000000000000000000000000000001'\n * ```\n *\n * @param value - The boolean value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function fromBoolean(\n  value: boolean,\n  options: fromBoolean.Options = {},\n): Hex {\n  const hex: Hex = `0x${Number(value)}`\n  if (typeof options.size === 'number') {\n    internal.assertSize(hex, options.size)\n    return padLeft(hex, options.size)\n  }\n  return hex\n}\n\nexport declare namespace fromBoolean {\n  type Options = {\n    /** The size (in bytes) of the output hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | padLeft.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a {@link ox#Bytes.Bytes} value into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.fromBytes(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function fromBytes(\n  value: Bytes.Bytes,\n  options: fromBytes.Options = {},\n): Hex {\n  let string = ''\n  for (let i = 0; i < value.length; i++) string += hexes[value[i]!]\n  const hex = `0x${string}` as const\n\n  if (typeof options.size === 'number') {\n    internal.assertSize(hex, options.size)\n    return padRight(hex, options.size)\n  }\n  return hex\n}\n\nexport declare namespace fromBytes {\n  type Options = {\n    /** The size (in bytes) of the output hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | padRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a number or bigint into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromNumber(420)\n * // @log: '0x1a4'\n *\n * Hex.fromNumber(420, { size: 32 })\n * // @log: '0x00000000000000000000000000000000000000000000000000000000000001a4'\n * ```\n *\n * @param value - The number or bigint value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function fromNumber(\n  value: number | bigint,\n  options: fromNumber.Options = {},\n): Hex {\n  const { signed, size } = options\n\n  const value_ = BigInt(value)\n\n  let maxValue: bigint | number | undefined\n  if (size) {\n    if (signed) maxValue = (1n << (BigInt(size) * 8n - 1n)) - 1n\n    else maxValue = 2n ** (BigInt(size) * 8n) - 1n\n  } else if (typeof value === 'number') {\n    maxValue = BigInt(Number.MAX_SAFE_INTEGER)\n  }\n\n  const minValue = typeof maxValue === 'bigint' && signed ? -maxValue - 1n : 0\n\n  if ((maxValue && value_ > maxValue) || value_ < minValue) {\n    const suffix = typeof value === 'bigint' ? 'n' : ''\n    throw new IntegerOutOfRangeError({\n      max: maxValue ? `${maxValue}${suffix}` : undefined,\n      min: `${minValue}${suffix}`,\n      signed,\n      size,\n      value: `${value}${suffix}`,\n    })\n  }\n\n  const stringValue = (\n    signed && value_ < 0 ? (1n << BigInt(size * 8)) + BigInt(value_) : value_\n  ).toString(16)\n\n  const hex = `0x${stringValue}` as Hex\n  if (size) return padLeft(hex, size) as Hex\n  return hex\n}\n\nexport declare namespace fromNumber {\n  type Options =\n    | {\n        /** Whether or not the number of a signed representation. */\n        signed?: boolean | undefined\n        /** The size (in bytes) of the output hex value. */\n        size: number\n      }\n    | {\n        signed?: undefined\n        /** The size (in bytes) of the output hex value. */\n        size?: number | undefined\n      }\n\n  type ErrorType =\n    | IntegerOutOfRangeError\n    | padLeft.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a string into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n * Hex.fromString('Hello World!')\n * // '0x48656c6c6f20576f726c6421'\n *\n * Hex.fromString('Hello World!', { size: 32 })\n * // '0x48656c6c6f20576f726c64210000000000000000000000000000000000000000'\n * ```\n *\n * @param value - The string value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nexport function fromString(\n  value: string,\n  options: fromString.Options = {},\n): Hex {\n  return fromBytes(encoder.encode(value), options)\n}\n\nexport declare namespace fromString {\n  type Options = {\n    /** The size (in bytes) of the output hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType = fromBytes.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Checks if two {@link ox#Hex.Hex} values are equal.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.isEqual('0xdeadbeef', '0xdeadbeef')\n * // @log: true\n *\n * Hex.isEqual('0xda', '0xba')\n * // @log: false\n * ```\n *\n * @param hexA - The first {@link ox#Hex.Hex} value.\n * @param hexB - The second {@link ox#Hex.Hex} value.\n * @returns `true` if the two {@link ox#Hex.Hex} values are equal, `false` otherwise.\n */\nexport function isEqual(hexA: Hex, hexB: Hex) {\n  return equalBytes(Bytes.fromHex(hexA), Bytes.fromHex(hexB))\n}\n\nexport declare namespace isEqual {\n  type ErrorType = Bytes.fromHex.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Pads a {@link ox#Hex.Hex} value to the left with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.padLeft('0x1234', 4)\n * // @log: '0x00001234'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to pad.\n * @param size - The size (in bytes) of the output hex value.\n * @returns The padded {@link ox#Hex.Hex} value.\n */\nexport function padLeft(\n  value: Hex,\n  size?: number | undefined,\n): padLeft.ReturnType {\n  return internal.pad(value, { dir: 'left', size })\n}\n\nexport declare namespace padLeft {\n  type ReturnType = Hex\n  type ErrorType = internal.pad.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Pads a {@link ox#Hex.Hex} value to the right with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts\n * import { Hex } from 'ox'\n *\n * Hex.padRight('0x1234', 4)\n * // @log: '0x12340000'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to pad.\n * @param size - The size (in bytes) of the output hex value.\n * @returns The padded {@link ox#Hex.Hex} value.\n */\nexport function padRight(\n  value: Hex,\n  size?: number | undefined,\n): padRight.ReturnType {\n  return internal.pad(value, { dir: 'right', size })\n}\n\nexport declare namespace padRight {\n  type ReturnType = Hex\n  type ErrorType = internal.pad.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Generates a random {@link ox#Hex.Hex} value of the specified length.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * const hex = Hex.random(32)\n * // @log: '0x...'\n * ```\n *\n * @returns Random {@link ox#Hex.Hex} value.\n */\nexport function random(length: number): Hex {\n  return fromBytes(Bytes.random(length))\n}\n\nexport declare namespace random {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Returns a section of a {@link ox#Bytes.Bytes} value given a start/end bytes offset.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.slice('0x0123456789', 1, 4)\n * // @log: '0x234567'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to slice.\n * @param start - The start offset (in bytes).\n * @param end - The end offset (in bytes).\n * @param options - Options.\n * @returns The sliced {@link ox#Hex.Hex} value.\n */\nexport function slice(\n  value: Hex,\n  start?: number | undefined,\n  end?: number | undefined,\n  options: slice.Options = {},\n): Hex {\n  const { strict } = options\n  internal.assertStartOffset(value, start)\n  const value_ = `0x${value\n    .replace('0x', '')\n    .slice((start ?? 0) * 2, (end ?? value.length) * 2)}` as const\n  if (strict) internal.assertEndOffset(value_, start, end)\n  return value_\n}\n\nexport declare namespace slice {\n  type Options = {\n    /** Asserts that the sliced value is the same size as the given start/end offsets. */\n    strict?: boolean | undefined\n  }\n\n  type ErrorType =\n    | internal.assertStartOffset.ErrorType\n    | internal.assertEndOffset.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Retrieves the size of a {@link ox#Hex.Hex} value (in bytes).\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.size('0xdeadbeef')\n * // @log: 4\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to get the size of.\n * @returns The size of the {@link ox#Hex.Hex} value (in bytes).\n */\nexport function size(value: Hex): number {\n  return Math.ceil((value.length - 2) / 2)\n}\n\nexport declare namespace size {\n  export type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Trims leading zeros from a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.trimLeft('0x00000000deadbeef')\n * // @log: '0xdeadbeef'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to trim.\n * @returns The trimmed {@link ox#Hex.Hex} value.\n */\nexport function trimLeft(value: Hex): trimLeft.ReturnType {\n  return internal.trim(value, { dir: 'left' })\n}\n\nexport declare namespace trimLeft {\n  type ReturnType = Hex\n\n  type ErrorType = internal.trim.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Trims trailing zeros from a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.trimRight('0xdeadbeef00000000')\n * // @log: '0xdeadbeef'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to trim.\n * @returns The trimmed {@link ox#Hex.Hex} value.\n */\nexport function trimRight(value: Hex): trimRight.ReturnType {\n  return internal.trim(value, { dir: 'right' })\n}\n\nexport declare namespace trimRight {\n  type ReturnType = Hex\n\n  type ErrorType = internal.trim.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a BigInt.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBigInt('0x1a4')\n * // @log: 420n\n *\n * Hex.toBigInt('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // @log: 420n\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded BigInt.\n */\nexport function toBigInt(hex: Hex, options: toBigInt.Options = {}): bigint {\n  const { signed } = options\n\n  if (options.size) internal.assertSize(hex, options.size)\n\n  const value = BigInt(hex)\n  if (!signed) return value\n\n  const size = (hex.length - 2) / 2\n\n  const max_unsigned = (1n << (BigInt(size) * 8n)) - 1n\n  const max_signed = max_unsigned >> 1n\n\n  if (value <= max_signed) return value\n  return value - max_unsigned - 1n\n}\n\nexport declare namespace toBigInt {\n  type Options = {\n    /** Whether or not the number of a signed representation. */\n    signed?: boolean | undefined\n    /** Size (in bytes) of the hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType = internal.assertSize.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBoolean('0x01')\n * // @log: true\n *\n * Hex.toBoolean('0x0000000000000000000000000000000000000000000000000000000000000001', { size: 32 })\n * // @log: true\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded boolean.\n */\nexport function toBoolean(hex: Hex, options: toBoolean.Options = {}): boolean {\n  if (options.size) internal.assertSize(hex, options.size)\n  const hex_ = trimLeft(hex)\n  if (hex_ === '0x') return false\n  if (hex_ === '0x1') return true\n  throw new InvalidHexBooleanError(hex)\n}\n\nexport declare namespace toBoolean {\n  type Options = {\n    /** Size (in bytes) of the hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal.assertSize.ErrorType\n    | trimLeft.ErrorType\n    | InvalidHexBooleanError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * const data = Hex.toBytes('0x48656c6c6f20776f726c6421')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded {@link ox#Bytes.Bytes}.\n */\nexport function toBytes(hex: Hex, options: toBytes.Options = {}): Bytes.Bytes {\n  return Bytes.fromHex(hex, options)\n}\n\nexport declare namespace toBytes {\n  type Options = {\n    /** Size (in bytes) of the hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType = Bytes.fromHex.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a number.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toNumber('0x1a4')\n * // @log: 420\n *\n * Hex.toNumber('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // @log: 420\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded number.\n */\nexport function toNumber(hex: Hex, options: toNumber.Options = {}): number {\n  const { signed, size } = options\n  if (!signed && !size) return Number(hex)\n  return Number(toBigInt(hex, options))\n}\n\nexport declare namespace toNumber {\n  type Options = toBigInt.Options\n\n  type ErrorType = toBigInt.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Decodes a {@link ox#Hex.Hex} value into a string.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toString('0x48656c6c6f20576f726c6421')\n * // @log: 'Hello world!'\n *\n * Hex.toString('0x48656c6c6f20576f726c64210000000000000000000000000000000000000000', {\n *  size: 32,\n * })\n * // @log: 'Hello world'\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded string.\n */\nexport function toString(hex: Hex, options: toString.Options = {}): string {\n  const { size } = options\n\n  let bytes = Bytes.fromHex(hex)\n  if (size) {\n    internal_bytes.assertSize(bytes, size)\n    bytes = Bytes.trimRight(bytes)\n  }\n  return new TextDecoder().decode(bytes)\n}\n\nexport declare namespace toString {\n  type Options = {\n    /** Size (in bytes) of the hex value. */\n    size?: number | undefined\n  }\n\n  type ErrorType =\n    | internal_bytes.assertSize.ErrorType\n    | Bytes.fromHex.ErrorType\n    | Bytes.trimRight.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Checks if the given value is {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.validate('0xdeadbeef')\n * // @log: true\n *\n * Hex.validate(Bytes.from([1, 2, 3]))\n * // @log: false\n * ```\n *\n * @param value - The value to check.\n * @param options - Options.\n * @returns `true` if the value is a {@link ox#Hex.Hex}, `false` otherwise.\n */\nexport function validate(\n  value: unknown,\n  options: validate.Options = {},\n): value is Hex {\n  const { strict = false } = options\n  try {\n    assert(value, { strict })\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  type Options = {\n    /** Checks if the {@link ox#Hex.Hex} value contains invalid hexadecimal characters. @default false */\n    strict?: boolean | undefined\n  }\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Thrown when the provided integer is out of range, and cannot be represented as a hex value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromNumber(420182738912731283712937129)\n * // @error: Hex.IntegerOutOfRangeError: Number \\`4.2018273891273126e+26\\` is not in safe unsigned integer range (`0` to `9007199254740991`)\n * ```\n */\nexport class IntegerOutOfRangeError extends Errors.BaseError {\n  override readonly name = 'Hex.IntegerOutOfRangeError'\n\n  constructor({\n    max,\n    min,\n    signed,\n    size,\n    value,\n  }: {\n    max?: string | undefined\n    min: string\n    signed?: boolean | undefined\n    size?: number | undefined\n    value: string\n  }) {\n    super(\n      `Number \\`${value}\\` is not in safe${\n        size ? ` ${size * 8}-bit` : ''\n      }${signed ? ' signed' : ' unsigned'} integer range ${max ? `(\\`${min}\\` to \\`${max}\\`)` : `(above \\`${min}\\`)`}`,\n    )\n  }\n}\n\n/**\n * Thrown when the provided hex value cannot be represented as a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBoolean('0xa')\n * // @error: Hex.InvalidHexBooleanError: Hex value `\"0xa\"` is not a valid boolean.\n * // @error: The hex value must be `\"0x0\"` (false) or `\"0x1\"` (true).\n * ```\n */\nexport class InvalidHexBooleanError extends Errors.BaseError {\n  override readonly name = 'Hex.InvalidHexBooleanError'\n\n  constructor(hex: Hex) {\n    super(`Hex value \\`\"${hex}\"\\` is not a valid boolean.`, {\n      metaMessages: [\n        'The hex value must be `\"0x0\"` (false) or `\"0x1\"` (true).',\n      ],\n    })\n  }\n}\n\n/**\n * Thrown when the provided value is not a valid hex type.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert(1)\n * // @error: Hex.InvalidHexTypeError: Value `1` of type `number` is an invalid hex type.\n * ```\n */\nexport class InvalidHexTypeError extends Errors.BaseError {\n  override readonly name = 'Hex.InvalidHexTypeError'\n\n  constructor(value: unknown) {\n    super(\n      `Value \\`${typeof value === 'object' ? Json.stringify(value) : value}\\` of type \\`${typeof value}\\` is an invalid hex type.`,\n      {\n        metaMessages: ['Hex types must be represented as `\"0x${string}\"`.'],\n      },\n    )\n  }\n}\n\n/**\n * Thrown when the provided hex value is invalid.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert('0x0123456789abcdefg')\n * // @error: Hex.InvalidHexValueError: Value `0x0123456789abcdefg` is an invalid hex value.\n * // @error: Hex values must start with `\"0x\"` and contain only hexadecimal characters (0-9, a-f, A-F).\n * ```\n */\nexport class InvalidHexValueError extends Errors.BaseError {\n  override readonly name = 'Hex.InvalidHexValueError'\n\n  constructor(value: unknown) {\n    super(`Value \\`${value}\\` is an invalid hex value.`, {\n      metaMessages: [\n        'Hex values must start with `\"0x\"` and contain only hexadecimal characters (0-9, a-f, A-F).',\n      ],\n    })\n  }\n}\n\n/**\n * Thrown when the provided hex value is an odd length.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.fromHex('0xabcde')\n * // @error: Hex.InvalidLengthError: Hex value `\"0xabcde\"` is an odd length (5 nibbles).\n * ```\n */\nexport class InvalidLengthError extends Errors.BaseError {\n  override readonly name = 'Hex.InvalidLengthError'\n\n  constructor(value: Hex) {\n    super(\n      `Hex value \\`\"${value}\"\\` is an odd length (${value.length - 2} nibbles).`,\n      {\n        metaMessages: ['It must be an even length.'],\n      },\n    )\n  }\n}\n\n/**\n * Thrown when the size of the value exceeds the expected max size.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromString('Hello World!', { size: 8 })\n * // @error: Hex.SizeOverflowError: Size cannot exceed `8` bytes. Given size: `12` bytes.\n * ```\n */\nexport class SizeOverflowError extends Errors.BaseError {\n  override readonly name = 'Hex.SizeOverflowError'\n\n  constructor({ givenSize, maxSize }: { givenSize: number; maxSize: number }) {\n    super(\n      `Size cannot exceed \\`${maxSize}\\` bytes. Given size: \\`${givenSize}\\` bytes.`,\n    )\n  }\n}\n\n/**\n * Thrown when the slice offset exceeds the bounds of the value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.slice('0x0123456789', 6)\n * // @error: Hex.SliceOffsetOutOfBoundsError: Slice starting at offset `6` is out-of-bounds (size: `5`).\n * ```\n */\nexport class SliceOffsetOutOfBoundsError extends Errors.BaseError {\n  override readonly name = 'Hex.SliceOffsetOutOfBoundsError'\n\n  constructor({\n    offset,\n    position,\n    size,\n  }: { offset: number; position: 'start' | 'end'; size: number }) {\n    super(\n      `Slice ${\n        position === 'start' ? 'starting' : 'ending'\n      } at offset \\`${offset}\\` is out-of-bounds (size: \\`${size}\\`).`,\n    )\n  }\n}\n\n/**\n * Thrown when the size of the value exceeds the pad size.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.padLeft('0x1a4e12a45a21323123aaa87a897a897a898a6567a578a867a98778a667a85a875a87a6a787a65a675a6a9', 32)\n * // @error: Hex.SizeExceedsPaddingSizeError: Hex size (`43`) exceeds padding size (`32`).\n * ```\n */\nexport class SizeExceedsPaddingSizeError extends Errors.BaseError {\n  override readonly name = 'Hex.SizeExceedsPaddingSizeError'\n\n  constructor({\n    size,\n    targetSize,\n    type,\n  }: {\n    size: number\n    targetSize: number\n    type: 'Hex' | 'Bytes'\n  }) {\n    super(\n      `${type.charAt(0).toUpperCase()}${type\n        .slice(1)\n        .toLowerCase()} size (\\`${size}\\`) exceeds padding size (\\`${targetSize}\\`).`,\n    )\n  }\n}\n", "import { ripemd160 as noble_ripemd160 } from '@noble/hashes/ripemd160'\nimport { keccak_256 as noble_keccak256 } from '@noble/hashes/sha3'\nimport { sha256 as noble_sha256 } from '@noble/hashes/sha256'\nimport * as Bytes from './Bytes.js'\nimport type * as Errors from './Errors.js'\nimport * as Hex from './Hex.js'\n\n/**\n * Calculates the [Keccak256](https://en.wikipedia.org/wiki/SHA-3) hash of a {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n *\n * This function is a re-export of `keccak_256` from [`@noble/hashes`](https://github.com/paulmillr/noble-hashes), an audited & minimal JS hashing library.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.keccak256('0xdeadbeef')\n * // @log: '0xd4fd4e189132273036449fc9e11198c739161b4c0116a9a2dccdfa1c492006f1'\n * ```\n *\n * @example\n * ### Calculate Hash of a String\n *\n * ```ts twoslash\n * import { Hash, Hex } from 'ox'\n *\n * Hash.keccak256(Hex.fromString('hello world'))\n * // @log: '0x3ea2f1d0abf3fc66cf29eebb70cbd4e7fe762ef8a09bcc06c8edf641230afec0'\n * ```\n *\n * @example\n * ### Configure Return Type\n *\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.keccak256('0xdeadbeef', { as: 'Bytes' })\n * // @log: Uint8Array [...]\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n * @param options - Options.\n * @returns Keccak256 hash.\n */\nexport function keccak256<\n  value extends Hex.Hex | Bytes.Bytes,\n  as extends 'Hex' | 'Bytes' =\n    | (value extends Hex.Hex ? 'Hex' : never)\n    | (value extends Bytes.Bytes ? 'Bytes' : never),\n>(\n  value: value | Hex.Hex | Bytes.Bytes,\n  options: keccak256.Options<as> = {},\n): keccak256.ReturnType<as> {\n  const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options\n  const bytes = noble_keccak256(Bytes.from(value))\n  if (as === 'Bytes') return bytes as never\n  return Hex.fromBytes(bytes) as never\n}\n\nexport declare namespace keccak256 {\n  type Options<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> = {\n    /** The return type. @default 'Hex' */\n    as?: as | 'Hex' | 'Bytes' | undefined\n  }\n\n  type ReturnType<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> =\n    | (as extends 'Bytes' ? Bytes.Bytes : never)\n    | (as extends 'Hex' ? Hex.Hex : never)\n\n  type ErrorType =\n    | Bytes.from.ErrorType\n    | Hex.fromBytes.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Calculates the [Ripemd160](https://en.wikipedia.org/wiki/RIPEMD) hash of a {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n *\n * This function is a re-export of `ripemd160` from [`@noble/hashes`](https://github.com/paulmillr/noble-hashes), an audited & minimal JS hashing library.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.ripemd160('0xdeadbeef')\n * // '0x226821c2f5423e11fe9af68bd285c249db2e4b5a'\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n * @param options - Options.\n * @returns Ripemd160 hash.\n */\nexport function ripemd160<\n  value extends Hex.Hex | Bytes.Bytes,\n  as extends 'Hex' | 'Bytes' =\n    | (value extends Hex.Hex ? 'Hex' : never)\n    | (value extends Bytes.Bytes ? 'Bytes' : never),\n>(\n  value: value | Hex.Hex | Bytes.Bytes,\n  options: ripemd160.Options<as> = {},\n): ripemd160.ReturnType<as> {\n  const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options\n  const bytes = noble_ripemd160(Bytes.from(value))\n  if (as === 'Bytes') return bytes as never\n  return Hex.fromBytes(bytes) as never\n}\n\nexport declare namespace ripemd160 {\n  type Options<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> = {\n    /** The return type. @default 'Hex' */\n    as?: as | 'Hex' | 'Bytes' | undefined\n  }\n\n  type ReturnType<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> =\n    | (as extends 'Bytes' ? Bytes.Bytes : never)\n    | (as extends 'Hex' ? Hex.Hex : never)\n\n  type ErrorType =\n    | Bytes.from.ErrorType\n    | Hex.fromBytes.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Calculates the [Sha256](https://en.wikipedia.org/wiki/SHA-256) hash of a {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n *\n * This function is a re-export of `sha256` from [`@noble/hashes`](https://github.com/paulmillr/noble-hashes), an audited & minimal JS hashing library.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.sha256('0xdeadbeef')\n * // '0x5f78c33274e43fa9de5659265c1d917e25c03722dcb0b8d27db8d5feaa813953'\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n * @param options - Options.\n * @returns Sha256 hash.\n */\nexport function sha256<\n  value extends Hex.Hex | Bytes.Bytes,\n  as extends 'Hex' | 'Bytes' =\n    | (value extends Hex.Hex ? 'Hex' : never)\n    | (value extends Bytes.Bytes ? 'Bytes' : never),\n>(\n  value: value | Hex.Hex | Bytes.Bytes,\n  options: sha256.Options<as> = {},\n): sha256.ReturnType<as> {\n  const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options\n  const bytes = noble_sha256(Bytes.from(value))\n  if (as === 'Bytes') return bytes as never\n  return Hex.fromBytes(bytes) as never\n}\n\nexport declare namespace sha256 {\n  type Options<as extends 'Hex' | 'Bytes' = 'Hex'> = {\n    /** The return type. @default 'Hex' */\n    as?: as | 'Hex' | 'Bytes' | undefined\n  }\n\n  type ReturnType<as extends 'Hex' | 'Bytes' = 'Hex'> =\n    | (as extends 'Bytes' ? Bytes.Bytes : never)\n    | (as extends 'Hex' ? Hex.Hex : never)\n\n  type ErrorType =\n    | Bytes.from.ErrorType\n    | Hex.fromBytes.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Checks if a string is a valid hash value.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.validate('0x')\n * // @log: false\n *\n * Hash.validate('0x3ea2f1d0abf3fc66cf29eebb70cbd4e7fe762ef8a09bcc06c8edf641230afec0')\n * // @log: true\n * ```\n *\n * @param value - Value to check.\n * @returns Whether the value is a valid hash.\n */\nexport function validate(value: string): value is Hex.Hex {\n  return Hex.validate(value) && Hex.size(value) === 32\n}\n\nexport declare namespace validate {\n  type ErrorType =\n    | Hex.validate.ErrorType\n    | Hex.size.ErrorType\n    | Errors.GlobalErrorType\n}\n", "/**\n * @internal\n *\n * Map with a LRU (Least recently used) policy.\n * @see https://en.wikipedia.org/wiki/Cache_replacement_policies#LRU\n */\nexport class LruMap<value = unknown> extends Map<string, value> {\n  maxSize: number\n\n  constructor(size: number) {\n    super()\n    this.maxSize = size\n  }\n\n  override get(key: string) {\n    const value = super.get(key)\n\n    if (super.has(key) && value !== undefined) {\n      this.delete(key)\n      super.set(key, value)\n    }\n\n    return value\n  }\n\n  override set(key: string, value: value) {\n    super.set(key, value)\n    if (this.maxSize && this.size > this.maxSize) {\n      const firstKey = this.keys().next().value\n      if (firstKey) this.delete(firstKey)\n    }\n    return this\n  }\n}\n", "import type * as Address from './Address.js'\nimport { LruMap } from './internal/lru.js'\n\nconst caches = {\n  checksum: /*#__PURE__*/ new LruMap<Address.Address>(8192),\n}\n\nexport const checksum = caches.checksum\n\n/**\n * Clears all global caches.\n *\n * @example\n * ```ts\n * import { Caches } from 'ox'\n * Caches.clear()\n * ```\n */\nexport function clear() {\n  for (const cache of Object.values(caches)) cache.clear()\n}\n", "import type { Address as abitype_Address } from 'abitype'\nimport * as Bytes from './Bytes.js'\nimport * as Caches from './Caches.js'\nimport * as Errors from './Errors.js'\nimport * as Hash from './Hash.js'\nimport * as <PERSON>Key from './PublicKey.js'\n\nconst addressRegex = /*#__PURE__*/ /^0x[a-fA-F0-9]{40}$/\n\n/** Root type for Address. */\nexport type Address = abitype_Address\n\n/**\n * Asserts that the given value is a valid {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.assert('******************************************')\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.assert('0xdeadbeef')\n * // @error: InvalidAddressError: Address \"0xdeadbeef\" is invalid.\n * ```\n *\n * @param value - Value to assert if it is a valid address.\n * @param options - Assertion options.\n */\nexport function assert(\n  value: string,\n  options: assert.Options = {},\n): asserts value is Address {\n  const { strict = true } = options\n\n  if (!addressRegex.test(value))\n    throw new InvalidAddressError({\n      address: value,\n      cause: new InvalidInputError(),\n    })\n\n  if (strict) {\n    if (value.toLowerCase() === value) return\n    if (checksum(value as Address) !== value)\n      throw new InvalidAddressError({\n        address: value,\n        cause: new InvalidChecksumError(),\n      })\n  }\n}\n\nexport declare namespace assert {\n  type Options = {\n    /**\n     * Enables strict mode. Whether or not to compare the address against its checksum.\n     *\n     * @default true\n     */\n    strict?: boolean | undefined\n  }\n\n  type ErrorType = InvalidAddressError | Errors.GlobalErrorType\n}\n\n/**\n * Computes the checksum address for the given {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.checksum('******************************************')\n * // @log: '******************************************'\n * ```\n *\n * @param address - The address to compute the checksum for.\n * @returns The checksummed address.\n */\nexport function checksum(address: string): Address {\n  if (Caches.checksum.has(address)) return Caches.checksum.get(address)!\n\n  assert(address, { strict: false })\n\n  const hexAddress = address.substring(2).toLowerCase()\n  const hash = Hash.keccak256(Bytes.fromString(hexAddress), { as: 'Bytes' })\n\n  const characters = hexAddress.split('')\n  for (let i = 0; i < 40; i += 2) {\n    if (hash[i >> 1]! >> 4 >= 8 && characters[i]) {\n      characters[i] = characters[i]!.toUpperCase()\n    }\n    if ((hash[i >> 1]! & 0x0f) >= 8 && characters[i + 1]) {\n      characters[i + 1] = characters[i + 1]!.toUpperCase()\n    }\n  }\n\n  const result = `0x${characters.join('')}` as const\n  Caches.checksum.set(address, result)\n  return result\n}\n\nexport declare namespace checksum {\n  type ErrorType =\n    | assert.ErrorType\n    | Hash.keccak256.ErrorType\n    | Bytes.fromString.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts a stringified address to a typed (checksummed) {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('******************************************')\n * // @log: '******************************************'\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('******************************************', {\n *   checksum: false\n * })\n * // @log: '******************************************'\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('hello')\n * // @error: InvalidAddressError: Address \"0xa\" is invalid.\n * ```\n *\n * @param address - An address string to convert to a typed Address.\n * @param options - Conversion options.\n * @returns The typed Address.\n */\nexport function from(address: string, options: from.Options = {}): Address {\n  const { checksum: checksumVal = false } = options\n  assert(address)\n  if (checksumVal) return checksum(address)\n  return address as Address\n}\n\nexport declare namespace from {\n  type Options = {\n    /**\n     * Whether to checksum the address.\n     *\n     * @default false\n     */\n    checksum?: boolean | undefined\n  }\n\n  type ErrorType =\n    | assert.ErrorType\n    | checksum.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts an ECDSA public key to an {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address, PublicKey } from 'ox'\n *\n * const publicKey = PublicKey.from(\n *   '0x048318535b54105d4a7aae60c08fc45f9687181b4fdfc625bd1a753fa7397fed753547f11ca8696646f2f3acb08e31016afac23e630c5d11f59f61fef57b0d2aa5',\n * )\n * const address = Address.fromPublicKey(publicKey)\n * // @log: '******************************************'\n * ```\n *\n * @param publicKey - The ECDSA public key to convert to an {@link ox#Address.Address}.\n * @param options - Conversion options.\n * @returns The {@link ox#Address.Address} corresponding to the public key.\n */\nexport function fromPublicKey(\n  publicKey: PublicKey.PublicKey,\n  options: fromPublicKey.Options = {},\n): Address {\n  const address = Hash.keccak256(\n    `0x${PublicKey.toHex(publicKey).slice(4)}`,\n  ).substring(26)\n  return from(`0x${address}`, options)\n}\n\nexport declare namespace fromPublicKey {\n  type Options = {\n    /**\n     * Whether to checksum the address.\n     *\n     * @default false\n     */\n    checksum?: boolean | undefined\n  }\n\n  type ErrorType =\n    | Hash.keccak256.ErrorType\n    | PublicKey.toHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Checks if two {@link ox#Address.Address} are equal.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.isEqual(\n *   '******************************************',\n *   '******************************************'\n * )\n * // @log: true\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.isEqual(\n *   '******************************************',\n *   '******************************************'\n * )\n * // @log: false\n * ```\n *\n * @param addressA - The first address to compare.\n * @param addressB - The second address to compare.\n * @returns Whether the addresses are equal.\n */\nexport function isEqual(addressA: Address, addressB: Address): boolean {\n  assert(addressA, { strict: false })\n  assert(addressB, { strict: false })\n  return addressA.toLowerCase() === addressB.toLowerCase()\n}\n\nexport declare namespace isEqual {\n  type ErrorType = assert.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Checks if the given address is a valid {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.validate('******************************************')\n * // @log: true\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.validate('0xdeadbeef')\n * // @log: false\n * ```\n *\n * @param address - Value to check if it is a valid address.\n * @param options - Check options.\n * @returns Whether the address is a valid address.\n */\nexport function validate(\n  address: string,\n  options: validate.Options = {},\n): address is Address {\n  const { strict = true } = options ?? {}\n  try {\n    assert(address, { strict })\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  type Options = {\n    /**\n     * Enables strict mode. Whether or not to compare the address against its checksum.\n     *\n     * @default true\n     */\n    strict?: boolean | undefined\n  }\n}\n\n/**\n * Thrown when an address is invalid.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('0x123')\n * // @error: Address.InvalidAddressError: Address `0x123` is invalid.\n * ```\n */\nexport class InvalidAddressError<\n  cause extends InvalidInputError | InvalidChecksumError =\n    | InvalidInputError\n    | InvalidChecksumError,\n> extends Errors.BaseError<cause> {\n  override readonly name = 'Address.InvalidAddressError'\n\n  constructor({ address, cause }: { address: string; cause: cause }) {\n    super(`Address \"${address}\" is invalid.`, {\n      cause,\n    })\n  }\n}\n\n/** Thrown when an address is not a 20 byte (40 hexadecimal character) value. */\nexport class InvalidInputError extends Errors.BaseError {\n  override readonly name = 'Address.InvalidInputError'\n\n  constructor() {\n    super('Address is not a 20 byte (40 hexadecimal character) value.')\n  }\n}\n\n/** Thrown when an address does not match its checksum counterpart. */\nexport class InvalidChecksumError extends Errors.BaseError {\n  override readonly name = 'Address.InvalidChecksumError'\n\n  constructor() {\n    super('Address does not match its checksum counterpart.')\n  }\n}\n", "export const arrayRegex = /^(.*)\\[([0-9]*)\\]$/\n\n// `bytes<M>`: binary type of `M` bytes, `0 < M <= 32`\n// https://regexr.com/6va55\nexport const bytesRegex = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/\n\n// `(u)int<M>`: (un)signed integer type of `M` bits, `0 < M <= 256`, `M % 8 == 0`\n// https://regexr.com/6v8hp\nexport const integerRegex =\n  /^(u?int)(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/\n\nexport const maxInt8 = 2n ** (8n - 1n) - 1n\nexport const maxInt16 = 2n ** (16n - 1n) - 1n\nexport const maxInt24 = 2n ** (24n - 1n) - 1n\nexport const maxInt32 = 2n ** (32n - 1n) - 1n\nexport const maxInt40 = 2n ** (40n - 1n) - 1n\nexport const maxInt48 = 2n ** (48n - 1n) - 1n\nexport const maxInt56 = 2n ** (56n - 1n) - 1n\nexport const maxInt64 = 2n ** (64n - 1n) - 1n\nexport const maxInt72 = 2n ** (72n - 1n) - 1n\nexport const maxInt80 = 2n ** (80n - 1n) - 1n\nexport const maxInt88 = 2n ** (88n - 1n) - 1n\nexport const maxInt96 = 2n ** (96n - 1n) - 1n\nexport const maxInt104 = 2n ** (104n - 1n) - 1n\nexport const maxInt112 = 2n ** (112n - 1n) - 1n\nexport const maxInt120 = 2n ** (120n - 1n) - 1n\nexport const maxInt128 = 2n ** (128n - 1n) - 1n\nexport const maxInt136 = 2n ** (136n - 1n) - 1n\nexport const maxInt144 = 2n ** (144n - 1n) - 1n\nexport const maxInt152 = 2n ** (152n - 1n) - 1n\nexport const maxInt160 = 2n ** (160n - 1n) - 1n\nexport const maxInt168 = 2n ** (168n - 1n) - 1n\nexport const maxInt176 = 2n ** (176n - 1n) - 1n\nexport const maxInt184 = 2n ** (184n - 1n) - 1n\nexport const maxInt192 = 2n ** (192n - 1n) - 1n\nexport const maxInt200 = 2n ** (200n - 1n) - 1n\nexport const maxInt208 = 2n ** (208n - 1n) - 1n\nexport const maxInt216 = 2n ** (216n - 1n) - 1n\nexport const maxInt224 = 2n ** (224n - 1n) - 1n\nexport const maxInt232 = 2n ** (232n - 1n) - 1n\nexport const maxInt240 = 2n ** (240n - 1n) - 1n\nexport const maxInt248 = 2n ** (248n - 1n) - 1n\nexport const maxInt256 = 2n ** (256n - 1n) - 1n\n\nexport const minInt8 = -(2n ** (8n - 1n))\nexport const minInt16 = -(2n ** (16n - 1n))\nexport const minInt24 = -(2n ** (24n - 1n))\nexport const minInt32 = -(2n ** (32n - 1n))\nexport const minInt40 = -(2n ** (40n - 1n))\nexport const minInt48 = -(2n ** (48n - 1n))\nexport const minInt56 = -(2n ** (56n - 1n))\nexport const minInt64 = -(2n ** (64n - 1n))\nexport const minInt72 = -(2n ** (72n - 1n))\nexport const minInt80 = -(2n ** (80n - 1n))\nexport const minInt88 = -(2n ** (88n - 1n))\nexport const minInt96 = -(2n ** (96n - 1n))\nexport const minInt104 = -(2n ** (104n - 1n))\nexport const minInt112 = -(2n ** (112n - 1n))\nexport const minInt120 = -(2n ** (120n - 1n))\nexport const minInt128 = -(2n ** (128n - 1n))\nexport const minInt136 = -(2n ** (136n - 1n))\nexport const minInt144 = -(2n ** (144n - 1n))\nexport const minInt152 = -(2n ** (152n - 1n))\nexport const minInt160 = -(2n ** (160n - 1n))\nexport const minInt168 = -(2n ** (168n - 1n))\nexport const minInt176 = -(2n ** (176n - 1n))\nexport const minInt184 = -(2n ** (184n - 1n))\nexport const minInt192 = -(2n ** (192n - 1n))\nexport const minInt200 = -(2n ** (200n - 1n))\nexport const minInt208 = -(2n ** (208n - 1n))\nexport const minInt216 = -(2n ** (216n - 1n))\nexport const minInt224 = -(2n ** (224n - 1n))\nexport const minInt232 = -(2n ** (232n - 1n))\nexport const minInt240 = -(2n ** (240n - 1n))\nexport const minInt248 = -(2n ** (248n - 1n))\nexport const minInt256 = -(2n ** (256n - 1n))\n\nexport const maxUint8 = 2n ** 8n - 1n\nexport const maxUint16 = 2n ** 16n - 1n\nexport const maxUint24 = 2n ** 24n - 1n\nexport const maxUint32 = 2n ** 32n - 1n\nexport const maxUint40 = 2n ** 40n - 1n\nexport const maxUint48 = 2n ** 48n - 1n\nexport const maxUint56 = 2n ** 56n - 1n\nexport const maxUint64 = 2n ** 64n - 1n\nexport const maxUint72 = 2n ** 72n - 1n\nexport const maxUint80 = 2n ** 80n - 1n\nexport const maxUint88 = 2n ** 88n - 1n\nexport const maxUint96 = 2n ** 96n - 1n\nexport const maxUint104 = 2n ** 104n - 1n\nexport const maxUint112 = 2n ** 112n - 1n\nexport const maxUint120 = 2n ** 120n - 1n\nexport const maxUint128 = 2n ** 128n - 1n\nexport const maxUint136 = 2n ** 136n - 1n\nexport const maxUint144 = 2n ** 144n - 1n\nexport const maxUint152 = 2n ** 152n - 1n\nexport const maxUint160 = 2n ** 160n - 1n\nexport const maxUint168 = 2n ** 168n - 1n\nexport const maxUint176 = 2n ** 176n - 1n\nexport const maxUint184 = 2n ** 184n - 1n\nexport const maxUint192 = 2n ** 192n - 1n\nexport const maxUint200 = 2n ** 200n - 1n\nexport const maxUint208 = 2n ** 208n - 1n\nexport const maxUint216 = 2n ** 216n - 1n\nexport const maxUint224 = 2n ** 224n - 1n\nexport const maxUint232 = 2n ** 232n - 1n\nexport const maxUint240 = 2n ** 240n - 1n\nexport const maxUint248 = 2n ** 248n - 1n\nexport const maxUint256 = 2n ** 256n - 1n\n", "import type { Bytes } from '../Bytes.js'\nimport * as Errors from '../Errors.js'\n\n/** @internal */\nexport type Cursor = {\n  bytes: Bytes\n  dataView: DataView\n  position: number\n  positionReadCount: Map<number, number>\n  recursiveReadCount: number\n  recursiveReadLimit: number\n  remaining: number\n  assertReadLimit(position?: number): void\n  assertPosition(position: number): void\n  decrementPosition(offset: number): void\n  getReadCount(position?: number): number\n  incrementPosition(offset: number): void\n  inspectByte(position?: number): Bytes[number]\n  inspectBytes(length: number, position?: number): Bytes\n  inspectUint8(position?: number): number\n  inspectUint16(position?: number): number\n  inspectUint24(position?: number): number\n  inspectUint32(position?: number): number\n  pushByte(byte: Bytes[number]): void\n  pushBytes(bytes: Bytes): void\n  pushUint8(value: number): void\n  pushUint16(value: number): void\n  pushUint24(value: number): void\n  pushUint32(value: number): void\n  readByte(): Bytes[number]\n  readBytes(length: number, size?: number): Bytes\n  readUint8(): number\n  readUint16(): number\n  readUint24(): number\n  readUint32(): number\n  setPosition(position: number): () => void\n  _touch(): void\n}\n\nconst staticCursor: Cursor = /*#__PURE__*/ {\n  bytes: new Uint8Array(),\n  dataView: new DataView(new ArrayBuffer(0)),\n  position: 0,\n  positionReadCount: new Map(),\n  recursiveReadCount: 0,\n  recursiveReadLimit: Number.POSITIVE_INFINITY,\n  assertReadLimit() {\n    if (this.recursiveReadCount >= this.recursiveReadLimit)\n      throw new RecursiveReadLimitExceededError({\n        count: this.recursiveReadCount + 1,\n        limit: this.recursiveReadLimit,\n      })\n  },\n  assertPosition(position) {\n    if (position < 0 || position > this.bytes.length - 1)\n      throw new PositionOutOfBoundsError({\n        length: this.bytes.length,\n        position,\n      })\n  },\n  decrementPosition(offset) {\n    if (offset < 0) throw new NegativeOffsetError({ offset })\n    const position = this.position - offset\n    this.assertPosition(position)\n    this.position = position\n  },\n  getReadCount(position) {\n    return this.positionReadCount.get(position || this.position) || 0\n  },\n  incrementPosition(offset) {\n    if (offset < 0) throw new NegativeOffsetError({ offset })\n    const position = this.position + offset\n    this.assertPosition(position)\n    this.position = position\n  },\n  inspectByte(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position)\n    return this.bytes[position]!\n  },\n  inspectBytes(length, position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + length - 1)\n    return this.bytes.subarray(position, position + length)\n  },\n  inspectUint8(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position)\n    return this.bytes[position]!\n  },\n  inspectUint16(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 1)\n    return this.dataView.getUint16(position)\n  },\n  inspectUint24(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 2)\n    return (\n      (this.dataView.getUint16(position) << 8) +\n      this.dataView.getUint8(position + 2)\n    )\n  },\n  inspectUint32(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 3)\n    return this.dataView.getUint32(position)\n  },\n  pushByte(byte: Bytes[number]) {\n    this.assertPosition(this.position)\n    this.bytes[this.position] = byte\n    this.position++\n  },\n  pushBytes(bytes: Bytes) {\n    this.assertPosition(this.position + bytes.length - 1)\n    this.bytes.set(bytes, this.position)\n    this.position += bytes.length\n  },\n  pushUint8(value: number) {\n    this.assertPosition(this.position)\n    this.bytes[this.position] = value\n    this.position++\n  },\n  pushUint16(value: number) {\n    this.assertPosition(this.position + 1)\n    this.dataView.setUint16(this.position, value)\n    this.position += 2\n  },\n  pushUint24(value: number) {\n    this.assertPosition(this.position + 2)\n    this.dataView.setUint16(this.position, value >> 8)\n    this.dataView.setUint8(this.position + 2, value & ~4294967040)\n    this.position += 3\n  },\n  pushUint32(value: number) {\n    this.assertPosition(this.position + 3)\n    this.dataView.setUint32(this.position, value)\n    this.position += 4\n  },\n  readByte() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectByte()\n    this.position++\n    return value\n  },\n  readBytes(length, size) {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectBytes(length)\n    this.position += size ?? length\n    return value\n  },\n  readUint8() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint8()\n    this.position += 1\n    return value\n  },\n  readUint16() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint16()\n    this.position += 2\n    return value\n  },\n  readUint24() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint24()\n    this.position += 3\n    return value\n  },\n  readUint32() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint32()\n    this.position += 4\n    return value\n  },\n  get remaining() {\n    return this.bytes.length - this.position\n  },\n  setPosition(position) {\n    const oldPosition = this.position\n    this.assertPosition(position)\n    this.position = position\n    return () => (this.position = oldPosition)\n  },\n  _touch() {\n    if (this.recursiveReadLimit === Number.POSITIVE_INFINITY) return\n    const count = this.getReadCount()\n    this.positionReadCount.set(this.position, count + 1)\n    if (count > 0) this.recursiveReadCount++\n  },\n}\n\n/** @internal */\nexport function create(\n  bytes: Bytes,\n  { recursiveReadLimit = 8_192 }: create.Config = {},\n): Cursor {\n  const cursor: Cursor = Object.create(staticCursor)\n  cursor.bytes = bytes\n  cursor.dataView = new DataView(\n    bytes.buffer,\n    bytes.byteOffset,\n    bytes.byteLength,\n  )\n  cursor.positionReadCount = new Map()\n  cursor.recursiveReadLimit = recursiveReadLimit\n  return cursor\n}\n\n/** @internal */\nexport declare namespace create {\n  type Config = { recursiveReadLimit?: number | undefined }\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/** @internal */\nexport class NegativeOffsetError extends Errors.BaseError {\n  override readonly name = 'Cursor.NegativeOffsetError'\n\n  constructor({ offset }: { offset: number }) {\n    super(`Offset \\`${offset}\\` cannot be negative.`)\n  }\n}\n\n/** @internal */\nexport class PositionOutOfBoundsError extends Errors.BaseError {\n  override readonly name = 'Cursor.PositionOutOfBoundsError'\n\n  constructor({ length, position }: { length: number; position: number }) {\n    super(\n      `Position \\`${position}\\` is out of bounds (\\`0 < position < ${length}\\`).`,\n    )\n  }\n}\n\n/** @internal */\nexport class RecursiveReadLimitExceededError extends Errors.BaseError {\n  override readonly name = 'Cursor.RecursiveReadLimitExceededError'\n\n  constructor({ count, limit }: { count: number; limit: number }) {\n    super(\n      `Recursive read limit of \\`${limit}\\` exceeded (recursive read count: \\`${count}\\`).`,\n    )\n  }\n}\n", "import * as abitype from 'abitype'\nimport * as Address from './Address.js'\nimport * as Bytes from './Bytes.js'\nimport * as Errors from './Errors.js'\nimport * as Hex from './Hex.js'\nimport * as Solidity from './Solidity.js'\nimport * as internal from './internal/abiParameters.js'\nimport * as Cursor from './internal/cursor.js'\n\n/** Root type for ABI parameters. */\nexport type AbiParameters = readonly abitype.AbiParameter[]\n\n/** A parameter on an {@link ox#AbiParameters.AbiParameters}. */\nexport type Parameter = abitype.AbiParameter\n\n/** A packed ABI type. */\nexport type PackedAbiType =\n  | abitype.SolidityAddress\n  | abitype.SolidityBool\n  | abitype.SolidityBytes\n  | abitype.SolidityInt\n  | abitype.SolidityString\n  | abitype.SolidityArrayWithoutTuple\n\n/**\n * Decodes ABI-encoded data into its respective primitive values based on ABI Parameters.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.decode(\n *   AbiParameters.from(['string', 'uint', 'bool']),\n *   '0x000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000001a4000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000057761676d69000000000000000000000000000000000000000000000000000000',\n * )\n * // @log: ['wagmi', 420n, true]\n * ```\n *\n * @example\n * ### JSON Parameters\n *\n * You can pass **JSON ABI** Parameters:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.decode(\n *   [\n *     { name: 'x', type: 'string' },\n *     { name: 'y', type: 'uint' },\n *     { name: 'z', type: 'bool' },\n *   ],\n *   '0x000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000001a4000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000057761676d69000000000000000000000000000000000000000000000000000000',\n * )\n * // @log: ['wagmi', 420n, true]\n * ```\n *\n * @param parameters - The set of ABI parameters to decode, in the shape of the `inputs` or `outputs` attribute of an ABI Item. These parameters must include valid [ABI types](https://docs.soliditylang.org/en/latest/types.html).\n * @param data - ABI encoded data.\n * @param options - Decoding options.\n * @returns Array of decoded values.\n */\nexport function decode<\n  const parameters extends AbiParameters,\n  as extends 'Object' | 'Array' = 'Array',\n>(\n  parameters: parameters,\n  data: Bytes.Bytes | Hex.Hex,\n  options?: decode.Options<as>,\n): decode.ReturnType<parameters, as>\n\n// eslint-disable-next-line jsdoc/require-jsdoc\nexport function decode(\n  parameters: AbiParameters,\n  data: Bytes.Bytes | Hex.Hex,\n  options: {\n    as?: 'Array' | 'Object' | undefined\n    checksumAddress?: boolean | undefined\n  } = {},\n): readonly unknown[] | Record<string, unknown> {\n  const { as = 'Array', checksumAddress = false } = options\n\n  const bytes = typeof data === 'string' ? Bytes.fromHex(data) : data\n  const cursor = Cursor.create(bytes)\n\n  if (Bytes.size(bytes) === 0 && parameters.length > 0)\n    throw new ZeroDataError()\n  if (Bytes.size(bytes) && Bytes.size(bytes) < 32)\n    throw new DataSizeTooSmallError({\n      data: typeof data === 'string' ? data : Hex.fromBytes(data),\n      parameters: parameters as readonly Parameter[],\n      size: Bytes.size(bytes),\n    })\n\n  let consumed = 0\n  const values: any = as === 'Array' ? [] : {}\n  for (let i = 0; i < parameters.length; ++i) {\n    const param = parameters[i] as Parameter\n    cursor.setPosition(consumed)\n    const [data, consumed_] = internal.decodeParameter(cursor, param, {\n      checksumAddress,\n      staticPosition: 0,\n    })\n    consumed += consumed_\n    if (as === 'Array') values.push(data)\n    else values[param.name ?? i] = data\n  }\n  return values\n}\n\nexport declare namespace decode {\n  type Options<as extends 'Object' | 'Array'> = {\n    /**\n     * Whether the decoded values should be returned as an `Object` or `Array`.\n     *\n     * @default \"Array\"\n     */\n    as?: as | 'Object' | 'Array' | undefined\n    /**\n     * Whether decoded addresses should be checksummed.\n     *\n     * @default false\n     */\n    checksumAddress?: boolean | undefined\n  }\n\n  type ReturnType<\n    parameters extends AbiParameters = AbiParameters,\n    as extends 'Object' | 'Array' = 'Array',\n  > = parameters extends readonly []\n    ? as extends 'Object'\n      ? {}\n      : []\n    : as extends 'Object'\n      ? internal.ToObject<parameters>\n      : internal.ToPrimitiveTypes<parameters>\n\n  type ErrorType =\n    | Bytes.fromHex.ErrorType\n    | internal.decodeParameter.ErrorType\n    | ZeroDataError\n    | DataSizeTooSmallError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes primitive values into ABI encoded data as per the [Application Binary Interface (ABI) Specification](https://docs.soliditylang.org/en/latest/abi-spec).\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.encode(\n *   AbiParameters.from(['string', 'uint', 'bool']),\n *   ['wagmi', 420n, true],\n * )\n * ```\n *\n * @example\n * ### JSON Parameters\n *\n * Specify **JSON ABI** Parameters as schema:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.encode(\n *   [\n *     { type: 'string', name: 'name' },\n *     { type: 'uint', name: 'age' },\n *     { type: 'bool', name: 'isOwner' },\n *   ],\n *   ['wagmi', 420n, true],\n * )\n * ```\n *\n * @param parameters - The set of ABI parameters to encode, in the shape of the `inputs` or `outputs` attribute of an ABI Item. These parameters must include valid [ABI types](https://docs.soliditylang.org/en/latest/types.html).\n * @param values - The set of primitive values that correspond to the ABI types defined in `parameters`.\n * @returns ABI encoded data.\n */\nexport function encode<\n  const parameters extends AbiParameters | readonly unknown[],\n>(\n  parameters: parameters,\n  values: parameters extends AbiParameters\n    ? internal.ToPrimitiveTypes<parameters>\n    : never,\n  options?: encode.Options,\n): Hex.Hex {\n  const { checksumAddress = false } = options ?? {}\n\n  if (parameters.length !== values.length)\n    throw new LengthMismatchError({\n      expectedLength: parameters.length as number,\n      givenLength: values.length as any,\n    })\n  // Prepare the parameters to determine dynamic types to encode.\n  const preparedParameters = internal.prepareParameters({\n    checksumAddress,\n    parameters: parameters as readonly Parameter[],\n    values: values as any,\n  })\n  const data = internal.encode(preparedParameters)\n  if (data.length === 0) return '0x'\n  return data\n}\n\nexport declare namespace encode {\n  type ErrorType =\n    | LengthMismatchError\n    | internal.encode.ErrorType\n    | internal.prepareParameters.ErrorType\n    | Errors.GlobalErrorType\n\n  type Options = {\n    /**\n     * Whether addresses should be checked against their checksum.\n     *\n     * @default false\n     */\n    checksumAddress?: boolean | undefined\n  }\n}\n\n/**\n * Encodes an array of primitive values to a [packed ABI encoding](https://docs.soliditylang.org/en/latest/abi-spec.html#non-standard-packed-mode).\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const encoded = AbiParameters.encodePacked(\n *   ['address', 'string'],\n *   ['******************************************', 'hello world'],\n * )\n * // @log: '******************************************68656c6c6f20776f726c64'\n * ```\n *\n * @param types - Set of ABI types to pack encode.\n * @param values - The set of primitive values that correspond to the ABI types defined in `types`.\n * @returns The encoded packed data.\n */\nexport function encodePacked<\n  const packedAbiTypes extends readonly PackedAbiType[] | readonly unknown[],\n>(types: packedAbiTypes, values: encodePacked.Values<packedAbiTypes>): Hex.Hex {\n  if (types.length !== values.length)\n    throw new LengthMismatchError({\n      expectedLength: types.length as number,\n      givenLength: values.length as number,\n    })\n\n  const data: Hex.Hex[] = []\n  for (let i = 0; i < (types as unknown[]).length; i++) {\n    const type = types[i]\n    const value = values[i]\n    data.push(encodePacked.encode(type, value))\n  }\n  return Hex.concat(...data)\n}\n\nexport namespace encodePacked {\n  export type ErrorType =\n    | Hex.concat.ErrorType\n    | LengthMismatchError\n    | Errors.GlobalErrorType\n\n  export type Values<\n    packedAbiTypes extends readonly PackedAbiType[] | readonly unknown[],\n  > = {\n    [key in keyof packedAbiTypes]: packedAbiTypes[key] extends abitype.AbiType\n      ? abitype.AbiParameterToPrimitiveType<{ type: packedAbiTypes[key] }>\n      : unknown\n  }\n\n  // eslint-disable-next-line jsdoc/require-jsdoc\n  export function encode<const packedAbiType extends PackedAbiType | unknown>(\n    type: packedAbiType,\n    value: Values<[packedAbiType]>[0],\n    isArray = false,\n  ): Hex.Hex {\n    if (type === 'address') {\n      const address = value as Address.Address\n      Address.assert(address)\n      return Hex.padLeft(\n        address.toLowerCase() as Hex.Hex,\n        isArray ? 32 : 0,\n      ) as Address.Address\n    }\n    if (type === 'string') return Hex.fromString(value as string)\n    if (type === 'bytes') return value as Hex.Hex\n    if (type === 'bool')\n      return Hex.padLeft(Hex.fromBoolean(value as boolean), isArray ? 32 : 1)\n\n    const intMatch = (type as string).match(Solidity.integerRegex)\n    if (intMatch) {\n      const [_type, baseType, bits = '256'] = intMatch\n      const size = Number.parseInt(bits) / 8\n      return Hex.fromNumber(value as number, {\n        size: isArray ? 32 : size,\n        signed: baseType === 'int',\n      })\n    }\n\n    const bytesMatch = (type as string).match(Solidity.bytesRegex)\n    if (bytesMatch) {\n      const [_type, size] = bytesMatch\n      if (Number.parseInt(size!) !== ((value as Hex.Hex).length - 2) / 2)\n        throw new BytesSizeMismatchError({\n          expectedSize: Number.parseInt(size!),\n          value: value as Hex.Hex,\n        })\n      return Hex.padRight(value as Hex.Hex, isArray ? 32 : 0) as Hex.Hex\n    }\n\n    const arrayMatch = (type as string).match(Solidity.arrayRegex)\n    if (arrayMatch && Array.isArray(value)) {\n      const [_type, childType] = arrayMatch\n      const data: Hex.Hex[] = []\n      for (let i = 0; i < value.length; i++) {\n        data.push(encode(childType, value[i], true))\n      }\n      if (data.length === 0) return '0x'\n      return Hex.concat(...data)\n    }\n\n    throw new InvalidTypeError(type as string)\n  }\n}\n\n/**\n * Formats {@link ox#AbiParameters.AbiParameters} into **Human Readable ABI Parameters**.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const formatted = AbiParameters.format([\n *   {\n *     name: 'spender',\n *     type: 'address',\n *   },\n *   {\n *     name: 'amount',\n *     type: 'uint256',\n *   },\n * ])\n *\n * formatted\n * //    ^?\n *\n *\n * ```\n *\n * @param parameters - The ABI Parameters to format.\n * @returns The formatted ABI Parameters  .\n */\nexport function format<\n  const parameters extends readonly [\n    Parameter | abitype.AbiEventParameter,\n    ...(readonly (Parameter | abitype.AbiEventParameter)[]),\n  ],\n>(\n  parameters:\n    | parameters\n    | readonly [\n        Parameter | abitype.AbiEventParameter,\n        ...(readonly (Parameter | abitype.AbiEventParameter)[]),\n      ],\n): abitype.FormatAbiParameters<parameters> {\n  return abitype.formatAbiParameters(parameters)\n}\n\nexport declare namespace format {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Parses arbitrary **JSON ABI Parameters** or **Human Readable ABI Parameters** into typed {@link ox#AbiParameters.AbiParameters}.\n *\n * @example\n * ### JSON Parameters\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const parameters = AbiParameters.from([\n *   {\n *     name: 'spender',\n *     type: 'address',\n *   },\n *   {\n *     name: 'amount',\n *     type: 'uint256',\n *   },\n * ])\n *\n * parameters\n * //^?\n *\n *\n *\n *\n *\n *\n *\n * ```\n *\n * @example\n * ### Human Readable Parameters\n *\n * Human Readable ABI Parameters can be parsed into a typed {@link ox#AbiParameters.AbiParameters}:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const parameters = AbiParameters.from('address spender, uint256 amount')\n *\n * parameters\n * //^?\n *\n *\n *\n *\n *\n *\n *\n * ```\n *\n * @example\n * It is possible to specify `struct`s along with your definitions:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const parameters = AbiParameters.from([\n *   'struct Foo { address spender; uint256 amount; }', // [!code hl]\n *   'Foo foo, address bar',\n * ])\n *\n * parameters\n * //^?\n *\n *\n *\n *\n *\n *\n *\n *\n *\n *\n *\n *\n * ```\n *\n *\n *\n * @param parameters - The ABI Parameters to parse.\n * @returns The typed ABI Parameters.\n */\nexport function from<\n  const parameters extends AbiParameters | string | readonly string[],\n>(\n  parameters: parameters | AbiParameters | string | readonly string[],\n): from.ReturnType<parameters> {\n  if (Array.isArray(parameters) && typeof parameters[0] === 'string')\n    return abitype.parseAbiParameters(parameters) as never\n  if (typeof parameters === 'string')\n    return abitype.parseAbiParameters(parameters) as never\n  return parameters as never\n}\n\nexport declare namespace from {\n  type ReturnType<\n    parameters extends AbiParameters | string | readonly string[],\n  > = parameters extends string\n    ? abitype.ParseAbiParameters<parameters>\n    : parameters extends readonly string[]\n      ? abitype.ParseAbiParameters<parameters>\n      : parameters\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Throws when the data size is too small for the given parameters.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '0x010f')\n * //                                             ↑ ❌ 2 bytes\n * // @error: AbiParameters.DataSizeTooSmallError: Data size of 2 bytes is too small for given parameters.\n * // @error: Params: (uint256)\n * // @error: Data:   0x010f (2 bytes)\n * ```\n *\n * ### Solution\n *\n * Pass a valid data size.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '0x00000000000000000000000000000000000000000000000000000000000010f')\n * //                                             ↑ ✅ 32 bytes\n * ```\n */\nexport class DataSizeTooSmallError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.DataSizeTooSmallError'\n  constructor({\n    data,\n    parameters,\n    size,\n  }: { data: Hex.Hex; parameters: readonly Parameter[]; size: number }) {\n    super(`Data size of ${size} bytes is too small for given parameters.`, {\n      metaMessages: [\n        `Params: (${abitype.formatAbiParameters(parameters as readonly [Parameter])})`,\n        `Data:   ${data} (${size} bytes)`,\n      ],\n    })\n  }\n}\n\n/**\n * Throws when zero data is provided, but data is expected.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '0x')\n * //                                           ↑ ❌ zero data\n * // @error: AbiParameters.DataSizeTooSmallError: Data size of 2 bytes is too small for given parameters.\n * // @error: Params: (uint256)\n * // @error: Data:   0x010f (2 bytes)\n * ```\n *\n * ### Solution\n *\n * Pass valid data.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '0x00000000000000000000000000000000000000000000000000000000000010f')\n * //                                             ↑ ✅ 32 bytes\n * ```\n */\nexport class ZeroDataError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.ZeroDataError'\n  constructor() {\n    super('Cannot decode zero data (\"0x\") with ABI parameters.')\n  }\n}\n\n/**\n * The length of the array value does not match the length specified in the corresponding ABI parameter.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from('uint256[3]'), [[69n, 420n]])\n * //                                               ↑ expected: 3  ↑ ❌ length: 2\n * // @error: AbiParameters.ArrayLengthMismatchError: ABI encoding array length mismatch\n * // @error: for type `uint256[3]`. Expected: `3`. Given: `2`.\n * ```\n *\n * ### Solution\n *\n * Pass an array of the correct length.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['uint256[3]']), [[69n, 420n, 69n]])\n * //                                                         ↑ ✅ length: 3\n * ```\n */\nexport class ArrayLengthMismatchError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.ArrayLengthMismatchError'\n  constructor({\n    expectedLength,\n    givenLength,\n    type,\n  }: { expectedLength: number; givenLength: number; type: string }) {\n    super(\n      `Array length mismatch for type \\`${type}\\`. Expected: \\`${expectedLength}\\`. Given: \\`${givenLength}\\`.`,\n    )\n  }\n}\n\n/**\n * The size of the bytes value does not match the size specified in the corresponding ABI parameter.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from('bytes8'), [['0xdeadbeefdeadbeefdeadbeef']])\n * //                                            ↑ expected: 8 bytes  ↑ ❌ size: 12 bytes\n * // @error: BytesSizeMismatchError: Size of bytes \"0xdeadbeefdeadbeefdeadbeef\"\n * // @error: (bytes12) does not match expected size (bytes8).\n * ```\n *\n * ### Solution\n *\n * Pass a bytes value of the correct size.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['bytes8']), ['0xdeadbeefdeadbeef'])\n * //                                                       ↑ ✅ size: 8 bytes\n * ```\n */\nexport class BytesSizeMismatchError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.BytesSizeMismatchError'\n  constructor({\n    expectedSize,\n    value,\n  }: { expectedSize: number; value: Hex.Hex }) {\n    super(\n      `Size of bytes \"${value}\" (bytes${Hex.size(\n        value,\n      )}) does not match expected size (bytes${expectedSize}).`,\n    )\n  }\n}\n\n/**\n * The length of the values to encode does not match the length of the ABI parameters.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['string', 'uint256']), ['hello'])\n * // @error: LengthMismatchError: ABI encoding params/values length mismatch.\n * // @error: Expected length (params): 2\n * // @error: Given length (values): 1\n * ```\n *\n * ### Solution\n *\n * Pass the correct number of values to encode.\n *\n * ### Solution\n *\n * Pass a [valid ABI type](https://docs.soliditylang.org/en/develop/abi-spec.html#types).\n */\nexport class LengthMismatchError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.LengthMismatchError'\n  constructor({\n    expectedLength,\n    givenLength,\n  }: { expectedLength: number; givenLength: number }) {\n    super(\n      [\n        'ABI encoding parameters/values length mismatch.',\n        `Expected length (parameters): ${expectedLength}`,\n        `Given length (values): ${givenLength}`,\n      ].join('\\n'),\n    )\n  }\n}\n\n/**\n * The value provided is not a valid array as specified in the corresponding ABI parameter.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['uint256[3]']), [69])\n * ```\n *\n * ### Solution\n *\n * Pass an array value.\n */\nexport class InvalidArrayError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.InvalidArrayError'\n  constructor(value: unknown) {\n    super(`Value \\`${value}\\` is not a valid array.`)\n  }\n}\n\n/**\n * Throws when the ABI parameter type is invalid.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'lol' }], '0x00000000000000000000000000000000000000000000000000000000000010f')\n * //                             ↑ ❌ invalid type\n * // @error: AbiParameters.InvalidTypeError: Type `lol` is not a valid ABI Type.\n * ```\n */\nexport class InvalidTypeError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.InvalidTypeError'\n  constructor(type: string) {\n    super(`Type \\`${type}\\` is not a valid ABI Type.`)\n  }\n}\n", null, null, null, null, null, null, null, null, null, null, null, null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,KAAC,SAASA,IAAEC,IAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQA,GAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAOA,EAAC,KAAGD,KAAE,eAAa,OAAO,aAAW,aAAWA,MAAG,MAAM,QAAMC,GAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,UAAID,KAAE,KAAIC,KAAE,KAAIC,KAAE,MAAKC,KAAE,eAAcC,KAAE,UAASC,KAAE,UAASC,KAAE,QAAO,IAAE,OAAMC,KAAE,QAAOC,KAAE,SAAQC,KAAE,WAAUC,KAAE,QAAOC,KAAE,QAAOC,KAAE,gBAAe,IAAE,8FAA6FC,KAAE,uFAAsFC,KAAE,EAAC,MAAK,MAAK,UAAS,2DAA2D,MAAM,GAAG,GAAE,QAAO,wFAAwF,MAAM,GAAG,GAAE,SAAQ,SAASd,IAAE;AAAC,YAAIC,KAAE,CAAC,MAAK,MAAK,MAAK,IAAI,GAAEC,KAAEF,KAAE;AAAI,eAAM,MAAIA,MAAGC,IAAGC,KAAE,MAAI,EAAE,KAAGD,GAAEC,EAAC,KAAGD,GAAE,CAAC,KAAG;AAAA,MAAG,EAAC,GAAEc,KAAE,SAASf,IAAEC,IAAEC,IAAE;AAAC,YAAIC,KAAE,OAAOH,EAAC;AAAE,eAAM,CAACG,MAAGA,GAAE,UAAQF,KAAED,KAAE,KAAG,MAAMC,KAAE,IAAEE,GAAE,MAAM,EAAE,KAAKD,EAAC,IAAEF;AAAA,MAAC,GAAE,IAAE,EAAC,GAAEe,IAAE,GAAE,SAASf,IAAE;AAAC,YAAIC,KAAE,CAACD,GAAE,UAAU,GAAEE,KAAE,KAAK,IAAID,EAAC,GAAEE,KAAE,KAAK,MAAMD,KAAE,EAAE,GAAEE,KAAEF,KAAE;AAAG,gBAAOD,MAAG,IAAE,MAAI,OAAKc,GAAEZ,IAAE,GAAE,GAAG,IAAE,MAAIY,GAAEX,IAAE,GAAE,GAAG;AAAA,MAAC,GAAE,GAAE,SAASJ,GAAEC,IAAEC,IAAE;AAAC,YAAGD,GAAE,KAAK,IAAEC,GAAE,KAAK,EAAE,QAAM,CAACF,GAAEE,IAAED,EAAC;AAAE,YAAIE,KAAE,MAAID,GAAE,KAAK,IAAED,GAAE,KAAK,MAAIC,GAAE,MAAM,IAAED,GAAE,MAAM,IAAGG,KAAEH,GAAE,MAAM,EAAE,IAAIE,IAAEK,EAAC,GAAEH,KAAEH,KAAEE,KAAE,GAAEE,KAAEL,GAAE,MAAM,EAAE,IAAIE,MAAGE,KAAE,KAAG,IAAGG,EAAC;AAAE,eAAM,EAAE,EAAEL,MAAGD,KAAEE,OAAIC,KAAED,KAAEE,KAAEA,KAAEF,QAAK;AAAA,MAAE,GAAE,GAAE,SAASJ,IAAE;AAAC,eAAOA,KAAE,IAAE,KAAK,KAAKA,EAAC,KAAG,IAAE,KAAK,MAAMA,EAAC;AAAA,MAAC,GAAE,GAAE,SAASA,IAAE;AAAC,eAAM,EAAC,GAAEQ,IAAE,GAAEE,IAAE,GAAEH,IAAE,GAAE,GAAE,GAAEI,IAAE,GAAEL,IAAE,GAAED,IAAE,GAAED,IAAE,IAAGD,IAAE,GAAEM,GAAC,EAAET,EAAC,KAAG,OAAOA,MAAG,EAAE,EAAE,YAAY,EAAE,QAAQ,MAAK,EAAE;AAAA,MAAC,GAAE,GAAE,SAASA,IAAE;AAAC,eAAO,WAASA;AAAA,MAAC,EAAC,GAAE,IAAE,MAAK,IAAE,CAAC;AAAE,QAAE,CAAC,IAAEc;AAAE,UAAI,IAAE,kBAAiBE,KAAE,SAAShB,IAAE;AAAC,eAAOA,cAAa,KAAG,EAAE,CAACA,MAAG,CAACA,GAAE,CAAC;AAAA,MAAE,GAAE,IAAE,SAASA,GAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAIC;AAAE,YAAG,CAACH,GAAE,QAAO;AAAE,YAAG,YAAU,OAAOA,IAAE;AAAC,cAAII,KAAEJ,GAAE,YAAY;AAAE,YAAEI,EAAC,MAAID,KAAEC,KAAGH,OAAI,EAAEG,EAAC,IAAEH,IAAEE,KAAEC;AAAG,cAAIC,KAAEL,GAAE,MAAM,GAAG;AAAE,cAAG,CAACG,MAAGE,GAAE,SAAO,EAAE,QAAON,GAAEM,GAAE,CAAC,CAAC;AAAA,QAAC,OAAK;AAAC,cAAIW,KAAEhB,GAAE;AAAK,YAAEgB,EAAC,IAAEhB,IAAEG,KAAEa;AAAA,QAAC;AAAC,eAAM,CAACd,MAAGC,OAAI,IAAEA,KAAGA,MAAG,CAACD,MAAG;AAAA,MAAC,GAAEe,KAAE,SAASlB,IAAEC,IAAE;AAAC,YAAGe,GAAEhB,EAAC,EAAE,QAAOA,GAAE,MAAM;AAAE,YAAIE,KAAE,YAAU,OAAOD,KAAEA,KAAE,CAAC;AAAE,eAAOC,GAAE,OAAKF,IAAEE,GAAE,OAAK,WAAU,IAAI,EAAEA,EAAC;AAAA,MAAC,GAAE,IAAE;AAAE,QAAE,IAAE,GAAE,EAAE,IAAEc,IAAE,EAAE,IAAE,SAAShB,IAAEC,IAAE;AAAC,eAAOiB,GAAElB,IAAE,EAAC,QAAOC,GAAE,IAAG,KAAIA,GAAE,IAAG,GAAEA,GAAE,IAAG,SAAQA,GAAE,QAAO,CAAC;AAAA,MAAC;AAAE,UAAI,IAAE,WAAU;AAAC,iBAASa,GAAEd,IAAE;AAAC,eAAK,KAAG,EAAEA,GAAE,QAAO,MAAK,IAAE,GAAE,KAAK,MAAMA,EAAC,GAAE,KAAK,KAAG,KAAK,MAAIA,GAAE,KAAG,CAAC,GAAE,KAAK,CAAC,IAAE;AAAA,QAAE;AAAC,YAAIe,KAAED,GAAE;AAAU,eAAOC,GAAE,QAAM,SAASf,IAAE;AAAC,eAAK,KAAG,SAASA,IAAE;AAAC,gBAAIC,KAAED,GAAE,MAAKE,KAAEF,GAAE;AAAI,gBAAG,SAAOC,GAAE,QAAO,oBAAI,KAAK,GAAG;AAAE,gBAAG,EAAE,EAAEA,EAAC,EAAE,QAAO,oBAAI;AAAK,gBAAGA,cAAa,KAAK,QAAO,IAAI,KAAKA,EAAC;AAAE,gBAAG,YAAU,OAAOA,MAAG,CAAC,MAAM,KAAKA,EAAC,GAAE;AAAC,kBAAIE,KAAEF,GAAE,MAAM,CAAC;AAAE,kBAAGE,IAAE;AAAC,oBAAIC,KAAED,GAAE,CAAC,IAAE,KAAG,GAAEE,MAAGF,GAAE,CAAC,KAAG,KAAK,UAAU,GAAE,CAAC;AAAE,uBAAOD,KAAE,IAAI,KAAK,KAAK,IAAIC,GAAE,CAAC,GAAEC,IAAED,GAAE,CAAC,KAAG,GAAEA,GAAE,CAAC,KAAG,GAAEA,GAAE,CAAC,KAAG,GAAEA,GAAE,CAAC,KAAG,GAAEE,EAAC,CAAC,IAAE,IAAI,KAAKF,GAAE,CAAC,GAAEC,IAAED,GAAE,CAAC,KAAG,GAAEA,GAAE,CAAC,KAAG,GAAEA,GAAE,CAAC,KAAG,GAAEA,GAAE,CAAC,KAAG,GAAEE,EAAC;AAAA,cAAC;AAAA,YAAC;AAAC,mBAAO,IAAI,KAAKJ,EAAC;AAAA,UAAC,EAAED,EAAC,GAAE,KAAK,KAAK;AAAA,QAAC,GAAEe,GAAE,OAAK,WAAU;AAAC,cAAIf,KAAE,KAAK;AAAG,eAAK,KAAGA,GAAE,YAAY,GAAE,KAAK,KAAGA,GAAE,SAAS,GAAE,KAAK,KAAGA,GAAE,QAAQ,GAAE,KAAK,KAAGA,GAAE,OAAO,GAAE,KAAK,KAAGA,GAAE,SAAS,GAAE,KAAK,KAAGA,GAAE,WAAW,GAAE,KAAK,KAAGA,GAAE,WAAW,GAAE,KAAK,MAAIA,GAAE,gBAAgB;AAAA,QAAC,GAAEe,GAAE,SAAO,WAAU;AAAC,iBAAO;AAAA,QAAC,GAAEA,GAAE,UAAQ,WAAU;AAAC,iBAAM,EAAE,KAAK,GAAG,SAAS,MAAIH;AAAA,QAAE,GAAEG,GAAE,SAAO,SAASf,IAAEC,IAAE;AAAC,cAAIC,KAAEgB,GAAElB,EAAC;AAAE,iBAAO,KAAK,QAAQC,EAAC,KAAGC,MAAGA,MAAG,KAAK,MAAMD,EAAC;AAAA,QAAC,GAAEc,GAAE,UAAQ,SAASf,IAAEC,IAAE;AAAC,iBAAOiB,GAAElB,EAAC,IAAE,KAAK,QAAQC,EAAC;AAAA,QAAC,GAAEc,GAAE,WAAS,SAASf,IAAEC,IAAE;AAAC,iBAAO,KAAK,MAAMA,EAAC,IAAEiB,GAAElB,EAAC;AAAA,QAAC,GAAEe,GAAE,KAAG,SAASf,IAAEC,IAAEC,IAAE;AAAC,iBAAO,EAAE,EAAEF,EAAC,IAAE,KAAKC,EAAC,IAAE,KAAK,IAAIC,IAAEF,EAAC;AAAA,QAAC,GAAEe,GAAE,OAAK,WAAU;AAAC,iBAAO,KAAK,MAAM,KAAK,QAAQ,IAAE,GAAG;AAAA,QAAC,GAAEA,GAAE,UAAQ,WAAU;AAAC,iBAAO,KAAK,GAAG,QAAQ;AAAA,QAAC,GAAEA,GAAE,UAAQ,SAASf,IAAEC,IAAE;AAAC,cAAIC,KAAE,MAAKC,KAAE,CAAC,CAAC,EAAE,EAAEF,EAAC,KAAGA,IAAEQ,KAAE,EAAE,EAAET,EAAC,GAAEY,KAAE,SAASZ,IAAEC,IAAE;AAAC,gBAAIG,KAAE,EAAE,EAAEF,GAAE,KAAG,KAAK,IAAIA,GAAE,IAAGD,IAAED,EAAC,IAAE,IAAI,KAAKE,GAAE,IAAGD,IAAED,EAAC,GAAEE,EAAC;AAAE,mBAAOC,KAAEC,KAAEA,GAAE,MAAM,CAAC;AAAA,UAAC,GAAEe,KAAE,SAASnB,IAAEC,IAAE;AAAC,mBAAO,EAAE,EAAEC,GAAE,OAAO,EAAEF,EAAC,EAAE,MAAME,GAAE,OAAO,GAAG,IAAGC,KAAE,CAAC,GAAE,GAAE,GAAE,CAAC,IAAE,CAAC,IAAG,IAAG,IAAG,GAAG,GAAG,MAAMF,EAAC,CAAC,GAAEC,EAAC;AAAA,UAAC,GAAEW,KAAE,KAAK,IAAGC,KAAE,KAAK,IAAGC,KAAE,KAAK,IAAGK,KAAE,SAAO,KAAK,KAAG,QAAM;AAAI,kBAAOX,IAAE;AAAA,YAAC,KAAKC;AAAE,qBAAOP,KAAES,GAAE,GAAE,CAAC,IAAEA,GAAE,IAAG,EAAE;AAAA,YAAE,KAAKJ;AAAE,qBAAOL,KAAES,GAAE,GAAEE,EAAC,IAAEF,GAAE,GAAEE,KAAE,CAAC;AAAA,YAAE,KAAKP;AAAE,kBAAIc,KAAE,KAAK,QAAQ,EAAE,aAAW,GAAEC,MAAGT,KAAEQ,KAAER,KAAE,IAAEA,MAAGQ;AAAE,qBAAOT,GAAET,KAAEY,KAAEO,KAAEP,MAAG,IAAEO,KAAGR,EAAC;AAAA,YAAE,KAAK;AAAA,YAAE,KAAKH;AAAE,qBAAOQ,GAAEC,KAAE,SAAQ,CAAC;AAAA,YAAE,KAAKd;AAAE,qBAAOa,GAAEC,KAAE,WAAU,CAAC;AAAA,YAAE,KAAKf;AAAE,qBAAOc,GAAEC,KAAE,WAAU,CAAC;AAAA,YAAE,KAAKhB;AAAE,qBAAOe,GAAEC,KAAE,gBAAe,CAAC;AAAA,YAAE;AAAQ,qBAAO,KAAK,MAAM;AAAA,UAAC;AAAA,QAAC,GAAEL,GAAE,QAAM,SAASf,IAAE;AAAC,iBAAO,KAAK,QAAQA,IAAE,KAAE;AAAA,QAAC,GAAEe,GAAE,OAAK,SAASf,IAAEC,IAAE;AAAC,cAAIC,IAAEK,KAAE,EAAE,EAAEP,EAAC,GAAES,KAAE,SAAO,KAAK,KAAG,QAAM,KAAIG,MAAGV,KAAE,CAAC,GAAEA,GAAE,CAAC,IAAEO,KAAE,QAAOP,GAAES,EAAC,IAAEF,KAAE,QAAOP,GAAEM,EAAC,IAAEC,KAAE,SAAQP,GAAEQ,EAAC,IAAED,KAAE,YAAWP,GAAEI,EAAC,IAAEG,KAAE,SAAQP,GAAEG,EAAC,IAAEI,KAAE,WAAUP,GAAEE,EAAC,IAAEK,KAAE,WAAUP,GAAEC,EAAC,IAAEM,KAAE,gBAAeP,IAAGK,EAAC,GAAEY,KAAEZ,OAAI,IAAE,KAAK,MAAIN,KAAE,KAAK,MAAIA;AAAE,cAAGM,OAAIC,MAAGD,OAAIG,IAAE;AAAC,gBAAIG,KAAE,KAAK,MAAM,EAAE,IAAIF,IAAE,CAAC;AAAE,YAAAE,GAAE,GAAGD,EAAC,EAAEO,EAAC,GAAEN,GAAE,KAAK,GAAE,KAAK,KAAGA,GAAE,IAAIF,IAAE,KAAK,IAAI,KAAK,IAAGE,GAAE,YAAY,CAAC,CAAC,EAAE;AAAA,UAAE,MAAM,CAAAD,MAAG,KAAK,GAAGA,EAAC,EAAEO,EAAC;AAAE,iBAAO,KAAK,KAAK,GAAE;AAAA,QAAI,GAAEJ,GAAE,MAAI,SAASf,IAAEC,IAAE;AAAC,iBAAO,KAAK,MAAM,EAAE,KAAKD,IAAEC,EAAC;AAAA,QAAC,GAAEc,GAAE,MAAI,SAASf,IAAE;AAAC,iBAAO,KAAK,EAAE,EAAEA,EAAC,CAAC,EAAE;AAAA,QAAC,GAAEe,GAAE,MAAI,SAASZ,IAAEM,IAAE;AAAC,cAAIE,IAAEC,KAAE;AAAK,UAAAT,KAAE,OAAOA,EAAC;AAAE,cAAIgB,KAAE,EAAE,EAAEV,EAAC,GAAEI,KAAE,SAASb,IAAE;AAAC,gBAAIC,KAAEiB,GAAEN,EAAC;AAAE,mBAAO,EAAE,EAAEX,GAAE,KAAKA,GAAE,KAAK,IAAE,KAAK,MAAMD,KAAEG,EAAC,CAAC,GAAES,EAAC;AAAA,UAAC;AAAE,cAAGO,OAAIX,GAAE,QAAO,KAAK,IAAIA,IAAE,KAAK,KAAGL,EAAC;AAAE,cAAGgB,OAAIT,GAAE,QAAO,KAAK,IAAIA,IAAE,KAAK,KAAGP,EAAC;AAAE,cAAGgB,OAAI,EAAE,QAAON,GAAE,CAAC;AAAE,cAAGM,OAAIZ,GAAE,QAAOM,GAAE,CAAC;AAAE,cAAIC,MAAGH,KAAE,CAAC,GAAEA,GAAEN,EAAC,IAAEJ,IAAEU,GAAEL,EAAC,IAAEJ,IAAES,GAAEP,EAAC,IAAEJ,IAAEW,IAAGQ,EAAC,KAAG,GAAEJ,KAAE,KAAK,GAAG,QAAQ,IAAEZ,KAAEW;AAAE,iBAAO,EAAE,EAAEC,IAAE,IAAI;AAAA,QAAC,GAAEA,GAAE,WAAS,SAASf,IAAEC,IAAE;AAAC,iBAAO,KAAK,IAAI,KAAGD,IAAEC,EAAC;AAAA,QAAC,GAAEc,GAAE,SAAO,SAASf,IAAE;AAAC,cAAIC,KAAE,MAAKC,KAAE,KAAK,QAAQ;AAAE,cAAG,CAAC,KAAK,QAAQ,EAAE,QAAOA,GAAE,eAAaU;AAAE,cAAIT,KAAEH,MAAG,wBAAuBI,KAAE,EAAE,EAAE,IAAI,GAAEC,KAAE,KAAK,IAAGC,KAAE,KAAK,IAAGW,KAAE,KAAK,IAAGV,KAAEL,GAAE,UAASM,KAAEN,GAAE,QAAOO,KAAEP,GAAE,UAASQ,KAAE,SAASV,IAAEE,IAAEE,IAAEC,IAAE;AAAC,mBAAOL,OAAIA,GAAEE,EAAC,KAAGF,GAAEC,IAAEE,EAAC,MAAIC,GAAEF,EAAC,EAAE,MAAM,GAAEG,EAAC;AAAA,UAAC,GAAEM,KAAE,SAASX,IAAE;AAAC,mBAAO,EAAE,EAAEK,KAAE,MAAI,IAAGL,IAAE,GAAG;AAAA,UAAC,GAAEmB,KAAEV,MAAG,SAAST,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,KAAEH,KAAE,KAAG,OAAK;AAAK,mBAAOE,KAAEC,GAAE,YAAY,IAAEA;AAAA,UAAC;AAAE,iBAAOA,GAAE,QAAQU,IAAG,SAASb,IAAEG,IAAE;AAAC,mBAAOA,MAAG,SAASH,IAAE;AAAC,sBAAOA,IAAE;AAAA,gBAAC,KAAI;AAAK,yBAAO,OAAOC,GAAE,EAAE,EAAE,MAAM,EAAE;AAAA,gBAAE,KAAI;AAAO,yBAAO,EAAE,EAAEA,GAAE,IAAG,GAAE,GAAG;AAAA,gBAAE,KAAI;AAAI,yBAAOgB,KAAE;AAAA,gBAAE,KAAI;AAAK,yBAAO,EAAE,EAAEA,KAAE,GAAE,GAAE,GAAG;AAAA,gBAAE,KAAI;AAAM,yBAAOP,GAAER,GAAE,aAAYe,IAAET,IAAE,CAAC;AAAA,gBAAE,KAAI;AAAO,yBAAOE,GAAEF,IAAES,EAAC;AAAA,gBAAE,KAAI;AAAI,yBAAOhB,GAAE;AAAA,gBAAG,KAAI;AAAK,yBAAO,EAAE,EAAEA,GAAE,IAAG,GAAE,GAAG;AAAA,gBAAE,KAAI;AAAI,yBAAO,OAAOA,GAAE,EAAE;AAAA,gBAAE,KAAI;AAAK,yBAAOS,GAAER,GAAE,aAAYD,GAAE,IAAGM,IAAE,CAAC;AAAA,gBAAE,KAAI;AAAM,yBAAOG,GAAER,GAAE,eAAcD,GAAE,IAAGM,IAAE,CAAC;AAAA,gBAAE,KAAI;AAAO,yBAAOA,GAAEN,GAAE,EAAE;AAAA,gBAAE,KAAI;AAAI,yBAAO,OAAOI,EAAC;AAAA,gBAAE,KAAI;AAAK,yBAAO,EAAE,EAAEA,IAAE,GAAE,GAAG;AAAA,gBAAE,KAAI;AAAI,yBAAOM,GAAE,CAAC;AAAA,gBAAE,KAAI;AAAK,yBAAOA,GAAE,CAAC;AAAA,gBAAE,KAAI;AAAI,yBAAOQ,GAAEd,IAAEC,IAAE,IAAE;AAAA,gBAAE,KAAI;AAAI,yBAAOa,GAAEd,IAAEC,IAAE,KAAE;AAAA,gBAAE,KAAI;AAAI,yBAAO,OAAOA,EAAC;AAAA,gBAAE,KAAI;AAAK,yBAAO,EAAE,EAAEA,IAAE,GAAE,GAAG;AAAA,gBAAE,KAAI;AAAI,yBAAO,OAAOL,GAAE,EAAE;AAAA,gBAAE,KAAI;AAAK,yBAAO,EAAE,EAAEA,GAAE,IAAG,GAAE,GAAG;AAAA,gBAAE,KAAI;AAAM,yBAAO,EAAE,EAAEA,GAAE,KAAI,GAAE,GAAG;AAAA,gBAAE,KAAI;AAAI,yBAAOG;AAAA,cAAC;AAAC,qBAAO;AAAA,YAAI,EAAEJ,EAAC,KAAGI,GAAE,QAAQ,KAAI,EAAE;AAAA,UAAC,CAAE;AAAA,QAAC,GAAEW,GAAE,YAAU,WAAU;AAAC,iBAAO,KAAG,CAAC,KAAK,MAAM,KAAK,GAAG,kBAAkB,IAAE,EAAE;AAAA,QAAC,GAAEA,GAAE,OAAK,SAASZ,IAAEQ,IAAEC,IAAE;AAAC,cAAIO,IAAEN,KAAE,MAAKC,KAAE,EAAE,EAAEH,EAAC,GAAEI,KAAEG,GAAEf,EAAC,GAAEiB,MAAGL,GAAE,UAAU,IAAE,KAAK,UAAU,KAAGd,IAAEoB,KAAE,OAAKN,IAAEO,KAAE,WAAU;AAAC,mBAAO,EAAE,EAAET,IAAEE,EAAC;AAAA,UAAC;AAAE,kBAAOD,IAAE;AAAA,YAAC,KAAKJ;AAAE,cAAAS,KAAEG,GAAE,IAAE;AAAG;AAAA,YAAM,KAAKd;AAAE,cAAAW,KAAEG,GAAE;AAAE;AAAA,YAAM,KAAKb;AAAE,cAAAU,KAAEG,GAAE,IAAE;AAAE;AAAA,YAAM,KAAKf;AAAE,cAAAY,MAAGE,KAAED,MAAG;AAAO;AAAA,YAAM,KAAK;AAAE,cAAAD,MAAGE,KAAED,MAAG;AAAM;AAAA,YAAM,KAAKd;AAAE,cAAAa,KAAEE,KAAEnB;AAAE;AAAA,YAAM,KAAKG;AAAE,cAAAc,KAAEE,KAAEpB;AAAE;AAAA,YAAM,KAAKG;AAAE,cAAAe,KAAEE,KAAErB;AAAE;AAAA,YAAM;AAAQ,cAAAmB,KAAEE;AAAA,UAAC;AAAC,iBAAOT,KAAEO,KAAE,EAAE,EAAEA,EAAC;AAAA,QAAC,GAAEJ,GAAE,cAAY,WAAU;AAAC,iBAAO,KAAK,MAAMP,EAAC,EAAE;AAAA,QAAE,GAAEO,GAAE,UAAQ,WAAU;AAAC,iBAAO,EAAE,KAAK,EAAE;AAAA,QAAC,GAAEA,GAAE,SAAO,SAASf,IAAEC,IAAE;AAAC,cAAG,CAACD,GAAE,QAAO,KAAK;AAAG,cAAIE,KAAE,KAAK,MAAM,GAAEC,KAAE,EAAEH,IAAEC,IAAE,IAAE;AAAE,iBAAOE,OAAID,GAAE,KAAGC,KAAGD;AAAA,QAAC,GAAEa,GAAE,QAAM,WAAU;AAAC,iBAAO,EAAE,EAAE,KAAK,IAAG,IAAI;AAAA,QAAC,GAAEA,GAAE,SAAO,WAAU;AAAC,iBAAO,IAAI,KAAK,KAAK,QAAQ,CAAC;AAAA,QAAC,GAAEA,GAAE,SAAO,WAAU;AAAC,iBAAO,KAAK,QAAQ,IAAE,KAAK,YAAY,IAAE;AAAA,QAAI,GAAEA,GAAE,cAAY,WAAU;AAAC,iBAAO,KAAK,GAAG,YAAY;AAAA,QAAC,GAAEA,GAAE,WAAS,WAAU;AAAC,iBAAO,KAAK,GAAG,YAAY;AAAA,QAAC,GAAED;AAAA,MAAC,EAAE,GAAES,KAAE,EAAE;AAAU,aAAOL,GAAE,YAAUK,IAAE,CAAC,CAAC,OAAMpB,EAAC,GAAE,CAAC,MAAKC,EAAC,GAAE,CAAC,MAAKC,EAAC,GAAE,CAAC,MAAKC,EAAC,GAAE,CAAC,MAAK,CAAC,GAAE,CAAC,MAAKE,EAAC,GAAE,CAAC,MAAKE,EAAC,GAAE,CAAC,MAAKC,EAAC,CAAC,EAAE,QAAS,SAASX,IAAE;AAAC,QAAAuB,GAAEvB,GAAE,CAAC,CAAC,IAAE,SAASC,IAAE;AAAC,iBAAO,KAAK,GAAGA,IAAED,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE,GAAEkB,GAAE,SAAO,SAASlB,IAAEC,IAAE;AAAC,eAAOD,GAAE,OAAKA,GAAEC,IAAE,GAAEiB,EAAC,GAAElB,GAAE,KAAG,OAAIkB;AAAA,MAAC,GAAEA,GAAE,SAAO,GAAEA,GAAE,UAAQF,IAAEE,GAAE,OAAK,SAASlB,IAAE;AAAC,eAAOkB,GAAE,MAAIlB,EAAC;AAAA,MAAC,GAAEkB,GAAE,KAAG,EAAE,CAAC,GAAEA,GAAE,KAAG,GAAEA,GAAE,IAAE,CAAC,GAAEA;AAAA,IAAC,CAAE;AAAA;AAAA;;;ACAt/N;AAAA;AAAA,KAAC,SAASM,IAAEC,IAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQA,GAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAOA,EAAC,KAAGD,KAAE,eAAa,OAAO,aAAW,aAAWA,MAAG,MAAM,kBAAgBC,GAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,aAAM,EAAC,MAAK,MAAK,UAAS,2DAA2D,MAAM,GAAG,GAAE,QAAO,wFAAwF,MAAM,GAAG,GAAE,SAAQ,SAASD,IAAE;AAAC,YAAIC,KAAE,CAAC,MAAK,MAAK,MAAK,IAAI,GAAEC,KAAEF,KAAE;AAAI,eAAM,MAAIA,MAAGC,IAAGC,KAAE,MAAI,EAAE,KAAGD,GAAEC,EAAC,KAAGD,GAAE,CAAC,KAAG;AAAA,MAAG,EAAC;AAAA,IAAC,CAAE;AAAA;AAAA;;;ACAhiB;AAAA;AAAA,KAAC,SAASE,IAAEC,IAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQA,GAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAOA,EAAC,KAAGD,KAAE,eAAa,OAAO,aAAW,aAAWA,MAAG,MAAM,4BAA0BC,GAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,aAAO,SAASD,IAAEC,IAAEC,IAAE;AAAC,QAAAF,KAAEA,MAAG,CAAC;AAAE,YAAIG,KAAEF,GAAE,WAAUG,KAAE,EAAC,QAAO,SAAQ,MAAK,UAAS,GAAE,iBAAgB,GAAE,YAAW,IAAG,cAAa,GAAE,WAAU,IAAG,YAAW,GAAE,SAAQ,IAAG,WAAU,GAAE,WAAU,IAAG,aAAY,GAAE,UAAS,IAAG,WAAU;AAAE,iBAASC,GAAEL,IAAEC,IAAEC,IAAEE,IAAE;AAAC,iBAAOD,GAAE,WAAWH,IAAEC,IAAEC,IAAEE,EAAC;AAAA,QAAC;AAAC,QAAAF,GAAE,GAAG,eAAaE,IAAED,GAAE,aAAW,SAASF,IAAEE,IAAEE,IAAEC,IAAEC,IAAE;AAAC,mBAAQC,IAAE,GAAEC,IAAEC,KAAEL,GAAE,QAAQ,EAAE,gBAAcD,IAAEO,KAAEX,GAAE,cAAY,CAAC,EAAC,GAAE,KAAI,GAAE,IAAG,GAAE,SAAQ,GAAE,EAAC,GAAE,KAAI,GAAE,GAAE,GAAE,EAAC,GAAE,MAAK,GAAE,IAAG,GAAE,SAAQ,GAAE,EAAC,GAAE,KAAI,GAAE,GAAE,GAAE,EAAC,GAAE,MAAK,GAAE,IAAG,GAAE,OAAM,GAAE,EAAC,GAAE,KAAI,GAAE,GAAE,GAAE,EAAC,GAAE,MAAK,GAAE,IAAG,GAAE,MAAK,GAAE,EAAC,GAAE,KAAI,GAAE,GAAE,GAAE,EAAC,GAAE,MAAK,GAAE,IAAG,GAAE,QAAO,GAAE,EAAC,GAAE,KAAI,GAAE,GAAE,GAAE,EAAC,GAAE,MAAK,GAAE,OAAM,CAAC,GAAEY,KAAED,GAAE,QAAOE,KAAE,GAAEA,KAAED,IAAEC,MAAG,GAAE;AAAC,gBAAIC,KAAEH,GAAEE,EAAC;AAAE,YAAAC,GAAE,MAAIN,KAAEF,KAAEJ,GAAED,EAAC,EAAE,KAAKI,IAAES,GAAE,GAAE,IAAE,IAAET,GAAE,KAAKJ,IAAEa,GAAE,GAAE,IAAE;AAAG,gBAAI,KAAGd,GAAE,YAAU,KAAK,OAAO,KAAK,IAAIQ,EAAC,CAAC;AAAE,gBAAGC,KAAED,KAAE,GAAE,KAAGM,GAAE,KAAG,CAACA,GAAE,GAAE;AAAC,mBAAG,KAAGD,KAAE,MAAIC,KAAEH,GAAEE,KAAE,CAAC;AAAG,kBAAI,IAAEH,GAAEI,GAAE,CAAC;AAAE,cAAAP,OAAI,IAAEA,GAAE,KAAG,CAAC,IAAG,IAAE,YAAU,OAAO,IAAE,EAAE,QAAQ,MAAK,CAAC,IAAE,EAAE,GAAEJ,IAAEW,GAAE,GAAEL,EAAC;AAAE;AAAA,YAAK;AAAA,UAAC;AAAC,cAAGN,GAAE,QAAO;AAAE,cAAIY,KAAEN,KAAEC,GAAE,SAAOA,GAAE;AAAK,iBAAM,cAAY,OAAOK,KAAEA,GAAE,CAAC,IAAEA,GAAE,QAAQ,MAAK,CAAC;AAAA,QAAC,GAAEZ,GAAE,KAAG,SAASH,IAAEC,IAAE;AAAC,iBAAOI,GAAEL,IAAEC,IAAE,MAAK,IAAE;AAAA,QAAC,GAAEE,GAAE,OAAK,SAASH,IAAEC,IAAE;AAAC,iBAAOI,GAAEL,IAAEC,IAAE,IAAI;AAAA,QAAC;AAAE,YAAIK,KAAE,SAASN,IAAE;AAAC,iBAAOA,GAAE,KAAGE,GAAE,IAAI,IAAEA,GAAE;AAAA,QAAC;AAAE,QAAAC,GAAE,QAAM,SAASH,IAAE;AAAC,iBAAO,KAAK,GAAGM,GAAE,IAAI,GAAEN,EAAC;AAAA,QAAC,GAAEG,GAAE,UAAQ,SAASH,IAAE;AAAC,iBAAO,KAAK,KAAKM,GAAE,IAAI,GAAEN,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;;;ACA54C;AAAA;AAAA,KAAC,SAASgB,IAAEC,IAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQA,GAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAOA,EAAC,KAAGD,KAAE,eAAa,OAAO,aAAW,aAAWA,MAAG,MAAM,4BAA0BC,GAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,aAAO,SAASD,IAAEC,IAAEC,IAAE;AAAC,QAAAA,GAAE,eAAa,SAASF,IAAEC,IAAE;AAAC,cAAIE,KAAED,GAAE,GAAGF,EAAC;AAAE,cAAGG,GAAE,SAAOF,KAAE,OAAO,KAAKA,EAAC,IAAE,CAAC,GAAG,QAAS,SAASD,IAAE;AAAC,YAAAG,GAAEH,EAAC,IAAEC,GAAED,EAAC;AAAA,UAAC,CAAE,GAAEG;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;;;ACG7X,IAAM,gBAAgB;EAC3B,gBAAgB;EAChB,uBAAuB;EACvB,wBAAwB;EACxB,eAAe;EACf,aAAa;EAEb,cAAc;IACZ,gBAAgB;IAChB,UAAU;IACV,iBAAiB;IACjB,UAAU;IACV,cAAc;IACd,MAAM;IACN,QAAQ;IACR,KAAK;IACL,SAAS;IACT,MAAM;;EAER,iBAAiB;IACf,MAAM;;EAER,iCAAiC,CAAC,UAAU,QAAQ;EACpD,QAAQ;IACN,sBAAsB;;EAExB,OAAO;IACL,KAAK;IACL,QAAQ;IACR,UAAU;IACV,SAAS;;EAEX,gBAAgB;IACd,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,QAAQ;;EAEV,eAAe;IACb,SAAS;IACT,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,SAAS;;EAEX,yBAAyB;IAEvB;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;;EAEF,mBAAmB;IACjB,qBAAqB;IACrB,WAAW;;EAEb,0BAA0B;EAC1B,yBACG,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,cACtD,QAAQ,IAAI,gCAAgC,IAC5C,WAAc;;;;ACzEtB,mBAAkB;AAClB,gBAA0B;AAC1B,0BAAyB;AACzB,0BAAyB;AAEzB,aAAAC,QAAM,OAAO,oBAAAC,OAAY;AACzB,aAAAD,QAAM,OAAO,oBAAAE,OAAY;AAEzB,IAAM,eAAe;EACnB,GAAG,UAAAC;EACH,MAAM;EACN,cAAc;IACZ,QAAQ;IACR,MAAM;IACN,GAAG;IACH,GAAG;IACH,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,GAAG;IACH,IAAI;;;AAmBR,aAAAC,QAAM,OAAO,iBAAiB,YAAY;;;ACxCnC,IAAM,cAAc;EACzB,sBAAsB,eAA6B;AACjD,WAAO,gBAAgB,OAAO,cAAc,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI;EAC/D;EAEA,gBAAgB,SAAwB;AACtC,WAAO,OAAO,YAAY,WACtB,KAAK,sBAAsB,OAAwB,IACnD;EACN;EAEA,uBAAuB,UAAqC,WAAyB;AACnF,YAAO,qCAAU,OAAO,aAAW,QAAQ,mBAAmB,eAAc,CAAA;EAC9E;EAEA,2BAA2B,UAAqC,WAAyB;AACvF,WAAO,KAAK,uBAAuB,UAAU,SAAS,EAAE,CAAC;EAC3D;EAEA,8BACE,cACA,eAA4B;AAxBhC;AA0BI,QAAI,CAAC,eAAe;AAClB,aAAO;IACT;AAEA,UAAM,cAAc,aAAa,KAAK,aAAW,QAAQ,kBAAkB,aAAa;AAExF,QAAI,aAAa;AACf,aAAO,YAAY;IACrB;AAEA,UAAM,CAAC,SAAS,IAAI,cAAc,MAAM,GAAG;AAE3C,aAAO,mBAAc,mBAAd,mBAA+B,eAAc;EACtD;;;;ACtBF,IAAI,KAAK;AAAT,IAUE,KAAK;AAVP,IAaE,SAAS;AAbX,IAgBE,YAAY;AAhBd,IAuBE,KAAK;AAvBP,IA8BE,KAAK;AA9BP,IAqCE,SAAS;AArCX,IA4CE,OAAO;AA5CT,IA6CE,UAAU,OAAO;AA7CnB,IA8CE,aAAa,UAAU;AA9CzB,IA+CE,aAAa,UAAU;AA/CzB,IAgDE,cAAc,OAAO;AAhDvB,IAmDE,IAAI,CAAC;AAnDP,IAoDE,YAAY;AApDd,IAqDE,UAAU;AAMZ,SAAS,QAAQ;AAQf,WAASC,KAAIC,IAAG;AACd,QAAI,IAAI;AAGR,QAAI,EAAE,aAAaD,MAAM,QAAOC,OAAM,YAAY,MAAM,IAAI,IAAID,KAAIC,EAAC;AAGrE,QAAIA,cAAaD,MAAK;AACpB,QAAE,IAAIC,GAAE;AACR,QAAE,IAAIA,GAAE;AACR,QAAE,IAAIA,GAAE,EAAE,MAAM;AAAA,IAClB,OAAO;AACL,UAAI,OAAOA,OAAM,UAAU;AACzB,YAAID,KAAI,WAAW,QAAQ,OAAOC,OAAM,UAAU;AAChD,gBAAM,UAAU,UAAU,OAAO;AAAA,QACnC;AAGA,QAAAA,KAAIA,OAAM,KAAK,IAAIA,KAAI,IAAI,OAAO,OAAOA,EAAC;AAAA,MAC5C;AAEA,YAAM,GAAGA,EAAC;AAAA,IACZ;AAIA,MAAE,cAAcD;AAAA,EAClB;AAEA,EAAAA,KAAI,YAAY;AAChB,EAAAA,KAAI,KAAK;AACT,EAAAA,KAAI,KAAK;AACT,EAAAA,KAAI,KAAK;AACT,EAAAA,KAAI,KAAK;AACT,EAAAA,KAAI,SAAS;AACb,EAAAA,KAAI,YAAY;AAChB,EAAAA,KAAI,cAAc;AAClB,EAAAA,KAAI,gBAAgB;AACpB,EAAAA,KAAI,UAAU;AAEd,SAAOA;AACT;AASA,SAAS,MAAM,GAAGC,IAAG;AACnB,MAAIC,IAAGC,IAAG;AAEV,MAAI,CAAC,QAAQ,KAAKF,EAAC,GAAG;AACpB,UAAM,MAAM,UAAU,QAAQ;AAAA,EAChC;AAGA,IAAE,IAAIA,GAAE,OAAO,CAAC,KAAK,OAAOA,KAAIA,GAAE,MAAM,CAAC,GAAG,MAAM;AAGlD,OAAKC,KAAID,GAAE,QAAQ,GAAG,KAAK,GAAI,CAAAA,KAAIA,GAAE,QAAQ,KAAK,EAAE;AAGpD,OAAKE,KAAIF,GAAE,OAAO,IAAI,KAAK,GAAG;AAG5B,QAAIC,KAAI,EAAG,CAAAA,KAAIC;AACf,IAAAD,MAAK,CAACD,GAAE,MAAME,KAAI,CAAC;AACnB,IAAAF,KAAIA,GAAE,UAAU,GAAGE,EAAC;AAAA,EACtB,WAAWD,KAAI,GAAG;AAGhB,IAAAA,KAAID,GAAE;AAAA,EACR;AAEA,OAAKA,GAAE;AAGP,OAAKE,KAAI,GAAGA,KAAI,MAAMF,GAAE,OAAOE,EAAC,KAAK,MAAM,GAAEA;AAE7C,MAAIA,MAAK,IAAI;AAGX,MAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,EAChB,OAAO;AAGL,WAAO,KAAK,KAAKF,GAAE,OAAO,EAAE,EAAE,KAAK,MAAK;AACxC,MAAE,IAAIC,KAAIC,KAAI;AACd,MAAE,IAAI,CAAC;AAGP,SAAKD,KAAI,GAAGC,MAAK,KAAK,GAAE,EAAED,IAAG,IAAI,CAACD,GAAE,OAAOE,IAAG;AAAA,EAChD;AAEA,SAAO;AACT;AAWA,SAAS,MAAM,GAAG,IAAI,IAAI,MAAM;AAC9B,MAAIC,MAAK,EAAE;AAEX,MAAI,OAAO,UAAW,MAAK,EAAE,YAAY;AACzC,MAAI,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,GAAG;AAChD,UAAM,MAAM,UAAU;AAAA,EACxB;AAEA,MAAI,KAAK,GAAG;AACV,WACE,OAAO,MAAM,QAAQ,CAAC,CAACA,IAAG,CAAC,MAAM,OAAO,MACxC,OAAO,KAAKA,IAAG,CAAC,KAAK,KACrB,OAAO,MAAMA,IAAG,CAAC,IAAI,KAAKA,IAAG,CAAC,MAAM,MAAM,QAAQA,IAAG,CAAC,MAAM;AAG9D,IAAAA,IAAG,SAAS;AAEZ,QAAI,MAAM;AAGR,QAAE,IAAI,EAAE,IAAI,KAAK;AACjB,MAAAA,IAAG,CAAC,IAAI;AAAA,IACV,OAAO;AAGL,MAAAA,IAAG,CAAC,IAAI,EAAE,IAAI;AAAA,IAChB;AAAA,EACF,WAAW,KAAKA,IAAG,QAAQ;AAGzB,WACE,OAAO,KAAKA,IAAG,EAAE,KAAK,KACtB,OAAO,MAAMA,IAAG,EAAE,IAAI,KAAKA,IAAG,EAAE,MAAM,MACnC,QAAQA,IAAG,KAAK,CAAC,MAAM,aAAaA,IAAG,KAAK,CAAC,IAAI,OACpD,OAAO,MAAM,QAAQ,CAAC,CAACA,IAAG,CAAC;AAG7B,IAAAA,IAAG,SAAS;AAGZ,QAAI,MAAM;AAGR,aAAO,EAAEA,IAAG,EAAE,EAAE,IAAI,KAAI;AACtB,QAAAA,IAAG,EAAE,IAAI;AACT,YAAI,OAAO,GAAG;AACZ,YAAE,EAAE;AACJ,UAAAA,IAAG,QAAQ,CAAC;AACZ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,SAAK,KAAKA,IAAG,QAAQ,CAACA,IAAG,EAAE,EAAE,IAAI,CAAAA,IAAG,IAAI;AAAA,EAC1C;AAEA,SAAO;AACT;AAOA,SAAS,UAAU,GAAG,eAAe,WAAW;AAC9C,MAAIF,KAAI,EAAE,GACRG,KAAI,EAAE,EAAE,KAAK,EAAE,GACfJ,KAAII,GAAE;AAGR,MAAI,eAAe;AACjB,IAAAA,KAAIA,GAAE,OAAO,CAAC,KAAKJ,KAAI,IAAI,MAAMI,GAAE,MAAM,CAAC,IAAI,OAAOH,KAAI,IAAI,MAAM,QAAQA;AAAA,EAG7E,WAAWA,KAAI,GAAG;AAChB,WAAO,EAAEA,KAAI,CAAAG,KAAI,MAAMA;AACvB,IAAAA,KAAI,OAAOA;AAAA,EACb,WAAWH,KAAI,GAAG;AAChB,QAAI,EAAEA,KAAID,IAAG;AACX,WAAKC,MAAKD,IAAGC,OAAM,CAAAG,MAAK;AAAA,IAC1B,WAAWH,KAAID,IAAG;AAChB,MAAAI,KAAIA,GAAE,MAAM,GAAGH,EAAC,IAAI,MAAMG,GAAE,MAAMH,EAAC;AAAA,IACrC;AAAA,EACF,WAAWD,KAAI,GAAG;AAChB,IAAAI,KAAIA,GAAE,OAAO,CAAC,IAAI,MAAMA,GAAE,MAAM,CAAC;AAAA,EACnC;AAEA,SAAO,EAAE,IAAI,KAAK,YAAY,MAAMA,KAAIA;AAC1C;AASA,EAAE,MAAM,WAAY;AAClB,MAAI,IAAI,IAAI,KAAK,YAAY,IAAI;AACjC,IAAE,IAAI;AACN,SAAO;AACT;AAQA,EAAE,MAAM,SAAUC,IAAG;AACnB,MAAI,OACF,IAAI,MACJF,MAAK,EAAE,GACPG,OAAMD,KAAI,IAAI,EAAE,YAAYA,EAAC,GAAG,GAChCH,KAAI,EAAE,GACN,IAAIG,GAAE,GACNE,KAAI,EAAE,GACNC,KAAIH,GAAE;AAGR,MAAI,CAACF,IAAG,CAAC,KAAK,CAACG,IAAG,CAAC,EAAG,QAAO,CAACH,IAAG,CAAC,IAAI,CAACG,IAAG,CAAC,IAAI,IAAI,CAAC,IAAIJ;AAGxD,MAAIA,MAAK,EAAG,QAAOA;AAEnB,UAAQA,KAAI;AAGZ,MAAIK,MAAKC,GAAG,QAAOD,KAAIC,KAAI,QAAQ,IAAI;AAEvC,OAAKD,KAAIJ,IAAG,WAAWK,KAAIF,IAAG,UAAUC,KAAIC;AAG5C,OAAKN,KAAI,IAAI,EAAEA,KAAI,KAAI;AACrB,QAAIC,IAAGD,EAAC,KAAKI,IAAGJ,EAAC,EAAG,QAAOC,IAAGD,EAAC,IAAII,IAAGJ,EAAC,IAAI,QAAQ,IAAI;AAAA,EACzD;AAGA,SAAOK,MAAKC,KAAI,IAAID,KAAIC,KAAI,QAAQ,IAAI;AAC1C;AAOA,EAAE,MAAM,SAAUH,IAAG;AACnB,MAAI,IAAI,MACNN,OAAM,EAAE,aACR,IAAI,EAAE,GACN,KAAKM,KAAI,IAAIN,KAAIM,EAAC,GAAG,GACrBE,KAAI,EAAE,KAAKF,GAAE,IAAI,IAAI,IACrB,KAAKN,KAAI;AAEX,MAAI,OAAO,CAAC,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ;AACxC,UAAM,MAAM,UAAU;AAAA,EACxB;AAGA,MAAI,CAAC,EAAE,CAAC,GAAG;AACT,UAAM,MAAM,WAAW;AAAA,EACzB;AAGA,MAAI,CAAC,EAAE,CAAC,GAAG;AACT,IAAAM,GAAE,IAAIE;AACN,IAAAF,GAAE,IAAI,CAACA,GAAE,IAAI,CAAC;AACd,WAAOA;AAAA,EACT;AAEA,MAAI,IAAII,KAAIT,IAAG,KAAKU,KAClB,KAAK,EAAE,MAAM,GACbC,MAAK,KAAK,EAAE,QACZ,KAAK,EAAE,QACPC,KAAI,EAAE,MAAM,GAAG,EAAE,GACjB,KAAKA,GAAE,QACPC,KAAIR,IACJS,MAAKD,GAAE,IAAI,CAAC,GACZE,MAAK,GACL,IAAI,MAAMF,GAAE,IAAI,EAAE,IAAIR,GAAE,KAAK;AAE/B,EAAAQ,GAAE,IAAIN;AACN,EAAAA,KAAI,IAAI,IAAI,IAAI;AAGhB,KAAG,QAAQ,CAAC;AAGZ,SAAO,OAAO,KAAK,CAAAK,GAAE,KAAK,CAAC;AAE3B,KAAG;AAGD,SAAKZ,KAAI,GAAGA,KAAI,IAAIA,MAAK;AAGvB,UAAI,OAAO,KAAKY,GAAE,SAAS;AACzB,cAAM,KAAK,KAAK,IAAI;AAAA,MACtB,OAAO;AACL,aAAKF,MAAK,IAAI,MAAM,GAAG,EAAEA,MAAK,MAAK;AACjC,cAAI,EAAEA,GAAE,KAAKE,GAAEF,GAAE,GAAG;AAClB,kBAAM,EAAEA,GAAE,IAAIE,GAAEF,GAAE,IAAI,IAAI;AAC1B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,UAAI,MAAM,GAAG;AAIX,aAAKD,MAAK,MAAM,KAAK,IAAI,IAAI,MAAK;AAChC,cAAIG,GAAE,EAAE,EAAE,IAAIH,IAAG,EAAE,GAAG;AACpB,YAAAC,MAAK;AACL,mBAAOA,OAAM,CAACE,GAAE,EAAEF,GAAE,IAAI,CAAAE,GAAEF,GAAE,IAAI;AAChC,cAAEE,GAAEF,GAAE;AACN,YAAAE,GAAE,EAAE,KAAK;AAAA,UACX;AACA,UAAAA,GAAE,EAAE,KAAKH,IAAG,EAAE;AAAA,QAChB;AAEA,eAAO,CAACG,GAAE,CAAC,IAAI,CAAAA,GAAE,MAAM;AAAA,MACzB,OAAO;AACL;AAAA,MACF;AAAA,IACF;AAGA,IAAAE,IAAGC,KAAI,IAAI,MAAMf,KAAI,EAAEA;AAGvB,QAAIY,GAAE,CAAC,KAAK,IAAK,CAAAA,GAAE,EAAE,IAAI,EAAED,GAAE,KAAK;AAAA,QAC7B,CAAAC,KAAI,CAAC,EAAED,GAAE,CAAC;AAAA,EAEjB,UAAUA,QAAO,MAAMC,GAAE,CAAC,MAAM,cAAcL;AAG9C,MAAI,CAACO,IAAG,CAAC,KAAKC,OAAM,GAAG;AAGrB,IAAAD,IAAG,MAAM;AACT,IAAAD,GAAE;AACF;AAAA,EACF;AAGA,MAAIE,MAAK,EAAG,OAAMF,IAAG,GAAGd,KAAI,IAAIa,GAAE,CAAC,MAAM,SAAS;AAElD,SAAOC;AACT;AAMA,EAAE,KAAK,SAAUR,IAAG;AAClB,SAAO,KAAK,IAAIA,EAAC,MAAM;AACzB;AAOA,EAAE,KAAK,SAAUA,IAAG;AAClB,SAAO,KAAK,IAAIA,EAAC,IAAI;AACvB;AAOA,EAAE,MAAM,SAAUA,IAAG;AACnB,SAAO,KAAK,IAAIA,EAAC,IAAI;AACvB;AAMA,EAAE,KAAK,SAAUA,IAAG;AAClB,SAAO,KAAK,IAAIA,EAAC,IAAI;AACvB;AAOA,EAAE,MAAM,SAAUA,IAAG;AACnB,SAAO,KAAK,IAAIA,EAAC,IAAI;AACvB;AAMA,EAAE,QAAQ,EAAE,MAAM,SAAUA,IAAG;AAC7B,MAAIH,IAAG,GAAGc,IAAG,MACX,IAAI,MACJjB,OAAM,EAAE,aACR,IAAI,EAAE,GACN,KAAKM,KAAI,IAAIN,KAAIM,EAAC,GAAG;AAGvB,MAAI,KAAK,GAAG;AACV,IAAAA,GAAE,IAAI,CAAC;AACP,WAAO,EAAE,KAAKA,EAAC;AAAA,EACjB;AAEA,MAAIF,MAAK,EAAE,EAAE,MAAM,GACjBc,MAAK,EAAE,GACPX,MAAKD,GAAE,GACPa,MAAKb,GAAE;AAGT,MAAI,CAACF,IAAG,CAAC,KAAK,CAACG,IAAG,CAAC,GAAG;AACpB,QAAIA,IAAG,CAAC,GAAG;AACT,MAAAD,GAAE,IAAI,CAAC;AAAA,IACT,WAAWF,IAAG,CAAC,GAAG;AAChB,MAAAE,KAAI,IAAIN,KAAI,CAAC;AAAA,IACf,OAAO;AACL,MAAAM,GAAE,IAAI;AAAA,IACR;AACA,WAAOA;AAAA,EACT;AAGA,MAAI,IAAIY,MAAKC,KAAI;AAEf,QAAI,OAAO,IAAI,GAAG;AAChB,UAAI,CAAC;AACL,MAAAF,KAAIb;AAAA,IACN,OAAO;AACL,MAAAe,MAAKD;AACL,MAAAD,KAAIV;AAAA,IACN;AAEA,IAAAU,GAAE,QAAQ;AACV,SAAK,IAAI,GAAG,MAAM,CAAAA,GAAE,KAAK,CAAC;AAC1B,IAAAA,GAAE,QAAQ;AAAA,EACZ,OAAO;AAGL,UAAM,OAAOb,IAAG,SAASG,IAAG,UAAUH,MAAKG,KAAI;AAE/C,SAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAIH,IAAG,CAAC,KAAKG,IAAG,CAAC,GAAG;AAClB,eAAOH,IAAG,CAAC,IAAIG,IAAG,CAAC;AACnB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,MAAI,MAAM;AACR,IAAAU,KAAIb;AACJ,IAAAA,MAAKG;AACL,IAAAA,MAAKU;AACL,IAAAX,GAAE,IAAI,CAACA,GAAE;AAAA,EACX;AAMA,OAAK,KAAK,IAAIC,IAAG,WAAWJ,KAAIC,IAAG,WAAW,EAAG,QAAO,MAAM,CAAAA,IAAGD,IAAG,IAAI;AAGxE,OAAK,IAAIA,IAAG,IAAI,KAAI;AAClB,QAAIC,IAAG,EAAE,CAAC,IAAIG,IAAG,CAAC,GAAG;AACnB,WAAKJ,KAAI,GAAGA,MAAK,CAACC,IAAG,EAAED,EAAC,IAAI,CAAAC,IAAGD,EAAC,IAAI;AACpC,QAAEC,IAAGD,EAAC;AACN,MAAAC,IAAG,CAAC,KAAK;AAAA,IACX;AAEA,IAAAA,IAAG,CAAC,KAAKG,IAAG,CAAC;AAAA,EACf;AAGA,SAAOH,IAAG,EAAE,CAAC,MAAM,IAAI,CAAAA,IAAG,IAAI;AAG9B,SAAOA,IAAG,CAAC,MAAM,KAAI;AACnB,IAAAA,IAAG,MAAM;AACT,MAAEe;AAAA,EACJ;AAEA,MAAI,CAACf,IAAG,CAAC,GAAG;AAGV,IAAAE,GAAE,IAAI;AAGN,IAAAF,MAAK,CAACe,MAAK,CAAC;AAAA,EACd;AAEA,EAAAb,GAAE,IAAIF;AACN,EAAAE,GAAE,IAAIa;AAEN,SAAOb;AACT;AAMA,EAAE,MAAM,SAAUA,IAAG;AACnB,MAAI,MACF,IAAI,MACJN,OAAM,EAAE,aACR,IAAI,EAAE,GACN,KAAKM,KAAI,IAAIN,KAAIM,EAAC,GAAG;AAEvB,MAAI,CAACA,GAAE,EAAE,CAAC,GAAG;AACX,UAAM,MAAM,WAAW;AAAA,EACzB;AAEA,IAAE,IAAIA,GAAE,IAAI;AACZ,SAAOA,GAAE,IAAI,CAAC,KAAK;AACnB,IAAE,IAAI;AACN,EAAAA,GAAE,IAAI;AAEN,MAAI,KAAM,QAAO,IAAIN,KAAI,CAAC;AAE1B,MAAIA,KAAI;AACR,MAAIA,KAAI;AACR,EAAAA,KAAI,KAAKA,KAAI,KAAK;AAClB,MAAI,EAAE,IAAIM,EAAC;AACX,EAAAN,KAAI,KAAK;AACT,EAAAA,KAAI,KAAK;AAET,SAAO,KAAK,MAAM,EAAE,MAAMM,EAAC,CAAC;AAC9B;AAMA,EAAE,MAAM,WAAY;AAClB,MAAI,IAAI,IAAI,KAAK,YAAY,IAAI;AACjC,IAAE,IAAI,CAAC,EAAE;AACT,SAAO;AACT;AAMA,EAAE,OAAO,EAAE,MAAM,SAAUA,IAAG;AAC5B,MAAIJ,IAAGM,IAAGS,IACR,IAAI,MACJjB,OAAM,EAAE;AAEV,EAAAM,KAAI,IAAIN,KAAIM,EAAC;AAGb,MAAI,EAAE,KAAKA,GAAE,GAAG;AACd,IAAAA,GAAE,IAAI,CAACA,GAAE;AACT,WAAO,EAAE,MAAMA,EAAC;AAAA,EAClB;AAEA,MAAIY,MAAK,EAAE,GACTd,MAAK,EAAE,GACPe,MAAKb,GAAE,GACPC,MAAKD,GAAE;AAGT,MAAI,CAACF,IAAG,CAAC,KAAK,CAACG,IAAG,CAAC,GAAG;AACpB,QAAI,CAACA,IAAG,CAAC,GAAG;AACV,UAAIH,IAAG,CAAC,GAAG;AACT,QAAAE,KAAI,IAAIN,KAAI,CAAC;AAAA,MACf,OAAO;AACL,QAAAM,GAAE,IAAI,EAAE;AAAA,MACV;AAAA,IACF;AACA,WAAOA;AAAA,EACT;AAEA,EAAAF,MAAKA,IAAG,MAAM;AAId,MAAIF,KAAIgB,MAAKC,KAAI;AACf,QAAIjB,KAAI,GAAG;AACT,MAAAiB,MAAKD;AACL,MAAAD,KAAIV;AAAA,IACN,OAAO;AACL,MAAAL,KAAI,CAACA;AACL,MAAAe,KAAIb;AAAA,IACN;AAEA,IAAAa,GAAE,QAAQ;AACV,WAAOf,OAAM,CAAAe,GAAE,KAAK,CAAC;AACrB,IAAAA,GAAE,QAAQ;AAAA,EACZ;AAGA,MAAIb,IAAG,SAASG,IAAG,SAAS,GAAG;AAC7B,IAAAU,KAAIV;AACJ,IAAAA,MAAKH;AACL,IAAAA,MAAKa;AAAA,EACP;AAEA,EAAAf,KAAIK,IAAG;AAGP,OAAKC,KAAI,GAAGN,IAAGE,IAAGF,EAAC,KAAK,GAAI,CAAAM,MAAKJ,IAAG,EAAEF,EAAC,IAAIE,IAAGF,EAAC,IAAIK,IAAGL,EAAC,IAAIM,MAAK,KAAK;AAIrE,MAAIA,IAAG;AACL,IAAAJ,IAAG,QAAQI,EAAC;AACZ,MAAEW;AAAA,EACJ;AAGA,OAAKjB,KAAIE,IAAG,QAAQA,IAAG,EAAEF,EAAC,MAAM,IAAI,CAAAE,IAAG,IAAI;AAE3C,EAAAE,GAAE,IAAIF;AACN,EAAAE,GAAE,IAAIa;AAEN,SAAOb;AACT;AAUA,EAAE,MAAM,SAAUL,IAAG;AACnB,MAAI,IAAI,MACN,MAAM,IAAI,EAAE,YAAY,GAAG,GAC3BK,KAAI,KACJ,QAAQL,KAAI;AAEd,MAAIA,OAAM,CAAC,CAACA,MAAKA,KAAI,CAAC,aAAaA,KAAI,WAAW;AAChD,UAAM,MAAM,UAAU,UAAU;AAAA,EAClC;AAEA,MAAI,MAAO,CAAAA,KAAI,CAACA;AAEhB,aAAS;AACP,QAAIA,KAAI,EAAG,CAAAK,KAAIA,GAAE,MAAM,CAAC;AACxB,IAAAL,OAAM;AACN,QAAI,CAACA,GAAG;AACR,QAAI,EAAE,MAAM,CAAC;AAAA,EACf;AAEA,SAAO,QAAQ,IAAI,IAAIK,EAAC,IAAIA;AAC9B;AAUA,EAAE,OAAO,SAAU,IAAI,IAAI;AACzB,MAAI,OAAO,CAAC,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ;AACxC,UAAM,MAAM,UAAU,WAAW;AAAA,EACnC;AACA,SAAO,MAAM,IAAI,KAAK,YAAY,IAAI,GAAG,IAAI,EAAE;AACjD;AAYA,EAAE,QAAQ,SAAU,IAAI,IAAI;AAC1B,MAAI,OAAO,UAAW,MAAK;AAAA,WAClB,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,UAAU,KAAK,QAAQ;AACnD,UAAM,MAAM,UAAU;AAAA,EACxB;AACA,SAAO,MAAM,IAAI,KAAK,YAAY,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,EAAE;AAC9D;AAOA,EAAE,OAAO,WAAY;AACnB,MAAIO,IAAGO,IAAGH,IACR,IAAI,MACJjB,OAAM,EAAE,aACRK,KAAI,EAAE,GACNH,KAAI,EAAE,GACN,OAAO,IAAIF,KAAI,KAAK;AAGtB,MAAI,CAAC,EAAE,EAAE,CAAC,EAAG,QAAO,IAAIA,KAAI,CAAC;AAG7B,MAAIK,KAAI,GAAG;AACT,UAAM,MAAM,OAAO,gBAAgB;AAAA,EACrC;AAGA,EAAAA,KAAI,KAAK,KAAK,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC;AAIvC,MAAIA,OAAM,KAAKA,OAAM,IAAI,GAAG;AAC1B,IAAAe,KAAI,EAAE,EAAE,KAAK,EAAE;AACf,QAAI,EAAEA,GAAE,SAASlB,KAAI,GAAI,CAAAkB,MAAK;AAC9B,IAAAf,KAAI,KAAK,KAAKe,EAAC;AACf,IAAAlB,OAAMA,KAAI,KAAK,IAAI,MAAMA,KAAI,KAAKA,KAAI;AACtC,IAAAW,KAAI,IAAIb,MAAKK,MAAK,IAAI,IAAI,QAAQA,KAAIA,GAAE,cAAc,GAAG,MAAM,GAAGA,GAAE,QAAQ,GAAG,IAAI,CAAC,KAAKH,EAAC;AAAA,EAC5F,OAAO;AACL,IAAAW,KAAI,IAAIb,KAAIK,KAAI,EAAE;AAAA,EACpB;AAEA,EAAAH,KAAIW,GAAE,KAAKb,KAAI,MAAM;AAGrB,KAAG;AACD,IAAAiB,KAAIJ;AACJ,IAAAA,KAAI,KAAK,MAAMI,GAAE,KAAK,EAAE,IAAIA,EAAC,CAAC,CAAC;AAAA,EACjC,SAASA,GAAE,EAAE,MAAM,GAAGf,EAAC,EAAE,KAAK,EAAE,MAAMW,GAAE,EAAE,MAAM,GAAGX,EAAC,EAAE,KAAK,EAAE;AAE7D,SAAO,MAAMW,KAAIb,KAAI,MAAM,KAAKa,GAAE,IAAI,GAAGb,KAAI,EAAE;AACjD;AAMA,EAAE,QAAQ,EAAE,MAAM,SAAUM,IAAG;AAC7B,MAAIc,IACF,IAAI,MACJpB,OAAM,EAAE,aACRI,MAAK,EAAE,GACPG,OAAMD,KAAI,IAAIN,KAAIM,EAAC,GAAG,GACtB,IAAIF,IAAG,QACP,IAAIG,IAAG,QACPJ,KAAI,EAAE,GACN,IAAIG,GAAE;AAGR,EAAAA,GAAE,IAAI,EAAE,KAAKA,GAAE,IAAI,IAAI;AAGvB,MAAI,CAACF,IAAG,CAAC,KAAK,CAACG,IAAG,CAAC,GAAG;AACpB,IAAAD,GAAE,IAAI,CAACA,GAAE,IAAI,CAAC;AACd,WAAOA;AAAA,EACT;AAGA,EAAAA,GAAE,IAAIH,KAAI;AAGV,MAAI,IAAI,GAAG;AACT,IAAAiB,KAAIhB;AACJ,IAAAA,MAAKG;AACL,IAAAA,MAAKa;AACL,QAAI;AACJ,QAAI;AACJ,QAAI;AAAA,EACN;AAGA,OAAKA,KAAI,IAAI,MAAM,IAAI,IAAI,CAAC,GAAG,MAAM,CAAAA,GAAE,CAAC,IAAI;AAK5C,OAAKjB,KAAI,GAAGA,QAAM;AAChB,QAAI;AAGJ,SAAK,IAAI,IAAIA,IAAG,IAAIA,MAAI;AAGtB,UAAIiB,GAAE,CAAC,IAAIb,IAAGJ,EAAC,IAAIC,IAAG,IAAID,KAAI,CAAC,IAAI;AACnC,MAAAiB,GAAE,GAAG,IAAI,IAAI;AAGb,UAAI,IAAI,KAAK;AAAA,IACf;AAEA,IAAAA,GAAE,CAAC,IAAI;AAAA,EACT;AAGA,MAAI,EAAG,GAAEd,GAAE;AAAA,MACN,CAAAc,GAAE,MAAM;AAGb,OAAKjB,KAAIiB,GAAE,QAAQ,CAACA,GAAE,EAAEjB,EAAC,IAAI,CAAAiB,GAAE,IAAI;AACnC,EAAAd,GAAE,IAAIc;AAEN,SAAOd;AACT;AAUA,EAAE,gBAAgB,SAAU,IAAI,IAAI;AAClC,MAAI,IAAI,MACNL,KAAI,EAAE,EAAE,CAAC;AAEX,MAAI,OAAO,WAAW;AACpB,QAAI,OAAO,CAAC,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ;AACxC,YAAM,MAAM,UAAU;AAAA,IACxB;AACA,QAAI,MAAM,IAAI,EAAE,YAAY,CAAC,GAAG,EAAE,IAAI,EAAE;AACxC,WAAO,EAAE,EAAE,SAAS,KAAK,GAAE,EAAE,KAAK,CAAC;AAAA,EACrC;AAEA,SAAO,UAAU,GAAG,MAAM,CAAC,CAACA,EAAC;AAC/B;AAaA,EAAE,UAAU,SAAU,IAAI,IAAI;AAC5B,MAAI,IAAI,MACNA,KAAI,EAAE,EAAE,CAAC;AAEX,MAAI,OAAO,WAAW;AACpB,QAAI,OAAO,CAAC,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ;AACxC,YAAM,MAAM,UAAU;AAAA,IACxB;AACA,QAAI,MAAM,IAAI,EAAE,YAAY,CAAC,GAAG,KAAK,EAAE,IAAI,GAAG,EAAE;AAGhD,SAAK,KAAK,KAAK,EAAE,IAAI,GAAG,EAAE,EAAE,SAAS,KAAK,GAAE,EAAE,KAAK,CAAC;AAAA,EACtD;AAEA,SAAO,UAAU,GAAG,OAAO,CAAC,CAACA,EAAC;AAChC;AASA,EAAE,OAAO,IAAI,4BAA4B,CAAC,IAAI,EAAE,SAAS,EAAE,WAAW,WAAY;AAChF,MAAI,IAAI,MACND,OAAM,EAAE;AACV,SAAO,UAAU,GAAG,EAAE,KAAKA,KAAI,MAAM,EAAE,KAAKA,KAAI,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9D;AAMA,EAAE,WAAW,WAAY;AACvB,MAAIC,KAAI,CAAC,UAAU,MAAM,MAAM,IAAI;AACnC,MAAI,KAAK,YAAY,WAAW,QAAQ,CAAC,KAAK,GAAGA,GAAE,SAAS,CAAC,GAAG;AAC9D,UAAM,MAAM,OAAO,sBAAsB;AAAA,EAC3C;AACA,SAAOA;AACT;AAYA,EAAE,cAAc,SAAU,IAAI,IAAI;AAChC,MAAI,IAAI,MACND,OAAM,EAAE,aACRC,KAAI,EAAE,EAAE,CAAC;AAEX,MAAI,OAAO,WAAW;AACpB,QAAI,OAAO,CAAC,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ;AACxC,YAAM,MAAM,UAAU,WAAW;AAAA,IACnC;AACA,QAAI,MAAM,IAAID,KAAI,CAAC,GAAG,IAAI,EAAE;AAC5B,WAAO,EAAE,EAAE,SAAS,KAAK,GAAE,EAAE,KAAK,CAAC;AAAA,EACrC;AAEA,SAAO,UAAU,GAAG,MAAM,EAAE,KAAK,EAAE,KAAKA,KAAI,MAAM,EAAE,KAAKA,KAAI,IAAI,CAAC,CAACC,EAAC;AACtE;AASA,EAAE,UAAU,WAAY;AACtB,MAAI,IAAI,MACND,OAAM,EAAE;AACV,MAAIA,KAAI,WAAW,MAAM;AACvB,UAAM,MAAM,OAAO,oBAAoB;AAAA,EACzC;AACA,SAAO,UAAU,GAAG,EAAE,KAAKA,KAAI,MAAM,EAAE,KAAKA,KAAI,IAAI,IAAI;AAC1D;AAMO,IAAI,MAAM,MAAM;AAGvB,IAAO,cAAQ;;;AChgCR,IAAM,aAAa;EAExB,UAAU,OAAwC;AAChD,QAAI,CAAC,OAAO;AACV,aAAO,IAAI,YAAI,CAAC;IAClB;AAEA,WAAO,IAAI,YAAI,KAAK;EACtB;EASA,SAAS,GAAsC,GAAoC;AACjF,QAAI,MAAM,UAAa,MAAM,QAAW;AACtC,aAAO,IAAI,YAAI,CAAC;IAClB;AAEA,UAAM,aAAa,IAAI,YAAI,CAAC;AAC5B,UAAM,aAAa,IAAI,YAAI,CAAC;AAE5B,WAAO,WAAW,MAAM,UAAU;EACpC;EAOA,0BAA0B,OAAoC,WAAW,GAAC;AACxE,QAAI,UAAU,QAAW;AACvB,aAAO;IACT;AAEA,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,MAAM,eAAe,SAAS;QACnC,uBAAuB;QACvB,uBAAuB;OACxB;IACH;AAEA,WAAO,WAAW,KAAK,EAAE,eAAe,SAAS;MAC/C,uBAAuB;MACvB,uBAAuB;KACxB;EACH;EAMA,yBAAyB,OAAyB;AAChD,QAAI,UAAU,QAAW;AACvB,aAAO;IACT;AAGA,WAAO,WAAW,MAAM,QAAQ,OAAO,EAAE,CAAC;EAC5C;;;;AChEK,IAAM,WAAW;EACtB;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;IAGV,SAAS;MACP;QACE,MAAM;QACN,MAAM;;;;EAIZ;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;IAGV,SAAS;MACP;QACE,MAAM;QACN,MAAM;;;;;;;AC3CP,IAAM,UAAU;EACrB;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN,EAAE,MAAM,WAAW,MAAM,UAAS;MAClC,EAAE,MAAM,UAAU,MAAM,UAAS;;IAEnC,SAAS,CAAC,EAAE,MAAM,OAAM,CAAE;;;;;ACTvB,IAAM,UAAU;EACrB;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;IAGV,SAAS,CAAA;;EAEX;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;IAGV,SAAS;MACP;QACE,MAAM;QACN,MAAM;;;;;;;ACjCP,IAAM,eAAe;EAC1B,aAAa,CAAC,iBAAwB;AAEpC,QAAI,cAAc,wBAAwB,SAAS,YAAY,GAAG;AAChE,aAAO;IACT;AAEA,WAAO;EACT;EAEA,YAAY,MAAM;;;;ACVd,IAAO,UAAP,MAAO,SAAO;EAIX,GAAwB,WAAc,UAAqC;AAJpF;AAKI,QAAI,CAAC,SAAQ,eAAe,IAAI,SAAS,GAAG;AAC1C,eAAQ,eAAe,IAAI,WAAW,oBAAI,IAAG,CAAE;IACjD;AACA,mBAAQ,eAAe,IAAI,SAAS,MAApC,mBAAuC,IAAI;EAC7C;EAEO,IAAyB,WAAc,UAAqC;AACjF,UAAM,YAAY,SAAQ,eAAe,IAAI,SAAS;AACtD,QAAI,WAAW;AACb,gBAAU,OAAO,QAAQ;IAC3B;EACF;EAEO,KAA0B,WAAc,MAAmB;AAChE,UAAM,YAAY,SAAQ,eAAe,IAAI,SAAS;AACtD,QAAI,WAAW;AACb,gBAAU,QAAQ,cAAY,SAAS,IAAI,CAAC;IAC9C;EACF;EAEO,MAAM,WAAoB;AAC/B,aAAQ,eAAe,OAAO,SAAS;EACzC;EAEO,WAAQ;AACb,aAAQ,eAAe,MAAK;EAC9B;;AA7Be,QAAA,iBAAiB,oBAAI,IAAG;;;ACMlC,IAAM,YAAY;EACvB,oBAAoB,SAAe;AADrC;AAEI,UAAI,aAAQ,MAAM,GAAG,MAAjB,mBAAoB,YAAW,GAAG;AACpC,YAAM,IAAI,MAAM,sBAAsB;IACxC;AAEA,WAAO;EACT;EACA,iBAAiB,aAAwB;AACvC,UAAM,QAAQ,YAAY,MAAM,GAAG;AACnC,QAAI,MAAM,WAAW,GAAG;AACtB,YAAM,IAAI,MAAM,4BAA4B,WAAW,EAAE;IAC3D;AAEA,UAAM,CAAC,gBAAgB,SAAS,OAAO,IAAI;AAE3C,QAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,SAAS;AAC3C,YAAM,IAAI,MAAM,4BAA4B,WAAW,EAAE;IAC3D;AAEA,WAAO;MACL;MACA;MACA;;EAEJ;EACA,mBAAmB,eAA4B;AAC7C,UAAM,QAAQ,cAAc,MAAM,GAAG;AACrC,QAAI,MAAM,WAAW,GAAG;AACtB,YAAM,IAAI,MAAM,8BAA8B,aAAa,EAAE;IAC/D;AAEA,UAAM,CAAC,gBAAgB,OAAO,IAAI;AAElC,QAAI,CAAC,kBAAkB,CAAC,SAAS;AAC/B,YAAM,IAAI,MAAM,8BAA8B,aAAa,EAAE;IAC/D;AAEA,WAAO;MACL;MACA;;EAEJ;;;;ACxBK,IAAM,uBAAuB;EAClC,WAAW;EACX,aAAa;EACb,eAAe;EACf,mBAAmB;EACnB,wBAAwB;EACxB,kBAAkB;EAClB,2BAA2B;EAC3B,gBAAgB;EAChB,iBAAiB;EACjB,kBAAkB;EAClB,sBAAsB;EACtB,mBAAmB;EACnB,iBAAiB;EACjB,kBAAkB;EAClB,0BAA0B;EAC1B,sBAAsB;EACtB,iBAAiB;EACjB,WAAW;EACX,gBAAgB;EAChB,yBAAyB;EACzB,aAAa;;AAKT,SAAU,sBAAsB,WAA0B;AAC9D,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,kDAAkD;EACpE;AAEA,SAAO,WAAW,SAAS;AAC7B;AAEO,IAAM,mBAAmB;EAC9B,QAAQ,KAA0B,OAAc;AAC9C,QAAI,OAAM,KAAM,UAAU,QAAW;AACnC,mBAAa,QAAQ,KAAK,KAAK;IACjC;EACF;EACA,QAAQ,KAAwB;AAC9B,QAAI,OAAM,GAAI;AACZ,aAAO,aAAa,QAAQ,GAAG,KAAK;IACtC;AAEA,WAAO;EACT;EACA,WAAW,KAAwB;AACjC,QAAI,OAAM,GAAI;AACZ,mBAAa,WAAW,GAAG;IAC7B;EACF;EACA,QAAK;AACH,QAAI,OAAM,GAAI;AACZ,mBAAa,MAAK;IACpB;EACF;;AAGI,SAAU,SAAM;AACpB,SAAO,OAAO,WAAW,eAAe,OAAO,iBAAiB;AAClE;;;ACxEM,SAAU,qBAAqB,gBAAiC,WAAqB;AACzF,MAAI,cAAc,SAAS;AACzB,WAAO;MACL,iBAAgB,iDAAiB,oBAAmB;MACpD,oBAAoB;;EAExB;AAEA,SAAO;IACL,iBAAgB,iDAAiB,oBAAmB;IACpD,oBAAoB;;AAExB;;;ACXO,IAAM,cAAc;;EAEzB,aAAa;IACX,WAAW;IACX,eAAe;IACf,KAAK;IACL,UAAU;;EAEZ,eAAe,WAAmB,aAAmB;AACnD,WAAO,KAAK,IAAG,IAAK,YAAY;EAClC;EACA,wBAAqB;AACnB,UAAM,YAAY,YAAY,mBAAkB;AAChD,UAAM,gBAAgB,YAAY,uBAAsB;AACxD,UAAM,gBAAgB,gBAAgB,cAAc,MAAM,GAAG,EAAE,CAAC,IAAI;AAGpE,UAAM,UAAU,gBACZ,MAAM,OAAO,aAAa,CAAC,IACzB,gBACA,OAAO,aAAa,IACtB;AAEJ,WAAO;MACL;MACA;MACA;;EAEJ;EAEA,yBAAyB,EAAE,MAAM,KAAI,GAAkC;AACrE,QAAI;AACF,uBAAiB,QAAQ,qBAAqB,iBAAiB,KAAK,UAAU,EAAE,MAAM,KAAI,CAAE,CAAC;IAC/F,QAAQ;AACN,cAAQ,KAAK,uCAAuC;IACtD;EACF;EAEA,2BAAwB;AACtB,QAAI;AACF,YAAM,WAAW,iBAAiB,QAAQ,qBAAqB,eAAe;AAC9E,UAAI,UAAU;AACZ,eAAO,KAAK,MAAM,QAAQ;MAC5B;IACF,QAAQ;AACN,cAAQ,KAAK,uCAAuC;IACtD;AAEA,WAAO;EACT;EAEA,8BAA2B;AACzB,QAAI;AACF,uBAAiB,WAAW,qBAAqB,eAAe;IAClE,QAAQ;AACN,cAAQ,KAAK,0CAA0C;IACzD;EACF;EAEA,mBAAmB,WAAyB;AAC1C,QAAI;AACF,uBAAiB,QAAQ,qBAAqB,kBAAkB,SAAS;IAC3E,QAAQ;AACN,cAAQ,KAAK,gCAAgC;IAC/C;EACF;EAEA,uBAAuB,eAA4B;AACjD,QAAI;AACF,uBAAiB,QAAQ,qBAAqB,wBAAwB,aAAa;AACnF,kBAAY,mBAAmB,cAAc,MAAM,GAAG,EAAE,CAAC,CAAmB;IAC9E,QAAQ;AACN,cAAQ,KAAK,sCAAsC;IACrD;EACF;EAEA,yBAAsB;AACpB,QAAI;AACF,aAAO,iBAAiB,QAAQ,qBAAqB,sBAAsB;IAG7E,QAAQ;AACN,cAAQ,KAAK,sCAAsC;AAEnD,aAAO;IACT;EACF;EAEA,4BAAyB;AACvB,QAAI;AACF,uBAAiB,WAAW,qBAAqB,sBAAsB;IACzE,QAAQ;AACN,cAAQ,KAAK,yCAAyC;IACxD;EACF;EAEA,2BAA2B,WAAyB;AAClD,QAAI;AACF,YAAM,MAAM,sBAAsB,SAAS;AAC3C,uBAAiB,WAAW,GAAG;IACjC,QAAQ;AACN,cAAQ,KAAK,yCAAyC;IACxD;EACF;EAEA,gBAAgB,QAAgB;AAC9B,QAAI;AACF,YAAM,gBAAgB,YAAY,iBAAgB;AAClD,YAAM,SAAS,cAAc,KAAK,OAAK,EAAE,OAAO,OAAO,EAAE;AACzD,UAAI,CAAC,QAAQ;AACX,sBAAc,QAAQ,MAAM;AAC5B,YAAI,cAAc,SAAS,GAAG;AAC5B,wBAAc,IAAG;QACnB;AACA,yBAAiB,QAAQ,qBAAqB,gBAAgB,KAAK,UAAU,aAAa,CAAC;MAC7F;IACF,QAAQ;AACN,cAAQ,KAAK,6BAA6B;IAC5C;EACF;EAEA,mBAAgB;AACd,QAAI;AACF,YAAM,SAAS,iBAAiB,QAAQ,qBAAqB,cAAc;AAE3E,aAAO,SAAS,KAAK,MAAM,MAAM,IAAI,CAAA;IACvC,QAAQ;AACN,cAAQ,KAAK,6BAA6B;IAC5C;AAEA,WAAO,CAAA;EACT;EAEA,wBAAwB,WAA2B,aAAmB;AACpE,QAAI;AACF,YAAM,MAAM,sBAAsB,SAAS;AAC3C,uBAAiB,QAAQ,KAAK,WAAW;IAC3C,QAAQ;AACN,cAAQ,KAAK,sCAAsC;IACrD;EACF;EAEA,qBAAkB;AAChB,QAAI;AACF,YAAM,kBAAkB,iBAAiB,QAAQ,qBAAqB,gBAAgB;AAEtF,aAAO;IACT,QAAQ;AACN,cAAQ,KAAK,gCAAgC;IAC/C;AAEA,WAAO;EACT;EAEA,wBAAwB,WAAqC;AAC3D,QAAI,CAAC,WAAW;AACd,aAAO;IACT;AAEA,QAAI;AACF,YAAM,MAAM,sBAAsB,SAAS;AAE3C,aAAO,iBAAiB,QAAQ,GAAG;IACrC,SAASqB,IAAG;AACV,cAAQ,KAAK,sDAAsD,SAAS;IAC9E;AAEA,WAAO;EACT;EAEA,2BAA2B,gBAA8B;AACvD,QAAI;AACF,uBAAiB,QAAQ,qBAAqB,kBAAkB,cAAc;IAChF,QAAQ;AACN,cAAQ,KAAK,yCAAyC;IACxD;EACF;EAEA,6BAA0B;AACxB,QAAI;AACF,aAAO,iBAAiB,QAAQ,qBAAqB,gBAAgB;IACvE,QAAQ;AACN,cAAQ,KAAK,yCAAyC;IACxD;AAEA,WAAO;EACT;EAEA,gCAA6B;AAC3B,QAAI;AACF,uBAAiB,WAAW,qBAAqB,gBAAgB;IACnE,QAAQ;AACN,cAAQ,KAAK,4CAA4C;IAC3D;EACF;EAEA,6BAA0B;AACxB,QAAI;AACF,aAAO,iBAAiB,QAAQ,qBAAqB,yBAAyB;IAChF,QAAQ;AACN,cAAQ,KAAK,yCAAyC;IACxD;AAEA,WAAO;EACT;EAEA,+BAA4B;AAnO9B;AAoOI,UAAM,sBAAsB,iBAAiB,QAC3C,qBAAqB,sBAAsB;AAE7C,UAAM,aAAY,gEAAqB,MAAM,SAA3B,mBAAkC;AAEpD,WAAO;EACT;EAEA,oBAAoB,QAAwB;AAC1C,QAAI;AACF,uBAAiB,QAAQ,qBAAqB,mBAAmB,MAAM;IACzE,QAAQ;AACN,cAAQ,KAAK,iCAAiC;IAChD;EACF;EAEA,sBAAmB;AACjB,QAAI;AACF,aAAO,iBAAiB,QAAQ,qBAAqB,iBAAiB;IACxE,QAAQ;AACN,aAAO;IACT;EACF;EAEA,yBAAsB;AACpB,QAAI;AACF,YAAM,aAAa,iBAAiB,QAAQ,qBAAqB,oBAAoB;AAErF,UAAI,EAAC,yCAAY,SAAQ;AACvB,eAAO,CAAA;MACT;AAEA,aAAO,WAAW,MAAM,GAAG;IAC7B,QAAQ;AACN,aAAO,CAAA;IACT;EACF;EAEA,uBAAuB,YAA4B;AACjD,QAAI;AACF,YAAM,mBAAmB,MAAM,KAAK,IAAI,IAAI,UAAU,CAAC;AACvD,uBAAiB,QACf,qBAAqB,sBACrB,iBAAiB,KAAK,GAAG,CAAC;IAE9B,QAAQ;AACN,cAAQ,KAAK,qCAAqC;IACpD;EACF;EAEA,sBAAsB,WAAyB;AAC7C,QAAI;AACF,YAAM,aAAa,YAAY,uBAAsB;AACrD,UAAI,CAAC,WAAW,SAAS,SAAS,GAAG;AACnC,mBAAW,KAAK,SAAS;AACzB,oBAAY,uBAAuB,UAAU;MAC/C;IACF,QAAQ;AACN,cAAQ,KAAK,mCAAmC;IAClD;EACF;EAEA,yBAAyB,WAAyB;AAChD,QAAI;AACF,YAAM,aAAa,YAAY,uBAAsB;AACrD,YAAM,QAAQ,WAAW,QAAQ,SAAS;AAC1C,UAAI,QAAQ,IAAI;AACd,mBAAW,OAAO,OAAO,CAAC;AAC1B,oBAAY,uBAAuB,UAAU;MAC/C;IACF,QAAQ;AACN,cAAQ,KAAK,sCAAsC;IACrD;EACF;EACA,4BAAyB;AACvB,QAAI;AACF,aAAO,iBAAiB,QAAQ,qBAAqB,wBAAwB;IAG/E,QAAQ;AACN,cAAQ,KAAK,wCAAwC;AAErD,aAAO;IACT;EACF;EACA,0BAA0B,gBAA8B;AACtD,QAAI;AACF,uBAAiB,QAAQ,qBAAqB,0BAA0B,cAAc;IACxF,QAAQ;AACN,cAAQ,KAAK,wCAAwC;IACvD;EACF;EACA,+BAA4B;AAC1B,QAAI;AACF,uBAAiB,WAAW,qBAAqB,wBAAwB;IAC3E,QAAQ;AACN,cAAQ,KAAK,2CAA2C;IAC1D;EACF;EACA,kBAAe;AACb,QAAI,QAAsF,CAAA;AAC1F,QAAI;AACF,YAAM,SAAS,iBAAiB,QAAQ,qBAAqB,eAAe;AAC5E,cAAQ,SAAS,KAAK,MAAM,MAAM,IAAI,CAAA;IACxC,QAAQ;AACN,cAAQ,KAAK,6BAA6B;IAC5C;AAEA,WAAO;EACT;EACA,8BAA8B,aAAmB;AAC/C,QAAI;AACF,YAAM,QAAQ,YAAY,gBAAe;AACzC,uBAAiB,QACf,qBAAqB,iBACrB,KAAK,UAAU,EAAE,GAAG,OAAO,CAAC,WAAW,GAAG,OAAS,CAAE,CAAC;IAE1D,QAAQ;AACN,cAAQ,KAAK,+CAA+C,WAAW;IACzE;EACF;EACA,8BAA8B,aAAmB;AAC/C,QAAI;AACF,YAAM,QAAQ,YAAY,gBAAe;AACzC,YAAM,eAAe,MAAM,WAAW;AAEtC,UACE,gBACA,CAAC,KAAK,eAAe,aAAa,WAAW,KAAK,YAAY,SAAS,GACvE;AACA,eAAO,aAAa;MACtB;AAEA,kBAAY,8BAA8B,WAAW;IACvD,QAAQ;AACN,cAAQ,KAAK,2CAA2C,WAAW;IACrE;AAEA,WAAO;EACT;EACA,mBAAmB,QAIlB;AACC,QAAI;AACF,YAAM,QAAQ,YAAY,gBAAe;AACzC,YAAM,OAAO,WAAW,IAAI;AAC5B,uBAAiB,QAAQ,qBAAqB,iBAAiB,KAAK,UAAU,KAAK,CAAC;IACtF,QAAQ;AACN,cAAQ,KAAK,kCAAkC,MAAM;IACvD;EACF;EAEA,wBAAqB;AACnB,QAAI,QAGA,CAAA;AACJ,QAAI;AACF,YAAM,SAAS,iBAAiB,QAAQ,qBAAqB,oBAAoB;AACjF,cAAQ,SAAS,KAAK,MAAM,MAAM,IAAI,CAAA;IACxC,QAAQ;AACN,cAAQ,KAAK,6BAA6B;IAC5C;AAEA,WAAO;EACT;EACA,oCAAoC,aAAmB;AACrD,QAAI;AACF,YAAM,QAAQ,YAAY,gBAAe;AACzC,uBAAiB,QACf,qBAAqB,sBACrB,KAAK,UAAU,EAAE,GAAG,OAAO,CAAC,WAAW,GAAG,OAAS,CAAE,CAAC;IAE1D,QAAQ;AACN,cAAQ,KAAK,+CAA+C,WAAW;IACzE;EACF;EACA,oCAAoC,aAAmB;AACrD,QAAI;AACF,YAAM,QAAQ,YAAY,sBAAqB;AAC/C,YAAM,qBAAqB,MAAM,WAAW;AAE5C,UACE,sBACA,CAAC,KAAK,eAAe,mBAAmB,WAAW,KAAK,YAAY,aAAa,GACjF;AACA,eAAO;MACT;AAEA,cAAQ,KAAK,gCAAgC,WAAW;AACxD,kBAAY,8BAA8B,WAAW;IACvD,QAAQ;AACN,cAAQ,KAAK,2CAA2C,WAAW;IACrE;AAEA,WAAO;EACT;EACA,yBAAyB,QAKxB;AACC,QAAI;AACF,YAAM,QAAQ,YAAY,sBAAqB;AAC/C,YAAM,OAAO,WAAW,IAAI;AAC5B,uBAAiB,QAAQ,qBAAqB,sBAAsB,KAAK,UAAU,KAAK,CAAC;IAC3F,QAAQ;AACN,cAAQ,KAAK,kCAAkC,MAAM;IACvD;EACF;EAEA,cAAW;AACT,QAAI,QAAkF,CAAA;AACtF,QAAI;AACF,YAAM,SAAS,iBAAiB,QAAQ,qBAAqB,SAAS;AACtE,cAAQ,SAAS,KAAK,MAAM,MAAM,IAAI,CAAA;IACxC,QAAQ;AACN,cAAQ,KAAK,8BAA8B;IAC7C;AAEA,WAAO;EACT;EACA,0BAA0B,SAAe;AACvC,QAAI;AACF,YAAM,QAAQ,YAAY,YAAW;AACrC,YAAM,WAAW,MAAM,OAAO;AAE9B,UAAI,YAAY,CAAC,KAAK,eAAe,SAAS,WAAW,KAAK,YAAY,GAAG,GAAG;AAC9E,eAAO,SAAS;MAClB;AACA,kBAAY,mBAAmB,OAAO;IACxC,QAAQ;AACN,cAAQ,KAAK,qCAAqC,OAAO;IAC3D;AAEA,WAAO;EACT;EACA,eAAe,QAId;AACC,QAAI;AACF,YAAM,QAAQ,YAAY,YAAW;AACrC,YAAM,OAAO,OAAO,IAAI;AACxB,uBAAiB,QAAQ,qBAAqB,WAAW,KAAK,UAAU,KAAK,CAAC;IAChF,QAAQ;AACN,cAAQ,KAAK,mCAAmC,MAAM;IACxD;EACF;EACA,mBAAmB,SAAe;AAChC,QAAI;AACF,YAAM,QAAQ,YAAY,YAAW;AACrC,uBAAiB,QACf,qBAAqB,WACrB,KAAK,UAAU,EAAE,GAAG,OAAO,CAAC,OAAO,GAAG,OAAS,CAAE,CAAC;IAEtD,QAAQ;AACN,cAAQ,KAAK,wCAAwC,OAAO;IAC9D;EACF;EACA,mBAAgB;AACd,QAAI,QAMA,CAAA;AACJ,QAAI;AACF,YAAM,SAAS,iBAAiB,QAAQ,qBAAqB,cAAc;AAC3E,cAAQ,SAAS,KAAK,MAAM,MAAM,IAAI,CAAA;IACxC,QAAQ;AACN,cAAQ,KAAK,8BAA8B;IAC7C;AAEA,WAAO;EACT;EACA,+BAA+B,SAAe;AAC5C,QAAI;AACF,YAAM,QAAQ,YAAY,iBAAgB;AAC1C,YAAM,gBAAgB,MAAM,OAAO;AAEnC,UACE,iBACA,CAAC,KAAK,eAAe,cAAc,WAAW,KAAK,YAAY,QAAQ,GACvE;AACA,eAAO,cAAc;MACvB;AACA,kBAAY,wBAAwB,OAAO;IAC7C,QAAQ;AACN,cAAQ,KAAK,qCAAqC,OAAO;IAC3D;AAEA,WAAO;EACT;EACA,oBAAoB,QAInB;AACC,QAAI;AACF,YAAM,QAAQ,YAAY,iBAAgB;AAC1C,YAAM,OAAO,OAAO,IAAI;QACtB,UAAU,OAAO;QACjB,WAAW,OAAO;;AAEpB,uBAAiB,QAAQ,qBAAqB,gBAAgB,KAAK,UAAU,KAAK,CAAC;IACrF,QAAQ;AACN,cAAQ,KAAK,mCAAmC,MAAM;IACxD;EACF;EACA,wBAAwB,SAAe;AACrC,QAAI;AACF,YAAM,QAAQ,YAAY,iBAAgB;AAC1C,uBAAiB,QACf,qBAAqB,gBACrB,KAAK,UAAU,EAAE,GAAG,OAAO,CAAC,OAAO,GAAG,OAAS,CAAE,CAAC;IAEtD,QAAQ;AACN,cAAQ,KAAK,wCAAwC,OAAO;IAC9D;EACF;EAEA,oBAAiB;AACf,QAAI;AACF,uBAAiB,WAAW,qBAAqB,eAAe;AAChE,uBAAiB,WAAW,qBAAqB,oBAAoB;AACrE,uBAAiB,WAAW,qBAAqB,SAAS;AAC1D,uBAAiB,WAAW,qBAAqB,cAAc;IACjE,QAAQ;AACN,cAAQ,KAAK,+BAA+B;IAC9C;EACF;EACA,yBAAyB,cAAmC;AAC1D,QAAI;AACF,uBAAiB,QACf,qBAAqB,yBACrB,KAAK,UAAU,YAAY,CAAC;IAEhC,QAAQ;AACN,cAAQ,KAAK,yCAAyC,YAAY;IACpE;EACF;EACA,2BAAwB;AACtB,QAAI;AACF,YAAM,SAAS,iBAAiB,QAAQ,qBAAqB,uBAAuB;AACpF,UAAI,CAAC,QAAQ;AACX,eAAO,CAAA;MACT;AAEA,aAAO,KAAK,MAAM,MAAM;IAC1B,QAAQ;AACN,cAAQ,KAAK,uCAAuC;IACtD;AAEA,WAAO,CAAA;EACT;EACA,eAAe,aAA2B,gBAA8B;AACtE,QAAI;AACF,YAAM,iBAAiB;QACrB,GAAG,YAAY,eAAc;QAC7B,CAAC,cAAc,GAAG;;AAGpB,uBAAiB,QAAQ,qBAAqB,aAAa,KAAK,UAAU,cAAc,CAAC;IAC3F,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAyC,KAAK;IAC9D;EACF;EACA,iBAAc;AACZ,QAAI;AACF,YAAM,qBAAqB,iBAAiB,QAAQ,qBAAqB,WAAW;AAEpF,UAAI,CAAC,oBAAoB;AACvB,eAAO,CAAA;MACT;AAEA,aAAO,KAAK,MAAM,kBAAkB;IACtC,SAAS,OAAO;AACd,cAAQ,MAAM,0CAA0C,KAAK;AAE7D,aAAO,CAAA;IACT;EACF;;;;AC9lBF,IAAM;;GAEH,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,cACtD,QAAQ,IAAI,gCAAgC,IAC5C,WAAc;;AAEb,IAAM,mBAAmB;EAC9B;IACE,OAAO;IACP,MAAM;IACN,UAAU;IACV,KAAK;IACL,iBAAiB,CAAC,QAAQ;;EAE5B;IACE,OAAO;IACP,MAAM;IACN,UAAU;IACV,KAAK;IACL,iBAAiB,CAAC,UAAU,QAAQ;;;AAIjC,IAAM,kBAAkB;AAExB,IAAMC,iBAAgB;EAC3B,iBAAiB;EAEjB,YAAY;EAEZ,aAAa;EAEb,cAAc;EAEd,YAAY;EAEZ;EAEA,uBAAuB,GAAG,WAAW;EAErC,qBAAqB,GAAG,WAAW;EAEnC,sBAAsB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;;;;EAOF,4BAA4B;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;EAGF,oCAAoC;EAEpC,oCAAoC;IAClC,UAAU;IACV,gBAAgB;IAChB,SAAS;IACT,WAAW;IACX,WAAW;IACX,cAAc;IACd,MAAM;IACN,MAAM;;EAGR,2BAA2B;EAE3B,uBAAuB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;EAGF,qBAAqB;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;EAEF,0BAA0B,CAAC,UAAU,QAAQ;EAC7C,yBAAyB;;IAEvB;;IAEA;;IAEA;;IAEA;;IAEA;;IAEA;;IAEA;;IAEA;;IAEA;;IAEA;;IAEA;;IAEA;;EAGF,kCAAkC,CAAC,QAAQ;EAC3C,mCAAmC,CAAC,UAAU,QAAQ;EACtD,mCAAmC,CAAC,QAAQ;EAC5C,sBAAsB;IACpB,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,QAAQ;;EAGV,4BAA4B;EAE5B,gBAAgB;IACd,QAAQ;IACR,KAAK;;EAGP,2BAA2B,CAAC,UAAU,QAAQ;EAC9C,yBAAyB;IACvB,OAAO,CAAC,OAAO;IACf,QAAQ,CAAC,YAAY,MAAM;IAC3B,OAAO;IACP,SAAS;MACP;MACA;MACA;MACA;MACA;MACA;MACA;;IAEF,UAAU;IACV,eAAe;;EAEjB,kCAAkC;IAChC,OAAO;IACP,SAAS;IACT,OAAO;IACP,QAAQ;IACR,UAAU;IACV,eAAe;;EAEjB,kBAAkB;IAChB,SAAS;IACT,MAAM;IACN,kBAAkB;IAClB,oBAAoB;MAClB;MACA;MACA;MACA;MACA;MACA;MACA;;IAEF,WAAW;IACX,YAAY;IACZ,eAAe;IACf,eAAe;IACf,iBAAiB;IACjB,qBAAqB,CAAC,UAAU,SAAS,WAAW,MAAM;IAC1D,qBAAqB;IACrB,KAAK;;EAGP,iBAAiB;IACf;IACA;IACA;IACA;IACA;IACA;IACA;;EAGF,uBAAuB;IACrB,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,QAAQ;;EAEV,eAAe;IACb,WAAW;IACX,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,SAAS;IACT,SAAS;;;;;ACjSN,IAAM,iBAAiB;EAC5B,WAAQ;AAXV;AAYI,QAAI,KAAK,SAAQ,GAAI;AACnB,aAAO,QACJ,QAAO,iCAAQ,gBAAe,gBAC7B,sCAAQ,WAAW,wBAAnB,mBAAwC,YACxC,wDAAwD,KAAK,UAAU,SAAS,CAAC;IAEvF;AAEA,WAAO;EACT;EAEA,iBAAiB,SAAkC,cAAc,IAAE;AACjE,WAAO,mCAAS,cAAc,oBAAoB,SAAS,YAAY,YAAW;EACpF;EAEA,YAAS;AACP,QAAI,CAAC,KAAK,SAAQ,GAAI;AACpB,aAAO;IACT;AAEA,UAAMC,MAAK,iCAAQ,UAAU,UAAU;AAEvC,WAAO,eAAe,SAAQ,KAAMA,IAAG,SAAS,SAAS;EAC3D;EAEA,QAAK;AACH,QAAI,CAAC,KAAK,SAAQ,GAAI;AACpB,aAAO;IACT;AAEA,UAAMA,MAAK,iCAAQ,UAAU,UAAU;AAEvC,WAAOA,IAAG,SAAS,QAAQ,KAAKA,IAAG,SAAS,MAAM;EACpD;EAEA,WAAQ;AACN,QAAI,CAAC,KAAK,SAAQ,GAAI;AACpB,aAAO;IACT;AAEA,UAAMA,MAAK,iCAAQ,UAAU,UAAU;AAEvC,WAAOA,IAAG,SAAS,QAAQ;EAC7B;EAEA,WAAQ;AACN,WAAO,OAAO,WAAW;EAC3B;EAEA,iBAAiB,QAAe;AAC9B,WAAO,SAAS,SAAS,KAAK,IAAG,KAAMC,eAAc,aAAa;EACpE;EAEA,eAAe,WAAmB,eAAeA,eAAc,YAAU;AACvE,WAAO,KAAK,IAAG,IAAK,aAAa;EACnC;EAEA,gBAAgB,MAAY;AAC1B,cAAU,UAAU,UAAU,IAAI;EACpC;EAEA,WAAQ;AACN,QAAI;AACF,cAAO,iCAAQ,WAAS,iCAAQ;IAClC,SAASC,IAAG;AACV,aAAO;IACT;EACF;EACA,YAAS;AAhFX;AAiFI,QAAI,eAAe,SAAQ,KAAM,OAAO,SAAS,OAAO,KAAK;AAC3D,UAAI;AACF,cAAM,YAAW,4CAAQ,aAAR,mBAAkB,oBAAlB,mBAAoC;AAErD,cAAM,aAAa;AACnB,YAAI,UAAU;AACZ,gBAAM,cAAc,IAAI,IAAI,QAAQ;AACpC,gBAAM,UAAU,IAAI,IAAI,UAAU;AAElC,iBAAO,YAAY,aAAa,QAAQ;QAC1C;MACF,QAAQ;AACN,eAAO;MACT;IACF;AAEA,WAAO;EACT;EAEA,mBAAgB;AACd,WAAO,KAAK,IAAG,IAAKD,eAAc;EACpC;EAEA,aAAa,aAAoC;AAC/C,WAAO,2CAAa,MAAM,KAAK;EACjC;EAEA,gBAAgB,aAAoC;AAClD,WAAO,2CAAa,MAAM,KAAK;EACjC;EAEA,MAAM,KAAK,cAAoB;AAC7B,WAAO,IAAI,QAAQ,aAAU;AAC3B,iBAAW,SAAS,YAAY;IAClC,CAAC;EACH;;EAGA,SAAS,MAAmC,UAAU,KAAG;AACvD,QAAI,QAAmD;AAEvD,WAAO,IAAI,SAAmB;AAC5B,eAAS,OAAI;AACX,aAAK,GAAG,IAAI;MACd;AAEA,UAAI,OAAO;AACT,qBAAa,KAAK;MACpB;AACA,cAAQ,WAAW,MAAM,OAAO;IAClC;EACF;EAEA,UAAU,KAAW;AACnB,WAAO,IAAI,WAAW,SAAS,KAAK,IAAI,WAAW,UAAU;EAC/D;EAEA,gBACE,QACA,OACA,gBAA+B,MAAI;AAEnC,QAAI,eAAe,UAAU,MAAM,GAAG;AACpC,aAAO,KAAK,mBAAmB,QAAQ,KAAK;IAC9C;AAEA,QAAI,aAAa;AACjB,QAAI,oBAAoB;AAExB,QAAI,CAAC,WAAW,SAAS,KAAK,GAAG;AAC/B,mBAAa,OAAO,WAAW,KAAK,EAAE,EAAE,WAAW,KAAK,EAAE;AAC1D,mBAAa,GAAG,UAAU;IAC5B;AAEA,QAAI,CAAC,WAAW,SAAS,GAAG,GAAG;AAC7B,mBAAa,GAAG,UAAU;IAC5B;AAEA,QAAI,qBAAqB,EAAC,uDAAmB,SAAS,OAAM;AAC1D,0BAAoB,GAAG,iBAAiB;IAC1C;AAGA,QAAI,KAAK,WAAU,KAAM,KAAK,UAAS,GAAI;AAEzC,cAAQ,mBAAmB,KAAK;IAClC;AACA,UAAM,eAAe,mBAAmB,KAAK;AAE7C,WAAO;MACL,UAAU,GAAG,UAAU,UAAU,YAAY;MAC7C,uBAAuB,oBACnB,GAAG,iBAAiB,UAAU,YAAY,KAC1C;MACJ,MAAM;;EAEV;EAEA,mBAAmB,QAAgB,OAAa;AAC9C,QAAI,CAAC,eAAe,UAAU,MAAM,GAAG;AACrC,aAAO,KAAK,gBAAgB,QAAQ,KAAK;IAC3C;AACA,QAAI,aAAa;AACjB,QAAI,CAAC,WAAW,SAAS,GAAG,GAAG;AAC7B,mBAAa,GAAG,UAAU;IAC5B;AACA,UAAM,eAAe,mBAAmB,KAAK;AAE7C,WAAO;MACL,UAAU,GAAG,UAAU,UAAU,YAAY;MAC7C,MAAM;;EAEV;EACA,yBAAyB,QAAkB;AACzC,QAAI,WAAW,eAAe;AAC5B,aAAO;IACT;AAEA,QAAI,KAAK,WAAU,GAAI;AAErB,UAAI,YAAY,0BAAyB,GAAI;AAC3C,eAAO;MACT;AAEA,aAAO;IACT;AAEA,WAAO;EACT;EACA,SAAS,MAAc,QAAoB,UAAiB;AAC1D,qCAAQ,KAAK,MAAM,KAAK,yBAAyB,MAAM,GAAG,YAAY;EACxE;EAEA,eAAe,MAAc,QAAoB,UAAiB;AAChE,WAAO,iCAAQ,KACb,MACA,KAAK,yBAAyB,MAAM,GACpC,YAAY;EAEhB;EAEA,aAAU;AACR,WACE,OAAO,WAAW;KAEjB,QAAS,OAAe,oBAAoB;IAE3C,QAAS,OAAe,QAAQ;IAEhC,QAAS,OAAe,yBAAyB;EAEvD;EAEA,QAAK;AA1OP,gBAAAE;AA2OI,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO;IACT;AAEA,UAAM,2BAA0B,kBAAO,eAAP,gCAAoB,kCAApB,mBAAmD;AACnF,UAAM,mBAAmBA,MAAA,iCAAQ,cAAR,gBAAAA,IAA0D;AAEnF,WAAO,QAAQ,2BAA2B,eAAe;EAC3D;EAEA,MAAM,aAAa,KAAW;AAC5B,UAAM,eAAe,IAAI,QAAQ,CAAC,SAAS,WAAU;AACnD,YAAM,QAAQ,IAAI,MAAK;AACvB,YAAM,SAAS;AACf,YAAM,UAAU;AAChB,YAAM,cAAc;AACpB,YAAM,MAAM;IACd,CAAC;AAED,WAAO,QAAQ,KAAK,CAAC,cAAc,eAAe,KAAK,GAAI,CAAC,CAAC;EAC/D;EAEA,cAAc,SAA6B,QAA0B;AACnE,QAAI,mBAAmB;AAEvB,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,SAAS,OAAO,OAAO;AAC7B,UAAI,QAAQ;AACV,cAAM,iBAAiB,KAAK,MAAM,SAAS,GAAI,IAAI;AACnD,YAAI,gBAAgB;AAClB,6BAAmB,eAAe,SAAQ;QAC5C;MACF;IACF;AAEA,WAAO,GAAG,gBAAgB,GAAG,SAAS,IAAI,MAAM,KAAK,EAAE;EACzD;EAEA,eAAe,SAA6B,QAA0B;AAjRxE;AAkRI,QAAI,mBAAmB;AAEvB,QAAI,YAAY,KAAK;AACnB,yBAAmB;IACrB,WAAW,OAAO,YAAY,UAAU;AACtC,YAAM,SAAS,OAAO,OAAO;AAC7B,UAAI,QAAQ;AACV,4BAAmB,YAAO,SAAQ,EAAG,MAAM,uBAAuB,MAA/C,mBAAmD;MACxE;IACF;AAEA,WAAO;MACL,OAAO,oBAAoB;MAC3B,MAAM,qBAAqB,MAAM,QAAQ;MACzC;;EAEJ;EAEA,YAAS;AACP,WAAO,cAAgB;EACzB;EAEA,sBAAmB;AACjB,WAAO,cAAgB;EACzB;EAEA,kBAAe;AACb,WAAO,cAAgB;EACzB;EAEA,UAAO;AACL,QAAI,iCAAQ,YAAY;AACtB,aAAO,OAAO,WAAU;IAC1B;AAEA,WAAO,uCAAuC,QAAQ,UAAU,CAAAC,OAAI;AAClE,YAAMC,KAAK,KAAK,OAAM,IAAK,KAAM;AACjC,YAAM,IAAID,OAAM,MAAMC,KAAKA,KAAI,IAAO;AAEtC,aAAO,EAAE,SAAS,EAAE;IACtB,CAAC;EACH;;EAGA,WAAW,OAAU;AA9TvB;AA+TI,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO;IACT,WAAW,SAAO,0CAAO,WAAP,mBAAgB,OAAhB,mBAAoB,aAAY,UAAU;AAC1D,aAAO,MAAM,OAAO,CAAC,EAAE;IACzB,WAAW,iBAAiB,OAAO;AACjC,aAAO,MAAM;IACf;AAEA,WAAO;EACT;EAEA,sBACE,aACA,oBAAmC,CAAA,GAAE;AAErC,UAAM,mBAA2C,CAAA;AAEjD,QAAI,qBAAqB,aAAa;AACpC,kBAAY,QAAQ,CAAC,IAAI,UAAS;AAChC,yBAAiB,EAAE,IAAI;MACzB,CAAC;AAED,wBAAkB,KAAK,CAAC,GAAG,MAAK;AAC9B,cAAM,SAAS,iBAAiB,EAAE,EAAE;AACpC,cAAM,SAAS,iBAAiB,EAAE,EAAE;AAEpC,YAAI,WAAW,UAAa,WAAW,QAAW;AAChD,iBAAO,SAAS;QAClB,WAAW,WAAW,QAAW;AAC/B,iBAAO;QACT,WAAW,WAAW,QAAW;AAC/B,iBAAO;QACT;AAEA,eAAO;MACT,CAAC;IACH;AAEA,WAAO;EACT;EAEA,iBAAiB,OAAgB;AAC/B,QAAI,MAAM;AACV,eAAW,QAAQ,OAAO;AACxB,aAAO,KAAK,SAAS;IACvB;AAEA,WAAO;EACT;EAEA,mBAAmB,QAAc;AAC/B,UAAM,gBAAgB,OAAO,QAAQ,CAAC;AACtC,UAAM,CAAC,SAAS,OAAO,IAAI,cAAc,MAAM,GAAG;AAElD,WAAO,EAAE,SAAS,QAAO;EAC3B;EAEA,UAAU,SAAiB,QAAwB,UAAQ;AACzD,YAAQ,OAAO;MACb,KAAK;AACH,YAAI,CAAC,0BAA0B,KAAK,OAAO,GAAG;AAC5C,iBAAO;QACT,WACE,0BAA0B,KAAK,OAAO,KACtC,0BAA0B,KAAK,OAAO,GACtC;AACA,iBAAO;QACT;AAEA,eAAO;MACT,KAAK;AACH,eAAO,iCAAiC,KAAK,OAAO;MAEtD;AACE,eAAO;IACX;EACF;EAEA,SAAY,KAAU,KAAY;AAChC,UAAM,MAAM,oBAAI,IAAG;AAEnB,WAAO,IAAI,OAAO,UAAO;AACvB,YAAM,WAAW,KAAK,GAAG;AACzB,UAAI,IAAI,IAAI,QAAQ,GAAG;AACrB,eAAO;MACT;AACA,UAAI,IAAI,QAAQ;AAEhB,aAAO;IACT,CAAC;EACH;EAEA,mBACE,UACA,UACAC,UAAe;AAEf,UAAM,gBAAgB,SAAS,WAAW;AAC1C,UAAM,eACJ,gBACIL,eAAc,cAAc,YAC5B,SAAS,IAAI,aAAW,QAAQ,WAAW,EAAE,KAAK,GAAG;AAG3D,WAAO,GAAG,QAAQ,IAAI,YAAY,IAAIK,QAAO;EAC/C;;EAGA,cACE,WACA,SACA,MACA,WACA,MAAa;AAEb,WAAO;MACL;MACA;MACA;MACA;MACA;;EAEJ;EAEA,cAAc,SAAiB;AAC7B,QAAI,OAAO,YAAY,UAAU;AAC/B,aAAO;IACT;AAEA,UAAM,WAAW,QAAQ,MAAM,GAAG;AAClC,UAAM,YAAY,SAAS,CAAC;AAE5B,WACE,SAAS,OAAO,OAAO,EAAE,WAAW,KACnC,aAAwB,cAAgB;EAE7C;EACA,QAAK;AACH,UAAMN,MAAK,iCAAQ,UAAU,UAAU;AAEvC,WAAOA,IAAG,SAAS,WAAW,KAAK,CAACA,IAAG,SAAS,QAAQ;EAC1D;EAEA,6BAA6B,KAAW;AACtC,UAAM,gBAAgB,KAAK,mBAAmB,iCAAQ,SAAS,IAAI,CAAC;AACpE,UAAM,gBAAgB;AACtB,UAAM,YAAY,IAAI,IAAI,GAAG;AAC7B,QAAI,UAAU,SAAS,mBAAmB;AACxC,YAAM,gBAAgB;AACtB,YAAM,cAAc,IAAI,UAAU,IAAI,QAAQ,aAAa,IAAI,cAAc,MAAM;AACnF,YAAM,YAAY,KAAK,cACrB,mBAAmB,WAAW,GAC9B,eACA,aAAa;AAGf,aAAO,IAAI,QAAQ,aAAa,mBAAmB,SAAS,CAAC;IAC/D;AAEA,WAAO,KAAK,cAAc,KAAK,eAAe,aAAa;EAC7D;EACA,cAAc,KAAa,KAAa,cAAoB;AAE1D,UAAM,WAAW,IAAI,QAAQ,GAAG;AAEhC,QAAI,aAAa,IAAI;AACnB,YAAM,IAAI,MAAM,GAAG,GAAG,oCAAoC,GAAG,EAAE;IACjE;AAGA,UAAM,cAAc,IAAI,QAAQ,KAAK,QAAQ;AAC7C,UAAM,YAAY,IAAI;AAGtB,UAAM,cAAc,gBAAgB,KAAK,cAAc,IAAI;AAE3D,UAAM,iBAAiB,IAAI,UAAU,GAAG,WAAW,SAAS;AAE5D,UAAM,kBAAkB,IAAI,UAAU,WAAW,WAAW,WAAW;AAEvE,UAAM,gBAAgB,IAAI,UAAU,WAAW;AAE/C,UAAM,cAAc,kBAAkB;AAEtC,UAAM,SAAS,iBAAiB,cAAc;AAE9C,WAAO;EACT;;;;AC1fF,IAAMO,IAAoBC,OAAAA;AAA1B,IACMC,IAAsBD,OAAAA;AAsB5B,IAAME,IAAWC,OAAOC;AAAxB,IAEMC,IAAiB,oBAAIC;AAF3B,IAKMC,IAAsBC,CAAAA,OAC1BA,OAAQH,EAAeI,IAAID,EAAAA,IACvBH,EAAeK,IAAIF,EAAAA,IAClBN,EAASM,EAAAA,MAASL,OAAOQ,aAAaT,EAASM,EAAAA,MAASI,MAAMD;AARrE,IAoWaE,IAAmBC,CAAAA,OAC1BC,EAAgBD,EAAAA,KACVA,GAAsCE,CAAAA,KAEzC;AAxWT,IAuYaC,IAAcA,CAACH,IAAaI,KAAAA,SAAO;AAC9CC,IAAeC,IAAIN,IAAKI,EAAAA;AAC1B;;;AC/ZA,IAAM,WAAW,CAAC,MAAM,OAAO,MAAM,YAAY,MAAM;AACvD,IAAM,gBAAgC,oBAAI,QAAQ;AAClD,IAAM,SAAyB,oBAAI,QAAQ;AAC3C,IAAM,qBAAqB,CAAC,WAAW,OAAO,IAAI,WAAW,CAAC,QAAQ,YAAY,IAAI,MAAM,QAAQ,OAAO,GAAG,WAAW,CAAC,MAAM,SAAS,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,MAAM,MAAM,QAAQ,CAAC,KAAK,EAAE,OAAO,YAAY,OAAO,EAAE,aAAa,YAAY,EAAE,aAAa,YAAY,EAAE,aAAa,UAAU,EAAE,aAAa,WAAW,EAAE,aAAa,SAAS,EAAE,aAAa,WAAW,EAAE,aAAa,WAAW,EAAE,aAAa,cAAc,uBAAuB,CAAC,YAAY;AACtc,UAAQ,QAAQ,QAAQ;AAAA,IACtB,KAAK;AACH,aAAO,QAAQ;AAAA,IACjB,KAAK;AACH,YAAM,QAAQ;AAAA,IAChB;AACE,YAAM;AAAA,EACV;AACF,GAAG,YAA4B,oBAAI,QAAQ,GAAG,iBAAiB,CAAC,QAAQG,UAAS,gBAAgB,yBAAyB;AACxH,QAAM,QAAQ,UAAU,IAAI,MAAM;AAClC,OAAK,SAAS,OAAO,SAAS,MAAM,CAAC,OAAOA,UAAS;AACnD,WAAO,MAAM,CAAC;AAAA,EAChB;AACA,QAAM,OAAO,MAAM,QAAQ,MAAM,IAAI,CAAC,IAAI,OAAO,OAAO,OAAO,eAAe,MAAM,CAAC;AACrF,IAAY,MAAM,IAAI;AACtB,YAAU,IAAI,QAAQ,CAACA,UAAS,IAAI,CAAC;AACrC,UAAQ,QAAQ,MAAM,EAAE,QAAQ,CAAC,QAAQ;AACvC,QAAI,OAAO,yBAAyB,MAAM,GAAG,GAAG;AAC9C;AAAA,IACF;AACA,UAAM,QAAQ,QAAQ,IAAI,QAAQ,GAAG;AACrC,UAAM,EAAE,WAAW,IAAI,QAAQ;AAAA,MAC7B;AAAA,MACA;AAAA,IACF;AACA,UAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA;AAAA;AAAA,MAGA,cAAc;AAAA,IAChB;AACA,QAAI,OAAO,IAAI,KAAK,GAAG;AACrB,QAAY,OAAO,KAAK;AAAA,IAC1B,WAAW,iBAAiB,SAAS;AACnC,aAAO,KAAK;AACZ,WAAK,MAAM,MAAM,cAAc,KAAK;AAAA,IACtC,WAAW,cAAc,IAAI,KAAK,GAAG;AACnC,YAAM,CAAC,SAAS,aAAa,IAAI,cAAc;AAAA,QAC7C;AAAA,MACF;AACA,WAAK,QAAQ;AAAA,QACX;AAAA,QACA,cAAc;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACA,WAAO,eAAe,MAAM,KAAK,IAAI;AAAA,EACvC,CAAC;AACD,SAAO,OAAO,kBAAkB,IAAI;AACtC,GAAG,aAA6B,oBAAI,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,kBAAkB;AACxG,MAAI,CAAC,SAAS,aAAa,GAAG;AAC5B,UAAM,IAAI,MAAM,iBAAiB;AAAA,EACnC;AACA,QAAM,QAAQ,WAAW,IAAI,aAAa;AAC1C,MAAI,OAAO;AACT,WAAO;AAAA,EACT;AACA,MAAIA,WAAU,cAAc,CAAC;AAC7B,QAAM,YAA4B,oBAAI,IAAI;AAC1C,QAAM,eAAe,CAAC,IAAI,cAAc,EAAE,cAAc,CAAC,MAAM;AAC7D,QAAIA,aAAY,aAAa;AAC3B,MAAAA,WAAU;AACV,gBAAU,QAAQ,CAAC,aAAa,SAAS,IAAI,WAAW,CAAC;AAAA,IAC3D;AAAA,EACF;AACA,MAAI,eAAe,cAAc,CAAC;AAClC,QAAM,gBAAgB,CAAC,mBAAmB,EAAE,cAAc,CAAC,MAAM;AAC/D,QAAI,iBAAiB,oBAAoB,CAAC,UAAU,MAAM;AACxD,qBAAe;AACf,sBAAgB,QAAQ,CAAC,CAAC,cAAc,MAAM;AAC5C,cAAM,cAAc,eAAe,CAAC,EAAE,gBAAgB;AACtD,YAAI,cAAcA,UAAS;AACzB,UAAAA,WAAU;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAOA;AAAA,EACT;AACA,QAAM,qBAAqB,CAAC,SAAS,CAAC,IAAI,gBAAgB;AACxD,UAAM,QAAQ,CAAC,GAAG,EAAE;AACpB,UAAM,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;AAC7B,iBAAa,OAAO,WAAW;AAAA,EACjC;AACA,QAAM,kBAAkC,oBAAI,IAAI;AAChD,QAAM,kBAAkB,CAAC,MAAM,mBAAmB;AAChD,SAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,gBAAgB,IAAI,IAAI,GAAG;AACnG,YAAM,IAAI,MAAM,8BAA8B;AAAA,IAChD;AACA,QAAI,UAAU,MAAM;AAClB,YAAM,SAAS,eAAe,CAAC,EAAE,mBAAmB,IAAI,CAAC;AACzD,sBAAgB,IAAI,MAAM,CAAC,gBAAgB,MAAM,CAAC;AAAA,IACpD,OAAO;AACL,sBAAgB,IAAI,MAAM,CAAC,cAAc,CAAC;AAAA,IAC5C;AAAA,EACF;AACA,QAAM,qBAAqB,CAAC,SAAS;AACnC,QAAI;AACJ,UAAM,QAAQ,gBAAgB,IAAI,IAAI;AACtC,QAAI,OAAO;AACT,sBAAgB,OAAO,IAAI;AAC3B,OAAC,KAAK,MAAM,CAAC,MAAM,OAAO,SAAS,GAAG,KAAK,KAAK;AAAA,IAClD;AAAA,EACF;AACA,QAAM,cAAc,CAAC,aAAa;AAChC,cAAU,IAAI,QAAQ;AACtB,QAAI,UAAU,SAAS,GAAG;AACxB,sBAAgB,QAAQ,CAAC,CAAC,gBAAgB,UAAU,GAAG,SAAS;AAC9D,aAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,YAAY;AACpF,gBAAM,IAAI,MAAM,uBAAuB;AAAA,QACzC;AACA,cAAM,SAAS,eAAe,CAAC,EAAE,mBAAmB,IAAI,CAAC;AACzD,wBAAgB,IAAI,MAAM,CAAC,gBAAgB,MAAM,CAAC;AAAA,MACpD,CAAC;AAAA,IACH;AACA,UAAM,iBAAiB,MAAM;AAC3B,gBAAU,OAAO,QAAQ;AACzB,UAAI,UAAU,SAAS,GAAG;AACxB,wBAAgB,QAAQ,CAAC,CAAC,gBAAgB,MAAM,GAAG,SAAS;AAC1D,cAAI,QAAQ;AACV,mBAAO;AACP,4BAAgB,IAAI,MAAM,CAAC,cAAc,CAAC;AAAA,UAC5C;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,aAAa,MAAM,QAAQ,aAAa,IAAI,CAAC,IAAI,OAAO,OAAO,OAAO,eAAe,aAAa,CAAC;AACzG,QAAM,UAAU;AAAA,IACd,eAAe,QAAQ,MAAM;AAC3B,YAAM,YAAY,QAAQ,IAAI,QAAQ,IAAI;AAC1C,yBAAmB,IAAI;AACvB,YAAM,UAAU,QAAQ,eAAe,QAAQ,IAAI;AACnD,UAAI,SAAS;AACX,qBAAa,CAAC,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC;AAAA,MAC5C;AACA,aAAO;AAAA,IACT;AAAA,IACA,IAAI,QAAQ,MAAM,OAAO,UAAU;AACjC,YAAM,eAAe,QAAQ,IAAI,QAAQ,IAAI;AAC7C,YAAM,YAAY,QAAQ,IAAI,QAAQ,MAAM,QAAQ;AACpD,UAAI,iBAAiB,SAAS,WAAW,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,SAAS,WAAW,WAAW,IAAI,KAAK,CAAC,IAAI;AACvH,eAAO;AAAA,MACT;AACA,yBAAmB,IAAI;AACvB,UAAI,SAAS,KAAK,GAAG;AACnB,gBAAQ,EAAa,KAAK,KAAK;AAAA,MACjC;AACA,UAAI,YAAY;AAChB,UAAI,iBAAiB,SAAS;AAC5B,cAAM,KAAK,CAAC,MAAM;AAChB,gBAAM,SAAS;AACf,gBAAM,QAAQ;AACd,uBAAa,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC;AAAA,QACrC,CAAC,EAAE,MAAM,CAACC,OAAM;AACd,gBAAM,SAAS;AACf,gBAAM,SAASA;AACf,uBAAa,CAAC,UAAU,CAAC,IAAI,GAAGA,EAAC,CAAC;AAAA,QACpC,CAAC;AAAA,MACH,OAAO;AACL,YAAI,CAAC,cAAc,IAAI,KAAK,KAAK,SAAS,KAAK,GAAG;AAChD,sBAAY,cAAc,KAAK;AAAA,QACjC;AACA,cAAM,kBAAkB,CAAC,OAAO,IAAI,SAAS,KAAK,cAAc,IAAI,SAAS;AAC7E,YAAI,iBAAiB;AACnB,0BAAgB,MAAM,eAAe;AAAA,QACvC;AAAA,MACF;AACA,cAAQ,IAAI,QAAQ,MAAM,WAAW,QAAQ;AAC7C,mBAAa,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,SAAS,CAAC;AAC9C,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,cAAc,SAAS,YAAY,OAAO;AAChD,aAAW,IAAI,eAAe,WAAW;AACzC,QAAM,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,gBAAc,IAAI,aAAa,UAAU;AACzC,UAAQ,QAAQ,aAAa,EAAE,QAAQ,CAAC,QAAQ;AAC9C,UAAM,OAAO,OAAO;AAAA,MAClB;AAAA,MACA;AAAA,IACF;AACA,QAAI,WAAW,MAAM;AACnB,kBAAY,GAAG,IAAI,cAAc,GAAG;AACpC,aAAO,KAAK;AACZ,aAAO,KAAK;AAAA,IACd;AACA,WAAO,eAAe,YAAY,KAAK,IAAI;AAAA,EAC7C,CAAC;AACD,SAAO;AACT,MAAM;AAAA;AAAA,EAEJ;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,CAAC,oBAAoB,IAAI,mBAAmB;AAClD,SAAS,MAAM,gBAAgB,CAAC,GAAG;AACjC,SAAO,qBAAqB,aAAa;AAC3C;AAKA,SAAS,UAAU,aAAa,UAAU,cAAc;AACtD,QAAM,aAAa,cAAc,IAAI,WAAW;AAChD,OAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,CAAC,YAAY;AACrF,YAAQ,KAAK,yBAAyB;AAAA,EACxC;AACA,MAAI;AACJ,QAAM,MAAM,CAAC;AACb,QAAM,cAAc,WAAW,CAAC;AAChC,MAAI,mBAAmB;AACvB,QAAM,WAAW,CAAC,OAAO;AACvB,QAAI,KAAK,EAAE;AACX,QAAI,cAAc;AAChB,eAAS,IAAI,OAAO,CAAC,CAAC;AACtB;AAAA,IACF;AACA,QAAI,CAAC,SAAS;AACZ,gBAAU,QAAQ,QAAQ,EAAE,KAAK,MAAM;AACrC,kBAAU;AACV,YAAI,kBAAkB;AACpB,mBAAS,IAAI,OAAO,CAAC,CAAC;AAAA,QACxB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,iBAAiB,YAAY,QAAQ;AAC3C,qBAAmB;AACnB,SAAO,MAAM;AACX,uBAAmB;AACnB,mBAAe;AAAA,EACjB;AACF;AACA,SAAS,SAAS,aAAa,eAAe;AAC5C,QAAM,aAAa,cAAc,IAAI,WAAW;AAChD,OAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,CAAC,YAAY;AACrF,YAAQ,KAAK,yBAAyB;AAAA,EACxC;AACA,QAAM,CAAC,QAAQ,eAAe,cAAc,IAAI;AAChD,SAAO,eAAe,QAAQ,cAAc,GAAG,aAAa;AAC9D;AACA,SAAS,IAAI,KAAK;AAChB,SAAO,IAAI,GAAG;AACd,SAAO;AACT;;;ACxQA,SAAS,aAAa,aAAa,KAAK,UAAU,cAAc;AAC9D,MAAI,YAAY,YAAY,GAAG;AAC/B,SAAO;AAAA,IACL;AAAA,IACA,MAAM;AACJ,YAAM,YAAY,YAAY,GAAG;AACjC,UAAI,CAAC,OAAO,GAAG,WAAW,SAAS,GAAG;AACpC,iBAAS,YAAY,SAAS;AAAA,MAChC;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACF;AA2DA,IAAM,WAAW,OAAO;AA+PxB,SAAS,SAASC,UAAS;AACzB,QAAM,MAAM,MAAM;AAAA,IAChB,MAAM,MAAM,KAAKA,YAAW,CAAC,CAAC;AAAA,IAC9B,IAAI,KAAK;AACP,aAAO,KAAK,KAAK,KAAK,CAAC,MAAM,EAAE,CAAC,MAAM,GAAG;AAAA,IAC3C;AAAA,IACA,IAAI,KAAK,OAAO;AACd,YAAM,SAAS,KAAK,KAAK,KAAK,CAAC,MAAM,EAAE,CAAC,MAAM,GAAG;AACjD,UAAI,QAAQ;AACV,eAAO,CAAC,IAAI;AAAA,MACd,OAAO;AACL,aAAK,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AAAA,IACA,IAAI,KAAK;AACP,UAAI;AACJ,cAAQ,KAAK,KAAK,KAAK,KAAK,CAAC,MAAM,EAAE,CAAC,MAAM,GAAG,MAAM,OAAO,SAAS,GAAG,CAAC;AAAA,IAC3E;AAAA,IACA,OAAO,KAAK;AACV,YAAM,QAAQ,KAAK,KAAK,UAAU,CAAC,MAAM,EAAE,CAAC,MAAM,GAAG;AACrD,UAAI,UAAU,IAAI;AAChB,eAAO;AAAA,MACT;AACA,WAAK,KAAK,OAAO,OAAO,CAAC;AACzB,aAAO;AAAA,IACT;AAAA,IACA,QAAQ;AACN,WAAK,KAAK,OAAO,CAAC;AAAA,IACpB;AAAA,IACA,IAAI,OAAO;AACT,aAAO,KAAK,KAAK;AAAA,IACnB;AAAA,IACA,SAAS;AACP,aAAO,IAAI,IAAI,KAAK,IAAI;AAAA,IAC1B;AAAA,IACA,QAAQ,IAAI;AACV,WAAK,KAAK,QAAQ,CAAC,MAAM;AACvB,WAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,IACA,OAAO;AACL,aAAO,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,OAAO;AAAA,IAC3C;AAAA,IACA,SAAS;AACP,aAAO,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,OAAO;AAAA,IAC3C;AAAA,IACA,UAAU;AACR,aAAO,IAAI,IAAI,KAAK,IAAI,EAAE,QAAQ;AAAA,IACpC;AAAA,IACA,KAAK,OAAO,WAAW,IAAI;AACzB,aAAO;AAAA,IACT;AAAA,IACA,CAAC,OAAO,QAAQ,IAAI;AAClB,aAAO,KAAK,QAAQ;AAAA,IACtB;AAAA,EACF,CAAC;AACD,SAAO,iBAAiB,KAAK;AAAA,IAC3B,MAAM;AAAA,MACJ,YAAY;AAAA,IACd;AAAA,IACA,MAAM;AAAA,MACJ,YAAY;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,IACd;AAAA,EACF,CAAC;AACD,SAAO,KAAK,GAAG;AACf,SAAO;AACT;;;AC5YO,IAAM,cAAc;EACzB,gBAAgB,KAAmB,UAAmB;AACpD,UAAM,cAAc,qCAAW;AAE/B,QAAI,gBAAgB,QAAW;AAC7B,aAAOC,eAAc,iBAAiB,GAAG;IAC3C;AAEA,WAAO;EACT;EACA,wBAA2B,SAAkC;AAC3D,QAAI,CAAC,WAAW,CAAC,QAAQ,QAAQ;AAC/B,aAAO;IACT;AAEA,QAAI,eAAe,WAAU,GAAI;AAC/B,UAAI,eAAe,MAAK,GAAI;AAC1B,eAAO,QAAQ,OAAO,CAAAC,OAAKA,OAAM,QAAQ;MAC3C;AACA,UAAI,eAAe,MAAK,GAAI;AAC1B,eAAO,QAAQ,OAAO,CAAAA,OAAKA,OAAM,GAAG;MACtC;AACA,UAAI,eAAe,UAAS,GAAI;AAC9B,eAAO,QAAQ,OAAO,CAAAA,OAAK,CAAC,CAAC,YAAY,GAAG,EAAE,SAASA,EAAC,CAAC;MAC3D;IACF;AAEA,WAAO;EACT;;;;AC8KF,IAAM,QAAQ,MAA8B;EAC1C,UAAUC,eAAc;EACxB,WAAW;EACX,SAAS;EACT,YAAY;EACZ,qBAAqBA,eAAc;EACnC,qBAAqB;EACrB,mCAAmC;EACnC,gBAAgB,CAAA;CACjB;AAGM,IAAM,oBAAoB;EAC/B;EAEA,aAAiC,KAAQ,UAAoD;AAC3F,WAAO,aAAO,OAAO,KAAK,QAAQ;EACpC;EAEA,WAAW,SAA+B;AACxC,WAAO,OAAO,OAAO,OAAO;EAC9B;EAEA,kBAAkB,gBAAwD;AArO5E;AAsOI,QAAI,CAAC,gBAAgB;AACnB;IACF;AAEA,UAAM,oBAAoB,EAAE,GAAG,MAAM,gBAAgB,GAAG,eAAc;AACtE,UAAM,iBAAiB;AAEvB,SAAI,WAAM,mBAAN,mBAAsB,SAAS;AACjC,YAAM,eAAe,UAAU,YAAY,wBACzC,MAAM,eAAe,OAAO;IAEhC;EACF;EAEA,YAAY,UAAwD;AAClE,QAAI,CAAC,UAAU;AACb;IACF;AAEA,QAAI,CAAC,MAAM,UAAU;AACnB,YAAM,WAAWA,eAAc;IACjC;AAEA,UAAM,cAAc,EAAE,GAAG,MAAM,UAAU,GAAG,SAAQ;AACpD,UAAM,WAAW;EACnB;EAEA,aAAa,WAA8C;AACzD,UAAM,YAAY;EACpB;EAEA,iBAAiB,eAAsD;AACrE,UAAM,gBAAgB;EACxB;EAEA,cAAc,YAAgD;AAC5D,UAAM,aAAa;EACrB;EAEA,oBAAoB,kBAA4D;AAC9E,UAAM,mBAAmB;EAC3B;EAEA,oBAAoB,kBAA4D;AAC9E,UAAM,mBAAmB;EAC3B;EAEA,qBAAqB,mBAA8D;AACjF,UAAM,oBAAoB;EAC5B;EAEA,UAAU,QAAwC;AAChD,UAAM,SAAS;EACjB;EAEA,sBAAsB,oBAAgE;AACpF,UAAM,qBAAqB;EAC7B;EAEA,oBAAoB,kBAA4D;AAC9E,UAAM,mBAAmB;EAC3B;EAEA,iBAAiB,eAAsD;AACrE,UAAM,gBAAgB;EACxB;EAEA,iBAAiB,eAAsD;AACrE,UAAM,gBAAgB;EACxB;EAEA,uBAAuB,qBAAkE;AACvF,UAAM,sBAAsB;EAC9B;EAEA,cAAc,YAAgD;AAC5D,UAAM,aAAa;EACrB;EAEA,YAAY,UAA4C;AACtD,UAAM,WAAW;EACnB;EAEA,iBAAiB,eAAsD;AACrE,UAAM,gBAAgB;EACxB;EAEA,kBAAkB,eAAsD;AACtE,UAAM,gBAAgB;EACxB;EAEA,SAAS,OAAsC;AAC7C,UAAM,QAAQ;EAChB;EAEA,uBAAuB,qBAAkE;AACvF,UAAM,sBAAsB;EAC9B;EAEA,qBAAqB,mBAA8D;AACjF,UAAM,oBAAoB;EAC5B;EAEA,oBAAoB,kBAA4D;AAC9E,UAAM,mBAAmB;EAC3B;EAEA,iBAAiB,eAAsD;AACrE,UAAM,gBAAgB;EACxB;EAEA,wBACE,sBAAiF;AAEjF,UAAM,oCAAoC;EAC5C;EAEA,wBAAwB,sBAAoE;AAC1F,UAAM,uBAAuB;EAC/B;EAEA,QAAQ,MAAoC;AAC1C,UAAM,OAAO;EACf;EAEA,uBAAuB,qBAAoC;AACzD,UAAM,WAAW;MACf,GAAG,MAAM;MACT;;EAEJ;EAEA,uBAAuB,qBAAoC;AACzD,UAAM,WAAW;MACf,GAAG,MAAM;MACT;;EAEJ;EAEA,gBAAgB,cAA8B;AAC5C,UAAM,iBAAiB;MACrB,GAAG,MAAM;MACT,SAAS;;EAEb;EAEA,mBAAmB,iBAAwB;AACzC,UAAM,WAAW;MACf,GAAG,MAAM;MACT;;EAEJ;EAEA,kBAAkB,gBAAwD;AACxE,UAAM,iBAAiB;EACzB;EAEA,yBAAyB,uBAAsE;AAC7F,UAAM,wBAAwB;EAChC;EAEA,mBAAmB,iBAA0D;AAC3E,UAAM,kBAAkB;EAC1B;EAEA,uBAAuB,qBAAkE;AACvF,UAAM,sBAAsB;EAC9B;EAEA,uBACE,qBAA6E,CAAA,GAAE;AAE/E,WAAO,QAAQ,kBAAkB,EAAE,QAAQ,CAAC,CAAC,WAAW,WAAW,MAAK;AACtE,UAAI,aAAa;AAEf,cAAM,oBAAoB,SAAS,IAAI;MACzC;IACF,CAAC;EACH;EAEA,mCACE,iCAA0F;AAE1F,UAAM,kCAAkC;EAC1C;EAEA,qCAAkC;AAChC,WAAO,MAAM;EACf;EAEA,cAAW;AACT,WAAO,SAAS,KAAK;EACvB;;;;ACpZF,eAAe,aAAa,MAA8B;AACxD,QAAM,WAAW,MAAM,MAAM,GAAG,IAAI;AACpC,MAAI,CAAC,SAAS,IAAI;AAEhB,UAAM,MAAM,IAAI,MAAM,qBAAqB,SAAS,MAAM,IAAI;MAC5D,OAAO;KACR;AACD,UAAM;EACR;AAEA,SAAO;AACT;AAGM,IAAO,YAAP,MAAgB;EAIpB,YAAmB,EAAE,SAAAC,UAAS,SAAQ,GAAW;AAC/C,SAAK,UAAUA;AACf,SAAK,WAAW;EAClB;EAEO,MAAM,IAAO,EAAE,SAAS,QAAQ,OAAO,GAAG,KAAI,GAAoB;AACvE,UAAM,MAAM,KAAK,UAAU,IAAI;AAC/B,UAAM,WAAW,MAAM,UAAU,KAAK,EAAE,QAAQ,OAAO,SAAS,QAAQ,MAAK,CAAE;AAE/E,WAAO,SAAS,KAAI;EACtB;EAEO,MAAM,QAAQ,EAAE,SAAS,QAAQ,GAAG,KAAI,GAAoB;AACjE,UAAM,MAAM,KAAK,UAAU,IAAI;AAC/B,UAAM,WAAW,MAAM,UAAU,KAAK,EAAE,QAAQ,OAAO,SAAS,OAAM,CAAE;AAExE,WAAO,SAAS,KAAI;EACtB;EAEO,MAAM,KAAQ,EAAE,MAAM,SAAS,QAAQ,GAAG,KAAI,GAAiB;AACpE,UAAM,MAAM,KAAK,UAAU,IAAI;AAC/B,UAAM,WAAW,MAAM,UAAU,KAAK;MACpC,QAAQ;MACR;MACA,MAAM,OAAO,KAAK,UAAU,IAAI,IAAI;MACpC;KACD;AAED,WAAO,SAAS,KAAI;EACtB;EAEO,MAAM,IAAO,EAAE,MAAM,SAAS,QAAQ,GAAG,KAAI,GAAiB;AACnE,UAAM,MAAM,KAAK,UAAU,IAAI;AAC/B,UAAM,WAAW,MAAM,UAAU,KAAK;MACpC,QAAQ;MACR;MACA,MAAM,OAAO,KAAK,UAAU,IAAI,IAAI;MACpC;KACD;AAED,WAAO,SAAS,KAAI;EACtB;EAEO,MAAM,OAAU,EAAE,MAAM,SAAS,QAAQ,GAAG,KAAI,GAAiB;AACtE,UAAM,MAAM,KAAK,UAAU,IAAI;AAC/B,UAAM,WAAW,MAAM,UAAU,KAAK;MACpC,QAAQ;MACR;MACA,MAAM,OAAO,KAAK,UAAU,IAAI,IAAI;MACpC;KACD;AAED,WAAO,SAAS,KAAI;EACtB;EAEQ,UAAU,EAAE,MAAM,OAAM,GAAoB;AAClD,UAAM,MAAM,IAAI,IAAI,MAAM,KAAK,OAAO;AACtC,QAAI,QAAQ;AACV,aAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAK;AAC9C,YAAI,OAAO;AACT,cAAI,aAAa,OAAO,KAAK,KAAK;QACpC;MACF,CAAC;IACH;AACA,QAAI,KAAK,UAAU;AACjB,UAAI,aAAa,OAAO,YAAY,KAAK,QAAQ;IACnD;AAEA,WAAO;EACT;;;;AC5EF,IAAM,gBAAgB,OAAO,OAAiC;EAC5D,SAAS;EACT,QAAQ,CAAA;CACT;AAED,IAAM,MAAM,IAAI,UAAU,EAAE,SAAS,eAAe,gBAAe,GAAI,UAAU,KAAI,CAAE;AAGvF,IAAM,wBAAwB;AAC9B,IAAM,gBAAgB,KAAK;AAG3B,IAAMC,SAAQ,MAAgC;EAC5C,GAAG;CACJ;AAGM,IAAM,sBAAsB;EACjC,OAAAA;EAEA,aACE,KACA,UAAsD;AAEtD,WAAO,aAAOA,QAAO,KAAK,QAAQ;EACpC;EAEA,MAAM,UAAU,OAAc,UAAgC;AAC5D,QAAI,CAACA,OAAM,SAAS;AAClB;IACF;AAGA,UAAM,MAAM,KAAK,IAAG;AACpB,UAAM,eAAeA,OAAM,OAAO,OAAO,WAAQ;AAC/C,YAAM,YAAY,IAAI,KAAK,MAAM,WAAW,aAAa,EAAE,EAAE,QAAO;AAEpE,aAAO,MAAM,YAAY;IAC3B,CAAC;AAED,QAAI,aAAa,UAAU,uBAAuB;AAGhD;IACF;AAEA,UAAM,aAA6B;MACjC,MAAM;MACN,OAAO;MACP,YAAY;QACV,WAAW,MAAM;QACjB,cAAc,MAAM;QACpB,YAAY,MAAM;QAClB,YAAW,oBAAI,KAAI,GAAG,YAAW;;;AAIrC,IAAAA,OAAM,OAAO,KAAK,UAAU;AAC5B,QAAI;AACF,UAAI,OAAO,WAAW,aAAa;AACjC;MACF;AAEA,YAAM,EAAE,WAAW,SAAS,WAAU,IAAK,kBAAkB;AAE7D,YAAM,IAAI,KAAK;QACb,MAAM;QACN,QAAQ;UACN;UACA,IAAI;UACJ,IAAI,cAAc;;QAEpB,MAAM;UACJ,SAAS,eAAe,QAAO;UAC/B,KAAK,OAAO,SAAS;UACrB,QAAQ,OAAO,SAAS;UACxB,YAAW,oBAAI,KAAI,GAAG,YAAW;UACjC,OAAO;YACL,MAAM;YACN,OAAO;YACP,WAAW,MAAM;YACjB,cAAc,MAAM;YACpB,YAAY,MAAM;;;OAGvB;IACH,QAAQ;IAER;EACF;EAEA,SAAM;AACJ,IAAAA,OAAM,UAAU;EAClB;EAEA,UAAO;AACL,IAAAA,OAAM,UAAU;EAClB;EAEA,cAAW;AACT,IAAAA,OAAM,SAAS,CAAA;EACjB;;;;AC5HI,IAAO,cAAP,MAAO,qBAAoB,MAAK;EAIpC,YAAY,SAAiB,UAAkC,eAAuB;AACpF,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,gBAAgB;AAGrB,WAAO,eAAe,MAAM,aAAY,SAAS;AAEjD,QAAI,iCAAiC;AACrC,QACE,yBAAyB,SACzB,OAAO,cAAc,UAAU,YAC/B,cAAc,OACd;AACA,YAAM,qBAAqB,cAAc;AAKzC,YAAM,oBAAoB,mBAAmB,QAAQ,IAAI;AAEzD,UAAI,oBAAoB,IAAI;AAC1B,cAAM,iBAAiB,mBAAmB,UAAU,oBAAoB,CAAC;AACzE,aAAK,QAAQ,GAAG,KAAK,IAAI,KAAK,KAAK,OAAO;EAAK,cAAc;AAC7D,yCAAiC;MACnC;IACF;AAEA,QAAI,CAAC,gCAAgC;AAMnC,UAAI,MAAM,mBAAmB;AAC3B,cAAM,kBAAkB,MAAM,YAAW;MAC3C,WAAW,CAAC,KAAK,OAAO;AAOtB,aAAK,QAAQ,GAAG,KAAK,IAAI,KAAK,KAAK,OAAO;MAC5C;IACF;EACF;;AAIF,SAAS,aAAa,KAAU,iBAAuC;AACrE,QAAM,QACJ,eAAe,cACX,MACA,IAAI,YAAY,eAAe,QAAQ,IAAI,UAAU,OAAO,GAAG,GAAG,iBAAiB,GAAG;AAE5F,sBAAoB,UAAU,OAAO,MAAM,QAAQ;AACnD,QAAM;AACR;AAEM,SAAU,kBACdC,cACA,kBAA0C,sBAAoB;AAE9D,QAAM,gBAA4B,CAAA;AAElC,SAAO,KAAKA,YAAU,EAAE,QAAQ,SAAM;AACpC,UAAM,WAAWA,aAAW,GAAG;AAE/B,QAAI,OAAO,aAAa,YAAY;AAClC,UAAI,UAAU;AAEd,UAAI,SAAS,YAAY,SAAS,iBAAiB;AACjD,kBAAU,UAAU,SAAqC;AACvD,cAAI;AACF,mBAAO,MAAM,SAAS,GAAG,IAAI;UAC/B,SAAS,KAAK;AACZ,mBAAO,aAAa,KAAK,eAAe;UAC1C;QACF;MACF,OAAO;AACL,kBAAU,IAAI,SAAqC;AACjD,cAAI;AACF,mBAAO,SAAS,GAAG,IAAI;UACzB,SAAS,KAAK;AACZ,mBAAO,aAAa,KAAK,eAAe;UAC1C;QACF;MACF;AAEA,oBAAc,GAAG,IAAI;IACvB,OAAO;AACL,oBAAc,GAAG,IAAI;IACvB;EACF,CAAC;AAED,SAAO;AACT;;;AC1FA,IAAMC,SAAQ,MAA4B;EACxC,cAAc,CAAA;EACd,eAAe,CAAA;EACf,aAAa,CAAA;EACb,iBAAiB,CAAA;EACjB,aAAa,CAAA;EACb,gBAAgB,CAAA;CACjB;AAGD,IAAM,aAAa;EACjB,OAAAA;EAEA,uBAAuB,UAAgE;AACrF,WAAO,UAAIA,OAAM,eAAe,MAAM,SAASA,OAAM,aAAa,CAAC;EACrE;EAEA,aAAiC,KAAQ,UAAkD;AACzF,WAAO,aAAOA,QAAO,KAAK,QAAQ;EACpC;EAEA,UAAU,UAAkD;AAC1D,WAAO,UAAIA,QAAO,MAAM,SAASA,MAAK,CAAC;EACzC;EAEA,eAAe,KAAa,OAAa;AACvC,IAAAA,OAAM,aAAa,GAAG,IAAI;EAC5B;EAEA,gBAAgB,KAAa,OAAa;AACxC,IAAAA,OAAM,cAAc,GAAG,IAAI;EAC7B;EAEA,cAAc,KAAa,OAAa;AACtC,IAAAA,OAAM,YAAY,GAAG,IAAI;EAC3B;EAEA,kBAAkB,KAAa,OAAa;AAC1C,IAAAA,OAAM,kBAAkB,EAAE,GAAGA,OAAM,iBAAiB,CAAC,GAAG,GAAG,MAAK;EAClE;EAEA,cAAc,KAAa,OAAa;AACtC,IAAAA,OAAM,YAAY,GAAG,IAAI;EAC3B;EAEA,iBAAiB,KAAa,OAAa;AACzC,IAAAA,OAAM,eAAe,GAAG,IAAI;EAC9B;;AAIK,IAAM,kBAAkB,kBAAkB,UAAU;;;ACxD3D,IAAM,oBAAoD;;EAExD,QAAQ;;EAER,QAAQ;;EAER,UAAU;;EAEV,QAAQ;;EAER,QAAQ;;AAIV,IAAMC,SAAQ,MAAsB;EAClC,sBAAsB,CAAA;CACvB;AAGM,IAAM,YAAY;EACvB,MAAM,iBAAiB,SAAgB;AACrC,QAAI,CAAC,SAAS;AACZ,aAAO;IACT;AAEA,UAAM,cAAc,kBAAkB,OAAO;AAE7C,WAAO,KAAK,mBAAmB,OAAO;EACxC;EAEA,MAAM,kBAAkB,SAAgB;AACtC,QAAI,CAAC,SAAS;AACZ,aAAO;IACT;AAEA,UAAM,gBAAgB,KAAK,oBAAoB,OAAO;AAGtD,QAAI,eAAe;AACjB,aAAO;IACT;AAGA,QAAI,CAACA,OAAM,qBAAqB,OAAO,GAAG;AACxC,MAAAA,OAAM,qBAAqB,OAAO,IAAI,cAAc,mBAAmB,OAAO;IAChF;AAEA,UAAMA,OAAM,qBAAqB,OAAO;AAExC,WAAO,KAAK,oBAAoB,OAAO;EACzC;EAEA,mBAAmB,SAAgB;AACjC,QAAI,CAAC,SAAS;AACZ,aAAO;IACT;AAEA,WAAO,gBAAgB,MAAM,aAAa,OAAO;EACnD;EAEA,eAAe,QAAiB;AAC9B,QAAI,iCAAQ,WAAW;AACrB,aAAO,iCAAQ;IACjB;AAEA,QAAI,iCAAQ,UAAU;AACpB,aAAO,gBAAgB,MAAM,aAAa,OAAO,QAAQ;IAC3D;AAEA,WAAO;EACT;EAEA,gBAAgB,SAAqB;AArFvC,gBAAAC;AAsFI,SAAI,wCAAS,WAAT,mBAAiB,UAAU;AAC7B,cAAO,wCAAS,WAAT,mBAAiB;IAC1B;AAEA,SAAIA,MAAA,mCAAS,WAAT,gBAAAA,IAAiB,SAAS;AAC5B,aAAO,gBAAgB,MAAM,cAAc,QAAQ,OAAO,OAAO;IACnE;AAEA,WAAO;EACT;EAEA,oBAAoB,SAAgB;AAClC,QAAI,CAAC,SAAS;AACZ,aAAO;IACT;AAEA,WAAO,gBAAgB,MAAM,cAAc,OAAO;EACpD;EAEA,kBAAkB,WAAqB;AACrC,QAAI,uCAAW,UAAU;AACvB,aAAO,UAAU;IACnB;AAEA,QAAI,uCAAW,SAAS;AACtB,aAAO,gBAAgB,MAAM,gBAAgB,UAAU,OAAO;IAChE;AAEA,WAAO;EACT;EAEA,cAAc,OAAqB;AACjC,WAAO,gBAAgB,MAAM,cAAc,kBAAkB,KAAK,CAAC;EACrE;;;;AC/GK,IAAM,0BAA0B;EACrC,SAAS;IACP,IAAI;IACJ,KAAK;;EAEP,UAAU;IACR,IAAI;IACJ,KAAK;;EAEP,UAAU;IACR,IAAI;IACJ,KAAK;;;AAIF,IAAM,mBAAmB;;;;;;;EAO9B,6BAA6B,IAAY,WAAyB;AAMhE,UAAM,OAAO,OAAO,SAAS;AAC7B,UAAM,cAAc,mBAAmB,IAAI;AAE3C,QAAI,OAAO,wBAAwB,QAAQ,MAAM,EAAE,aAAa,SAAS;AACvE,YAAM,WAAW,KAAK,WAAW,OAAO,IAAI,UAAU;AACtD,YAAM,OAAO,KAAK,MAAM,GAAG,EAAE,CAAC;AAC9B,YAAM,aAAa,mBAAmB,GAAG,QAAQ,MAAM,IAAI,EAAE;AAE7D,aAAO,SAAS,OAAO,GAAG,wBAAwB,QAAQ,GAAG,cAAc,WAAW,QAAQ,UAAU;IAC1G;AAEA,QAAI,OAAO,wBAAwB,SAAS,MAAM,EAAE,cAAc,SAAS;AACzE,aAAO,SAAS,OAAO,GAAG,wBAAwB,SAAS,GAAG,iBAAiB,WAAW,QAAQ,WAAW;IAC/G;AAEA,QAAI,cAAc,cAAc,MAAM,QAAQ;AAC5C,UAAI,OAAO,wBAAwB,SAAS,MAAM,EAAE,oBAAoB,SAAS;AAC/E,eAAO,SAAS,OAAO,GAAG,wBAAwB,SAAS,GAAG,gBAAgB,WAAW;MAC3F;IACF;EACF;;;;AClDF,IAAMC,iBAAgB,OAAO,OAA6B;EACxD,SAAS;EACT,SAAS;EACT,KAAK;EACL,MAAM;EACN,WAAW;CACZ;AAoBD,IAAMC,SAAQ,MAA4B;EACxC,GAAGD;CACJ;AAGD,IAAME,cAAa;EACjB,OAAAD;EAEA,aAAiC,KAAQ,UAAkD;AACzF,WAAO,aAAOA,QAAO,KAAK,QAAQ;EACpC;EAEA,YAAY,SAA0C,UAAsC,CAAA,GAAE;AAC5F,SAAK,aAAa,EAAE,SAAS,SAAS,WAAW,GAAG,QAAO,CAAE;EAC/D;EAEA,YAAY,SAAwC;AAClD,SAAK,aAAa,EAAE,SAAS,SAAS,UAAS,CAAE;EACnD;EAEA,QAAQ,SAA0C,KAA6C;AAC7F,SAAK,aAAa,EAAE,SAAS,IAAG,CAAE;EACpC;EAEA,UAAU,SAAgB;AACxB,UAAM,eAAe,eAAe,WAAW,OAAO;AACtD,SAAK,aAAa,EAAE,SAAS,cAAc,SAAS,QAAO,CAAE;EAC/D;EAEA,OAAI;AACF,IAAAA,OAAM,UAAUD,eAAc;AAC9B,IAAAC,OAAM,UAAUD,eAAc;AAC9B,IAAAC,OAAM,MAAMD,eAAc;AAC1B,IAAAC,OAAM,OAAOD,eAAc;AAC3B,IAAAC,OAAM,YAAYD,eAAc;EAClC;EAEA,aAAa,EACX,SACA,KACA,UAAU,WACV,YAAYA,eAAc,UAAS,GACc;AACjD,QAAIC,OAAM,MAAM;AACd,MAAAA,OAAM,OAAO;AACb,iBAAW,MAAK;AACd,QAAAA,OAAM,UAAU;AAChB,QAAAA,OAAM,UAAU;AAChB,QAAAA,OAAM,MAAM;AACZ,QAAAA,OAAM,OAAO;AACb,QAAAA,OAAM,YAAY;MACpB,GAAG,GAAG;IACR,OAAO;AACL,MAAAA,OAAM,UAAU;AAChB,MAAAA,OAAM,UAAU;AAChB,MAAAA,OAAM,MAAM;AACZ,MAAAA,OAAM,OAAO;AACb,MAAAA,OAAM,YAAY;IACpB;EACF;;AAGK,IAAM,kBAAkBC;;;ACpD/B,IAAM,kBAAkB;EACtB,oBAAoB;IAClB;MACE,IAAI;MACJ,MAAM;MACN,QAAQ;MACR,UAAU;QACR;UACE,MAAM;UACN,cAAc;UACd,UAAU;UACV,kBAAkB;;QAEpB;UACE,MAAM;UACN,cAAc;UACd,UAAU;UACV,kBAAkB;;;;IAIxB;MACE,IAAI;MACJ,MAAM;MACN,QAAQ;MACR,UAAU;QACR;UACE,MAAM;UACN,cAAc;UACd,UAAU;UACV,kBAAkB;;QAEpB;UACE,MAAM;UACN,cAAc;UACd,UAAU;UACV,kBAAkB;;;;;EAK1B,mBAAmB;IACjB;MACE,IAAI;MACJ,uBAAuB;QACrB;UACE,IAAI;UACJ,KAAK;UACL,KAAK;;QAEP;UACE,IAAI;UACJ,KAAK;UACL,KAAK;;;;IAIX;MACE,IAAI;MACJ,uBAAuB;QACrB;UACE,IAAI;UACJ,KAAK;UACL,KAAK;;QAEP;UACE,IAAI;UACJ,KAAK;UACL,KAAK;;;;;;AAcf,IAAM,UAAU,eAAe,oBAAmB;AAGlD,IAAMC,SAAQ,MAAoC;EAChD,UAAU;EACV,KAAK,IAAI,UAAU,EAAE,SAAS,UAAU,KAAI,CAAE;EAC9C,iBAAiB,EAAE,MAAM,CAAA,GAAI,IAAI,CAAA,EAAE;CACpC;AAGM,IAAM,0BAA0B;EACrC,OAAAA;EAEA,MAAM,IAAO,SAAyB;AACpC,UAAM,EAAE,IAAAC,KAAI,GAAE,IAAK,wBAAwB,iBAAgB;AAC3D,UAAM,YAAY,kBAAkB,MAAM;AAE1C,UAAM,SAAS;MACb,GAAI,QAAQ,UAAU,CAAA;MACtB,IAAAA;MACA;MACA;;AAGF,WAAOD,OAAM,IAAI,IAAO;MACtB,GAAG;MACH;KACD;EACH;EAEA,mBAAgB;AACd,UAAM,EAAE,SAAS,WAAU,IAAK,kBAAkB;AAElD,WAAO;MACL,IAAI,WAAW;MACf,IAAI,cAAc;;EAEtB;EAEA,MAAM,mBAAmB,WAAyB;AAChD,QAAI,CAAC,WAAW;AACd,aAAO;IACT;AACA,QAAI;AACF,UAAI,CAACA,OAAM,gBAAgB,KAAK,QAAQ;AACtC,cAAM,wBAAwB,qBAAoB;MACpD;IACF,SAASE,IAAG;AACV,aAAO;IACT;AAEA,WAAOF,OAAM,gBAAgB,KAAK,SAAS,SAAS;EACtD;EAEA,MAAM,uBAAoB;AACxB,QAAI;AACF,YAAM,kBAAkB,MAAM,wBAAwB,IAEpD;QACA,MAAM;OACP;AAED,MAAAA,OAAM,kBAAkB;AAExB,aAAO;IACT,QAAQ;AACN,aAAOA,OAAM;IACf;EACF;EAEA,MAAM,cAAc,EAClB,SACA,cAAa,GAGd;AACC,UAAM,cAAc,MAAM,wBAAwB,mBAAmB,aAAa;AAElF,QAAI,CAAC,aAAa;AAChB,aAAO,EAAE,QAAQ,IAAI,MAAM,GAAE;IAC/B;AAEA,UAAM,gBAAgB,YAAY,+BAA+B,OAAO;AACxE,QAAI,eAAe;AACjB,aAAO;IACT;AAEA,UAAM,SAAS,MAAM,wBAAwB,IAAmC;MAC9E,MAAM,gBAAgB,OAAO;MAC7B,QAAQ;QACN,QAAQ,gBAAgB,MAAM,oBAC1B,eAAe,gBAAgB,gBAAgB,MAAM,iBAAiB,IACtE;;KAEP;AAED,gBAAY,oBAAoB;MAC9B;MACA,UAAU;MACV,WAAW,KAAK,IAAG;KACpB;AAED,WAAO;EACT;EAEA,MAAM,kBAAkB,EACtB,SACA,QACA,QACA,QACA,OACA,QAAO,GAC0B;AA5OrC;AA6OI,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,aAAO,EAAE,MAAM,CAAA,GAAI,MAAM,OAAS;IACpC;AAEA,WAAO,wBAAwB,IAAuC;MACpE,MAAM,eAAe,OAAO;MAC5B,QAAQ;QACN;QACA;QACA;;MAEF;MACA;KACD;EACH;EAEA,MAAM,eAAe,EAAE,QAAQ,aAAa,MAAAG,OAAM,IAAAC,KAAI,SAAQ,GAAiC;AAhQjG;AAiQI,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,aAAO,EAAE,QAAQ,CAAA,EAAE;IACrB;AAEA,WAAO,wBAAwB,IAAoC;MACjE,MAAM;MACN,SAAS;QACP,gBAAgB;;MAElB,QAAQ;QACN;QACA;QACA,MAAAD;QACA,IAAAC;QACA;;KAEH;EACH;EAEA,MAAM,gBAAgB,EACpB,QAAO,GACwB;AAzRnC;AA0RI,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,aAAO,EAAE,QAAQ,CAAA,EAAE;IACrB;AAEA,WAAO,wBAAwB,IAAqC;MAClE,MAAM;MACN,QAAQ,EAAE,QAAO;KAClB;EACH;EAEA,MAAM,gBAAgB,EAAE,UAAS,GAAkC;AAvSrE;AAwSI,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,aAAO,EAAE,WAAW,CAAA,EAAE;IACxB;AAEA,WAAOJ,OAAM,IAAI,KAAsC;MACrD,MAAM;MACN,MAAM;QACJ,UAAU;QACV;QACA,WAAW,kBAAkB,MAAM;;MAErC,SAAS;QACP,gBAAgB;;KAEnB;EACH;EAEA,MAAM,mBAAmB,EAAE,cAAc,YAAW,GAAqC;AA5T3F;AA6TI,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,aAAO,EAAE,WAAW,IAAG;IACzB;AAEA,WAAO,wBAAwB,IAAwC;MACrE,MAAM;MACN,QAAQ;QACN;QACA;;MAEF,SAAS;QACP,gBAAgB;;KAEnB;EACH;EAEA,MAAM,cAAc,EAAE,QAAO,GAAgC;AAhV/D;AAiVI,UAAM,EAAE,IAAAC,KAAI,GAAE,IAAK,wBAAwB,iBAAgB;AAE3D,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,qCAAqC;IACvD;AAEA,WAAO,wBAAwB,IAAmC;MAChE,MAAM;MACN,SAAS;QACP,gBAAgB;;MAElB,QAAQ;QACN;QACA,IAAAA;QACA;;KAEH;EACH;EAEA,MAAM,qBAAqB,EACzB,QACA,MAAAE,OACA,IAAAC,KACA,aACA,gBAAe,GAC0B;AA7W7C;AA8WI,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,iCAAiC;IACnD;AAEA,WAAOJ,OAAM,IAAI,KAAgD;MAC/D,MAAM;MACN,SAAS;QACP,gBAAgB;;MAElB,MAAM;QACJ;QACA,QAAQ;UACN,UAAUK,eAAc;;QAE1B,WAAW,kBAAkB,MAAM;QACnC,MAAAF;QACA,IAAAC;QACA;QACA;;KAEH;EACH;EAEA,MAAM,wBAAwB,EAC5B,MAAAD,OACA,IAAAC,KACA,YAAW,GACiC;AA5YhD;AA6YI,UAAM,EAAE,IAAAH,KAAI,GAAE,IAAK,wBAAwB,iBAAgB;AAE3D,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,iCAAiC;IACnD;AAEA,WAAO,wBAAwB,IAAkD;MAC/E,MAAM;MACN,SAAS;QACP,gBAAgB;;MAElB,QAAQ;QACN;QACA,MAAAE;QACA,IAAAC;QACA,IAAAH;QACA;;KAEH;EACH;EAEA,MAAM,WAAW,SAAiB,SAAkB,aAAoB;AAra1E;AAsaI,UAAM,EAAE,IAAAA,KAAI,GAAE,IAAK,wBAAwB,iBAAgB;AAE3D,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,sBAAgB,UAAU,2BAA2B;AAErD,aAAO,EAAE,UAAU,CAAA,EAAE;IACvB;AACA,UAAM,cAAc,GAAG,OAAO,IAAI,OAAO;AACzC,UAAM,gBAAgB,YAAY,8BAA8B,WAAW;AAC3E,QAAI,eAAe;AACjB,aAAO;IACT;AAEA,UAAM,UAAU,MAAM,wBAAwB,IAAkC;MAC9E,MAAM,eAAe,OAAO;MAC5B,QAAQ;QACN,UAAU;QACV;QACA;QACA,IAAAA;QACA;;KAEH;AAED,gBAAY,mBAAmB;MAC7B;MACA;MACA,WAAW,KAAK,IAAG;KACpB;AAED,WAAO;EACT;EAEA,MAAM,cAAc,MAAY;AA1clC;AA2cI,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,aAAO,EAAE,WAAW,CAAA,GAAI,YAAY,CAAA,EAAE;IACxC;AAEA,WAAO,wBAAwB,IAAgC;MAC7D,MAAM,uBAAuB,IAAI;MACjC,QAAQ,EAAE,YAAY,IAAG;KAC1B;EACH;EAEA,MAAM,qBAAqB,EAAE,QAAO,GAAuB;AAxd7D;AAydI,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,aAAO,CAAA;IACT;AAEA,WAAO,wBAAwB,IAAkC;MAC/D,MAAM,uBAAuB,OAAO;MACpC,QAAQ;QACN,QAAQ,kBAAkB,MAAM;QAChC,YAAY;;KAEf;EACH;EAEA,MAAM,sBAAsB,MAAY;AAze1C;AA0eI,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,aAAO,EAAE,aAAa,CAAA,EAAE;IAC1B;AAEA,WAAO,wBAAwB,IAAqC;MAClE,MAAM,2BAA2B,IAAI;MACrC,QAAQ,EAAE,MAAM,WAAU;KAC3B;EACH;EAEA,MAAM,gBAAgB,EACpB,UACA,SACA,SACA,UAAS,GACuB;AA5fpC;AA6fI,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,aAAO,EAAE,SAAS,MAAK;IACzB;AAEA,WAAOD,OAAM,IAAI,KAAK;MACpB,MAAM;MACN,MAAM,EAAE,WAAW,UAAU,SAAS,SAAS,UAAS;MACxD,SAAS;QACP,gBAAgB;;KAEnB;EACH;EAEA,MAAM,kBAAkB,EACtB,oBACA,eACA,gBACA,gBACA,cAAa,GACS;AAnhB1B;AAohBI,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,aAAO;IACT;AAEA,UAAM,WAAW,MAAMA,OAAM,IAAI,KAAsB;MACrD,MAAM;MACN,QAAQ;QACN,WAAW,kBAAkB,MAAM;;MAErC,MAAM;QACJ;QACA;QACA;QACA,mBAAmB;QACnB,oBAAoB;QACpB,kBAAkB;;KAErB;AAED,WAAO,SAAS;EAClB;EAEA,MAAM,mBAAgB;AA7iBxB;AA8iBI,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,aAAO,EAAE,mBAAmB,CAAA,GAAI,oBAAoB,CAAA,EAAE;IACxD;AAEA,QAAI;AACF,YAAM,WAAW,MAAM,wBAAwB,IAG5C;QACD,MAAM;OACP;AAED,aAAO;IACT,SAASE,IAAG;AACV,aAAO;IACT;EACF;EAEA,MAAM,eAAe,EACnB,kBACA,iBACA,QACA,QAAO,GACM;AAxkBjB;AAykBI,QAAI;AACF,YAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,UAAI,CAAC,aAAa;AAChB,eAAO;MACT;AAEA,YAAM,WAAW,MAAMF,OAAM,IAAI,KAAkB;QACjD,MAAM;QACN,QAAQ;UACN,WAAW,kBAAkB,MAAM;;QAErC,MAAM;UACJ;UACA;UACA;UACA;;OAEH;AAED,aAAO;IACT,SAASE,IAAG;AAEV,aAAO;QACL,aAAa,EAAE,QAAQ,UAAU,gBAAgB,GAAE;QACnD,YAAY,EAAE,QAAQ,UAAU,gBAAgB,GAAE;QAClD,iBAAiB,EAAE,QAAQ,UAAU,gBAAgB,GAAE;QACvD,cAAc,EAAE,QAAQ,UAAU,gBAAgB,GAAE;QACpD,gBAAgB,EAAE,QAAQ,UAAU,gBAAgB,GAAE;QACtD,SAAS;;IAEb;EACF;EAEA,MAAM,iBAAiB,aAAwB;AA5mBjD;AA6mBI,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,aAAO,CAAA;IACT;AAEA,WAAO,wBAAwB,IAAI;MACjC,MAAM,gBAAgB,WAAW;KAClC;EACH;EACA,MAAM,mBAAmB,SAAwB,KAAa,WAAiB;AAxnBjF;AAynBI,UAAM,cAAc,MAAM,wBAAwB,oBAChD,qBAAgB,MAAM,sBAAtB,mBAAyC,aAAa;AAExD,QAAI,CAAC,aAAa;AAChB,aAAO,EAAE,SAAS,MAAK;IACzB;AAEA,WAAOF,OAAM,IAAI,KAAK;MACpB,MAAM,gBAAgB,OAAO;MAC7B,QAAQ;QACN,WAAW,kBAAkB,MAAM;;MAErC,MAAM;QACJ;QACA;;KAEH;EACH;EACA,YAAY,UAAuB;AACjC,IAAAA,OAAM,WAAW;AACjB,IAAAA,OAAM,MAAM,IAAI,UAAU,EAAE,SAAS,SAAQ,CAAE;EACjD;;;;AC/lBF,IAAMM,SAAQ,MAA8B;EAC1C,YAAY;EACZ,cAAc,CAAA;EACd,sBAAsB;EACtB,eAAe,oBAAI,IAAG;EACtB,aAAa,CAAA;CACd;AAGD,IAAMC,cAAa;EACjB,OAAAD;EAEA,aAAa,UAA4C;AACvD,QAAI,CAAC,UAAU;AACb;IACF;AAEA,WAAO,OAAOA,QAAO,IAAI,QAAQ,CAAC;EACpC;EAEA,UAAU,UAA+C;AACvD,WAAO,gBAAgB,mBAAmB,gBAAgB,CAAAE,kBAAe;AACvE,UAAIA,eAAc;AAChB,eAAO,SAASA,aAAY;MAC9B;AAEA,aAAO;IACT,CAAC;EACH;EAEA,aACE,UACA,UACA,OAAsB;AAEtB,QAAI,OAA8C;AAElD,WAAO,gBAAgB,mBACrB,gBACA,CAAAA,kBAAe;AACb,UAAIA,eAAc;AAChB,cAAM,YAAYA,cAChB,QAAqC;AAEvC,YAAI,SAAS,WAAW;AACtB,iBAAO;AACP,mBAAS,SAAS;QACpB;MACF;IACF,GACA,KAAK;EAET;EAEA,UAAU,QAA0C,OAAiC;AACnF,oBAAgB,eAAe,UAAU,QAAQ,KAAK;EACxD;EAEA,eAAe,OAAiC;AAC9C,WAAO,gBAAgB,eAAe,eAAe,KAAK;EAC5D;EAEA,eACE,aACA,OAAiC;AAEjC,UAAM,aAAa,cAAc,eAAe,gBAAgB,WAAW,IAAI;AAE/E,QAAI,UAAU,gBAAgB,MAAM,aAAa;AAC/C,sBAAgB,MAAM,oBAAoB;IAC5C;AAEA,oBAAgB,eAAe,eAAe,aAAa,KAAK;AAChE,oBAAgB,eAAe,WAAW,YAAY,KAAK;EAC7D;EAEA,WACE,SACA,eACA,OAAqB;AAErB,oBAAgB,eAAe,WAAW,SAAS,KAAK;AACxD,oBAAgB,eAAe,iBAAiB,eAAe,KAAK;EACtE;EAEA,eAAe,aAAoD,OAAqB;AACtF,oBAAgB,eAAe,eAAe,aAAa,KAAK;EAClE;EAEA,gBAAgB,cAAsD,OAAsB;AAC1F,oBAAgB,eAAe,gBAAgB,cAAc,KAAK;EACpE;EAEA,QAAQ,MAAsC,OAAiC;AAC7E,oBAAgB,eAAe,QAAQ,MAAM,KAAK;EACpD;EAEA,sBACE,aACA,OAAiC;AAEjC,oBAAgB,eAAe,sBAAsB,aAAa,KAAK;EACzE;EAEA,wBAAwB,YAAqB,OAAiC;AAC5E,oBAAgB,eAAe,wBAAwB,YAAY,KAAK;EAC1E;EAEA,cAAc,YAAgD;AAC5D,oBAAgB,eAAe,cAAc,YAAY,gBAAgB,MAAM,WAAW;EAC5F;EAEA,gBACE,cACA,OAAiC;AAEjC,QAAI,cAAc;AAChB,sBAAgB,eAAe,gBAAgB,cAAc,KAAK;IACpE;EACF;EACA,yBAAyB,SAAiB,OAAiC;AACzE,oBAAgB,eAAe,yBAAyB,SAAS,KAAK;EACxE;EAEA,eAAyC,UAA+B,WAAY;AAClF,oBAAgB,eAAe,eAAe,UAAU,SAAS;EACnE;EAEA,gBAAgB,SAAiB,OAAe,OAAiC;AAC/E,UAAM,MAAM,gBAAgB,eAAe,iBAAiB,KAAK,KAAK,oBAAI,IAAG;AAC7E,QAAI,IAAI,SAAS,KAAK;AACtB,oBAAgB,eAAe,iBAAiB,KAAK,KAAK;EAC5D;EAEA,mBAAmB,SAAiB,OAAiC;AACnE,UAAM,MAAM,gBAAgB,eAAe,iBAAiB,KAAK,KAAK,oBAAI,IAAG;AAC7E,QAAI,OAAO,OAAO;AAClB,oBAAgB,eAAe,iBAAiB,KAAK,KAAK;EAC5D;EAEA,uBACE,qBACA,OAAqB;AAErB,oBAAgB,eAAe,uBAAuB,qBAAqB,OAAO,KAAK;EACzF;EAEA,wBACE,sBACA,OAAqB;AAErB,oBAAgB,eACd,yBACA;MACE,GAAGF,OAAM;MACT,CAAC,KAAK,GAAG;OAEX,KAAK;EAET;EAEA,yBAAyB,uBAA4C;AACnE,IAAAA,OAAM,wBAAwB;EAChC;EAEA,kBACE,gBACA,OAAiC;AAEjC,QAAI,gBAAgB;AAClB,sBAAgB,eAAe,kBAAkB,gBAAgB,KAAK;IACxE;EACF;EAEA,gBACE,cACA,OAAiC;AAEjC,oBAAgB,eACd,gBACA,eAAe,IAAI,YAAY,IAAI,QACnC,KAAK;EAET;EAEA,gBACE,cACA,OAAiC;AAEjC,oBAAgB,eAAe,gBAAgB,cAAc,KAAK;EACpE;EAEA,MAAM,kBAAkB,SAAkC;AA/O5D;AAgPI,IAAAA,OAAM,iBAAiB;AACvB,UAAM,WAAU,qBAAgB,MAAM,sBAAtB,mBAAyC;AACzD,UAAM,SAAQ,qBAAgB,MAAM,sBAAtB,mBAAyC;AACvD,UAAM,cAAc,gBAAgB,MAAM;AAC1C,UAAM,UAAU,cAAc,eAAe,gBAAgB,WAAW,IAAI;AAC5E,QACEA,OAAM,aACN,CAAC,eAAe,eAAeA,OAAM,WAAW,KAAKG,eAAc,UAAU,GAC7E;AACA,MAAAH,OAAM,iBAAiB;AAEvB,aAAO,CAAA;IACT;AAEA,QAAI;AACF,UAAI,WAAW,WAAW,OAAO;AAC/B,cAAM,WAAW,MAAM,wBAAwB,WAAW,SAAS,OAAO;AAM1E,cAAM,mBAAmB,SAAS,SAAS,OACzC,aAAW,QAAQ,SAAS,aAAa,GAAG;AAG9C,0BAAkB,gBAAgB,kBAAkB,KAAK;AACzD,QAAAA,OAAM,YAAY;AAClB,QAAAA,OAAM,iBAAiB;AAEvB,eAAO;MACT;IACF,SAAS,OAAO;AACd,MAAAA,OAAM,YAAY,KAAK,IAAG;AAE1B,yCAAU;AACV,sBAAgB,UAAU,2BAA2B;IACvD;AACE,MAAAA,OAAM,iBAAiB;IACzB;AAEA,WAAO,CAAA;EACT;EAEA,aAAa,OAAqB;AAChC,oBAAgB,aAAa,KAAK;EACpC;;AAGK,IAAM,oBAAoB,kBAAkBC,WAAU;;;AC1RtD,IAAMG,eAAc;;;;;;;;EAQzB,gBAAgB,EACd,SACA,2BAA2B,MAAK,GAIjC;AACC,UAAM,iBAAiB,gBAAgB,MAAM;AAC7C,UAAM,aAAa,iBAAiB,MAAM;AAC1C,UAAM,gBAAgB,QAAQ,QAAO,iDAAgB;AAErD,QAAI,eAAe;AACjB;IACF;AAEA,UAAM,8BAA8B,kBAAkB,eACpD,gBAAgB,MAAM,WAAW;AAEnC,UAAM,uBAAuB,QAAQ,mBAAmB,gBAAgB,MAAM;AAC9E,UAAM,2BAA2B,kBAAkB,eAAe,QAAQ,cAAc;AACxF,UAAM,cAAc,oBAAoB,eAAe,gBAAgB,MAAM,WAAW;AAMxF,UAAM,sBAAsB,gBAAgB,cAAc,aAAa;AACvE,UAAM,8BAA8B,cAAc,gCAAgC,KAChF,CAAAC,OAAKA,OAAM,QAAQ,cAAc;AAQnC,QAAI,4BAA6B,uBAAuB,6BAA8B;AACpF,uBAAiB,KAAK,iBAAiB,EAAE,GAAG,YAAY,QAAO,CAAE;IACnE;;;;MAIE,+BACA,wBACA,CAAC;MACD;AACA,uBAAiB,KAAK,qBAAqB;QACzC,eAAe,QAAQ;QACvB,YAAY;QACZ,qBAAqB;QACrB;OACD;IACH,OAAO;AACL,uBAAiB,KAAK,iBAAiB,EAAE,GAAG,YAAY,QAAO,CAAE;IACnE;EACF;;;;ACjDF,IAAMC,SAAQ,MAA4B;EACxC,SAAS;EACT,SAAS;EACT,MAAM;CACP;AAGD,IAAMC,cAAa;EACjB,OAAAD;EAEA,aAAiC,KAAQ,UAAkD;AACzF,WAAO,aAAOA,QAAO,KAAK,QAAQ;EACpC;EAEA,KAAK,SAAgC,SAAwC;AAC3E,UAAM,EAAE,MAAK,IAAK,kBAAkB;AAEpC,UAAM,EAAE,cAAc,YAAW,IAAK;AAEtC,QAAI,OAAO;AACT,MAAAA,OAAM,UAAU;AAChB,MAAAA,OAAM,UAAU;AAChB,MAAAA,OAAM,OAAO;IACf;AAEA,QAAI,aAAa;AAEf,cAAQ,MAAM,OAAO,gBAAgB,aAAa,YAAW,IAAK,WAAW;IAC/E;EACF;EAEA,QAAK;AACH,IAAAA,OAAM,OAAO;AACb,IAAAA,OAAM,UAAU;AAChB,IAAAA,OAAM,UAAU;EAClB;;AAIK,IAAM,kBAAkB,kBAAkBC,WAAU;;;AChD3D,IAAMC,WAAU,eAAe,gBAAe;AAC9C,IAAMC,OAAM,IAAI,UAAU,EAAE,SAAAD,UAAS,UAAU,KAAI,CAAE;AACrD,IAAM,WAAW,CAAC,eAAe;AAUjC,IAAME,SAAQ,MAA6B;EACzC,WAAW,KAAK,IAAG;EACnB,gBAAgB,CAAA;EAChB,MAAM;IACJ,MAAM;IACN,OAAO;;CAEV;AAGM,IAAM,mBAAmB;EAC9B,OAAAA;EAEA,UAAU,UAAmD;AAC3D,WAAO,UAAIA,QAAO,MAAM,SAASA,MAAK,CAAC;EACzC;EAEA,mBAAgB;AACd,UAAM,EAAE,WAAW,SAAS,WAAU,IAAK,kBAAkB;AAE7D,WAAO;MACL;MACA,IAAI;MACJ,IAAI,cAAc;;EAEtB;EAEA,MAAM,oBAAoB,SAA8B;AACtD,QAAI;AACF,YAAM,UAAU,kBAAkB,MAAM;AACxC,UAAI,SAAS,SAAS,QAAQ,KAAK,KAAK,KAAK,OAAO,WAAW,aAAa;AAC1E;MACF;AAEA,YAAMD,KAAI,KAAK;QACb,MAAM;QACN,QAAQ,iBAAiB,iBAAgB;QACzC,MAAM;UACJ,SAAS,eAAe,QAAO;UAC/B,KAAK,OAAO,SAAS;UACrB,QAAQ,OAAO,SAAS;UACxB,WAAW,QAAQ;UACnB,OAAO,EAAE,GAAG,QAAQ,MAAM,QAAO;;OAEpC;AAED,MAAAC,OAAM,eAAe,WAAW,IAAI;IACtC,SAAS,KAAK;AACZ,YAAM,mBACJ,eAAe,SACf,IAAI,iBAAiB,YACrB,IAAI,MAAM,WAAW,cAAc,kBAAkB,aACrD,CAACA,OAAM,eAAe,WAAW;AAEnC,UAAI,kBAAkB;AACpB,wBAAgB,KACd;UACE,cAAc;UACd,aAAa,UACX,OAAM,IAAK,OAAO,SAAS,QAC7B;WAEF,OAAO;AAGT,QAAAA,OAAM,eAAe,WAAW,IAAI;MACtC;IACF;EACF;EAEA,UAAU,MAAmC;AA9F/C;AA+FI,IAAAA,OAAM,YAAY,KAAK,IAAG;AAC1B,IAAAA,OAAM,OAAO;AACb,SAAI,uBAAkB,MAAM,aAAxB,mBAAkC,WAAW;AAC/C,uBAAiB,oBAAoBA,MAAK;IAC5C;EACF;;;;ACjEF,IAAMC,UAAQ,MAAkC;EAC9C,SAAS;EACT,MAAM;EACN,mBAAmB;EACnB,aAAa;EACb,aAAa;CACd;AAGM,IAAM,wBAAwB;EACnC,OAAAA;EAEA,UAAU,UAAwD;AAChE,WAAO,UAAIA,SAAO,MAAM,SAASA,OAAK,CAAC;EACzC;EAEA,cAAc,UAAgE;AAC5E,WAAO,aAAaA,SAAO,QAAQ,QAAQ;EAC7C;EAEA,IAAI,UAA6C;AAC/C,WAAO,OAAOA,SAAO,EAAE,GAAGA,SAAO,GAAG,SAAQ,CAAE;EAChD;;;;AClBF,IAAMC,UAAQ,MAA4B;EACxC,SAAS;EACT,qBAAqB,oBAAI,IAAG;EAC5B,MAAM;EACN,OAAO;EACP,WAAW;CACZ;AAGD,IAAMC,cAAa;EACjB,OAAAD;EAEA,UAAU,UAAkD;AAC1D,WAAO,UAAIA,SAAO,MAAM,SAASA,OAAK,CAAC;EACzC;EAEA,aAAiC,KAAQ,UAAkD;AACzF,WAAO,aAAOA,SAAO,KAAK,QAAQ;EACpC;EAEA,MAAM,KAAK,SAA0C;AA3DvD;AA4DI,UAAM,cAAc,kBAAkB,MAAM,WAAW;AACvD,UAAM,YAAY,mCAAS;AAC3B,UAAM,mBAAmB,gBAAgB,MAAM;AAC/C,UAAM,uBAAuB,aAAa,cAAc;AACxD,UAAM,eAAc,qBAAgB,eAAe,mCAAS,SAAS,MAAjD,mBAAoD;AAExE,QAAI,qBAAqB,MAAM,SAAS;AAEtC,oBAAc,SAAS,EAAE,oBAAoB,OAAO,sBAAsB,MAAK,CAAE;IACnF,OAAO;AACL,YAAM,cAAc,SAAS;QAC3B,sBAAsB,CAAC;QACvB,sBAAsB,CAAC;QACvB,yBAAyB,CAAC;OAC3B;IACH;AAEA,wBAAoB,qBAAqB,mCAAS,SAAS;AAC3D,oBAAgB,WAAW,MAAM,SAAS;AAE1C,QAAI,aAAa,sBAAsB;AACrC,YAAM,qBACJ,qBAAgB,eAAe,SAAS,MAAxC,mBAA2C,gBAC3C,gBAAgB,yBAAyB,SAA2B,EAAE,CAAC;AAEzE,UAAI,kBAAkB;AACpB,QAAAE,aAAY,gBAAgB,EAAE,SAAS,kBAAkB,0BAA0B,KAAI,CAAE;MAC3F;IACF,OAAO;AACL,YAAM,gBAAgB,gBAAgB,MAAM;AAE5C,UAAI,kBAAkB,MAAM,mBAAoB,iBAAiB,CAAC,aAAc;AAC9E,YAAI,eAAe,SAAQ,GAAI;AAC7B,2BAAiB,MAAM,YAAY;QACrC,OAAO;AACL,2BAAiB,MAAM,8BAA8B;QACvD;MACF,WAAW,mCAAS,MAAM;AACxB,yBAAiB,MAAM,QAAQ,MAAM,QAAQ,IAAI;MACnD,WAAW,aAAa;AACtB,yBAAiB,MAAM,SAAS;MAClC,OAAO;AACL,yBAAiB,MAAM,SAAS;MAClC;IACF;AAEA,IAAAF,QAAM,OAAO;AACb,0BAAsB,IAAI,EAAE,MAAM,KAAI,CAAE;AACxC,qBAAiB,UAAU;MACzB,MAAM;MACN,OAAO;MACP,YAAY,EAAE,WAAW,QAAQ,WAAW,EAAC;KAC9C;EACH;EAEA,QAAK;AACH,UAAM,oBAAoB,kBAAkB,MAAM;AAClD,UAAM,cAAc,QAAQ,gBAAgB,MAAM,iBAAiB;AAGnE,QAAIA,QAAM,MAAM;AACd,uBAAiB,UAAU;QACzB,MAAM;QACN,OAAO;QACP,YAAY,EAAE,WAAW,YAAW;OACrC;IACH;AAEA,IAAAA,QAAM,OAAO;AACb,qBAAiB,MAAM,SAAS;AAChC,oBAAgB,aAAY;AAE5B,QAAI,mBAAmB;AACrB,UAAI,aAAa;AACf,yBAAiB,QAAQ,SAAS;MACpC,OAAO;AACL,yBAAiB,KAAK,SAAS;MACjC;IACF,OAAO;AACL,4BAAsB,IAAI,EAAE,MAAM,MAAK,CAAE;IAC3C;AAEA,yBAAqB,SAAQ;EAC/B;EAEA,WAAW,SAA0C,WAA0B;AAC7E,QAAI,WAAW;AACb,MAAAA,QAAM,oBAAoB,IAAI,WAAW,OAAO;IAClD;AACA,IAAAA,QAAM,UAAU;AAChB,0BAAsB,IAAI,EAAE,QAAO,CAAE;EACvC;EAEA,eAAY;AACV,IAAAA,QAAM,oBAAoB,MAAK;AAC/B,IAAAA,QAAM,UAAU;EAClB;EAEA,QAAK;AACH,QAAIA,QAAM,OAAO;AACf;IACF;AACA,IAAAA,QAAM,QAAQ;AACd,eAAW,MAAK;AACd,MAAAA,QAAM,QAAQ;IAChB,GAAG,GAAG;EACR;;AAIK,IAAM,kBAAkB,kBAAkBC,WAAU;;;AChE3D,IAAME,UAAQ,MAA6B;EACzC,MAAM;EACN,SAAS,CAAC,SAAS;EACnB,kBAAkB,CAAA;CACnB;AAKD,IAAMC,cAAa;EACjB,OAAAD;EAEA,aAAiC,KAAQ,UAAmD;AAC1F,WAAO,aAAOA,SAAO,KAAK,QAAQ;EACpC;EAEA,qBAAqB,QAAyB;AAC5C,IAAAA,QAAM,iBAAiB,KAAK,MAAM;EACpC;EAEA,oBAAoB,QAAsC;AACxD,UAAM,SAASA,QAAM,iBAAiB,IAAG;AACzC,QAAI,CAAC,QAAQ;AACX;IACF;AACA,UAAM,EAAE,WAAW,SAAS,SAAQ,IAAK;AAEzC,YAAQ,QAAQ;MACd,KAAK;AACH;AACA;MACF,KAAK;AACH;AACA,yBAAiB,OAAM;AACvB;MACF,KAAK;AACH;AACA,yBAAiB,OAAM;AACvB;MACF;IACF;EACF;EAEA,KAAK,MAAqC,MAAoC;AAC5E,QAAI,SAASA,QAAM,MAAM;AACvB,MAAAA,QAAM,OAAO;AACb,MAAAA,QAAM,QAAQ,KAAK,IAAI;AACvB,MAAAA,QAAM,OAAO;IACf;EACF;EAEA,MAAM,MAAqC,MAAoC;AAC7E,IAAAA,QAAM,OAAO;AACb,IAAAA,QAAM,UAAU,CAAC,IAAI;AACrB,IAAAA,QAAM,OAAO;EACf;EAEA,QAAQ,MAAqC,MAAoC;AAC/E,UAAM,WAAWA,QAAM,QAAQ,GAAG,EAAE;AACpC,UAAM,aAAa,aAAa;AAEhC,QAAI,CAAC,YAAY;AACf,MAAAA,QAAM,OAAO;AACb,MAAAA,QAAM,QAAQA,QAAM,QAAQ,SAAS,CAAC,IAAI;AAC1C,MAAAA,QAAM,OAAO;IACf;EACF;EAEA,SAAM;AA9KR;AA+KI,UAAM,cAAc,gBAAgB,MAAM;AAC1C,UAAM,kBAAkB,iBAAiB,MAAM,SAAS;AAExD,UAAM,eAAe,CAAC,eAAe;AAErC,QAAIA,QAAM,QAAQ,SAAS,GAAG;AAC5B,MAAAA,QAAM,QAAQ,IAAG;AACjB,YAAM,CAAC,IAAI,IAAIA,QAAM,QAAQ,MAAM,EAAE;AACrC,UAAI,MAAM;AACR,cAAM,gBAAgB,SAAS;AAC/B,YAAI,eAAe,eAAe;AAChC,UAAAA,QAAM,OAAO;QACf,OAAO;AACL,UAAAA,QAAM,OAAO;QACf;MACF;IACF,OAAO;AACL,sBAAgB,MAAK;IACvB;AAEA,SAAI,KAAAA,QAAM,SAAN,mBAAY,QAAQ;AACtB,MAAAA,QAAM,KAAK,SAAS;IACtB;AAGA,eAAW,MAAK;AAxMpB,UAAAE,KAAA,IAAAC;AAyMM,UAAI,cAAc;AAChB,0BAAkB,gBAAgB,QAAW,gBAAgB,MAAM,WAAW;AAC9E,cAAM,gBAAgB,oBAAoB,iBAAgB;AAC1D,SAAAD,MAAA,+CAAe,aAAf,gBAAAA,IAAyB;AAEzB,cAAM,eAAe,SAAS,kBAAkB,KAAK;AACrD,SAAAC,OAAA,oDAAe,aAAf,mBAAyB,iBAAzB,gBAAAA,IAAA,SAAwC;UACtC,UAAU,aAAa;UACvB,YAAY,aAAa;UACzB,WAAW,aAAa;UACxB,SAAS,aAAa;;MAE1B;IACF,GAAG,GAAG;EACR;EAEA,cAAc,cAAoB;AAChC,QAAIH,QAAM,QAAQ,SAAS,GAAG;AAC5B,MAAAA,QAAM,UAAUA,QAAM,QAAQ,MAAM,GAAG,eAAe,CAAC;AACvD,YAAM,CAAC,IAAI,IAAIA,QAAM,QAAQ,MAAM,EAAE;AACrC,UAAI,MAAM;AACR,QAAAA,QAAM,OAAO;MACf;IACF;EACF;EAEA,qBAAkB;AAChB,QAAI,iBAAiB,MAAM,QAAQ,SAAS,GAAG;AAC7C,uBAAiB,OAAM;IACzB,OAAO;AACL,sBAAgB,MAAK;IACvB;EACF;;AAIK,IAAM,mBAAmB,kBAAkBC,WAAU;;;AC5N5D,IAAMG,UAAQ,MAA4B;EACxC,WAAW;EACX,gBAAgB,CAAA;EAChB,mBAAmB;CACpB;AAGD,IAAMC,cAAa;EACjB,OAAAD;EAEA,UAAU,UAAkD;AAC1D,WAAO,UAAIA,SAAO,MAAM,SAASA,OAAK,CAAC;EACzC;EAEA,aAAa,WAA4C;AACvD,IAAAA,QAAM,YAAY;AAElB,QAAI;AACF,YAAM,gBAAgB,oBAAoB,iBAAgB;AAE1D,UAAI,eAAe;AACjB,cAAM,iBAAiBC,YAAW,YAAW,EAAG;AAEhD,sBAAc,SAAS,UAAU;UAC/B;UACA;UACA,mBAAmB,qBAAqB,gBAAgB,SAAS;SAClE;MACH;IACF,QAAQ;AAEN,cAAQ,KAAK,wCAAwC;IACvD;EACF;EAEA,kBAAkB,gBAAsD;AACtE,IAAAD,QAAM,iBAAiB,EAAE,GAAGA,QAAM,gBAAgB,GAAG,eAAc;AAEnE,QAAI;AACF,YAAM,gBAAgB,oBAAoB,iBAAgB;AAE1D,UAAI,eAAe;AACjB,cAAM,yBAAyBC,YAAW,YAAW,EAAG;AAExD,sBAAc,SAAS,UAAU;UAC/B,gBAAgB;UAChB,mBAAmB,qBAAqBD,QAAM,gBAAgBA,QAAM,SAAS;SAC9E;MACH;IACF,QAAQ;AAEN,cAAQ,KAAK,wCAAwC;IACvD;EACF;EAEA,cAAW;AACT,WAAO,SAASA,OAAK;EACvB;;AAIK,IAAM,kBAAkB,kBAAkBC,WAAU;;;AChD3D,IAAM,0BAA0B;EAC9B,QAAQ;EACR,QAAQ;EACR,UAAU;EACV,QAAQ;EACR,QAAQ;;AAIV,IAAMC,UAAQ,MAAgC;EAC5C,eAAe,CAAA;EACf,YAAY,CAAA;EACZ,iBAAiB;EACjB,mBAAmB;EACnB,oBAAoB,EAAE,GAAG,wBAAuB;EAChD,sBAAsB;IACpB,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,QAAQ;;CAEX;AAGD,IAAMC,cAAa;EACjB,OAAAD;EAEA,UAAU,UAAmD;AAC3D,WAAO,UAAIA,SAAO,MAAK;AACrB,eAASA,OAAK;IAChB,CAAC;EACH;EAEA,aAAiC,KAAQ,UAAsD;AAC7F,WAAO,aAAOA,SAAO,KAAK,QAAQ;EACpC;EAEA,WAAW,YAA4B;AACrC,eAAW,QAAQ,eAAY;AAC7B,YAAM,cAAc,YAAY,wBAAwB,SAAS;AACjE,UAAI,aAAa;AACf,4BAAoB,eAAe,aAAa,SAAS;MAC3D;IACF,CAAC;EACH;EAEA,mBAAmB,WAAsD;AACvE,QAAI,WAAW;AACb,MAAAA,QAAM,kBAAkB,IAAI,SAAS;IACvC;EACF;EAEA,cAAc,YAAkD;AAC9D,UAAM,gBAAgB,WAAW,OAC/B,kBACE,CAACA,QAAM,cAAc,KACnB,uBACE,kBAAkB,OAAO,aAAa,MACtC,oBAAoB,iBAAiB,kBAAkB,IAAI,MACzD,oBAAoB,iBAAiB,aAAa,IAAI,KACxD,kBAAkB,UAAU,aAAa,KAAK,CACjD;AAQL,kBAAc,QAAQ,eAAY;AAChC,UAAI,UAAU,SAAS,eAAe;AACpC,QAAAA,QAAM,cAAc,KAAK,IAAI,SAAS,CAAC;MACzC;IACF,CAAC;AAED,UAAM,oBAAoB,oBAAoB,qBAAoB;AAClE,UAAM,iCACJ,oBAAoB,qBAAqB,iBAAiB;AAE5D,IAAAA,QAAM,aAAa,oBAAoB,0BAA0B,8BAA8B;EACjG;EAEA,mBAAmB,mBAAmC;AACpD,WAAO,KAAKA,QAAM,oBAAoB,EAAE,QAAQ,eAAY;AAC1D,MAAAA,QAAM,qBAAqB,SAA2B,IAAI;IAC5D,CAAC;AAED,sBAAkB,QAAQ,eAAY;AACpC,MAAAA,QAAM,qBAAqB,SAAS,IAAI;IAC1C,CAAC;AAED,wBAAoB,qCAAoC;EAC1D;EAEA,kBAAkB,WAA2B,SAAgB;AAC3D,IAAAA,QAAM,qBAAqB,SAAS,IAAI;AAExC,wBAAoB,qCAAoC;EAC1D;EAEA,uCAAoC;AAClC,UAAM,oBAAoB,oBAAoB,qBAAoB;AAClE,UAAM,oBAAoB,oBAAoB,qBAAqB,iBAAiB;AACpF,UAAM,0BAA0B,oBAAoB,wBAAuB;AAE3E,IAAAA,QAAM,aAAa,oBAAoB,0BAA0B,iBAAiB;AAElF,QAAI,yBAAyB;AAC3B,oBAAc,wBAAuB;IACvC,OAAO;AACL,oBAAc,mBAAmB,iBAAiB;IACpD;EACF;EAEA,uBAAoB;AAClB,WAAO,OAAO,QAAQA,QAAM,oBAAoB,EAC7C,OAAO,CAAC,CAAC,GAAG,OAAO,MAAM,OAAO,EAChC,IAAI,CAAC,CAAC,SAAS,MAAM,SAA2B;EACrD;EAEA,qBAAqB,mBAAmC;AACtD,WAAOA,QAAM,cAAc,OAAO,eAChC,kBAAkB,SAAS,UAAU,KAAuB,CAAC;EAEjE;EAEA,0BAAuB;AACrB,WAAO,OAAO,OAAOA,QAAM,oBAAoB,EAAE,MAAM,aAAW,OAAO;EAC3E;EAEA,0BAA0B,YAAuB;AAC/C,UAAM,sBAAsB,oBAAoB,2BAA2B,UAAU;AACrF,UAAM,mBAA6C,CAAA;AAEnD,wBAAoB,QAAQ,mBAAgB;AAC1C,YAAM,YAAY,cAAc,CAAC;AACjC,YAAM,mBAAkB,uCAAW,QAAO,cAAc,aAAa;AAErE,UAAI,cAAc,SAAS,KAAK,WAAW;AACzC,yBAAiB,KAAK;UACpB,MAAM,UAAU;UAChB,UAAU,UAAU;UACpB,SAAS,UAAU;UACnB,YAAY,CAAC,GAAG,aAAa;UAC7B,MAAM,kBAAkB,SAAS;;UAEjC,OAAO;UACP,KAAI,uCAAW,OAAM;SACtB;MACH,WAAW,WAAW;AACpB,yBAAiB,KAAK,SAAS;MACjC;IACF,CAAC;AAED,WAAO;EACT;EAEA,2BAA2B,YAAuB;AAChD,UAAM,sBAAsB,oBAAI,IAAG;AAEnC,eAAW,QAAQ,eAAY;AAC7B,YAAM,EAAE,KAAI,IAAK;AACjB,YAAM,gBAAgB,oBAAoB,iBAAiB,IAAI;AAE/D,UAAI,CAAC,eAAe;AAClB;MACF;AAEA,YAAM,mBAAmB,oBAAoB,IAAI,aAAa,KAAK,CAAA;AACnE,YAAM,oBAAoB,iBAAiB,KAAK,CAAAE,OAAKA,GAAE,UAAU,UAAU,KAAK;AAChF,UAAI,CAAC,mBAAmB;AACtB,yBAAiB,KAAK,SAAS;MACjC;AACA,0BAAoB,IAAI,eAAe,gBAAgB;IACzD,CAAC;AAED,WAAO;EACT;EAEA,iBAAiB,MAAwB;AACvC,QAAI,CAAC,MAAM;AACT,aAAO;IACT;AAEA,UAAM,kBAAkB;MACtB,gBAAgB;;AAGlB,WAAQ,gBAA2C,IAAI,KAAK;EAC9D;EAEA,0BAA0B,YAAuB;AAC/C,UAAM,mBAAgC,CAAA;AAEtC,eAAW,QAAQ,CAAAA,OAAI;AACrB,UAAI,CAAC,iBAAiB,KAAK,CAAAC,QAAMA,IAAG,UAAUD,GAAE,KAAK,GAAG;AACtD,yBAAiB,KAAKA,EAAC;MACzB;IACF,CAAC;AAED,WAAO;EACT;EAEA,aAAa,WAAoC;AA1OnD,gBAAAE;AA2OI,QAAI,UAAU,OAAO,cAAc,aAAa,MAAM;AACpD,YAAM,gBAAgB;AAEtB,YAAM,eAAe,SAAS,kBAAkB,KAAK;AACrD,YAAM,YAAY,gBAAgB,YAAW,EAAG;AAChD,YAAM,iBAAiB,gBAAgB,YAAW,EAAG;AAErD,iEAAe,aAAf,mBAAyB,iBAAzB,4BAAwC;QACtC,UAAU,aAAa;QACvB,YAAY,aAAa;QACzB,WAAW,aAAa;QACxB,SAAS,aAAa;;AAExB,OAAAA,MAAA,+CAAe,aAAf,gBAAAA,IAAyB,UAAU;QACjC;QACA;QACA,mBAAmB,qBAAqB,gBAAgB,SAAS;;AAEnE,0BAAoB,cAAc,CAAC,SAAS,CAAC;IAC/C,OAAO;AACL,0BAAoB,cAAc,CAAC,SAAS,CAAC;IAC/C;EACF;EAEA,iBAAiB,gBAA+B;AAnQlD;AAoQI,UAAM,kBAAkB,kBAAkB,gBAAgB,MAAM;AAChE,UAAM,gBAAgBJ,QAAM,WAAW,KAAK,CAAAE,OAAKA,GAAE,OAAO,cAAc,aAAa,IAAI;AAEzF,QAAI,CAAC,eAAe;AAClB,aAAO;IACT;AAEA,SAAI,oDAAe,eAAf,mBAA2B,QAAQ;AACrC,YAAM,YAAY,cAAc,WAAW,KAAK,CAAAA,OAAKA,GAAE,UAAU,eAAe;AAEhF,aAAO;IACT;AAEA,WAAO;EACT;EAEA,4BAAyB;AACvB,WAAOF,QAAM,WAAW,OAAO,CAAAE,OAAKA,GAAE,SAAS,WAAW,EAAE,IAAI,CAAAA,OAAE;AArRtE;AAqRyE,mBAAAA,GAAE,SAAF,mBAAQ;KAAI;EACnF;EAEA,iBAAiB,IAAU;AACzB,WAAOF,QAAM,cAAc,KAAK,CAAAE,OAAKA,GAAE,OAAO,EAAE;EAClD;EAEA,aAAa,IAAY,MAAoB;AAC3C,UAAM,wBAAwBF,QAAM,cAAc,OAChD,CAAAE,OAAKA,GAAE,UAAU,gBAAgB,MAAM,WAAW;AAGpD,WAAO,sBAAsB,KAAK,CAAAA,OAAE;AAjSxC;AAiS2C,aAAAA,GAAE,eAAe,QAAM,KAAAA,GAAE,SAAF,mBAAQ,UAAS;KAAI;EACrF;EAEA,oBAAoB,WAAoC;AApS1D;AAqSI,QAAI,UAAU,OAAO,WAAW;AAC9B;IACF;AAEA,UAAM,gBAAgB;AAEtB,UAAM,eAAe,SAAS,kBAAkB,KAAK;AACrD,UAAM,YAAY,gBAAgB,YAAW,EAAG;AAChD,UAAM,iBAAiB,gBAAgB,YAAW,EAAG;AAErD,+DAAe,aAAf,mBAAyB,iBAAzB,4BAAwC;MACtC,UAAU,aAAa;MACvB,YAAY,aAAa;MACzB,SAAS,aAAa;MACtB,WAAW,aAAa;;AAE1B,kBAAc,SAAS,UAAU;MAC/B;MACA;MACA,mBAAmB,qBAAqB,gBAAgB,SAAS;KAClE;EACH;;;;;;EAOA,yBAAyB,WAAyB;AAChD,UAAM,sBAAsBF,QAAM,cAAc,OAC9C,eAAa,UAAU,UAAU,SAAS;AAG5C,WAAO,oBAAoB,0BAA0B,mBAAmB;EAC1E;EAEA,sBAAsB,QAAgB;AACpC,UAAM,YAAY,oBAAoB,aAAa,OAAO,IAAI,OAAO,IAAI;AACzE,UAAM,YAAY,gBAAgB,MAAM;AACxC,qBAAiB,8BAA6B,uCAAW,eAAc,OAAO,IAAI,SAAS;AAE3F,QAAI,WAAW;AACb,uBAAiB,KAAK,sBAAsB,EAAE,UAAS,CAAE;IAC3D,OAAO;AACL,uBAAiB,KAAK,2BAA2B,EAAE,OAAM,CAAE;IAC7D;EACF;;;;;;EAOA,cAAc,WAA0B;AACtC,QAAI,WAAW;AACb,aAAO,oBAAoB,yBAAyB,SAAS;IAC/D;AAEA,WAAO,oBAAoB,0BAA0BA,QAAM,aAAa;EAC1E;;;;;EAMA,qBAAqB,WAAqC;AACxD,IAAAA,QAAM,oBAAoB;AAC1B,IAAAA,QAAM,aAAa,oBAAoB,cAAc,SAAS;AAC9D,kBAAc,qBAAqB,SAAS;EAC9C;EAEA,eAAe,aAAqB,WAAyB;AAC3D,QAAI,aAAa;AACf,MAAAA,QAAM,qBAAqB;QACzB,GAAGA,QAAM;QACT,CAAC,SAAS,GAAG;;AAEf,kBAAY,wBAAwB,WAAW,WAAW;IAC5D;EACF;EAEA,kBAAkB,WAAyB;AACzC,IAAAA,QAAM,qBAAqB;MACzB,GAAGA,QAAM;MACT,CAAC,SAAS,GAAG;;AAEf,gBAAY,2BAA2B,SAAS;EAClD;EAEA,eAAe,WAAqC;AAClD,QAAI,CAAC,WAAW;AACd,aAAO;IACT;AAEA,WAAOA,QAAM,mBAAmB,SAAS;EAC3C;EAEA,YAAY,WAA0B;AACpC,QAAI,CAAC,WAAW;AACd,aAAO,OAAO,OAAOA,QAAM,kBAAkB,EAAE,KAAK,QAAM,QAAQ,EAAE,CAAC;IACvE;AAEA,WAAO,QAAQA,QAAM,mBAAmB,SAAS,CAAC;EACpD;EAEA,oBAAiB;AACf,IAAAA,QAAM,qBAAqB,EAAE,GAAG,wBAAuB;EACzD;;AAIK,IAAM,sBAAsB,kBAAkBC,WAAU;;;ACnZ/D,IAAM,kBAAkB;AAEjB,IAAM,mBACV,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,cACtD,QAAQ,IAAI,iCAAiC,IAC7C,WAAc;AAEb,IAAM,qBACV,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,cACtD,QAAQ,IAAI,+BAA+B,IAC3C,WAAc;AAEb,IAAM,2BACV,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,cACtD,QAAQ,IAAI,qCAAqC,IACjD,WAAc;AAmGb,IAAM,uBAAuB;EAClC,kBAAkB;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;;EAEF,sBAAsB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;EAEF,cAAc;EACd,gCAAgC;EAChC,mCAAmC;EACnC,eAAe;IACb,KAAK;IACL,eAAe;;;;;ACrJnB,IAAMI,UAAQ,MAAmC;EAC/C,cAAc,CAAA;EACd,sBAAsB,CAAA;EACtB,oBAAoB,CAAA;EACpB,mBAAmB;EACnB,SAAS;EACT,OAAO;EACP,MAAM;CACP;AAGD,IAAMC,cAAa;EACjB,OAAAD;EAEA,UAAU,UAAyD;AACjE,WAAO,UAAIA,SAAO,MAAM,SAASA,OAAK,CAAC;EACzC;EAEA,qBAAqB,mBAAmE;AACtF,IAAAA,QAAM,oBAAoB;EAC5B;EAEA,MAAM,kBAAkB,gBAAyB,QAAmB;AAnDtE;AAoDI,QAAI,CAAC,gBAAgB;AACnB,YAAM,IAAI,MAAM,yDAAyD;IAC3E;AAEA,IAAAA,QAAM,UAAU;AAEhB,QAAI;AACF,YAAM,WAAW,MAAM,wBAAwB,kBAAkB;QAC/D,SAAS;QACT,QAAQA,QAAM;QACd;;QAEA,OAAO,WAAW,aAAa,aAAa;QAC5C,UAAS,qBAAgB,MAAM,sBAAtB,mBAAyC;OACnD;AAED,YAAM,sBAAsB,uBAAuB,uBAAuB,SAAS,IAAI;AACvF,YAAM,wBACJ,uBAAuB,uBAAuB,mBAAmB;AACnE,YAAM,uBAAuB,CAAC,GAAGA,QAAM,cAAc,GAAG,qBAAqB;AAE7E,MAAAA,QAAM,UAAU;AAEhB,UAAI,WAAW,YAAY;AACzB,QAAAA,QAAM,uBAAuB,uBAAuB,gCAClDA,QAAM,sBACN,SAAS,IAAI;MAEjB,OAAO;AACL,QAAAA,QAAM,eAAe;AACrB,QAAAA,QAAM,qBAAqB,uBAAuB,gCAChDA,QAAM,oBACN,qBAAqB;MAEzB;AAEA,MAAAA,QAAM,QAAQ,qBAAqB,WAAW;AAC9C,MAAAA,QAAM,OAAO,SAAS,OAAO,SAAS,OAAO;IAC/C,SAAS,OAAO;AACd,YAAM,uBAAuB,gBAAgB,MAAM;AACnD,uBAAiB,UAAU;QACzB,MAAM;QACN,OAAO;QACP,YAAY;UACV,SAAS;UACT,WAAW,kBAAkB,MAAM;UACnC,QAAQA,QAAM;UACd,kBACE,uBAAkB,MAAM,0BAAxB,mBAAgD,2BAChD,qBAAqB,cAAc;;OAExC;AACD,sBAAgB,UAAU,8BAA8B;AACxD,MAAAA,QAAM,UAAU;AAChB,MAAAA,QAAM,QAAQ;AACd,MAAAA,QAAM,OAAO;IACf;EACF;EAEA,gCACE,kBAAwC,CAAA,GACxC,eAA8B,CAAA,GAAE;AAEhC,UAAM,UAAU;AAChB,iBAAa,QAAQ,iBAAc;AACjC,YAAM,OAAO,IAAI,KAAK,YAAY,SAAS,OAAO,EAAE,YAAW;AAC/D,YAAM,QAAQ,IAAI,KAAK,YAAY,SAAS,OAAO,EAAE,SAAQ;AAE7D,YAAM,mBAAmB,QAAQ,IAAI,KAAK,CAAA;AAC1C,YAAM,oBAAoB,iBAAiB,KAAK,KAAK,CAAA;AAGrD,YAAM,uBAAuB,kBAAkB,OAAO,QAAM,GAAG,OAAO,YAAY,EAAE;AAEpF,cAAQ,IAAI,IAAI;QACd,GAAG;QACH,CAAC,KAAK,GAAG,CAAC,GAAG,sBAAsB,WAAW,EAAE,KAC9C,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,SAAS,OAAO,EAAE,QAAO,IAAK,IAAI,KAAK,EAAE,SAAS,OAAO,EAAE,QAAO,CAAE;;IAG/F,CAAC;AAED,WAAO;EACT;EAEA,uBAAuB,cAA2B;AAChD,WAAO,aAAa,OAAO,iBAAc;AACvC,YAAM,YAAY,YAAY,UAAU,MACtC,cAAS;AA5IjB;AA4IoB,+BAAS,aAAT,mBAAmB,MAAM,aAAY;OAAI;AAGvD,aAAO,CAAC;IACV,CAAC;EACH;EAEA,uBAAuB,cAA2B;AAnJpD;AAoJI,UAAM,WAAU,qBAAgB,MAAM,sBAAtB,mBAAyC;AACzD,UAAM,uBAAuB,aAAa,OACxC,iBAAe,YAAY,SAAS,UAAU,OAAO;AAGvD,WAAO;EACT;EAEA,cAAW;AACT,IAAAA,QAAM,OAAO;EACf;EAEA,oBAAiB;AACf,IAAAA,QAAM,eAAe,CAAA;AACrB,IAAAA,QAAM,qBAAqB,CAAA;AAC3B,IAAAA,QAAM,oBAAoB;AAC1B,IAAAA,QAAM,UAAU;AAChB,IAAAA,QAAM,QAAQ;AACd,IAAAA,QAAM,OAAO;EACf;;AAIK,IAAM,yBAAyB,kBAAkBC,aAAY,WAAW;;;AC3E/E,IAAMC,UAAQ,MAAiC;EAC7C,aAAa,oBAAI,IAAG;EACpB,SAAS;EACT,WAAW;EACX,QAAQ;CACT;AAGD,IAAI;AAGJ,IAAMC,eAAa;EACjB,OAAAD;EAEA,aACE,KACA,UAAuD;AAEvD,WAAO,aAAOA,SAAO,KAAK,QAAQ;EACpC;EAEA,aAAU;AACR,WAAOA,QAAM;EACf;EAEA,UAAU,QAAkC;AAC1C,IAAAA,QAAM,UAAU,IAAI,MAAM;EAC5B;EAEA,MAAM,uBAAoB;AA7H5B,gBAAAE,KAAA;AA8HI,QAAI,eAAe,WAAU,KAAO,eAAe,SAAQ,KAAM,eAAe,MAAK,GAAK;AACxF,UAAI,qBAAqB;AACvB,cAAM;AACN,8BAAsB;AAEtB;MACF;AAEA,UAAI,CAAC,eAAe,iBAAiBF,WAAA,gBAAAA,QAAO,eAAe,GAAG;AAC5D,cAAM,OAAOA,QAAM;AACnB,QAAAA,QAAM,QAAQ;AAEd;MACF;AACA,6BAAsB,gCAAqB,WAAU,MAA/B,mBAClB,yBADkB,4BAEnB,MAAM,MAAM;AACf,2BAAqB,MAAM,SAAS;AACpC,YAAM;AACN,4BAAsB;AACtB,MAAAA,QAAM,kBAAkB;AACxB,2BAAqB,MAAM,SAAS;IACtC,OAAO;AACL,cAAM,MAAAE,MAAA,qBAAqB,WAAU,MAA/B,gBAAAA,IAAmC,yBAAnC,wBAAAA;IACR;EACF;EAEA,MAAM,gBAAgB,SAAiC,OAAuB,WAAW,MAAI;AAzJ/F;AA0JI,YAAM,gCAAqB,WAAU,MAA/B,mBAAmC,oBAAnC,4BAAqD;AAE3D,QAAI,UAAU;AACZ,sBAAgB,mBAAmB,KAAK;IAC1C;EACF;EAEA,MAAM,kBAAkB,SAA+B;AAjKzD;AAkKI,YAAM,gCAAqB,WAAU,MAA/B,mBAAmC,sBAAnC,4BAAuD;AAC7D,UAAM,YAAY,QAAQ,SAAS,gBAAgB,MAAM;AACzD,QAAI,WAAW;AACb,0BAAoB,eAAe,QAAQ,IAAI,SAAS;IAC1D;EACF;EAEA,MAAM,wBAAwB,aAAwC,WAAyB;AAzKjG;AA0KI,oBAAgB,WAAW,MAAM,gBAAgB,MAAM,WAAW;AAClE,UAAM,gBAAgB,oBAAoB,iBAAgB;AAC1D,QAAI,CAAC,eAAe;AAClB;IACF;AACA,sBAAkB,wBAAwB,aAAa,SAAS;AAChE,UAAM,cAAc,SAAS,oBAAoB,WAAW;AAC5D,gBAAY,yBACV,kBAAkB,MAAM,yBAAyB,EAAE,CAAC,SAAS,GAAG,YAAW,CAAE;AAE/E,UAAM,qBAAqB,kBAAkB,aAAa;AAC1D,oBAAgB,WAAW,OAAO,gBAAgB,MAAM,WAAW;AACnE,qBAAiB,UAAU;MACzB,MAAM;MACN,OAAO;MACP,YAAY;QACV;QACA,WAAS,qBAAgB,MAAM,sBAAtB,mBAAyC,kBAAiB;;KAEtE;EACH;EAEA,MAAM,YAAY,SAAe;AAhMnC;AAiMI,YAAO,0BAAqB,WAAU,MAA/B,mBAAmC,YAAY;EACxD;EAEA,WAAW,OAAe,UAAgB;AApM5C;AAqMI,YAAO,0BAAqB,WAAU,MAA/B,mBAAmC,WAAW,OAAO;EAC9D;EAEA,YAAY,OAAe,UAAgB;AAxM7C;AAyMI,YAAO,0BAAqB,WAAU,MAA/B,mBAAmC,YAAY,OAAO;EAC/D;EAEA,MAAM,gBAAgB,MAAyB;AA5MjD;AA6MI,YAAO,0BAAqB,WAAU,MAA/B,mBAAmC,gBAAgB;EAC5D;EAEA,MAAM,gBAAgB,QAAc;AAhNtC;AAiNI,YAAO,0BAAqB,WAAU,MAA/B,mBAAmC,gBAAgB;EAC5D;EAEA,MAAM,iBAAiB,QAAmC;AApN5D;AAqNI,YAAO,0BAAqB,WAAU,MAA/B,mBAAmC,iBAAiB;EAC7D;EAEA,MAAM,gBAAgB,QAA6B;AAxNrD;AAyNI,aAAO,0BAAqB,WAAU,MAA/B,mBAAmC,gBAAgB,YAAW,CAAA;EACvE;EAEA,MAAM,YAAY,MAAgC;AA5NpD;AA6NI,YAAO,0BAAqB,WAAU,MAA/B,mBAAmC,YAAY;EACxD;EAEA,MAAM,cAAc,MAAuB;AAhO7C;AAiOI,YAAO,0BAAqB,WAAU,MAA/B,mBAAmC,cAAc;EAC1D;EAEA,MAAM,cAAc,OAAa;AApOnC;AAqOI,YAAO,0BAAqB,WAAU,MAA/B,mBAAmC,cAAc;EAC1D;EAEA,MAAM,aAAa,OAAa;AAxOlC;AAyOI,YAAO,0BAAqB,WAAU,MAA/B,mBAAmC,aAAa;EACzD;EAEA,eAAe,KAAc;AA5O/B;AA6OI,aAAO,gCAAqB,WAAU,MAA/B,mBAAmC,mBAAnC,4BAAoD,SAAQ;EACrE;EAEA,oBAAiB;AACf,IAAAF,QAAM,QAAQ;AACd,IAAAA,QAAM,kBAAkB;AACxB,IAAAA,QAAM,YAAY;AAClB,IAAAA,QAAM,eAAe;AACrB,IAAAA,QAAM,SAAS;AACf,2BAAuB,kBAAiB;AACxC,gBAAY,4BAA2B;EACzC;EAEA,WAAQ;AACN,IAAAA,QAAM,QAAQ;AACd,IAAAA,QAAM,kBAAkB;AACxB,0BAAsB;EACxB;EAEA,uBAAoB;AAhQtB;AAiQI,UAAM,EAAE,WAAW,aAAY,IAAK,qBAAqB;AAEzD,QAAI,WAAW;AACb,kBAAY,yBAAyB,SAAS;IAChD;AAEA,QAAI,cAAc;AAChB,kBAAY,gBAAgB,YAAY;IAC1C;AAEA,qBAAiB,UAAU;MACzB,MAAM;MACN,OAAO;MACP,YAAY;QACV,QAAQ,YAAY,WAAW;QAC/B,QAAM,4BAAiB,MAAM,SAAvB,mBAA6B,WAA7B,mBAAqC,SAAQ;;KAEtD;EACH;EAEA,WAAW,SAA6C;AACtD,IAAAA,QAAM,UAAU;EAClB;EAEA,OAAO,KAAW;AAChB,IAAAA,QAAM,QAAQ;AACd,IAAAA,QAAM,kBAAkB,eAAe,iBAAgB;EACzD;EAEA,aAAa,WAAiD;AAC5D,IAAAA,QAAM,YAAY;EACpB;EAEA,WAAW,SAA6C;AACtD,IAAAA,QAAM,UAAU;AAChB,IAAAA,QAAM,YAAY;EACpB;EAEA,gBAAgB,QAAiD;AAC/D,IAAAA,QAAM,eAAe;EACvB;EAEA,aAAa,WAAiD;AAC5D,IAAAA,QAAM,YAAY;EACpB;EAEA,UAAU,QAA2C;AACnD,IAAAA,QAAM,SAAS;EACjB;EAEA,MAAM,WAAW,WAA0B;AAnT7C;AAoTI,QAAI;AACF,cAAM,0BAAqB,WAAU,MAA/B,mBAAmC,WAAW;IACtD,SAAS,OAAO;AACd,YAAM,IAAI,YAAY,wBAAwB,sBAAsB,KAAK;IAC3E;EACF;EAEA,eAAe,aAA2B,gBAA8B;AACtE,IAAAA,QAAM,YAAY,IAAI,gBAAgB,WAAW;EACnD;EAEA,cAAc,EAAE,YAAY,SAAS,UAAS,GAAuB;AACnE,UAAM,uBAAuB,oBAAoB,MAAM,mBAAmB,SAAS;AACnF,UAAM,uBAAuB,yBAAyB,WAAW;AAEjE,QAAI,sBAAsB;AACxB,YAAM,iBAAiB,gBAAgB,MAAM;AAE7C,UAAI,gBAAgB;AAClB,cAAM,cAAc,GAAG,SAAS,IAAI,eAAe,EAAE,IAAI,OAAO;AAChE,0BAAkB,eAAe,aAA4B,SAAS;MACxE,OAAO;AACL,gBAAQ,KAAK,2CAA2C,SAAS,GAAG;MACtE;IACF,OAAO;AACL,YAAM,YAAY,oBAAoB,aAAa,WAAW,WAAW;AAEzE,UAAI,WAAW;AACb,6BAAqB,gBAAgB,WAAW,SAAS;MAC3D,OAAO;AACL,gBAAQ,KAAK,qCAAqC,SAAS,GAAG;MAChE;IACF;EACF;;AAIK,IAAM,uBAAuB,kBAAkBC,YAAU;;;AC3TzD,IAAM,eAAe;;;;;;;EAO1B,cAAc,OAAc,SAAe;AACzC,UAAM,WAA0B;MAC9B,MAAO,MAAM,SAAS,MAAM,KAAK;MACjC,QAAS,MAAM,SAAS,QAAQ,KAAK;MACrC,UAAW,MAAM,SAAS,UAAU,KAAK;MACzC,OAAQ,MAAM,SAAS,OAAO,KAAK;MACnC,OAAQ,MAAM,SAAS,OAAO,KAAK;MACnC,SAAU,MAAM,SAAS,SAAS,KAAK;;AAGzC,WAAO;MACL,MAAM,SAAS;MACf,QAAQ,SAAS;MACjB;MACA,SACE,MAAM,YAAY,WACd,SACA,KAAK,8BAA8B,MAAM,SAAS,OAAO;MAC/D,OAAO,SAAS;MAChB,OAAO,SAAS;MAChB,UAAU;QACR,UAAU,SAAS,SAAS,SAAQ;QACpC,SAAS,KAAK,oBAAoB;UAChC,KAAK,MAAM;UACX,UAAU,SAAS;SACpB;;MAEH,SAAS,SAAS;;EAEtB;;;;;;;EAQA,oBAAoB,EAAE,KAAK,SAAQ,GAA4C;AAC7E,WAAO,YAAY,OAAO,GAAG,GAAG,QAAQ;EAC1C;;;;;;;EAQA,8BAA8B,SAAwB,SAAe;AACnE,WAAO,GAAG,OAAO,IAAI,OAAO;EAC9B;;;;;;;EAQA,mBAAmB,SAAwB,WAAyB;AAClE,WAAO,GAAG,SAAS,IAAI,SAAS,SAAS,EAAE,CAAC;EAC9C;;;;;;EAOA,8BAA8B,cAA2B;AACvD,UAAM,QAAQ,aAAa,MAAM,GAAG;AACpC,QAAI,MAAM,SAAS,KAAK,CAAC,MAAM,CAAC,GAAG;AACjC,aAAO;IACT;AACA,UAAM,YAAY,MAAM,CAAC;AACzB,UAAM,SAAS,SAAS,WAAW,EAAE;AAErC,WAAO,MAAM,MAAM,IAAI,QAAQ,KAAK,OAAO,SAAS,EAAE,CAAC;EACzD;;;;;;EAOA,0BACE,UAAiC;AAGjC,QAAI,OAAO,aAAa,YAAY,aAAa,MAAM;AACrD,aAAO;IACT;AAGA,WAAO,OAAO,OAAO,QAAQ,EAAE,MAC7B,WAAS,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,WAAS,KAAK,aAAa,KAAK,CAAC,CAAC;EAEnF;;;;;;EAOA,aAAa,OAAY;AACvB,WACE,OAAO,UAAU,YACjB,UAAU,QACV,OAAO,MAAM,YAAY,YACzB,OAAO,MAAM,YAAY,aACxB,MAAM,SAAS,WAAW,MAAM,SAAS,aAC1C,OAAO,MAAM,aAAa,YAC1B,MAAM,aAAa,QACnB,OAAO,MAAM,SAAS,MAAM,MAAM,YAClC,OAAO,MAAM,SAAS,QAAQ,MAAM,YACpC,OAAO,MAAM,SAAS,UAAU,MAAM,YACtC,OAAO,MAAM,SAAS,OAAO,MAAM,YACnC,OAAO,MAAM,SAAS,SAAS,MAAM;EAEzC;;;;AC9IK,IAAM,cAAc;EACzB,MAAM,uBACJ,aAAoB;AAEpB,UAAM,UAAU,kBAAkB,MAAM;AACxC,UAAM,cAAc,gBAAgB,MAAM;AAE1C,QAAI,CAAC,WAAW,CAAC,aAAa;AAC5B,aAAO,CAAA;IACT;AAGA,QAAI,YAAY,mBAAmB,UAAU;AAC3C,YAAM,iBAAiB,MAAM,KAAK,kBAAkB,SAAS,WAAW;AACxE,UAAI,gBAAgB;AAClB,eAAO,KAAK,uBAAuB,cAAc;MACnD;IACF;AAGA,UAAM,WAAW,MAAM,wBAAwB,WAC7C,SACA,YAAY,eACZ,WAAW;AAGb,WAAO,KAAK,uBAAuB,SAAS,QAAQ;EACtD;EAEA,MAAM,kBAAkB,SAAiB,aAAwB;AAtCnE;AAuCI,QAAI;AACF,YAAM,aAAa,aAAa,8BAA8B,YAAY,aAAa;AACvF,YAAM,qBAAsB,MAAM,qBAAqB,gBAAgB,OAAO;AAK9E,UAAI,GAAC,oEAAqB,gBAArB,mBAAmC,sBAAnC,mBAAsD,YAAW;AACpE,eAAO;MACT;AAEA,YAAM,0BAA0B,MAAM,qBAAqB,gBAAgB;QACzE,SAAS;QACT,aAAa,CAAC,UAAU;OACzB;AAED,UAAI,CAAC,aAAa,0BAA0B,uBAAuB,GAAG;AACpE,eAAO;MACT;AAEA,YAAM,SAAS,wBAAwB,UAAU,KAAK,CAAA;AAEtD,aAAO,OAAO,IAAI,WAAS,aAAa,cAAc,OAAO,YAAY,aAAa,CAAC;IACzF,SAAS,OAAO;AACd,aAAO;IACT;EACF;;;;;EAMA,uBAAuB,UAAkD;AACvE,WAAO,SAAS,OAAO,aAAW,QAAQ,SAAS,aAAa,GAAG;EACrE;EAEA,wBAAwB,UAAkD;AACxE,YACE,qCAAU,IACR,YACG;MACC,GAAG;MACH,UAAS,+BAAO,WACZ,MAAM,UACN,gBAAgB,6BAA4B;MAChD,UAAU,SAAS,MAAM,SAAS,UAAU,EAAE;MAC9C,SAAS,MAAM;MACf,SAAS;YAEV,CAAA;EAET;;;;ACxCF,IAAME,UAAQ,MAA2B;EACvC,eAAe,CAAA;EACf,SAAS;CACV;AAGD,IAAMC,eAAa;EACjB,OAAAD;EAEA,UAAU,UAAiD;AACzD,WAAO,UAAIA,SAAO,MAAM,SAASA,OAAK,CAAC;EACzC;EAEA,aAAiC,KAAQ,UAAiD;AACxF,WAAO,aAAOA,SAAO,KAAK,QAAQ;EACpC;EAEA,SAAS,OAAmC;AAC1C,QAAI,OAAO;AACT,MAAAA,QAAM,QAAQ,IAAI,KAAK;IACzB;EACF;EAEA,eAAe,iBAAuD;AACpE,IAAAA,QAAM,kBAAkB;EAC1B;EAEA,mBAAmB,iBAAuD;AACxE,IAAAA,QAAM,kBAAkB;EAC1B;EAEA,2BACE,yBAAuE;AAEvE,IAAAA,QAAM,0BAA0B;EAClC;EAEA,uBAAuB,qBAA+D;AACpF,IAAAA,QAAM,sBAAsB;EAC9B;EAEA,uBAAuB,qBAA+D;AACpF,IAAAA,QAAM,sBAAsB;EAC9B;EAEA,WAAW,SAAuC;AAChD,IAAAA,QAAM,UAAU;EAClB;EAEA,MAAM,YAAS;AArGjB;AAsGI,QAAI;AACF,qBAAe,WAAW,IAAI;AAC9B,eAAQ,qBAAgB,MAAM,sBAAtB,mBAAyC,gBAAgB;QAC/D,KAAK;AACH,gBAAM,eAAe,aAAY;AAEjC;QACF,KAAK;AACH,gBAAM,eAAe,gBAAe;AAEpC;QACF;AACE,gBAAM,IAAI,MAAM,mBAAmB;MACvC;IACF;AACE,qBAAe,WAAW,KAAK;IACjC;EACF;EAEA,MAAM,eAAY;AAzHpB,gBAAAE,KAAA;AA0HI,UAAM,uBAAuB,gBAAgB,MAAM;AACnD,UAAM,qBAAoB,uBAAkB,MAAM,0BAAxB,mBAAgD;AAE1E,QAAI,CAAC,eAAe,MAAM,mBAAmB,CAAC,eAAe,MAAM,iBAAiB;AAClF,YAAM,IAAI,MAAM,6CAA6C;IAC/D;AAEA,QAAI,CAAC,eAAe,MAAM,OAAO;AAC/B,YAAM,IAAI,MAAM,qBAAqB;IACvC;AAEA,SAAI,oBAAe,MAAM,UAArB,mBAA4B,SAAS;AACvC,uBAAiB,UAAU;QACzB,MAAM;QACN,OAAO;QACP,YAAY;UACV,gBAAgB,sBAAsB,qBAAqB,cAAc;UACzE,OAAO,eAAe,MAAM,MAAM;UAClC,QAAQ,eAAe,MAAM;UAC7B,WAASA,MAAA,gBAAgB,MAAM,sBAAtB,gBAAAA,IAAyC,kBAAiB;;OAEtE;AACD,YAAM,eAAe,eAAe;QAClC,iBAAiB,eAAe,MAAM;QACtC,cAAc,eAAe,MAAM,MAAM;QACzC,iBAAiB,eAAe,MAAM;QACtC,UAAU,eAAe,MAAM,MAAM,SAAS;OAC/C;IACH,OAAO;AACL,uBAAiB,UAAU;QACzB,MAAM;QACN,OAAO;QACP,YAAY;UACV,gBAAgB,sBAAsB,qBAAqB,cAAc;UACzE,OAAO,eAAe,MAAM,MAAM,UAAU;UAC5C,QAAQ,eAAe,MAAM;UAC7B,WAAS,qBAAgB,MAAM,sBAAtB,mBAAyC,kBAAiB;;OAEtE;AACD,YAAM,eAAe,gBAAgB;QACnC,iBAAiB,eAAe,MAAM;QACtC,iBAAiB,eAAe,MAAM;QACtC,UAAU,eAAe,MAAM,MAAM,SAAS;OAC/C;IACH;EACF;EAEA,MAAM,kBAAkB,SAAkC;AAzK5D;AA0KI,IAAAF,QAAM,UAAU;AAChB,UAAM,WAAU,qBAAgB,MAAM,sBAAtB,mBAAyC;AACzD,UAAM,SAAQ,qBAAgB,MAAM,sBAAtB,mBAAyC;AACvD,UAAM,cAAc,gBAAgB,MAAM;AAC1C,UAAM,UAAU,cAAc,eAAe,gBAAgB,WAAW,IAAI;AAC5E,QACEA,QAAM,aACN,CAAC,eAAe,eAAeA,QAAM,WAAW,KAAKG,eAAc,UAAU,GAC7E;AACA,MAAAH,QAAM,UAAU;AAEhB,aAAO,CAAA;IACT;AAEA,QAAI;AACF,UAAI,WAAW,WAAW,OAAO;AAC/B,cAAM,WAAW,MAAM,YAAY,uBAAsB;AACzD,QAAAA,QAAM,gBAAgB;AACtB,QAAAA,QAAM,YAAY;AAElB,eAAO;MACT;IACF,SAAS,OAAO;AACd,MAAAA,QAAM,YAAY,KAAK,IAAG;AAE1B,yCAAU;AACV,sBAAgB,UAAU,2BAA2B;IACvD;AACE,MAAAA,QAAM,UAAU;IAClB;AAEA,WAAO,CAAA;EACT;EAEA,sBAAmB;AACjB,QAAIA,QAAM,cAAc,WAAW,GAAG;AACpC;IACF;AAEA,UAAM,uBAAuB,YAAY,wBAAwBA,QAAM,aAAa;AACpF,QAAI,CAAC,sBAAsB;AACzB;IACF;AAEA,UAAM,eAAe,qBAAqB,KACxC,WAAS,MAAM,YAAY,gBAAgB,6BAA4B,CAAE;AAG3E,QAAI,CAAC,cAAc;AACjB;IACF;AAEA,IAAAA,QAAM,sBAAsB,eACxB,WAAW,SAAS,aAAa,SAAS,SAAS,aAAa,KAAK,EAAE,SAAQ,IAC/E;EACN;EAEA,MAAM,gBAAgB,QAAgB;AAnOxC,gBAAAE,KAAA;AAoOI,qBAAiB,qBAAqB,CAAA,CAAE;AAExC,UAAME,MAAK,OAAO;AAClB,UAAM,UAAU,kBAAkB,MAAM;AACxC,UAAM,QAAQ,qBAAqB,WACjC,OAAO,gBAAgB,SAAQ,GAC/B,OAAO,OAAO,QAAQ,CAAC;AAEzB,UAAM,OAAO;AAEb,UAAM,qBAAqB,gBAAgB;MACzC,gBAAgB;MAChB,IAAAA;MACA;MACA;MACA,OAAO,SAAS,OAAO,CAAC;KACzB;AAED,qBAAiB,UAAU;MACzB,MAAM;MACN,OAAO;MACP,YAAY;QACV,kBACE,uBAAkB,MAAM,0BAAxB,mBAAgD,eAChD,qBAAqB,cAAc;QACrC,SAAO,oBAAe,MAAM,UAArB,mBAA4B,WAAU;QAC7C,QAAQ,OAAO;QACf,WAASF,MAAA,gBAAgB,MAAM,sBAAtB,gBAAAA,IAAyC,kBAAiB;;KAEtE;AAED,+BAAqB,WAAU,MAA/B,mBAAmC,cAAc;AACjD,mBAAe,UAAS;EAC1B;EAEA,MAAM,eAAe,QAA2B;AAC9C,qBAAiB,qBAAqB;MACpC,YAAS;AACP,yBAAiB,QAAQ,SAAS;MACpC;KACD;AAED,UAAM,SAAS,qBAAqB,WAClC,OAAO,gBAAgB,SAAQ,GAC/B,OAAO,OAAO,QAAQ,CAAC;AAGzB,QACE,kBAAkB,MAAM,WACxB,OAAO,mBACP,OAAO,mBACP,OAAO,cACP;AACA,YAAM,eAAe,eAAe,gBAClC,OAAO,YAA2B;AAGpC,YAAM,qBAAqB,cAAc;QACvC,aAAa,kBAAkB,MAAM;QACrC;QACA,MAAM,CAAC,OAAO,iBAAkC,UAAU,OAAO,CAAC,CAAC;QACnE,QAAQ;QACR,KAAK,aAAa,YAAY,YAAY;QAC1C,gBAAgB;OACjB;AAED,qBAAe,UAAS;IAC1B;EACF;EAEA,MAAM,kBAAe;AA1SvB;AA2SI,QAAI,CAAC,eAAe,MAAM,mBAAmB,CAAC,eAAe,MAAM,iBAAiB;AAClF,YAAM,IAAI,MAAM,6CAA6C;IAC/D;AAEA,qBAAiB,qBAAqB;MACpC,YAAS;AACP,yBAAiB,QAAQ,SAAS;MACpC;KACD;AAED,UAAM,qBAAqB,gBAAgB;MACzC,gBAAgB;MAChB,IAAI,eAAe,MAAM;MACzB,OAAO,eAAe,MAAM;KAC7B;AAED,+BAAqB,WAAU,MAA/B,mBAAmC,cAAc;AACjD,mBAAe,UAAS;EAC1B;EAEA,YAAS;AACP,IAAAF,QAAM,QAAQ;AACd,IAAAA,QAAM,kBAAkB;AACxB,IAAAA,QAAM,kBAAkB;AACxB,IAAAA,QAAM,0BAA0B;AAChC,IAAAA,QAAM,sBAAsB;AAC5B,IAAAA,QAAM,UAAU;AAChB,IAAAA,QAAM,gBAAgB,CAAA;EACxB;;AAIK,IAAM,iBAAiB,kBAAkBC,YAAU;;;AC3S1D,IAAM,eAAuC;EAC3C,YAAY;EACZ,cAAc,CAAA;EACd,sBAAsB;EACtB,eAAe,oBAAI,IAAG;EACtB,aAAa,CAAA;EACb,MAAM;;AAGR,IAAM,eAAoC;EACxC,aAAa;EACb,qBAAqB;EACrB,6BAA6B,CAAA;;AAqB/B,IAAMI,UAAQ,MAA4B;EACxC,QAAQ,SAAQ;EAChB,mBAAmB;EACnB,aAAa;EACb,mBAAmB;EACnB,YAAY;EACZ,kBAAkB;IAChB,yBAAyB;IACzB,4BAA4B;;EAE9B,sBAAsB;CACvB;AAGD,IAAMC,eAAa;EACjB,OAAAD;EAEA,UAAU,UAA+C;AACvD,WAAO,UAAIA,SAAO,MAAK;AACrB,eAASA,OAAK;IAChB,CAAC;EACH;EAEA,aACE,KACA,UAAkD;AAElD,WAAO,aAAOA,SAAO,KAAK,QAAQ;EACpC;EAEA,mBACE,UACA,UACA,OAAsB;AAEtB,QAAI,OAAoC;AAExC,WAAO,UAAIA,QAAM,QAAQ,MAAK;AAtGlC;AAuGM,YAAM,cAAc,SAASA,QAAM;AAEnC,UAAI,aAAa;AACf,cAAM,aAAY,KAAAA,QAAM,OAAO,IAAI,WAAW,MAA5B,mBAAgC;AAClD,YAAI,SAAS,WAAW;AACtB,iBAAO;AACP,mBAAS,SAAS;QACpB;MACF;IACF,CAAC;EACH;EAEA,WACE,UACA,cACA,SAGC;AAED,UAAM,EAAE,SAAS,eAAe,WAAW,gBAAe,IACxD,YAAY,sBAAqB;AACnC,UAAM,oBAAoB,6CAAc,KACtC,aAAW,QAAQ,GAAG,SAAQ,OAAO,+CAAe;AAGtD,UAAM,iBAAiB,SAAS,KAAK,cAAW,mCAAS,eAAc,eAAe;AACtF,UAAM,oBAAoB,mBAAkB,qCAAW;AAEvD,UAAM,yBAAyB,SAAS,IAAI,OAAK,EAAE,SAAS,EAAE,OAAO,CAAAE,OAAKA,OAAM,MAAS;AAKzF,UAAM,aAAa,kBAAkB,MAAM,iBACvC,oBAAI,IAAI,CAAC,GAAG,sBAAsB,CAAC,IACnC,oBAAI,IAAI,CAAC,IAAI,6CAAc,IAAI,aAAW,QAAQ,oBAAmB,CAAA,CAAG,CAAC;AAE7E,SAAI,qCAAU,YAAW,KAAK,CAAC,mBAAmB;AAChD,MAAAF,QAAM,aAAa;IACrB;AAEA,QAAI,CAACA,QAAM,YAAY;AACrB,MAAAA,QAAM,cAAc,uDAAmB;AACvC,MAAAA,QAAM,oBAAoB;AAC1B,sBAAgB,oBAAoB,uDAAmB,WAAW;QAChE,aAAa;OACd;AAED,UAAIA,QAAM,aAAa;AACrB,8BAAsB,IAAI,EAAE,aAAa,uDAAmB,UAAS,CAAE;MACzE;IACF;AAEA,eAAW,QAAQ,eAAY;AAC7B,YAAM,oBAAoB,6CAAc,OACtC,aAAW,QAAQ,mBAAmB;AAExC,sBAAgB,MAAM,OAAO,IAAI,WAA6B;QAC5D;QACA,cAAc,MAAM;UAClB,GAAG;UACH,aAAa,uDAAoB;SAClC;QACD,cAAc,MAAM,YAAY;QAChC,cAAc,qBAAqB,CAAA;QACnC,GAAG;OACJ;AACD,sBAAgB,yBAAyB,qBAAqB,CAAA,GAAI,SAAS;IAC7E,CAAC;EACH;EAEA,cAAc,WAAyB;AA/KzC;AAgLI,QAAIA,QAAM,gBAAgB,WAAW;AACnC,YAAM,cAAc,MAAM,KAAKA,QAAM,OAAO,QAAO,CAAE,EAAE,KACrD,CAAC,CAAC,cAAc,MAAM,mBAAmB,SAAS;AAEpD,UAAI,aAAa;AACf,cAAM,eAAc,uBAAY,CAAC,MAAb,mBAAgB,iBAAhB,mBAA+B;AACnD,YAAI,aAAa;AACf,0BAAgB,qBAAqB,WAAW;QAClD;MACF;IACF;AACA,IAAAA,QAAM,OAAO,OAAO,SAAS;EAC/B;EAEA,WACE,SACA,EAAE,yBAAyB,2BAA0B,GACrD,cAA6C;AAE7C,IAAAA,QAAM,OAAO,IAAI,QAAQ,WAA6B;MACpD,WAAW,QAAQ;MACnB,cAAc;QACZ,GAAG;QACH,aAAa,aAAa,CAAC;;MAE7B;MACA;MACA;MACA;KACD;AACD,oBAAgB,0BACd,6CAAc,OAAO,iBAAe,YAAY,mBAAmB,QAAQ,eAAc,CAAA,GACzF,QAAQ,SAA2B;EAEvC;EAEA,WAAW,SAAoB;AApNjC;AAqNI,UAAM,eAAeA,QAAM,OAAO,IAAI,QAAQ,cAAc;AAE5D,QAAI,cAAc;AAChB,YAAM,cAAc,CAAC,GAAI,aAAa,gBAAgB,CAAA,CAAG;AACzD,UAAI,GAAC,kBAAa,iBAAb,mBAA2B,KAAK,iBAAe,YAAY,OAAO,QAAQ,MAAK;AAClF,oBAAY,KAAK,OAAO;MAC1B;AACA,MAAAA,QAAM,OAAO,IAAI,QAAQ,gBAAgB,EAAE,GAAG,cAAc,cAAc,YAAW,CAAE;AACvF,sBAAgB,yBAAyB,aAAa,QAAQ,cAAc;AAC5E,0BAAoB,kBAAkB,QAAQ,gBAAgB,IAAI;IACpE;EACF;EAEA,cAAc,WAA2B,WAA0B;AAlOrE,gBAAAG;AAmOI,UAAM,eAAeH,QAAM,OAAO,IAAI,SAAS;AAE/C,QAAI,cAAc;AAEhB,YAAM,oBAAkB,KAAAA,QAAM,sBAAN,mBAAyB,QAAO;AAGxD,YAAM,2BAA2B;QAC/B,KAAI,kBAAa,iBAAb,mBAA2B,OAAO,aAAW,QAAQ,OAAO,eAAc,CAAA;;AAIhF,UAAI,qBAAmBG,MAAA,6CAAc,iBAAd,gBAAAA,IAA6B,KAAI;AACtD,wBAAgB,qBAAqB,aAAa,aAAa,CAAC,CAAC;MACnE;AAEA,MAAAH,QAAM,OAAO,IAAI,WAAW,EAAE,GAAG,cAAc,cAAc,yBAAwB,CAAE;AACvF,sBAAgB,yBAAyB,4BAA4B,CAAA,GAAI,SAAS;AAElF,UAAI,yBAAyB,WAAW,GAAG;AACzC,4BAAoB,kBAAkB,WAAW,KAAK;MACxD;IACF;EACF;EAEA,uBAAuB,OAAuB,OAAmC;AAC/E,UAAM,eAAeA,QAAM,OAAO,IAAI,KAAK;AAE3C,QAAI,cAAc;AAChB,mBAAa,eAAe;QAC1B,GAAI,aAAa,gBAAgB;QACjC,GAAG;;AAGL,MAAAA,QAAM,OAAO,IAAI,OAAO,YAAY;IACtC;EACF;EAEA,oBACE,OACA,cACA,WAAW,MAAI;AAEf,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,MAAM,gDAAgD;IAClE;AAEA,UAAM,eAAeA,QAAM,OAAO,IAAI,KAAK;AAE3C,QAAI,cAAc;AAChB,YAAM,kBAAkB,EAAE,GAAI,aAAa,gBAAgB,cAAe,GAAG,aAAY;AACzF,MAAAA,QAAM,OAAO,IAAI,OAAO,EAAE,GAAG,cAAc,cAAc,gBAAe,CAAE;AAC1E,UAAIA,QAAM,OAAO,SAAS,KAAKA,QAAM,gBAAgB,OAAO;AAC1D,YAAI,aAAa,aAAa;AAC5B,UAAAA,QAAM,oBAAoB,aAAa;QACzC;AACA,0BAAkB,aAAa,eAAe;MAChD;IACF;EACF;EAEA,oBACE,OACA,cAA0C;AAE1C,QAAI,CAAC,OAAO;AACV;IACF;AACA,UAAM,eAAeA,QAAM,OAAO,IAAI,KAAK;AAC3C,QAAI,cAAc;AAChB,YAAM,kBAAkB,EAAE,GAAI,aAAa,gBAAgB,cAAe,GAAG,aAAY;AACzF,MAAAA,QAAM,OAAO,IAAI,OAAO,EAAE,GAAG,cAAc,cAAc,gBAAe,CAAE;IAC5E;EACF;;EAGA,eACE,MACA,OACA,OACA,eAAe,MAAI;AAEnB,oBAAgB,oBAAoB,OAAO,EAAE,CAAC,IAAI,GAAG,MAAK,GAAI,YAAY;AAC1E,QAAI,SAAS,YAAY,UAAU,kBAAkB,OAAO;AAC1D,0BAAoB,kBAAkB,KAAK;IAC7C;EACF;EAEA,mBAAmB,OAAiC;AA3TtD;AA4TI,IAAAA,QAAM,cAAc;AAEpB,UAAM,aAAa,QAAQA,QAAM,OAAO,IAAI,KAAK,IAAI;AACrD,UAAM,eAAc,8CAAY,iBAAZ,mBAA0B;AAE9C,SAAI,2CAAa,OAAM,OAAO;AAC5B,MAAAA,QAAM,qBAAoB,8CAAY,iBAAZ,mBAA0B;AACpD,MAAAA,QAAM,oBAAoB;AAC1B,sBAAgB,oBAAoB,OAAO,EAAE,YAAW,CAAE;AAC1D,kBAAY,uBAAuB,2CAAa,aAAa;AAC7D,4BAAsB,IAAI;QACxB,aAAa;QACb,mBAAmB,2CAAa;OACjC;IACH;EACF;EAEA,qBAAqB,aAA+C;AA7UtE,gBAAAG;AA8UI,QAAI,CAAC,aAAa;AAChB;IACF;AAEA,QAAIH,QAAM,gBAAgB,YAAY,gBAAgB;AACpD,sBAAgB,wBAAwB,IAAI;IAC9C;AAEA,UAAM,aAAaA,QAAM,OAAO,IAAI,YAAY,cAAc;AAC9D,IAAAA,QAAM,cAAc,YAAY;AAChC,IAAAA,QAAM,oBAAoB;AAC1B,oBAAgB,oBAAoB,YAAY,gBAAgB,EAAE,YAAW,CAAE;AAE/E,SAAI,8CAAY,iBAAZ,mBAA0B,SAAS;AACrC,MAAAA,QAAM,oBAAoB,GAAG,YAAY,cAAc,IAAI,YAAY,EAAE,KAAI,8CAAY,iBAAZ,mBAA0B,OAAO;IAChH,OAAO;AACL,MAAAA,QAAM,oBAAoB;IAC5B;AAGA,oBAAgB,eACd,eACAA,QAAM,mBACN,YAAY,cAAc;AAG5B,QAAI,YAAY;AACd,wBAAkB,aAAa,WAAW,YAAY;IACxD;AAEA,mBAAe,UAAS;AAExB,0BAAsB,IAAI;MACxB,aAAaA,QAAM;MACnB,oBAAmBG,MAAAH,QAAM,sBAAN,gBAAAG,IAAyB;KAC7C;AACD,gBAAY,uBAAuB,YAAY,aAAa;AAE5D,UAAM,cAAc,gBAAgB,wBAAwB,YAAY,cAAc;AAEtF,QACE,CAAC,eACD,kBAAkB,MAAM,uBACxB,CAAC,kBAAkB,MAAM,yBACzB,CAAC,qBAAqB,MAAM,SAC5B;AACA,sBAAgB,uBAAsB;IACxC;EACF;EAEA,eAAe,aAA+C;AAhYhE;AAiYI,QAAI,CAAC,aAAa;AAChB;IACF;AAEA,UAAM,QAAQH,QAAM,OAAO,IAAI,YAAY,cAAc;AACzD,QAAI,OAAO;AACT,2CAAO,iBAAP,mBAAqB,KAAK;IAC5B;EACF;EAEA,MAAM,sBAAsB,WAAqC;AA3YnE;AA4YI,QAAI,CAAC,WAAW;AACd;IACF;AAEA,UAAM,mBAAmB,cAAc,gBAAgB,MAAM;AAC7D,UAAM,0BAAyB,qBAAgB,eAAe,SAAS,MAAxC,mBAA2C;AAC1E,UAAM,wBAAwB,gBAAgB,0BAC5C,WACA,iEAAwB,EAAE;AAG5B,QAAI,oBAAoB,uBAAuB;AAC7C,YAAM,gBAAgB,oBAAoB,qBAAqB;IACjE;EACF;EAEA,MAAM,oBAAoB,SAAoB;AA5ZhD;AA6ZI,UAAM,gBAAgB,gBAAgB,MAAM,OAAO,IACjD,gBAAgB,MAAM,WAA6B;AAGrD,UAAM,qBAAqB,GAAC,oDAAe,iBAAf,mBAA6B,KACvD,iBAAY;AAlalB,UAAAI;AAkaqB,yBAAY,SAAOA,MAAAJ,QAAM,sBAAN,gBAAAI,IAAyB;;AAG7D,UAAM,0BAA0B,gBAAgB,2BAC9C,QAAQ,cAAc;AAGxB,QAAI,yBAAyB;AAC3B,UAAI;AACF,cAAM,wBAAwB,kBAAkB,OAAO;AACvD,YAAI,oBAAoB;AACtB,0BAAgB,MAAK;QACvB;MACF,SAAS,OAAO;AACd,yBAAiB,OAAM;MACzB;AAEA,uBAAiB,UAAU;QACzB,MAAM;QACN,OAAO;QACP,YAAY,EAAE,SAAS,QAAQ,cAAa;OAC7C;IACH;EACF;EAEA,2BAA2B,gBAA+B;AACxD,UAAM,QAAQ,kBAAkBJ,QAAM;AAEtC,UAAM,eAAeA,QAAM,OAAO,IAAI,KAAuB;AAE7D,QAAI,CAAC,cAAc;AACjB,YAAM,IAAI,MAAM,yBAAyB;IAC3C;AAEA,QAAI,CAAC,aAAa,yBAAyB;AACzC,YAAM,IAAI,MAAM,kCAAkC;IACpD;AAEA,WAAO,aAAa;EACtB;EAEA,8BAA8B,QAAuB;AACnD,UAAM,QAAQ,UAAUA,QAAM;AAE9B,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,MAAM,uDAAuD;IACzE;AAEA,UAAM,eAAeA,QAAM,OAAO,IAAI,KAAK;AAE3C,QAAI,EAAC,6CAAc,6BAA4B;AAC7C,YAAM,IAAI,MAAM,qCAAqC;IACvD;AAEA,WAAO,aAAa;EACtB;EAEA,eACE,KACA,QAAuB;AA7d3B;AA+dI,QAAI,QAAQA,QAAM;AAElB,QAAI,QAAQ;AACV,cAAQ;IACV;AAEA,QAAI,CAAC,OAAO;AACV,aAAO;IACT;AAEA,UAAM,qBAAoB,KAAAA,QAAM,OAAO,IAAI,KAAK,MAAtB,mBAAyB;AAEnD,QAAI,CAAC,mBAAmB;AACtB,aAAO;IACT;AAEA,WAAO,kBAAkB,GAAG;EAC9B;EAEA,eACE,KACA,WAAyB;AApf7B;AAsfI,UAAM,qBAAoB,KAAAA,QAAM,OAAO,IAAI,SAAS,MAA1B,mBAA6B;AAEvD,QAAI,CAAC,mBAAmB;AACtB,aAAO;IACT;AAEA,WAAO,kBAAkB,GAAG;EAC9B;EAEA,yBAAyB,eAA6B;AACpD,UAAM,UAAUA,QAAM,OAAO,IAAI,aAAa;AAC9C,UAAM,EAAE,yBAAyB,CAAA,GAAI,wBAAwB,CAAA,EAAE,KAAK,mCAAS,iBAAgB,CAAA;AAC7F,UAAM,iBAAiB,eAAe,sBACpC,wBACA,qBAAqB;AAGvB,WAAO;EACT;EAEA,8BAA2B;AACzB,UAAM,wBAAuC,CAAA;AAE7C,IAAAA,QAAM,OAAO,QAAQ,kBAAe;AAClC,YAAM,eAAe,gBAAgB,yBACnC,aAAa,SAA2B;AAE1C,4BAAsB,KAAK,GAAG,YAAY;IAC5C,CAAC;AAED,WAAO;EACT;EAEA,yBAAyB,cAA6B,OAAqB;AACzE,oBAAgB,uBAAuB,OAAO,EAAE,uBAAuB,aAAY,CAAE;AACrF,UAAM,2BAA2B,gBAAgB,4BAA2B;AAC5E,UAAM,aAAa,yBAAyB,IAAI,aAAW,QAAQ,cAAc;AACjF,UAAM,mBAAmB,MAAM,KAAK,IAAI,IAAI,UAAU,CAAC;AACvD,wBAAoB,mBAAmB,gBAAgB;EACzD;EAEA,+BAA4B;AAC1B,UAAM,yBAA0C,CAAA;AAEhD,IAAAA,QAAM,OAAO,QAAQ,kBAAe;AAClC,YAAM,cAAc,gBAAgB,0BAClC,aAAa,SAA2B;AAE1C,6BAAuB,KAAK,GAAG,WAAW;IAC5C,CAAC;AAED,WAAO;EACT;EAEA,uBAAoB;AAClB,WAAOA,QAAM;EACf;EAEA,uBAAoB;AAClB,WAAOA,QAAM;EACf;EAEA,0BAA0B,WAAyB;AApjBrD;AAqjBI,UAAM,UAAUA,QAAM,OAAO,IAAI,SAAS;AAC1C,UAAM,2BAAyB,wCAAS,iBAAT,mBAAuB,2BAA0B,CAAA;AAEhF,WAAO;EACT;EAEA,MAAM,4BAA4B,WAAyB;AACzD,UAAM,0BAA0B,gBAAgB,2BAA0B;AAC1E,UAAM,OAAO,OAAM,mEAAyB;AAE5C,oBAAgB,uBAAuB,WAAW;MAChD,wBAAwB,6BAAM;MAC9B,qBAAqB,6BAAM;KAC5B;EACH;EAEA,wBAAwB,WAA2B,aAAyB;AAC1E,UAAM,oBAAoB,eAAeA,QAAM;AAC/C,UAAM,wBAAwB,gBAAgB,yBAAyB,SAAS;AAEhF,QAAI,CAAC,sBAAsB,QAAQ;AACjC,aAAO;IACT;AAEA,WAAO,+DAAuB,KAAK,aAAW,QAAQ,QAAO,uDAAmB;EAClF;EAEA,wBAAwB,SAAwB;AAC9C,QAAI,CAACA,QAAM,aAAa;AACtB,aAAO;IACT;AAEA,UAAM,wBAAwB,gBAAgB,yBAAyBA,QAAM,WAAW;AAExF,WAAO,+DAAuB,KAAK,aAAW,QAAQ,OAAO;EAC/D;;EAGA,+BAA+B,6BAAuC,OAAqB;AACzF,oBAAgB,uBAAuB,OAAO,EAAE,4BAA2B,CAAE;EAC/E;EAEA,6BAA0B;AA/lB5B;AAgmBI,UAAM,YAAY,YAAY,uBAAsB,KAAAA,QAAM,sBAAN,mBAAyB,aAAa;AAC1F,UAAM,cAAcA,QAAM;AAE1B,QAAI,CAAC,eAAe,CAAC,WAAW;AAC9B,aAAO;IACT;AAEA,UAAM,8BAA8B,gBAAgB,eAClD,+BACA,WAAW;AAGb,WAAO,QAAQ,2EAA6B,SAAS,OAAO,SAAS,EAAE;EACzE;EAEA,+BAA4B;AA/mB9B;AAgnBI,UAAM,cAAY,KAAAA,QAAM,sBAAN,mBAAyB,mBAAkB;AAC7D,UAAM,YAAU,KAAAA,QAAM,sBAAN,mBAAyB,OAAM;AAC/C,UAAM,UAAUK,eAAc,qBAAqB,SAAS;AAE5D,WAAO,GAAG,SAAS,IAAI,OAAO,IAAI,OAAO;EAC3C;EAEA,yBAAsB;AACpB,oBAAgB,KAAK,EAAE,MAAM,mBAAkB,CAAE;EACnD;EAEA,wBAAqB;AACnB,UAAM,oBAAoBL,QAAM;AAEhC,WAAO,SACL,uDAAmB,mBACjBK,eAAc,iCAAiC,SAAS,kBAAkB,cAAc,CAAC;EAE/F;EAEA,aAAa,WAAyB;AACpC,oBAAgB,uBAAuB,WAAW;MAChD,wBAAwB;MACxB,qBAAqB;MACrB,6BAA6B,CAAA;KAC9B;EACH;EAEA,aAAa,OAAiC;AAC5C,UAAM,eAAe;AAErB,QAAI,CAAC,cAAc;AACjB,YAAM,IAAI,MAAM,uCAAuC;IACzD;AAEA,IAAAL,QAAM,oBAAoB;AAC1B,oBAAgB,oBAAoB,cAAc;MAChD,sBAAsB;MACtB,YAAY;MACZ,aAAa;MACb,SAAS;MACT,SAAS;MACT,eAAe;MACf,aAAa;MACb,cAAc;MACd,oBAAoB;MACpB,cAAc,CAAA;MACd,qBAAqB;MACrB,uBAAuB;MACvB,gBAAgB;MAChB,cAAc;MACd,cAAc;MACd,aAAa,CAAA;MACb,MAAM;MACN,QAAQ;KACT;AACD,wBAAoB,kBAAkB,YAAY;EACpD;EAEA,wBAAwB,sBAA6B;AACnD,IAAAA,QAAM,uBAAuB;EAC/B;EAEA,2CAAwC;AA/qB1C;AAgrBI,UAAM,kBAAoC,CAAA;AAC1C,QAAI,mBAA4C;AAEhD,IAAAA,QAAM,OAAO,QAAQ,WAAQ;AAC3B,UAAI,cAAoB,gCAAgC,KAAK,QAAM,OAAO,MAAM,SAAS,GAAG;AAC1F,YAAI,MAAM,WAAW;AACnB,0BAAgB,KAAK,MAAM,SAAS;QACtC;MACF;IACF,CAAC;AAED,QAAI,gBAAgB,SAAS,GAAG;AAC9B,YAAM,sBAAsB,gBAAgB,CAAC;AAC7C,yBAAmB,uBACf,WAAAA,QAAM,OAAO,IAAI,mBAAmB,MAApC,mBAAuC,iBAAvC,mBAAsD,KACtD;AAEJ,aAAO;IACT;AAEA,WAAO;EACT;EAEA,eAAe,gBAA+B;AAvsBhD;AAwsBI,QAAI,CAAC,gBAAgB;AACnB,aAAO,kBAAkB;IAC3B;AAEA,YAAO,qBAAgB,MAAM,OAAO,IAAI,cAAc,MAA/C,mBAAkD;EAC3D;EAEA,eAAe,gBAA+B;AA/sBhD;AAgtBI,UAAM,YAAY,kBAAkBA,QAAM;AAE1C,QAAI,CAAC,WAAW;AACd,aAAO;IACT;AAEA,YAAO,qBAAgB,MAAM,OAAO,IAAI,SAAS,MAA1C,mBAA6C;EACtD;EAEA,0BACE,gBACA,SAAqC;AA3tBzC,gBAAAG;AA6tBI,QAAI,CAAC,gBAAgB;AACnB,aAAO;IACT;AAEA,UAAM,QAAQ,gBAAgB,MAAM,OAAO,IAAI,cAAc;AAC7D,UAAM,aAAY,oCAAO,iBAAP,mBAAqB,KAAK,aAAW,QAAQ,OAAO;AAEtE,QAAI,WAAW;AACb,aAAO;IACT;AAEA,aAAO,oCAAO,iBAAP,mBAAqB,kBAAeA,MAAA,+BAAO,iBAAP,gBAAAA,IAAsB;EACnE;;;;;;EAOA,6BAA0B;AACxB,UAAM,YAAY,oBAAoB,MAAM;AAC5C,UAAM,SAAS,YAAY,CAACH,QAAM,OAAO,IAAI,SAAS,CAAC,IAAI,MAAM,KAAKA,QAAM,OAAO,OAAM,CAAE;AAE3F,WAAO,OACJ,QAAQ,YAAS,+BAAO,iBAAgB,CAAA,CAAE,EAC1C,IAAI,iBAAe,YAAY,aAAa;EACjD;EAEA,gBAAgB,WAA0B;AACxC,QAAI,WAAW;AACb,aAAO,gBAAgB,yBAAyB,SAAS;IAC3D;AAEA,WAAO,gBAAgB,4BAA2B;EACpD;;AAIK,IAAM,kBAAkB,kBAAkBC,YAAU;;;AC1uB3D,IAAMK,WAAU,eAAe,UAAS;AACjC,IAAMC,OAAM,IAAI,UAAU;EAC/B,SAAAD;EACA,UAAU;CACX;AACD,IAAM,UAAU;AAChB,IAAM,qBAAqB;AAC3B,IAAM,oBAAoB;AA6B1B,IAAME,UAAQ,MAA0B;EACtC,UAAU,CAAA;EACV,MAAM;EACN,OAAO;EACP,UAAU,CAAA;EACV,aAAa,CAAA;EACb,aAAa,CAAA;EACb,gBAAgB,CAAA;EAChB,SAAS,CAAA;EACT,iBAAiB,CAAA;EACjB,QAAQ,CAAA;EACR,oBAAoB;EACpB,iBAAiB,CAAA;EACjB,8BAA8B;CAC/B;AAGM,IAAM,gBAAgB;EAC3B,OAAAA;EAEA,aAAiC,KAAQ,UAAgD;AACvF,WAAO,aAAOA,SAAO,KAAK,QAAQ;EACpC;EAEA,oBAAiB;AACf,UAAM,EAAE,WAAW,SAAS,WAAU,IAAK,kBAAkB;AAE7D,WAAO;MACL;MACA,IAAI,WAAW;MACf,IAAI,cAAc;;EAEtB;EAEA,qBAAqB,SAAmB;AACtC,QAAI,kBAAkB,MAAM,qBAAqB;AAC/C,aAAO,QAAQ,OAAO,OAAK,QAAQ,EAAE,eAAe,EAAE,gBAAgB,EAAE,WAAW,CAAC;IACtF;AAEA,WAAO;EACT;EAEA,MAAM,kBAAkB,SAAe;AACrC,UAAM,WAAW,GAAGD,KAAI,OAAO,mBAAmB,OAAO;AACzD,UAAM,OAAO,MAAMA,KAAI,QAAQ,EAAE,MAAM,UAAU,QAAQ,cAAc,kBAAiB,EAAE,CAAE;AAC5F,oBAAgB,eAAe,SAAS,IAAI,gBAAgB,IAAI,CAAC;EACnE;EAEA,MAAM,mBAAmB,SAAe;AACtC,UAAM,WAAW,GAAGA,KAAI,OAAO,yBAAyB,OAAO;AAC/D,UAAM,OAAO,MAAMA,KAAI,QAAQ,EAAE,MAAM,UAAU,QAAQ,cAAc,kBAAiB,EAAE,CAAE;AAC5F,oBAAgB,gBAAgB,SAAS,IAAI,gBAAgB,IAAI,CAAC;EACpE;EAEA,MAAM,qBAAqB,SAAe;AACxC,UAAM,WAAW,GAAGA,KAAI,OAAO,yBAAyB,OAAO;AAC/D,UAAM,OAAO,MAAMA,KAAI,QAAQ,EAAE,MAAM,UAAU,QAAQ,cAAc,kBAAiB,EAAE,CAAE;AAC5F,oBAAgB,kBAAkB,SAAS,IAAI,gBAAgB,IAAI,CAAC;EACtE;EAEA,MAAM,oBAAoB,aAAmB;AAC3C,UAAM,WAAW,GAAGA,KAAI,OAAO,4BAA4B,WAAW;AACtE,UAAM,OAAO,MAAMA,KAAI,QAAQ,EAAE,MAAM,UAAU,QAAQ,cAAc,kBAAiB,EAAE,CAAE;AAC5F,oBAAgB,iBAAiB,aAAa,IAAI,gBAAgB,IAAI,CAAC;EACzE;EAEA,MAAM,iBAAiB,QAAc;AACnC,UAAM,WAAW,GAAGA,KAAI,OAAO,yBAAyB,MAAM;AAC9D,UAAM,OAAO,MAAMA,KAAI,QAAQ,EAAE,MAAM,UAAU,QAAQ,cAAc,kBAAiB,EAAE,CAAE;AAC5F,oBAAgB,cAAc,QAAQ,IAAI,gBAAgB,IAAI,CAAC;EACjE;EAEA,yBAAyB,SAAmB;AAC1C,UAAM,kBAAkB,eAAe,SAAQ,IAC3C,mCAAS,OAAO,OAAI;AAClB,UAAI,EAAE,aAAa;AACjB,eAAO;MACT;AAEA,UAAI,EAAE,OAAO,wBAAwB,SAAS,IAAI;AAChD,eAAO;MACT;AACA,YAAM,WAAW,gBAAgB,MAAM,gBAAgB;AAEvD,aACE,aACC,EAAE,OAAO,wBAAwB,SAAS,MACzC,EAAE,OAAO,wBAAwB,QAAQ;IAE/C,KACA;AAEJ,WAAO;EACT;EAEA,MAAM,qBAAkB;AACtB,UAAM,WAAW,MAAMA,KAAI,IAAiC;MAC1D,MAAM;MACN,QAAQ,cAAc,kBAAiB;KACxC;AAED,WAAO,SAAS;EAClB;EAEA,MAAM,sBAAmB;AACvB,QAAI;AACF,YAAM,EAAE,eAAc,IAAK,MAAMA,KAAI,IAAkC;QACrE,MAAM;QACN,QAAQ,cAAc,kBAAiB;OACxC;AAED,aAAO;IACT,SAAS,OAAO;AACd,aAAO,CAAA;IACT;EACF;EAEA,MAAM,qBAAkB;AACtB,UAAM,wBAAwB,gBAAgB,4BAA2B;AAEzE,UAAM,MAAM,+DACR,IAAI,CAAC,EAAE,OAAM,MAAO,iCAAQ,SAC7B,OAAO,SACP,OAAO,aAAW,CAAC,UAAU,oBAAoB,OAAO;AAE3D,QAAI,KAAK;AACP,YAAM,QAAQ,WAAY,IAAiB,IAAI,QAAM,cAAc,mBAAmB,EAAE,CAAC,CAAC;IAC5F;EACF;EAEA,MAAM,uBAAoB;AACxB,UAAM,EAAE,WAAU,IAAK,oBAAoB;AAC3C,UAAM,MAAM,WAAW,IAAI,CAAC,EAAE,QAAO,MAAO,OAAO,EAAE,OAAO,OAAO;AACnE,UAAM,QAAQ,WAAY,IAAiB,IAAI,QAAM,cAAc,qBAAqB,EAAE,CAAC,CAAC;EAC9F;EAEA,MAAM,oBAAoB,aAAuB,CAAA,GAAE;AACjD,UAAM,QAAQ,WACZ,WAAW,IAAI,cAAY,cAAc,oBAAoB,QAAQ,CAAC,CAAC;EAE3E;EAEA,MAAM,iBAAiB,SAAmB,CAAA,GAAE;AAC1C,UAAM,QAAQ,WAAW,OAAO,IAAI,WAAS,cAAc,iBAAiB,KAAK,CAAC,CAAC;EACrF;EAEA,MAAM,aAAa,QAAkE;AA/MvF;AAgNI,UAAM,UAAU,OAAO,WAAW,CAAA;AAClC,UAAM,gBAAgB,cAAc,kBAAiB;AACrD,QAAI,cAAc,GAAG,WAAW,YAAY,GAAG;AAC7C,cAAQ,KAAK,GAAG,OAAO,OAAO,uBAAuB,EAAE,IAAI,OAAK,EAAE,EAAE,CAAC;IACvE;AAEA,UAAM,UAAU,MAAMA,KAAI,IAA2B;MACnD,MAAM;MACN,QAAQ;QACN,GAAG,cAAc,kBAAiB;QAClC,GAAG;QACH,MAAM,OAAO,OAAO,IAAI;QACxB,SAAS,OAAO,OAAO,OAAO;QAC9B,UAAS,YAAO,YAAP,mBAAgB,KAAK;QAC9B,SAAS,QAAQ,KAAK,GAAG;;KAE5B;AAED,UAAM,kBAAkB,cAAc,yBAAyB,mCAAS,IAAI;AAE5E,WAAO;MACL,MAAM,mBAAmB,CAAA;;MAEzB,OAAO,mCAAS;;EAEpB;EAEA,MAAM,uBAAoB;AACxB,UAAM,EAAE,kBAAiB,IAAK,kBAAkB;AAChD,QAAI,uDAAmB,QAAQ;AAC7B,YAAM,SAAS;QACb,GAAG,cAAc,kBAAiB;QAClC,MAAM;QACN,UAAS,uDAAmB,WAAU;QACtC,SAAS;;AAEX,YAAM,EAAE,KAAI,IAAK,MAAM,cAAc,aAAa,MAAM;AAExD,YAAM,aAAa,CAAC,GAAG,IAAI,EAAE,KAC3B,CAAC,GAAG,MAAM,kBAAkB,QAAQ,EAAE,EAAE,IAAI,kBAAkB,QAAQ,EAAE,EAAE,CAAC;AAG7E,YAAM,SAAS,WAAW,IAAI,CAAAE,OAAKA,GAAE,QAAQ,EAAE,OAAO,OAAO;AAC7D,YAAM,QAAQ,WAAY,OAAoB,IAAI,QAAM,cAAc,kBAAkB,EAAE,CAAC,CAAC;AAC5F,MAAAD,QAAM,WAAW;AACjB,MAAAA,QAAM,cAAc;IACtB;EACF;EAEA,MAAM,0BAAuB;AAC3B,QAAI;AACF,MAAAA,QAAM,+BAA+B;AACrC,YAAM,EAAE,kBAAkB,kBAAkB,kBAAiB,IAAK,kBAAkB;AACpF,YAAM,UAAU,CAAC,GAAI,oBAAoB,CAAA,GAAK,GAAI,qBAAqB,CAAA,CAAG,EAAE,OAAO,OAAO;AAC1F,YAAM,SAAS,gBAAgB,2BAA0B,EAAG,KAAK,GAAG;AACpE,YAAM,SAAS;QACb,MAAM;QACN,SAAS;QACT,SAAS;QACT;QACA;;AAEF,YAAM,EAAE,MAAM,MAAK,IAAK,MAAM,cAAc,aAAa,MAAM;AAC/D,YAAM,SAAS,YAAY,iBAAgB;AAC3C,YAAM,oBAAoB,KAAK,IAAI,CAAAC,OAAKA,GAAE,QAAQ,EAAE,OAAO,OAAO;AAClE,YAAM,eAAe,OAAO,IAAI,CAAAC,OAAKA,GAAE,QAAQ,EAAE,OAAO,OAAO;AAC/D,YAAM,QAAQ,WACX,CAAC,GAAG,mBAAmB,GAAG,YAAY,EAAe,IAAI,QACxD,cAAc,kBAAkB,EAAE,CAAC,CACpC;AAEH,MAAAF,QAAM,cAAc;AACpB,MAAAA,QAAM,iBAAiB;AACvB,MAAAA,QAAM,QAAQ,SAAS;IACzB,QAAQ;IAER;AACE,MAAAA,QAAM,+BAA+B;IACvC;EACF;EAEA,MAAM,mBAAmB,EAAE,KAAI,GAAsC;AACnE,UAAM,EAAE,kBAAkB,kBAAkB,kBAAiB,IAAK,kBAAkB;AACpF,UAAM,SAAS,gBAAgB,2BAA0B,EAAG,KAAK,GAAG;AACpE,UAAM,UAAU;MACd,GAAGA,QAAM,YAAY,IAAI,CAAC,EAAE,GAAE,MAAO,EAAE;MACvC,GAAI,oBAAoB,CAAA;MACxB,GAAI,qBAAqB,CAAA;MACzB,OAAO,OAAO;AAChB,UAAM,SAAS;MACb;MACA;MACA,SAAS;MACT;MACA;;AAEF,UAAM,EAAE,MAAM,MAAK,IAAK,MAAM,cAAc,aAAa,MAAM;AAC/D,UAAM,SAAS,KACZ,MAAM,GAAG,iBAAiB,EAC1B,IAAI,OAAK,EAAE,QAAQ,EACnB,OAAO,OAAO;AACjB,UAAM,QAAQ,WAAY,OAAoB,IAAI,QAAM,cAAc,kBAAkB,EAAE,CAAC,CAAC;AAE5F,IAAAA,QAAM,UAAU,eAAe,SAC7B,CAAC,GAAGA,QAAM,SAAS,GAAG,cAAc,qBAAqB,IAAI,CAAC,GAC9D,IAAI,EACJ,OAAO,OAAE;AA1Tf;AA0TkB,qBAAE,WAAF,mBAAU,KAAK,WAAS,OAAO,SAAS,KAAK;KAAE;AAE7D,IAAAA,QAAM,QAAQ,QAAQA,QAAM,QAAQ,QAAQA,QAAM;AAClD,IAAAA,QAAM,OAAO;EACf;EAEA,MAAM,0BAA0B,EAAE,IAAG,GAAqB;AACxD,UAAM,SAAS;MACb,MAAM;MACN,SAAS,IAAI;MACb,SAAS;;AAEX,UAAM,EAAE,KAAI,IAAK,MAAM,cAAc,aAAa,MAAM;AAExD,QAAI,MAAM;AACR,WAAK,QAAQ,YAAS;AACpB,QAAAA,QAAM,gBAAgB,KAAK,EAAE,MAAM,OAAO,MAAM,MAAM,OAAO,KAAI,CAAE;MACrE,CAAC;IACH;EACF;EAEA,MAAM,aAAa,EAAE,QAAQ,MAAK,GAAkD;AAClF,UAAM,EAAE,kBAAkB,iBAAgB,IAAK,kBAAkB;AACjE,UAAM,SAAS,gBAAgB,2BAA0B,EAAG,KAAK,GAAG;AACpE,IAAAA,QAAM,SAAS,CAAA;AAEf,UAAM,SAAS;MACb,MAAM;MACN,SAAS;MACT,QAAQ,iCAAQ;MAChB,YAAY;MACZ,SAAS;MACT,SAAS;MACT;;AAGF,UAAM,EAAE,KAAI,IAAK,MAAM,cAAc,aAAa,MAAM;AAExD,qBAAiB,UAAU;MACzB,MAAM;MACN,OAAO;MACP,YAAY,EAAE,OAAO,SAAS,IAAI,QAAQ,UAAU,GAAE;KACvD;AACD,UAAM,SAAS,KAAK,IAAI,OAAK,EAAE,QAAQ,EAAE,OAAO,OAAO;AACvD,UAAM,QAAQ,WAAW;MACvB,GAAI,OAAoB,IAAI,QAAM,cAAc,kBAAkB,EAAE,CAAC;MACrE,eAAe,KAAK,GAAG;KACxB;AACD,IAAAA,QAAM,SAAS,cAAc,qBAAqB,IAAI;EACxD;EAEA,YAAY,KAAa,SAA4B;AACnD,UAAM,kBAAkBA,QAAM,SAAS,GAAG;AAE1C,QAAI,iBAAiB;AACnB,aAAO;IACT;AAEA,WAAQA,QAAM,SAAS,GAAG,IAAI,QAAO;EACvC;EAEA,SAAS,EACP,uBAAuB,MACvB,uBAAuB,MACvB,0BAA0B,MAC1B,qBAAqB,KAAI,IACH,CAAA,GAAE;AACxB,UAAM,WAAW;MACf,wBACE,cAAc,YAAY,mBAAmB,cAAc,oBAAoB;MACjF,wBACE,cAAc,YAAY,mBAAmB,cAAc,oBAAoB;MACjF,2BACE,cAAc,YAAY,sBAAsB,cAAc,uBAAuB;MACvF,sBACE,cAAc,YAAY,iBAAiB,cAAc,kBAAkB;MAC7E,OAAO,OAAO;AAEhB,WAAO,QAAQ,WAAW,QAAQ;EACpC;EAEA,0BAAuB;AA3YzB;AA4YI,SAAI,uBAAkB,MAAM,aAAxB,mBAAkC,WAAW;AAC/C,oBAAc,qBAAoB;IACpC;EACF;EAEA,MAAM,uBAAoB;AACxB,QAAI;AACF,YAAM,EAAE,mBAAkB,IAAK,MAAMD,KAAI,IAAmC;QAC1E,MAAM;QACN,QAAQ,cAAc,kBAAiB;OACxC;AACD,wBAAkB,YAAY,EAAE,WAAW,mBAAkB,CAAE;IACjE,SAAS,OAAO;AACd,wBAAkB,YAAY,EAAE,WAAW,MAAK,CAAE;IACpD;EACF;EAEA,mBAAmB,YAAwC;AACzD,QAAI,EAAC,yCAAY,SAAQ;AACvB,MAAAC,QAAM,WAAWA,QAAM;AACvB,MAAAA,QAAM,cAAcA,QAAM;AAE1B;IACF;AAEA,UAAM,iBAAiB,gBAAgB,2BAA0B,EAAG,KAAK,GAAG;AAE5E,IAAAA,QAAM,WAAWA,QAAM,YAAY,OAAO,YAAO;AAvarD;AAwaM,0BAAO,WAAP,mBAAe,KAAK,WAAS,eAAe,SAAS,KAAK;KAAE;AAG9D,IAAAA,QAAM,cAAcA,QAAM,eAAe,OAAO,YAAO;AA3a3D;AA4aM,0BAAO,WAAP,mBAAe,KAAK,WAAS,eAAe,SAAS,KAAK;KAAE;AAG9D,IAAAA,QAAM,kBAAkBA,QAAM,QAAQ,OAAO,YAAO;AA/axD;AAgbM,0BAAO,WAAP,mBAAe,KAAK,WAAS,eAAe,SAAS,KAAK;KAAE;EAEhE;EAEA,0BAAuB;AACrB,IAAAA,QAAM,kBAAkB,CAAA;EAC1B;EAEA,qBAAqB,WAAqC;AACxD,QAAI,CAAC,WAAW;AACd,MAAAA,QAAM,WAAWA,QAAM;AACvB,MAAAA,QAAM,cAAcA,QAAM;AAE1B;IACF;AAEA,UAAM,iBAAiB,gBAAgB,2BAA0B,EAAG,KAAK,GAAG;AAE5E,IAAAA,QAAM,WAAWA,QAAM,YAAY,OAAO,YAAO;AAlcrD;AAmcM,0BAAO,WAAP,mBAAe,KAAK,WAAS,eAAe,SAAS,KAAK;KAAE;AAG9D,IAAAA,QAAM,cAAcA,QAAM,eAAe,OAAO,YAAO;AAtc3D;AAucM,0BAAO,WAAP,mBAAe,KAAK,WAAS,eAAe,SAAS,KAAK;KAAE;AAG9D,IAAAA,QAAM,kBAAkBA,QAAM,QAAQ,OAAO,YAAO;AA1cxD;AA2cM,0BAAO,WAAP,mBAAe,KAAK,WAAS,eAAe,SAAS,KAAK;KAAE;EAEhE;;A;;;;;;;;;;;;;;;;ACvaK,IAAM,kBAAkB;EAC7B,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;;AAKH,SAAU,kBAAkB,aAAyC;AACzE,QAAM,eAAe;IACnB,GAAG;IACH,WAAW,YAAY,YAAY,YAAY,YAAY;IAC3D,aAAa,YAAY,cACrB,OAAO,YAAY,WAAW,IAC9B;IACJ,SAAS,YAAY,UAAU,YAAY,YAAY,OAAO,IAAI;IAClE,KAAK,YAAY,MAAM,OAAO,YAAY,GAAG,IAAI;IACjD,UAAU,YAAY,WAAW,OAAO,YAAY,QAAQ,IAAI;IAChE,kBAAkB,YAAY,mBAC1B,OAAO,YAAY,gBAAgB,IACnC;IACJ,cAAc,YAAY,eACtB,OAAO,YAAY,YAAY,IAC/B;IACJ,sBAAsB,YAAY,uBAC9B,OAAO,YAAY,oBAAoB,IACvC;IACJ,OAAO,YAAY,QAAQ,YAAY,YAAY,KAAK,IAAI;IAC5D,IAAI,YAAY,KAAK,YAAY,KAAK;IACtC,kBAAkB,YAAY,mBAC1B,OAAO,YAAY,gBAAgB,IACnC;IACJ,MAAM,YAAY,OACb,gBAAwB,YAAY,IAAI,IACzC;IACJ,SAAS,YAAY,OAAO,YAAY,OAAO;IAC/C,OAAO,YAAY,QAAQ,OAAO,YAAY,KAAK,IAAI;IACvD,GAAG,YAAY,IAAI,OAAO,YAAY,CAAC,IAAI;;AAG7C,MAAI,YAAY;AACd,iBAAa,oBAAoB,wBAC/B,YAAY,iBAAiB;AAGjC,eAAa,WAAW,MAAK;AAE3B,QAAI,YAAY;AAAS,aAAO,OAAO,YAAY,OAAO;AAG1D,QAAI,OAAO,aAAa,MAAM,UAAU;AACtC,UAAI,aAAa,MAAM,MAAM,aAAa,MAAM;AAAK,eAAO;AAC5D,UAAI,aAAa,MAAM,MAAM,aAAa,MAAM;AAAK,eAAO;AAC5D,UAAI,aAAa,KAAK;AAAK,eAAO,aAAa,IAAI,OAAO,KAAK,IAAI;IACrE;AAEA,WAAO;EACT,GAAE;AAEF,MAAI,aAAa,SAAS,UAAU;AAClC,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;EACtB;AACA,MAAI,aAAa,SAAS,WAAW;AACnC,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;EACtB;AACA,MAAI,aAAa,SAAS,WAAW;AACnC,WAAO,aAAa;EACtB;AACA,SAAO;AACT;AAIO,IAAM,oBAAkC,gBAC7C,eACA,iBAAiB;AAKnB,SAAS,wBACP,mBAAuC;AAEvC,SAAO,kBAAkB,IAAI,CAAC,mBAAmB;IAC/C,iBAAkB,cAAsB;IACxC,SAAS,OAAO,cAAc,OAAO;IACrC,OAAO,OAAO,cAAc,KAAK;IACjC,GAAG,cAAc;IACjB,GAAG,cAAc;IACjB,SAAS,OAAO,cAAc,OAAO;IACrC;AACJ;;;AC/FM,SAAU,YAAY,OAA6B;AACvD,QAAM,gBAAgB,MAAM,gBAAgB,CAAA,GAAI,IAAI,CAAC,gBAAe;AAClE,QAAI,OAAO,gBAAgB;AAAU,aAAO;AAC5C,WAAO,kBAAkB,WAAW;EACtC,CAAC;AACD,SAAO;IACL,GAAG;IACH,eAAe,MAAM,gBAAgB,OAAO,MAAM,aAAa,IAAI;IACnE,aAAa,MAAM,cAAc,OAAO,MAAM,WAAW,IAAI;IAC7D,YAAY,MAAM,aAAa,OAAO,MAAM,UAAU,IAAI;IAC1D,eAAe,MAAM,gBACjB,OAAO,MAAM,aAAa,IAC1B;IACJ,UAAU,MAAM,WAAW,OAAO,MAAM,QAAQ,IAAI;IACpD,SAAS,MAAM,UAAU,OAAO,MAAM,OAAO,IAAI;IACjD,MAAM,MAAM,OAAO,MAAM,OAAO;IAChC,WAAW,MAAM,YAAY,MAAM,YAAY;IAC/C,OAAO,MAAM,QAAQ,MAAM,QAAQ;IACnC,QAAQ,MAAM,SAAS,OAAO,MAAM,MAAM,IAAI;IAC9C,MAAM,MAAM,OAAO,OAAO,MAAM,IAAI,IAAI;IACxC,WAAW,MAAM,YAAY,OAAO,MAAM,SAAS,IAAI;IACvD;IACA,iBAAiB,MAAM,kBACnB,OAAO,MAAM,eAAe,IAC5B;;AAER;AAIO,IAAM,cAA4B,gBAAgB,SAAS,WAAW;;;ACR7E,eAAsB,oBAIpB,QACA,EAAE,SAAS,WAAW,UAAU,YAAW,GAAiC;AAE5E,QAAM,QAAQ,MAAM,OAAO,QACzB;IACE,QAAQ;IACR,QAAQ,CAAC,SAAS,cAAc,YAAY,WAAW,IAAI,QAAQ;KAErE,EAAE,QAAQ,QAAQ,WAAW,EAAC,CAAE;AAElC,SAAO,YAAY,KAAK;AAC1B;;;AC5EA,IAAM,sBAAsB;AAGrB,IAAM,uBAAuB;AAG7B,IAAM,uBAAuB;AAG7B,IAAM,eAAe,uBAAuB;AAG5C,IAAM,yBACX,eAAe;AAEf;AAEA,IAAI,uBAAuB;;;ACbvB,SAAU,UACd,KACA,EACE,MACA,UAAS,IACyD,CAAA,GAAE;AAEtE,SAAO;IACL,GAAG;IACH,WAAW,IAAI,YAAY,IAAI,YAAY;IAC3C,aAAa,IAAI,cAAc,OAAO,IAAI,WAAW,IAAI;IACzD,UAAU,IAAI,WAAW,OAAO,IAAI,QAAQ,IAAI;IAChD,iBAAiB,IAAI,kBAAkB,IAAI,kBAAkB;IAC7D,kBAAkB,IAAI,mBAClB,OAAO,IAAI,gBAAgB,IAC3B;IACJ,GAAI,YAAY,EAAE,MAAM,UAAS,IAAK,CAAA;;AAE1C;;;ACoCA,IAAM,0BAA0B,IAAI,OAAgB,GAAG;;;AC1DhD,IAAM,eAA6B,IAAI,OAAqB,IAAI;;;ACHvE,SAAS,gBAAa;AACpB,SAAO;IACL,SAAS;IACT,OAAI;AACF,aAAO,KAAK;IACd;IACA,QAAK;AACH,WAAK,UAAU;IACjB;;AAEJ;AAEO,IAAM,UAAwB,cAAa;;;ACU3C,IAAM,kBAAkB;EAC7B,OAAO;EACP,OAAO;;AAKH,SAAU,yBACd,oBAAuD;AAEvD,QAAM,UAAU;IACd,GAAG;IACH,aAAa,mBAAmB,cAC5B,OAAO,mBAAmB,WAAW,IACrC;IACJ,iBAAiB,mBAAmB,kBAChC,mBAAmB,kBACnB;IACJ,mBAAmB,mBAAmB,oBAClC,OAAO,mBAAmB,iBAAiB,IAC3C;IACJ,mBAAmB,mBAAmB,oBAClC,OAAO,mBAAmB,iBAAiB,IAC3C;IACJ,SAAS,mBAAmB,UACxB,OAAO,mBAAmB,OAAO,IACjC;IACJ,MAAM,mBAAmB,OACrB,mBAAmB,KAAK,IAAI,CAAC,QAAQ,UAAU,GAAG,CAAC,IACnD;IACJ,IAAI,mBAAmB,KAAK,mBAAmB,KAAK;IACpD,kBAAkB,mBAAmB,mBACjC,YAAY,mBAAmB,gBAAgB,IAC/C;IACJ,QAAQ,mBAAmB,SACvB,gBAAgB,mBAAmB,MAAM,IACzC;IACJ,MAAM,mBAAmB,OACrB,gBACE,mBAAmB,IAAoC,KACpD,mBAAmB,OACxB;;AAGN,MAAI,mBAAmB;AACrB,YAAQ,eAAe,OAAO,mBAAmB,YAAY;AAC/D,MAAI,mBAAmB;AACrB,YAAQ,cAAc,OAAO,mBAAmB,WAAW;AAE7D,SAAO;AACT;AAMO,IAAM,2BAAyC,gBACpD,sBACA,wBAAwB;;;ACvE1B,IAAM,MAAsB,IAAI,WAAW,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC;AACjG,IAAM,KAAqB,IAAI,WAAW,IAAI,MAAM,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAGG,OAAMA,EAAC,CAAC;AAChF,IAAM,KAAqB,GAAG,IAAI,CAACA,QAAO,IAAIA,KAAI,KAAK,EAAE;AACzD,IAAI,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,CAAC,EAAE;AACd,SAASA,KAAI,GAAGA,KAAI,GAAGA;AAAK,WAAS,KAAK,CAAC,MAAM,IAAI;AAAG,MAAE,KAAK,EAAEA,EAAC,EAAE,IAAI,CAACC,OAAM,IAAIA,EAAC,CAAC,CAAC;AAEtF,IAAM,SAAyB;EAC7B,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,IAAI,CAACD,OAAM,IAAI,WAAWA,EAAC,CAAC;AAC9B,IAAM,UAA0B,KAAK,IAAI,CAAC,KAAKA,OAAM,IAAI,IAAI,CAAC,MAAM,OAAOA,EAAC,EAAE,CAAC,CAAC,CAAC;AACjF,IAAM,UAA0B,KAAK,IAAI,CAAC,KAAKA,OAAM,IAAI,IAAI,CAAC,MAAM,OAAOA,EAAC,EAAE,CAAC,CAAC,CAAC;AACjF,IAAM,KAAqB,IAAI,YAAY;EACzC;EAAY;EAAY;EAAY;EAAY;CACjD;AACD,IAAM,KAAqB,IAAI,YAAY;EACzC;EAAY;EAAY;EAAY;EAAY;CACjD;AAED,SAASE,GAAE,OAAe,GAAWC,IAAW,GAAS;AACvD,MAAI,UAAU;AAAG,WAAO,IAAIA,KAAI;WACvB,UAAU;AAAG,WAAQ,IAAIA,KAAM,CAAC,IAAI;WACpC,UAAU;AAAG,YAAQ,IAAI,CAACA,MAAK;WAC/B,UAAU;AAAG,WAAQ,IAAI,IAAMA,KAAI,CAAC;;AACxC,WAAO,KAAKA,KAAI,CAAC;AACxB;AAEA,IAAM,QAAwB,IAAI,YAAY,EAAE;AAC1C,IAAO,YAAP,cAAyB,OAAiB;EAO9C,cAAA;AACE,UAAM,IAAI,IAAI,GAAG,IAAI;AAPf,SAAA,KAAK,aAAa;AAClB,SAAA,KAAK,aAAa;AAClB,SAAA,KAAK,aAAa;AAClB,SAAA,KAAK,YAAa;AAClB,SAAA,KAAK,aAAa;EAI1B;EACU,MAAG;AACX,UAAM,EAAE,IAAI,IAAI,IAAAC,KAAI,IAAAC,KAAI,GAAE,IAAK;AAC/B,WAAO,CAAC,IAAI,IAAID,KAAIC,KAAI,EAAE;EAC5B;EACU,IAAI,IAAY,IAAYD,KAAYC,KAAY,IAAU;AACtE,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAKD,MAAK;AACf,SAAK,KAAKC,MAAK;AACf,SAAK,KAAK,KAAK;EACjB;EACU,QAAQ,MAAgB,QAAc;AAC9C,aAASL,KAAI,GAAGA,KAAI,IAAIA,MAAK,UAAU;AAAG,YAAMA,EAAC,IAAI,KAAK,UAAU,QAAQ,IAAI;AAEhF,QAAI,KAAK,KAAK,KAAK,GAAGM,MAAK,IACvB,KAAK,KAAK,KAAK,GAAGC,MAAK,IACvB,KAAK,KAAK,KAAK,GAAGC,MAAK,IACvB,KAAK,KAAK,KAAK,GAAGC,MAAK,IACvB,KAAK,KAAK,KAAK,GAAGC,MAAK;AAI3B,aAAS,QAAQ,GAAG,QAAQ,GAAG,SAAS;AACtC,YAAM,SAAS,IAAI;AACnB,YAAM,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK;AACrC,YAAM,KAAK,KAAK,KAAK,GAAGC,MAAK,KAAK,KAAK;AACvC,YAAM,KAAK,QAAQ,KAAK,GAAGC,MAAK,QAAQ,KAAK;AAC7C,eAASZ,KAAI,GAAGA,KAAI,IAAIA,MAAK;AAC3B,cAAM,KAAM,KAAK,KAAKE,GAAE,OAAO,IAAI,IAAI,EAAE,IAAI,MAAM,GAAGF,EAAC,CAAC,IAAI,KAAK,GAAGA,EAAC,CAAC,IAAI,KAAM;AAChF,aAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,EAAE,IAAI,GAAG,KAAK,IAAI,KAAK;MACzD;AAEA,eAASA,KAAI,GAAGA,KAAI,IAAIA,MAAK;AAC3B,cAAMa,MAAM,KAAKP,MAAKJ,GAAE,QAAQK,KAAIC,KAAIC,GAAE,IAAI,MAAME,IAAGX,EAAC,CAAC,IAAI,KAAKY,IAAGZ,EAAC,CAAC,IAAIU,MAAM;AACjF,QAAAJ,MAAKI,KAAIA,MAAKD,KAAIA,MAAK,KAAKD,KAAI,EAAE,IAAI,GAAGA,MAAKD,KAAIA,MAAKM;MACzD;IACF;AAEA,SAAK,IACF,KAAK,KAAK,KAAKJ,MAAM,GACrB,KAAK,KAAK,KAAKC,MAAM,GACrB,KAAK,KAAK,KAAKJ,MAAM,GACrB,KAAK,KAAK,KAAKC,MAAM,GACrB,KAAK,KAAK,KAAKC,MAAM,CAAC;EAE3B;EACU,aAAU;AAClB,UAAM,KAAK,CAAC;EACd;EACA,UAAO;AACL,SAAK,YAAY;AACjB,SAAK,OAAO,KAAK,CAAC;AAClB,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;EACxB;;AAIK,IAAM,YAAmC,gBAAgB,MAAM,IAAI,UAAS,CAAE;;;ACnE/E,SAAU,mBACd,YAAwC;AAExC,QAAM,EAAE,OAAM,IAAK;AAEnB,QAAM,WAAW,oBAAI,IAAG;AACxB,QAAM,WAAW,IAAI,OAAe,IAAI;AACxC,QAAM,aAAa,oBAAI,IAAG;AAE1B,QAAM,SAAS,CAAC,EAAE,SAAS,QAAO,MAChC,GAAG,OAAO,IAAI,OAAO;AAEvB,SAAO;IACL,MAAM,QAAQ,EAAE,SAAS,SAAS,OAAM,GAAE;AACxC,YAAM,MAAM,OAAO,EAAE,SAAS,QAAO,CAAE;AACvC,YAAM,UAAU,KAAK,IAAI,EAAE,SAAS,SAAS,OAAM,CAAE;AAErD,WAAK,UAAU,EAAE,SAAS,QAAO,CAAE;AACnC,YAAM,QAAQ,MAAM;AAEpB,YAAM,OAAO,IAAI,EAAE,SAAS,QAAO,GAAI,KAAK;AAC5C,eAAS,IAAI,KAAK,KAAK;AAEvB,aAAO;IACT;IACA,MAAM,UAAU,EAAE,SAAS,QAAO,GAAE;AAClC,YAAM,MAAM,OAAO,EAAE,SAAS,QAAO,CAAE;AACvC,YAAM,QAAQ,SAAS,IAAI,GAAG,KAAK;AACnC,eAAS,IAAI,KAAK,QAAQ,CAAC;IAC7B;IACA,MAAM,IAAI,EAAE,SAAS,SAAS,OAAM,GAAE;AACpC,YAAM,MAAM,OAAO,EAAE,SAAS,QAAO,CAAE;AAEvC,UAAI,UAAU,WAAW,IAAI,GAAG;AAChC,UAAI,CAAC,SAAS;AACZ,mBAAW,YAAW;AACpB,cAAI;AACF,kBAAM,QAAQ,MAAM,OAAO,IAAI,EAAE,SAAS,SAAS,OAAM,CAAE;AAC3D,kBAAM,gBAAgB,SAAS,IAAI,GAAG,KAAK;AAC3C,gBAAI,gBAAgB,KAAK,SAAS;AAChC,qBAAO,gBAAgB;AACzB,qBAAS,OAAO,GAAG;AACnB,mBAAO;UACT;AACE,iBAAK,MAAM,EAAE,SAAS,QAAO,CAAE;UACjC;QACF,GAAE;AACF,mBAAW,IAAI,KAAK,OAAO;MAC7B;AAEA,YAAM,QAAQ,SAAS,IAAI,GAAG,KAAK;AACnC,aAAO,QAAS,MAAM;IACxB;IACA,MAAM,EAAE,SAAS,QAAO,GAAE;AACxB,YAAM,MAAM,OAAO,EAAE,SAAS,QAAO,CAAE;AACvC,eAAS,OAAO,GAAG;AACnB,iBAAW,OAAO,GAAG;IACvB;;AAEJ;AAaM,SAAU,UAAO;AACrB,SAAO;IACL,MAAM,IAAI,YAAU;AAClB,YAAM,EAAE,SAAS,OAAM,IAAK;AAC5B,aAAO,oBAAoB,QAAQ;QACjC;QACA,UAAU;OACX;IACH;IACA,MAAG;IAAI;;AAEX;AAMO,IAAM,eAA6B,mBAAmB;EAC3D,QAAQ,QAAO;CAChB;;;ACnIM,IAAM,UAAU;;;ACOjB,SAAUM,cAAU;AACxB,SAAO;AACT;;;ACKM,IAAOC,aAAP,MAAO,mBAEH,MAAK;EAWb,YAAY,cAAsB,UAAoC,CAAA,GAAE;AACtE,UAAM,WAAW,MAAK;AA7B1B;AA8BM,UAAI,QAAQ,iBAAiB,YAAW;AACtC,YAAI,QAAQ,MAAM;AAAS,iBAAO,QAAQ,MAAM;AAChD,YAAI,QAAQ,MAAM;AAAc,iBAAO,QAAQ,MAAM;MACvD;AACA,WAAI,aAAQ,UAAR,mBAAe;AAAS,eAAO,QAAQ,MAAM;AACjD,aAAO,QAAQ;IACjB,GAAE;AACF,UAAM,YAAY,MAAK;AACrB,UAAI,QAAQ,iBAAiB;AAC3B,eAAO,QAAQ,MAAM,YAAY,QAAQ;AAC3C,aAAO,QAAQ;IACjB,GAAE;AAEF,UAAM,cAAc;AACpB,UAAM,OAAO,GAAG,WAAW,GAAG,YAAY,EAAE;AAE5C,UAAM,UAAU;MACd,gBAAgB;MAChB,GAAI,QAAQ,eAAe,CAAC,IAAI,GAAG,QAAQ,YAAY,IAAI,CAAA;MAC3D,GAAI,WAAW,WACX;QACE;QACA,UAAU,YAAY,OAAO,KAAK;QAClC,WAAW,QAAQ,IAAI,KAAK;UAE9B,CAAA;MAEH,OAAO,CAAC,MAAM,OAAO,MAAM,QAAQ,EACnC,KAAK,IAAI;AAEZ,UAAM,SAAS,QAAQ,QAAQ,EAAE,OAAO,QAAQ,MAAK,IAAK,MAAS;AA1CrE,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,QAAA;;;;;;AACA,WAAA,eAAA,MAAA,YAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AAES,WAAA,eAAA,MAAA,SAAA;;;;;;AACA,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,WAAA;;;;aAAU,MAAMC,YAAU,CAAE;;AAoC1B,SAAK,QAAQ,QAAQ;AACrB,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,eAAe;EACtB;EAIA,KAAKC,KAAQ;AACX,WAAO,KAAK,MAAMA,GAAE;EACtB;;AAaF,SAAS,KACP,KACAA,KAA4C;AAE5C,MAAIA,OAAA,gBAAAA,IAAK;AAAM,WAAO;AACtB,MAAI,OAAO,OAAO,QAAQ,YAAY,WAAW,OAAO,IAAI;AAC1D,WAAO,KAAK,IAAI,OAAOA,GAAE;AAC3B,SAAOA,MAAK,OAAO;AACrB;;;AC1FM,SAAU,WAAW,OAAoB,OAAa;AAC1D,MAAUC,MAAK,KAAK,IAAI;AACtB,UAAM,IAAU,kBAAkB;MAChC,WAAiBA,MAAK,KAAK;MAC3B,SAAS;KACV;AACL;AA0DO,IAAM,cAAc;EACzB,MAAM;EACN,MAAM;EACN,GAAG;EACH,GAAG;EACH,GAAG;EACH,GAAG;;AAIC,SAAU,iBAAiB,MAAY;AAC3C,MAAI,QAAQ,YAAY,QAAQ,QAAQ,YAAY;AAClD,WAAO,OAAO,YAAY;AAC5B,MAAI,QAAQ,YAAY,KAAK,QAAQ,YAAY;AAC/C,WAAO,QAAQ,YAAY,IAAI;AACjC,MAAI,QAAQ,YAAY,KAAK,QAAQ,YAAY;AAC/C,WAAO,QAAQ,YAAY,IAAI;AACjC,SAAO;AACT;AAGM,SAAUC,KAAI,OAAoB,UAAuB,CAAA,GAAE;AAC/D,QAAM,EAAE,KAAK,MAAAC,QAAO,GAAE,IAAK;AAC3B,MAAIA,UAAS;AAAG,WAAO;AACvB,MAAI,MAAM,SAASA;AACjB,UAAM,IAAU,4BAA4B;MAC1C,MAAM,MAAM;MACZ,YAAYA;MACZ,MAAM;KACP;AACH,QAAM,cAAc,IAAI,WAAWA,KAAI;AACvC,WAASC,KAAI,GAAGA,KAAID,OAAMC,MAAK;AAC7B,UAAM,SAAS,QAAQ;AACvB,gBAAY,SAASA,KAAID,QAAOC,KAAI,CAAC,IACnC,MAAM,SAASA,KAAI,MAAM,SAASA,KAAI,CAAC;EAC3C;AACA,SAAO;AACT;;;ACrGM,SAAUC,YAAW,KAAc,OAAa;AACpD,MAAQC,MAAK,GAAG,IAAI;AAClB,UAAM,IAAQC,mBAAkB;MAC9B,WAAeD,MAAK,GAAG;MACvB,SAAS;KACV;AACL;AAsDM,SAAUE,KAAI,MAAe,UAAuB,CAAA,GAAE;AAC1D,QAAM,EAAE,KAAK,MAAAC,QAAO,GAAE,IAAK;AAE3B,MAAIA,UAAS;AAAG,WAAO;AAEvB,QAAM,MAAM,KAAK,QAAQ,MAAM,EAAE;AACjC,MAAI,IAAI,SAASA,QAAO;AACtB,UAAM,IAAQC,6BAA4B;MACxC,MAAM,KAAK,KAAK,IAAI,SAAS,CAAC;MAC9B,YAAYD;MACZ,MAAM;KACP;AAEH,SAAO,KAAK,IAAI,QAAQ,UAAU,WAAW,UAAU,EAAEA,QAAO,GAAG,GAAG,CAAC;AACzE;;;ACvEA,IAAM,UAAwB,IAAI,YAAW;AAC7C,IAAM,UAAwB,IAAI,YAAW;AAmGvC,SAAU,KAAK,OAA0C;AAC7D,MAAI,iBAAiB;AAAY,WAAO;AACxC,MAAI,OAAO,UAAU;AAAU,WAAOE,SAAQ,KAAK;AACnD,SAAO,UAAU,KAAK;AACxB;AAuBM,SAAU,UAAU,OAAqC;AAC7D,SAAO,iBAAiB,aAAa,QAAQ,IAAI,WAAW,KAAK;AACnE;AA2EM,SAAUC,SAAQ,OAAgB,UAA2B,CAAA,GAAE;AACnE,QAAM,EAAE,MAAAC,MAAI,IAAK;AAEjB,MAAI,MAAM;AACV,MAAIA,OAAM;AACR,IAAaC,YAAW,OAAOD,KAAI;AACnC,UAAU,SAAS,OAAOA,KAAI;EAChC;AAEA,MAAI,YAAY,IAAI,MAAM,CAAC;AAC3B,MAAI,UAAU,SAAS;AAAG,gBAAY,IAAI,SAAS;AAEnD,QAAM,SAAS,UAAU,SAAS;AAClC,QAAM,QAAQ,IAAI,WAAW,MAAM;AACnC,WAAS,QAAQ,GAAG,IAAI,GAAG,QAAQ,QAAQ,SAAS;AAClD,UAAM,aAAsB,iBAAiB,UAAU,WAAW,GAAG,CAAC;AACtE,UAAM,cAAuB,iBAAiB,UAAU,WAAW,GAAG,CAAC;AACvE,QAAI,eAAe,UAAa,gBAAgB,QAAW;AACzD,YAAM,IAAWE,WACf,2BAA2B,UAAU,IAAI,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,CAAC,SAAS,SAAS,KAAK;IAEzF;AACA,UAAM,KAAK,IAAI,aAAa,KAAK;EACnC;AACA,SAAO;AACT;AA6EM,SAAU,WACd,OACA,UAA8B,CAAA,GAAE;AAEhC,QAAM,EAAE,MAAAC,MAAI,IAAK;AAEjB,QAAM,QAAQ,QAAQ,OAAO,KAAK;AAClC,MAAI,OAAOA,UAAS,UAAU;AAC5B,IAAS,WAAW,OAAOA,KAAI;AAC/B,WAAOC,UAAS,OAAOD,KAAI;EAC7B;AACA,SAAO;AACT;AAkFM,SAAUE,UACd,OACAC,OAAyB;AAEzB,SAAgBC,KAAI,OAAO,EAAE,KAAK,SAAS,MAAAD,MAAI,CAAE;AACnD;AA2CM,SAAUE,MAAK,OAAY;AAC/B,SAAO,MAAM;AACf;AA0WM,IAAO,oBAAP,cAAwCC,WAAS;EAGrD,YAAY,EAAE,WAAW,QAAO,GAA0C;AACxE,UACE,wBAAwB,OAAO,2BAA2B,SAAS,WAAW;AAJhE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAyCI,IAAO,8BAAP,cAAkDC,WAAS;EAG/D,YAAY,EACV,MAAAC,OACA,YACA,KAAI,GAKL;AACC,UACE,GAAG,KAAK,OAAO,CAAC,EAAE,YAAW,CAAE,GAAG,KAC/B,MAAM,CAAC,EACP,YAAW,CAAE,YAAYA,KAAI,+BAA+B,UAAU,MAAM;AAdjE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAgBzB;;;;AC72BF,IAAMC,WAAwB,IAAI,YAAW;AAE7C,IAAM,QAAsB,MAAM,KAAK,EAAE,QAAQ,IAAG,GAAI,CAAC,IAAIC,OAC3DA,GAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AA6D3B,SAAUC,WAAU,QAAsB;AAC9C,SAAO,KAAM,OAAiB,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,QAAQ,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AAuEM,SAAU,YACd,OACA,UAA+B,CAAA,GAAE;AAEjC,QAAM,MAAW,KAAK,OAAO,KAAK,CAAC;AACnC,MAAI,OAAO,QAAQ,SAAS,UAAU;AACpC,IAASC,YAAW,KAAK,QAAQ,IAAI;AACrC,WAAO,QAAQ,KAAK,QAAQ,IAAI;EAClC;AACA,SAAO;AACT;AA6BM,SAAUC,WACd,OACA,UAA6B,CAAA,GAAE;AAE/B,MAAI,SAAS;AACb,WAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA;AAAK,cAAU,MAAM,MAAMA,EAAC,CAAE;AAChE,QAAM,MAAM,KAAK,MAAM;AAEvB,MAAI,OAAO,QAAQ,SAAS,UAAU;AACpC,IAASF,YAAW,KAAK,QAAQ,IAAI;AACrC,WAAO,SAAS,KAAK,QAAQ,IAAI;EACnC;AACA,SAAO;AACT;AAgCM,SAAU,WACd,OACA,UAA8B,CAAA,GAAE;AAEhC,QAAM,EAAE,QAAQ,MAAAG,MAAI,IAAK;AAEzB,QAAM,SAAS,OAAO,KAAK;AAE3B,MAAI;AACJ,MAAIA,OAAM;AACR,QAAI;AAAQ,kBAAY,MAAO,OAAOA,KAAI,IAAI,KAAK,MAAO;;AACrD,iBAAW,OAAO,OAAOA,KAAI,IAAI,MAAM;EAC9C,WAAW,OAAO,UAAU,UAAU;AACpC,eAAW,OAAO,OAAO,gBAAgB;EAC3C;AAEA,QAAM,WAAW,OAAO,aAAa,YAAY,SAAS,CAAC,WAAW,KAAK;AAE3E,MAAK,YAAY,SAAS,YAAa,SAAS,UAAU;AACxD,UAAM,SAAS,OAAO,UAAU,WAAW,MAAM;AACjD,UAAM,IAAI,uBAAuB;MAC/B,KAAK,WAAW,GAAG,QAAQ,GAAG,MAAM,KAAK;MACzC,KAAK,GAAG,QAAQ,GAAG,MAAM;MACzB;MACA,MAAAA;MACA,OAAO,GAAG,KAAK,GAAG,MAAM;KACzB;EACH;AAEA,QAAM,eACJ,UAAU,SAAS,KAAK,MAAM,OAAOA,QAAO,CAAC,KAAK,OAAO,MAAM,IAAI,QACnE,SAAS,EAAE;AAEb,QAAM,MAAM,KAAK,WAAW;AAC5B,MAAIA;AAAM,WAAO,QAAQ,KAAKA,KAAI;AAClC,SAAO;AACT;AAuCM,SAAUC,YACd,OACA,UAA8B,CAAA,GAAE;AAEhC,SAAOH,WAAUI,SAAQ,OAAO,KAAK,GAAG,OAAO;AACjD;AAoDM,SAAU,QACd,OACAC,OAAyB;AAEzB,SAAgBC,KAAI,OAAO,EAAE,KAAK,QAAQ,MAAAD,MAAI,CAAE;AAClD;AAsBM,SAAU,SACd,OACAA,OAAyB;AAEzB,SAAgBC,KAAI,OAAO,EAAE,KAAK,SAAS,MAAAD,MAAI,CAAE;AACnD;AAsFM,SAAUE,MAAK,OAAU;AAC7B,SAAO,KAAK,MAAM,MAAM,SAAS,KAAK,CAAC;AACzC;AAoSM,IAAO,yBAAP,cAA6CC,WAAS;EAG1D,YAAY,EACV,KACA,KACA,QACA,MAAAC,OACA,MAAK,GAON;AACC,UACE,YAAY,KAAK,oBACfA,QAAO,IAAIA,QAAO,CAAC,SAAS,EAC9B,GAAG,SAAS,YAAY,WAAW,kBAAkB,MAAM,MAAM,GAAG,WAAW,GAAG,QAAQ,YAAY,GAAG,KAAK,EAAE;AAlBlG,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAoBzB;;AA8GI,IAAOC,qBAAP,cAAwCC,WAAS;EAGrD,YAAY,EAAE,WAAW,QAAO,GAA0C;AACxE,UACE,wBAAwB,OAAO,2BAA2B,SAAS,WAAW;AAJhE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAyCI,IAAOC,+BAAP,cAAkDC,WAAS;EAG/D,YAAY,EACV,MAAAC,OACA,YACA,KAAI,GAKL;AACC,UACE,GAAG,KAAK,OAAO,CAAC,EAAE,YAAW,CAAE,GAAG,KAC/B,MAAM,CAAC,EACP,YAAW,CAAE,YAAYA,KAAI,+BAA+B,UAAU,MAAM;AAdjE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAgBzB;;;;AC55BI,SAAUC,WAMd,OACA,UAAiC,CAAA,GAAE;AAEnC,QAAM,EAAE,KAAK,OAAO,UAAU,WAAW,QAAQ,QAAO,IAAK;AAC7D,QAAM,QAAQ,WAAsB,KAAK,KAAK,CAAC;AAC/C,MAAI,OAAO;AAAS,WAAO;AAC3B,SAAWC,WAAU,KAAK;AAC5B;;;ACnDM,IAAOC,UAAP,cAAuC,IAAkB;EAG7D,YAAYC,OAAY;AACtB,UAAK;AAHP,WAAA,eAAA,MAAA,WAAA;;;;;;AAIE,SAAK,UAAUA;EACjB;EAES,IAAI,KAAW;AACtB,UAAM,QAAQ,MAAM,IAAI,GAAG;AAE3B,QAAI,MAAM,IAAI,GAAG,KAAK,UAAU,QAAW;AACzC,WAAK,OAAO,GAAG;AACf,YAAM,IAAI,KAAK,KAAK;IACtB;AAEA,WAAO;EACT;EAES,IAAI,KAAa,OAAY;AACpC,UAAM,IAAI,KAAK,KAAK;AACpB,QAAI,KAAK,WAAW,KAAK,OAAO,KAAK,SAAS;AAC5C,YAAM,WAAW,KAAK,KAAI,EAAG,KAAI,EAAG;AACpC,UAAI;AAAU,aAAK,OAAO,QAAQ;IACpC;AACA,WAAO;EACT;;;;AC7BF,IAAM,SAAS;EACb,UAAwB,IAAIC,QAAwB,IAAI;;AAGnD,IAAM,WAAW,OAAO;;;ACA/B,IAAM,eAA6B;AA0B7B,SAAU,OACd,OACA,UAA0B,CAAA,GAAE;AAE5B,QAAM,EAAE,SAAS,KAAI,IAAK;AAE1B,MAAI,CAAC,aAAa,KAAK,KAAK;AAC1B,UAAM,IAAIC,qBAAoB;MAC5B,SAAS;MACT,OAAO,IAAI,kBAAiB;KAC7B;AAEH,MAAI,QAAQ;AACV,QAAI,MAAM,YAAW,MAAO;AAAO;AACnC,QAAIC,UAAS,KAAgB,MAAM;AACjC,YAAM,IAAID,qBAAoB;QAC5B,SAAS;QACT,OAAO,IAAI,qBAAoB;OAChC;EACL;AACF;AA6BM,SAAUC,UAAS,SAAe;AACtC,MAAW,SAAS,IAAI,OAAO;AAAG,WAAc,SAAS,IAAI,OAAO;AAEpE,SAAO,SAAS,EAAE,QAAQ,MAAK,CAAE;AAEjC,QAAM,aAAa,QAAQ,UAAU,CAAC,EAAE,YAAW;AACnD,QAAM,OAAYC,WAAgB,WAAW,UAAU,GAAG,EAAE,IAAI,QAAO,CAAE;AAEzE,QAAM,aAAa,WAAW,MAAM,EAAE;AACtC,WAASC,KAAI,GAAGA,KAAI,IAAIA,MAAK,GAAG;AAC9B,QAAI,KAAKA,MAAK,CAAC,KAAM,KAAK,KAAK,WAAWA,EAAC,GAAG;AAC5C,iBAAWA,EAAC,IAAI,WAAWA,EAAC,EAAG,YAAW;IAC5C;AACA,SAAK,KAAKA,MAAK,CAAC,IAAK,OAAS,KAAK,WAAWA,KAAI,CAAC,GAAG;AACpD,iBAAWA,KAAI,CAAC,IAAI,WAAWA,KAAI,CAAC,EAAG,YAAW;IACpD;EACF;AAEA,QAAM,SAAS,KAAK,WAAW,KAAK,EAAE,CAAC;AACvC,EAAO,SAAS,IAAI,SAAS,MAAM;AACnC,SAAO;AACT;AA+MM,IAAOC,uBAAP,cAIWC,WAAgB;EAG/B,YAAY,EAAE,SAAS,MAAK,GAAqC;AAC/D,UAAM,YAAY,OAAO,iBAAiB;MACxC;KACD;AALe,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAII,IAAO,oBAAP,cAAwCA,WAAS;EAGrD,cAAA;AACE,UAAM,4DAA4D;AAHlD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;AAII,IAAO,uBAAP,cAA2CA,WAAS;EAGxD,cAAA;AACE,UAAM,kDAAkD;AAHxC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;;;ACnVK,IAAMC,cAAa;AAInB,IAAMC,cAAa;AAInB,IAAMC,gBACX;AAEK,IAAM,UAAU,OAAO,KAAK,MAAM;AAClC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AAEtC,IAAM,UAAU,EAAE,OAAO,KAAK;AAC9B,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAElC,IAAM,WAAW,MAAM,KAAK;AAC5B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAMC,cAAa,MAAM,OAAO;;;ACrEvC,IAAM,eAAqC;EACzC,OAAO,IAAI,WAAU;EACrB,UAAU,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC;EACzC,UAAU;EACV,mBAAmB,oBAAI,IAAG;EAC1B,oBAAoB;EACpB,oBAAoB,OAAO;EAC3B,kBAAe;AACb,QAAI,KAAK,sBAAsB,KAAK;AAClC,YAAM,IAAI,gCAAgC;QACxC,OAAO,KAAK,qBAAqB;QACjC,OAAO,KAAK;OACb;EACL;EACA,eAAe,UAAQ;AACrB,QAAI,WAAW,KAAK,WAAW,KAAK,MAAM,SAAS;AACjD,YAAM,IAAIC,0BAAyB;QACjC,QAAQ,KAAK,MAAM;QACnB;OACD;EACL;EACA,kBAAkB,QAAM;AACtB,QAAI,SAAS;AAAG,YAAM,IAAI,oBAAoB,EAAE,OAAM,CAAE;AACxD,UAAM,WAAW,KAAK,WAAW;AACjC,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;EAClB;EACA,aAAa,UAAQ;AACnB,WAAO,KAAK,kBAAkB,IAAI,YAAY,KAAK,QAAQ,KAAK;EAClE;EACA,kBAAkB,QAAM;AACtB,QAAI,SAAS;AAAG,YAAM,IAAI,oBAAoB,EAAE,OAAM,CAAE;AACxD,UAAM,WAAW,KAAK,WAAW;AACjC,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;EAClB;EACA,YAAY,WAAS;AACnB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,QAAQ;AAC5B,WAAO,KAAK,MAAM,QAAQ;EAC5B;EACA,aAAa,QAAQ,WAAS;AAC5B,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,SAAS,CAAC;AACzC,WAAO,KAAK,MAAM,SAAS,UAAU,WAAW,MAAM;EACxD;EACA,aAAa,WAAS;AACpB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,QAAQ;AAC5B,WAAO,KAAK,MAAM,QAAQ;EAC5B;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,WAAO,KAAK,SAAS,UAAU,QAAQ;EACzC;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,YACG,KAAK,SAAS,UAAU,QAAQ,KAAK,KACtC,KAAK,SAAS,SAAS,WAAW,CAAC;EAEvC;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,WAAO,KAAK,SAAS,UAAU,QAAQ;EACzC;EACA,SAAS,MAAmB;AAC1B,SAAK,eAAe,KAAK,QAAQ;AACjC,SAAK,MAAM,KAAK,QAAQ,IAAI;AAC5B,SAAK;EACP;EACA,UAAU,OAAY;AACpB,SAAK,eAAe,KAAK,WAAW,MAAM,SAAS,CAAC;AACpD,SAAK,MAAM,IAAI,OAAO,KAAK,QAAQ;AACnC,SAAK,YAAY,MAAM;EACzB;EACA,UAAU,OAAa;AACrB,SAAK,eAAe,KAAK,QAAQ;AACjC,SAAK,MAAM,KAAK,QAAQ,IAAI;AAC5B,SAAK;EACP;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,KAAK;AAC5C,SAAK,YAAY;EACnB;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,SAAS,CAAC;AACjD,SAAK,SAAS,SAAS,KAAK,WAAW,GAAG,QAAQ,CAAC,UAAU;AAC7D,SAAK,YAAY;EACnB;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,KAAK;AAC5C,SAAK,YAAY;EACnB;EACA,WAAQ;AACN,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,YAAW;AAC9B,SAAK;AACL,WAAO;EACT;EACA,UAAU,QAAQC,OAAI;AACpB,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,aAAa,MAAM;AACtC,SAAK,YAAYA,SAAQ;AACzB,WAAO;EACT;EACA,YAAS;AACP,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,aAAY;AAC/B,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,IAAI,YAAS;AACX,WAAO,KAAK,MAAM,SAAS,KAAK;EAClC;EACA,YAAY,UAAQ;AAClB,UAAM,cAAc,KAAK;AACzB,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;AAChB,WAAO,MAAO,KAAK,WAAW;EAChC;EACA,SAAM;AACJ,QAAI,KAAK,uBAAuB,OAAO;AAAmB;AAC1D,UAAM,QAAQ,KAAK,aAAY;AAC/B,SAAK,kBAAkB,IAAI,KAAK,UAAU,QAAQ,CAAC;AACnD,QAAI,QAAQ;AAAG,WAAK;EACtB;;AA4BI,IAAO,sBAAP,cAA0CC,WAAS;EAGvD,YAAY,EAAE,OAAM,GAAsB;AACxC,UAAM,YAAY,MAAM,wBAAwB;AAHhC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;AAII,IAAOC,4BAAP,cAA+CD,WAAS;EAG5D,YAAY,EAAE,QAAQ,SAAQ,GAAwC;AACpE,UACE,cAAc,QAAQ,yCAAyC,MAAM,MAAM;AAJ7D,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAII,IAAO,kCAAP,cAAsDA,WAAS;EAGnE,YAAY,EAAE,OAAO,MAAK,GAAoC;AAC5D,UACE,6BAA6B,KAAK,wCAAwC,KAAK,MAAM;AAJvE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;;;ACRI,SAAUE,cAEd,OAAuB,QAA2C;AAClE,MAAI,MAAM,WAAW,OAAO;AAC1B,UAAM,IAAI,oBAAoB;MAC5B,gBAAgB,MAAM;MACtB,aAAa,OAAO;KACrB;AAEH,QAAM,OAAkB,CAAA;AACxB,WAASC,KAAI,GAAGA,KAAK,MAAoB,QAAQA,MAAK;AACpD,UAAM,OAAO,MAAMA,EAAC;AACpB,UAAM,QAAQ,OAAOA,EAAC;AACtB,SAAK,KAAKD,cAAa,OAAO,MAAM,KAAK,CAAC;EAC5C;AACA,SAAWE,QAAO,GAAG,IAAI;AAC3B;CAEA,SAAiBF,eAAY;AAe3B,WAAgBG,QACd,MACA,OACA,UAAU,OAAK;AAEf,QAAI,SAAS,WAAW;AACtB,YAAM,UAAU;AAChB,MAAQ,OAAO,OAAO;AACtB,aAAW,QACT,QAAQ,YAAW,GACnB,UAAU,KAAK,CAAC;IAEpB;AACA,QAAI,SAAS;AAAU,aAAWC,YAAW,KAAe;AAC5D,QAAI,SAAS;AAAS,aAAO;AAC7B,QAAI,SAAS;AACX,aAAW,QAAY,YAAY,KAAgB,GAAG,UAAU,KAAK,CAAC;AAExE,UAAM,WAAY,KAAgB,MAAeC,aAAY;AAC7D,QAAI,UAAU;AACZ,YAAM,CAAC,OAAO,UAAU,OAAO,KAAK,IAAI;AACxC,YAAMC,QAAO,OAAO,SAAS,IAAI,IAAI;AACrC,aAAW,WAAW,OAAiB;QACrC,MAAM,UAAU,KAAKA;QACrB,QAAQ,aAAa;OACtB;IACH;AAEA,UAAM,aAAc,KAAgB,MAAeC,WAAU;AAC7D,QAAI,YAAY;AACd,YAAM,CAAC,OAAOD,KAAI,IAAI;AACtB,UAAI,OAAO,SAASA,KAAK,OAAQ,MAAkB,SAAS,KAAK;AAC/D,cAAM,IAAIE,wBAAuB;UAC/B,cAAc,OAAO,SAASF,KAAK;UACnC;SACD;AACH,aAAW,SAAS,OAAkB,UAAU,KAAK,CAAC;IACxD;AAEA,UAAM,aAAc,KAAgB,MAAeG,WAAU;AAC7D,QAAI,cAAc,MAAM,QAAQ,KAAK,GAAG;AACtC,YAAM,CAAC,OAAO,SAAS,IAAI;AAC3B,YAAM,OAAkB,CAAA;AACxB,eAASR,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,aAAK,KAAKE,QAAO,WAAW,MAAMF,EAAC,GAAG,IAAI,CAAC;MAC7C;AACA,UAAI,KAAK,WAAW;AAAG,eAAO;AAC9B,aAAWC,QAAO,GAAG,IAAI;IAC3B;AAEA,UAAM,IAAI,iBAAiB,IAAc;EAC3C;AAnDgB,EAAAF,cAAA,SAAMG;AAoDxB,GAnEiBH,kBAAAA,gBAAY,CAAA,EAAA;AA0WvB,IAAOU,0BAAP,cAA6CC,WAAS;EAE1D,YAAY,EACV,cACA,MAAK,GACoC;AACzC,UACE,kBAAkB,KAAK,WAAeC,MACpC,KAAK,CACN,wCAAwC,YAAY,IAAI;AAR3C,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAUzB;;AA0BI,IAAO,sBAAP,cAA0CD,WAAS;EAEvD,YAAY,EACV,gBACA,YAAW,GACqC;AAChD,UACE;MACE;MACA,iCAAiC,cAAc;MAC/C,0BAA0B,WAAW;MACrC,KAAK,IAAI,CAAC;AAVE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYzB;;AAsCI,IAAO,mBAAP,cAAuCE,WAAS;EAEpD,YAAY,MAAY;AACtB,UAAM,UAAU,IAAI,6BAA6B;AAFjC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGzB;;A;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtrBK,IAAM,WAAW;EACtB,UAAO;AACL,WAAO,kBAAkB,MAAM;EACjC;EAEA,MAAM,sBAAmB;AAzB3B;AA0BI,UAAM,OAAO,kBAAkB,MAAM;AACrC,UAAM,cAAc,gBAAgB,qBAAoB;AAExD,QAAI,EAAE,QAAQ,cAAc;AAC1B;IACF;AACA,UAAM,CAAC,WAAW,SAAS,OAAO,IAAI,YAAY,MAAM,GAAG;AAE3D,QAAI,CAAC,gBAAgB,wBAAwB,SAAS,GAAG;AACvD;IACF;AAEA,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,YAAY,GAAG,SAAS,IAAI,OAAO,IAAI,OAAO;AAE1E,UAAI,SAAS,QAAQ;AACnB;MACF;AAEA,YAAM,gBAAgB,KAAK;QACzB,MAAM;OACP;IACH,SAAS,OAAgB;AAEvB,cAAQ,MAAM,gCAAgC,KAAK;AAEnD,uBAAiB,UAAU;QACzB,MAAM;QACN,OAAO;QACP,YAAY,KAAK,uBAAsB;OACxC;AAGD,cAAM,0BAAqB,WAAU,MAA/B,mBAAmC,aAAa,MAAM,QAAQ;AACpE,uBAAiB,MAAM,SAAS;AAChC,sBAAgB,UAAU,2DAA2D;IACvF;EACF;EACA,MAAM,qBAAkB;AACtB,UAAM,OAAO,kBAAkB,MAAM;AACrC,UAAM,UAAU,eAAe,gBAAgB,gBAAgB,qBAAoB,CAAE;AACrF,UAAM,UAAU,gBAAgB,qBAAoB;AACpD,UAAM,SAAS,qBAAqB,WAAU;AAE9C,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,qBAAqB;IACvC;AAEA,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,4BAA4B;IAC9C;AAEA,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,sCAAsC;IACxD;AAEA,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,sCAAsC;IACxD;AAEA,QAAI;AACF,YAAM,cAAc,MAAM,KAAK,cAAc;QAC3C,SAAS,QAAQ;QACjB,gBAAgB;OACjB;AAED,YAAM,UAAU,YAAY,SAAQ;AACpC,YAAM,cAAc,oBAAoB,eAAe,QAAQ,cAAc;AAE7E,UAAI,gBAAgB,cAAoB,aAAa,MAAM;AACzD,yBAAiB,qBAAqB,CAAA,CAAE;MAC1C;AAEA,YAAM,YAAY,MAAM,OAAO,YAAY,OAAO;AAElD,YAAM,KAAK,WAAW;QACpB,MAAM;QACN;QACA;OACD;AAED,sBAAgB,MAAK;AAErB,uBAAiB,UAAU;QACzB,MAAM;QACN,OAAO;QACP,YAAY,KAAK,uBAAsB;OACxC;IACH,SAAS,OAAO;AACd,YAAM,aAAa,KAAK,uBAAsB;AAE9C,UAAI,CAAC,gBAAgB,MAAM,QAAQ,iBAAiB,MAAM,SAAS,sBAAsB;AACvF,cAAM,gBAAgB,KAAK;UACzB,MAAM;SACP;MACH;AAEA,UAAI,WAAW,gBAAgB;AAC7B,wBAAgB,UAAU,mDAAmD;MAC/E,OAAO;AACL,wBAAgB,UAAU,oBAAoB;MAChD;AAEA,uBAAiB,UAAU;QACzB,MAAM;QACN,OAAO;QACP;OACD;AAGD,cAAQ,MAAM,+BAA+B,KAAK;IACpD;EACF;EACA,MAAM,oBAAiB;AA3IzB;AA4II,QAAI;AACF,YAAM,OAAO,KAAK,QAAO;AACzB,YAAM,cAAa,kCAAM,gBAAN;AAEnB,UAAI,YAAY;AACd,cAAM,qBAAqB,WAAU;MACvC,OAAO;AACL,wBAAgB,MAAK;MACvB;AAEA,uBAAiB,MAAM,SAAS;AAEhC,uBAAiB,UAAU;QACzB,OAAO;QACP,MAAM;QACN,YAAY,KAAK,uBAAsB;OACxC;IACH,SAAS,OAAO;AAEd,cAAQ,MAAM,8BAA8B,KAAK;IACnD;EACF;EACA,MAAM,cAAW;AACf,UAAM,OAAO,kBAAkB,MAAM;AACrC,UAAM,UAAU,eAAe,gBAAgB,gBAAgB,qBAAoB,CAAE;AACrF,UAAM,UAAU,gBAAgB,qBAAoB;AAEpD,QAAI,EAAE,QAAQ,WAAW,UAAU;AACjC,aAAO,CAAA;IACT;AAEA,WAAO,KAAK,YAAY,QAAQ,eAAe,OAAO;EACxD;EACA,MAAM,sBAAmB;AA7K3B;AA8KI,UAAM,OAAO,KAAK,QAAO;AAEzB,QAAI,MAAM;AACR,YAAM,sBAAsB,iBAAiB,MAAM,SAAS;AAC5D,YAAM,oBAAoB,iBAAiB,MAAM,SAAS;AAE1D,UAAI,uBAAuB,mBAAmB;AAC5C,iBAAO,UAAK,gBAAL,mCAAyB,MAAM,KAAK,YAAW,GAAI,WAAW;MACvE;IACF;AAEA,WAAO;EACT;EACA,MAAM,8BAA8B,EAClC,mBACA,QACA,QAAO,GAKR;AAnMH,gBAAAC;AAoMI,UAAM,OAAO,SAAS,QAAO;AAE7B,UAAM,aAAa,IAAI,IAAI,OAAO,IAAI,WAAS,MAAM,MAAM,GAAG,EAAE,CAAC,CAAmB,CAAC;AAErF,QAAI,CAAC,QAAQ,WAAW,SAAS,KAAK,CAAC,WAAW,IAAI,QAAQ,GAAG;AAC/D,aAAO;IACT;AAGA,UAAM,cAAc,MAAM,KAAK,cAAc;MAC3C,WAAS,qBAAgB,qBAAoB,MAApC,mBAAwC,kBAAkB;MACnE,gBAAgB;KACjB;AAED,UAAM,SAAS,MAAM,kBAAkB,aAAa;MAClD,OAAO,YAAY;MACnB,QAAQ,YAAY;MACpB,KAAK,YAAY;MACjB,KAAK,YAAY;MACjB,KAAK,YAAY;MACjB,KAAK,YAAY;MACjB,WAAW,YAAY;MACvB,SAAS,YAAY;MACrB,WAAW,YAAY;MACvB,WAAW,YAAY;MACvB,SAAS,YAAY;MACrB;;MAEA,QAAQ,CAAC,YAAY,SAAS,GAAG,OAAO,OAAO,WAAS,UAAU,YAAY,OAAO,CAAC;KACvF;AAED,oBAAgB,YAAY,qBAAqB,EAAE,WAAW,MAAK,CAAE;AAErE,sBAAkB,uBAChB;MACE,GAAG,OAAO,QAAQ,KAAK;MACvB,MAAM,OAAO,QAAQ,KAAK,SAAS;MACnC,OAAM,YAAO,QAAQ,KAAK,SAAS,UAA7B,mBAAqC;MAC3C,MAAM;OAER,MAAM,KAAK,UAAU,EAAE,CAAC,CAAmB;AAG7C,SAAIA,MAAA,iCAAQ,UAAR,gBAAAA,IAAe,QAAQ;AACzB,YAAM,WAAW,OAAO,MAAM,IAAiB,WAAQ;AACrD,cAAM,UAAU,kBAAkB,OAAO,kBAAkB;UACzD,SAAS,MAAM;UACf,KAAK,MAAM,EAAE;SACd;AAED,eAAO;UACL,MAAM;YACJ,GAAG,MAAM;YACT,gBAAgB,MAAM,EAAE,IAAI,MAAM,GAAG,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE;YACxD,SAAS,MAAM,EAAE,IAAI,MAAM,GAAG,EAAE,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG;YACpD,KAAK,MAAM,EAAE;YACb,SAAS,MAAM,EAAE,WAAW,YAAY;YACxC,gBAAgB,MAAM,EAAE;YACxB,UAAU,MAAM,EAAE;YAClB,WAAW,MAAM,EAAE;;UAErB;UACA,WAAW,MAAM,EAAE;UACnB;;MAEJ,CAAC;AAED,UAAI;AACF,cAAM,KAAK,YAAY,QAAQ;AAE/B,yBAAiB,UAAU;UACzB,MAAM;UACN,OAAO;UACP,YAAY,SAAS,uBAAsB;SAC5C;MACH,SAAS,OAAO;AAEd,gBAAQ,MAAM,uDAAuD,KAAK;AAE1E,yBAAiB,UAAU;UACzB,MAAM;UACN,OAAO;UACP,YAAY,SAAS,uBAAsB;SAC5C;AAGD,cAAM,kBAAkB,WAAU,EAAG,MAAM,QAAQ,KAAK;AACxD,cAAM;MACR;AACE,wBAAgB,KAAI;MACtB;IACF;AAEA,WAAO;EACT;EACA,yBAAsB;AAnSxB;AAoSI,UAAM,uBAAuB,gBAAgB,MAAM;AAEnD,WAAO;MACL,WAAS,qBAAgB,MAAM,sBAAtB,mBAAyC,kBAAiB;MACnE,kBACE,uBAAkB,MAAM,0BAAxB,mBAAgD,2BAChD,qBAAqB,cAAc;;EAEzC;EACA,MAAM,gBAAa;AACjB,UAAM,OAAO,KAAK,QAAO;AAEzB,QAAI,MAAM;AACR,YAAM,KAAK,YAAY,CAAA,CAAE;IAC3B;EACF;;;;AC/SK,IAAM,YAAY;EACvB,yBAAsB;AACpB,WACE,iBAAiB,MAAM,SAAS,sBAC/B,iBAAiB,MAAM,SAAS,mBAC/B,iBAAiB,MAAM,QAAQ,SAAS,kBAAkB;EAEhE;EAEA,MAAM,YAAS;AACb,QAAI,KAAK,uBAAsB,GAAI;AACjC,sBAAgB,MAAK;AAErB;IACF;AAEA,UAAM,sBAAsB,MAAM,SAAS,oBAAmB;AAC9D,QAAI,qBAAqB;AACvB,sBAAgB,MAAK;AAErB;IACF;AAEA,oBAAgB,MAAK;EACvB;;;;ACaK,IAAM,wBAAwB;EACnC,IAAI;EACJ,MAAM;EACN,QAAQ;EACR,UAAU;IACR;MACE,MAAM;MACN,cAAc;MACd,UAAU;MACV,kBAAkB;;IAEpB;MACE,MAAM;MACN,cAAc;MACd,UAAU;MACV,kBAAkB;;;;AAKjB,IAAM,uBAAuB;EAClC,IAAI;EACJ,uBAAuB;IACrB;MACE,IAAI;MACJ,KAAK;MACL,KAAK;;IAEP;MACE,IAAI;MACJ,KAAK;MACL,KAAK;;;;AAKX,IAAM,eAAe;EACnB,WAAW;EACX,kBAAkB;EAClB,OAAO;EACP,kBAAkB;EAClB,iBAAiB;EACjB,oBAAoB,CAAC,qBAAqB;EAC1C,mBAAmB,CAAA;EACnB,eAAe;;AAIjB,IAAMC,UAAQ,MAA6B,YAAY;AAGvD,IAAMC,eAAa;EACjB,OAAAD;EAEA,UAAU,UAAmD;AAC3D,WAAO,UAAIA,SAAO,MAAM,SAASA,OAAK,CAAC;EACzC;EAEA,aAAiC,KAAQ,UAAmD;AAC1F,WAAO,aAAOA,SAAO,KAAK,QAAQ;EACpC;EAEA,oBAAoB,UAA+B;AACjD,QAAI,YAAY,SAAS,SAAS,QAAQ;AACxC,YAAM,WACJ,gBAAgB,MAAM,gBAAgB,cAAc,MAAM,SAAS,QAAQ;AAC7E,YAAM,UAAU,kBAAkB,MAAM,WAAW;AACnD,YAAM,MAAM,IAAI,IAAI,SAAS,GAAG;AAChC,UAAI,aAAa,OAAO,aAAa,eAAe;AACpD,UAAI,aAAa,OAAO,2BAA2B,QAAQ;AAC3D,UAAI,aAAa,OAAO,iBAAiB,OAAO;AAChD,UAAI,aAAa,OAAO,sBAAsB,kBAAkB,MAAM,SAAS;AAC/E,MAAAA,QAAM,mBAAmB,EAAE,GAAG,UAAU,KAAK,IAAI,SAAQ,EAAE;IAC7D,OAAO;AACL,MAAAA,QAAM,mBAAmB;IAC3B;EACF;EAEA,mBAAmB,WAA+B;AAChD,QAAI,MAAM,QAAQ,SAAS,KAAK,UAAU,MAAM,UAAQ,OAAO,SAAS,QAAQ,GAAG;AACjF,YAAM,cAAc;AAEpB,YAAM,eAAe,iBAAiB,OAAO,cAAY,YAAY,SAAS,SAAS,IAAI,CAAC;AAE5F,MAAAA,QAAM,YAAY;IACpB,OAAO;AACL,MAAAA,QAAM,YAAY,CAAA;IACpB;EACF;EAEA,oBAAoB,UAA0B;AAC5C,IAAAA,QAAM,mBAAmB;EAC3B;EAEA,mBAAmB,UAAyB;AAC1C,IAAAA,QAAM,kBAAkB;EAC1B;EAEA,kBAAkB,QAAc;AAC9B,qBAAiB,MAAM,iBAAiB;EAC1C;EAEA,iBAAiB,QAAc;AAC7B,qBAAiB,MAAM,gBAAgB;EACzC;EAEA,MAAM,yBAAsB;AAC1B,UAAM,UAAU,MAAM,wBAAwB,iBAAgB;AAC9D,IAAAA,QAAM,qBAAqB,QAAQ;AACnC,IAAAA,QAAM,oBAAoB,QAAQ;AAClC,IAAAA,QAAM,kBAAkB,QAAQ,kBAAkB,CAAC,KAAK;AACxD,IAAAA,QAAM,mBAAmB,QAAQ,mBAAmB,CAAC,KAAK;AAC1D,UAAM,cAAc,oBAAoB,QAAQ,kBAAkB,IAAI,cAAY,SAAS,EAAE,CAAC;AAC9F,UAAM,cAAc,iBAClB,QAAQ,mBAAmB,IAAI,cAAY,SAAS,MAAM,CAAC;EAE/D;EAEA,MAAM,WAAQ;AA/JhB;AAgKI,IAAAA,QAAM,gBAAgB;AACtB,QAAI;AACF,YAAM,QAAQ,MAAM,wBAAwB,eAAe;QACzD,kBAAkBA,QAAM;QACxB,iBAAiBA,QAAM;QACvB,UAAQ,KAAAA,QAAM,kBAAN,mBAAqB,eAAc;QAC3C,UAAS,KAAAA,QAAM,qBAAN,mBAAwB;OAClC;AACD,MAAAA,QAAM,gBAAgB;AACtB,MAAAA,QAAM,iBAAiB,OAAO,+BAAO,eAAe,MAAM;AAE1D,aAAO;IACT,SAAS,OAAO;AACd,MAAAA,QAAM,QAAS,MAAgB;AAC/B,MAAAA,QAAM,gBAAgB;AAEtB,aAAO;IACT;AACE,MAAAA,QAAM,gBAAgB;IACxB;EACF;EAEA,aAAU;AACR,IAAAA,QAAM,mBAAmB;AACzB,IAAAA,QAAM,QAAQ;AACd,IAAAA,QAAM,mBAAmB;AACzB,IAAAA,QAAM,kBAAkB;AACxB,IAAAA,QAAM,qBAAqB,CAAC,qBAAqB;AACjD,IAAAA,QAAM,oBAAoB,CAAA;AAC1B,IAAAA,QAAM,gBAAgB;AACtB,IAAAA,QAAM,iBAAiB;AACvB,IAAAA,QAAM,gBAAgB;EACxB;;AAIK,IAAM,mBAAmB,kBAAkBC,YAAU;;;AC/KrD,IAAM,cAAc;EACzB,MAAM,eAAY;AAtBpB;AAuBI,UAAM,cAAc,gBAAgB,MAAM;AAC1C,UAAM,WAAW,MAAM,wBAAwB,gBAAgB;MAC7D,SAAS,2CAAa;KACvB;AACD,UAAM,WACJ,0CAAU,WAAV,mBAAkB,IAChB,YACG;MACC,GAAG;MACH,SAAS;MACT,UAAU;QACR,UAAU;QACV,SAAS;;MAEX,OAAO;MACP,OAAO;YAER,CAAA;AAEP,WAAO;EACT;EAEA,MAAM,gBAAa;AA7CrB;AA8CI,UAAM,cAAc,gBAAgB,MAAM;AAE1C,QAAI,CAAC,aAAa;AAChB,aAAO;IACT;AAEA,QAAI;AACF,cAAQ,YAAY,gBAAgB;QAClC,KAAK;AAEH,gBAAM,wBACJ,aAAM,iDAAsB,YAAY,EAAE,gBAAgB,SAAQ,QAAlE,mBACC;AAEH,iBAAO;YACL,UAAU;YACV,MAAM;YACN,SAAS;;QAGb,KAAK;QACL;AACE,iBAAO,MAAM,wBAAwB,cAAc;YACjD,SAAS,YAAY;WACtB;MACL;IACF,QAAQ;AACN,aAAO;IACT;EACF;EAEA,MAAM,mBAAmB,EACvB,cACA,aACA,mBACA,oBAAmB,GAIpB;AACC,UAAM,WAAW,MAAM,wBAAwB,mBAAmB;MAChE;MACA;KACD;AAED,SAAI,qCAAU,cAAa,qBAAqB,qBAAqB;AACnE,YAAM,cACJ,qBAAqB,WAAW,mBAAmB,mBAAmB,KAAK;AAC7E,YAAM,eAAe,OAAO,SAAS,SAAS,KAAK;AAEnD,aAAO;IACT;AAEA,WAAO;EACT;EAEA,MAAM,uBAAuB,aAAoB;AAC/C,UAAM,UAAU,kBAAkB,MAAM;AACxC,UAAM,cAAc,gBAAgB,MAAM;AAE1C,QAAI,CAAC,WAAW,CAAC,aAAa;AAC5B,aAAO,CAAA;IACT;AAEA,UAAM,WAAW,MAAM,wBAAwB,WAC7C,SACA,YAAY,eACZ,WAAW;AAMb,UAAM,WAAW,SAAS,SAAS,OAAO,aAAW,QAAQ,SAAS,aAAa,GAAG;AAEtF,sBAAkB,gBAAgB,UAAU,gBAAgB,MAAM,WAAW;AAE7E,WAAO,KAAK,wBAAwB,QAAQ;EAC9C;EAEA,wBAAwB,UAAkD;AACxE,YACE,qCAAU,IACR,YACG;MACC,GAAG;MACH,UAAS,+BAAO,WACZ,MAAM,UACN,gBAAgB,6BAA4B;MAChD,UAAU,SAAS,MAAM,SAAS,UAAU,EAAE;MAC9C,SAAS,MAAM;MACf,SAAS;YAEV,CAAA;EAET;;;;ACvIK,IAAM,sBAAsB;EACjC,mBAAmB,KAAa,UAAgB;AAC9C,UAAM,oBAAoB,WAAW;AACrC,UAAM,sBAAsB,OAAO,iBAAiB,IAAI;AAExD,WAAO;EACT;EAEA,iBAAiB,cAAsB,KAAa,UAAgB;AAClE,UAAM,sBAAsB,oBAAoB,mBAAmB,KAAK,QAAQ;AAChF,UAAM,oBAAoB,WAAW,UAAU,YAAY;AAC3D,UAAM,eAAe,kBAAkB,MAAM,mBAAmB;AAEhE,WAAO,aAAa,SAAQ;EAC9B;EAEA,eAAe,EACb,mBACA,uBACA,mBACA,cAAa,GAMd;AACC,UAAM,aAAa,WAAW,UAAU,iBAAiB,EAAE,MAAM,qBAAqB;AACtF,UAAM,cAAc,WAAW,UAAU,aAAa,EAAE,MAAM,iBAAiB;AAC/E,UAAM,cAAc,WAAW,MAAM,WAAW,EAAE,IAAI,UAAU,EAAE,MAAM,GAAG;AAE3E,WAAO,YAAY,SAAQ;EAC7B;EAEA,eAAe,UAAkB,eAAqB;AACpD,UAAM,2BAA2B,WAAW,UAAU,QAAQ,EAAE,IAAI,GAAG;AACvE,UAAM,oBAAoB,WAAW,SAAS,eAAe,wBAAwB;AAErF,WAAO,kBAAkB,SAAQ;EACnC;EAEA,eAAe,mBAA2B,gBAAgB,OAAM;AAC9D,UAAM,cAAc,WAAW,UAAU,iBAAiB,EAAE,MAAM,aAAa;AAE/E,WAAO,YAAY,SAAQ;EAC7B;EAEA,iCAAiC,qBAA6B,eAAiC;AAC7F,UAAM,WAAW,iBAAiB;AAElC,QAAI,WAAW,UAAU,mBAAmB,EAAE,GAAG,CAAC,GAAG;AACnD,aAAO;IACT;AAEA,WAAO,WAAW,UAAU,WAAW,UAAU,QAAQ,CAAC,EAAE,GAAG,mBAAmB;EACpF;EAEA,iCACE,mBACA,oBACA,SAA2C;AAlE/C;AAoEI,UAAM,sBAAqB,8CAAS,KAAK,WAAS,MAAM,YAAY,wBAAzC,mBACvB,aADuB,mBACb;AAEd,UAAM,wBAAwB,WAAW,UAAU,sBAAsB,GAAG,EAAE,GAC5E,iBAAiB;AAGnB,WAAO;EACT;EAEA,iBAAiB,EACf,aACA,SACA,kBACA,cACA,kBAAiB,GAOlB;AACC,QAAI,sBAAsB,KAAK;AAC7B,aAAO;IACT;AAEA,QAAI,CAAC,eAAe,CAAC,SAAS;AAC5B,aAAO;IACT;AAEA,UAAM,sBAAsB,YAAY;AACxC,UAAM,wBAAwB;AAC9B,UAAM,kBAAkB,QAAQ;AAChC,UAAM,oBAAoB;AAE1B,QAAI,qBAAqB,GAAG;AAC1B,aAAO;IACT;AAGA,UAAM,cAAc,WAAW,UAAU,iBAAiB,EAAE,MAAM,KAAM;AAGxE,UAAM,4BAA4B,WAAW,UAAU,iBAAiB,EAAE,MAAM,WAAW;AAG3F,UAAM,6BAA6B,0BAA0B,MAC3D,WAAW,UAAU,EAAE,EAAE,IAAI,mBAAmB,CAAC;AAGnD,UAAM,aAAa,WAAW,UAAU,qBAAqB,EAAE,IAAI,iBAAiB;AAEpF,UAAM,oBAAoB,sBAAsB;AAChD,UAAM,8BAA8B,2BACjC,MAAM,UAAU,EAChB,IAAI,WAAW,UAAU,EAAE,EAAE,IAAI,iBAAiB,CAAC;AAEtD,UAAM,gBAAgB,4BAA4B,IAChD,WAAW,UAAU,EAAE,EAAE,IAAI,eAAe,CAAC;AAG/C,UAAM,SAAS,cAAc,QAAQ,eAAe,EAAE,SAAQ;AAE9D,WAAO;EACT;;;;AC5GK,IAAM,oBAAoB;AAC1B,IAAM,qBAAqB;AA8FlC,IAAM,eAAoC;;EAExC,cAAc;EACd,aAAa;EACb,eAAe;EACf,cAAc;EACd,4BAA4B;EAC5B,yBAAyB;EACzB,oBAAoB;;EAGpB,YAAY;;EAGZ,qBAAqB;EACrB,iBAAiB;EACjB,kBAAkB;;EAGlB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,SAAS;EACT,eAAe;EACf,mBAAmB;EACnB,cAAc;EACd,qBAAqB;EACrB,oBAAoB;EACpB,YAAY;;EAGZ,UAAUC,eAAc;;EAGxB,QAAQ;EACR,eAAe;EACf,iBAAiB;EACjB,aAAa;EACb,qBAAqB;EACrB,gBAAgB,CAAA;;EAGhB,QAAQ;EACR,eAAe;EACf,aAAa;EACb,aAAa;EACb,aAAa;;AAGf,IAAMC,UAAQ,MAA2B,YAAY;AAGrD,IAAMC,eAAa;EACjB,OAAAD;EAEA,UAAU,UAAiD;AACzD,WAAO,UAAIA,SAAO,MAAM,SAASA,OAAK,CAAC;EACzC;EAEA,aAAiC,KAAQ,UAAiD;AACxF,WAAO,aAAOA,SAAO,KAAK,QAAQ;EACpC;EAEA,YAAS;AAvLX,gBAAAE,KAAA,IAAAC,KAAA;AAwLI,UAAM,cAAc,gBAAgB,MAAM;AAC1C,UAAM,YAAY,gBAAgB,MAAM;AACxC,UAAM,UAAU,eAAe,gBAAgB,WAAW;AAC1D,UAAM,iBAAiB,gBAAgB,6BAA4B;AACnE,UAAM,cAAc,oBAAoB,eAAe,SAAS;AAEhE,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,2CAA2C;IAC7D;AAEA,UAAM,iBAAiB,GAAC,KAAAH,QAAM,YAAN,mBAAe,YAAW,GAAC,KAAAA,QAAM,YAAN,mBAAe;AAClE,UAAM,qBACJ,GAACE,MAAAF,QAAM,gBAAN,gBAAAE,IAAmB,YACpB,GAAC,KAAAF,QAAM,gBAAN,mBAAmB,aACpB,CAAC,WAAW,UAAUA,QAAM,iBAAiB,EAAE,GAAG,CAAC;AACrD,UAAM,2BAA2B,CAACA,QAAM;AAExC,WAAO;MACL;MACA,aAAa;MACb,iBAAiB;MACjB,qBAAoBG,MAAAH,QAAM,gBAAN,gBAAAG,IAAmB;MACvC,iBAAgB,KAAAH,QAAM,YAAN,mBAAe;MAC/B,eAAeA,QAAM;MACrB,kBAAiB,KAAAA,QAAM,YAAN,mBAAe;MAChC,mBAAmBA,QAAM;MACzB,sBAAqB,KAAAA,QAAM,gBAAN,mBAAmB;MACxC;MACA;MACA;MACA,iBACE,eAAe,CAAC,kBAAkB,CAAC,sBAAsB,CAAC;MAC5D,iBAAiB,gBAAgB,cAAoB,aAAa;;EAEtE;EAEA,eAAe,aAA6C;AAC1D,QAAI,CAAC,aAAa;AAChB,MAAAA,QAAM,cAAc;AACpB,MAAAA,QAAM,oBAAoB;AAC1B,MAAAA,QAAM,wBAAwB;AAE9B;IACF;AAEA,IAAAA,QAAM,cAAc;AACpB,mBAAe,cAAc,YAAY,SAAS,aAAa;EACjE;EAEA,qBAAqB,QAAc;AACjC,IAAAA,QAAM,oBAAoB;EAC5B;EAEA,WAAW,SAAyC;AAClD,QAAI,CAAC,SAAS;AACZ,MAAAA,QAAM,UAAU;AAChB,MAAAA,QAAM,gBAAgB;AACtB,MAAAA,QAAM,oBAAoB;AAE1B;IACF;AAEA,IAAAA,QAAM,UAAU;AAChB,mBAAe,cAAc,QAAQ,SAAS,SAAS;EACzD;EAEA,iBAAiB,QAAc;AAC7B,IAAAA,QAAM,gBAAgB,SAClB,WAAW,0BAA0B,QAAQ,kBAAkB,IAC/D;EACN;EAEA,MAAM,cAAc,SAAiB,QAAuB;AAC1D,QAAI,QAAQA,QAAM,eAAe,OAAO,KAAK;AAE7C,QAAI,CAAC,OAAO;AACV,MAAAA,QAAM,gBAAgB;AACtB,cAAQ,MAAM,eAAe,gBAAgB,OAAO;IACtD;AAEA,QAAI,WAAW,eAAe;AAC5B,MAAAA,QAAM,wBAAwB;IAChC,WAAW,WAAW,WAAW;AAC/B,MAAAA,QAAM,oBAAoB;IAC5B;AAEA,QAAIA,QAAM,eAAe;AACvB,MAAAA,QAAM,gBAAgB;IACxB;AAEA,QAAI,eAAe,UAAS,EAAG,iBAAiB;AAC9C,qBAAe,WAAU;IAC3B;EACF;EAEA,eAAY;AACV,QAAIA,QAAM,gBAAgB,CAACA,QAAM,aAAa;AAC5C;IACF;AAEA,UAAM,iBAAiBA,QAAM,UAAU,EAAE,GAAGA,QAAM,QAAO,IAAK;AAC9D,UAAM,aAAaA,QAAM,cAAc,EAAE,GAAGA,QAAM,YAAW,IAAK;AAClE,UAAM,uBACJ,kBAAkBA,QAAM,kBAAkB,KAAK,MAAMA,QAAM;AAE7D,mBAAe,eAAe,cAAc;AAC5C,mBAAe,WAAW,UAAU;AAEpC,mBAAe,qBAAqB,oBAAoB;AACxD,mBAAe,iBAAiB,EAAE;AAClC,mBAAe,WAAU;EAC3B;EAEA,aAAU;AACR,IAAAA,QAAM,sBAAsB,aAAa;AACzC,IAAAA,QAAM,iBAAiB,aAAa;AACpC,IAAAA,QAAM,cAAc,aAAa;AACjC,IAAAA,QAAM,cAAc,aAAa;AACjC,IAAAA,QAAM,oBAAoB,aAAa;AACvC,IAAAA,QAAM,wBAAwB,aAAa;AAC3C,IAAAA,QAAM,UAAU,aAAa;AAC7B,IAAAA,QAAM,gBAAgB,aAAa;AACnC,IAAAA,QAAM,oBAAoB,aAAa;AACvC,IAAAA,QAAM,eAAe,aAAa;AAClC,IAAAA,QAAM,qBAAqB,aAAa;AACxC,IAAAA,QAAM,sBAAsB,aAAa;AACzC,IAAAA,QAAM,aAAa,aAAa;AAChC,IAAAA,QAAM,sBAAsB,aAAa;EAC3C;EAEA,cAAW;AA1Tb;AA2TI,UAAM,EAAE,eAAc,IAAK,eAAe,UAAS;AAEnD,UAAM,gBAAe,KAAAA,QAAM,WAAN,mBAAc,KAAK,WAAS,MAAM,YAAY;AACnE,mBAAe,eAAe,YAAY;AAC1C,mBAAe,WAAW,MAAS;EACrC;EAEA,0BAAuB;AACrB,WAAOA,QAAM;EACf;EAEA,aAAU;AACR,IAAAA,QAAM,mBAAmB;EAC3B;EAEA,MAAM,kBAAe;AACnB,QAAIA,QAAM,cAAc;AACtB;IACF;AAEA,IAAAA,QAAM,eAAe;AACrB,QAAI,CAACA,QAAM,aAAa;AACtB,UAAI;AACF,cAAM,eAAe,YAAW;AAChC,QAAAA,QAAM,cAAc;MACtB,SAAS,OAAO;AACd,QAAAA,QAAM,cAAc;AACpB,wBAAgB,UAAU,2BAA2B;AACrD,yBAAiB,OAAM;MACzB;IACF;AACA,IAAAA,QAAM,eAAe;EACvB;EAEA,MAAM,cAAW;AA7VnB;AA8VI,UAAM,EAAE,eAAc,IAAK,eAAe,UAAS;AAEnD,UAAM,eAAe,aAAY;AACjC,UAAM,eAAe,qBAAoB;AACzC,UAAM,eAAe,uBAAsB;AAE3C,UAAM,gBAAe,KAAAA,QAAM,WAAN,mBAAc,KAAK,WAAS,MAAM,YAAY;AAEnE,QAAI,cAAc;AAChB,MAAAA,QAAM,qBAAqB,aAAa;AACxC,qBAAe,eAAe,YAAY;AAC1C,qBAAe,qBAAqB,GAAG;IACzC;EACF;EAEA,MAAM,eAAY;AAChB,UAAM,SAAS,MAAM,YAAY,aAAY;AAE7C,IAAAA,QAAM,SAAS;AACf,IAAAA,QAAM,gBAAgB,OAAO,KAAK,CAAC,YAAY,eAAc;AAC3D,UAAI,WAAW,SAAS,WAAW,QAAQ;AACzC,eAAO;MACT;AACA,UAAI,WAAW,SAAS,WAAW,QAAQ;AACzC,eAAO;MACT;AAEA,aAAO;IACT,CAAC;AACD,IAAAA,QAAM,kBAAkB,OAAO,OAAO,WAAQ;AAC5C,UAAID,eAAc,sBAAsB,SAAS,MAAM,MAAM,GAAG;AAC9D,eAAO;MACT;AAEA,aAAO;IACT,GAAG,CAAA,CAAE;EACP;EAEA,MAAM,gBAAgB,SAAe;AApYvC;AAqYI,UAAM,aAAaC,QAAM,eAAe,OAAO;AAE/C,QAAI,YAAY;AACd,aAAO;IACT;AAEA,UAAM,WAAW,MAAM,wBAAwB,gBAAgB;MAC7D,WAAW,CAAC,OAAO;KACpB;AACD,UAAM,aAAY,qCAAU,cAAa,CAAA;AACzC,UAAM,YAAY,CAAC,GAAIA,QAAM,UAAU,CAAA,GAAK,GAAIA,QAAM,uBAAuB,CAAA,CAAG;AAChF,UAAM,UAAS,4CAAW,KAAK,WAAS,MAAM,YAAY,aAA3C,mBAAqD;AACpE,UAAM,UAAQ,eAAU,KAAK,OAAK,EAAE,OAAO,YAAW,OAAO,iCAAQ,cAAa,MAApE,mBAAuE,UAAS;AAC9F,UAAM,eAAe,WAAW,MAAM,SAAQ,CAAE;AAEhD,IAAAA,QAAM,eAAe,OAAO,IAAI;AAEhC,WAAO;EACT;EAEA,MAAM,uBAAoB;AAzZ5B;AA0ZI,UAAM,EAAE,eAAc,IAAK,eAAe,UAAS;AAEnD,UAAM,WAAW,MAAM,wBAAwB,gBAAgB;MAC7D,WAAW,CAAC,cAAc;KAC3B,EAAE,MAAM,MAAK;AACZ,sBAAgB,UAAU,qCAAqC;AAE/D,aAAO,EAAE,WAAW,CAAA,EAAE;IACxB,CAAC;AACD,UAAM,SAAQ,cAAS,cAAT,mBAAqB;AACnC,UAAM,SAAQ,+BAAO,MAAM,eAAc;AACzC,IAAAA,QAAM,eAAe,cAAc,IAAI,WAAW,KAAK;AACvD,IAAAA,QAAM,sBAAqB,+BAAO,WAAU;AAC5C,IAAAA,QAAM,eAAe;EACvB;EAEA,MAAM,uBAAuB,aAAoB;AAC/C,UAAM,WAAW,MAAM,YAAY,uBAAuB,WAAW;AACrE,UAAM,eAAe,YAAY,wBAAwB,QAAQ;AACjE,QAAI,CAAC,cAAc;AACjB;IACF;AAEA,UAAM,eAAe,mBAAkB;AACvC,mBAAe,YAAY,YAAY;EACzC;EAEA,YAAY,UAAgC;AAC1C,UAAM,EAAE,eAAc,IAAK,eAAe,UAAS;AACnD,UAAM,cAAc,gBAAgB,MAAM;AAE1C,QAAI,CAAC,aAAa;AAChB;IACF;AAEA,UAAM,eAAe,SAAS,KAAK,WAAS,MAAM,YAAY,cAAc;AAE5E,aAAS,QAAQ,WAAQ;AACvB,MAAAA,QAAM,eAAe,MAAM,OAAO,IAAI,MAAM,SAAS;IACvD,CAAC;AACD,IAAAA,QAAM,sBAAsB,SAAS,OAAO,WAC1C,MAAM,QAAQ,WAAW,YAAY,aAAa,CAAC;AAErD,IAAAA,QAAM,sBAAsB,eACxB,WAAW,SAAS,aAAa,SAAS,SAAS,aAAa,KAAK,EAAE,SAAQ,IAC/E;EACN;EAEA,MAAM,qBAAkB;AA1c1B;AA2cI,UAAM,MAAM,MAAM,YAAY,cAAa;AAE3C,QAAI,CAAC,KAAK;AACR,aAAO,EAAE,UAAU,MAAM,eAAe,KAAI;IAC9C;AAEA,aAAQ,2BAAgB,UAAhB,mBAAuB,sBAAvB,mBAA0C,gBAAgB;MAChE,KAAK;AACH,QAAAA,QAAM,SAAS,IAAI,YAAY;AAC/B,QAAAA,QAAM,gBAAgB,WAAW,SAAS,IAAI,UAAUA,QAAM,YAAY,EACvE,IAAI,GAAG,EACP,SAAQ;AAEX,eAAO;UACL,UAAU,OAAOA,QAAM,MAAM;UAC7B,eAAe,OAAOA,QAAM,aAAa;;MAG7C,KAAK;MACL;AAEE,cAAM,QAAQ,IAAI,YAAY;AAE9B,cAAM,SAAS,OAAO,KAAK;AAE3B,cAAM,WAAW,OAAO,iBAAiB;AAEzC,cAAM,WAAW,oBAAoB,iBAAiBA,QAAM,cAAc,UAAU,MAAM;AAE1F,QAAAA,QAAM,SAAS;AACf,QAAAA,QAAM,gBAAgB;AAEtB,eAAO,EAAE,UAAU,QAAQ,eAAe,SAAQ;IACtD;EACF;;EAGA,MAAM,aAAU;AAhflB;AAifI,UAAM,UAAU,kBAAkB,MAAM;AACxC,UAAM,cAAcA,QAAM;AAC1B,UAAM,UAAUA,QAAM;AACtB,UAAM,wBAAwB,WAAW,UAAUA,QAAM,iBAAiB,EAAE,GAAG,CAAC;AAEhF,QAAI,CAAC,uBAAuB;AAC1B,qBAAe,iBAAiB,EAAE;IACpC;AAEA,QAAI,CAAC,WAAW,CAAC,eAAeA,QAAM,iBAAiB,CAAC,uBAAuB;AAC7E;IACF;AAEA,IAAAA,QAAM,eAAe;AAErB,UAAM,gBAAgB,WAAW,UAAUA,QAAM,iBAAiB,EAC/D,MAAM,MAAM,YAAY,QAAQ,EAChC,MAAM,CAAC;AAEV,QAAI;AACF,YAAM,gBAAgB,MAAM,wBAAwB,eAAe;QACjE,aAAa;QACb,MAAM,YAAY;QAClB,IAAI,QAAQ;QACZ,UAAUA,QAAM;QAChB,QAAQ,cAAc,SAAQ;OAC/B;AAED,MAAAA,QAAM,eAAe;AAErB,YAAM,iBAAgB,0DAAe,WAAf,mBAAwB,OAAxB,mBAA4B;AAElD,UAAI,CAAC,eAAe;AAClB,wBAAgB,KACd;UACE,cAAc;UACd,aAAa;WAEf,OAAO;AAGT;MACF;AAEA,YAAM,gBAAgB,WAAW,UAAU,aAAa,EACrD,IAAI,MAAM,QAAQ,QAAQ,EAC1B,SAAQ;AAEX,qBAAe,iBAAiB,aAAa;AAE7C,YAAM,sBAAsB,eAAe,qBACzCA,QAAM,mBACN,YAAY,OAAO;AAGrB,UAAI,qBAAqB;AACvB,QAAAA,QAAM,aAAa;MACrB,OAAO;AACL,QAAAA,QAAM,aAAa;AACnB,uBAAe,sBAAqB;MACtC;IACF,SAAS,OAAO;AACd,MAAAA,QAAM,eAAe;AACrB,MAAAA,QAAM,aAAa;IACrB;EACF;;EAGA,MAAM,iBAAc;AAClB,UAAM,EAAE,iBAAiB,gBAAe,IAAK,eAAe,UAAS;AACrE,UAAM,cAAcA,QAAM;AAC1B,UAAM,UAAUA,QAAM;AAEtB,QAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,eAAe,CAAC,WAAWA,QAAM,cAAc;AAC1F,aAAO;IACT;AAEA,QAAI;AACF,MAAAA,QAAM,0BAA0B;AAChC,YAAM,eAAe,MAAM,YAAY,mBAAmB;QACxD,aAAa;QACb,cAAc,YAAY;QAC1B,mBAAmBA,QAAM;QACzB,qBAAqB,YAAY;OAClC;AAED,UAAI,cAA6C;AAEjD,UAAI,cAAc;AAChB,sBAAc,MAAM,eAAe,sBAAqB;MAC1D,OAAO;AACL,sBAAc,MAAM,eAAe,2BAA0B;MAC/D;AAEA,MAAAA,QAAM,0BAA0B;AAChC,MAAAA,QAAM,aAAa;AAEnB,aAAO;IACT,SAAS,OAAO;AACd,uBAAiB,OAAM;AACvB,sBAAgB,UAAU,2BAA2B;AACrD,MAAAA,QAAM,0BAA0B;AAChC,MAAAA,QAAM,sBAAsB;AAC5B,MAAAA,QAAM,kBAAkB;AACxB,MAAAA,QAAM,aAAa;AAEnB,aAAO;IACT;EACF;EAEA,MAAM,6BAA0B;AAC9B,UAAM,EAAE,iBAAiB,oBAAoB,eAAc,IAAK,eAAe,UAAS;AAExF,QAAI,CAAC,mBAAmB,CAAC,gBAAgB;AACvC,aAAO;IACT;AAEA,QAAI,CAAC,oBAAoB;AACvB,YAAM,IAAI,MAAM,6DAA6D;IAC/E;AAEA,QAAI;AACF,YAAM,WAAW,MAAM,wBAAwB,wBAAwB;QACrE,MAAM;QACN,IAAI;QACJ,aAAa;OACd;AACD,YAAM,cAAc;QAClB,MAAM,SAAS,GAAG;QAClB,IAAI,eAAe,gBAAgB,SAAS,GAAG,IAAI;QACnD,UAAU,OAAO,SAAS,GAAG,OAAO,QAAQ;QAC5C,OAAO,OAAO,SAAS,GAAG,KAAK;QAC/B,UAAUA,QAAM;;AAElB,MAAAA,QAAM,kBAAkB;AACxB,MAAAA,QAAM,sBAAsB;QAC1B,MAAM,YAAY;QAClB,IAAI,YAAY;QAChB,UAAU,YAAY;QACtB,OAAO,YAAY;QACnB,UAAU,YAAY;;AAGxB,aAAO;QACL,MAAM,YAAY;QAClB,IAAI,YAAY;QAChB,UAAU,YAAY;QACtB,OAAO,YAAY;QACnB,UAAU,YAAY;;IAE1B,SAAS,OAAO;AACd,uBAAiB,OAAM;AACvB,sBAAgB,UAAU,uCAAuC;AACjE,MAAAA,QAAM,sBAAsB;AAC5B,MAAAA,QAAM,kBAAkB;AACxB,MAAAA,QAAM,aAAa;AAEnB,aAAO;IACT;EACF;EAEA,MAAM,wBAAqB;AAlpB7B;AAmpBI,UAAM,EAAE,gBAAgB,iBAAiB,kBAAiB,IAAK,eAAe,UAAS;AACvF,UAAM,cAAcA,QAAM;AAC1B,UAAM,UAAUA,QAAM;AAEtB,QAAI,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,eAAe,CAAC,SAAS;AACtE,aAAO;IACT;AAEA,UAAM,UAAS,0BAAqB,WAClC,mBACA,YAAY,QAAQ,MAFP,mBAGZ;AAEH,QAAI;AACF,YAAM,WAAW,MAAM,wBAAwB,qBAAqB;QAClE,aAAa;QACb,MAAM,YAAY;QAClB,IAAI,QAAQ;QACZ;QACA,iBAAiB;OAClB;AAED,YAAM,8BAA8B,YAAY,YAAY;AAE5D,YAAM,MAAM,OAAO,SAAS,GAAG,OAAO,GAAG;AACzC,YAAM,WAAW,OAAO,SAAS,GAAG,OAAO,QAAQ;AAEnD,YAAM,cAAc;QAClB,MAAM,SAAS,GAAG;QAClB,IAAI,eAAe,gBAAgB,SAAS,GAAG,EAAE;QACjD;QACA;QACA,OAAO,8BAA8B,OAAO,UAAU,GAAG,IAAI,OAAO,GAAG;QACvE,UAAUA,QAAM;;AAGlB,MAAAA,QAAM,gBAAgB,oBAAoB,iBAAiBA,QAAM,cAAc,KAAK,QAAQ;AAC5F,MAAAA,QAAM,sBAAsB;AAC5B,MAAAA,QAAM,kBAAkB;AAExB,aAAO;IACT,SAAS,OAAO;AACd,uBAAiB,OAAM;AACvB,sBAAgB,UAAU,8BAA8B;AACxD,MAAAA,QAAM,sBAAsB;AAC5B,MAAAA,QAAM,kBAAkB;AACxB,MAAAA,QAAM,aAAa;AAEnB,aAAO;IACT;EACF;;EAGA,MAAM,2BAA2B,MAAuB;AAxsB1D,gBAAAE,KAAA;AAysBI,UAAM,EAAE,aAAa,gBAAe,IAAK,eAAe,UAAS;AAEjE,IAAAF,QAAM,6BAA6B;AACnC,UAAM,sBAAsB;AAE5B,QAAI,iBAAiB;AACnB,uBAAiB,qBAAqB;QACpC,YAAS;AACP,0BAAgB,YAAY,mBAAmB;QACjD;OACD;IACH,OAAO;AACL,sBAAgB,YAAY,mBAAmB;IACjD;AAEA,QAAI;AACF,YAAM,qBAAqB,gBAAgB;QACzC,SAAS;QACT,IAAI,KAAK;QACT,MAAM,KAAK;QACX,OAAO,KAAK;QACZ,gBAAgB;OACjB;AAED,YAAM,eAAe,WAAU;AAC/B,YAAM,eAAe,eAAc;AACnC,MAAAA,QAAM,sBAAsB;AAC5B,MAAAA,QAAM,6BAA6B;IACrC,SAAS,KAAK;AACZ,YAAM,QAAQ;AACd,MAAAA,QAAM,mBAAmB,+BAAO;AAChC,MAAAA,QAAM,6BAA6B;AACnC,sBAAgB,WAAU,+BAAO,iBAAgB,mBAAmB;AACpE,uBAAiB,UAAU;QACzB,MAAM;QACN,OAAO;QACP,YAAY;UACV,UAAS,+BAAO,kBAAgB,+BAAO,YAAW;UAClD,WAAS,qBAAgB,MAAM,sBAAtB,mBAAyC,kBAAiB;UACnE,iBAAe,oBAAe,MAAM,gBAArB,mBAAkC,WAAU;UAC3D,eAAaE,MAAA,eAAe,MAAM,YAArB,gBAAAA,IAA8B,WAAU;UACrD,gBAAgB,eAAe,MAAM,qBAAqB;UAC1D,cAAc,eAAe,MAAM,iBAAiB;UACpD,kBACE,uBAAkB,MAAM,0BAAxB,mBAA+C,YAC/C,qBAAqB,cAAc;;OAExC;IACH;EACF;EAEA,MAAM,uBAAuB,MAAmC;AA5vBlE,gBAAAA,KAAA,IAAAC,KAAA,YAAAC,KAAA,gBAAAC;AA6vBI,QAAI,CAAC,MAAM;AACT,aAAO;IACT;AAEA,UAAM,EAAE,aAAa,eAAe,gBAAe,IAAK,eAAe,UAAS;AAEhF,IAAAL,QAAM,qBAAqB;AAE3B,UAAM,yBAAyB,aAC7B,KAAAA,QAAM,gBAAN,mBAAmB,MACrB,OAAO,WAAW,0BAA0B,eAAe,CAAC,CAAC,KAAI,KAAAA,QAAM,YAAN,mBAAe,MAAM;AACtF,UAAM,yBAAyB,YAC7BE,MAAAF,QAAM,gBAAN,gBAAAE,IAAmB,MACrB,OAAO,WAAW,0BAA0B,eAAe,CAAC,CAAC,KAAI,KAAAF,QAAM,YAAN,mBAAe,MAAM;AAEtF,QAAI,iBAAiB;AACnB,uBAAiB,qBAAqB;QACpC,YAAS;AACP,2BAAiB,QAAQ,SAAS;AAClC,0BAAgB,YAAY,sBAAsB;AAClD,UAAAC,aAAW,WAAU;QACvB;OACD;IACH,OAAO;AACL,sBAAgB,YAAY,oCAAoC;IAClE;AAEA,QAAI;AACF,YAAM,uBAAuB,EAACE,MAAAH,QAAM,gBAAN,gBAAAG,IAAmB,UAAS,KAAAH,QAAM,YAAN,mBAAe,OAAO,EAAE,KAAK,GAAG;AAC1F,YAAM,kBAAkB,MAAM,qBAAqB,gBAAgB;QACjE,SAAS;QACT,IAAI,KAAK;QACT,MAAM,KAAK;QACX,OAAO,KAAK;QACZ,gBAAgB;OACjB;AAED,MAAAA,QAAM,qBAAqB;AAC3B,sBAAgB,YAAY,sBAAsB;AAClD,uBAAiB,UAAU;QACzB,MAAM;QACN,OAAO;QACP,YAAY;UACV,WAAS,qBAAgB,MAAM,sBAAtB,mBAAyC,kBAAiB;UACnE,iBAAe,oBAAe,MAAM,gBAArB,mBAAkC,WAAU;UAC3D,eAAaI,MAAA,eAAe,MAAM,YAArB,gBAAAA,IAA8B,WAAU;UACrD,gBAAgB,eAAe,MAAM,qBAAqB;UAC1D,cAAc,eAAe,MAAM,iBAAiB;UACpD,kBACE,uBAAkB,MAAM,0BAAxB,mBAA+C,YAC/C,qBAAqB,cAAc;;OAExC;AACD,MAAAH,aAAW,WAAU;AACrB,UAAI,CAAC,iBAAiB;AACpB,yBAAiB,QAAQ,SAAS;MACpC;AACA,MAAAA,aAAW,uBAAuB,oBAAoB;AAEtD,aAAO;IACT,SAAS,KAAK;AACZ,YAAM,QAAQ;AACd,MAAAD,QAAM,mBAAmB,+BAAO;AAChC,MAAAA,QAAM,qBAAqB;AAC3B,sBAAgB,WAAU,+BAAO,iBAAgB,mBAAmB;AACpE,uBAAiB,UAAU;QACzB,MAAM;QACN,OAAO;QACP,YAAY;UACV,UAAS,+BAAO,kBAAgB,+BAAO,YAAW;UAClD,WAAS,qBAAgB,MAAM,sBAAtB,mBAAyC,kBAAiB;UACnE,iBAAe,oBAAe,MAAM,gBAArB,mBAAkC,WAAU;UAC3D,eAAa,oBAAe,MAAM,YAArB,mBAA8B,WAAU;UACrD,gBAAgB,eAAe,MAAM,qBAAqB;UAC1D,cAAc,eAAe,MAAM,iBAAiB;UACpD,kBACEK,MAAA,kBAAkB,MAAM,0BAAxB,gBAAAA,IAA+C,YAC/C,qBAAqB,cAAc;;OAExC;AAED,aAAO;IACT;EACF;;EAGA,qBAAqB,mBAA2B,oBAA0B;AACxE,UAAM,mCAAmC,oBAAoB,iCAC3D,mBACA,oBACAL,QAAM,mBAAmB;AAG3B,WAAO;EACT;;EAGA,wBAAqB;AACnB,UAAM,EAAE,gBAAgB,gBAAe,IAAK,eAAe,UAAS;AAEpE,QAAI,CAAC,kBAAkB,CAAC,iBAAiB;AACvC;IACF;AAEA,IAAAA,QAAM,gBAAgB,oBAAoB,iBACxCA,QAAM,cACN,OAAOA,QAAM,MAAM,GACnB,OAAO,iBAAiB,CAAC;AAE3B,IAAAA,QAAM,cAAc,oBAAoB,eAAe;MACrD,mBAAmBA,QAAM;MACzB,uBAAuBA,QAAM;MAC7B,mBAAmBA,QAAM;MACzB,eAAeA,QAAM;KACtB;AACD,IAAAA,QAAM,cAAc,oBAAoB,eAAeA,QAAM,UAAUA,QAAM,aAAa;AAC1F,IAAAA,QAAM,cAAc,oBAAoB,eAAeA,QAAM,iBAAiB;EAChF;;AAIK,IAAM,iBAAiB,kBAAkBC,YAAU;;;AC/1B1D,IAAMK,UAAQ,MAA8B;EAC1C,SAAS;EACT,MAAM;EACN,aAAa;IACX,OAAO;IACP,QAAQ;IACR,KAAK;IACL,MAAM;;EAER,SAAS;CACV;AAGD,IAAMC,eAAa;EACjB,OAAAD;EAEA,UAAU,UAAoD;AAC5D,WAAO,UAAIA,SAAO,MAAM,SAASA,OAAK,CAAC;EACzC;EAEA,aAAiC,KAAQ,UAAoD;AAC3F,WAAO,aAAOA,SAAO,KAAK,QAAQ;EACpC;EAEA,YAAY,EACV,SACA,aACA,QAAO,GAKR;AACC,IAAAA,QAAM,OAAO;AACb,IAAAA,QAAM,UAAU;AAChB,IAAAA,QAAM,cAAc;AACpB,IAAAA,QAAM,UAAU;EAClB;EAEA,OAAI;AACF,IAAAA,QAAM,OAAO;AACb,IAAAA,QAAM,UAAU;AAChB,IAAAA,QAAM,cAAc;MAClB,OAAO;MACP,QAAQ;MACR,KAAK;MACL,MAAM;;EAEV;;AAIK,IAAM,oBAAoB,kBAAkBC,YAAU;;;AC3E7D,IAAM,aAAa;AAEZ,IAAM,UAAU;EACrB,4BAA4B,SAAe;AACzC,QAAI,WAAW,YAAY;AACzB,YAAM,IAAI,MAAM,iBAAiB;IACnC;AAEA,YAAQ,aAAa,aAAa;EACpC;;;;ACqBF,IAAMC,UAAQ,MAA0B;EACtC,aAAa,CAAA;EACb,SAAS;CACV;AAGD,IAAMC,eAAa;EACjB,OAAAD;EAEA,UAAU,UAAgD;AACxD,WAAO,UAAIA,SAAO,MAAM,SAASA,OAAK,CAAC;EACzC;EAEA,aAAiC,KAAQ,UAAgD;AACvF,WAAO,aAAOA,SAAO,KAAK,QAAQ;EACpC;EAEA,MAAM,YAAY,MAAY;AA/ChC;AAgDI,QAAI;AACF,aAAO,MAAM,wBAAwB,cAAc,IAAI;IACzD,SAASE,IAAG;AACV,YAAM,QAAQA;AACd,YAAM,IAAI,QAAM,0CAAO,YAAP,mBAAiB,OAAjB,mBAAqB,gBAAe,sBAAsB;IAC5E;EACF;EAEA,MAAM,iBAAiB,MAAY;AACjC,QAAI;AACF,YAAM,wBAAwB,cAAc,IAAI;AAEhD,aAAO;IACT,QAAQ;AACN,aAAO;IACT;EACF;EAEA,MAAM,eAAe,OAAa;AAChC,QAAI;AACF,MAAAF,QAAM,UAAU;AAChB,MAAAA,QAAM,cAAc,CAAA;AACpB,YAAM,WAAW,MAAM,wBAAwB,sBAAsB,KAAK;AAC1E,MAAAA,QAAM,cACJ,SAAS,YAAY,IAAI,iBAAe;QACtC,GAAG;QACH,MAAM,WAAW;QACjB,KAAK,CAAA;AAET,aAAOA,QAAM;IACf,SAASE,IAAG;AACV,YAAM,eAAe,cAAc,iBAAiBA,IAAG,iCAAiC;AACxF,YAAM,IAAI,MAAM,YAAY;IAC9B;AACE,MAAAF,QAAM,UAAU;IAClB;EACF;EAEA,MAAM,mBAAmB,SAAe;AACtC,QAAI;AACF,YAAM,UAAU,gBAAgB,MAAM;AACtC,UAAI,CAAC,SAAS;AACZ,eAAO,CAAA;MACT;AACA,YAAM,YAAY,YAAY,0BAA0B,OAAO;AAC/D,UAAI,WAAW;AACb,eAAO;MACT;AAEA,YAAM,WAAW,MAAM,wBAAwB,qBAAqB,EAAE,QAAO,CAAE;AAE/E,kBAAY,eAAe;QACzB;QACA,KAAK;QACL,WAAW,KAAK,IAAG;OACpB;AAED,aAAO;IACT,SAASE,IAAG;AACV,YAAM,eAAe,cAAc,iBAAiBA,IAAG,kCAAkC;AACzF,YAAM,IAAI,MAAM,YAAY;IAC9B;EACF;EAEA,MAAM,aAAa,MAAe;AAChC,UAAM,UAAU,gBAAgB,MAAM;AACtC,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,mBAAmB;IACrC;AAEA,UAAM,UAAU,kBAAkB,MAAM;AACxC,UAAM,iBAAiB,oBAAoB,iBAAgB;AAC3D,QAAI,CAAC,WAAW,CAAC,gBAAgB;AAC/B,YAAM,IAAI,MAAM,qCAAqC;IACvD;AAEA,IAAAF,QAAM,UAAU;AAEhB,QAAI;AACF,YAAM,UAAU,KAAK,UAAU;QAC7B;QACA,YAAY,CAAA;;QAEZ,WAAW,KAAK,MAAM,KAAK,IAAG,IAAK,GAAI;OACxC;AAED,uBAAiB,qBAAqB;QACpC,WAAQ;AACN,2BAAiB,QAAQ,qBAAqB;QAChD;OACD;AAED,YAAM,YAAY,MAAM,qBAAqB,YAAY,OAAO;AAChE,MAAAA,QAAM,UAAU;AAChB,YAAM,YAAY,QAAQ;AAE1B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,mBAAmB;MACrC;AAEA,YAAM,WAAW,QAAQ,4BAA4B,OAAO,SAAS,CAAC;AACtE,YAAM,wBAAwB,gBAAgB;QAC5C;QACA;QACA;QACA;OACD;AAED,wBAAkB,eAAe,MAAM,QAAQ,cAAc;AAC7D,uBAAiB,QAAQ,4BAA4B;IACvD,SAASE,IAAG;AACV,YAAM,eAAe,cAAc,iBAAiBA,IAAG,0BAA0B,IAAI,EAAE;AACvF,uBAAiB,QAAQ,qBAAqB;AAC9C,YAAM,IAAI,MAAM,YAAY;IAC9B;AACE,MAAAF,QAAM,UAAU;IAClB;EACF;EACA,aAAa,MAAY;AACvB,WAAO,sBAAsB,KAAK,IAAI;EACxC;EACA,iBAAiB,OAAgB,cAAoB;AAzKvD;AA0KI,UAAM,WAAW;AAEjB,aAAO,gDAAU,YAAV,mBAAoB,OAApB,mBAAwB,gBAAe;EAChD;;AAIK,IAAM,gBAAgB,kBAAkBC,YAAU;;;ACtKzD,IAAME,UAAQ,MAAmC;EAC/C,wBAAwB;CACzB;;;ACPD,IAAI,WAAyC;AAC7C,IAAI,cAA4C;AAChD,IAAI,eAA6C;AAE3C,SAAU,kBAAkB,gBAAiC,WAAqB;AACtF,aAAW,SAAS,cAAc,OAAO;AACzC,gBAAc,SAAS,cAAc,OAAO;AAC5C,iBAAe,SAAS,cAAc,OAAO;AAC7C,WAAS,cAAc,iBAAiB,cAAc,EAAE,KAAK;AAC7D,cAAY,cAAc,iBAAiB,cAAc,EAAE,KAAK;AAChE,eAAa,cAAc,iBAAiB,cAAc,EAAE,MAAM;AAClE,WAAS,KAAK,YAAY,QAAQ;AAClC,WAAS,KAAK,YAAY,WAAW;AACrC,WAAS,KAAK,YAAY,YAAY;AACtC,gBAAc,SAAS;AACzB;AAEM,SAAU,cAAc,WAAkB;AAC9C,MAAI,eAAe,cAAc;AAC/B,QAAI,cAAc,SAAS;AACzB,kBAAY,gBAAgB,OAAO;AACnC,mBAAa,QAAQ;IACvB,OAAO;AACL,mBAAa,gBAAgB,OAAO;AACpC,kBAAY,QAAQ;IACtB;EACF;AACF;AAEM,SAAU,kBAAkB,gBAA8B;AAC9D,MAAI,YAAY,eAAe,cAAc;AAC3C,aAAS,cAAc,iBAAiB,cAAc,EAAE,KAAK;AAC7D,gBAAY,cAAc,iBAAiB,cAAc,EAAE,KAAK;AAChE,iBAAa,cAAc,iBAAiB,cAAc,EAAE,MAAM;EACpE;AACF;AAEA,SAAS,iBAAiB,gBAA+B;AACvD,SAAO;IACL,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAoD0B,WAC1B,iDAAiB,+BACb,GAAG,eAAe,0BAA0B,CAAC,MAC7C,IAAI,CACT;6BACoB,WACnB,iDAAiB,yBACf,wGAAwG,CAC3G;kCACyB,WAAU,iDAAiB,8BAA6B,MAAM,CAAC;sCAC3D,WAC5B,iDAAiB,kCAAiC,KAAK,CACxD;yBACgB,WAAU,iDAAiB,qBAAoB,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2axE,OAAO;;2BAEgB,WAAU,iDAAiB,uBAAsB,MAAM,CAAC;wBAC3D,UAAU,qBAAqB,gBAAgB,MAAM,EAAE,cAAc,CAAC,CAAC;;;qCAG1D,UAC3B,qBAAqB,gBAAgB,MAAM,EAAE,kBAAkB,CAAC,CACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqGL,MAAM;;2BAEiB,WAAU,iDAAiB,uBAAsB,MAAM,CAAC;wBAC3D,UAAU,qBAAqB,gBAAgB,OAAO,EAAE,cAAc,CAAC,CAAC;;;qCAG3D,UAC3B,qBAAqB,gBAAgB,OAAO,EAAE,kBAAkB,CAAC,CAClE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGT;AAGO,IAAM,cAAc;;;;;;;;;;;;;;;;;AAkBpB,IAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDtB,IAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9zBpB,IAAM,eAAe;EAC1B,iBAAiB,SAAsC,OAAa;AAClE,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,aAAO,QAAQ,KAAK,IAAI,qBAAqB,QAAQ,KAAK,CAAC,MAAM;IACnE,WAAW,OAAO,YAAY,UAAU;AACtC,aAAO,qBAAqB,OAAO;IACrC;AAEA,WAAO;EACT;EAEA,iBAAiB,MAAU;AACzB,WAAO,IAAI,KAAK,eAAe,SAAS,EAAE,OAAO,SAAS,KAAK,UAAS,CAAE,EAAE,OAAO,IAAI;EACzF;EAEA,YAAY,KAAW;AACrB,QAAI;AACF,YAAM,SAAS,IAAI,IAAI,GAAG;AAE1B,aAAO,OAAO;IAChB,SAAS,OAAO;AACd,aAAO;IACT;EACF;EAEA,kBAAkB,EAAE,QAAQ,YAAY,UAAU,SAAQ,GAAmB;AAC3E,QAAI,OAAO,UAAU,aAAa,UAAU;AAC1C,aAAO;IACT;AAEA,QAAI,aAAa,OAAO;AACtB,aAAO,GAAG,OAAO,UAAU,GAAG,UAAU,CAAC;IAC3C,WAAW,aAAa,SAAS;AAC/B,aAAO,MAAM,OAAO,UAAU,OAAO,SAAS,QAAQ,CAAC;IACzD;AAEA,WAAO,GAAG,OAAO,UAAU,GAAG,KAAK,MAAM,UAAU,CAAC,CAAC,MAAM,OAAO,UAChE,OAAO,SAAS,KAAK,MAAM,QAAQ,CAAC,CACrC;EACH;EAEA,qBAAqB,SAAe;AAClC,UAAM,OAAO,QACV,YAAW,EACX,QAAQ,SAAS,EAAE,EACnB,QAAQ,eAAe,EAAE;AAC5B,UAAM,YAAY,KAAK,UAAU,GAAG,CAAC,EAAE,OAAO,GAAG,GAAG;AACpD,UAAM,WAAW,KAAK,SAAS,SAAS;AACxC,UAAM,qBAAqB,iBAAiB,SAAS,eAAe,EAAE,iBACpE,4BAA4B;AAE9B,UAAM,SAAS,OAAO,yDAAoB,QAAQ,MAAM,GAAG;AAC3D,UAAM,OAAO,MAAM,IAAI;AAEvB,UAAM,iBAAiB,GAAG,IAAI,KAAK,IAAI;AAEvC,UAAM,SAAmB,CAAA;AAEzB,aAASC,KAAI,GAAGA,KAAI,GAAGA,MAAK,GAAG;AAC7B,YAAM,cAAc,KAAK,UAAU,UAAU,OAAOA,EAAC;AACrD,aAAO,KAAK,OAAO,YAAY,CAAC,CAAC,KAAK,YAAY,CAAC,CAAC,KAAK,YAAY,CAAC,CAAC,GAAG;IAC5E;AAEA,WAAO;uBACY,OAAO,CAAC,CAAC;uBACT,OAAO,CAAC,CAAC;uBACT,OAAO,CAAC,CAAC;uBACT,OAAO,CAAC,CAAC;uBACT,OAAO,CAAC,CAAC;6BACH,cAAc;;EAEzC;EAEA,SAAS,KAAW;AAClB,UAAM,SAAS,SAAS,KAAK,EAAE;AAE/B,UAAMC,KAAK,UAAU,KAAM;AAC3B,UAAM,IAAK,UAAU,IAAK;AAC1B,UAAM,IAAI,SAAS;AAEnB,WAAO,CAACA,IAAG,GAAG,CAAC;EACjB;EAEA,UAAU,KAA+B,MAAY;AACnD,UAAM,CAACA,IAAG,GAAG,CAAC,IAAI;AAClB,UAAM,UAAU,KAAK,MAAMA,MAAK,MAAMA,MAAK,IAAI;AAC/C,UAAM,UAAU,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAC/C,UAAM,UAAU,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAE/C,WAAO,CAAC,SAAS,SAAS,OAAO;EACnC;EAEA,SAAS,WAAiB;AACxB,UAAM,QAAQ;MACZ,QAAQ;;AAGV,WAAO,MAAM,OAAO,KAAK,SAAS;EACpC;EAEA,cAAc,OAA4B;AApG5C;AAqGI,QAAI,OAAO;AACT,aAAO;IACT,WAAW,OAAO,WAAW,eAAe,OAAO,YAAY;AAC7D,WAAI,YAAO,WAAW,8BAA8B,MAAhD,mBAAmD,SAAS;AAC9D,eAAO;MACT;AAEA,aAAO;IACT;AAEA,WAAO;EACT;EACA,aAAa,OAAa;AACxB,UAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,QAAI,MAAM,WAAW,GAAG;AACtB,aAAO,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;IAC5B;AAEA,WAAO,CAAC,KAAK,IAAI;EACnB;EACA,YAAY,QAAgB,WAAmB,OAAa;AAC1D,UAAM,gBACJ,OAAO,SAAQ,EAAG,UAAU,YAAY,OAAO,MAAM,EAAE,QAAQ,KAAK,IAAI;AAE1E,WAAO;EACT;EAOA,0BAA0B,OAAoC,WAAW,GAAC;AACxE,QAAI,UAAU,QAAW;AACvB,aAAO;IACT;AAEA,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,MAAM,eAAe,SAAS;QACnC,uBAAuB;QACvB,uBAAuB;OACxB;IACH;AAEA,WAAO,WAAW,KAAK,EAAE,eAAe,SAAS;MAC/C,uBAAuB;MACvB,uBAAuB;KACxB;EACH;;;;ACjIF,SAAS,sBAAsB,SAAiB,YAA2B;AACzE,QAAM,EAAE,MAAM,SAAQ,IAAK;AAE3B,SAAO;IACL;IACA;IACA,SAAS,OAA+B;AACtC,UAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,uBAAe,OAAO,SAAS,KAAK;MACtC;IACF;;AAEJ;AAEA,SAAS,oBAAoB,SAAiB,OAA+B;AAC3E,MAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,mBAAe,OAAO,SAAS,KAAK;EACtC;AAEA,SAAO;AACT;AAEM,SAAU,cAAc,SAAe;AAC3C,SAAO,SAASC,QAAO,mBAA6D;AAClF,WAAO,OAAO,sBAAsB,aAChC,oBAAoB,SAAS,iBAAiB,IAC9C,sBAAsB,SAAS,iBAAiB;EACtD;AACF;;;ACjDO,IAAMC,iBAAgB;EAC3B,cAAc,CAAC,EAAE,OAAO,SAAQ,GAAI,EAAE,OAAO,OAAM,GAAI,EAAE,OAAO,WAAU,CAAE;EAC5E,qBAEG,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,cACtD,QAAQ,IAAI,gCAAgC,IAC5C,WAAc;EACpB,gBAAgB;IACd,MAAM;IACN,MAAM;;EAER,8BAA8B,CAAC,SAAS,UAAU,QAAQ;EAC1D,qBAAqB;IACnB,YAAY;IACZ,aAAa;IACb,gBAAgB;;;", "names": ["t", "e", "n", "r", "i", "s", "u", "o", "c", "f", "h", "d", "l", "y", "M", "m", "S", "a", "O", "$", "v", "g", "D", "k", "e", "n", "t", "r", "e", "t", "n", "o", "i", "d", "u", "f", "s", "l", "h", "m", "c", "y", "M", "e", "n", "t", "o", "dayjs", "relativeTime", "updateLocale", "englishLocale", "dayjs", "Big", "n", "e", "i", "xc", "s", "y", "yc", "k", "l", "bt", "ri", "ai", "r", "q", "qc", "qi", "t", "xe", "ye", "c", "e", "Constants<PERSON><PERSON>", "ua", "Constants<PERSON><PERSON>", "e", "_c", "c", "r", "version", "TRACK_MEMO_SYMBOL", "Symbol", "GET_ORIGINAL_SYMBOL", "getProto", "Object", "getPrototypeOf", "objectsToTrack", "WeakMap", "isObjectToTrack", "obj", "has", "get", "prototype", "Array", "getUntracked", "obj", "isObjectToTrack", "GET_ORIGINAL_SYMBOL", "markToTrack", "mark", "objectsToTrack", "set", "version", "e", "entries", "Constants<PERSON><PERSON>", "s", "Constants<PERSON><PERSON>", "baseUrl", "state", "controller", "state", "state", "_c", "DEFAULT_STATE", "state", "controller", "state", "st", "e", "from", "to", "Constants<PERSON><PERSON>", "state", "controller", "accountState", "Constants<PERSON><PERSON>", "NetworkUtil", "c", "state", "controller", "baseUrl", "api", "state", "state", "state", "controller", "NetworkUtil", "state", "controller", "_a", "_c", "state", "controller", "state", "controller", "c", "uc", "_c", "state", "controller", "state", "controller", "_c", "state", "controller", "_c", "Constants<PERSON><PERSON>", "to", "state", "controller", "n", "_c", "_a", "Constants<PERSON><PERSON>", "baseUrl", "api", "state", "d", "r", "i", "k", "f", "y", "h2", "h3", "ar", "br", "cr", "dr", "er", "rr", "sr", "tr", "getVersion", "BaseError", "getVersion", "fn", "size", "pad", "size", "i", "assertSize", "size", "SizeOverflowError", "pad", "size", "SizeExceedsPaddingSizeError", "fromHex", "fromHex", "size", "assertSize", "BaseError", "size", "padRight", "padRight", "size", "pad", "size", "BaseError", "BaseError", "size", "encoder", "i", "concat", "assertSize", "fromBytes", "i", "size", "fromString", "encoder", "size", "pad", "size", "BaseError", "size", "SizeOverflowError", "BaseError", "SizeExceedsPaddingSizeError", "BaseError", "size", "keccak256", "fromBytes", "LruMap", "size", "LruMap", "InvalidAddressError", "checksum", "keccak256", "i", "InvalidAddressError", "BaseError", "arrayRegex", "bytesRegex", "integerRegex", "maxUint256", "PositionOutOfBoundsError", "size", "BaseError", "PositionOutOfBoundsError", "encodePacked", "i", "concat", "encode", "fromString", "integerRegex", "size", "bytesRegex", "BytesSizeMismatchError", "arrayRegex", "BytesSizeMismatchError", "BaseError", "size", "BaseError", "_c", "state", "controller", "Constants<PERSON><PERSON>", "state", "controller", "_c", "_e", "_i", "_n", "state", "controller", "state", "controller", "e", "state", "i", "r", "create", "Constants<PERSON><PERSON>"]}