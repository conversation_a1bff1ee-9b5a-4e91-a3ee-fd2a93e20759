import {
  svg
} from "./chunk-KAPNL25W.js";
import "./chunk-4CFW2BUT.js";

// node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-left.js
var chevronLeftSvg = svg`<svg fill="none" viewBox="0 0 16 16">
  <path
    fill="currentColor"
    fill-rule="evenodd"
    d="M11.04 1.46a1 1 0 0 1 0 1.41L5.91 8l5.13 5.13a1 1 0 1 1-1.41 1.41L3.79 8.71a1 1 0 0 1 0-1.42l5.84-5.83a1 1 0 0 1 1.41 0Z"
    clip-rule="evenodd"
  />
</svg>`;
export {
  chevronLeftSvg
};
//# sourceMappingURL=chevron-left-BXJC5T3C.js.map
