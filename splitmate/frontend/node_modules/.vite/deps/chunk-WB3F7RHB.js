import {
  esm_exports,
  require_cjs,
  require_cjs2,
  require_cjs3
} from "./chunk-Z7G2EXAU.js";
import {
  css,
  unsafeCSS
} from "./chunk-KAPNL25W.js";
import {
  HashMD
} from "./chunk-DLPCIHFH.js";
import {
  <PERSON>ruMap,
  defineFormatter,
  hexToNumber,
  keccak_256,
  numberToHex
} from "./chunk-YH4ABYEH.js";
import {
  rotl,
  wrapConstructor
} from "./chunk-632RNAPF.js";
import {
  formatUnits
} from "./chunk-DS754V24.js";
import {
  require_events
} from "./chunk-UW7JWTTR.js";
import {
  __commonJS,
  __toESM
} from "./chunk-4CFW2BUT.js";

// node_modules/dayjs/dayjs.min.js
var require_dayjs_min = __commonJS({
  "node_modules/dayjs/dayjs.min.js"(exports, module) {
    !function(t2, e2) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = e2() : "function" == typeof define && define.amd ? define(e2) : (t2 = "undefined" != typeof globalThis ? globalThis : t2 || self).dayjs = e2();
    }(exports, function() {
      "use strict";
      var t2 = 1e3, e2 = 6e4, n2 = 36e5, r2 = "millisecond", i3 = "second", s2 = "minute", u2 = "hour", a = "day", o2 = "week", c2 = "month", f6 = "quarter", h4 = "year", d2 = "date", l3 = "Invalid Date", $ = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/, y4 = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g, M3 = { name: "en", weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"), months: "January_February_March_April_May_June_July_August_September_October_November_December".split("_"), ordinal: function(t3) {
        var e3 = ["th", "st", "nd", "rd"], n3 = t3 % 100;
        return "[" + t3 + (e3[(n3 - 20) % 10] || e3[n3] || e3[0]) + "]";
      } }, m2 = function(t3, e3, n3) {
        var r3 = String(t3);
        return !r3 || r3.length >= e3 ? t3 : "" + Array(e3 + 1 - r3.length).join(n3) + t3;
      }, v = { s: m2, z: function(t3) {
        var e3 = -t3.utcOffset(), n3 = Math.abs(e3), r3 = Math.floor(n3 / 60), i4 = n3 % 60;
        return (e3 <= 0 ? "+" : "-") + m2(r3, 2, "0") + ":" + m2(i4, 2, "0");
      }, m: function t3(e3, n3) {
        if (e3.date() < n3.date()) return -t3(n3, e3);
        var r3 = 12 * (n3.year() - e3.year()) + (n3.month() - e3.month()), i4 = e3.clone().add(r3, c2), s3 = n3 - i4 < 0, u3 = e3.clone().add(r3 + (s3 ? -1 : 1), c2);
        return +(-(r3 + (n3 - i4) / (s3 ? i4 - u3 : u3 - i4)) || 0);
      }, a: function(t3) {
        return t3 < 0 ? Math.ceil(t3) || 0 : Math.floor(t3);
      }, p: function(t3) {
        return { M: c2, y: h4, w: o2, d: a, D: d2, h: u2, m: s2, s: i3, ms: r2, Q: f6 }[t3] || String(t3 || "").toLowerCase().replace(/s$/, "");
      }, u: function(t3) {
        return void 0 === t3;
      } }, g = "en", D = {};
      D[g] = M3;
      var p = "$isDayjsObject", S2 = function(t3) {
        return t3 instanceof _ || !(!t3 || !t3[p]);
      }, w = function t3(e3, n3, r3) {
        var i4;
        if (!e3) return g;
        if ("string" == typeof e3) {
          var s3 = e3.toLowerCase();
          D[s3] && (i4 = s3), n3 && (D[s3] = n3, i4 = s3);
          var u3 = e3.split("-");
          if (!i4 && u3.length > 1) return t3(u3[0]);
        } else {
          var a2 = e3.name;
          D[a2] = e3, i4 = a2;
        }
        return !r3 && i4 && (g = i4), i4 || !r3 && g;
      }, O2 = function(t3, e3) {
        if (S2(t3)) return t3.clone();
        var n3 = "object" == typeof e3 ? e3 : {};
        return n3.date = t3, n3.args = arguments, new _(n3);
      }, b = v;
      b.l = w, b.i = S2, b.w = function(t3, e3) {
        return O2(t3, { locale: e3.$L, utc: e3.$u, x: e3.$x, $offset: e3.$offset });
      };
      var _ = function() {
        function M4(t3) {
          this.$L = w(t3.locale, null, true), this.parse(t3), this.$x = this.$x || t3.x || {}, this[p] = true;
        }
        var m3 = M4.prototype;
        return m3.parse = function(t3) {
          this.$d = function(t4) {
            var e3 = t4.date, n3 = t4.utc;
            if (null === e3) return /* @__PURE__ */ new Date(NaN);
            if (b.u(e3)) return /* @__PURE__ */ new Date();
            if (e3 instanceof Date) return new Date(e3);
            if ("string" == typeof e3 && !/Z$/i.test(e3)) {
              var r3 = e3.match($);
              if (r3) {
                var i4 = r3[2] - 1 || 0, s3 = (r3[7] || "0").substring(0, 3);
                return n3 ? new Date(Date.UTC(r3[1], i4, r3[3] || 1, r3[4] || 0, r3[5] || 0, r3[6] || 0, s3)) : new Date(r3[1], i4, r3[3] || 1, r3[4] || 0, r3[5] || 0, r3[6] || 0, s3);
              }
            }
            return new Date(e3);
          }(t3), this.init();
        }, m3.init = function() {
          var t3 = this.$d;
          this.$y = t3.getFullYear(), this.$M = t3.getMonth(), this.$D = t3.getDate(), this.$W = t3.getDay(), this.$H = t3.getHours(), this.$m = t3.getMinutes(), this.$s = t3.getSeconds(), this.$ms = t3.getMilliseconds();
        }, m3.$utils = function() {
          return b;
        }, m3.isValid = function() {
          return !(this.$d.toString() === l3);
        }, m3.isSame = function(t3, e3) {
          var n3 = O2(t3);
          return this.startOf(e3) <= n3 && n3 <= this.endOf(e3);
        }, m3.isAfter = function(t3, e3) {
          return O2(t3) < this.startOf(e3);
        }, m3.isBefore = function(t3, e3) {
          return this.endOf(e3) < O2(t3);
        }, m3.$g = function(t3, e3, n3) {
          return b.u(t3) ? this[e3] : this.set(n3, t3);
        }, m3.unix = function() {
          return Math.floor(this.valueOf() / 1e3);
        }, m3.valueOf = function() {
          return this.$d.getTime();
        }, m3.startOf = function(t3, e3) {
          var n3 = this, r3 = !!b.u(e3) || e3, f7 = b.p(t3), l4 = function(t4, e4) {
            var i4 = b.w(n3.$u ? Date.UTC(n3.$y, e4, t4) : new Date(n3.$y, e4, t4), n3);
            return r3 ? i4 : i4.endOf(a);
          }, $2 = function(t4, e4) {
            return b.w(n3.toDate()[t4].apply(n3.toDate("s"), (r3 ? [0, 0, 0, 0] : [23, 59, 59, 999]).slice(e4)), n3);
          }, y5 = this.$W, M5 = this.$M, m4 = this.$D, v2 = "set" + (this.$u ? "UTC" : "");
          switch (f7) {
            case h4:
              return r3 ? l4(1, 0) : l4(31, 11);
            case c2:
              return r3 ? l4(1, M5) : l4(0, M5 + 1);
            case o2:
              var g2 = this.$locale().weekStart || 0, D2 = (y5 < g2 ? y5 + 7 : y5) - g2;
              return l4(r3 ? m4 - D2 : m4 + (6 - D2), M5);
            case a:
            case d2:
              return $2(v2 + "Hours", 0);
            case u2:
              return $2(v2 + "Minutes", 1);
            case s2:
              return $2(v2 + "Seconds", 2);
            case i3:
              return $2(v2 + "Milliseconds", 3);
            default:
              return this.clone();
          }
        }, m3.endOf = function(t3) {
          return this.startOf(t3, false);
        }, m3.$set = function(t3, e3) {
          var n3, o3 = b.p(t3), f7 = "set" + (this.$u ? "UTC" : ""), l4 = (n3 = {}, n3[a] = f7 + "Date", n3[d2] = f7 + "Date", n3[c2] = f7 + "Month", n3[h4] = f7 + "FullYear", n3[u2] = f7 + "Hours", n3[s2] = f7 + "Minutes", n3[i3] = f7 + "Seconds", n3[r2] = f7 + "Milliseconds", n3)[o3], $2 = o3 === a ? this.$D + (e3 - this.$W) : e3;
          if (o3 === c2 || o3 === h4) {
            var y5 = this.clone().set(d2, 1);
            y5.$d[l4]($2), y5.init(), this.$d = y5.set(d2, Math.min(this.$D, y5.daysInMonth())).$d;
          } else l4 && this.$d[l4]($2);
          return this.init(), this;
        }, m3.set = function(t3, e3) {
          return this.clone().$set(t3, e3);
        }, m3.get = function(t3) {
          return this[b.p(t3)]();
        }, m3.add = function(r3, f7) {
          var d3, l4 = this;
          r3 = Number(r3);
          var $2 = b.p(f7), y5 = function(t3) {
            var e3 = O2(l4);
            return b.w(e3.date(e3.date() + Math.round(t3 * r3)), l4);
          };
          if ($2 === c2) return this.set(c2, this.$M + r3);
          if ($2 === h4) return this.set(h4, this.$y + r3);
          if ($2 === a) return y5(1);
          if ($2 === o2) return y5(7);
          var M5 = (d3 = {}, d3[s2] = e2, d3[u2] = n2, d3[i3] = t2, d3)[$2] || 1, m4 = this.$d.getTime() + r3 * M5;
          return b.w(m4, this);
        }, m3.subtract = function(t3, e3) {
          return this.add(-1 * t3, e3);
        }, m3.format = function(t3) {
          var e3 = this, n3 = this.$locale();
          if (!this.isValid()) return n3.invalidDate || l3;
          var r3 = t3 || "YYYY-MM-DDTHH:mm:ssZ", i4 = b.z(this), s3 = this.$H, u3 = this.$m, a2 = this.$M, o3 = n3.weekdays, c3 = n3.months, f7 = n3.meridiem, h5 = function(t4, n4, i5, s4) {
            return t4 && (t4[n4] || t4(e3, r3)) || i5[n4].slice(0, s4);
          }, d3 = function(t4) {
            return b.s(s3 % 12 || 12, t4, "0");
          }, $2 = f7 || function(t4, e4, n4) {
            var r4 = t4 < 12 ? "AM" : "PM";
            return n4 ? r4.toLowerCase() : r4;
          };
          return r3.replace(y4, function(t4, r4) {
            return r4 || function(t5) {
              switch (t5) {
                case "YY":
                  return String(e3.$y).slice(-2);
                case "YYYY":
                  return b.s(e3.$y, 4, "0");
                case "M":
                  return a2 + 1;
                case "MM":
                  return b.s(a2 + 1, 2, "0");
                case "MMM":
                  return h5(n3.monthsShort, a2, c3, 3);
                case "MMMM":
                  return h5(c3, a2);
                case "D":
                  return e3.$D;
                case "DD":
                  return b.s(e3.$D, 2, "0");
                case "d":
                  return String(e3.$W);
                case "dd":
                  return h5(n3.weekdaysMin, e3.$W, o3, 2);
                case "ddd":
                  return h5(n3.weekdaysShort, e3.$W, o3, 3);
                case "dddd":
                  return o3[e3.$W];
                case "H":
                  return String(s3);
                case "HH":
                  return b.s(s3, 2, "0");
                case "h":
                  return d3(1);
                case "hh":
                  return d3(2);
                case "a":
                  return $2(s3, u3, true);
                case "A":
                  return $2(s3, u3, false);
                case "m":
                  return String(u3);
                case "mm":
                  return b.s(u3, 2, "0");
                case "s":
                  return String(e3.$s);
                case "ss":
                  return b.s(e3.$s, 2, "0");
                case "SSS":
                  return b.s(e3.$ms, 3, "0");
                case "Z":
                  return i4;
              }
              return null;
            }(t4) || i4.replace(":", "");
          });
        }, m3.utcOffset = function() {
          return 15 * -Math.round(this.$d.getTimezoneOffset() / 15);
        }, m3.diff = function(r3, d3, l4) {
          var $2, y5 = this, M5 = b.p(d3), m4 = O2(r3), v2 = (m4.utcOffset() - this.utcOffset()) * e2, g2 = this - m4, D2 = function() {
            return b.m(y5, m4);
          };
          switch (M5) {
            case h4:
              $2 = D2() / 12;
              break;
            case c2:
              $2 = D2();
              break;
            case f6:
              $2 = D2() / 3;
              break;
            case o2:
              $2 = (g2 - v2) / 6048e5;
              break;
            case a:
              $2 = (g2 - v2) / 864e5;
              break;
            case u2:
              $2 = g2 / n2;
              break;
            case s2:
              $2 = g2 / e2;
              break;
            case i3:
              $2 = g2 / t2;
              break;
            default:
              $2 = g2;
          }
          return l4 ? $2 : b.a($2);
        }, m3.daysInMonth = function() {
          return this.endOf(c2).$D;
        }, m3.$locale = function() {
          return D[this.$L];
        }, m3.locale = function(t3, e3) {
          if (!t3) return this.$L;
          var n3 = this.clone(), r3 = w(t3, e3, true);
          return r3 && (n3.$L = r3), n3;
        }, m3.clone = function() {
          return b.w(this.$d, this);
        }, m3.toDate = function() {
          return new Date(this.valueOf());
        }, m3.toJSON = function() {
          return this.isValid() ? this.toISOString() : null;
        }, m3.toISOString = function() {
          return this.$d.toISOString();
        }, m3.toString = function() {
          return this.$d.toUTCString();
        }, M4;
      }(), k2 = _.prototype;
      return O2.prototype = k2, [["$ms", r2], ["$s", i3], ["$m", s2], ["$H", u2], ["$W", a], ["$M", c2], ["$y", h4], ["$D", d2]].forEach(function(t3) {
        k2[t3[1]] = function(e3) {
          return this.$g(e3, t3[0], t3[1]);
        };
      }), O2.extend = function(t3, e3) {
        return t3.$i || (t3(e3, _, O2), t3.$i = true), O2;
      }, O2.locale = w, O2.isDayjs = S2, O2.unix = function(t3) {
        return O2(1e3 * t3);
      }, O2.en = D[g], O2.Ls = D, O2.p = {}, O2;
    });
  }
});

// node_modules/dayjs/locale/en.js
var require_en = __commonJS({
  "node_modules/dayjs/locale/en.js"(exports, module) {
    !function(e2, n2) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = n2() : "function" == typeof define && define.amd ? define(n2) : (e2 = "undefined" != typeof globalThis ? globalThis : e2 || self).dayjs_locale_en = n2();
    }(exports, function() {
      "use strict";
      return { name: "en", weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"), months: "January_February_March_April_May_June_July_August_September_October_November_December".split("_"), ordinal: function(e2) {
        var n2 = ["th", "st", "nd", "rd"], t2 = e2 % 100;
        return "[" + e2 + (n2[(t2 - 20) % 10] || n2[t2] || n2[0]) + "]";
      } };
    });
  }
});

// node_modules/dayjs/plugin/relativeTime.js
var require_relativeTime = __commonJS({
  "node_modules/dayjs/plugin/relativeTime.js"(exports, module) {
    !function(r2, e2) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = e2() : "function" == typeof define && define.amd ? define(e2) : (r2 = "undefined" != typeof globalThis ? globalThis : r2 || self).dayjs_plugin_relativeTime = e2();
    }(exports, function() {
      "use strict";
      return function(r2, e2, t2) {
        r2 = r2 || {};
        var n2 = e2.prototype, o2 = { future: "in %s", past: "%s ago", s: "a few seconds", m: "a minute", mm: "%d minutes", h: "an hour", hh: "%d hours", d: "a day", dd: "%d days", M: "a month", MM: "%d months", y: "a year", yy: "%d years" };
        function i3(r3, e3, t3, o3) {
          return n2.fromToBase(r3, e3, t3, o3);
        }
        t2.en.relativeTime = o2, n2.fromToBase = function(e3, n3, i4, d3, u2) {
          for (var f6, a, s2, l3 = i4.$locale().relativeTime || o2, h4 = r2.thresholds || [{ l: "s", r: 44, d: "second" }, { l: "m", r: 89 }, { l: "mm", r: 44, d: "minute" }, { l: "h", r: 89 }, { l: "hh", r: 21, d: "hour" }, { l: "d", r: 35 }, { l: "dd", r: 25, d: "day" }, { l: "M", r: 45 }, { l: "MM", r: 10, d: "month" }, { l: "y", r: 17 }, { l: "yy", d: "year" }], m2 = h4.length, c2 = 0; c2 < m2; c2 += 1) {
            var y4 = h4[c2];
            y4.d && (f6 = d3 ? t2(e3).diff(i4, y4.d, true) : i4.diff(e3, y4.d, true));
            var p = (r2.rounding || Math.round)(Math.abs(f6));
            if (s2 = f6 > 0, p <= y4.r || !y4.r) {
              p <= 1 && c2 > 0 && (y4 = h4[c2 - 1]);
              var v = l3[y4.l];
              u2 && (p = u2("" + p)), a = "string" == typeof v ? v.replace("%d", p) : v(p, n3, y4.l, s2);
              break;
            }
          }
          if (n3) return a;
          var M3 = s2 ? l3.future : l3.past;
          return "function" == typeof M3 ? M3(a) : M3.replace("%s", a);
        }, n2.to = function(r3, e3) {
          return i3(r3, e3, this, true);
        }, n2.from = function(r3, e3) {
          return i3(r3, e3, this);
        };
        var d2 = function(r3) {
          return r3.$u ? t2.utc() : t2();
        };
        n2.toNow = function(r3) {
          return this.to(d2(this), r3);
        }, n2.fromNow = function(r3) {
          return this.from(d2(this), r3);
        };
      };
    });
  }
});

// node_modules/dayjs/plugin/updateLocale.js
var require_updateLocale = __commonJS({
  "node_modules/dayjs/plugin/updateLocale.js"(exports, module) {
    !function(e2, n2) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = n2() : "function" == typeof define && define.amd ? define(n2) : (e2 = "undefined" != typeof globalThis ? globalThis : e2 || self).dayjs_plugin_updateLocale = n2();
    }(exports, function() {
      "use strict";
      return function(e2, n2, t2) {
        t2.updateLocale = function(e3, n3) {
          var o2 = t2.Ls[e3];
          if (o2) return (n3 ? Object.keys(n3) : []).forEach(function(e4) {
            o2[e4] = n3[e4];
          }), o2;
        };
      };
    });
  }
});

// node_modules/@reown/appkit-common/dist/esm/src/utils/ConstantsUtil.js
var ConstantsUtil = {
  WC_NAME_SUFFIX: ".reown.id",
  WC_NAME_SUFFIX_LEGACY: ".wcn.id",
  BLOCKCHAIN_API_RPC_URL: "https://rpc.walletconnect.org",
  PULSE_API_URL: "https://pulse.walletconnect.org",
  W3M_API_URL: "https://api.web3modal.org",
  CONNECTOR_ID: {
    WALLET_CONNECT: "walletConnect",
    INJECTED: "injected",
    WALLET_STANDARD: "announced",
    COINBASE: "coinbaseWallet",
    COINBASE_SDK: "coinbaseWalletSDK",
    SAFE: "safe",
    LEDGER: "ledger",
    OKX: "okx",
    EIP6963: "eip6963",
    AUTH: "ID_AUTH"
  },
  CONNECTOR_NAMES: {
    AUTH: "Auth"
  },
  AUTH_CONNECTOR_SUPPORTED_CHAINS: ["eip155", "solana"],
  LIMITS: {
    PENDING_TRANSACTIONS: 99
  },
  CHAIN: {
    EVM: "eip155",
    SOLANA: "solana",
    POLKADOT: "polkadot",
    BITCOIN: "bip122"
  },
  CHAIN_NAME_MAP: {
    eip155: "EVM Networks",
    solana: "Solana",
    polkadot: "Polkadot",
    bip122: "Bitcoin",
    cosmos: "Cosmos"
  },
  ADAPTER_TYPES: {
    BITCOIN: "bitcoin",
    SOLANA: "solana",
    WAGMI: "wagmi",
    ETHERS: "ethers",
    ETHERS5: "ethers5"
  },
  USDT_CONTRACT_ADDRESSES: [
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************"
  ],
  HTTP_STATUS_CODES: {
    SERVICE_UNAVAILABLE: 503,
    FORBIDDEN: 403
  },
  UNSUPPORTED_NETWORK_NAME: "Unknown Network",
  SECURE_SITE_SDK_ORIGIN: (typeof process !== "undefined" && typeof process.env !== "undefined" ? process.env["NEXT_PUBLIC_SECURE_SITE_ORIGIN"] : void 0) || "https://secure.walletconnect.org"
};

// node_modules/@reown/appkit-common/dist/esm/src/utils/DateUtil.js
var import_dayjs = __toESM(require_dayjs_min(), 1);
var import_en = __toESM(require_en(), 1);
var import_relativeTime = __toESM(require_relativeTime(), 1);
var import_updateLocale = __toESM(require_updateLocale(), 1);
import_dayjs.default.extend(import_relativeTime.default);
import_dayjs.default.extend(import_updateLocale.default);
var localeObject = {
  ...import_en.default,
  name: "en-web3-modal",
  relativeTime: {
    future: "in %s",
    past: "%s ago",
    s: "%d sec",
    m: "1 min",
    mm: "%d min",
    h: "1 hr",
    hh: "%d hrs",
    d: "1 d",
    dd: "%d d",
    M: "1 mo",
    MM: "%d mo",
    y: "1 yr",
    yy: "%d yr"
  }
};
import_dayjs.default.locale("en-web3-modal", localeObject);

// node_modules/@reown/appkit-common/dist/esm/src/utils/NetworkUtil.js
var NetworkUtil = {
  caipNetworkIdToNumber(caipnetworkId) {
    return caipnetworkId ? Number(caipnetworkId.split(":")[1]) : void 0;
  },
  parseEvmChainId(chainId) {
    return typeof chainId === "string" ? this.caipNetworkIdToNumber(chainId) : chainId;
  },
  getNetworksByNamespace(networks, namespace) {
    return (networks == null ? void 0 : networks.filter((network) => network.chainNamespace === namespace)) || [];
  },
  getFirstNetworkByNamespace(networks, namespace) {
    return this.getNetworksByNamespace(networks, namespace)[0];
  },
  getNetworkNameByCaipNetworkId(caipNetworks, caipNetworkId) {
    var _a;
    if (!caipNetworkId) {
      return void 0;
    }
    const caipNetwork = caipNetworks.find((network) => network.caipNetworkId === caipNetworkId);
    if (caipNetwork) {
      return caipNetwork.name;
    }
    const [namespace] = caipNetworkId.split(":");
    return ((_a = ConstantsUtil.CHAIN_NAME_MAP) == null ? void 0 : _a[namespace]) || void 0;
  }
};

// node_modules/big.js/big.mjs
var DP = 20;
var RM = 1;
var MAX_DP = 1e6;
var MAX_POWER = 1e6;
var NE = -7;
var PE = 21;
var STRICT = false;
var NAME = "[big.js] ";
var INVALID = NAME + "Invalid ";
var INVALID_DP = INVALID + "decimal places";
var INVALID_RM = INVALID + "rounding mode";
var DIV_BY_ZERO = NAME + "Division by zero";
var P = {};
var UNDEFINED = void 0;
var NUMERIC = /^-?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i;
function _Big_() {
  function Big2(n2) {
    var x = this;
    if (!(x instanceof Big2)) return n2 === UNDEFINED ? _Big_() : new Big2(n2);
    if (n2 instanceof Big2) {
      x.s = n2.s;
      x.e = n2.e;
      x.c = n2.c.slice();
    } else {
      if (typeof n2 !== "string") {
        if (Big2.strict === true && typeof n2 !== "bigint") {
          throw TypeError(INVALID + "value");
        }
        n2 = n2 === 0 && 1 / n2 < 0 ? "-0" : String(n2);
      }
      parse(x, n2);
    }
    x.constructor = Big2;
  }
  Big2.prototype = P;
  Big2.DP = DP;
  Big2.RM = RM;
  Big2.NE = NE;
  Big2.PE = PE;
  Big2.strict = STRICT;
  Big2.roundDown = 0;
  Big2.roundHalfUp = 1;
  Big2.roundHalfEven = 2;
  Big2.roundUp = 3;
  return Big2;
}
function parse(x, n2) {
  var e2, i3, nl;
  if (!NUMERIC.test(n2)) {
    throw Error(INVALID + "number");
  }
  x.s = n2.charAt(0) == "-" ? (n2 = n2.slice(1), -1) : 1;
  if ((e2 = n2.indexOf(".")) > -1) n2 = n2.replace(".", "");
  if ((i3 = n2.search(/e/i)) > 0) {
    if (e2 < 0) e2 = i3;
    e2 += +n2.slice(i3 + 1);
    n2 = n2.substring(0, i3);
  } else if (e2 < 0) {
    e2 = n2.length;
  }
  nl = n2.length;
  for (i3 = 0; i3 < nl && n2.charAt(i3) == "0"; ) ++i3;
  if (i3 == nl) {
    x.c = [x.e = 0];
  } else {
    for (; nl > 0 && n2.charAt(--nl) == "0"; ) ;
    x.e = e2 - i3 - 1;
    x.c = [];
    for (e2 = 0; i3 <= nl; ) x.c[e2++] = +n2.charAt(i3++);
  }
  return x;
}
function round(x, sd, rm, more) {
  var xc2 = x.c;
  if (rm === UNDEFINED) rm = x.constructor.RM;
  if (rm !== 0 && rm !== 1 && rm !== 2 && rm !== 3) {
    throw Error(INVALID_RM);
  }
  if (sd < 1) {
    more = rm === 3 && (more || !!xc2[0]) || sd === 0 && (rm === 1 && xc2[0] >= 5 || rm === 2 && (xc2[0] > 5 || xc2[0] === 5 && (more || xc2[1] !== UNDEFINED)));
    xc2.length = 1;
    if (more) {
      x.e = x.e - sd + 1;
      xc2[0] = 1;
    } else {
      xc2[0] = x.e = 0;
    }
  } else if (sd < xc2.length) {
    more = rm === 1 && xc2[sd] >= 5 || rm === 2 && (xc2[sd] > 5 || xc2[sd] === 5 && (more || xc2[sd + 1] !== UNDEFINED || xc2[sd - 1] & 1)) || rm === 3 && (more || !!xc2[0]);
    xc2.length = sd;
    if (more) {
      for (; ++xc2[--sd] > 9; ) {
        xc2[sd] = 0;
        if (sd === 0) {
          ++x.e;
          xc2.unshift(1);
          break;
        }
      }
    }
    for (sd = xc2.length; !xc2[--sd]; ) xc2.pop();
  }
  return x;
}
function stringify(x, doExponential, isNonzero) {
  var e2 = x.e, s2 = x.c.join(""), n2 = s2.length;
  if (doExponential) {
    s2 = s2.charAt(0) + (n2 > 1 ? "." + s2.slice(1) : "") + (e2 < 0 ? "e" : "e+") + e2;
  } else if (e2 < 0) {
    for (; ++e2; ) s2 = "0" + s2;
    s2 = "0." + s2;
  } else if (e2 > 0) {
    if (++e2 > n2) {
      for (e2 -= n2; e2--; ) s2 += "0";
    } else if (e2 < n2) {
      s2 = s2.slice(0, e2) + "." + s2.slice(e2);
    }
  } else if (n2 > 1) {
    s2 = s2.charAt(0) + "." + s2.slice(1);
  }
  return x.s < 0 && isNonzero ? "-" + s2 : s2;
}
P.abs = function() {
  var x = new this.constructor(this);
  x.s = 1;
  return x;
};
P.cmp = function(y4) {
  var isneg, x = this, xc2 = x.c, yc2 = (y4 = new x.constructor(y4)).c, i3 = x.s, j = y4.s, k2 = x.e, l3 = y4.e;
  if (!xc2[0] || !yc2[0]) return !xc2[0] ? !yc2[0] ? 0 : -j : i3;
  if (i3 != j) return i3;
  isneg = i3 < 0;
  if (k2 != l3) return k2 > l3 ^ isneg ? 1 : -1;
  j = (k2 = xc2.length) < (l3 = yc2.length) ? k2 : l3;
  for (i3 = -1; ++i3 < j; ) {
    if (xc2[i3] != yc2[i3]) return xc2[i3] > yc2[i3] ^ isneg ? 1 : -1;
  }
  return k2 == l3 ? 0 : k2 > l3 ^ isneg ? 1 : -1;
};
P.div = function(y4) {
  var x = this, Big2 = x.constructor, a = x.c, b = (y4 = new Big2(y4)).c, k2 = x.s == y4.s ? 1 : -1, dp = Big2.DP;
  if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {
    throw Error(INVALID_DP);
  }
  if (!b[0]) {
    throw Error(DIV_BY_ZERO);
  }
  if (!a[0]) {
    y4.s = k2;
    y4.c = [y4.e = 0];
    return y4;
  }
  var bl, bt2, n2, cmp, ri3, bz = b.slice(), ai2 = bl = b.length, al = a.length, r2 = a.slice(0, bl), rl = r2.length, q2 = y4, qc2 = q2.c = [], qi2 = 0, p = dp + (q2.e = x.e - y4.e) + 1;
  q2.s = k2;
  k2 = p < 0 ? 0 : p;
  bz.unshift(0);
  for (; rl++ < bl; ) r2.push(0);
  do {
    for (n2 = 0; n2 < 10; n2++) {
      if (bl != (rl = r2.length)) {
        cmp = bl > rl ? 1 : -1;
      } else {
        for (ri3 = -1, cmp = 0; ++ri3 < bl; ) {
          if (b[ri3] != r2[ri3]) {
            cmp = b[ri3] > r2[ri3] ? 1 : -1;
            break;
          }
        }
      }
      if (cmp < 0) {
        for (bt2 = rl == bl ? b : bz; rl; ) {
          if (r2[--rl] < bt2[rl]) {
            ri3 = rl;
            for (; ri3 && !r2[--ri3]; ) r2[ri3] = 9;
            --r2[ri3];
            r2[rl] += 10;
          }
          r2[rl] -= bt2[rl];
        }
        for (; !r2[0]; ) r2.shift();
      } else {
        break;
      }
    }
    qc2[qi2++] = cmp ? n2 : ++n2;
    if (r2[0] && cmp) r2[rl] = a[ai2] || 0;
    else r2 = [a[ai2]];
  } while ((ai2++ < al || r2[0] !== UNDEFINED) && k2--);
  if (!qc2[0] && qi2 != 1) {
    qc2.shift();
    q2.e--;
    p--;
  }
  if (qi2 > p) round(q2, p, Big2.RM, r2[0] !== UNDEFINED);
  return q2;
};
P.eq = function(y4) {
  return this.cmp(y4) === 0;
};
P.gt = function(y4) {
  return this.cmp(y4) > 0;
};
P.gte = function(y4) {
  return this.cmp(y4) > -1;
};
P.lt = function(y4) {
  return this.cmp(y4) < 0;
};
P.lte = function(y4) {
  return this.cmp(y4) < 1;
};
P.minus = P.sub = function(y4) {
  var i3, j, t2, xlty, x = this, Big2 = x.constructor, a = x.s, b = (y4 = new Big2(y4)).s;
  if (a != b) {
    y4.s = -b;
    return x.plus(y4);
  }
  var xc2 = x.c.slice(), xe2 = x.e, yc2 = y4.c, ye2 = y4.e;
  if (!xc2[0] || !yc2[0]) {
    if (yc2[0]) {
      y4.s = -b;
    } else if (xc2[0]) {
      y4 = new Big2(x);
    } else {
      y4.s = 1;
    }
    return y4;
  }
  if (a = xe2 - ye2) {
    if (xlty = a < 0) {
      a = -a;
      t2 = xc2;
    } else {
      ye2 = xe2;
      t2 = yc2;
    }
    t2.reverse();
    for (b = a; b--; ) t2.push(0);
    t2.reverse();
  } else {
    j = ((xlty = xc2.length < yc2.length) ? xc2 : yc2).length;
    for (a = b = 0; b < j; b++) {
      if (xc2[b] != yc2[b]) {
        xlty = xc2[b] < yc2[b];
        break;
      }
    }
  }
  if (xlty) {
    t2 = xc2;
    xc2 = yc2;
    yc2 = t2;
    y4.s = -y4.s;
  }
  if ((b = (j = yc2.length) - (i3 = xc2.length)) > 0) for (; b--; ) xc2[i3++] = 0;
  for (b = i3; j > a; ) {
    if (xc2[--j] < yc2[j]) {
      for (i3 = j; i3 && !xc2[--i3]; ) xc2[i3] = 9;
      --xc2[i3];
      xc2[j] += 10;
    }
    xc2[j] -= yc2[j];
  }
  for (; xc2[--b] === 0; ) xc2.pop();
  for (; xc2[0] === 0; ) {
    xc2.shift();
    --ye2;
  }
  if (!xc2[0]) {
    y4.s = 1;
    xc2 = [ye2 = 0];
  }
  y4.c = xc2;
  y4.e = ye2;
  return y4;
};
P.mod = function(y4) {
  var ygtx, x = this, Big2 = x.constructor, a = x.s, b = (y4 = new Big2(y4)).s;
  if (!y4.c[0]) {
    throw Error(DIV_BY_ZERO);
  }
  x.s = y4.s = 1;
  ygtx = y4.cmp(x) == 1;
  x.s = a;
  y4.s = b;
  if (ygtx) return new Big2(x);
  a = Big2.DP;
  b = Big2.RM;
  Big2.DP = Big2.RM = 0;
  x = x.div(y4);
  Big2.DP = a;
  Big2.RM = b;
  return this.minus(x.times(y4));
};
P.neg = function() {
  var x = new this.constructor(this);
  x.s = -x.s;
  return x;
};
P.plus = P.add = function(y4) {
  var e2, k2, t2, x = this, Big2 = x.constructor;
  y4 = new Big2(y4);
  if (x.s != y4.s) {
    y4.s = -y4.s;
    return x.minus(y4);
  }
  var xe2 = x.e, xc2 = x.c, ye2 = y4.e, yc2 = y4.c;
  if (!xc2[0] || !yc2[0]) {
    if (!yc2[0]) {
      if (xc2[0]) {
        y4 = new Big2(x);
      } else {
        y4.s = x.s;
      }
    }
    return y4;
  }
  xc2 = xc2.slice();
  if (e2 = xe2 - ye2) {
    if (e2 > 0) {
      ye2 = xe2;
      t2 = yc2;
    } else {
      e2 = -e2;
      t2 = xc2;
    }
    t2.reverse();
    for (; e2--; ) t2.push(0);
    t2.reverse();
  }
  if (xc2.length - yc2.length < 0) {
    t2 = yc2;
    yc2 = xc2;
    xc2 = t2;
  }
  e2 = yc2.length;
  for (k2 = 0; e2; xc2[e2] %= 10) k2 = (xc2[--e2] = xc2[e2] + yc2[e2] + k2) / 10 | 0;
  if (k2) {
    xc2.unshift(k2);
    ++ye2;
  }
  for (e2 = xc2.length; xc2[--e2] === 0; ) xc2.pop();
  y4.c = xc2;
  y4.e = ye2;
  return y4;
};
P.pow = function(n2) {
  var x = this, one = new x.constructor("1"), y4 = one, isneg = n2 < 0;
  if (n2 !== ~~n2 || n2 < -MAX_POWER || n2 > MAX_POWER) {
    throw Error(INVALID + "exponent");
  }
  if (isneg) n2 = -n2;
  for (; ; ) {
    if (n2 & 1) y4 = y4.times(x);
    n2 >>= 1;
    if (!n2) break;
    x = x.times(x);
  }
  return isneg ? one.div(y4) : y4;
};
P.prec = function(sd, rm) {
  if (sd !== ~~sd || sd < 1 || sd > MAX_DP) {
    throw Error(INVALID + "precision");
  }
  return round(new this.constructor(this), sd, rm);
};
P.round = function(dp, rm) {
  if (dp === UNDEFINED) dp = 0;
  else if (dp !== ~~dp || dp < -MAX_DP || dp > MAX_DP) {
    throw Error(INVALID_DP);
  }
  return round(new this.constructor(this), dp + this.e + 1, rm);
};
P.sqrt = function() {
  var r2, c2, t2, x = this, Big2 = x.constructor, s2 = x.s, e2 = x.e, half = new Big2("0.5");
  if (!x.c[0]) return new Big2(x);
  if (s2 < 0) {
    throw Error(NAME + "No square root");
  }
  s2 = Math.sqrt(+stringify(x, true, true));
  if (s2 === 0 || s2 === 1 / 0) {
    c2 = x.c.join("");
    if (!(c2.length + e2 & 1)) c2 += "0";
    s2 = Math.sqrt(c2);
    e2 = ((e2 + 1) / 2 | 0) - (e2 < 0 || e2 & 1);
    r2 = new Big2((s2 == 1 / 0 ? "5e" : (s2 = s2.toExponential()).slice(0, s2.indexOf("e") + 1)) + e2);
  } else {
    r2 = new Big2(s2 + "");
  }
  e2 = r2.e + (Big2.DP += 4);
  do {
    t2 = r2;
    r2 = half.times(t2.plus(x.div(t2)));
  } while (t2.c.slice(0, e2).join("") !== r2.c.slice(0, e2).join(""));
  return round(r2, (Big2.DP -= 4) + r2.e + 1, Big2.RM);
};
P.times = P.mul = function(y4) {
  var c2, x = this, Big2 = x.constructor, xc2 = x.c, yc2 = (y4 = new Big2(y4)).c, a = xc2.length, b = yc2.length, i3 = x.e, j = y4.e;
  y4.s = x.s == y4.s ? 1 : -1;
  if (!xc2[0] || !yc2[0]) {
    y4.c = [y4.e = 0];
    return y4;
  }
  y4.e = i3 + j;
  if (a < b) {
    c2 = xc2;
    xc2 = yc2;
    yc2 = c2;
    j = a;
    a = b;
    b = j;
  }
  for (c2 = new Array(j = a + b); j--; ) c2[j] = 0;
  for (i3 = b; i3--; ) {
    b = 0;
    for (j = a + i3; j > i3; ) {
      b = c2[j] + yc2[i3] * xc2[j - i3 - 1] + b;
      c2[j--] = b % 10;
      b = b / 10 | 0;
    }
    c2[j] = b;
  }
  if (b) ++y4.e;
  else c2.shift();
  for (i3 = c2.length; !c2[--i3]; ) c2.pop();
  y4.c = c2;
  return y4;
};
P.toExponential = function(dp, rm) {
  var x = this, n2 = x.c[0];
  if (dp !== UNDEFINED) {
    if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {
      throw Error(INVALID_DP);
    }
    x = round(new x.constructor(x), ++dp, rm);
    for (; x.c.length < dp; ) x.c.push(0);
  }
  return stringify(x, true, !!n2);
};
P.toFixed = function(dp, rm) {
  var x = this, n2 = x.c[0];
  if (dp !== UNDEFINED) {
    if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {
      throw Error(INVALID_DP);
    }
    x = round(new x.constructor(x), dp + x.e + 1, rm);
    for (dp = dp + x.e + 1; x.c.length < dp; ) x.c.push(0);
  }
  return stringify(x, false, !!n2);
};
P[Symbol.for("nodejs.util.inspect.custom")] = P.toJSON = P.toString = function() {
  var x = this, Big2 = x.constructor;
  return stringify(x, x.e <= Big2.NE || x.e >= Big2.PE, !!x.c[0]);
};
P.toNumber = function() {
  var n2 = +stringify(this, true, true);
  if (this.constructor.strict === true && !this.eq(n2.toString())) {
    throw Error(NAME + "Imprecise conversion");
  }
  return n2;
};
P.toPrecision = function(sd, rm) {
  var x = this, Big2 = x.constructor, n2 = x.c[0];
  if (sd !== UNDEFINED) {
    if (sd !== ~~sd || sd < 1 || sd > MAX_DP) {
      throw Error(INVALID + "precision");
    }
    x = round(new Big2(x), sd, rm);
    for (; x.c.length < sd; ) x.c.push(0);
  }
  return stringify(x, sd <= x.e || x.e <= Big2.NE || x.e >= Big2.PE, !!n2);
};
P.valueOf = function() {
  var x = this, Big2 = x.constructor;
  if (Big2.strict === true) {
    throw Error(NAME + "valueOf disallowed");
  }
  return stringify(x, x.e <= Big2.NE || x.e >= Big2.PE, true);
};
var Big = _Big_();
var big_default = Big;

// node_modules/@reown/appkit-common/dist/esm/src/utils/NumberUtil.js
var NumberUtil = {
  bigNumber(value) {
    if (!value) {
      return new big_default(0);
    }
    return new big_default(value);
  },
  multiply(a, b) {
    if (a === void 0 || b === void 0) {
      return new big_default(0);
    }
    const aBigNumber = new big_default(a);
    const bBigNumber = new big_default(b);
    return aBigNumber.times(bBigNumber);
  },
  formatNumberToLocalString(value, decimals = 2) {
    if (value === void 0) {
      return "0.00";
    }
    if (typeof value === "number") {
      return value.toLocaleString("en-US", {
        maximumFractionDigits: decimals,
        minimumFractionDigits: decimals
      });
    }
    return parseFloat(value).toLocaleString("en-US", {
      maximumFractionDigits: decimals,
      minimumFractionDigits: decimals
    });
  },
  parseLocalStringToNumber(value) {
    if (value === void 0) {
      return 0;
    }
    return parseFloat(value.replace(/,/gu, ""));
  }
};

// node_modules/@reown/appkit-common/dist/esm/src/contracts/erc20.js
var erc20ABI = [
  {
    type: "function",
    name: "transfer",
    stateMutability: "nonpayable",
    inputs: [
      {
        name: "_to",
        type: "address"
      },
      {
        name: "_value",
        type: "uint256"
      }
    ],
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ]
  },
  {
    type: "function",
    name: "transferFrom",
    stateMutability: "nonpayable",
    inputs: [
      {
        name: "_from",
        type: "address"
      },
      {
        name: "_to",
        type: "address"
      },
      {
        name: "_value",
        type: "uint256"
      }
    ],
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ]
  }
];

// node_modules/@reown/appkit-common/dist/esm/src/contracts/swap.js
var swapABI = [
  {
    type: "function",
    name: "approve",
    stateMutability: "nonpayable",
    inputs: [
      { name: "spender", type: "address" },
      { name: "amount", type: "uint256" }
    ],
    outputs: [{ type: "bool" }]
  }
];

// node_modules/@reown/appkit-common/dist/esm/src/contracts/usdt.js
var usdtABI = [
  {
    type: "function",
    name: "transfer",
    stateMutability: "nonpayable",
    inputs: [
      {
        name: "recipient",
        type: "address"
      },
      {
        name: "amount",
        type: "uint256"
      }
    ],
    outputs: []
  },
  {
    type: "function",
    name: "transferFrom",
    stateMutability: "nonpayable",
    inputs: [
      {
        name: "sender",
        type: "address"
      },
      {
        name: "recipient",
        type: "address"
      },
      {
        name: "amount",
        type: "uint256"
      }
    ],
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ]
  }
];

// node_modules/@reown/appkit-common/dist/esm/src/utils/ContractUtil.js
var ContractUtil = {
  getERC20Abi: (tokenAddress) => {
    if (ConstantsUtil.USDT_CONTRACT_ADDRESSES.includes(tokenAddress)) {
      return usdtABI;
    }
    return erc20ABI;
  },
  getSwapAbi: () => swapABI
};

// node_modules/@reown/appkit-common/dist/esm/src/utils/EmitterUtil.js
var Emitter = class _Emitter {
  on(eventName, callback) {
    var _a;
    if (!_Emitter.eventListeners.has(eventName)) {
      _Emitter.eventListeners.set(eventName, /* @__PURE__ */ new Set());
    }
    (_a = _Emitter.eventListeners.get(eventName)) == null ? void 0 : _a.add(callback);
  }
  off(eventName, callback) {
    const listeners = _Emitter.eventListeners.get(eventName);
    if (listeners) {
      listeners.delete(callback);
    }
  }
  emit(eventName, data) {
    const listeners = _Emitter.eventListeners.get(eventName);
    if (listeners) {
      listeners.forEach((callback) => callback(data));
    }
  }
  clear(eventName) {
    _Emitter.eventListeners.delete(eventName);
  }
  clearAll() {
    _Emitter.eventListeners.clear();
  }
};
Emitter.eventListeners = /* @__PURE__ */ new Map();

// node_modules/@reown/appkit-common/dist/esm/src/utils/ParseUtil.js
var ParseUtil = {
  validateCaipAddress(address) {
    var _a;
    if (((_a = address.split(":")) == null ? void 0 : _a.length) !== 3) {
      throw new Error("Invalid CAIP Address");
    }
    return address;
  },
  parseCaipAddress(caipAddress) {
    const parts = caipAddress.split(":");
    if (parts.length !== 3) {
      throw new Error(`Invalid CAIP-10 address: ${caipAddress}`);
    }
    const [chainNamespace, chainId, address] = parts;
    if (!chainNamespace || !chainId || !address) {
      throw new Error(`Invalid CAIP-10 address: ${caipAddress}`);
    }
    return {
      chainNamespace,
      chainId,
      address
    };
  },
  parseCaipNetworkId(caipNetworkId) {
    const parts = caipNetworkId.split(":");
    if (parts.length !== 2) {
      throw new Error(`Invalid CAIP-2 network id: ${caipNetworkId}`);
    }
    const [chainNamespace, chainId] = parts;
    if (!chainNamespace || !chainId) {
      throw new Error(`Invalid CAIP-2 network id: ${caipNetworkId}`);
    }
    return {
      chainNamespace,
      chainId
    };
  }
};

// node_modules/@reown/appkit-common/dist/esm/src/utils/SafeLocalStorage.js
var SafeLocalStorageKeys = {
  WALLET_ID: "@appkit/wallet_id",
  WALLET_NAME: "@appkit/wallet_name",
  SOLANA_WALLET: "@appkit/solana_wallet",
  SOLANA_CAIP_CHAIN: "@appkit/solana_caip_chain",
  ACTIVE_CAIP_NETWORK_ID: "@appkit/active_caip_network_id",
  CONNECTED_SOCIAL: "@appkit/connected_social",
  CONNECTED_SOCIAL_USERNAME: "@appkit-wallet/SOCIAL_USERNAME",
  RECENT_WALLETS: "@appkit/recent_wallets",
  DEEPLINK_CHOICE: "WALLETCONNECT_DEEPLINK_CHOICE",
  ACTIVE_NAMESPACE: "@appkit/active_namespace",
  CONNECTED_NAMESPACES: "@appkit/connected_namespaces",
  CONNECTION_STATUS: "@appkit/connection_status",
  SIWX_AUTH_TOKEN: "@appkit/siwx-auth-token",
  SIWX_NONCE_TOKEN: "@appkit/siwx-nonce-token",
  TELEGRAM_SOCIAL_PROVIDER: "@appkit/social_provider",
  NATIVE_BALANCE_CACHE: "@appkit/native_balance_cache",
  PORTFOLIO_CACHE: "@appkit/portfolio_cache",
  ENS_CACHE: "@appkit/ens_cache",
  IDENTITY_CACHE: "@appkit/identity_cache",
  PREFERRED_ACCOUNT_TYPES: "@appkit/preferred_account_types",
  CONNECTIONS: "@appkit/connections"
};
function getSafeConnectorIdKey(namespace) {
  if (!namespace) {
    throw new Error("Namespace is required for CONNECTED_CONNECTOR_ID");
  }
  return `@appkit/${namespace}:connected_connector_id`;
}
var SafeLocalStorage = {
  setItem(key, value) {
    if (isSafe() && value !== void 0) {
      localStorage.setItem(key, value);
    }
  },
  getItem(key) {
    if (isSafe()) {
      return localStorage.getItem(key) || void 0;
    }
    return void 0;
  },
  removeItem(key) {
    if (isSafe()) {
      localStorage.removeItem(key);
    }
  },
  clear() {
    if (isSafe()) {
      localStorage.clear();
    }
  }
};
function isSafe() {
  return typeof window !== "undefined" && typeof localStorage !== "undefined";
}

// node_modules/@reown/appkit-common/dist/esm/src/utils/ThemeUtil.js
function getW3mThemeVariables(themeVariables, themeType) {
  if (themeType === "light") {
    return {
      "--w3m-accent": (themeVariables == null ? void 0 : themeVariables["--w3m-accent"]) || "hsla(231, 100%, 70%, 1)",
      "--w3m-background": "#fff"
    };
  }
  return {
    "--w3m-accent": (themeVariables == null ? void 0 : themeVariables["--w3m-accent"]) || "hsla(230, 100%, 67%, 1)",
    "--w3m-background": "#121313"
  };
}

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/StorageUtil.js
var StorageUtil = {
  // Cache expiry in milliseconds
  cacheExpiry: {
    portfolio: 3e4,
    nativeBalance: 3e4,
    ens: 3e5,
    identity: 3e5
  },
  isCacheExpired(timestamp, cacheExpiry) {
    return Date.now() - timestamp > cacheExpiry;
  },
  getActiveNetworkProps() {
    const namespace = StorageUtil.getActiveNamespace();
    const caipNetworkId = StorageUtil.getActiveCaipNetworkId();
    const stringChainId = caipNetworkId ? caipNetworkId.split(":")[1] : void 0;
    const chainId = stringChainId ? isNaN(Number(stringChainId)) ? stringChainId : Number(stringChainId) : void 0;
    return {
      namespace,
      caipNetworkId,
      chainId
    };
  },
  setWalletConnectDeepLink({ name, href }) {
    try {
      SafeLocalStorage.setItem(SafeLocalStorageKeys.DEEPLINK_CHOICE, JSON.stringify({ href, name }));
    } catch {
      console.info("Unable to set WalletConnect deep link");
    }
  },
  getWalletConnectDeepLink() {
    try {
      const deepLink = SafeLocalStorage.getItem(SafeLocalStorageKeys.DEEPLINK_CHOICE);
      if (deepLink) {
        return JSON.parse(deepLink);
      }
    } catch {
      console.info("Unable to get WalletConnect deep link");
    }
    return void 0;
  },
  deleteWalletConnectDeepLink() {
    try {
      SafeLocalStorage.removeItem(SafeLocalStorageKeys.DEEPLINK_CHOICE);
    } catch {
      console.info("Unable to delete WalletConnect deep link");
    }
  },
  setActiveNamespace(namespace) {
    try {
      SafeLocalStorage.setItem(SafeLocalStorageKeys.ACTIVE_NAMESPACE, namespace);
    } catch {
      console.info("Unable to set active namespace");
    }
  },
  setActiveCaipNetworkId(caipNetworkId) {
    try {
      SafeLocalStorage.setItem(SafeLocalStorageKeys.ACTIVE_CAIP_NETWORK_ID, caipNetworkId);
      StorageUtil.setActiveNamespace(caipNetworkId.split(":")[0]);
    } catch {
      console.info("Unable to set active caip network id");
    }
  },
  getActiveCaipNetworkId() {
    try {
      return SafeLocalStorage.getItem(SafeLocalStorageKeys.ACTIVE_CAIP_NETWORK_ID);
    } catch {
      console.info("Unable to get active caip network id");
      return void 0;
    }
  },
  deleteActiveCaipNetworkId() {
    try {
      SafeLocalStorage.removeItem(SafeLocalStorageKeys.ACTIVE_CAIP_NETWORK_ID);
    } catch {
      console.info("Unable to delete active caip network id");
    }
  },
  deleteConnectedConnectorId(namespace) {
    try {
      const key = getSafeConnectorIdKey(namespace);
      SafeLocalStorage.removeItem(key);
    } catch {
      console.info("Unable to delete connected connector id");
    }
  },
  setAppKitRecent(wallet) {
    try {
      const recentWallets = StorageUtil.getRecentWallets();
      const exists = recentWallets.find((w) => w.id === wallet.id);
      if (!exists) {
        recentWallets.unshift(wallet);
        if (recentWallets.length > 2) {
          recentWallets.pop();
        }
        SafeLocalStorage.setItem(SafeLocalStorageKeys.RECENT_WALLETS, JSON.stringify(recentWallets));
      }
    } catch {
      console.info("Unable to set AppKit recent");
    }
  },
  getRecentWallets() {
    try {
      const recent = SafeLocalStorage.getItem(SafeLocalStorageKeys.RECENT_WALLETS);
      return recent ? JSON.parse(recent) : [];
    } catch {
      console.info("Unable to get AppKit recent");
    }
    return [];
  },
  setConnectedConnectorId(namespace, connectorId) {
    try {
      const key = getSafeConnectorIdKey(namespace);
      SafeLocalStorage.setItem(key, connectorId);
    } catch {
      console.info("Unable to set Connected Connector Id");
    }
  },
  getActiveNamespace() {
    try {
      const activeNamespace = SafeLocalStorage.getItem(SafeLocalStorageKeys.ACTIVE_NAMESPACE);
      return activeNamespace;
    } catch {
      console.info("Unable to get active namespace");
    }
    return void 0;
  },
  getConnectedConnectorId(namespace) {
    if (!namespace) {
      return void 0;
    }
    try {
      const key = getSafeConnectorIdKey(namespace);
      return SafeLocalStorage.getItem(key);
    } catch (e2) {
      console.info("Unable to get connected connector id in namespace ", namespace);
    }
    return void 0;
  },
  setConnectedSocialProvider(socialProvider) {
    try {
      SafeLocalStorage.setItem(SafeLocalStorageKeys.CONNECTED_SOCIAL, socialProvider);
    } catch {
      console.info("Unable to set connected social provider");
    }
  },
  getConnectedSocialProvider() {
    try {
      return SafeLocalStorage.getItem(SafeLocalStorageKeys.CONNECTED_SOCIAL);
    } catch {
      console.info("Unable to get connected social provider");
    }
    return void 0;
  },
  deleteConnectedSocialProvider() {
    try {
      SafeLocalStorage.removeItem(SafeLocalStorageKeys.CONNECTED_SOCIAL);
    } catch {
      console.info("Unable to delete connected social provider");
    }
  },
  getConnectedSocialUsername() {
    try {
      return SafeLocalStorage.getItem(SafeLocalStorageKeys.CONNECTED_SOCIAL_USERNAME);
    } catch {
      console.info("Unable to get connected social username");
    }
    return void 0;
  },
  getStoredActiveCaipNetworkId() {
    var _a;
    const storedCaipNetworkId = SafeLocalStorage.getItem(SafeLocalStorageKeys.ACTIVE_CAIP_NETWORK_ID);
    const networkId = (_a = storedCaipNetworkId == null ? void 0 : storedCaipNetworkId.split(":")) == null ? void 0 : _a[1];
    return networkId;
  },
  setConnectionStatus(status) {
    try {
      SafeLocalStorage.setItem(SafeLocalStorageKeys.CONNECTION_STATUS, status);
    } catch {
      console.info("Unable to set connection status");
    }
  },
  getConnectionStatus() {
    try {
      return SafeLocalStorage.getItem(SafeLocalStorageKeys.CONNECTION_STATUS);
    } catch {
      return void 0;
    }
  },
  getConnectedNamespaces() {
    try {
      const namespaces = SafeLocalStorage.getItem(SafeLocalStorageKeys.CONNECTED_NAMESPACES);
      if (!(namespaces == null ? void 0 : namespaces.length)) {
        return [];
      }
      return namespaces.split(",");
    } catch {
      return [];
    }
  },
  setConnectedNamespaces(namespaces) {
    try {
      const uniqueNamespaces = Array.from(new Set(namespaces));
      SafeLocalStorage.setItem(SafeLocalStorageKeys.CONNECTED_NAMESPACES, uniqueNamespaces.join(","));
    } catch {
      console.info("Unable to set namespaces in storage");
    }
  },
  addConnectedNamespace(namespace) {
    try {
      const namespaces = StorageUtil.getConnectedNamespaces();
      if (!namespaces.includes(namespace)) {
        namespaces.push(namespace);
        StorageUtil.setConnectedNamespaces(namespaces);
      }
    } catch {
      console.info("Unable to add connected namespace");
    }
  },
  removeConnectedNamespace(namespace) {
    try {
      const namespaces = StorageUtil.getConnectedNamespaces();
      const index = namespaces.indexOf(namespace);
      if (index > -1) {
        namespaces.splice(index, 1);
        StorageUtil.setConnectedNamespaces(namespaces);
      }
    } catch {
      console.info("Unable to remove connected namespace");
    }
  },
  getTelegramSocialProvider() {
    try {
      return SafeLocalStorage.getItem(SafeLocalStorageKeys.TELEGRAM_SOCIAL_PROVIDER);
    } catch {
      console.info("Unable to get telegram social provider");
      return null;
    }
  },
  setTelegramSocialProvider(socialProvider) {
    try {
      SafeLocalStorage.setItem(SafeLocalStorageKeys.TELEGRAM_SOCIAL_PROVIDER, socialProvider);
    } catch {
      console.info("Unable to set telegram social provider");
    }
  },
  removeTelegramSocialProvider() {
    try {
      SafeLocalStorage.removeItem(SafeLocalStorageKeys.TELEGRAM_SOCIAL_PROVIDER);
    } catch {
      console.info("Unable to remove telegram social provider");
    }
  },
  getBalanceCache() {
    let cache = {};
    try {
      const result = SafeLocalStorage.getItem(SafeLocalStorageKeys.PORTFOLIO_CACHE);
      cache = result ? JSON.parse(result) : {};
    } catch {
      console.info("Unable to get balance cache");
    }
    return cache;
  },
  removeAddressFromBalanceCache(caipAddress) {
    try {
      const cache = StorageUtil.getBalanceCache();
      SafeLocalStorage.setItem(SafeLocalStorageKeys.PORTFOLIO_CACHE, JSON.stringify({ ...cache, [caipAddress]: void 0 }));
    } catch {
      console.info("Unable to remove address from balance cache", caipAddress);
    }
  },
  getBalanceCacheForCaipAddress(caipAddress) {
    try {
      const cache = StorageUtil.getBalanceCache();
      const balanceCache = cache[caipAddress];
      if (balanceCache && !this.isCacheExpired(balanceCache.timestamp, this.cacheExpiry.portfolio)) {
        return balanceCache.balance;
      }
      StorageUtil.removeAddressFromBalanceCache(caipAddress);
    } catch {
      console.info("Unable to get balance cache for address", caipAddress);
    }
    return void 0;
  },
  updateBalanceCache(params) {
    try {
      const cache = StorageUtil.getBalanceCache();
      cache[params.caipAddress] = params;
      SafeLocalStorage.setItem(SafeLocalStorageKeys.PORTFOLIO_CACHE, JSON.stringify(cache));
    } catch {
      console.info("Unable to update balance cache", params);
    }
  },
  getNativeBalanceCache() {
    let cache = {};
    try {
      const result = SafeLocalStorage.getItem(SafeLocalStorageKeys.NATIVE_BALANCE_CACHE);
      cache = result ? JSON.parse(result) : {};
    } catch {
      console.info("Unable to get balance cache");
    }
    return cache;
  },
  removeAddressFromNativeBalanceCache(caipAddress) {
    try {
      const cache = StorageUtil.getBalanceCache();
      SafeLocalStorage.setItem(SafeLocalStorageKeys.NATIVE_BALANCE_CACHE, JSON.stringify({ ...cache, [caipAddress]: void 0 }));
    } catch {
      console.info("Unable to remove address from balance cache", caipAddress);
    }
  },
  getNativeBalanceCacheForCaipAddress(caipAddress) {
    try {
      const cache = StorageUtil.getNativeBalanceCache();
      const nativeBalanceCache = cache[caipAddress];
      if (nativeBalanceCache && !this.isCacheExpired(nativeBalanceCache.timestamp, this.cacheExpiry.nativeBalance)) {
        return nativeBalanceCache;
      }
      console.info("Discarding cache for address", caipAddress);
      StorageUtil.removeAddressFromBalanceCache(caipAddress);
    } catch {
      console.info("Unable to get balance cache for address", caipAddress);
    }
    return void 0;
  },
  updateNativeBalanceCache(params) {
    try {
      const cache = StorageUtil.getNativeBalanceCache();
      cache[params.caipAddress] = params;
      SafeLocalStorage.setItem(SafeLocalStorageKeys.NATIVE_BALANCE_CACHE, JSON.stringify(cache));
    } catch {
      console.info("Unable to update balance cache", params);
    }
  },
  getEnsCache() {
    let cache = {};
    try {
      const result = SafeLocalStorage.getItem(SafeLocalStorageKeys.ENS_CACHE);
      cache = result ? JSON.parse(result) : {};
    } catch {
      console.info("Unable to get ens name cache");
    }
    return cache;
  },
  getEnsFromCacheForAddress(address) {
    try {
      const cache = StorageUtil.getEnsCache();
      const ensCache = cache[address];
      if (ensCache && !this.isCacheExpired(ensCache.timestamp, this.cacheExpiry.ens)) {
        return ensCache.ens;
      }
      StorageUtil.removeEnsFromCache(address);
    } catch {
      console.info("Unable to get ens name from cache", address);
    }
    return void 0;
  },
  updateEnsCache(params) {
    try {
      const cache = StorageUtil.getEnsCache();
      cache[params.address] = params;
      SafeLocalStorage.setItem(SafeLocalStorageKeys.ENS_CACHE, JSON.stringify(cache));
    } catch {
      console.info("Unable to update ens name cache", params);
    }
  },
  removeEnsFromCache(address) {
    try {
      const cache = StorageUtil.getEnsCache();
      SafeLocalStorage.setItem(SafeLocalStorageKeys.ENS_CACHE, JSON.stringify({ ...cache, [address]: void 0 }));
    } catch {
      console.info("Unable to remove ens name from cache", address);
    }
  },
  getIdentityCache() {
    let cache = {};
    try {
      const result = SafeLocalStorage.getItem(SafeLocalStorageKeys.IDENTITY_CACHE);
      cache = result ? JSON.parse(result) : {};
    } catch {
      console.info("Unable to get identity cache");
    }
    return cache;
  },
  getIdentityFromCacheForAddress(address) {
    try {
      const cache = StorageUtil.getIdentityCache();
      const identityCache = cache[address];
      if (identityCache && !this.isCacheExpired(identityCache.timestamp, this.cacheExpiry.identity)) {
        return identityCache.identity;
      }
      StorageUtil.removeIdentityFromCache(address);
    } catch {
      console.info("Unable to get identity from cache", address);
    }
    return void 0;
  },
  updateIdentityCache(params) {
    try {
      const cache = StorageUtil.getIdentityCache();
      cache[params.address] = {
        identity: params.identity,
        timestamp: params.timestamp
      };
      SafeLocalStorage.setItem(SafeLocalStorageKeys.IDENTITY_CACHE, JSON.stringify(cache));
    } catch {
      console.info("Unable to update identity cache", params);
    }
  },
  removeIdentityFromCache(address) {
    try {
      const cache = StorageUtil.getIdentityCache();
      SafeLocalStorage.setItem(SafeLocalStorageKeys.IDENTITY_CACHE, JSON.stringify({ ...cache, [address]: void 0 }));
    } catch {
      console.info("Unable to remove identity from cache", address);
    }
  },
  clearAddressCache() {
    try {
      SafeLocalStorage.removeItem(SafeLocalStorageKeys.PORTFOLIO_CACHE);
      SafeLocalStorage.removeItem(SafeLocalStorageKeys.NATIVE_BALANCE_CACHE);
      SafeLocalStorage.removeItem(SafeLocalStorageKeys.ENS_CACHE);
      SafeLocalStorage.removeItem(SafeLocalStorageKeys.IDENTITY_CACHE);
    } catch {
      console.info("Unable to clear address cache");
    }
  },
  setPreferredAccountTypes(accountTypes) {
    try {
      SafeLocalStorage.setItem(SafeLocalStorageKeys.PREFERRED_ACCOUNT_TYPES, JSON.stringify(accountTypes));
    } catch {
      console.info("Unable to set preferred account types", accountTypes);
    }
  },
  getPreferredAccountTypes() {
    try {
      const result = SafeLocalStorage.getItem(SafeLocalStorageKeys.PREFERRED_ACCOUNT_TYPES);
      if (!result) {
        return {};
      }
      return JSON.parse(result);
    } catch {
      console.info("Unable to get preferred account types");
    }
    return {};
  },
  setConnections(connections, chainNamespace) {
    try {
      const newConnections = {
        ...StorageUtil.getConnections(),
        [chainNamespace]: connections
      };
      SafeLocalStorage.setItem(SafeLocalStorageKeys.CONNECTIONS, JSON.stringify(newConnections));
    } catch (error) {
      console.error("Unable to sync connections to storage", error);
    }
  },
  getConnections() {
    try {
      const connectionsStorage = SafeLocalStorage.getItem(SafeLocalStorageKeys.CONNECTIONS);
      if (!connectionsStorage) {
        return {};
      }
      return JSON.parse(connectionsStorage);
    } catch (error) {
      console.error("Unable to get connections from storage", error);
      return {};
    }
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/ConstantsUtil.js
var SECURE_SITE = (
  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain
  (typeof process !== "undefined" && typeof process.env !== "undefined" ? process.env["NEXT_PUBLIC_SECURE_SITE_ORIGIN"] : void 0) || "https://secure.walletconnect.org"
);
var ONRAMP_PROVIDERS = [
  {
    label: "Coinbase",
    name: "coinbase",
    feeRange: "1-2%",
    url: "",
    supportedChains: ["eip155"]
  },
  {
    label: "Meld.io",
    name: "meld",
    feeRange: "1-2%",
    url: "https://meldcrypto.com",
    supportedChains: ["eip155", "solana"]
  }
];
var MELD_PUBLIC_KEY = "WXETMuFUQmqqybHuRkSgxv:25B8LJHSfpG6LVjR2ytU5Cwh7Z4Sch2ocoU";
var ConstantsUtil2 = {
  FOUR_MINUTES_MS: 24e4,
  TEN_SEC_MS: 1e4,
  FIVE_SEC_MS: 5e3,
  THREE_SEC_MS: 3e3,
  ONE_SEC_MS: 1e3,
  SECURE_SITE,
  SECURE_SITE_DASHBOARD: `${SECURE_SITE}/dashboard`,
  SECURE_SITE_FAVICON: `${SECURE_SITE}/images/favicon.png`,
  RESTRICTED_TIMEZONES: [
    "ASIA/SHANGHAI",
    "ASIA/URUMQI",
    "ASIA/CHONGQING",
    "ASIA/HARBIN",
    "ASIA/KASHGAR",
    "ASIA/MACAU",
    "ASIA/HONG_KONG",
    "ASIA/MACAO",
    "ASIA/BEIJING",
    "ASIA/HARBIN"
  ],
  /**
   * Network name to Coinbase Pay SDK chain name map object
   * @see supported chain names on Coinbase for Pay SDK: https://github.com/coinbase/cbpay-js/blob/d4bda2c05c4d5917c8db6a05476b603546046394/src/types/onramp.ts
   */
  WC_COINBASE_PAY_SDK_CHAINS: [
    "ethereum",
    "arbitrum",
    "polygon",
    "berachain",
    "avalanche-c-chain",
    "optimism",
    "celo",
    "base"
  ],
  WC_COINBASE_PAY_SDK_FALLBACK_CHAIN: "ethereum",
  WC_COINBASE_PAY_SDK_CHAIN_NAME_MAP: {
    Ethereum: "ethereum",
    "Arbitrum One": "arbitrum",
    Polygon: "polygon",
    Berachain: "berachain",
    Avalanche: "avalanche-c-chain",
    "OP Mainnet": "optimism",
    Celo: "celo",
    Base: "base"
  },
  WC_COINBASE_ONRAMP_APP_ID: "bf18c88d-495a-463b-b249-0b9d3656cf5e",
  SWAP_SUGGESTED_TOKENS: [
    "ETH",
    "UNI",
    "1INCH",
    "AAVE",
    "SOL",
    "ADA",
    "AVAX",
    "DOT",
    "LINK",
    "NITRO",
    "GAIA",
    "MILK",
    "TRX",
    "NEAR",
    "GNO",
    "WBTC",
    "DAI",
    "WETH",
    "USDC",
    "USDT",
    "ARB",
    "BAL",
    "BICO",
    "CRV",
    "ENS",
    "MATIC",
    "OP"
  ],
  SWAP_POPULAR_TOKENS: [
    "ETH",
    "UNI",
    "1INCH",
    "AAVE",
    "SOL",
    "ADA",
    "AVAX",
    "DOT",
    "LINK",
    "NITRO",
    "GAIA",
    "MILK",
    "TRX",
    "NEAR",
    "GNO",
    "WBTC",
    "DAI",
    "WETH",
    "USDC",
    "USDT",
    "ARB",
    "BAL",
    "BICO",
    "CRV",
    "ENS",
    "MATIC",
    "OP",
    "METAL",
    "DAI",
    "CHAMP",
    "WOLF",
    "SALE",
    "BAL",
    "BUSD",
    "MUST",
    "BTCpx",
    "ROUTE",
    "HEX",
    "WELT",
    "amDAI",
    "VSQ",
    "VISION",
    "AURUM",
    "pSP",
    "SNX",
    "VC",
    "LINK",
    "CHP",
    "amUSDT",
    "SPHERE",
    "FOX",
    "GIDDY",
    "GFC",
    "OMEN",
    "OX_OLD",
    "DE",
    "WNT"
  ],
  BALANCE_SUPPORTED_CHAINS: ["eip155", "solana"],
  SWAP_SUPPORTED_NETWORKS: [
    // Ethereum'
    "eip155:1",
    // Arbitrum One'
    "eip155:42161",
    // Optimism'
    "eip155:10",
    // ZKSync Era'
    "eip155:324",
    // Base'
    "eip155:8453",
    // BNB Smart Chain'
    "eip155:56",
    // Polygon'
    "eip155:137",
    // Gnosis'
    "eip155:100",
    // Avalanche'
    "eip155:43114",
    // Fantom'
    "eip155:250",
    // Klaytn'
    "eip155:8217",
    // Aurora
    "eip155:1313161554"
  ],
  NAMES_SUPPORTED_CHAIN_NAMESPACES: ["eip155"],
  ONRAMP_SUPPORTED_CHAIN_NAMESPACES: ["eip155", "solana"],
  ACTIVITY_ENABLED_CHAIN_NAMESPACES: ["eip155"],
  NATIVE_TOKEN_ADDRESS: {
    eip155: "******************************************",
    solana: "So11111111111111111111111111111111111111111",
    polkadot: "0x",
    bip122: "0x",
    cosmos: "0x"
  },
  CONVERT_SLIPPAGE_TOLERANCE: 1,
  CONNECT_LABELS: {
    MOBILE: "Open and continue in the wallet app",
    WEB: "Open and continue in the wallet app"
  },
  SEND_SUPPORTED_NAMESPACES: ["eip155", "solana"],
  DEFAULT_REMOTE_FEATURES: {
    swaps: ["1inch"],
    onramp: ["coinbase", "meld"],
    email: true,
    socials: [
      "google",
      "x",
      "discord",
      "farcaster",
      "github",
      "apple",
      "facebook"
    ],
    activity: true,
    reownBranding: true
  },
  DEFAULT_REMOTE_FEATURES_DISABLED: {
    email: false,
    socials: false,
    swaps: false,
    onramp: false,
    activity: false,
    reownBranding: false
  },
  DEFAULT_FEATURES: {
    receive: true,
    send: true,
    emailShowWallets: true,
    connectorTypeOrder: [
      "walletConnect",
      "recent",
      "injected",
      "featured",
      "custom",
      "external",
      "recommended"
    ],
    analytics: true,
    allWallets: true,
    legalCheckbox: false,
    smartSessions: false,
    collapseWallets: false,
    walletFeaturesOrder: ["onramp", "swaps", "receive", "send"],
    connectMethodsOrder: void 0,
    pay: false
  },
  DEFAULT_SOCIALS: [
    "google",
    "x",
    "farcaster",
    "discord",
    "apple",
    "github",
    "facebook"
  ],
  DEFAULT_ACCOUNT_TYPES: {
    bip122: "payment",
    eip155: "smartAccount",
    polkadot: "eoa",
    solana: "eoa"
  },
  ADAPTER_TYPES: {
    UNIVERSAL: "universal",
    SOLANA: "solana",
    WAGMI: "wagmi",
    ETHERS: "ethers",
    ETHERS5: "ethers5",
    BITCOIN: "bitcoin"
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/CoreHelperUtil.js
var CoreHelperUtil = {
  isMobile() {
    var _a;
    if (this.isClient()) {
      return Boolean(typeof (window == null ? void 0 : window.matchMedia) === "function" && ((_a = window == null ? void 0 : window.matchMedia("(pointer:coarse)")) == null ? void 0 : _a.matches) || /Android|webOS|iPhone|iPad|iPod|BlackBerry|Opera Mini/u.test(navigator.userAgent));
    }
    return false;
  },
  checkCaipNetwork(network, networkName = "") {
    return network == null ? void 0 : network.caipNetworkId.toLocaleLowerCase().includes(networkName.toLowerCase());
  },
  isAndroid() {
    if (!this.isMobile()) {
      return false;
    }
    const ua2 = window == null ? void 0 : window.navigator.userAgent.toLowerCase();
    return CoreHelperUtil.isMobile() && ua2.includes("android");
  },
  isIos() {
    if (!this.isMobile()) {
      return false;
    }
    const ua2 = window == null ? void 0 : window.navigator.userAgent.toLowerCase();
    return ua2.includes("iphone") || ua2.includes("ipad");
  },
  isSafari() {
    if (!this.isClient()) {
      return false;
    }
    const ua2 = window == null ? void 0 : window.navigator.userAgent.toLowerCase();
    return ua2.includes("safari");
  },
  isClient() {
    return typeof window !== "undefined";
  },
  isPairingExpired(expiry) {
    return expiry ? expiry - Date.now() <= ConstantsUtil2.TEN_SEC_MS : true;
  },
  isAllowedRetry(lastRetry, differenceMs = ConstantsUtil2.ONE_SEC_MS) {
    return Date.now() - lastRetry >= differenceMs;
  },
  copyToClopboard(text) {
    navigator.clipboard.writeText(text);
  },
  isIframe() {
    try {
      return (window == null ? void 0 : window.self) !== (window == null ? void 0 : window.top);
    } catch (e2) {
      return false;
    }
  },
  isSafeApp() {
    var _a, _b;
    if (CoreHelperUtil.isClient() && window.self !== window.top) {
      try {
        const ancestor = (_b = (_a = window == null ? void 0 : window.location) == null ? void 0 : _a.ancestorOrigins) == null ? void 0 : _b[0];
        const safeAppUrl = "https://app.safe.global";
        if (ancestor) {
          const ancestorUrl = new URL(ancestor);
          const safeUrl = new URL(safeAppUrl);
          return ancestorUrl.hostname === safeUrl.hostname;
        }
      } catch {
        return false;
      }
    }
    return false;
  },
  getPairingExpiry() {
    return Date.now() + ConstantsUtil2.FOUR_MINUTES_MS;
  },
  getNetworkId(caipAddress) {
    return caipAddress == null ? void 0 : caipAddress.split(":")[1];
  },
  getPlainAddress(caipAddress) {
    return caipAddress == null ? void 0 : caipAddress.split(":")[2];
  },
  async wait(milliseconds) {
    return new Promise((resolve) => {
      setTimeout(resolve, milliseconds);
    });
  },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  debounce(func, timeout = 500) {
    let timer = void 0;
    return (...args) => {
      function next() {
        func(...args);
      }
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(next, timeout);
    };
  },
  isHttpUrl(url) {
    return url.startsWith("http://") || url.startsWith("https://");
  },
  formatNativeUrl(appUrl, wcUri, universalLink = null) {
    if (CoreHelperUtil.isHttpUrl(appUrl)) {
      return this.formatUniversalUrl(appUrl, wcUri);
    }
    let safeAppUrl = appUrl;
    let safeUniversalLink = universalLink;
    if (!safeAppUrl.includes("://")) {
      safeAppUrl = appUrl.replaceAll("/", "").replaceAll(":", "");
      safeAppUrl = `${safeAppUrl}://`;
    }
    if (!safeAppUrl.endsWith("/")) {
      safeAppUrl = `${safeAppUrl}/`;
    }
    if (safeUniversalLink && !(safeUniversalLink == null ? void 0 : safeUniversalLink.endsWith("/"))) {
      safeUniversalLink = `${safeUniversalLink}/`;
    }
    if (this.isTelegram() && this.isAndroid()) {
      wcUri = encodeURIComponent(wcUri);
    }
    const encodedWcUrl = encodeURIComponent(wcUri);
    return {
      redirect: `${safeAppUrl}wc?uri=${encodedWcUrl}`,
      redirectUniversalLink: safeUniversalLink ? `${safeUniversalLink}wc?uri=${encodedWcUrl}` : void 0,
      href: safeAppUrl
    };
  },
  formatUniversalUrl(appUrl, wcUri) {
    if (!CoreHelperUtil.isHttpUrl(appUrl)) {
      return this.formatNativeUrl(appUrl, wcUri);
    }
    let safeAppUrl = appUrl;
    if (!safeAppUrl.endsWith("/")) {
      safeAppUrl = `${safeAppUrl}/`;
    }
    const encodedWcUrl = encodeURIComponent(wcUri);
    return {
      redirect: `${safeAppUrl}wc?uri=${encodedWcUrl}`,
      href: safeAppUrl
    };
  },
  getOpenTargetForPlatform(target) {
    if (target === "popupWindow") {
      return target;
    }
    if (this.isTelegram()) {
      if (StorageUtil.getTelegramSocialProvider()) {
        return "_top";
      }
      return "_blank";
    }
    return target;
  },
  openHref(href, target, features) {
    window == null ? void 0 : window.open(href, this.getOpenTargetForPlatform(target), features || "noreferrer noopener");
  },
  returnOpenHref(href, target, features) {
    return window == null ? void 0 : window.open(href, this.getOpenTargetForPlatform(target), features || "noreferrer noopener");
  },
  isTelegram() {
    return typeof window !== "undefined" && // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (Boolean(window.TelegramWebviewProxy) || // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Boolean(window.Telegram) || // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Boolean(window.TelegramWebviewProxyProto));
  },
  isPWA() {
    var _a, _b, _c2;
    if (typeof window === "undefined") {
      return false;
    }
    const isStandaloneDisplayMode = (_b = (_a = window.matchMedia) == null ? void 0 : _a.call(window, "(display-mode: standalone)")) == null ? void 0 : _b.matches;
    const isIOSStandalone = (_c2 = window == null ? void 0 : window.navigator) == null ? void 0 : _c2.standalone;
    return Boolean(isStandaloneDisplayMode || isIOSStandalone);
  },
  async preloadImage(src) {
    const imagePromise = new Promise((resolve, reject) => {
      const image = new Image();
      image.onload = resolve;
      image.onerror = reject;
      image.crossOrigin = "anonymous";
      image.src = src;
    });
    return Promise.race([imagePromise, CoreHelperUtil.wait(2e3)]);
  },
  formatBalance(balance, symbol) {
    let formattedBalance = "0.000";
    if (typeof balance === "string") {
      const number = Number(balance);
      if (number) {
        const formattedValue = Math.floor(number * 1e3) / 1e3;
        if (formattedValue) {
          formattedBalance = formattedValue.toString();
        }
      }
    }
    return `${formattedBalance}${symbol ? ` ${symbol}` : ""}`;
  },
  formatBalance2(balance, symbol) {
    var _a;
    let formattedBalance = void 0;
    if (balance === "0") {
      formattedBalance = "0";
    } else if (typeof balance === "string") {
      const number = Number(balance);
      if (number) {
        formattedBalance = (_a = number.toString().match(/^-?\d+(?:\.\d{0,3})?/u)) == null ? void 0 : _a[0];
      }
    }
    return {
      value: formattedBalance ?? "0",
      rest: formattedBalance === "0" ? "000" : "",
      symbol
    };
  },
  getApiUrl() {
    return ConstantsUtil.W3M_API_URL;
  },
  getBlockchainApiUrl() {
    return ConstantsUtil.BLOCKCHAIN_API_RPC_URL;
  },
  getAnalyticsUrl() {
    return ConstantsUtil.PULSE_API_URL;
  },
  getUUID() {
    if (crypto == null ? void 0 : crypto.randomUUID) {
      return crypto.randomUUID();
    }
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/gu, (c2) => {
      const r2 = Math.random() * 16 | 0;
      const v = c2 === "x" ? r2 : r2 & 3 | 8;
      return v.toString(16);
    });
  },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  parseError(error) {
    var _a, _b;
    if (typeof error === "string") {
      return error;
    } else if (typeof ((_b = (_a = error == null ? void 0 : error.issues) == null ? void 0 : _a[0]) == null ? void 0 : _b.message) === "string") {
      return error.issues[0].message;
    } else if (error instanceof Error) {
      return error.message;
    }
    return "Unknown error";
  },
  sortRequestedNetworks(approvedIds, requestedNetworks = []) {
    const approvedIndexMap = {};
    if (requestedNetworks && approvedIds) {
      approvedIds.forEach((id, index) => {
        approvedIndexMap[id] = index;
      });
      requestedNetworks.sort((a, b) => {
        const indexA = approvedIndexMap[a.id];
        const indexB = approvedIndexMap[b.id];
        if (indexA !== void 0 && indexB !== void 0) {
          return indexA - indexB;
        } else if (indexA !== void 0) {
          return -1;
        } else if (indexB !== void 0) {
          return 1;
        }
        return 0;
      });
    }
    return requestedNetworks;
  },
  calculateBalance(array) {
    let sum = 0;
    for (const item of array) {
      sum += item.value ?? 0;
    }
    return sum;
  },
  formatTokenBalance(number) {
    const roundedNumber = number.toFixed(2);
    const [dollars, pennies] = roundedNumber.split(".");
    return { dollars, pennies };
  },
  isAddress(address, chain = "eip155") {
    switch (chain) {
      case "eip155":
        if (!/^(?:0x)?[0-9a-f]{40}$/iu.test(address)) {
          return false;
        } else if (/^(?:0x)?[0-9a-f]{40}$/iu.test(address) || /^(?:0x)?[0-9A-F]{40}$/iu.test(address)) {
          return true;
        }
        return false;
      case "solana":
        return /[1-9A-HJ-NP-Za-km-z]{32,44}$/iu.test(address);
      default:
        return false;
    }
  },
  uniqueBy(arr, key) {
    const set = /* @__PURE__ */ new Set();
    return arr.filter((item) => {
      const keyValue = item[key];
      if (set.has(keyValue)) {
        return false;
      }
      set.add(keyValue);
      return true;
    });
  },
  generateSdkVersion(adapters, platform, version2) {
    const hasNoAdapters = adapters.length === 0;
    const adapterNames = hasNoAdapters ? ConstantsUtil2.ADAPTER_TYPES.UNIVERSAL : adapters.map((adapter) => adapter.adapterType).join(",");
    return `${platform}-${adapterNames}-${version2}`;
  },
  // eslint-disable-next-line max-params
  createAccount(namespace, address, type, publicKey, path) {
    return {
      namespace,
      address,
      type,
      publicKey,
      path
    };
  },
  isCaipAddress(address) {
    if (typeof address !== "string") {
      return false;
    }
    const sections = address.split(":");
    const namespace = sections[0];
    return sections.filter(Boolean).length === 3 && namespace in ConstantsUtil.CHAIN_NAME_MAP;
  },
  isMac() {
    const ua2 = window == null ? void 0 : window.navigator.userAgent.toLowerCase();
    return ua2.includes("macintosh") && !ua2.includes("safari");
  },
  formatTelegramSocialLoginUrl(url) {
    const valueToInject = `--${encodeURIComponent(window == null ? void 0 : window.location.href)}`;
    const paramToInject = "state=";
    const parsedUrl = new URL(url);
    if (parsedUrl.host === "auth.magic.link") {
      const providerParam = "provider_authorization_url=";
      const providerUrl = url.substring(url.indexOf(providerParam) + providerParam.length);
      const resultUrl = this.injectIntoUrl(decodeURIComponent(providerUrl), paramToInject, valueToInject);
      return url.replace(providerUrl, encodeURIComponent(resultUrl));
    }
    return this.injectIntoUrl(url, paramToInject, valueToInject);
  },
  injectIntoUrl(url, key, appendString) {
    const keyIndex = url.indexOf(key);
    if (keyIndex === -1) {
      throw new Error(`${key} parameter not found in the URL: ${url}`);
    }
    const keyEndIndex = url.indexOf("&", keyIndex);
    const keyLength = key.length;
    const keyParamEnd = keyEndIndex !== -1 ? keyEndIndex : url.length;
    const beforeKeyValue = url.substring(0, keyIndex + keyLength);
    const currentKeyValue = url.substring(keyIndex + keyLength, keyParamEnd);
    const afterKeyValue = url.substring(keyEndIndex);
    const newKeyValue = currentKeyValue + appendString;
    const newUrl = beforeKeyValue + newKeyValue + afterKeyValue;
    return newUrl;
  }
};

// node_modules/proxy-compare/dist/index.modern.js
var e = Symbol();
var t = Symbol();
var s = Object.getPrototypeOf;
var c = /* @__PURE__ */ new WeakMap();
var l = (e2) => e2 && (c.has(e2) ? c.get(e2) : s(e2) === Object.prototype || s(e2) === Array.prototype);
var y = (e2) => l(e2) && e2[t] || null;
var h = (e2, t2 = true) => {
  c.set(e2, t2);
};

// node_modules/valtio/esm/vanilla.mjs
var isObject = (x) => typeof x === "object" && x !== null;
var proxyStateMap = /* @__PURE__ */ new WeakMap();
var refSet = /* @__PURE__ */ new WeakSet();
var buildProxyFunction = (objectIs = Object.is, newProxy = (target, handler) => new Proxy(target, handler), canProxy = (x) => isObject(x) && !refSet.has(x) && (Array.isArray(x) || !(Symbol.iterator in x)) && !(x instanceof WeakMap) && !(x instanceof WeakSet) && !(x instanceof Error) && !(x instanceof Number) && !(x instanceof Date) && !(x instanceof String) && !(x instanceof RegExp) && !(x instanceof ArrayBuffer), defaultHandlePromise = (promise) => {
  switch (promise.status) {
    case "fulfilled":
      return promise.value;
    case "rejected":
      throw promise.reason;
    default:
      throw promise;
  }
}, snapCache = /* @__PURE__ */ new WeakMap(), createSnapshot = (target, version2, handlePromise = defaultHandlePromise) => {
  const cache = snapCache.get(target);
  if ((cache == null ? void 0 : cache[0]) === version2) {
    return cache[1];
  }
  const snap = Array.isArray(target) ? [] : Object.create(Object.getPrototypeOf(target));
  h(snap, true);
  snapCache.set(target, [version2, snap]);
  Reflect.ownKeys(target).forEach((key) => {
    if (Object.getOwnPropertyDescriptor(snap, key)) {
      return;
    }
    const value = Reflect.get(target, key);
    const { enumerable } = Reflect.getOwnPropertyDescriptor(
      target,
      key
    );
    const desc = {
      value,
      enumerable,
      // This is intentional to avoid copying with proxy-compare.
      // It's still non-writable, so it avoids assigning a value.
      configurable: true
    };
    if (refSet.has(value)) {
      h(value, false);
    } else if (value instanceof Promise) {
      delete desc.value;
      desc.get = () => handlePromise(value);
    } else if (proxyStateMap.has(value)) {
      const [target2, ensureVersion] = proxyStateMap.get(
        value
      );
      desc.value = createSnapshot(
        target2,
        ensureVersion(),
        handlePromise
      );
    }
    Object.defineProperty(snap, key, desc);
  });
  return Object.preventExtensions(snap);
}, proxyCache = /* @__PURE__ */ new WeakMap(), versionHolder = [1, 1], proxyFunction = (initialObject) => {
  if (!isObject(initialObject)) {
    throw new Error("object required");
  }
  const found = proxyCache.get(initialObject);
  if (found) {
    return found;
  }
  let version2 = versionHolder[0];
  const listeners = /* @__PURE__ */ new Set();
  const notifyUpdate = (op, nextVersion = ++versionHolder[0]) => {
    if (version2 !== nextVersion) {
      version2 = nextVersion;
      listeners.forEach((listener) => listener(op, nextVersion));
    }
  };
  let checkVersion = versionHolder[1];
  const ensureVersion = (nextCheckVersion = ++versionHolder[1]) => {
    if (checkVersion !== nextCheckVersion && !listeners.size) {
      checkVersion = nextCheckVersion;
      propProxyStates.forEach(([propProxyState]) => {
        const propVersion = propProxyState[1](nextCheckVersion);
        if (propVersion > version2) {
          version2 = propVersion;
        }
      });
    }
    return version2;
  };
  const createPropListener = (prop) => (op, nextVersion) => {
    const newOp = [...op];
    newOp[1] = [prop, ...newOp[1]];
    notifyUpdate(newOp, nextVersion);
  };
  const propProxyStates = /* @__PURE__ */ new Map();
  const addPropListener = (prop, propProxyState) => {
    if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production" && propProxyStates.has(prop)) {
      throw new Error("prop listener already exists");
    }
    if (listeners.size) {
      const remove = propProxyState[3](createPropListener(prop));
      propProxyStates.set(prop, [propProxyState, remove]);
    } else {
      propProxyStates.set(prop, [propProxyState]);
    }
  };
  const removePropListener = (prop) => {
    var _a;
    const entry = propProxyStates.get(prop);
    if (entry) {
      propProxyStates.delete(prop);
      (_a = entry[1]) == null ? void 0 : _a.call(entry);
    }
  };
  const addListener = (listener) => {
    listeners.add(listener);
    if (listeners.size === 1) {
      propProxyStates.forEach(([propProxyState, prevRemove], prop) => {
        if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production" && prevRemove) {
          throw new Error("remove already exists");
        }
        const remove = propProxyState[3](createPropListener(prop));
        propProxyStates.set(prop, [propProxyState, remove]);
      });
    }
    const removeListener = () => {
      listeners.delete(listener);
      if (listeners.size === 0) {
        propProxyStates.forEach(([propProxyState, remove], prop) => {
          if (remove) {
            remove();
            propProxyStates.set(prop, [propProxyState]);
          }
        });
      }
    };
    return removeListener;
  };
  const baseObject = Array.isArray(initialObject) ? [] : Object.create(Object.getPrototypeOf(initialObject));
  const handler = {
    deleteProperty(target, prop) {
      const prevValue = Reflect.get(target, prop);
      removePropListener(prop);
      const deleted = Reflect.deleteProperty(target, prop);
      if (deleted) {
        notifyUpdate(["delete", [prop], prevValue]);
      }
      return deleted;
    },
    set(target, prop, value, receiver) {
      const hasPrevValue = Reflect.has(target, prop);
      const prevValue = Reflect.get(target, prop, receiver);
      if (hasPrevValue && (objectIs(prevValue, value) || proxyCache.has(value) && objectIs(prevValue, proxyCache.get(value)))) {
        return true;
      }
      removePropListener(prop);
      if (isObject(value)) {
        value = y(value) || value;
      }
      let nextValue = value;
      if (value instanceof Promise) {
        value.then((v) => {
          value.status = "fulfilled";
          value.value = v;
          notifyUpdate(["resolve", [prop], v]);
        }).catch((e2) => {
          value.status = "rejected";
          value.reason = e2;
          notifyUpdate(["reject", [prop], e2]);
        });
      } else {
        if (!proxyStateMap.has(value) && canProxy(value)) {
          nextValue = proxyFunction(value);
        }
        const childProxyState = !refSet.has(nextValue) && proxyStateMap.get(nextValue);
        if (childProxyState) {
          addPropListener(prop, childProxyState);
        }
      }
      Reflect.set(target, prop, nextValue, receiver);
      notifyUpdate(["set", [prop], value, prevValue]);
      return true;
    }
  };
  const proxyObject = newProxy(baseObject, handler);
  proxyCache.set(initialObject, proxyObject);
  const proxyState = [
    baseObject,
    ensureVersion,
    createSnapshot,
    addListener
  ];
  proxyStateMap.set(proxyObject, proxyState);
  Reflect.ownKeys(initialObject).forEach((key) => {
    const desc = Object.getOwnPropertyDescriptor(
      initialObject,
      key
    );
    if ("value" in desc) {
      proxyObject[key] = initialObject[key];
      delete desc.value;
      delete desc.writable;
    }
    Object.defineProperty(baseObject, key, desc);
  });
  return proxyObject;
}) => [
  // public functions
  proxyFunction,
  // shared state
  proxyStateMap,
  refSet,
  // internal things
  objectIs,
  newProxy,
  canProxy,
  defaultHandlePromise,
  snapCache,
  createSnapshot,
  proxyCache,
  versionHolder
];
var [defaultProxyFunction] = buildProxyFunction();
function proxy(initialObject = {}) {
  return defaultProxyFunction(initialObject);
}
function subscribe(proxyObject, callback, notifyInSync) {
  const proxyState = proxyStateMap.get(proxyObject);
  if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production" && !proxyState) {
    console.warn("Please use proxy object");
  }
  let promise;
  const ops = [];
  const addListener = proxyState[3];
  let isListenerActive = false;
  const listener = (op) => {
    ops.push(op);
    if (notifyInSync) {
      callback(ops.splice(0));
      return;
    }
    if (!promise) {
      promise = Promise.resolve().then(() => {
        promise = void 0;
        if (isListenerActive) {
          callback(ops.splice(0));
        }
      });
    }
  };
  const removeListener = addListener(listener);
  isListenerActive = true;
  return () => {
    isListenerActive = false;
    removeListener();
  };
}
function snapshot(proxyObject, handlePromise) {
  const proxyState = proxyStateMap.get(proxyObject);
  if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production" && !proxyState) {
    console.warn("Please use proxy object");
  }
  const [target, ensureVersion, createSnapshot] = proxyState;
  return createSnapshot(target, ensureVersion(), handlePromise);
}
function ref(obj) {
  refSet.add(obj);
  return obj;
}

// node_modules/valtio/esm/vanilla/utils.mjs
function subscribeKey(proxyObject, key, callback, notifyInSync) {
  let prevValue = proxyObject[key];
  return subscribe(
    proxyObject,
    () => {
      const nextValue = proxyObject[key];
      if (!Object.is(prevValue, nextValue)) {
        callback(prevValue = nextValue);
      }
    },
    notifyInSync
  );
}
var DEVTOOLS = Symbol();
function proxyMap(entries2) {
  const map = proxy({
    data: Array.from(entries2 || []),
    has(key) {
      return this.data.some((p) => p[0] === key);
    },
    set(key, value) {
      const record = this.data.find((p) => p[0] === key);
      if (record) {
        record[1] = value;
      } else {
        this.data.push([key, value]);
      }
      return this;
    },
    get(key) {
      var _a;
      return (_a = this.data.find((p) => p[0] === key)) == null ? void 0 : _a[1];
    },
    delete(key) {
      const index = this.data.findIndex((p) => p[0] === key);
      if (index === -1) {
        return false;
      }
      this.data.splice(index, 1);
      return true;
    },
    clear() {
      this.data.splice(0);
    },
    get size() {
      return this.data.length;
    },
    toJSON() {
      return new Map(this.data);
    },
    forEach(cb) {
      this.data.forEach((p) => {
        cb(p[1], p[0], this);
      });
    },
    keys() {
      return this.data.map((p) => p[0]).values();
    },
    values() {
      return this.data.map((p) => p[1]).values();
    },
    entries() {
      return new Map(this.data).entries();
    },
    get [Symbol.toStringTag]() {
      return "Map";
    },
    [Symbol.iterator]() {
      return this.entries();
    }
  });
  Object.defineProperties(map, {
    data: {
      enumerable: false
    },
    size: {
      enumerable: false
    },
    toJSON: {
      enumerable: false
    }
  });
  Object.seal(map);
  return map;
}

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/OptionsUtil.js
var OptionsUtil = {
  getFeatureValue(key, features) {
    const optionValue = features == null ? void 0 : features[key];
    if (optionValue === void 0) {
      return ConstantsUtil2.DEFAULT_FEATURES[key];
    }
    return optionValue;
  },
  filterSocialsByPlatform(socials) {
    if (!socials || !socials.length) {
      return socials;
    }
    if (CoreHelperUtil.isTelegram()) {
      if (CoreHelperUtil.isIos()) {
        return socials.filter((s2) => s2 !== "google");
      }
      if (CoreHelperUtil.isMac()) {
        return socials.filter((s2) => s2 !== "x");
      }
      if (CoreHelperUtil.isAndroid()) {
        return socials.filter((s2) => !["facebook", "x"].includes(s2));
      }
    }
    return socials;
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/OptionsController.js
var state = proxy({
  features: ConstantsUtil2.DEFAULT_FEATURES,
  projectId: "",
  sdkType: "appkit",
  sdkVersion: "html-wagmi-undefined",
  defaultAccountTypes: ConstantsUtil2.DEFAULT_ACCOUNT_TYPES,
  enableNetworkSwitch: true,
  experimental_preferUniversalLinks: false,
  remoteFeatures: {}
});
var OptionsController = {
  state,
  subscribeKey(key, callback) {
    return subscribeKey(state, key, callback);
  },
  setOptions(options) {
    Object.assign(state, options);
  },
  setRemoteFeatures(remoteFeatures) {
    var _a;
    if (!remoteFeatures) {
      return;
    }
    const newRemoteFeatures = { ...state.remoteFeatures, ...remoteFeatures };
    state.remoteFeatures = newRemoteFeatures;
    if ((_a = state.remoteFeatures) == null ? void 0 : _a.socials) {
      state.remoteFeatures.socials = OptionsUtil.filterSocialsByPlatform(state.remoteFeatures.socials);
    }
  },
  setFeatures(features) {
    if (!features) {
      return;
    }
    if (!state.features) {
      state.features = ConstantsUtil2.DEFAULT_FEATURES;
    }
    const newFeatures = { ...state.features, ...features };
    state.features = newFeatures;
  },
  setProjectId(projectId) {
    state.projectId = projectId;
  },
  setCustomRpcUrls(customRpcUrls) {
    state.customRpcUrls = customRpcUrls;
  },
  setAllWallets(allWallets) {
    state.allWallets = allWallets;
  },
  setIncludeWalletIds(includeWalletIds) {
    state.includeWalletIds = includeWalletIds;
  },
  setExcludeWalletIds(excludeWalletIds) {
    state.excludeWalletIds = excludeWalletIds;
  },
  setFeaturedWalletIds(featuredWalletIds) {
    state.featuredWalletIds = featuredWalletIds;
  },
  setTokens(tokens) {
    state.tokens = tokens;
  },
  setTermsConditionsUrl(termsConditionsUrl) {
    state.termsConditionsUrl = termsConditionsUrl;
  },
  setPrivacyPolicyUrl(privacyPolicyUrl) {
    state.privacyPolicyUrl = privacyPolicyUrl;
  },
  setCustomWallets(customWallets) {
    state.customWallets = customWallets;
  },
  setIsSiweEnabled(isSiweEnabled) {
    state.isSiweEnabled = isSiweEnabled;
  },
  setIsUniversalProvider(isUniversalProvider) {
    state.isUniversalProvider = isUniversalProvider;
  },
  setSdkVersion(sdkVersion) {
    state.sdkVersion = sdkVersion;
  },
  setMetadata(metadata) {
    state.metadata = metadata;
  },
  setDisableAppend(disableAppend) {
    state.disableAppend = disableAppend;
  },
  setEIP6963Enabled(enableEIP6963) {
    state.enableEIP6963 = enableEIP6963;
  },
  setDebug(debug) {
    state.debug = debug;
  },
  setEnableWalletConnect(enableWalletConnect) {
    state.enableWalletConnect = enableWalletConnect;
  },
  setEnableWalletGuide(enableWalletGuide) {
    state.enableWalletGuide = enableWalletGuide;
  },
  setEnableAuthLogger(enableAuthLogger) {
    state.enableAuthLogger = enableAuthLogger;
  },
  setEnableWallets(enableWallets) {
    state.enableWallets = enableWallets;
  },
  setPreferUniversalLinks(preferUniversalLinks) {
    state.experimental_preferUniversalLinks = preferUniversalLinks;
  },
  setHasMultipleAddresses(hasMultipleAddresses) {
    state.hasMultipleAddresses = hasMultipleAddresses;
  },
  setSIWX(siwx) {
    state.siwx = siwx;
  },
  setConnectMethodsOrder(connectMethodsOrder) {
    state.features = {
      ...state.features,
      connectMethodsOrder
    };
  },
  setWalletFeaturesOrder(walletFeaturesOrder) {
    state.features = {
      ...state.features,
      walletFeaturesOrder
    };
  },
  setSocialsOrder(socialsOrder) {
    state.remoteFeatures = {
      ...state.remoteFeatures,
      socials: socialsOrder
    };
  },
  setCollapseWallets(collapseWallets) {
    state.features = {
      ...state.features,
      collapseWallets
    };
  },
  setEnableEmbedded(enableEmbedded) {
    state.enableEmbedded = enableEmbedded;
  },
  setAllowUnsupportedChain(allowUnsupportedChain) {
    state.allowUnsupportedChain = allowUnsupportedChain;
  },
  setManualWCControl(manualWCControl) {
    state.manualWCControl = manualWCControl;
  },
  setEnableNetworkSwitch(enableNetworkSwitch) {
    state.enableNetworkSwitch = enableNetworkSwitch;
  },
  setDefaultAccountTypes(defaultAccountType = {}) {
    Object.entries(defaultAccountType).forEach(([namespace, accountType]) => {
      if (accountType) {
        state.defaultAccountTypes[namespace] = accountType;
      }
    });
  },
  setUniversalProviderConfigOverride(universalProviderConfigOverride) {
    state.universalProviderConfigOverride = universalProviderConfigOverride;
  },
  getUniversalProviderConfigOverride() {
    return state.universalProviderConfigOverride;
  },
  getSnapshot() {
    return snapshot(state);
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/FetchUtil.js
async function fetchData(...args) {
  const response = await fetch(...args);
  if (!response.ok) {
    const err = new Error(`HTTP status code: ${response.status}`, {
      cause: response
    });
    throw err;
  }
  return response;
}
var FetchUtil = class {
  constructor({ baseUrl: baseUrl4, clientId }) {
    this.baseUrl = baseUrl4;
    this.clientId = clientId;
  }
  async get({ headers, signal, cache, ...args }) {
    const url = this.createUrl(args);
    const response = await fetchData(url, { method: "GET", headers, signal, cache });
    return response.json();
  }
  async getBlob({ headers, signal, ...args }) {
    const url = this.createUrl(args);
    const response = await fetchData(url, { method: "GET", headers, signal });
    return response.blob();
  }
  async post({ body, headers, signal, ...args }) {
    const url = this.createUrl(args);
    const response = await fetchData(url, {
      method: "POST",
      headers,
      body: body ? JSON.stringify(body) : void 0,
      signal
    });
    return response.json();
  }
  async put({ body, headers, signal, ...args }) {
    const url = this.createUrl(args);
    const response = await fetchData(url, {
      method: "PUT",
      headers,
      body: body ? JSON.stringify(body) : void 0,
      signal
    });
    return response.json();
  }
  async delete({ body, headers, signal, ...args }) {
    const url = this.createUrl(args);
    const response = await fetchData(url, {
      method: "DELETE",
      headers,
      body: body ? JSON.stringify(body) : void 0,
      signal
    });
    return response.json();
  }
  createUrl({ path, params }) {
    const url = new URL(path, this.baseUrl);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value) {
          url.searchParams.append(key, value);
        }
      });
    }
    if (this.clientId) {
      url.searchParams.append("clientId", this.clientId);
    }
    return url;
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/TelemetryController.js
var DEFAULT_STATE = Object.freeze({
  enabled: true,
  events: []
});
var api = new FetchUtil({ baseUrl: CoreHelperUtil.getAnalyticsUrl(), clientId: null });
var MAX_ERRORS_PER_MINUTE = 5;
var ONE_MINUTE_MS = 60 * 1e3;
var state2 = proxy({
  ...DEFAULT_STATE
});
var TelemetryController = {
  state: state2,
  subscribeKey(key, callback) {
    return subscribeKey(state2, key, callback);
  },
  async sendError(error, category) {
    if (!state2.enabled) {
      return;
    }
    const now = Date.now();
    const recentErrors = state2.events.filter((event) => {
      const eventTime = new Date(event.properties.timestamp || "").getTime();
      return now - eventTime < ONE_MINUTE_MS;
    });
    if (recentErrors.length >= MAX_ERRORS_PER_MINUTE) {
      return;
    }
    const errorEvent = {
      type: "error",
      event: category,
      properties: {
        errorType: error.name,
        errorMessage: error.message,
        stackTrace: error.stack,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      }
    };
    state2.events.push(errorEvent);
    try {
      if (typeof window === "undefined") {
        return;
      }
      const { projectId, sdkType, sdkVersion } = OptionsController.state;
      await api.post({
        path: "/e",
        params: {
          projectId,
          st: sdkType,
          sv: sdkVersion || "html-wagmi-4.2.2"
        },
        body: {
          eventId: CoreHelperUtil.getUUID(),
          url: window.location.href,
          domain: window.location.hostname,
          timestamp: (/* @__PURE__ */ new Date()).toISOString(),
          props: {
            type: "error",
            event: category,
            errorType: error.name,
            errorMessage: error.message,
            stackTrace: error.stack
          }
        }
      });
    } catch {
    }
  },
  enable() {
    state2.enabled = true;
  },
  disable() {
    state2.enabled = false;
  },
  clearEvents() {
    state2.events = [];
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/withErrorBoundary.js
var AppKitError = class _AppKitError extends Error {
  constructor(message, category, originalError) {
    super(message);
    this.name = "AppKitError";
    this.category = category;
    this.originalError = originalError;
    Object.setPrototypeOf(this, _AppKitError.prototype);
    let isStackConstructedFromOriginal = false;
    if (originalError instanceof Error && typeof originalError.stack === "string" && originalError.stack) {
      const originalErrorStack = originalError.stack;
      const firstNewlineIndex = originalErrorStack.indexOf("\n");
      if (firstNewlineIndex > -1) {
        const originalFrames = originalErrorStack.substring(firstNewlineIndex + 1);
        this.stack = `${this.name}: ${this.message}
${originalFrames}`;
        isStackConstructedFromOriginal = true;
      }
    }
    if (!isStackConstructedFromOriginal) {
      if (Error.captureStackTrace) {
        Error.captureStackTrace(this, _AppKitError);
      } else if (!this.stack) {
        this.stack = `${this.name}: ${this.message}`;
      }
    }
  }
};
function errorHandler(err, defaultCategory) {
  const error = err instanceof AppKitError ? err : new AppKitError(err instanceof Error ? err.message : String(err), defaultCategory, err);
  TelemetryController.sendError(error, error.category);
  throw error;
}
function withErrorBoundary(controller17, defaultCategory = "INTERNAL_SDK_ERROR") {
  const newController = {};
  Object.keys(controller17).forEach((key) => {
    const original = controller17[key];
    if (typeof original === "function") {
      let wrapped = original;
      if (original.constructor.name === "AsyncFunction") {
        wrapped = async (...args) => {
          try {
            return await original(...args);
          } catch (err) {
            return errorHandler(err, defaultCategory);
          }
        };
      } else {
        wrapped = (...args) => {
          try {
            return original(...args);
          } catch (err) {
            return errorHandler(err, defaultCategory);
          }
        };
      }
      newController[key] = wrapped;
    } else {
      newController[key] = original;
    }
  });
  return newController;
}

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/AssetController.js
var state3 = proxy({
  walletImages: {},
  networkImages: {},
  chainImages: {},
  connectorImages: {},
  tokenImages: {},
  currencyImages: {}
});
var controller = {
  state: state3,
  subscribeNetworkImages(callback) {
    return subscribe(state3.networkImages, () => callback(state3.networkImages));
  },
  subscribeKey(key, callback) {
    return subscribeKey(state3, key, callback);
  },
  subscribe(callback) {
    return subscribe(state3, () => callback(state3));
  },
  setWalletImage(key, value) {
    state3.walletImages[key] = value;
  },
  setNetworkImage(key, value) {
    state3.networkImages[key] = value;
  },
  setChainImage(key, value) {
    state3.chainImages[key] = value;
  },
  setConnectorImage(key, value) {
    state3.connectorImages = { ...state3.connectorImages, [key]: value };
  },
  setTokenImage(key, value) {
    state3.tokenImages[key] = value;
  },
  setCurrencyImage(key, value) {
    state3.currencyImages[key] = value;
  }
};
var AssetController = withErrorBoundary(controller);

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/AssetUtil.js
var namespaceImageIds = {
  // Ethereum
  eip155: "ba0ba0cd-17c6-4806-ad93-f9d174f17900",
  // Solana
  solana: "a1b58899-f671-4276-6a5e-56ca5bd59700",
  // Polkadot
  polkadot: "",
  // Bitcoin
  bip122: "0b4838db-0161-4ffe-022d-532bf03dba00",
  // Cosmos
  cosmos: ""
};
var state4 = proxy({
  networkImagePromises: {}
});
var AssetUtil = {
  async fetchWalletImage(imageId) {
    if (!imageId) {
      return void 0;
    }
    await ApiController._fetchWalletImage(imageId);
    return this.getWalletImageById(imageId);
  },
  async fetchNetworkImage(imageId) {
    if (!imageId) {
      return void 0;
    }
    const existingImage = this.getNetworkImageById(imageId);
    if (existingImage) {
      return existingImage;
    }
    if (!state4.networkImagePromises[imageId]) {
      state4.networkImagePromises[imageId] = ApiController._fetchNetworkImage(imageId);
    }
    await state4.networkImagePromises[imageId];
    return this.getNetworkImageById(imageId);
  },
  getWalletImageById(imageId) {
    if (!imageId) {
      return void 0;
    }
    return AssetController.state.walletImages[imageId];
  },
  getWalletImage(wallet) {
    if (wallet == null ? void 0 : wallet.image_url) {
      return wallet == null ? void 0 : wallet.image_url;
    }
    if (wallet == null ? void 0 : wallet.image_id) {
      return AssetController.state.walletImages[wallet.image_id];
    }
    return void 0;
  },
  getNetworkImage(network) {
    var _a, _b, _c2;
    if ((_a = network == null ? void 0 : network.assets) == null ? void 0 : _a.imageUrl) {
      return (_b = network == null ? void 0 : network.assets) == null ? void 0 : _b.imageUrl;
    }
    if ((_c2 = network == null ? void 0 : network.assets) == null ? void 0 : _c2.imageId) {
      return AssetController.state.networkImages[network.assets.imageId];
    }
    return void 0;
  },
  getNetworkImageById(imageId) {
    if (!imageId) {
      return void 0;
    }
    return AssetController.state.networkImages[imageId];
  },
  getConnectorImage(connector) {
    if (connector == null ? void 0 : connector.imageUrl) {
      return connector.imageUrl;
    }
    if (connector == null ? void 0 : connector.imageId) {
      return AssetController.state.connectorImages[connector.imageId];
    }
    return void 0;
  },
  getChainImage(chain) {
    return AssetController.state.networkImages[namespaceImageIds[chain]];
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/MobileWallet.js
var CUSTOM_DEEPLINK_WALLETS = {
  PHANTOM: {
    id: "a797aa35c0fadbfc1a53e7f675162ed5226968b44a19ee3d24385c64d1d3c393",
    url: "https://phantom.app"
  },
  SOLFLARE: {
    id: "1ca0bdd4747578705b1939af023d120677c64fe6ca76add81fda36e350605e79",
    url: "https://solflare.com"
  },
  COINBASE: {
    id: "fd20dc426fb37566d803205b19bbc1d4096b248ac04548e3cfb6b3a38bd033aa",
    url: "https://go.cb-w.com"
  }
};
var MobileWalletUtil = {
  /**
   * Handles mobile wallet redirection for wallets that have Universal Links and doesn't support WalletConnect Deep Links.
   *
   * @param {string} id - The id of the wallet.
   * @param {ChainNamespace} namespace - The namespace of the chain.
   */
  handleMobileDeeplinkRedirect(id, namespace) {
    const href = window.location.href;
    const encodedHref = encodeURIComponent(href);
    if (id === CUSTOM_DEEPLINK_WALLETS.PHANTOM.id && !("phantom" in window)) {
      const protocol = href.startsWith("https") ? "https" : "http";
      const host = href.split("/")[2];
      const encodedRef = encodeURIComponent(`${protocol}://${host}`);
      window.location.href = `${CUSTOM_DEEPLINK_WALLETS.PHANTOM.url}/ul/browse/${encodedHref}?ref=${encodedRef}`;
    }
    if (id === CUSTOM_DEEPLINK_WALLETS.SOLFLARE.id && !("solflare" in window)) {
      window.location.href = `${CUSTOM_DEEPLINK_WALLETS.SOLFLARE.url}/ul/v1/browse/${encodedHref}?ref=${encodedHref}`;
    }
    if (namespace === ConstantsUtil.CHAIN.SOLANA) {
      if (id === CUSTOM_DEEPLINK_WALLETS.COINBASE.id && !("coinbaseSolana" in window)) {
        window.location.href = `${CUSTOM_DEEPLINK_WALLETS.COINBASE.url}/dapp?cb_url=${encodedHref}`;
      }
    }
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/SnackController.js
var DEFAULT_STATE2 = Object.freeze({
  message: "",
  variant: "success",
  svg: void 0,
  open: false,
  autoClose: true
});
var state5 = proxy({
  ...DEFAULT_STATE2
});
var controller2 = {
  state: state5,
  subscribeKey(key, callback) {
    return subscribeKey(state5, key, callback);
  },
  showLoading(message, options = {}) {
    this._showMessage({ message, variant: "loading", ...options });
  },
  showSuccess(message) {
    this._showMessage({ message, variant: "success" });
  },
  showSvg(message, svg) {
    this._showMessage({ message, svg });
  },
  showError(message) {
    const errorMessage = CoreHelperUtil.parseError(message);
    this._showMessage({ message: errorMessage, variant: "error" });
  },
  hide() {
    state5.message = DEFAULT_STATE2.message;
    state5.variant = DEFAULT_STATE2.variant;
    state5.svg = DEFAULT_STATE2.svg;
    state5.open = DEFAULT_STATE2.open;
    state5.autoClose = DEFAULT_STATE2.autoClose;
  },
  _showMessage({ message, svg, variant = "success", autoClose = DEFAULT_STATE2.autoClose }) {
    if (state5.open) {
      state5.open = false;
      setTimeout(() => {
        state5.message = message;
        state5.variant = variant;
        state5.svg = svg;
        state5.open = true;
        state5.autoClose = autoClose;
      }, 150);
    } else {
      state5.message = message;
      state5.variant = variant;
      state5.svg = svg;
      state5.open = true;
      state5.autoClose = autoClose;
    }
  }
};
var SnackController = controller2;

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/BlockchainApiController.js
var DEFAULT_OPTIONS = {
  purchaseCurrencies: [
    {
      id: "2b92315d-eab7-5bef-84fa-089a131333f5",
      name: "USD Coin",
      symbol: "USDC",
      networks: [
        {
          name: "ethereum-mainnet",
          display_name: "Ethereum",
          chain_id: "1",
          contract_address: "******************************************"
        },
        {
          name: "polygon-mainnet",
          display_name: "Polygon",
          chain_id: "137",
          contract_address: "******************************************"
        }
      ]
    },
    {
      id: "2b92315d-eab7-5bef-84fa-089a131333f5",
      name: "Ether",
      symbol: "ETH",
      networks: [
        {
          name: "ethereum-mainnet",
          display_name: "Ethereum",
          chain_id: "1",
          contract_address: "******************************************"
        },
        {
          name: "polygon-mainnet",
          display_name: "Polygon",
          chain_id: "137",
          contract_address: "******************************************"
        }
      ]
    }
  ],
  paymentCurrencies: [
    {
      id: "USD",
      payment_method_limits: [
        {
          id: "card",
          min: "10.00",
          max: "7500.00"
        },
        {
          id: "ach_bank_account",
          min: "10.00",
          max: "25000.00"
        }
      ]
    },
    {
      id: "EUR",
      payment_method_limits: [
        {
          id: "card",
          min: "10.00",
          max: "7500.00"
        },
        {
          id: "ach_bank_account",
          min: "10.00",
          max: "25000.00"
        }
      ]
    }
  ]
};
var baseUrl = CoreHelperUtil.getBlockchainApiUrl();
var state6 = proxy({
  clientId: null,
  api: new FetchUtil({ baseUrl, clientId: null }),
  supportedChains: { http: [], ws: [] }
});
var BlockchainApiController = {
  state: state6,
  async get(request) {
    const { st: st2, sv } = BlockchainApiController.getSdkProperties();
    const projectId = OptionsController.state.projectId;
    const params = {
      ...request.params || {},
      st: st2,
      sv,
      projectId
    };
    return state6.api.get({
      ...request,
      params
    });
  },
  getSdkProperties() {
    const { sdkType, sdkVersion } = OptionsController.state;
    return {
      st: sdkType || "unknown",
      sv: sdkVersion || "unknown"
    };
  },
  async isNetworkSupported(networkId) {
    if (!networkId) {
      return false;
    }
    try {
      if (!state6.supportedChains.http.length) {
        await BlockchainApiController.getSupportedNetworks();
      }
    } catch (e2) {
      return false;
    }
    return state6.supportedChains.http.includes(networkId);
  },
  async getSupportedNetworks() {
    try {
      const supportedChains = await BlockchainApiController.get({
        path: "v1/supported-chains"
      });
      state6.supportedChains = supportedChains;
      return supportedChains;
    } catch {
      return state6.supportedChains;
    }
  },
  async fetchIdentity({ address, caipNetworkId }) {
    const isSupported = await BlockchainApiController.isNetworkSupported(caipNetworkId);
    if (!isSupported) {
      return { avatar: "", name: "" };
    }
    const identityCache = StorageUtil.getIdentityFromCacheForAddress(address);
    if (identityCache) {
      return identityCache;
    }
    const result = await BlockchainApiController.get({
      path: `/v1/identity/${address}`,
      params: {
        sender: ChainController.state.activeCaipAddress ? CoreHelperUtil.getPlainAddress(ChainController.state.activeCaipAddress) : void 0
      }
    });
    StorageUtil.updateIdentityCache({
      address,
      identity: result,
      timestamp: Date.now()
    });
    return result;
  },
  async fetchTransactions({ account, cursor, onramp, signal, cache, chainId }) {
    var _a;
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      return { data: [], next: void 0 };
    }
    return BlockchainApiController.get({
      path: `/v1/account/${account}/history`,
      params: {
        cursor,
        onramp,
        chainId
      },
      signal,
      cache
    });
  },
  async fetchSwapQuote({ amount, userAddress, from: from6, to: to2, gasPrice }) {
    var _a;
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      return { quotes: [] };
    }
    return BlockchainApiController.get({
      path: `/v1/convert/quotes`,
      headers: {
        "Content-Type": "application/json"
      },
      params: {
        amount,
        userAddress,
        from: from6,
        to: to2,
        gasPrice
      }
    });
  },
  async fetchSwapTokens({ chainId }) {
    var _a;
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      return { tokens: [] };
    }
    return BlockchainApiController.get({
      path: `/v1/convert/tokens`,
      params: { chainId }
    });
  },
  async fetchTokenPrice({ addresses }) {
    var _a;
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      return { fungibles: [] };
    }
    return state6.api.post({
      path: "/v1/fungible/price",
      body: {
        currency: "usd",
        addresses,
        projectId: OptionsController.state.projectId
      },
      headers: {
        "Content-Type": "application/json"
      }
    });
  },
  async fetchSwapAllowance({ tokenAddress, userAddress }) {
    var _a;
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      return { allowance: "0" };
    }
    return BlockchainApiController.get({
      path: `/v1/convert/allowance`,
      params: {
        tokenAddress,
        userAddress
      },
      headers: {
        "Content-Type": "application/json"
      }
    });
  },
  async fetchGasPrice({ chainId }) {
    var _a;
    const { st: st2, sv } = BlockchainApiController.getSdkProperties();
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      throw new Error("Network not supported for Gas Price");
    }
    return BlockchainApiController.get({
      path: `/v1/convert/gas-price`,
      headers: {
        "Content-Type": "application/json"
      },
      params: {
        chainId,
        st: st2,
        sv
      }
    });
  },
  async generateSwapCalldata({ amount, from: from6, to: to2, userAddress, disableEstimate }) {
    var _a;
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      throw new Error("Network not supported for Swaps");
    }
    return state6.api.post({
      path: "/v1/convert/build-transaction",
      headers: {
        "Content-Type": "application/json"
      },
      body: {
        amount,
        eip155: {
          slippage: ConstantsUtil2.CONVERT_SLIPPAGE_TOLERANCE
        },
        projectId: OptionsController.state.projectId,
        from: from6,
        to: to2,
        userAddress,
        disableEstimate
      }
    });
  },
  async generateApproveCalldata({ from: from6, to: to2, userAddress }) {
    var _a;
    const { st: st2, sv } = BlockchainApiController.getSdkProperties();
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      throw new Error("Network not supported for Swaps");
    }
    return BlockchainApiController.get({
      path: `/v1/convert/build-approve`,
      headers: {
        "Content-Type": "application/json"
      },
      params: {
        userAddress,
        from: from6,
        to: to2,
        st: st2,
        sv
      }
    });
  },
  async getBalance(address, chainId, forceUpdate) {
    var _a;
    const { st: st2, sv } = BlockchainApiController.getSdkProperties();
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      SnackController.showError("Token Balance Unavailable");
      return { balances: [] };
    }
    const caipAddress = `${chainId}:${address}`;
    const cachedBalance = StorageUtil.getBalanceCacheForCaipAddress(caipAddress);
    if (cachedBalance) {
      return cachedBalance;
    }
    const balance = await BlockchainApiController.get({
      path: `/v1/account/${address}/balance`,
      params: {
        currency: "usd",
        chainId,
        forceUpdate,
        st: st2,
        sv
      }
    });
    StorageUtil.updateBalanceCache({
      caipAddress,
      balance,
      timestamp: Date.now()
    });
    return balance;
  },
  async lookupEnsName(name) {
    var _a;
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      return { addresses: {}, attributes: [] };
    }
    return BlockchainApiController.get({
      path: `/v1/profile/account/${name}`,
      params: { apiVersion: "2" }
    });
  },
  async reverseLookupEnsName({ address }) {
    var _a;
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      return [];
    }
    return BlockchainApiController.get({
      path: `/v1/profile/reverse/${address}`,
      params: {
        sender: AccountController.state.address,
        apiVersion: "2"
      }
    });
  },
  async getEnsNameSuggestions(name) {
    var _a;
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      return { suggestions: [] };
    }
    return BlockchainApiController.get({
      path: `/v1/profile/suggestions/${name}`,
      params: { zone: "reown.id" }
    });
  },
  async registerEnsName({ coinType, address, message, signature }) {
    var _a;
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      return { success: false };
    }
    return state6.api.post({
      path: `/v1/profile/account`,
      body: { coin_type: coinType, address, message, signature },
      headers: {
        "Content-Type": "application/json"
      }
    });
  },
  async generateOnRampURL({ destinationWallets, partnerUserId, defaultNetwork, purchaseAmount, paymentAmount }) {
    var _a;
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      return "";
    }
    const response = await state6.api.post({
      path: `/v1/generators/onrampurl`,
      params: {
        projectId: OptionsController.state.projectId
      },
      body: {
        destinationWallets,
        defaultNetwork,
        partnerUserId,
        defaultExperience: "buy",
        presetCryptoAmount: purchaseAmount,
        presetFiatAmount: paymentAmount
      }
    });
    return response.url;
  },
  async getOnrampOptions() {
    var _a;
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      return { paymentCurrencies: [], purchaseCurrencies: [] };
    }
    try {
      const response = await BlockchainApiController.get({
        path: `/v1/onramp/options`
      });
      return response;
    } catch (e2) {
      return DEFAULT_OPTIONS;
    }
  },
  async getOnrampQuote({ purchaseCurrency, paymentCurrency, amount, network }) {
    var _a;
    try {
      const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
      if (!isSupported) {
        return null;
      }
      const response = await state6.api.post({
        path: `/v1/onramp/quote`,
        params: {
          projectId: OptionsController.state.projectId
        },
        body: {
          purchaseCurrency,
          paymentCurrency,
          amount,
          network
        }
      });
      return response;
    } catch (e2) {
      return {
        coinbaseFee: { amount, currency: paymentCurrency.id },
        networkFee: { amount, currency: paymentCurrency.id },
        paymentSubtotal: { amount, currency: paymentCurrency.id },
        paymentTotal: { amount, currency: paymentCurrency.id },
        purchaseAmount: { amount, currency: paymentCurrency.id },
        quoteId: "mocked-quote-id"
      };
    }
  },
  async getSmartSessions(caipAddress) {
    var _a;
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      return [];
    }
    return BlockchainApiController.get({
      path: `/v1/sessions/${caipAddress}`
    });
  },
  async revokeSmartSession(address, pci, signature) {
    var _a;
    const isSupported = await BlockchainApiController.isNetworkSupported((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    if (!isSupported) {
      return { success: false };
    }
    return state6.api.post({
      path: `/v1/sessions/${address}/revoke`,
      params: {
        projectId: OptionsController.state.projectId
      },
      body: {
        pci,
        signature
      }
    });
  },
  setClientId(clientId) {
    state6.clientId = clientId;
    state6.api = new FetchUtil({ baseUrl, clientId });
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/AccountController.js
var state7 = proxy({
  currentTab: 0,
  tokenBalance: [],
  smartAccountDeployed: false,
  addressLabels: /* @__PURE__ */ new Map(),
  allAccounts: []
});
var controller3 = {
  state: state7,
  replaceState(newState) {
    if (!newState) {
      return;
    }
    Object.assign(state7, ref(newState));
  },
  subscribe(callback) {
    return ChainController.subscribeChainProp("accountState", (accountState2) => {
      if (accountState2) {
        return callback(accountState2);
      }
      return void 0;
    });
  },
  subscribeKey(property, callback, chain) {
    let prev = void 0;
    return ChainController.subscribeChainProp("accountState", (accountState2) => {
      if (accountState2) {
        const nextValue = accountState2[property];
        if (prev !== nextValue) {
          prev = nextValue;
          callback(nextValue);
        }
      }
    }, chain);
  },
  setStatus(status, chain) {
    ChainController.setAccountProp("status", status, chain);
  },
  getCaipAddress(chain) {
    return ChainController.getAccountProp("caipAddress", chain);
  },
  setCaipAddress(caipAddress, chain) {
    const newAddress = caipAddress ? CoreHelperUtil.getPlainAddress(caipAddress) : void 0;
    if (chain === ChainController.state.activeChain) {
      ChainController.state.activeCaipAddress = caipAddress;
    }
    ChainController.setAccountProp("caipAddress", caipAddress, chain);
    ChainController.setAccountProp("address", newAddress, chain);
  },
  setBalance(balance, balanceSymbol, chain) {
    ChainController.setAccountProp("balance", balance, chain);
    ChainController.setAccountProp("balanceSymbol", balanceSymbol, chain);
  },
  setProfileName(profileName, chain) {
    ChainController.setAccountProp("profileName", profileName, chain);
  },
  setProfileImage(profileImage, chain) {
    ChainController.setAccountProp("profileImage", profileImage, chain);
  },
  setUser(user, chain) {
    ChainController.setAccountProp("user", user, chain);
  },
  setAddressExplorerUrl(explorerUrl, chain) {
    ChainController.setAccountProp("addressExplorerUrl", explorerUrl, chain);
  },
  setSmartAccountDeployed(isDeployed, chain) {
    ChainController.setAccountProp("smartAccountDeployed", isDeployed, chain);
  },
  setCurrentTab(currentTab) {
    ChainController.setAccountProp("currentTab", currentTab, ChainController.state.activeChain);
  },
  setTokenBalance(tokenBalance, chain) {
    if (tokenBalance) {
      ChainController.setAccountProp("tokenBalance", tokenBalance, chain);
    }
  },
  setShouldUpdateToAddress(address, chain) {
    ChainController.setAccountProp("shouldUpdateToAddress", address, chain);
  },
  setAllAccounts(accounts, namespace) {
    ChainController.setAccountProp("allAccounts", accounts, namespace);
  },
  addAddressLabel(address, label, chain) {
    const map = ChainController.getAccountProp("addressLabels", chain) || /* @__PURE__ */ new Map();
    map.set(address, label);
    ChainController.setAccountProp("addressLabels", map, chain);
  },
  removeAddressLabel(address, chain) {
    const map = ChainController.getAccountProp("addressLabels", chain) || /* @__PURE__ */ new Map();
    map.delete(address);
    ChainController.setAccountProp("addressLabels", map, chain);
  },
  setConnectedWalletInfo(connectedWalletInfo, chain) {
    ChainController.setAccountProp("connectedWalletInfo", connectedWalletInfo, chain, false);
  },
  setPreferredAccountType(preferredAccountType, chain) {
    ChainController.setAccountProp("preferredAccountTypes", {
      ...state7.preferredAccountTypes,
      [chain]: preferredAccountType
    }, chain);
  },
  setPreferredAccountTypes(preferredAccountTypes) {
    state7.preferredAccountTypes = preferredAccountTypes;
  },
  setSocialProvider(socialProvider, chain) {
    if (socialProvider) {
      ChainController.setAccountProp("socialProvider", socialProvider, chain);
    }
  },
  setSocialWindow(socialWindow, chain) {
    ChainController.setAccountProp("socialWindow", socialWindow ? ref(socialWindow) : void 0, chain);
  },
  setFarcasterUrl(farcasterUrl, chain) {
    ChainController.setAccountProp("farcasterUrl", farcasterUrl, chain);
  },
  async fetchTokenBalance(onError) {
    var _a, _b;
    state7.balanceLoading = true;
    const chainId = (_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId;
    const chain = (_b = ChainController.state.activeCaipNetwork) == null ? void 0 : _b.chainNamespace;
    const caipAddress = ChainController.state.activeCaipAddress;
    const address = caipAddress ? CoreHelperUtil.getPlainAddress(caipAddress) : void 0;
    if (state7.lastRetry && !CoreHelperUtil.isAllowedRetry(state7.lastRetry, 30 * ConstantsUtil2.ONE_SEC_MS)) {
      state7.balanceLoading = false;
      return [];
    }
    try {
      if (address && chainId && chain) {
        const response = await BlockchainApiController.getBalance(address, chainId);
        const filteredBalances = response.balances.filter((balance) => balance.quantity.decimals !== "0");
        AccountController.setTokenBalance(filteredBalances, chain);
        state7.lastRetry = void 0;
        state7.balanceLoading = false;
        return filteredBalances;
      }
    } catch (error) {
      state7.lastRetry = Date.now();
      onError == null ? void 0 : onError(error);
      SnackController.showError("Token Balance Unavailable");
    } finally {
      state7.balanceLoading = false;
    }
    return [];
  },
  resetAccount(chain) {
    ChainController.resetAccount(chain);
  }
};
var AccountController = withErrorBoundary(controller3);

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/NetworkUtil.js
var NetworkUtil2 = {
  /**
   * Function to handle the network switch.
   * This function has variety of conditions to handle the network switch depending on the connectors or namespace's connection states.
   * @param args.network - The network to switch to.
   * @param args.shouldConfirmSwitch - Whether to confirm the switch. If true, the user will be asked to confirm the switch if necessary.
   * @returns void
   */
  onSwitchNetwork({ network, ignoreSwitchConfirmation = false }) {
    const currentNetwork = ChainController.state.activeCaipNetwork;
    const routerData = RouterController.state.data;
    const isSameNetwork = network.id === (currentNetwork == null ? void 0 : currentNetwork.id);
    if (isSameNetwork) {
      return;
    }
    const isCurrentNamespaceConnected = AccountController.getCaipAddress(ChainController.state.activeChain);
    const isDifferentNamespace = network.chainNamespace !== ChainController.state.activeChain;
    const isNextNamespaceConnected = AccountController.getCaipAddress(network.chainNamespace);
    const connectorId = ConnectorController.getConnectorId(ChainController.state.activeChain);
    const isConnectedWithAuth = connectorId === ConstantsUtil.CONNECTOR_ID.AUTH;
    const isSupportedForAuthConnector = ConstantsUtil.AUTH_CONNECTOR_SUPPORTED_CHAINS.find((c2) => c2 === network.chainNamespace);
    if (ignoreSwitchConfirmation || isConnectedWithAuth && isSupportedForAuthConnector) {
      RouterController.push("SwitchNetwork", { ...routerData, network });
    } else if (
      /**
       * If user switching to a different namespace and next namespace is not connected, we need to show switch active chain view for confirmation first.
       */
      isCurrentNamespaceConnected && isDifferentNamespace && !isNextNamespaceConnected
    ) {
      RouterController.push("SwitchActiveChain", {
        switchToChain: network.chainNamespace,
        navigateTo: "Connect",
        navigateWithReplace: true,
        network
      });
    } else {
      RouterController.push("SwitchNetwork", { ...routerData, network });
    }
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/AlertController.js
var state8 = proxy({
  message: "",
  variant: "info",
  open: false
});
var controller4 = {
  state: state8,
  subscribeKey(key, callback) {
    return subscribeKey(state8, key, callback);
  },
  open(message, variant) {
    const { debug } = OptionsController.state;
    const { shortMessage, longMessage } = message;
    if (debug) {
      state8.message = shortMessage;
      state8.variant = variant;
      state8.open = true;
    }
    if (longMessage) {
      console.error(typeof longMessage === "function" ? longMessage() : longMessage);
    }
  },
  close() {
    state8.open = false;
    state8.message = "";
    state8.variant = "info";
  }
};
var AlertController = withErrorBoundary(controller4);

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/EventsController.js
var baseUrl2 = CoreHelperUtil.getAnalyticsUrl();
var api2 = new FetchUtil({ baseUrl: baseUrl2, clientId: null });
var excluded = ["MODAL_CREATED"];
var state9 = proxy({
  timestamp: Date.now(),
  reportedErrors: {},
  data: {
    type: "track",
    event: "MODAL_CREATED"
  }
});
var EventsController = {
  state: state9,
  subscribe(callback) {
    return subscribe(state9, () => callback(state9));
  },
  getSdkProperties() {
    const { projectId, sdkType, sdkVersion } = OptionsController.state;
    return {
      projectId,
      st: sdkType,
      sv: sdkVersion || "html-wagmi-4.2.2"
    };
  },
  async _sendAnalyticsEvent(payload) {
    try {
      const address = AccountController.state.address;
      if (excluded.includes(payload.data.event) || typeof window === "undefined") {
        return;
      }
      await api2.post({
        path: "/e",
        params: EventsController.getSdkProperties(),
        body: {
          eventId: CoreHelperUtil.getUUID(),
          url: window.location.href,
          domain: window.location.hostname,
          timestamp: payload.timestamp,
          props: { ...payload.data, address }
        }
      });
      state9.reportedErrors["FORBIDDEN"] = false;
    } catch (err) {
      const isForbiddenError = err instanceof Error && err.cause instanceof Response && err.cause.status === ConstantsUtil.HTTP_STATUS_CODES.FORBIDDEN && !state9.reportedErrors["FORBIDDEN"];
      if (isForbiddenError) {
        AlertController.open({
          shortMessage: "Invalid App Configuration",
          longMessage: `Origin ${isSafe() ? window.origin : "uknown"} not found on Allowlist - update configuration on cloud.reown.com`
        }, "error");
        state9.reportedErrors["FORBIDDEN"] = true;
      }
    }
  },
  sendEvent(data) {
    var _a;
    state9.timestamp = Date.now();
    state9.data = data;
    if ((_a = OptionsController.state.features) == null ? void 0 : _a.analytics) {
      EventsController._sendAnalyticsEvent(state9);
    }
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/PublicStateController.js
var state10 = proxy({
  loading: false,
  open: false,
  selectedNetworkId: void 0,
  activeChain: void 0,
  initialized: false
});
var PublicStateController = {
  state: state10,
  subscribe(callback) {
    return subscribe(state10, () => callback(state10));
  },
  subscribeOpen(callback) {
    return subscribeKey(state10, "open", callback);
  },
  set(newState) {
    Object.assign(state10, { ...state10, ...newState });
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/ModalController.js
var state11 = proxy({
  loading: false,
  loadingNamespaceMap: /* @__PURE__ */ new Map(),
  open: false,
  shake: false,
  namespace: void 0
});
var controller5 = {
  state: state11,
  subscribe(callback) {
    return subscribe(state11, () => callback(state11));
  },
  subscribeKey(key, callback) {
    return subscribeKey(state11, key, callback);
  },
  async open(options) {
    var _a, _b;
    const isConnected = AccountController.state.status === "connected";
    const namespace = options == null ? void 0 : options.namespace;
    const currentNamespace = ChainController.state.activeChain;
    const isSwitchingNamespace = namespace && namespace !== currentNamespace;
    const caipAddress = (_a = ChainController.getAccountData(options == null ? void 0 : options.namespace)) == null ? void 0 : _a.caipAddress;
    if (ConnectionController.state.wcBasic) {
      ApiController.prefetch({ fetchNetworkImages: false, fetchConnectorImages: false });
    } else {
      await ApiController.prefetch({
        fetchConnectorImages: !isConnected,
        fetchFeaturedWallets: !isConnected,
        fetchRecommendedWallets: !isConnected
      });
    }
    ConnectorController.setFilterByNamespace(options == null ? void 0 : options.namespace);
    ModalController.setLoading(true, namespace);
    if (namespace && isSwitchingNamespace) {
      const namespaceNetwork = ((_b = ChainController.getNetworkData(namespace)) == null ? void 0 : _b.caipNetwork) || ChainController.getRequestedCaipNetworks(namespace)[0];
      if (namespaceNetwork) {
        NetworkUtil2.onSwitchNetwork({ network: namespaceNetwork, ignoreSwitchConfirmation: true });
      }
    } else {
      const hasNoAdapters = ChainController.state.noAdapters;
      if (OptionsController.state.manualWCControl || hasNoAdapters && !caipAddress) {
        if (CoreHelperUtil.isMobile()) {
          RouterController.reset("AllWallets");
        } else {
          RouterController.reset("ConnectingWalletConnectBasic");
        }
      } else if (options == null ? void 0 : options.view) {
        RouterController.reset(options.view, options.data);
      } else if (caipAddress) {
        RouterController.reset("Account");
      } else {
        RouterController.reset("Connect");
      }
    }
    state11.open = true;
    PublicStateController.set({ open: true });
    EventsController.sendEvent({
      type: "track",
      event: "MODAL_OPEN",
      properties: { connected: Boolean(caipAddress) }
    });
  },
  close() {
    const isEmbeddedEnabled = OptionsController.state.enableEmbedded;
    const isConnected = Boolean(ChainController.state.activeCaipAddress);
    if (state11.open) {
      EventsController.sendEvent({
        type: "track",
        event: "MODAL_CLOSE",
        properties: { connected: isConnected }
      });
    }
    state11.open = false;
    RouterController.reset("Connect");
    ModalController.clearLoading();
    if (isEmbeddedEnabled) {
      if (isConnected) {
        RouterController.replace("Account");
      } else {
        RouterController.push("Connect");
      }
    } else {
      PublicStateController.set({ open: false });
    }
    ConnectionController.resetUri();
  },
  setLoading(loading, namespace) {
    if (namespace) {
      state11.loadingNamespaceMap.set(namespace, loading);
    }
    state11.loading = loading;
    PublicStateController.set({ loading });
  },
  clearLoading() {
    state11.loadingNamespaceMap.clear();
    state11.loading = false;
  },
  shake() {
    if (state11.shake) {
      return;
    }
    state11.shake = true;
    setTimeout(() => {
      state11.shake = false;
    }, 500);
  }
};
var ModalController = withErrorBoundary(controller5);

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/RouterController.js
var state12 = proxy({
  view: "Connect",
  history: ["Connect"],
  transactionStack: []
});
var controller6 = {
  state: state12,
  subscribeKey(key, callback) {
    return subscribeKey(state12, key, callback);
  },
  pushTransactionStack(action) {
    state12.transactionStack.push(action);
  },
  popTransactionStack(status) {
    const action = state12.transactionStack.pop();
    if (!action) {
      return;
    }
    const { onSuccess, onError, onCancel } = action;
    switch (status) {
      case "success":
        onSuccess == null ? void 0 : onSuccess();
        break;
      case "error":
        onError == null ? void 0 : onError();
        RouterController.goBack();
        break;
      case "cancel":
        onCancel == null ? void 0 : onCancel();
        RouterController.goBack();
        break;
      default:
    }
  },
  push(view, data) {
    if (view !== state12.view) {
      state12.view = view;
      state12.history.push(view);
      state12.data = data;
    }
  },
  reset(view, data) {
    state12.view = view;
    state12.history = [view];
    state12.data = data;
  },
  replace(view, data) {
    const lastView = state12.history.at(-1);
    const isSameView = lastView === view;
    if (!isSameView) {
      state12.view = view;
      state12.history[state12.history.length - 1] = view;
      state12.data = data;
    }
  },
  goBack() {
    var _a;
    const isConnected = ChainController.state.activeCaipAddress;
    const isFarcasterView = RouterController.state.view === "ConnectingFarcaster";
    const shouldReload = !isConnected && isFarcasterView;
    if (state12.history.length > 1) {
      state12.history.pop();
      const [last] = state12.history.slice(-1);
      if (last) {
        const isConnectView = last === "Connect";
        if (isConnected && isConnectView) {
          state12.view = "Account";
        } else {
          state12.view = last;
        }
      }
    } else {
      ModalController.close();
    }
    if ((_a = state12.data) == null ? void 0 : _a.wallet) {
      state12.data.wallet = void 0;
    }
    setTimeout(() => {
      var _a2, _b, _c2;
      if (shouldReload) {
        AccountController.setFarcasterUrl(void 0, ChainController.state.activeChain);
        const authConnector = ConnectorController.getAuthConnector();
        (_a2 = authConnector == null ? void 0 : authConnector.provider) == null ? void 0 : _a2.reload();
        const optionsState = snapshot(OptionsController.state);
        (_c2 = (_b = authConnector == null ? void 0 : authConnector.provider) == null ? void 0 : _b.syncDappData) == null ? void 0 : _c2.call(_b, {
          metadata: optionsState.metadata,
          sdkVersion: optionsState.sdkVersion,
          projectId: optionsState.projectId,
          sdkType: optionsState.sdkType
        });
      }
    }, 100);
  },
  goBackToIndex(historyIndex) {
    if (state12.history.length > 1) {
      state12.history = state12.history.slice(0, historyIndex + 1);
      const [last] = state12.history.slice(-1);
      if (last) {
        state12.view = last;
      }
    }
  },
  goBackOrCloseModal() {
    if (RouterController.state.history.length > 1) {
      RouterController.goBack();
    } else {
      ModalController.close();
    }
  }
};
var RouterController = withErrorBoundary(controller6);

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/ThemeController.js
var state13 = proxy({
  themeMode: "dark",
  themeVariables: {},
  w3mThemeVariables: void 0
});
var controller7 = {
  state: state13,
  subscribe(callback) {
    return subscribe(state13, () => callback(state13));
  },
  setThemeMode(themeMode) {
    state13.themeMode = themeMode;
    try {
      const authConnector = ConnectorController.getAuthConnector();
      if (authConnector) {
        const themeVariables = controller7.getSnapshot().themeVariables;
        authConnector.provider.syncTheme({
          themeMode,
          themeVariables,
          w3mThemeVariables: getW3mThemeVariables(themeVariables, themeMode)
        });
      }
    } catch {
      console.info("Unable to sync theme to auth connector");
    }
  },
  setThemeVariables(themeVariables) {
    state13.themeVariables = { ...state13.themeVariables, ...themeVariables };
    try {
      const authConnector = ConnectorController.getAuthConnector();
      if (authConnector) {
        const themeVariablesSnapshot = controller7.getSnapshot().themeVariables;
        authConnector.provider.syncTheme({
          themeVariables: themeVariablesSnapshot,
          w3mThemeVariables: getW3mThemeVariables(state13.themeVariables, state13.themeMode)
        });
      }
    } catch {
      console.info("Unable to sync theme to auth connector");
    }
  },
  getSnapshot() {
    return snapshot(state13);
  }
};
var ThemeController = withErrorBoundary(controller7);

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/ConnectorController.js
var defaultActiveConnectors = {
  eip155: void 0,
  solana: void 0,
  polkadot: void 0,
  bip122: void 0,
  cosmos: void 0
};
var state14 = proxy({
  allConnectors: [],
  connectors: [],
  activeConnector: void 0,
  filterByNamespace: void 0,
  activeConnectorIds: { ...defaultActiveConnectors },
  filterByNamespaceMap: {
    eip155: true,
    solana: true,
    polkadot: true,
    bip122: true,
    cosmos: true
  }
});
var controller8 = {
  state: state14,
  subscribe(callback) {
    return subscribe(state14, () => {
      callback(state14);
    });
  },
  subscribeKey(key, callback) {
    return subscribeKey(state14, key, callback);
  },
  initialize(namespaces) {
    namespaces.forEach((namespace) => {
      const connectorId = StorageUtil.getConnectedConnectorId(namespace);
      if (connectorId) {
        ConnectorController.setConnectorId(connectorId, namespace);
      }
    });
  },
  setActiveConnector(connector) {
    if (connector) {
      state14.activeConnector = ref(connector);
    }
  },
  setConnectors(connectors) {
    const newConnectors = connectors.filter((newConnector) => !state14.allConnectors.some((existingConnector) => existingConnector.id === newConnector.id && ConnectorController.getConnectorName(existingConnector.name) === ConnectorController.getConnectorName(newConnector.name) && existingConnector.chain === newConnector.chain));
    newConnectors.forEach((connector) => {
      if (connector.type !== "MULTI_CHAIN") {
        state14.allConnectors.push(ref(connector));
      }
    });
    const enabledNamespaces = ConnectorController.getEnabledNamespaces();
    const connectorsFilteredByNamespaces = ConnectorController.getEnabledConnectors(enabledNamespaces);
    state14.connectors = ConnectorController.mergeMultiChainConnectors(connectorsFilteredByNamespaces);
  },
  filterByNamespaces(enabledNamespaces) {
    Object.keys(state14.filterByNamespaceMap).forEach((namespace) => {
      state14.filterByNamespaceMap[namespace] = false;
    });
    enabledNamespaces.forEach((namespace) => {
      state14.filterByNamespaceMap[namespace] = true;
    });
    ConnectorController.updateConnectorsForEnabledNamespaces();
  },
  filterByNamespace(namespace, enabled) {
    state14.filterByNamespaceMap[namespace] = enabled;
    ConnectorController.updateConnectorsForEnabledNamespaces();
  },
  updateConnectorsForEnabledNamespaces() {
    const enabledNamespaces = ConnectorController.getEnabledNamespaces();
    const enabledConnectors = ConnectorController.getEnabledConnectors(enabledNamespaces);
    const areAllNamespacesEnabled = ConnectorController.areAllNamespacesEnabled();
    state14.connectors = ConnectorController.mergeMultiChainConnectors(enabledConnectors);
    if (areAllNamespacesEnabled) {
      ApiController.clearFilterByNamespaces();
    } else {
      ApiController.filterByNamespaces(enabledNamespaces);
    }
  },
  getEnabledNamespaces() {
    return Object.entries(state14.filterByNamespaceMap).filter(([_, enabled]) => enabled).map(([namespace]) => namespace);
  },
  getEnabledConnectors(enabledNamespaces) {
    return state14.allConnectors.filter((connector) => enabledNamespaces.includes(connector.chain));
  },
  areAllNamespacesEnabled() {
    return Object.values(state14.filterByNamespaceMap).every((enabled) => enabled);
  },
  mergeMultiChainConnectors(connectors) {
    const connectorsByNameMap = ConnectorController.generateConnectorMapByName(connectors);
    const mergedConnectors = [];
    connectorsByNameMap.forEach((keyConnectors) => {
      const firstItem = keyConnectors[0];
      const isAuthConnector = (firstItem == null ? void 0 : firstItem.id) === ConstantsUtil.CONNECTOR_ID.AUTH;
      if (keyConnectors.length > 1 && firstItem) {
        mergedConnectors.push({
          name: firstItem.name,
          imageUrl: firstItem.imageUrl,
          imageId: firstItem.imageId,
          connectors: [...keyConnectors],
          type: isAuthConnector ? "AUTH" : "MULTI_CHAIN",
          // These values are just placeholders, we don't use them in multi-chain connector select screen
          chain: "eip155",
          id: (firstItem == null ? void 0 : firstItem.id) || ""
        });
      } else if (firstItem) {
        mergedConnectors.push(firstItem);
      }
    });
    return mergedConnectors;
  },
  generateConnectorMapByName(connectors) {
    const connectorsByNameMap = /* @__PURE__ */ new Map();
    connectors.forEach((connector) => {
      const { name } = connector;
      const connectorName = ConnectorController.getConnectorName(name);
      if (!connectorName) {
        return;
      }
      const connectorsByName = connectorsByNameMap.get(connectorName) || [];
      const haveSameConnector = connectorsByName.find((c2) => c2.chain === connector.chain);
      if (!haveSameConnector) {
        connectorsByName.push(connector);
      }
      connectorsByNameMap.set(connectorName, connectorsByName);
    });
    return connectorsByNameMap;
  },
  getConnectorName(name) {
    if (!name) {
      return name;
    }
    const nameOverrideMap = {
      "Trust Wallet": "Trust"
    };
    return nameOverrideMap[name] || name;
  },
  getUniqueConnectorsByName(connectors) {
    const uniqueConnectors = [];
    connectors.forEach((c2) => {
      if (!uniqueConnectors.find((uc2) => uc2.chain === c2.chain)) {
        uniqueConnectors.push(c2);
      }
    });
    return uniqueConnectors;
  },
  addConnector(connector) {
    var _a, _b, _c2;
    if (connector.id === ConstantsUtil.CONNECTOR_ID.AUTH) {
      const authConnector = connector;
      const optionsState = snapshot(OptionsController.state);
      const themeMode = ThemeController.getSnapshot().themeMode;
      const themeVariables = ThemeController.getSnapshot().themeVariables;
      (_b = (_a = authConnector == null ? void 0 : authConnector.provider) == null ? void 0 : _a.syncDappData) == null ? void 0 : _b.call(_a, {
        metadata: optionsState.metadata,
        sdkVersion: optionsState.sdkVersion,
        projectId: optionsState.projectId,
        sdkType: optionsState.sdkType
      });
      (_c2 = authConnector == null ? void 0 : authConnector.provider) == null ? void 0 : _c2.syncTheme({
        themeMode,
        themeVariables,
        w3mThemeVariables: getW3mThemeVariables(themeVariables, themeMode)
      });
      ConnectorController.setConnectors([connector]);
    } else {
      ConnectorController.setConnectors([connector]);
    }
  },
  getAuthConnector(chainNamespace) {
    var _a;
    const activeNamespace = chainNamespace || ChainController.state.activeChain;
    const authConnector = state14.connectors.find((c2) => c2.id === ConstantsUtil.CONNECTOR_ID.AUTH);
    if (!authConnector) {
      return void 0;
    }
    if ((_a = authConnector == null ? void 0 : authConnector.connectors) == null ? void 0 : _a.length) {
      const connector = authConnector.connectors.find((c2) => c2.chain === activeNamespace);
      return connector;
    }
    return authConnector;
  },
  getAnnouncedConnectorRdns() {
    return state14.connectors.filter((c2) => c2.type === "ANNOUNCED").map((c2) => {
      var _a;
      return (_a = c2.info) == null ? void 0 : _a.rdns;
    });
  },
  getConnectorById(id) {
    return state14.allConnectors.find((c2) => c2.id === id);
  },
  getConnector(id, rdns) {
    const connectorsByNamespace = state14.allConnectors.filter((c2) => c2.chain === ChainController.state.activeChain);
    return connectorsByNamespace.find((c2) => {
      var _a;
      return c2.explorerId === id || ((_a = c2.info) == null ? void 0 : _a.rdns) === rdns;
    });
  },
  syncIfAuthConnector(connector) {
    var _a, _b;
    if (connector.id !== "ID_AUTH") {
      return;
    }
    const authConnector = connector;
    const optionsState = snapshot(OptionsController.state);
    const themeMode = ThemeController.getSnapshot().themeMode;
    const themeVariables = ThemeController.getSnapshot().themeVariables;
    (_b = (_a = authConnector == null ? void 0 : authConnector.provider) == null ? void 0 : _a.syncDappData) == null ? void 0 : _b.call(_a, {
      metadata: optionsState.metadata,
      sdkVersion: optionsState.sdkVersion,
      sdkType: optionsState.sdkType,
      projectId: optionsState.projectId
    });
    authConnector.provider.syncTheme({
      themeMode,
      themeVariables,
      w3mThemeVariables: getW3mThemeVariables(themeVariables, themeMode)
    });
  },
  /**
   * Returns the connectors filtered by namespace.
   * @param namespace - The namespace to filter the connectors by.
   * @returns ConnectorWithProviders[].
   */
  getConnectorsByNamespace(namespace) {
    const namespaceConnectors = state14.allConnectors.filter((connector) => connector.chain === namespace);
    return ConnectorController.mergeMultiChainConnectors(namespaceConnectors);
  },
  selectWalletConnector(wallet) {
    const connector = ConnectorController.getConnector(wallet.id, wallet.rdns);
    const namespace = ChainController.state.activeChain;
    MobileWalletUtil.handleMobileDeeplinkRedirect((connector == null ? void 0 : connector.explorerId) || wallet.id, namespace);
    if (connector) {
      RouterController.push("ConnectingExternal", { connector });
    } else {
      RouterController.push("ConnectingWalletConnect", { wallet });
    }
  },
  /**
   * Returns the connectors. If a namespace is provided, the connectors are filtered by namespace.
   * @param namespace - The namespace to filter the connectors by. If not provided, all connectors are returned.
   * @returns ConnectorWithProviders[].
   */
  getConnectors(namespace) {
    if (namespace) {
      return ConnectorController.getConnectorsByNamespace(namespace);
    }
    return ConnectorController.mergeMultiChainConnectors(state14.allConnectors);
  },
  /**
   * Sets the filter by namespace and updates the connectors.
   * @param namespace - The namespace to filter the connectors by.
   */
  setFilterByNamespace(namespace) {
    state14.filterByNamespace = namespace;
    state14.connectors = ConnectorController.getConnectors(namespace);
    ApiController.setFilterByNamespace(namespace);
  },
  setConnectorId(connectorId, namespace) {
    if (connectorId) {
      state14.activeConnectorIds = {
        ...state14.activeConnectorIds,
        [namespace]: connectorId
      };
      StorageUtil.setConnectedConnectorId(namespace, connectorId);
    }
  },
  removeConnectorId(namespace) {
    state14.activeConnectorIds = {
      ...state14.activeConnectorIds,
      [namespace]: void 0
    };
    StorageUtil.deleteConnectedConnectorId(namespace);
  },
  getConnectorId(namespace) {
    if (!namespace) {
      return void 0;
    }
    return state14.activeConnectorIds[namespace];
  },
  isConnected(namespace) {
    if (!namespace) {
      return Object.values(state14.activeConnectorIds).some((id) => Boolean(id));
    }
    return Boolean(state14.activeConnectorIds[namespace]);
  },
  resetConnectorIds() {
    state14.activeConnectorIds = { ...defaultActiveConnectors };
  }
};
var ConnectorController = withErrorBoundary(controller8);

// node_modules/@reown/appkit-wallet/dist/esm/src/W3mFrameConstants.js
var DEFAULT_SDK_URL = "https://secure.walletconnect.org/sdk";
var SECURE_SITE_SDK = (typeof process !== "undefined" && typeof process.env !== "undefined" ? process.env["NEXT_PUBLIC_SECURE_SITE_SDK_URL"] : void 0) || DEFAULT_SDK_URL;
var DEFAULT_LOG_LEVEL = (typeof process !== "undefined" && typeof process.env !== "undefined" ? process.env["NEXT_PUBLIC_DEFAULT_LOG_LEVEL"] : void 0) || "error";
var SECURE_SITE_SDK_VERSION = (typeof process !== "undefined" && typeof process.env !== "undefined" ? process.env["NEXT_PUBLIC_SECURE_SITE_SDK_VERSION"] : void 0) || "4";
var W3mFrameRpcConstants = {
  SAFE_RPC_METHODS: [
    "eth_accounts",
    "eth_blockNumber",
    "eth_call",
    "eth_chainId",
    "eth_estimateGas",
    "eth_feeHistory",
    "eth_gasPrice",
    "eth_getAccount",
    "eth_getBalance",
    "eth_getBlockByHash",
    "eth_getBlockByNumber",
    "eth_getBlockReceipts",
    "eth_getBlockTransactionCountByHash",
    "eth_getBlockTransactionCountByNumber",
    "eth_getCode",
    "eth_getFilterChanges",
    "eth_getFilterLogs",
    "eth_getLogs",
    "eth_getProof",
    "eth_getStorageAt",
    "eth_getTransactionByBlockHashAndIndex",
    "eth_getTransactionByBlockNumberAndIndex",
    "eth_getTransactionByHash",
    "eth_getTransactionCount",
    "eth_getTransactionReceipt",
    "eth_getUncleCountByBlockHash",
    "eth_getUncleCountByBlockNumber",
    "eth_maxPriorityFeePerGas",
    "eth_newBlockFilter",
    "eth_newFilter",
    "eth_newPendingTransactionFilter",
    "eth_sendRawTransaction",
    "eth_syncing",
    "eth_uninstallFilter",
    "wallet_getCapabilities",
    "wallet_getCallsStatus",
    "eth_getUserOperationReceipt",
    "eth_estimateUserOperationGas",
    "eth_getUserOperationByHash",
    "eth_supportedEntryPoints",
    "wallet_getAssets"
  ],
  NOT_SAFE_RPC_METHODS: [
    "personal_sign",
    "eth_signTypedData_v4",
    "eth_sendTransaction",
    "solana_signMessage",
    "solana_signTransaction",
    "solana_signAllTransactions",
    "solana_signAndSendTransaction",
    "wallet_sendCalls",
    "wallet_grantPermissions",
    "wallet_revokePermissions",
    "eth_sendUserOperation"
  ],
  GET_CHAIN_ID: "eth_chainId",
  RPC_METHOD_NOT_ALLOWED_MESSAGE: "Requested RPC call is not allowed",
  RPC_METHOD_NOT_ALLOWED_UI_MESSAGE: "Action not allowed",
  ACCOUNT_TYPES: {
    EOA: "eoa",
    SMART_ACCOUNT: "smartAccount"
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/TransactionsController.js
var state15 = proxy({
  transactions: [],
  coinbaseTransactions: {},
  transactionsByYear: {},
  lastNetworkInView: void 0,
  loading: false,
  empty: false,
  next: void 0
});
var controller9 = {
  state: state15,
  subscribe(callback) {
    return subscribe(state15, () => callback(state15));
  },
  setLastNetworkInView(lastNetworkInView) {
    state15.lastNetworkInView = lastNetworkInView;
  },
  async fetchTransactions(accountAddress, onramp) {
    var _a, _b;
    if (!accountAddress) {
      throw new Error("Transactions can't be fetched without an accountAddress");
    }
    state15.loading = true;
    try {
      const response = await BlockchainApiController.fetchTransactions({
        account: accountAddress,
        cursor: state15.next,
        onramp,
        // Coinbase transaction history state updates require the latest data
        cache: onramp === "coinbase" ? "no-cache" : void 0,
        chainId: (_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId
      });
      const nonSpamTransactions = TransactionsController.filterSpamTransactions(response.data);
      const sameChainTransactions = TransactionsController.filterByConnectedChain(nonSpamTransactions);
      const filteredTransactions = [...state15.transactions, ...sameChainTransactions];
      state15.loading = false;
      if (onramp === "coinbase") {
        state15.coinbaseTransactions = TransactionsController.groupTransactionsByYearAndMonth(state15.coinbaseTransactions, response.data);
      } else {
        state15.transactions = filteredTransactions;
        state15.transactionsByYear = TransactionsController.groupTransactionsByYearAndMonth(state15.transactionsByYear, sameChainTransactions);
      }
      state15.empty = filteredTransactions.length === 0;
      state15.next = response.next ? response.next : void 0;
    } catch (error) {
      const activeChainNamespace = ChainController.state.activeChain;
      EventsController.sendEvent({
        type: "track",
        event: "ERROR_FETCH_TRANSACTIONS",
        properties: {
          address: accountAddress,
          projectId: OptionsController.state.projectId,
          cursor: state15.next,
          isSmartAccount: ((_b = AccountController.state.preferredAccountTypes) == null ? void 0 : _b[activeChainNamespace]) === W3mFrameRpcConstants.ACCOUNT_TYPES.SMART_ACCOUNT
        }
      });
      SnackController.showError("Failed to fetch transactions");
      state15.loading = false;
      state15.empty = true;
      state15.next = void 0;
    }
  },
  groupTransactionsByYearAndMonth(transactionsMap = {}, transactions = []) {
    const grouped = transactionsMap;
    transactions.forEach((transaction) => {
      const year = new Date(transaction.metadata.minedAt).getFullYear();
      const month = new Date(transaction.metadata.minedAt).getMonth();
      const yearTransactions = grouped[year] ?? {};
      const monthTransactions = yearTransactions[month] ?? [];
      const newMonthTransactions = monthTransactions.filter((tx) => tx.id !== transaction.id);
      grouped[year] = {
        ...yearTransactions,
        [month]: [...newMonthTransactions, transaction].sort((a, b) => new Date(b.metadata.minedAt).getTime() - new Date(a.metadata.minedAt).getTime())
      };
    });
    return grouped;
  },
  filterSpamTransactions(transactions) {
    return transactions.filter((transaction) => {
      const isAllSpam = transaction.transfers.every((transfer) => {
        var _a;
        return ((_a = transfer.nft_info) == null ? void 0 : _a.flags.is_spam) === true;
      });
      return !isAllSpam;
    });
  },
  filterByConnectedChain(transactions) {
    var _a;
    const chainId = (_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId;
    const filteredTransactions = transactions.filter((transaction) => transaction.metadata.chain === chainId);
    return filteredTransactions;
  },
  clearCursor() {
    state15.next = void 0;
  },
  resetTransactions() {
    state15.transactions = [];
    state15.transactionsByYear = {};
    state15.lastNetworkInView = void 0;
    state15.loading = false;
    state15.empty = false;
    state15.next = void 0;
  }
};
var TransactionsController = withErrorBoundary(controller9, "API_ERROR");

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/ConnectionController.js
var state16 = proxy({
  connections: /* @__PURE__ */ new Map(),
  wcError: false,
  buffering: false,
  status: "disconnected"
});
var wcConnectionPromise;
var controller10 = {
  state: state16,
  subscribeKey(key, callback) {
    return subscribeKey(state16, key, callback);
  },
  _getClient() {
    return state16._client;
  },
  setClient(client) {
    state16._client = ref(client);
  },
  async connectWalletConnect() {
    var _a, _b, _c2, _d;
    if (CoreHelperUtil.isTelegram() || CoreHelperUtil.isSafari() && CoreHelperUtil.isIos()) {
      if (wcConnectionPromise) {
        await wcConnectionPromise;
        wcConnectionPromise = void 0;
        return;
      }
      if (!CoreHelperUtil.isPairingExpired(state16 == null ? void 0 : state16.wcPairingExpiry)) {
        const link = state16.wcUri;
        state16.wcUri = link;
        return;
      }
      wcConnectionPromise = (_b = (_a = ConnectionController._getClient()) == null ? void 0 : _a.connectWalletConnect) == null ? void 0 : _b.call(_a).catch(() => void 0);
      ConnectionController.state.status = "connecting";
      await wcConnectionPromise;
      wcConnectionPromise = void 0;
      state16.wcPairingExpiry = void 0;
      ConnectionController.state.status = "connected";
    } else {
      await ((_d = (_c2 = ConnectionController._getClient()) == null ? void 0 : _c2.connectWalletConnect) == null ? void 0 : _d.call(_c2));
    }
  },
  async connectExternal(options, chain, setChain = true) {
    var _a, _b;
    await ((_b = (_a = ConnectionController._getClient()) == null ? void 0 : _a.connectExternal) == null ? void 0 : _b.call(_a, options));
    if (setChain) {
      ChainController.setActiveNamespace(chain);
    }
  },
  async reconnectExternal(options) {
    var _a, _b;
    await ((_b = (_a = ConnectionController._getClient()) == null ? void 0 : _a.reconnectExternal) == null ? void 0 : _b.call(_a, options));
    const namespace = options.chain || ChainController.state.activeChain;
    if (namespace) {
      ConnectorController.setConnectorId(options.id, namespace);
    }
  },
  async setPreferredAccountType(accountType, namespace) {
    var _a;
    ModalController.setLoading(true, ChainController.state.activeChain);
    const authConnector = ConnectorController.getAuthConnector();
    if (!authConnector) {
      return;
    }
    AccountController.setPreferredAccountType(accountType, namespace);
    await authConnector.provider.setPreferredAccount(accountType);
    StorageUtil.setPreferredAccountTypes(AccountController.state.preferredAccountTypes ?? { [namespace]: accountType });
    await ConnectionController.reconnectExternal(authConnector);
    ModalController.setLoading(false, ChainController.state.activeChain);
    EventsController.sendEvent({
      type: "track",
      event: "SET_PREFERRED_ACCOUNT_TYPE",
      properties: {
        accountType,
        network: ((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId) || ""
      }
    });
  },
  async signMessage(message) {
    var _a;
    return (_a = ConnectionController._getClient()) == null ? void 0 : _a.signMessage(message);
  },
  parseUnits(value, decimals) {
    var _a;
    return (_a = ConnectionController._getClient()) == null ? void 0 : _a.parseUnits(value, decimals);
  },
  formatUnits(value, decimals) {
    var _a;
    return (_a = ConnectionController._getClient()) == null ? void 0 : _a.formatUnits(value, decimals);
  },
  async sendTransaction(args) {
    var _a;
    return (_a = ConnectionController._getClient()) == null ? void 0 : _a.sendTransaction(args);
  },
  async getCapabilities(params) {
    var _a;
    return (_a = ConnectionController._getClient()) == null ? void 0 : _a.getCapabilities(params);
  },
  async grantPermissions(params) {
    var _a;
    return (_a = ConnectionController._getClient()) == null ? void 0 : _a.grantPermissions(params);
  },
  async walletGetAssets(params) {
    var _a;
    return ((_a = ConnectionController._getClient()) == null ? void 0 : _a.walletGetAssets(params)) ?? {};
  },
  async estimateGas(args) {
    var _a;
    return (_a = ConnectionController._getClient()) == null ? void 0 : _a.estimateGas(args);
  },
  async writeContract(args) {
    var _a;
    return (_a = ConnectionController._getClient()) == null ? void 0 : _a.writeContract(args);
  },
  async getEnsAddress(value) {
    var _a;
    return (_a = ConnectionController._getClient()) == null ? void 0 : _a.getEnsAddress(value);
  },
  async getEnsAvatar(value) {
    var _a;
    return (_a = ConnectionController._getClient()) == null ? void 0 : _a.getEnsAvatar(value);
  },
  checkInstalled(ids) {
    var _a, _b;
    return ((_b = (_a = ConnectionController._getClient()) == null ? void 0 : _a.checkInstalled) == null ? void 0 : _b.call(_a, ids)) || false;
  },
  resetWcConnection() {
    state16.wcUri = void 0;
    state16.wcPairingExpiry = void 0;
    state16.wcLinking = void 0;
    state16.recentWallet = void 0;
    state16.status = "disconnected";
    TransactionsController.resetTransactions();
    StorageUtil.deleteWalletConnectDeepLink();
  },
  resetUri() {
    state16.wcUri = void 0;
    state16.wcPairingExpiry = void 0;
    wcConnectionPromise = void 0;
  },
  finalizeWcConnection() {
    var _a, _b;
    const { wcLinking, recentWallet } = ConnectionController.state;
    if (wcLinking) {
      StorageUtil.setWalletConnectDeepLink(wcLinking);
    }
    if (recentWallet) {
      StorageUtil.setAppKitRecent(recentWallet);
    }
    EventsController.sendEvent({
      type: "track",
      event: "CONNECT_SUCCESS",
      properties: {
        method: wcLinking ? "mobile" : "qrcode",
        name: ((_b = (_a = RouterController.state.data) == null ? void 0 : _a.wallet) == null ? void 0 : _b.name) || "Unknown"
      }
    });
  },
  setWcBasic(wcBasic) {
    state16.wcBasic = wcBasic;
  },
  setUri(uri) {
    state16.wcUri = uri;
    state16.wcPairingExpiry = CoreHelperUtil.getPairingExpiry();
  },
  setWcLinking(wcLinking) {
    state16.wcLinking = wcLinking;
  },
  setWcError(wcError) {
    state16.wcError = wcError;
    state16.buffering = false;
  },
  setRecentWallet(wallet) {
    state16.recentWallet = wallet;
  },
  setBuffering(buffering) {
    state16.buffering = buffering;
  },
  setStatus(status) {
    state16.status = status;
  },
  async disconnect(namespace) {
    var _a;
    try {
      await ((_a = ConnectionController._getClient()) == null ? void 0 : _a.disconnect(namespace));
    } catch (error) {
      throw new AppKitError("Failed to disconnect", "INTERNAL_SDK_ERROR", error);
    }
  },
  setConnections(connections, chainNamespace) {
    state16.connections.set(chainNamespace, connections);
  },
  switchAccount({ connection, address, namespace }) {
    const connectedConnectorId = ConnectorController.state.activeConnectorIds[namespace];
    const isConnectorConnected = connectedConnectorId === connection.connectorId;
    if (isConnectorConnected) {
      const currentNetwork = ChainController.state.activeCaipNetwork;
      if (currentNetwork) {
        const caipAddress = `${namespace}:${currentNetwork.id}:${address}`;
        AccountController.setCaipAddress(caipAddress, namespace);
      } else {
        console.warn(`No current network found for namespace "${namespace}"`);
      }
    } else {
      const connector = ConnectorController.getConnector(connection.connectorId);
      if (connector) {
        ConnectionController.connectExternal(connector, namespace);
      } else {
        console.warn(`No connector found for namespace "${namespace}"`);
      }
    }
  }
};
var ConnectionController = withErrorBoundary(controller10);

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/ERC7811Util.js
var ERC7811Utils = {
  /**
   * Creates a Balance object from an ERC7811 Asset object
   * @param asset - Asset object to convert
   * @param chainId - Chain ID in CAIP-2 format
   * @returns Balance object
   */
  createBalance(asset, chainId) {
    const metadata = {
      name: asset.metadata["name"] || "",
      symbol: asset.metadata["symbol"] || "",
      decimals: asset.metadata["decimals"] || 0,
      value: asset.metadata["value"] || 0,
      price: asset.metadata["price"] || 0,
      iconUrl: asset.metadata["iconUrl"] || ""
    };
    return {
      name: metadata.name,
      symbol: metadata.symbol,
      chainId,
      address: asset.address === "native" ? void 0 : this.convertAddressToCAIP10Address(asset.address, chainId),
      value: metadata.value,
      price: metadata.price,
      quantity: {
        decimals: metadata.decimals.toString(),
        numeric: this.convertHexToBalance({
          hex: asset.balance,
          decimals: metadata.decimals
        })
      },
      iconUrl: metadata.iconUrl
    };
  },
  /**
   * Converts a hex string to a Balance object
   * @param hex - Hex string to convert
   * @param decimals - Number of decimals to use
   * @returns Balance object
   */
  convertHexToBalance({ hex, decimals }) {
    return formatUnits(BigInt(hex), decimals);
  },
  /**
   * Converts an address to a CAIP-10 address
   * @param address - Address to convert
   * @param chainId - Chain ID in CAIP-2 format
   * @returns CAIP-10 address
   */
  convertAddressToCAIP10Address(address, chainId) {
    return `${chainId}:${address}`;
  },
  /**
   *  Creates a CAIP-2 Chain ID from a chain ID and namespace
   * @param chainId  - Chain ID in hex format
   * @param namespace  - Chain namespace
   * @returns
   */
  createCAIP2ChainId(chainId, namespace) {
    return `${namespace}:${parseInt(chainId, 16)}`;
  },
  /**
   * Gets the chain ID in hex format from a CAIP-2 Chain ID
   * @param caip2ChainId - CAIP-2 Chain ID
   * @returns Chain ID in hex format
   */
  getChainIdHexFromCAIP2ChainId(caip2ChainId) {
    const parts = caip2ChainId.split(":");
    if (parts.length < 2 || !parts[1]) {
      return "0x0";
    }
    const chainPart = parts[1];
    const parsed = parseInt(chainPart, 10);
    return isNaN(parsed) ? "0x0" : `0x${parsed.toString(16)}`;
  },
  /**
   * Checks if a response is a valid WalletGetAssetsResponse
   * @param response - The response to check
   * @returns True if the response is a valid WalletGetAssetsResponse, false otherwise
   */
  isWalletGetAssetsResponse(response) {
    if (typeof response !== "object" || response === null) {
      return false;
    }
    return Object.values(response).every((value) => Array.isArray(value) && value.every((asset) => this.isValidAsset(asset)));
  },
  /**
   * Checks if an asset object is valid.
   * @param asset - The asset object to check.
   * @returns True if the asset is valid, false otherwise.
   */
  isValidAsset(asset) {
    return typeof asset === "object" && asset !== null && typeof asset.address === "string" && typeof asset.balance === "string" && (asset.type === "ERC20" || asset.type === "NATIVE") && typeof asset.metadata === "object" && asset.metadata !== null && typeof asset.metadata["name"] === "string" && typeof asset.metadata["symbol"] === "string" && typeof asset.metadata["decimals"] === "number" && typeof asset.metadata["price"] === "number" && typeof asset.metadata["iconUrl"] === "string";
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/SendApiUtil.js
var SendApiUtil = {
  async getMyTokensWithBalance(forceUpdate) {
    const address = AccountController.state.address;
    const caipNetwork = ChainController.state.activeCaipNetwork;
    if (!address || !caipNetwork) {
      return [];
    }
    if (caipNetwork.chainNamespace === "eip155") {
      const eip155Balances = await this.getEIP155Balances(address, caipNetwork);
      if (eip155Balances) {
        return this.filterLowQualityTokens(eip155Balances);
      }
    }
    const response = await BlockchainApiController.getBalance(address, caipNetwork.caipNetworkId, forceUpdate);
    return this.filterLowQualityTokens(response.balances);
  },
  async getEIP155Balances(address, caipNetwork) {
    var _a, _b;
    try {
      const chainIdHex = ERC7811Utils.getChainIdHexFromCAIP2ChainId(caipNetwork.caipNetworkId);
      const walletCapabilities = await ConnectionController.getCapabilities(address);
      if (!((_b = (_a = walletCapabilities == null ? void 0 : walletCapabilities[chainIdHex]) == null ? void 0 : _a["assetDiscovery"]) == null ? void 0 : _b.supported)) {
        return null;
      }
      const walletGetAssetsResponse = await ConnectionController.walletGetAssets({
        account: address,
        chainFilter: [chainIdHex]
      });
      if (!ERC7811Utils.isWalletGetAssetsResponse(walletGetAssetsResponse)) {
        return null;
      }
      const assets = walletGetAssetsResponse[chainIdHex] || [];
      return assets.map((asset) => ERC7811Utils.createBalance(asset, caipNetwork.caipNetworkId));
    } catch (error) {
      return null;
    }
  },
  /**
   * The 1Inch API includes many low-quality tokens in the balance response,
   * which appear inconsistently. This filter prevents them from being displayed.
   */
  filterLowQualityTokens(balances) {
    return balances.filter((balance) => balance.quantity.decimals !== "0");
  },
  mapBalancesToSwapTokens(balances) {
    return (balances == null ? void 0 : balances.map((token) => ({
      ...token,
      address: (token == null ? void 0 : token.address) ? token.address : ChainController.getActiveNetworkTokenAddress(),
      decimals: parseInt(token.quantity.decimals, 10),
      logoUri: token.iconUrl,
      eip2612: false
    }))) || [];
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/SendController.js
var state17 = proxy({
  tokenBalances: [],
  loading: false
});
var controller11 = {
  state: state17,
  subscribe(callback) {
    return subscribe(state17, () => callback(state17));
  },
  subscribeKey(key, callback) {
    return subscribeKey(state17, key, callback);
  },
  setToken(token) {
    if (token) {
      state17.token = ref(token);
    }
  },
  setTokenAmount(sendTokenAmount) {
    state17.sendTokenAmount = sendTokenAmount;
  },
  setReceiverAddress(receiverAddress) {
    state17.receiverAddress = receiverAddress;
  },
  setReceiverProfileImageUrl(receiverProfileImageUrl) {
    state17.receiverProfileImageUrl = receiverProfileImageUrl;
  },
  setReceiverProfileName(receiverProfileName) {
    state17.receiverProfileName = receiverProfileName;
  },
  setNetworkBalanceInUsd(networkBalanceInUSD) {
    state17.networkBalanceInUSD = networkBalanceInUSD;
  },
  setLoading(loading) {
    state17.loading = loading;
  },
  async sendToken() {
    var _a;
    try {
      SendController.setLoading(true);
      switch ((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.chainNamespace) {
        case "eip155":
          await SendController.sendEvmToken();
          return;
        case "solana":
          await SendController.sendSolanaToken();
          return;
        default:
          throw new Error("Unsupported chain");
      }
    } finally {
      SendController.setLoading(false);
    }
  },
  async sendEvmToken() {
    var _a, _b, _c2, _d;
    const activeChainNamespace = ChainController.state.activeChain;
    const activeAccountType = (_a = AccountController.state.preferredAccountTypes) == null ? void 0 : _a[activeChainNamespace];
    if (!SendController.state.sendTokenAmount || !SendController.state.receiverAddress) {
      throw new Error("An amount and receiver address are required");
    }
    if (!SendController.state.token) {
      throw new Error("A token is required");
    }
    if ((_b = SendController.state.token) == null ? void 0 : _b.address) {
      EventsController.sendEvent({
        type: "track",
        event: "SEND_INITIATED",
        properties: {
          isSmartAccount: activeAccountType === W3mFrameRpcConstants.ACCOUNT_TYPES.SMART_ACCOUNT,
          token: SendController.state.token.address,
          amount: SendController.state.sendTokenAmount,
          network: ((_c2 = ChainController.state.activeCaipNetwork) == null ? void 0 : _c2.caipNetworkId) || ""
        }
      });
      await SendController.sendERC20Token({
        receiverAddress: SendController.state.receiverAddress,
        tokenAddress: SendController.state.token.address,
        sendTokenAmount: SendController.state.sendTokenAmount,
        decimals: SendController.state.token.quantity.decimals
      });
    } else {
      EventsController.sendEvent({
        type: "track",
        event: "SEND_INITIATED",
        properties: {
          isSmartAccount: activeAccountType === W3mFrameRpcConstants.ACCOUNT_TYPES.SMART_ACCOUNT,
          token: SendController.state.token.symbol || "",
          amount: SendController.state.sendTokenAmount,
          network: ((_d = ChainController.state.activeCaipNetwork) == null ? void 0 : _d.caipNetworkId) || ""
        }
      });
      await SendController.sendNativeToken({
        receiverAddress: SendController.state.receiverAddress,
        sendTokenAmount: SendController.state.sendTokenAmount,
        decimals: SendController.state.token.quantity.decimals
      });
    }
  },
  async fetchTokenBalance(onError) {
    var _a, _b;
    state17.loading = true;
    const chainId = (_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId;
    const chain = (_b = ChainController.state.activeCaipNetwork) == null ? void 0 : _b.chainNamespace;
    const caipAddress = ChainController.state.activeCaipAddress;
    const address = caipAddress ? CoreHelperUtil.getPlainAddress(caipAddress) : void 0;
    if (state17.lastRetry && !CoreHelperUtil.isAllowedRetry(state17.lastRetry, 30 * ConstantsUtil2.ONE_SEC_MS)) {
      state17.loading = false;
      return [];
    }
    try {
      if (address && chainId && chain) {
        const balances = await SendApiUtil.getMyTokensWithBalance();
        state17.tokenBalances = balances;
        state17.lastRetry = void 0;
        return balances;
      }
    } catch (error) {
      state17.lastRetry = Date.now();
      onError == null ? void 0 : onError(error);
      SnackController.showError("Token Balance Unavailable");
    } finally {
      state17.loading = false;
    }
    return [];
  },
  fetchNetworkBalance() {
    if (state17.tokenBalances.length === 0) {
      return;
    }
    const networkTokenBalances = SendApiUtil.mapBalancesToSwapTokens(state17.tokenBalances);
    if (!networkTokenBalances) {
      return;
    }
    const networkToken = networkTokenBalances.find((token) => token.address === ChainController.getActiveNetworkTokenAddress());
    if (!networkToken) {
      return;
    }
    state17.networkBalanceInUSD = networkToken ? NumberUtil.multiply(networkToken.quantity.numeric, networkToken.price).toString() : "0";
  },
  async sendNativeToken(params) {
    var _a, _b, _c2, _d;
    RouterController.pushTransactionStack({});
    const to2 = params.receiverAddress;
    const address = AccountController.state.address;
    const value = ConnectionController.parseUnits(params.sendTokenAmount.toString(), Number(params.decimals));
    const data = "0x";
    await ConnectionController.sendTransaction({
      chainNamespace: "eip155",
      to: to2,
      address,
      data,
      value: value ?? BigInt(0)
    });
    EventsController.sendEvent({
      type: "track",
      event: "SEND_SUCCESS",
      properties: {
        isSmartAccount: ((_a = AccountController.state.preferredAccountTypes) == null ? void 0 : _a["eip155"]) === W3mFrameRpcConstants.ACCOUNT_TYPES.SMART_ACCOUNT,
        token: ((_b = SendController.state.token) == null ? void 0 : _b.symbol) || "",
        amount: params.sendTokenAmount,
        network: ((_c2 = ChainController.state.activeCaipNetwork) == null ? void 0 : _c2.caipNetworkId) || ""
      }
    });
    (_d = ConnectionController._getClient()) == null ? void 0 : _d.updateBalance("eip155");
    SendController.resetSend();
  },
  async sendERC20Token(params) {
    RouterController.pushTransactionStack({
      onSuccess() {
        RouterController.replace("Account");
      }
    });
    const amount = ConnectionController.parseUnits(params.sendTokenAmount.toString(), Number(params.decimals));
    if (AccountController.state.address && params.sendTokenAmount && params.receiverAddress && params.tokenAddress) {
      const tokenAddress = CoreHelperUtil.getPlainAddress(params.tokenAddress);
      await ConnectionController.writeContract({
        fromAddress: AccountController.state.address,
        tokenAddress,
        args: [params.receiverAddress, amount ?? BigInt(0)],
        method: "transfer",
        abi: ContractUtil.getERC20Abi(tokenAddress),
        chainNamespace: "eip155"
      });
      SendController.resetSend();
    }
  },
  async sendSolanaToken() {
    var _a;
    if (!SendController.state.sendTokenAmount || !SendController.state.receiverAddress) {
      throw new Error("An amount and receiver address are required");
    }
    RouterController.pushTransactionStack({
      onSuccess() {
        RouterController.replace("Account");
      }
    });
    await ConnectionController.sendTransaction({
      chainNamespace: "solana",
      to: SendController.state.receiverAddress,
      value: SendController.state.sendTokenAmount
    });
    (_a = ConnectionController._getClient()) == null ? void 0 : _a.updateBalance("solana");
    SendController.resetSend();
  },
  resetSend() {
    state17.token = void 0;
    state17.sendTokenAmount = void 0;
    state17.receiverAddress = void 0;
    state17.receiverProfileImageUrl = void 0;
    state17.receiverProfileName = void 0;
    state17.loading = false;
    state17.tokenBalances = [];
  }
};
var SendController = withErrorBoundary(controller11);

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/ChainController.js
var accountState = {
  currentTab: 0,
  tokenBalance: [],
  smartAccountDeployed: false,
  addressLabels: /* @__PURE__ */ new Map(),
  allAccounts: [],
  user: void 0
};
var networkState = {
  caipNetwork: void 0,
  supportsAllNetworks: true,
  smartAccountEnabledNetworks: []
};
var state18 = proxy({
  chains: proxyMap(),
  activeCaipAddress: void 0,
  activeChain: void 0,
  activeCaipNetwork: void 0,
  noAdapters: false,
  universalAdapter: {
    networkControllerClient: void 0,
    connectionControllerClient: void 0
  },
  isSwitchingNamespace: false
});
var controller12 = {
  state: state18,
  subscribe(callback) {
    return subscribe(state18, () => {
      callback(state18);
    });
  },
  subscribeKey(key, callback) {
    return subscribeKey(state18, key, callback);
  },
  subscribeChainProp(property, callback, chain) {
    let prev = void 0;
    return subscribe(state18.chains, () => {
      var _a;
      const activeChain = chain || state18.activeChain;
      if (activeChain) {
        const nextValue = (_a = state18.chains.get(activeChain)) == null ? void 0 : _a[property];
        if (prev !== nextValue) {
          prev = nextValue;
          callback(nextValue);
        }
      }
    });
  },
  initialize(adapters, caipNetworks, clients) {
    const { chainId: activeChainId, namespace: activeNamespace } = StorageUtil.getActiveNetworkProps();
    const activeCaipNetwork = caipNetworks == null ? void 0 : caipNetworks.find((network) => network.id.toString() === (activeChainId == null ? void 0 : activeChainId.toString()));
    const defaultAdapter = adapters.find((adapter) => (adapter == null ? void 0 : adapter.namespace) === activeNamespace);
    const adapterToActivate = defaultAdapter || (adapters == null ? void 0 : adapters[0]);
    const namespacesFromAdapters = adapters.map((a) => a.namespace).filter((n2) => n2 !== void 0);
    const namespaces = OptionsController.state.enableEmbedded ? /* @__PURE__ */ new Set([...namespacesFromAdapters]) : /* @__PURE__ */ new Set([...(caipNetworks == null ? void 0 : caipNetworks.map((network) => network.chainNamespace)) ?? []]);
    if ((adapters == null ? void 0 : adapters.length) === 0 || !adapterToActivate) {
      state18.noAdapters = true;
    }
    if (!state18.noAdapters) {
      state18.activeChain = adapterToActivate == null ? void 0 : adapterToActivate.namespace;
      state18.activeCaipNetwork = activeCaipNetwork;
      ChainController.setChainNetworkData(adapterToActivate == null ? void 0 : adapterToActivate.namespace, {
        caipNetwork: activeCaipNetwork
      });
      if (state18.activeChain) {
        PublicStateController.set({ activeChain: adapterToActivate == null ? void 0 : adapterToActivate.namespace });
      }
    }
    namespaces.forEach((namespace) => {
      const namespaceNetworks = caipNetworks == null ? void 0 : caipNetworks.filter((network) => network.chainNamespace === namespace);
      ChainController.state.chains.set(namespace, {
        namespace,
        networkState: proxy({
          ...networkState,
          caipNetwork: namespaceNetworks == null ? void 0 : namespaceNetworks[0]
        }),
        accountState: proxy(accountState),
        caipNetworks: namespaceNetworks ?? [],
        ...clients
      });
      ChainController.setRequestedCaipNetworks(namespaceNetworks ?? [], namespace);
    });
  },
  removeAdapter(namespace) {
    var _a, _b;
    if (state18.activeChain === namespace) {
      const nextAdapter = Array.from(state18.chains.entries()).find(([chainNamespace]) => chainNamespace !== namespace);
      if (nextAdapter) {
        const caipNetwork = (_b = (_a = nextAdapter[1]) == null ? void 0 : _a.caipNetworks) == null ? void 0 : _b[0];
        if (caipNetwork) {
          ChainController.setActiveCaipNetwork(caipNetwork);
        }
      }
    }
    state18.chains.delete(namespace);
  },
  addAdapter(adapter, { networkControllerClient, connectionControllerClient }, caipNetworks) {
    state18.chains.set(adapter.namespace, {
      namespace: adapter.namespace,
      networkState: {
        ...networkState,
        caipNetwork: caipNetworks[0]
      },
      accountState,
      caipNetworks,
      connectionControllerClient,
      networkControllerClient
    });
    ChainController.setRequestedCaipNetworks((caipNetworks == null ? void 0 : caipNetworks.filter((caipNetwork) => caipNetwork.chainNamespace === adapter.namespace)) ?? [], adapter.namespace);
  },
  addNetwork(network) {
    var _a;
    const chainAdapter = state18.chains.get(network.chainNamespace);
    if (chainAdapter) {
      const newNetworks = [...chainAdapter.caipNetworks || []];
      if (!((_a = chainAdapter.caipNetworks) == null ? void 0 : _a.find((caipNetwork) => caipNetwork.id === network.id))) {
        newNetworks.push(network);
      }
      state18.chains.set(network.chainNamespace, { ...chainAdapter, caipNetworks: newNetworks });
      ChainController.setRequestedCaipNetworks(newNetworks, network.chainNamespace);
      ConnectorController.filterByNamespace(network.chainNamespace, true);
    }
  },
  removeNetwork(namespace, networkId) {
    var _a, _b, _c2;
    const chainAdapter = state18.chains.get(namespace);
    if (chainAdapter) {
      const isActiveNetwork = ((_a = state18.activeCaipNetwork) == null ? void 0 : _a.id) === networkId;
      const newCaipNetworksOfAdapter = [
        ...((_b = chainAdapter.caipNetworks) == null ? void 0 : _b.filter((network) => network.id !== networkId)) || []
      ];
      if (isActiveNetwork && ((_c2 = chainAdapter == null ? void 0 : chainAdapter.caipNetworks) == null ? void 0 : _c2[0])) {
        ChainController.setActiveCaipNetwork(chainAdapter.caipNetworks[0]);
      }
      state18.chains.set(namespace, { ...chainAdapter, caipNetworks: newCaipNetworksOfAdapter });
      ChainController.setRequestedCaipNetworks(newCaipNetworksOfAdapter || [], namespace);
      if (newCaipNetworksOfAdapter.length === 0) {
        ConnectorController.filterByNamespace(namespace, false);
      }
    }
  },
  setAdapterNetworkState(chain, props) {
    const chainAdapter = state18.chains.get(chain);
    if (chainAdapter) {
      chainAdapter.networkState = {
        ...chainAdapter.networkState || networkState,
        ...props
      };
      state18.chains.set(chain, chainAdapter);
    }
  },
  setChainAccountData(chain, accountProps, _unknown = true) {
    if (!chain) {
      throw new Error("Chain is required to update chain account data");
    }
    const chainAdapter = state18.chains.get(chain);
    if (chainAdapter) {
      const newAccountState = { ...chainAdapter.accountState || accountState, ...accountProps };
      state18.chains.set(chain, { ...chainAdapter, accountState: newAccountState });
      if (state18.chains.size === 1 || state18.activeChain === chain) {
        if (accountProps.caipAddress) {
          state18.activeCaipAddress = accountProps.caipAddress;
        }
        AccountController.replaceState(newAccountState);
      }
    }
  },
  setChainNetworkData(chain, networkProps) {
    if (!chain) {
      return;
    }
    const chainAdapter = state18.chains.get(chain);
    if (chainAdapter) {
      const newNetworkState = { ...chainAdapter.networkState || networkState, ...networkProps };
      state18.chains.set(chain, { ...chainAdapter, networkState: newNetworkState });
    }
  },
  // eslint-disable-next-line max-params
  setAccountProp(prop, value, chain, replaceState = true) {
    ChainController.setChainAccountData(chain, { [prop]: value }, replaceState);
    if (prop === "status" && value === "disconnected" && chain) {
      ConnectorController.removeConnectorId(chain);
    }
  },
  setActiveNamespace(chain) {
    var _a, _b;
    state18.activeChain = chain;
    const newAdapter = chain ? state18.chains.get(chain) : void 0;
    const caipNetwork = (_a = newAdapter == null ? void 0 : newAdapter.networkState) == null ? void 0 : _a.caipNetwork;
    if ((caipNetwork == null ? void 0 : caipNetwork.id) && chain) {
      state18.activeCaipAddress = (_b = newAdapter == null ? void 0 : newAdapter.accountState) == null ? void 0 : _b.caipAddress;
      state18.activeCaipNetwork = caipNetwork;
      ChainController.setChainNetworkData(chain, { caipNetwork });
      StorageUtil.setActiveCaipNetworkId(caipNetwork == null ? void 0 : caipNetwork.caipNetworkId);
      PublicStateController.set({
        activeChain: chain,
        selectedNetworkId: caipNetwork == null ? void 0 : caipNetwork.caipNetworkId
      });
    }
  },
  setActiveCaipNetwork(caipNetwork) {
    var _a, _b, _c2;
    if (!caipNetwork) {
      return;
    }
    if (state18.activeChain !== caipNetwork.chainNamespace) {
      ChainController.setIsSwitchingNamespace(true);
    }
    const newAdapter = state18.chains.get(caipNetwork.chainNamespace);
    state18.activeChain = caipNetwork.chainNamespace;
    state18.activeCaipNetwork = caipNetwork;
    ChainController.setChainNetworkData(caipNetwork.chainNamespace, { caipNetwork });
    if ((_a = newAdapter == null ? void 0 : newAdapter.accountState) == null ? void 0 : _a.address) {
      state18.activeCaipAddress = `${caipNetwork.chainNamespace}:${caipNetwork.id}:${(_b = newAdapter == null ? void 0 : newAdapter.accountState) == null ? void 0 : _b.address}`;
    } else {
      state18.activeCaipAddress = void 0;
    }
    ChainController.setAccountProp("caipAddress", state18.activeCaipAddress, caipNetwork.chainNamespace);
    if (newAdapter) {
      AccountController.replaceState(newAdapter.accountState);
    }
    SendController.resetSend();
    PublicStateController.set({
      activeChain: state18.activeChain,
      selectedNetworkId: (_c2 = state18.activeCaipNetwork) == null ? void 0 : _c2.caipNetworkId
    });
    StorageUtil.setActiveCaipNetworkId(caipNetwork.caipNetworkId);
    const isSupported = ChainController.checkIfSupportedNetwork(caipNetwork.chainNamespace);
    if (!isSupported && OptionsController.state.enableNetworkSwitch && !OptionsController.state.allowUnsupportedChain && !ConnectionController.state.wcBasic) {
      ChainController.showUnsupportedChainUI();
    }
  },
  addCaipNetwork(caipNetwork) {
    var _a;
    if (!caipNetwork) {
      return;
    }
    const chain = state18.chains.get(caipNetwork.chainNamespace);
    if (chain) {
      (_a = chain == null ? void 0 : chain.caipNetworks) == null ? void 0 : _a.push(caipNetwork);
    }
  },
  async switchActiveNamespace(namespace) {
    var _a;
    if (!namespace) {
      return;
    }
    const isDifferentChain = namespace !== ChainController.state.activeChain;
    const caipNetworkOfNamespace = (_a = ChainController.getNetworkData(namespace)) == null ? void 0 : _a.caipNetwork;
    const firstNetworkWithChain = ChainController.getCaipNetworkByNamespace(namespace, caipNetworkOfNamespace == null ? void 0 : caipNetworkOfNamespace.id);
    if (isDifferentChain && firstNetworkWithChain) {
      await ChainController.switchActiveNetwork(firstNetworkWithChain);
    }
  },
  async switchActiveNetwork(network) {
    var _a;
    const activeAdapter = ChainController.state.chains.get(ChainController.state.activeChain);
    const unsupportedNetwork = !((_a = activeAdapter == null ? void 0 : activeAdapter.caipNetworks) == null ? void 0 : _a.some((caipNetwork) => {
      var _a2;
      return caipNetwork.id === ((_a2 = state18.activeCaipNetwork) == null ? void 0 : _a2.id);
    }));
    const networkControllerClient = ChainController.getNetworkControllerClient(network.chainNamespace);
    if (networkControllerClient) {
      try {
        await networkControllerClient.switchCaipNetwork(network);
        if (unsupportedNetwork) {
          ModalController.close();
        }
      } catch (error) {
        RouterController.goBack();
      }
      EventsController.sendEvent({
        type: "track",
        event: "SWITCH_NETWORK",
        properties: { network: network.caipNetworkId }
      });
    }
  },
  getNetworkControllerClient(chainNamespace) {
    const chain = chainNamespace || state18.activeChain;
    const chainAdapter = state18.chains.get(chain);
    if (!chainAdapter) {
      throw new Error("Chain adapter not found");
    }
    if (!chainAdapter.networkControllerClient) {
      throw new Error("NetworkController client not set");
    }
    return chainAdapter.networkControllerClient;
  },
  getConnectionControllerClient(_chain) {
    const chain = _chain || state18.activeChain;
    if (!chain) {
      throw new Error("Chain is required to get connection controller client");
    }
    const chainAdapter = state18.chains.get(chain);
    if (!(chainAdapter == null ? void 0 : chainAdapter.connectionControllerClient)) {
      throw new Error("ConnectionController client not set");
    }
    return chainAdapter.connectionControllerClient;
  },
  getAccountProp(key, _chain) {
    var _a;
    let chain = state18.activeChain;
    if (_chain) {
      chain = _chain;
    }
    if (!chain) {
      return void 0;
    }
    const chainAccountState = (_a = state18.chains.get(chain)) == null ? void 0 : _a.accountState;
    if (!chainAccountState) {
      return void 0;
    }
    return chainAccountState[key];
  },
  getNetworkProp(key, namespace) {
    var _a;
    const chainNetworkState = (_a = state18.chains.get(namespace)) == null ? void 0 : _a.networkState;
    if (!chainNetworkState) {
      return void 0;
    }
    return chainNetworkState[key];
  },
  getRequestedCaipNetworks(chainToFilter) {
    const adapter = state18.chains.get(chainToFilter);
    const { approvedCaipNetworkIds = [], requestedCaipNetworks = [] } = (adapter == null ? void 0 : adapter.networkState) || {};
    const sortedNetworks = CoreHelperUtil.sortRequestedNetworks(approvedCaipNetworkIds, requestedCaipNetworks);
    return sortedNetworks;
  },
  getAllRequestedCaipNetworks() {
    const requestedCaipNetworks = [];
    state18.chains.forEach((chainAdapter) => {
      const caipNetworks = ChainController.getRequestedCaipNetworks(chainAdapter.namespace);
      requestedCaipNetworks.push(...caipNetworks);
    });
    return requestedCaipNetworks;
  },
  setRequestedCaipNetworks(caipNetworks, chain) {
    ChainController.setAdapterNetworkState(chain, { requestedCaipNetworks: caipNetworks });
    const allRequestedCaipNetworks = ChainController.getAllRequestedCaipNetworks();
    const namespaces = allRequestedCaipNetworks.map((network) => network.chainNamespace);
    const uniqueNamespaces = Array.from(new Set(namespaces));
    ConnectorController.filterByNamespaces(uniqueNamespaces);
  },
  getAllApprovedCaipNetworkIds() {
    const approvedCaipNetworkIds = [];
    state18.chains.forEach((chainAdapter) => {
      const approvedIds = ChainController.getApprovedCaipNetworkIds(chainAdapter.namespace);
      approvedCaipNetworkIds.push(...approvedIds);
    });
    return approvedCaipNetworkIds;
  },
  getActiveCaipNetwork() {
    return state18.activeCaipNetwork;
  },
  getActiveCaipAddress() {
    return state18.activeCaipAddress;
  },
  getApprovedCaipNetworkIds(namespace) {
    var _a;
    const adapter = state18.chains.get(namespace);
    const approvedCaipNetworkIds = ((_a = adapter == null ? void 0 : adapter.networkState) == null ? void 0 : _a.approvedCaipNetworkIds) || [];
    return approvedCaipNetworkIds;
  },
  async setApprovedCaipNetworksData(namespace) {
    const networkControllerClient = ChainController.getNetworkControllerClient();
    const data = await (networkControllerClient == null ? void 0 : networkControllerClient.getApprovedCaipNetworksData());
    ChainController.setAdapterNetworkState(namespace, {
      approvedCaipNetworkIds: data == null ? void 0 : data.approvedCaipNetworkIds,
      supportsAllNetworks: data == null ? void 0 : data.supportsAllNetworks
    });
  },
  checkIfSupportedNetwork(namespace, caipNetwork) {
    const activeCaipNetwork = caipNetwork || state18.activeCaipNetwork;
    const requestedCaipNetworks = ChainController.getRequestedCaipNetworks(namespace);
    if (!requestedCaipNetworks.length) {
      return true;
    }
    return requestedCaipNetworks == null ? void 0 : requestedCaipNetworks.some((network) => network.id === (activeCaipNetwork == null ? void 0 : activeCaipNetwork.id));
  },
  checkIfSupportedChainId(chainId) {
    if (!state18.activeChain) {
      return true;
    }
    const requestedCaipNetworks = ChainController.getRequestedCaipNetworks(state18.activeChain);
    return requestedCaipNetworks == null ? void 0 : requestedCaipNetworks.some((network) => network.id === chainId);
  },
  // Smart Account Network Handlers
  setSmartAccountEnabledNetworks(smartAccountEnabledNetworks, chain) {
    ChainController.setAdapterNetworkState(chain, { smartAccountEnabledNetworks });
  },
  checkIfSmartAccountEnabled() {
    var _a;
    const networkId = NetworkUtil.caipNetworkIdToNumber((_a = state18.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId);
    const activeChain = state18.activeChain;
    if (!activeChain || !networkId) {
      return false;
    }
    const smartAccountEnabledNetworks = ChainController.getNetworkProp("smartAccountEnabledNetworks", activeChain);
    return Boolean(smartAccountEnabledNetworks == null ? void 0 : smartAccountEnabledNetworks.includes(Number(networkId)));
  },
  getActiveNetworkTokenAddress() {
    var _a, _b;
    const namespace = ((_a = state18.activeCaipNetwork) == null ? void 0 : _a.chainNamespace) || "eip155";
    const chainId = ((_b = state18.activeCaipNetwork) == null ? void 0 : _b.id) || 1;
    const address = ConstantsUtil2.NATIVE_TOKEN_ADDRESS[namespace];
    return `${namespace}:${chainId}:${address}`;
  },
  showUnsupportedChainUI() {
    ModalController.open({ view: "UnsupportedChain" });
  },
  checkIfNamesSupported() {
    const activeCaipNetwork = state18.activeCaipNetwork;
    return Boolean((activeCaipNetwork == null ? void 0 : activeCaipNetwork.chainNamespace) && ConstantsUtil2.NAMES_SUPPORTED_CHAIN_NAMESPACES.includes(activeCaipNetwork.chainNamespace));
  },
  resetNetwork(namespace) {
    ChainController.setAdapterNetworkState(namespace, {
      approvedCaipNetworkIds: void 0,
      supportsAllNetworks: true,
      smartAccountEnabledNetworks: []
    });
  },
  resetAccount(chain) {
    const chainToWrite = chain;
    if (!chainToWrite) {
      throw new Error("Chain is required to set account prop");
    }
    state18.activeCaipAddress = void 0;
    ChainController.setChainAccountData(chainToWrite, {
      smartAccountDeployed: false,
      currentTab: 0,
      caipAddress: void 0,
      address: void 0,
      balance: void 0,
      balanceSymbol: void 0,
      profileName: void 0,
      profileImage: void 0,
      addressExplorerUrl: void 0,
      tokenBalance: [],
      connectedWalletInfo: void 0,
      preferredAccountTypes: void 0,
      socialProvider: void 0,
      socialWindow: void 0,
      farcasterUrl: void 0,
      allAccounts: [],
      user: void 0,
      status: "disconnected"
    });
    ConnectorController.removeConnectorId(chainToWrite);
  },
  setIsSwitchingNamespace(isSwitchingNamespace) {
    state18.isSwitchingNamespace = isSwitchingNamespace;
  },
  getFirstCaipNetworkSupportsAuthConnector() {
    var _a, _b;
    const availableChains = [];
    let firstCaipNetwork = void 0;
    state18.chains.forEach((chain) => {
      if (ConstantsUtil.AUTH_CONNECTOR_SUPPORTED_CHAINS.find((ns) => ns === chain.namespace)) {
        if (chain.namespace) {
          availableChains.push(chain.namespace);
        }
      }
    });
    if (availableChains.length > 0) {
      const firstAvailableChain = availableChains[0];
      firstCaipNetwork = firstAvailableChain ? (_b = (_a = state18.chains.get(firstAvailableChain)) == null ? void 0 : _a.caipNetworks) == null ? void 0 : _b[0] : void 0;
      return firstCaipNetwork;
    }
    return void 0;
  },
  getAccountData(chainNamespace) {
    var _a;
    if (!chainNamespace) {
      return AccountController.state;
    }
    return (_a = ChainController.state.chains.get(chainNamespace)) == null ? void 0 : _a.accountState;
  },
  getNetworkData(chainNamespace) {
    var _a;
    const namespace = chainNamespace || state18.activeChain;
    if (!namespace) {
      return void 0;
    }
    return (_a = ChainController.state.chains.get(namespace)) == null ? void 0 : _a.networkState;
  },
  getCaipNetworkByNamespace(chainNamespace, chainId) {
    var _a, _b, _c2;
    if (!chainNamespace) {
      return void 0;
    }
    const chain = ChainController.state.chains.get(chainNamespace);
    const byChainId = (_a = chain == null ? void 0 : chain.caipNetworks) == null ? void 0 : _a.find((network) => network.id === chainId);
    if (byChainId) {
      return byChainId;
    }
    return ((_b = chain == null ? void 0 : chain.networkState) == null ? void 0 : _b.caipNetwork) || ((_c2 = chain == null ? void 0 : chain.caipNetworks) == null ? void 0 : _c2[0]);
  },
  /**
   * Get the requested CaipNetwork IDs for a given namespace. If namespace is not provided, all requested CaipNetwork IDs will be returned
   * @param namespace - The namespace to get the requested CaipNetwork IDs for
   * @returns The requested CaipNetwork IDs
   */
  getRequestedCaipNetworkIds() {
    const namespace = ConnectorController.state.filterByNamespace;
    const chains = namespace ? [state18.chains.get(namespace)] : Array.from(state18.chains.values());
    return chains.flatMap((chain) => (chain == null ? void 0 : chain.caipNetworks) || []).map((caipNetwork) => caipNetwork.caipNetworkId);
  },
  getCaipNetworks(namespace) {
    if (namespace) {
      return ChainController.getRequestedCaipNetworks(namespace);
    }
    return ChainController.getAllRequestedCaipNetworks();
  }
};
var ChainController = withErrorBoundary(controller12);

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/ApiController.js
var baseUrl3 = CoreHelperUtil.getApiUrl();
var api3 = new FetchUtil({
  baseUrl: baseUrl3,
  clientId: null
});
var entries = 40;
var recommendedEntries = 4;
var imageCountToFetch = 20;
var state19 = proxy({
  promises: {},
  page: 1,
  count: 0,
  featured: [],
  allFeatured: [],
  recommended: [],
  allRecommended: [],
  wallets: [],
  filteredWallets: [],
  search: [],
  isAnalyticsEnabled: false,
  excludedWallets: [],
  isFetchingRecommendedWallets: false
});
var ApiController = {
  state: state19,
  subscribeKey(key, callback) {
    return subscribeKey(state19, key, callback);
  },
  _getSdkProperties() {
    const { projectId, sdkType, sdkVersion } = OptionsController.state;
    return {
      projectId,
      st: sdkType || "appkit",
      sv: sdkVersion || "html-wagmi-4.2.2"
    };
  },
  _filterOutExtensions(wallets) {
    if (OptionsController.state.isUniversalProvider) {
      return wallets.filter((w) => Boolean(w.mobile_link || w.desktop_link || w.webapp_link));
    }
    return wallets;
  },
  async _fetchWalletImage(imageId) {
    const imageUrl = `${api3.baseUrl}/getWalletImage/${imageId}`;
    const blob = await api3.getBlob({ path: imageUrl, params: ApiController._getSdkProperties() });
    AssetController.setWalletImage(imageId, URL.createObjectURL(blob));
  },
  async _fetchNetworkImage(imageId) {
    const imageUrl = `${api3.baseUrl}/public/getAssetImage/${imageId}`;
    const blob = await api3.getBlob({ path: imageUrl, params: ApiController._getSdkProperties() });
    AssetController.setNetworkImage(imageId, URL.createObjectURL(blob));
  },
  async _fetchConnectorImage(imageId) {
    const imageUrl = `${api3.baseUrl}/public/getAssetImage/${imageId}`;
    const blob = await api3.getBlob({ path: imageUrl, params: ApiController._getSdkProperties() });
    AssetController.setConnectorImage(imageId, URL.createObjectURL(blob));
  },
  async _fetchCurrencyImage(countryCode) {
    const imageUrl = `${api3.baseUrl}/public/getCurrencyImage/${countryCode}`;
    const blob = await api3.getBlob({ path: imageUrl, params: ApiController._getSdkProperties() });
    AssetController.setCurrencyImage(countryCode, URL.createObjectURL(blob));
  },
  async _fetchTokenImage(symbol) {
    const imageUrl = `${api3.baseUrl}/public/getTokenImage/${symbol}`;
    const blob = await api3.getBlob({ path: imageUrl, params: ApiController._getSdkProperties() });
    AssetController.setTokenImage(symbol, URL.createObjectURL(blob));
  },
  _filterWalletsByPlatform(wallets) {
    const filteredWallets = CoreHelperUtil.isMobile() ? wallets == null ? void 0 : wallets.filter((w) => {
      if (w.mobile_link) {
        return true;
      }
      if (w.id === CUSTOM_DEEPLINK_WALLETS.COINBASE.id) {
        return true;
      }
      const isSolana = ChainController.state.activeChain === "solana";
      return isSolana && (w.id === CUSTOM_DEEPLINK_WALLETS.SOLFLARE.id || w.id === CUSTOM_DEEPLINK_WALLETS.PHANTOM.id);
    }) : wallets;
    return filteredWallets;
  },
  async fetchProjectConfig() {
    const response = await api3.get({
      path: "/appkit/v1/config",
      params: ApiController._getSdkProperties()
    });
    return response.features;
  },
  async fetchAllowedOrigins() {
    try {
      const { allowedOrigins } = await api3.get({
        path: "/projects/v1/origins",
        params: ApiController._getSdkProperties()
      });
      return allowedOrigins;
    } catch (error) {
      return [];
    }
  },
  async fetchNetworkImages() {
    const requestedCaipNetworks = ChainController.getAllRequestedCaipNetworks();
    const ids = requestedCaipNetworks == null ? void 0 : requestedCaipNetworks.map(({ assets }) => assets == null ? void 0 : assets.imageId).filter(Boolean).filter((imageId) => !AssetUtil.getNetworkImageById(imageId));
    if (ids) {
      await Promise.allSettled(ids.map((id) => ApiController._fetchNetworkImage(id)));
    }
  },
  async fetchConnectorImages() {
    const { connectors } = ConnectorController.state;
    const ids = connectors.map(({ imageId }) => imageId).filter(Boolean);
    await Promise.allSettled(ids.map((id) => ApiController._fetchConnectorImage(id)));
  },
  async fetchCurrencyImages(currencies = []) {
    await Promise.allSettled(currencies.map((currency) => ApiController._fetchCurrencyImage(currency)));
  },
  async fetchTokenImages(tokens = []) {
    await Promise.allSettled(tokens.map((token) => ApiController._fetchTokenImage(token)));
  },
  async fetchWallets(params) {
    var _a;
    const exclude = params.exclude ?? [];
    const sdkProperties = ApiController._getSdkProperties();
    if (sdkProperties.sv.startsWith("html-core-")) {
      exclude.push(...Object.values(CUSTOM_DEEPLINK_WALLETS).map((w) => w.id));
    }
    const wallets = await api3.get({
      path: "/getWallets",
      params: {
        ...ApiController._getSdkProperties(),
        ...params,
        page: String(params.page),
        entries: String(params.entries),
        include: (_a = params.include) == null ? void 0 : _a.join(","),
        exclude: exclude.join(",")
      }
    });
    const filteredWallets = ApiController._filterWalletsByPlatform(wallets == null ? void 0 : wallets.data);
    return {
      data: filteredWallets || [],
      // Keep original count for display on main page
      count: wallets == null ? void 0 : wallets.count
    };
  },
  async fetchFeaturedWallets() {
    const { featuredWalletIds } = OptionsController.state;
    if (featuredWalletIds == null ? void 0 : featuredWalletIds.length) {
      const params = {
        ...ApiController._getSdkProperties(),
        page: 1,
        entries: (featuredWalletIds == null ? void 0 : featuredWalletIds.length) ?? recommendedEntries,
        include: featuredWalletIds
      };
      const { data } = await ApiController.fetchWallets(params);
      const sortedData = [...data].sort((a, b) => featuredWalletIds.indexOf(a.id) - featuredWalletIds.indexOf(b.id));
      const images = sortedData.map((d2) => d2.image_id).filter(Boolean);
      await Promise.allSettled(images.map((id) => ApiController._fetchWalletImage(id)));
      state19.featured = sortedData;
      state19.allFeatured = sortedData;
    }
  },
  async fetchRecommendedWallets() {
    try {
      state19.isFetchingRecommendedWallets = true;
      const { includeWalletIds, excludeWalletIds, featuredWalletIds } = OptionsController.state;
      const exclude = [...excludeWalletIds ?? [], ...featuredWalletIds ?? []].filter(Boolean);
      const chains = ChainController.getRequestedCaipNetworkIds().join(",");
      const params = {
        page: 1,
        entries: recommendedEntries,
        include: includeWalletIds,
        exclude,
        chains
      };
      const { data, count } = await ApiController.fetchWallets(params);
      const recent = StorageUtil.getRecentWallets();
      const recommendedImages = data.map((d2) => d2.image_id).filter(Boolean);
      const recentImages = recent.map((r2) => r2.image_id).filter(Boolean);
      await Promise.allSettled([...recommendedImages, ...recentImages].map((id) => ApiController._fetchWalletImage(id)));
      state19.recommended = data;
      state19.allRecommended = data;
      state19.count = count ?? 0;
    } catch {
    } finally {
      state19.isFetchingRecommendedWallets = false;
    }
  },
  async fetchWalletsByPage({ page }) {
    const { includeWalletIds, excludeWalletIds, featuredWalletIds } = OptionsController.state;
    const chains = ChainController.getRequestedCaipNetworkIds().join(",");
    const exclude = [
      ...state19.recommended.map(({ id }) => id),
      ...excludeWalletIds ?? [],
      ...featuredWalletIds ?? []
    ].filter(Boolean);
    const params = {
      page,
      entries,
      include: includeWalletIds,
      exclude,
      chains
    };
    const { data, count } = await ApiController.fetchWallets(params);
    const images = data.slice(0, imageCountToFetch).map((w) => w.image_id).filter(Boolean);
    await Promise.allSettled(images.map((id) => ApiController._fetchWalletImage(id)));
    state19.wallets = CoreHelperUtil.uniqueBy([...state19.wallets, ...ApiController._filterOutExtensions(data)], "id").filter((w) => {
      var _a;
      return (_a = w.chains) == null ? void 0 : _a.some((chain) => chains.includes(chain));
    });
    state19.count = count > state19.count ? count : state19.count;
    state19.page = page;
  },
  async initializeExcludedWallets({ ids }) {
    const params = {
      page: 1,
      entries: ids.length,
      include: ids
    };
    const { data } = await ApiController.fetchWallets(params);
    if (data) {
      data.forEach((wallet) => {
        state19.excludedWallets.push({ rdns: wallet.rdns, name: wallet.name });
      });
    }
  },
  async searchWallet({ search, badge }) {
    const { includeWalletIds, excludeWalletIds } = OptionsController.state;
    const chains = ChainController.getRequestedCaipNetworkIds().join(",");
    state19.search = [];
    const params = {
      page: 1,
      entries: 100,
      search: search == null ? void 0 : search.trim(),
      badge_type: badge,
      include: includeWalletIds,
      exclude: excludeWalletIds,
      chains
    };
    const { data } = await ApiController.fetchWallets(params);
    EventsController.sendEvent({
      type: "track",
      event: "SEARCH_WALLET",
      properties: { badge: badge ?? "", search: search ?? "" }
    });
    const images = data.map((w) => w.image_id).filter(Boolean);
    await Promise.allSettled([
      ...images.map((id) => ApiController._fetchWalletImage(id)),
      CoreHelperUtil.wait(300)
    ]);
    state19.search = ApiController._filterOutExtensions(data);
  },
  initPromise(key, fetchFn) {
    const existingPromise = state19.promises[key];
    if (existingPromise) {
      return existingPromise;
    }
    return state19.promises[key] = fetchFn();
  },
  prefetch({ fetchConnectorImages = true, fetchFeaturedWallets = true, fetchRecommendedWallets = true, fetchNetworkImages = true } = {}) {
    const promises = [
      fetchConnectorImages && ApiController.initPromise("connectorImages", ApiController.fetchConnectorImages),
      fetchFeaturedWallets && ApiController.initPromise("featuredWallets", ApiController.fetchFeaturedWallets),
      fetchRecommendedWallets && ApiController.initPromise("recommendedWallets", ApiController.fetchRecommendedWallets),
      fetchNetworkImages && ApiController.initPromise("networkImages", ApiController.fetchNetworkImages)
    ].filter(Boolean);
    return Promise.allSettled(promises);
  },
  prefetchAnalyticsConfig() {
    var _a;
    if ((_a = OptionsController.state.features) == null ? void 0 : _a.analytics) {
      ApiController.fetchAnalyticsConfig();
    }
  },
  async fetchAnalyticsConfig() {
    try {
      const { isAnalyticsEnabled } = await api3.get({
        path: "/getAnalyticsConfig",
        params: ApiController._getSdkProperties()
      });
      OptionsController.setFeatures({ analytics: isAnalyticsEnabled });
    } catch (error) {
      OptionsController.setFeatures({ analytics: false });
    }
  },
  filterByNamespaces(namespaces) {
    if (!(namespaces == null ? void 0 : namespaces.length)) {
      state19.featured = state19.allFeatured;
      state19.recommended = state19.allRecommended;
      return;
    }
    const caipNetworkIds = ChainController.getRequestedCaipNetworkIds().join(",");
    state19.featured = state19.allFeatured.filter((wallet) => {
      var _a;
      return (_a = wallet.chains) == null ? void 0 : _a.some((chain) => caipNetworkIds.includes(chain));
    });
    state19.recommended = state19.allRecommended.filter((wallet) => {
      var _a;
      return (_a = wallet.chains) == null ? void 0 : _a.some((chain) => caipNetworkIds.includes(chain));
    });
    state19.filteredWallets = state19.wallets.filter((wallet) => {
      var _a;
      return (_a = wallet.chains) == null ? void 0 : _a.some((chain) => caipNetworkIds.includes(chain));
    });
  },
  clearFilterByNamespaces() {
    state19.filteredWallets = [];
  },
  setFilterByNamespace(namespace) {
    if (!namespace) {
      state19.featured = state19.allFeatured;
      state19.recommended = state19.allRecommended;
      return;
    }
    const caipNetworkIds = ChainController.getRequestedCaipNetworkIds().join(",");
    state19.featured = state19.allFeatured.filter((wallet) => {
      var _a;
      return (_a = wallet.chains) == null ? void 0 : _a.some((chain) => caipNetworkIds.includes(chain));
    });
    state19.recommended = state19.allRecommended.filter((wallet) => {
      var _a;
      return (_a = wallet.chains) == null ? void 0 : _a.some((chain) => caipNetworkIds.includes(chain));
    });
    state19.filteredWallets = state19.wallets.filter((wallet) => {
      var _a;
      return (_a = wallet.chains) == null ? void 0 : _a.some((chain) => caipNetworkIds.includes(chain));
    });
  }
};

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/core/dist/index.es.js
var import_events3 = __toESM(require_events());

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/types/dist/index.es.js
var import_events2 = __toESM(require_events());

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/core/dist/index.es.js
var import_time2 = __toESM(require_cjs());

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/utils/dist/index.es.js
var import_time = __toESM(require_cjs());
var import_window_getters = __toESM(require_cjs2());
var import_window_metadata = __toESM(require_cjs3());

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/formatters/transaction.js
var transactionType = {
  "0x0": "legacy",
  "0x1": "eip2930",
  "0x2": "eip1559",
  "0x3": "eip4844",
  "0x4": "eip7702"
};
function formatTransaction(transaction) {
  const transaction_ = {
    ...transaction,
    blockHash: transaction.blockHash ? transaction.blockHash : null,
    blockNumber: transaction.blockNumber ? BigInt(transaction.blockNumber) : null,
    chainId: transaction.chainId ? hexToNumber(transaction.chainId) : void 0,
    gas: transaction.gas ? BigInt(transaction.gas) : void 0,
    gasPrice: transaction.gasPrice ? BigInt(transaction.gasPrice) : void 0,
    maxFeePerBlobGas: transaction.maxFeePerBlobGas ? BigInt(transaction.maxFeePerBlobGas) : void 0,
    maxFeePerGas: transaction.maxFeePerGas ? BigInt(transaction.maxFeePerGas) : void 0,
    maxPriorityFeePerGas: transaction.maxPriorityFeePerGas ? BigInt(transaction.maxPriorityFeePerGas) : void 0,
    nonce: transaction.nonce ? hexToNumber(transaction.nonce) : void 0,
    to: transaction.to ? transaction.to : null,
    transactionIndex: transaction.transactionIndex ? Number(transaction.transactionIndex) : null,
    type: transaction.type ? transactionType[transaction.type] : void 0,
    typeHex: transaction.type ? transaction.type : void 0,
    value: transaction.value ? BigInt(transaction.value) : void 0,
    v: transaction.v ? BigInt(transaction.v) : void 0
  };
  if (transaction.authorizationList)
    transaction_.authorizationList = formatAuthorizationList(transaction.authorizationList);
  transaction_.yParity = (() => {
    if (transaction.yParity)
      return Number(transaction.yParity);
    if (typeof transaction_.v === "bigint") {
      if (transaction_.v === 0n || transaction_.v === 27n)
        return 0;
      if (transaction_.v === 1n || transaction_.v === 28n)
        return 1;
      if (transaction_.v >= 35n)
        return transaction_.v % 2n === 0n ? 1 : 0;
    }
    return void 0;
  })();
  if (transaction_.type === "legacy") {
    delete transaction_.accessList;
    delete transaction_.maxFeePerBlobGas;
    delete transaction_.maxFeePerGas;
    delete transaction_.maxPriorityFeePerGas;
    delete transaction_.yParity;
  }
  if (transaction_.type === "eip2930") {
    delete transaction_.maxFeePerBlobGas;
    delete transaction_.maxFeePerGas;
    delete transaction_.maxPriorityFeePerGas;
  }
  if (transaction_.type === "eip1559") {
    delete transaction_.maxFeePerBlobGas;
  }
  return transaction_;
}
var defineTransaction = defineFormatter("transaction", formatTransaction);
function formatAuthorizationList(authorizationList) {
  return authorizationList.map((authorization) => ({
    contractAddress: authorization.address,
    chainId: Number(authorization.chainId),
    nonce: Number(authorization.nonce),
    r: authorization.r,
    s: authorization.s,
    yParity: Number(authorization.yParity)
  }));
}

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/formatters/block.js
function formatBlock(block) {
  const transactions = (block.transactions ?? []).map((transaction) => {
    if (typeof transaction === "string")
      return transaction;
    return formatTransaction(transaction);
  });
  return {
    ...block,
    baseFeePerGas: block.baseFeePerGas ? BigInt(block.baseFeePerGas) : null,
    blobGasUsed: block.blobGasUsed ? BigInt(block.blobGasUsed) : void 0,
    difficulty: block.difficulty ? BigInt(block.difficulty) : void 0,
    excessBlobGas: block.excessBlobGas ? BigInt(block.excessBlobGas) : void 0,
    gasLimit: block.gasLimit ? BigInt(block.gasLimit) : void 0,
    gasUsed: block.gasUsed ? BigInt(block.gasUsed) : void 0,
    hash: block.hash ? block.hash : null,
    logsBloom: block.logsBloom ? block.logsBloom : null,
    nonce: block.nonce ? block.nonce : null,
    number: block.number ? BigInt(block.number) : null,
    size: block.size ? BigInt(block.size) : void 0,
    timestamp: block.timestamp ? BigInt(block.timestamp) : void 0,
    transactions,
    totalDifficulty: block.totalDifficulty ? BigInt(block.totalDifficulty) : null
  };
}
var defineBlock = defineFormatter("block", formatBlock);

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/_esm/actions/public/getTransactionCount.js
async function getTransactionCount(client, { address, blockTag = "latest", blockNumber }) {
  const count = await client.request({
    method: "eth_getTransactionCount",
    params: [address, blockNumber ? numberToHex(blockNumber) : blockTag]
  }, { dedupe: Boolean(blockNumber) });
  return hexToNumber(count);
}

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/_esm/constants/blob.js
var blobsPerTransaction = 6;
var bytesPerFieldElement = 32;
var fieldElementsPerBlob = 4096;
var bytesPerBlob = bytesPerFieldElement * fieldElementsPerBlob;
var maxBytesPerTransaction = bytesPerBlob * blobsPerTransaction - // terminator byte (0x80).
1 - // zero byte (0x00) appended to each field element.
1 * fieldElementsPerBlob * blobsPerTransaction;

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/formatters/log.js
function formatLog(log, { args, eventName } = {}) {
  return {
    ...log,
    blockHash: log.blockHash ? log.blockHash : null,
    blockNumber: log.blockNumber ? BigInt(log.blockNumber) : null,
    logIndex: log.logIndex ? Number(log.logIndex) : null,
    transactionHash: log.transactionHash ? log.transactionHash : null,
    transactionIndex: log.transactionIndex ? Number(log.transactionIndex) : null,
    ...eventName ? { args, eventName } : {}
  };
}

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/_esm/actions/wallet/sendTransaction.js
var supportsWalletNamespace = new LruMap(128);

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/promise/withDedupe.js
var promiseCache = new LruMap(8192);

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/rpc/id.js
function createIdStore() {
  return {
    current: 0,
    take() {
      return this.current++;
    },
    reset() {
      this.current = 0;
    }
  };
}
var idCache = createIdStore();

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/formatters/transactionReceipt.js
var receiptStatuses = {
  "0x0": "reverted",
  "0x1": "success"
};
function formatTransactionReceipt(transactionReceipt) {
  const receipt = {
    ...transactionReceipt,
    blockNumber: transactionReceipt.blockNumber ? BigInt(transactionReceipt.blockNumber) : null,
    contractAddress: transactionReceipt.contractAddress ? transactionReceipt.contractAddress : null,
    cumulativeGasUsed: transactionReceipt.cumulativeGasUsed ? BigInt(transactionReceipt.cumulativeGasUsed) : null,
    effectiveGasPrice: transactionReceipt.effectiveGasPrice ? BigInt(transactionReceipt.effectiveGasPrice) : null,
    gasUsed: transactionReceipt.gasUsed ? BigInt(transactionReceipt.gasUsed) : null,
    logs: transactionReceipt.logs ? transactionReceipt.logs.map((log) => formatLog(log)) : null,
    to: transactionReceipt.to ? transactionReceipt.to : null,
    transactionIndex: transactionReceipt.transactionIndex ? hexToNumber(transactionReceipt.transactionIndex) : null,
    status: transactionReceipt.status ? receiptStatuses[transactionReceipt.status] : null,
    type: transactionReceipt.type ? transactionType[transactionReceipt.type] || transactionReceipt.type : null
  };
  if (transactionReceipt.blobGasPrice)
    receipt.blobGasPrice = BigInt(transactionReceipt.blobGasPrice);
  if (transactionReceipt.blobGasUsed)
    receipt.blobGasUsed = BigInt(transactionReceipt.blobGasUsed);
  return receipt;
}
var defineTransactionReceipt = defineFormatter("transactionReceipt", formatTransactionReceipt);

// node_modules/@reown/appkit-controllers/node_modules/@noble/hashes/esm/ripemd160.js
var Rho = new Uint8Array([7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8]);
var Id = new Uint8Array(new Array(16).fill(0).map((_, i3) => i3));
var Pi = Id.map((i3) => (9 * i3 + 5) % 16);
var idxL = [Id];
var idxR = [Pi];
for (let i3 = 0; i3 < 4; i3++)
  for (let j of [idxL, idxR])
    j.push(j[i3].map((k2) => Rho[k2]));
var shifts = [
  [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8],
  [12, 13, 11, 15, 6, 9, 9, 7, 12, 15, 11, 13, 7, 8, 7, 7],
  [13, 15, 14, 11, 7, 7, 6, 8, 13, 14, 13, 12, 5, 5, 6, 9],
  [14, 11, 12, 14, 8, 6, 5, 5, 15, 12, 15, 14, 9, 9, 8, 6],
  [15, 12, 13, 13, 9, 5, 8, 6, 14, 11, 12, 11, 8, 6, 5, 5]
].map((i3) => new Uint8Array(i3));
var shiftsL = idxL.map((idx, i3) => idx.map((j) => shifts[i3][j]));
var shiftsR = idxR.map((idx, i3) => idx.map((j) => shifts[i3][j]));
var Kl = new Uint32Array([
  0,
  1518500249,
  1859775393,
  2400959708,
  2840853838
]);
var Kr = new Uint32Array([
  1352829926,
  1548603684,
  1836072691,
  2053994217,
  0
]);
function f2(group, x, y4, z) {
  if (group === 0)
    return x ^ y4 ^ z;
  else if (group === 1)
    return x & y4 | ~x & z;
  else if (group === 2)
    return (x | ~y4) ^ z;
  else if (group === 3)
    return x & z | y4 & ~z;
  else
    return x ^ (y4 | ~z);
}
var R_BUF = new Uint32Array(16);
var RIPEMD160 = class extends HashMD {
  constructor() {
    super(64, 20, 8, true);
    this.h0 = 1732584193 | 0;
    this.h1 = 4023233417 | 0;
    this.h2 = 2562383102 | 0;
    this.h3 = 271733878 | 0;
    this.h4 = 3285377520 | 0;
  }
  get() {
    const { h0, h1, h2: h22, h3: h32, h4 } = this;
    return [h0, h1, h22, h32, h4];
  }
  set(h0, h1, h22, h32, h4) {
    this.h0 = h0 | 0;
    this.h1 = h1 | 0;
    this.h2 = h22 | 0;
    this.h3 = h32 | 0;
    this.h4 = h4 | 0;
  }
  process(view, offset) {
    for (let i3 = 0; i3 < 16; i3++, offset += 4)
      R_BUF[i3] = view.getUint32(offset, true);
    let al = this.h0 | 0, ar2 = al, bl = this.h1 | 0, br2 = bl, cl = this.h2 | 0, cr2 = cl, dl = this.h3 | 0, dr3 = dl, el = this.h4 | 0, er3 = el;
    for (let group = 0; group < 5; group++) {
      const rGroup = 4 - group;
      const hbl = Kl[group], hbr = Kr[group];
      const rl = idxL[group], rr3 = idxR[group];
      const sl = shiftsL[group], sr2 = shiftsR[group];
      for (let i3 = 0; i3 < 16; i3++) {
        const tl = rotl(al + f2(group, bl, cl, dl) + R_BUF[rl[i3]] + hbl, sl[i3]) + el | 0;
        al = el, el = dl, dl = rotl(cl, 10) | 0, cl = bl, bl = tl;
      }
      for (let i3 = 0; i3 < 16; i3++) {
        const tr3 = rotl(ar2 + f2(rGroup, br2, cr2, dr3) + R_BUF[rr3[i3]] + hbr, sr2[i3]) + er3 | 0;
        ar2 = er3, er3 = dr3, dr3 = rotl(cr2, 10) | 0, cr2 = br2, br2 = tr3;
      }
    }
    this.set(this.h1 + cl + dr3 | 0, this.h2 + dl + er3 | 0, this.h3 + el + ar2 | 0, this.h4 + al + br2 | 0, this.h0 + bl + cr2 | 0);
  }
  roundClean() {
    R_BUF.fill(0);
  }
  destroy() {
    this.destroyed = true;
    this.buffer.fill(0);
    this.set(0, 0, 0, 0, 0);
  }
};
var ripemd160 = wrapConstructor(() => new RIPEMD160());

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/_esm/utils/nonceManager.js
function createNonceManager(parameters) {
  const { source } = parameters;
  const deltaMap = /* @__PURE__ */ new Map();
  const nonceMap = new LruMap(8192);
  const promiseMap = /* @__PURE__ */ new Map();
  const getKey = ({ address, chainId }) => `${address}.${chainId}`;
  return {
    async consume({ address, chainId, client }) {
      const key = getKey({ address, chainId });
      const promise = this.get({ address, chainId, client });
      this.increment({ address, chainId });
      const nonce = await promise;
      await source.set({ address, chainId }, nonce);
      nonceMap.set(key, nonce);
      return nonce;
    },
    async increment({ address, chainId }) {
      const key = getKey({ address, chainId });
      const delta = deltaMap.get(key) ?? 0;
      deltaMap.set(key, delta + 1);
    },
    async get({ address, chainId, client }) {
      const key = getKey({ address, chainId });
      let promise = promiseMap.get(key);
      if (!promise) {
        promise = (async () => {
          try {
            const nonce = await source.get({ address, chainId, client });
            const previousNonce = nonceMap.get(key) ?? 0;
            if (previousNonce > 0 && nonce <= previousNonce)
              return previousNonce + 1;
            nonceMap.delete(key);
            return nonce;
          } finally {
            this.reset({ address, chainId });
          }
        })();
        promiseMap.set(key, promise);
      }
      const delta = deltaMap.get(key) ?? 0;
      return delta + await promise;
    },
    reset({ address, chainId }) {
      const key = getKey({ address, chainId });
      deltaMap.delete(key);
      promiseMap.delete(key);
    }
  };
}
function jsonRpc() {
  return {
    async get(parameters) {
      const { address, client } = parameters;
      return getTransactionCount(client, {
        address,
        blockTag: "pending"
      });
    },
    set() {
    }
  };
}
var nonceManager = createNonceManager({
  source: jsonRpc()
});

// node_modules/@reown/appkit-controllers/node_modules/ox/_esm/core/version.js
var version = "0.1.1";

// node_modules/@reown/appkit-controllers/node_modules/ox/_esm/core/internal/errors.js
function getVersion2() {
  return version;
}

// node_modules/@reown/appkit-controllers/node_modules/ox/_esm/core/Errors.js
var BaseError2 = class _BaseError extends Error {
  constructor(shortMessage, options = {}) {
    const details = (() => {
      var _a;
      if (options.cause instanceof _BaseError) {
        if (options.cause.details)
          return options.cause.details;
        if (options.cause.shortMessage)
          return options.cause.shortMessage;
      }
      if ((_a = options.cause) == null ? void 0 : _a.message)
        return options.cause.message;
      return options.details;
    })();
    const docsPath = (() => {
      if (options.cause instanceof _BaseError)
        return options.cause.docsPath || options.docsPath;
      return options.docsPath;
    })();
    const docsBaseUrl = "https://oxlib.sh";
    const docs = `${docsBaseUrl}${docsPath ?? ""}`;
    const message = [
      shortMessage || "An error occurred.",
      ...options.metaMessages ? ["", ...options.metaMessages] : [],
      ...details || docsPath ? [
        "",
        details ? `Details: ${details}` : void 0,
        docsPath ? `See: ${docs}` : void 0
      ] : []
    ].filter((x) => typeof x === "string").join("\n");
    super(message, options.cause ? { cause: options.cause } : void 0);
    Object.defineProperty(this, "details", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "docs", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "docsPath", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "shortMessage", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "cause", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "BaseError"
    });
    Object.defineProperty(this, "version", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: `ox@${getVersion2()}`
    });
    this.cause = options.cause;
    this.details = details;
    this.docs = docs;
    this.docsPath = docsPath;
    this.shortMessage = shortMessage;
  }
  walk(fn3) {
    return walk(this, fn3);
  }
};
function walk(err, fn3) {
  if (fn3 == null ? void 0 : fn3(err))
    return err;
  if (err && typeof err === "object" && "cause" in err && err.cause)
    return walk(err.cause, fn3);
  return fn3 ? null : err;
}

// node_modules/@reown/appkit-controllers/node_modules/ox/_esm/core/internal/bytes.js
function assertSize(bytes, size_) {
  if (size2(bytes) > size_)
    throw new SizeOverflowError({
      givenSize: size2(bytes),
      maxSize: size_
    });
}
var charCodeMap = {
  zero: 48,
  nine: 57,
  A: 65,
  F: 70,
  a: 97,
  f: 102
};
function charCodeToBase16(char) {
  if (char >= charCodeMap.zero && char <= charCodeMap.nine)
    return char - charCodeMap.zero;
  if (char >= charCodeMap.A && char <= charCodeMap.F)
    return char - (charCodeMap.A - 10);
  if (char >= charCodeMap.a && char <= charCodeMap.f)
    return char - (charCodeMap.a - 10);
  return void 0;
}
function pad2(bytes, options = {}) {
  const { dir, size: size4 = 32 } = options;
  if (size4 === 0)
    return bytes;
  if (bytes.length > size4)
    throw new SizeExceedsPaddingSizeError({
      size: bytes.length,
      targetSize: size4,
      type: "Bytes"
    });
  const paddedBytes = new Uint8Array(size4);
  for (let i3 = 0; i3 < size4; i3++) {
    const padEnd = dir === "right";
    paddedBytes[padEnd ? i3 : size4 - i3 - 1] = bytes[padEnd ? i3 : bytes.length - i3 - 1];
  }
  return paddedBytes;
}

// node_modules/@reown/appkit-controllers/node_modules/ox/_esm/core/internal/hex.js
function assertSize2(hex, size_) {
  if (size3(hex) > size_)
    throw new SizeOverflowError2({
      givenSize: size3(hex),
      maxSize: size_
    });
}
function pad3(hex_, options = {}) {
  const { dir, size: size4 = 32 } = options;
  if (size4 === 0)
    return hex_;
  const hex = hex_.replace("0x", "");
  if (hex.length > size4 * 2)
    throw new SizeExceedsPaddingSizeError2({
      size: Math.ceil(hex.length / 2),
      targetSize: size4,
      type: "Hex"
    });
  return `0x${hex[dir === "right" ? "padEnd" : "padStart"](size4 * 2, "0")}`;
}

// node_modules/@reown/appkit-controllers/node_modules/ox/_esm/core/Bytes.js
var decoder = new TextDecoder();
var encoder = new TextEncoder();
function from(value) {
  if (value instanceof Uint8Array)
    return value;
  if (typeof value === "string")
    return fromHex2(value);
  return fromArray(value);
}
function fromArray(value) {
  return value instanceof Uint8Array ? value : new Uint8Array(value);
}
function fromHex2(value, options = {}) {
  const { size: size4 } = options;
  let hex = value;
  if (size4) {
    assertSize2(value, size4);
    hex = padRight(value, size4);
  }
  let hexString = hex.slice(2);
  if (hexString.length % 2)
    hexString = `0${hexString}`;
  const length = hexString.length / 2;
  const bytes = new Uint8Array(length);
  for (let index = 0, j = 0; index < length; index++) {
    const nibbleLeft = charCodeToBase16(hexString.charCodeAt(j++));
    const nibbleRight = charCodeToBase16(hexString.charCodeAt(j++));
    if (nibbleLeft === void 0 || nibbleRight === void 0) {
      throw new BaseError2(`Invalid byte sequence ("${hexString[j - 2]}${hexString[j - 1]}" in "${hexString}").`);
    }
    bytes[index] = nibbleLeft * 16 + nibbleRight;
  }
  return bytes;
}
function fromString(value, options = {}) {
  const { size: size4 } = options;
  const bytes = encoder.encode(value);
  if (typeof size4 === "number") {
    assertSize(bytes, size4);
    return padRight2(bytes, size4);
  }
  return bytes;
}
function padRight2(value, size4) {
  return pad2(value, { dir: "right", size: size4 });
}
function size2(value) {
  return value.length;
}
var SizeOverflowError = class extends BaseError2 {
  constructor({ givenSize, maxSize }) {
    super(`Size cannot exceed \`${maxSize}\` bytes. Given size: \`${givenSize}\` bytes.`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Bytes.SizeOverflowError"
    });
  }
};
var SizeExceedsPaddingSizeError = class extends BaseError2 {
  constructor({ size: size4, targetSize, type }) {
    super(`${type.charAt(0).toUpperCase()}${type.slice(1).toLowerCase()} size (\`${size4}\`) exceeds padding size (\`${targetSize}\`).`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Bytes.SizeExceedsPaddingSizeError"
    });
  }
};

// node_modules/@reown/appkit-controllers/node_modules/ox/_esm/core/Hex.js
var encoder2 = new TextEncoder();
var hexes = Array.from({ length: 256 }, (_v, i3) => i3.toString(16).padStart(2, "0"));
function concat2(...values) {
  return `0x${values.reduce((acc, x) => acc + x.replace("0x", ""), "")}`;
}
function fromBoolean(value, options = {}) {
  const hex = `0x${Number(value)}`;
  if (typeof options.size === "number") {
    assertSize2(hex, options.size);
    return padLeft(hex, options.size);
  }
  return hex;
}
function fromBytes2(value, options = {}) {
  let string = "";
  for (let i3 = 0; i3 < value.length; i3++)
    string += hexes[value[i3]];
  const hex = `0x${string}`;
  if (typeof options.size === "number") {
    assertSize2(hex, options.size);
    return padRight(hex, options.size);
  }
  return hex;
}
function fromNumber(value, options = {}) {
  const { signed, size: size4 } = options;
  const value_ = BigInt(value);
  let maxValue;
  if (size4) {
    if (signed)
      maxValue = (1n << BigInt(size4) * 8n - 1n) - 1n;
    else
      maxValue = 2n ** (BigInt(size4) * 8n) - 1n;
  } else if (typeof value === "number") {
    maxValue = BigInt(Number.MAX_SAFE_INTEGER);
  }
  const minValue = typeof maxValue === "bigint" && signed ? -maxValue - 1n : 0;
  if (maxValue && value_ > maxValue || value_ < minValue) {
    const suffix = typeof value === "bigint" ? "n" : "";
    throw new IntegerOutOfRangeError({
      max: maxValue ? `${maxValue}${suffix}` : void 0,
      min: `${minValue}${suffix}`,
      signed,
      size: size4,
      value: `${value}${suffix}`
    });
  }
  const stringValue = (signed && value_ < 0 ? (1n << BigInt(size4 * 8)) + BigInt(value_) : value_).toString(16);
  const hex = `0x${stringValue}`;
  if (size4)
    return padLeft(hex, size4);
  return hex;
}
function fromString2(value, options = {}) {
  return fromBytes2(encoder2.encode(value), options);
}
function padLeft(value, size4) {
  return pad3(value, { dir: "left", size: size4 });
}
function padRight(value, size4) {
  return pad3(value, { dir: "right", size: size4 });
}
function size3(value) {
  return Math.ceil((value.length - 2) / 2);
}
var IntegerOutOfRangeError = class extends BaseError2 {
  constructor({ max, min, signed, size: size4, value }) {
    super(`Number \`${value}\` is not in safe${size4 ? ` ${size4 * 8}-bit` : ""}${signed ? " signed" : " unsigned"} integer range ${max ? `(\`${min}\` to \`${max}\`)` : `(above \`${min}\`)`}`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Hex.IntegerOutOfRangeError"
    });
  }
};
var SizeOverflowError2 = class extends BaseError2 {
  constructor({ givenSize, maxSize }) {
    super(`Size cannot exceed \`${maxSize}\` bytes. Given size: \`${givenSize}\` bytes.`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Hex.SizeOverflowError"
    });
  }
};
var SizeExceedsPaddingSizeError2 = class extends BaseError2 {
  constructor({ size: size4, targetSize, type }) {
    super(`${type.charAt(0).toUpperCase()}${type.slice(1).toLowerCase()} size (\`${size4}\`) exceeds padding size (\`${targetSize}\`).`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Hex.SizeExceedsPaddingSizeError"
    });
  }
};

// node_modules/@reown/appkit-controllers/node_modules/ox/_esm/core/Hash.js
function keccak2562(value, options = {}) {
  const { as = typeof value === "string" ? "Hex" : "Bytes" } = options;
  const bytes = keccak_256(from(value));
  if (as === "Bytes")
    return bytes;
  return fromBytes2(bytes);
}

// node_modules/@reown/appkit-controllers/node_modules/ox/_esm/core/internal/lru.js
var LruMap2 = class extends Map {
  constructor(size4) {
    super();
    Object.defineProperty(this, "maxSize", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    this.maxSize = size4;
  }
  get(key) {
    const value = super.get(key);
    if (super.has(key) && value !== void 0) {
      this.delete(key);
      super.set(key, value);
    }
    return value;
  }
  set(key, value) {
    super.set(key, value);
    if (this.maxSize && this.size > this.maxSize) {
      const firstKey = this.keys().next().value;
      if (firstKey)
        this.delete(firstKey);
    }
    return this;
  }
};

// node_modules/@reown/appkit-controllers/node_modules/ox/_esm/core/Caches.js
var caches = {
  checksum: new LruMap2(8192)
};
var checksum = caches.checksum;

// node_modules/@reown/appkit-controllers/node_modules/ox/_esm/core/Address.js
var addressRegex = /^0x[a-fA-F0-9]{40}$/;
function assert(value, options = {}) {
  const { strict = true } = options;
  if (!addressRegex.test(value))
    throw new InvalidAddressError2({
      address: value,
      cause: new InvalidInputError()
    });
  if (strict) {
    if (value.toLowerCase() === value)
      return;
    if (checksum2(value) !== value)
      throw new InvalidAddressError2({
        address: value,
        cause: new InvalidChecksumError()
      });
  }
}
function checksum2(address) {
  if (checksum.has(address))
    return checksum.get(address);
  assert(address, { strict: false });
  const hexAddress = address.substring(2).toLowerCase();
  const hash = keccak2562(fromString(hexAddress), { as: "Bytes" });
  const characters = hexAddress.split("");
  for (let i3 = 0; i3 < 40; i3 += 2) {
    if (hash[i3 >> 1] >> 4 >= 8 && characters[i3]) {
      characters[i3] = characters[i3].toUpperCase();
    }
    if ((hash[i3 >> 1] & 15) >= 8 && characters[i3 + 1]) {
      characters[i3 + 1] = characters[i3 + 1].toUpperCase();
    }
  }
  const result = `0x${characters.join("")}`;
  checksum.set(address, result);
  return result;
}
var InvalidAddressError2 = class extends BaseError2 {
  constructor({ address, cause }) {
    super(`Address "${address}" is invalid.`, {
      cause
    });
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Address.InvalidAddressError"
    });
  }
};
var InvalidInputError = class extends BaseError2 {
  constructor() {
    super("Address is not a 20 byte (40 hexadecimal character) value.");
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Address.InvalidInputError"
    });
  }
};
var InvalidChecksumError = class extends BaseError2 {
  constructor() {
    super("Address does not match its checksum counterpart.");
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Address.InvalidChecksumError"
    });
  }
};

// node_modules/@reown/appkit-controllers/node_modules/ox/_esm/core/Solidity.js
var arrayRegex2 = /^(.*)\[([0-9]*)\]$/;
var bytesRegex2 = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/;
var integerRegex2 = /^(u?int)(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/;
var maxInt8 = 2n ** (8n - 1n) - 1n;
var maxInt16 = 2n ** (16n - 1n) - 1n;
var maxInt24 = 2n ** (24n - 1n) - 1n;
var maxInt32 = 2n ** (32n - 1n) - 1n;
var maxInt40 = 2n ** (40n - 1n) - 1n;
var maxInt48 = 2n ** (48n - 1n) - 1n;
var maxInt56 = 2n ** (56n - 1n) - 1n;
var maxInt64 = 2n ** (64n - 1n) - 1n;
var maxInt72 = 2n ** (72n - 1n) - 1n;
var maxInt80 = 2n ** (80n - 1n) - 1n;
var maxInt88 = 2n ** (88n - 1n) - 1n;
var maxInt96 = 2n ** (96n - 1n) - 1n;
var maxInt104 = 2n ** (104n - 1n) - 1n;
var maxInt112 = 2n ** (112n - 1n) - 1n;
var maxInt120 = 2n ** (120n - 1n) - 1n;
var maxInt128 = 2n ** (128n - 1n) - 1n;
var maxInt136 = 2n ** (136n - 1n) - 1n;
var maxInt144 = 2n ** (144n - 1n) - 1n;
var maxInt152 = 2n ** (152n - 1n) - 1n;
var maxInt160 = 2n ** (160n - 1n) - 1n;
var maxInt168 = 2n ** (168n - 1n) - 1n;
var maxInt176 = 2n ** (176n - 1n) - 1n;
var maxInt184 = 2n ** (184n - 1n) - 1n;
var maxInt192 = 2n ** (192n - 1n) - 1n;
var maxInt200 = 2n ** (200n - 1n) - 1n;
var maxInt208 = 2n ** (208n - 1n) - 1n;
var maxInt216 = 2n ** (216n - 1n) - 1n;
var maxInt224 = 2n ** (224n - 1n) - 1n;
var maxInt232 = 2n ** (232n - 1n) - 1n;
var maxInt240 = 2n ** (240n - 1n) - 1n;
var maxInt248 = 2n ** (248n - 1n) - 1n;
var maxInt256 = 2n ** (256n - 1n) - 1n;
var minInt8 = -(2n ** (8n - 1n));
var minInt16 = -(2n ** (16n - 1n));
var minInt24 = -(2n ** (24n - 1n));
var minInt32 = -(2n ** (32n - 1n));
var minInt40 = -(2n ** (40n - 1n));
var minInt48 = -(2n ** (48n - 1n));
var minInt56 = -(2n ** (56n - 1n));
var minInt64 = -(2n ** (64n - 1n));
var minInt72 = -(2n ** (72n - 1n));
var minInt80 = -(2n ** (80n - 1n));
var minInt88 = -(2n ** (88n - 1n));
var minInt96 = -(2n ** (96n - 1n));
var minInt104 = -(2n ** (104n - 1n));
var minInt112 = -(2n ** (112n - 1n));
var minInt120 = -(2n ** (120n - 1n));
var minInt128 = -(2n ** (128n - 1n));
var minInt136 = -(2n ** (136n - 1n));
var minInt144 = -(2n ** (144n - 1n));
var minInt152 = -(2n ** (152n - 1n));
var minInt160 = -(2n ** (160n - 1n));
var minInt168 = -(2n ** (168n - 1n));
var minInt176 = -(2n ** (176n - 1n));
var minInt184 = -(2n ** (184n - 1n));
var minInt192 = -(2n ** (192n - 1n));
var minInt200 = -(2n ** (200n - 1n));
var minInt208 = -(2n ** (208n - 1n));
var minInt216 = -(2n ** (216n - 1n));
var minInt224 = -(2n ** (224n - 1n));
var minInt232 = -(2n ** (232n - 1n));
var minInt240 = -(2n ** (240n - 1n));
var minInt248 = -(2n ** (248n - 1n));
var minInt256 = -(2n ** (256n - 1n));
var maxUint8 = 2n ** 8n - 1n;
var maxUint16 = 2n ** 16n - 1n;
var maxUint24 = 2n ** 24n - 1n;
var maxUint32 = 2n ** 32n - 1n;
var maxUint40 = 2n ** 40n - 1n;
var maxUint48 = 2n ** 48n - 1n;
var maxUint56 = 2n ** 56n - 1n;
var maxUint64 = 2n ** 64n - 1n;
var maxUint72 = 2n ** 72n - 1n;
var maxUint80 = 2n ** 80n - 1n;
var maxUint88 = 2n ** 88n - 1n;
var maxUint96 = 2n ** 96n - 1n;
var maxUint104 = 2n ** 104n - 1n;
var maxUint112 = 2n ** 112n - 1n;
var maxUint120 = 2n ** 120n - 1n;
var maxUint128 = 2n ** 128n - 1n;
var maxUint136 = 2n ** 136n - 1n;
var maxUint144 = 2n ** 144n - 1n;
var maxUint152 = 2n ** 152n - 1n;
var maxUint160 = 2n ** 160n - 1n;
var maxUint168 = 2n ** 168n - 1n;
var maxUint176 = 2n ** 176n - 1n;
var maxUint184 = 2n ** 184n - 1n;
var maxUint192 = 2n ** 192n - 1n;
var maxUint200 = 2n ** 200n - 1n;
var maxUint208 = 2n ** 208n - 1n;
var maxUint216 = 2n ** 216n - 1n;
var maxUint224 = 2n ** 224n - 1n;
var maxUint232 = 2n ** 232n - 1n;
var maxUint240 = 2n ** 240n - 1n;
var maxUint248 = 2n ** 248n - 1n;
var maxUint2562 = 2n ** 256n - 1n;

// node_modules/@reown/appkit-controllers/node_modules/ox/_esm/core/internal/cursor.js
var staticCursor = {
  bytes: new Uint8Array(),
  dataView: new DataView(new ArrayBuffer(0)),
  position: 0,
  positionReadCount: /* @__PURE__ */ new Map(),
  recursiveReadCount: 0,
  recursiveReadLimit: Number.POSITIVE_INFINITY,
  assertReadLimit() {
    if (this.recursiveReadCount >= this.recursiveReadLimit)
      throw new RecursiveReadLimitExceededError({
        count: this.recursiveReadCount + 1,
        limit: this.recursiveReadLimit
      });
  },
  assertPosition(position) {
    if (position < 0 || position > this.bytes.length - 1)
      throw new PositionOutOfBoundsError2({
        length: this.bytes.length,
        position
      });
  },
  decrementPosition(offset) {
    if (offset < 0)
      throw new NegativeOffsetError({ offset });
    const position = this.position - offset;
    this.assertPosition(position);
    this.position = position;
  },
  getReadCount(position) {
    return this.positionReadCount.get(position || this.position) || 0;
  },
  incrementPosition(offset) {
    if (offset < 0)
      throw new NegativeOffsetError({ offset });
    const position = this.position + offset;
    this.assertPosition(position);
    this.position = position;
  },
  inspectByte(position_) {
    const position = position_ ?? this.position;
    this.assertPosition(position);
    return this.bytes[position];
  },
  inspectBytes(length, position_) {
    const position = position_ ?? this.position;
    this.assertPosition(position + length - 1);
    return this.bytes.subarray(position, position + length);
  },
  inspectUint8(position_) {
    const position = position_ ?? this.position;
    this.assertPosition(position);
    return this.bytes[position];
  },
  inspectUint16(position_) {
    const position = position_ ?? this.position;
    this.assertPosition(position + 1);
    return this.dataView.getUint16(position);
  },
  inspectUint24(position_) {
    const position = position_ ?? this.position;
    this.assertPosition(position + 2);
    return (this.dataView.getUint16(position) << 8) + this.dataView.getUint8(position + 2);
  },
  inspectUint32(position_) {
    const position = position_ ?? this.position;
    this.assertPosition(position + 3);
    return this.dataView.getUint32(position);
  },
  pushByte(byte) {
    this.assertPosition(this.position);
    this.bytes[this.position] = byte;
    this.position++;
  },
  pushBytes(bytes) {
    this.assertPosition(this.position + bytes.length - 1);
    this.bytes.set(bytes, this.position);
    this.position += bytes.length;
  },
  pushUint8(value) {
    this.assertPosition(this.position);
    this.bytes[this.position] = value;
    this.position++;
  },
  pushUint16(value) {
    this.assertPosition(this.position + 1);
    this.dataView.setUint16(this.position, value);
    this.position += 2;
  },
  pushUint24(value) {
    this.assertPosition(this.position + 2);
    this.dataView.setUint16(this.position, value >> 8);
    this.dataView.setUint8(this.position + 2, value & ~4294967040);
    this.position += 3;
  },
  pushUint32(value) {
    this.assertPosition(this.position + 3);
    this.dataView.setUint32(this.position, value);
    this.position += 4;
  },
  readByte() {
    this.assertReadLimit();
    this._touch();
    const value = this.inspectByte();
    this.position++;
    return value;
  },
  readBytes(length, size4) {
    this.assertReadLimit();
    this._touch();
    const value = this.inspectBytes(length);
    this.position += size4 ?? length;
    return value;
  },
  readUint8() {
    this.assertReadLimit();
    this._touch();
    const value = this.inspectUint8();
    this.position += 1;
    return value;
  },
  readUint16() {
    this.assertReadLimit();
    this._touch();
    const value = this.inspectUint16();
    this.position += 2;
    return value;
  },
  readUint24() {
    this.assertReadLimit();
    this._touch();
    const value = this.inspectUint24();
    this.position += 3;
    return value;
  },
  readUint32() {
    this.assertReadLimit();
    this._touch();
    const value = this.inspectUint32();
    this.position += 4;
    return value;
  },
  get remaining() {
    return this.bytes.length - this.position;
  },
  setPosition(position) {
    const oldPosition = this.position;
    this.assertPosition(position);
    this.position = position;
    return () => this.position = oldPosition;
  },
  _touch() {
    if (this.recursiveReadLimit === Number.POSITIVE_INFINITY)
      return;
    const count = this.getReadCount();
    this.positionReadCount.set(this.position, count + 1);
    if (count > 0)
      this.recursiveReadCount++;
  }
};
var NegativeOffsetError = class extends BaseError2 {
  constructor({ offset }) {
    super(`Offset \`${offset}\` cannot be negative.`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Cursor.NegativeOffsetError"
    });
  }
};
var PositionOutOfBoundsError2 = class extends BaseError2 {
  constructor({ length, position }) {
    super(`Position \`${position}\` is out of bounds (\`0 < position < ${length}\`).`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Cursor.PositionOutOfBoundsError"
    });
  }
};
var RecursiveReadLimitExceededError = class extends BaseError2 {
  constructor({ count, limit }) {
    super(`Recursive read limit of \`${limit}\` exceeded (recursive read count: \`${count}\`).`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "Cursor.RecursiveReadLimitExceededError"
    });
  }
};

// node_modules/@reown/appkit-controllers/node_modules/ox/_esm/core/AbiParameters.js
function encodePacked2(types, values) {
  if (types.length !== values.length)
    throw new LengthMismatchError({
      expectedLength: types.length,
      givenLength: values.length
    });
  const data = [];
  for (let i3 = 0; i3 < types.length; i3++) {
    const type = types[i3];
    const value = values[i3];
    data.push(encodePacked2.encode(type, value));
  }
  return concat2(...data);
}
(function(encodePacked3) {
  function encode4(type, value, isArray = false) {
    if (type === "address") {
      const address = value;
      assert(address);
      return padLeft(address.toLowerCase(), isArray ? 32 : 0);
    }
    if (type === "string")
      return fromString2(value);
    if (type === "bytes")
      return value;
    if (type === "bool")
      return padLeft(fromBoolean(value), isArray ? 32 : 1);
    const intMatch = type.match(integerRegex2);
    if (intMatch) {
      const [_type, baseType, bits = "256"] = intMatch;
      const size4 = Number.parseInt(bits) / 8;
      return fromNumber(value, {
        size: isArray ? 32 : size4,
        signed: baseType === "int"
      });
    }
    const bytesMatch = type.match(bytesRegex2);
    if (bytesMatch) {
      const [_type, size4] = bytesMatch;
      if (Number.parseInt(size4) !== (value.length - 2) / 2)
        throw new BytesSizeMismatchError2({
          expectedSize: Number.parseInt(size4),
          value
        });
      return padRight(value, isArray ? 32 : 0);
    }
    const arrayMatch = type.match(arrayRegex2);
    if (arrayMatch && Array.isArray(value)) {
      const [_type, childType] = arrayMatch;
      const data = [];
      for (let i3 = 0; i3 < value.length; i3++) {
        data.push(encode4(childType, value[i3], true));
      }
      if (data.length === 0)
        return "0x";
      return concat2(...data);
    }
    throw new InvalidTypeError(type);
  }
  encodePacked3.encode = encode4;
})(encodePacked2 || (encodePacked2 = {}));
var BytesSizeMismatchError2 = class extends BaseError2 {
  constructor({ expectedSize, value }) {
    super(`Size of bytes "${value}" (bytes${size3(value)}) does not match expected size (bytes${expectedSize}).`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "AbiParameters.BytesSizeMismatchError"
    });
  }
};
var LengthMismatchError = class extends BaseError2 {
  constructor({ expectedLength, givenLength }) {
    super([
      "ABI encoding parameters/values length mismatch.",
      `Expected length (parameters): ${expectedLength}`,
      `Given length (values): ${givenLength}`
    ].join("\n"));
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "AbiParameters.LengthMismatchError"
    });
  }
};
var InvalidTypeError = class extends BaseError2 {
  constructor(type) {
    super(`Type \`${type}\` is not a valid ABI Type.`);
    Object.defineProperty(this, "name", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: "AbiParameters.InvalidTypeError"
    });
  }
};

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/utils/dist/index.es.js
function Wt(t2) {
  if (!Number.isSafeInteger(t2) || t2 < 0) throw new Error("positive integer expected, got " + t2);
}
function Ui(t2) {
  return t2 instanceof Uint8Array || ArrayBuffer.isView(t2) && t2.constructor.name === "Uint8Array";
}
function Xt(t2, ...e2) {
  if (!Ui(t2)) throw new Error("Uint8Array expected");
  if (e2.length > 0 && !e2.includes(t2.length)) throw new Error("Uint8Array expected of length " + e2 + ", got length=" + t2.length);
}
function Ce(t2) {
  if (typeof t2 != "function" || typeof t2.create != "function") throw new Error("Hash should be wrapped by utils.wrapConstructor");
  Wt(t2.outputLen), Wt(t2.blockLen);
}
function Rt(t2, e2 = true) {
  if (t2.destroyed) throw new Error("Hash instance has been destroyed");
  if (e2 && t2.finished) throw new Error("Hash#digest() has already been called");
}
function Gn(t2, e2) {
  Xt(t2);
  const n2 = e2.outputLen;
  if (t2.length < n2) throw new Error("digestInto() expects output buffer of length at least " + n2);
}
var le = BigInt(2 ** 32 - 1);
var Wn = BigInt(32);
function Ti(t2, e2 = false) {
  return e2 ? { h: Number(t2 & le), l: Number(t2 >> Wn & le) } : { h: Number(t2 >> Wn & le) | 0, l: Number(t2 & le) | 0 };
}
function Ri(t2, e2 = false) {
  let n2 = new Uint32Array(t2.length), r2 = new Uint32Array(t2.length);
  for (let o2 = 0; o2 < t2.length; o2++) {
    const { h: i3, l: s2 } = Ti(t2[o2], e2);
    [n2[o2], r2[o2]] = [i3, s2];
  }
  return [n2, r2];
}
var _i = (t2, e2, n2) => t2 << n2 | e2 >>> 32 - n2;
var $i = (t2, e2, n2) => e2 << n2 | t2 >>> 32 - n2;
var Li = (t2, e2, n2) => e2 << n2 - 32 | t2 >>> 64 - n2;
var ji = (t2, e2, n2) => t2 << n2 - 32 | e2 >>> 64 - n2;
var _t = typeof globalThis == "object" && "crypto" in globalThis ? globalThis.crypto : void 0;
function Ci(t2) {
  return new Uint32Array(t2.buffer, t2.byteOffset, Math.floor(t2.byteLength / 4));
}
function Pe(t2) {
  return new DataView(t2.buffer, t2.byteOffset, t2.byteLength);
}
function ct(t2, e2) {
  return t2 << 32 - e2 | t2 >>> e2;
}
var Xn = new Uint8Array(new Uint32Array([287454020]).buffer)[0] === 68;
function Pi2(t2) {
  return t2 << 24 & 4278190080 | t2 << 8 & 16711680 | t2 >>> 8 & 65280 | t2 >>> 24 & 255;
}
function Jn(t2) {
  for (let e2 = 0; e2 < t2.length; e2++) t2[e2] = Pi2(t2[e2]);
}
function ki(t2) {
  if (typeof t2 != "string") throw new Error("utf8ToBytes expected string, got " + typeof t2);
  return new Uint8Array(new TextEncoder().encode(t2));
}
function $t(t2) {
  return typeof t2 == "string" && (t2 = ki(t2)), Xt(t2), t2;
}
function Vi(...t2) {
  let e2 = 0;
  for (let r2 = 0; r2 < t2.length; r2++) {
    const o2 = t2[r2];
    Xt(o2), e2 += o2.length;
  }
  const n2 = new Uint8Array(e2);
  for (let r2 = 0, o2 = 0; r2 < t2.length; r2++) {
    const i3 = t2[r2];
    n2.set(i3, o2), o2 += i3.length;
  }
  return n2;
}
var ke = class {
  clone() {
    return this._cloneInto();
  }
};
function Qn(t2) {
  const e2 = (r2) => t2().update($t(r2)).digest(), n2 = t2();
  return e2.outputLen = n2.outputLen, e2.blockLen = n2.blockLen, e2.create = () => t2(), e2;
}
function Lt(t2 = 32) {
  if (_t && typeof _t.getRandomValues == "function") return _t.getRandomValues(new Uint8Array(t2));
  if (_t && typeof _t.randomBytes == "function") return _t.randomBytes(t2);
  throw new Error("crypto.getRandomValues must be defined");
}
var tr = [];
var er = [];
var nr = [];
var Mi = BigInt(0);
var Jt = BigInt(1);
var Di = BigInt(2);
var Hi = BigInt(7);
var qi = BigInt(256);
var Ki = BigInt(113);
for (let t2 = 0, e2 = Jt, n2 = 1, r2 = 0; t2 < 24; t2++) {
  [n2, r2] = [r2, (2 * n2 + 3 * r2) % 5], tr.push(2 * (5 * r2 + n2)), er.push((t2 + 1) * (t2 + 2) / 2 % 64);
  let o2 = Mi;
  for (let i3 = 0; i3 < 7; i3++) e2 = (e2 << Jt ^ (e2 >> Hi) * Ki) % qi, e2 & Di && (o2 ^= Jt << (Jt << BigInt(i3)) - Jt);
  nr.push(o2);
}
var [Fi, zi] = Ri(nr, true);
var rr = (t2, e2, n2) => n2 > 32 ? Li(t2, e2, n2) : _i(t2, e2, n2);
var or = (t2, e2, n2) => n2 > 32 ? ji(t2, e2, n2) : $i(t2, e2, n2);
function Zi(t2, e2 = 24) {
  const n2 = new Uint32Array(10);
  for (let r2 = 24 - e2; r2 < 24; r2++) {
    for (let s2 = 0; s2 < 10; s2++) n2[s2] = t2[s2] ^ t2[s2 + 10] ^ t2[s2 + 20] ^ t2[s2 + 30] ^ t2[s2 + 40];
    for (let s2 = 0; s2 < 10; s2 += 2) {
      const c2 = (s2 + 8) % 10, a = (s2 + 2) % 10, u2 = n2[a], l3 = n2[a + 1], f6 = rr(u2, l3, 1) ^ n2[c2], h4 = or(u2, l3, 1) ^ n2[c2 + 1];
      for (let y4 = 0; y4 < 50; y4 += 10) t2[s2 + y4] ^= f6, t2[s2 + y4 + 1] ^= h4;
    }
    let o2 = t2[2], i3 = t2[3];
    for (let s2 = 0; s2 < 24; s2++) {
      const c2 = er[s2], a = rr(o2, i3, c2), u2 = or(o2, i3, c2), l3 = tr[s2];
      o2 = t2[l3], i3 = t2[l3 + 1], t2[l3] = a, t2[l3 + 1] = u2;
    }
    for (let s2 = 0; s2 < 50; s2 += 10) {
      for (let c2 = 0; c2 < 10; c2++) n2[c2] = t2[s2 + c2];
      for (let c2 = 0; c2 < 10; c2++) t2[s2 + c2] ^= ~n2[(c2 + 2) % 10] & n2[(c2 + 4) % 10];
    }
    t2[0] ^= Fi[r2], t2[1] ^= zi[r2];
  }
  n2.fill(0);
}
var En = class _En extends ke {
  constructor(e2, n2, r2, o2 = false, i3 = 24) {
    if (super(), this.blockLen = e2, this.suffix = n2, this.outputLen = r2, this.enableXOF = o2, this.rounds = i3, this.pos = 0, this.posOut = 0, this.finished = false, this.destroyed = false, Wt(r2), 0 >= this.blockLen || this.blockLen >= 200) throw new Error("Sha3 supports only keccak-f1600 function");
    this.state = new Uint8Array(200), this.state32 = Ci(this.state);
  }
  keccak() {
    Xn || Jn(this.state32), Zi(this.state32, this.rounds), Xn || Jn(this.state32), this.posOut = 0, this.pos = 0;
  }
  update(e2) {
    Rt(this);
    const { blockLen: n2, state: r2 } = this;
    e2 = $t(e2);
    const o2 = e2.length;
    for (let i3 = 0; i3 < o2; ) {
      const s2 = Math.min(n2 - this.pos, o2 - i3);
      for (let c2 = 0; c2 < s2; c2++) r2[this.pos++] ^= e2[i3++];
      this.pos === n2 && this.keccak();
    }
    return this;
  }
  finish() {
    if (this.finished) return;
    this.finished = true;
    const { state: e2, suffix: n2, pos: r2, blockLen: o2 } = this;
    e2[r2] ^= n2, (n2 & 128) !== 0 && r2 === o2 - 1 && this.keccak(), e2[o2 - 1] ^= 128, this.keccak();
  }
  writeInto(e2) {
    Rt(this, false), Xt(e2), this.finish();
    const n2 = this.state, { blockLen: r2 } = this;
    for (let o2 = 0, i3 = e2.length; o2 < i3; ) {
      this.posOut >= r2 && this.keccak();
      const s2 = Math.min(r2 - this.posOut, i3 - o2);
      e2.set(n2.subarray(this.posOut, this.posOut + s2), o2), this.posOut += s2, o2 += s2;
    }
    return e2;
  }
  xofInto(e2) {
    if (!this.enableXOF) throw new Error("XOF is not possible for this instance");
    return this.writeInto(e2);
  }
  xof(e2) {
    return Wt(e2), this.xofInto(new Uint8Array(e2));
  }
  digestInto(e2) {
    if (Gn(e2, this), this.finished) throw new Error("digest() was already called");
    return this.writeInto(e2), this.destroy(), e2;
  }
  digest() {
    return this.digestInto(new Uint8Array(this.outputLen));
  }
  destroy() {
    this.destroyed = true, this.state.fill(0);
  }
  _cloneInto(e2) {
    const { blockLen: n2, suffix: r2, outputLen: o2, rounds: i3, enableXOF: s2 } = this;
    return e2 || (e2 = new _En(n2, r2, o2, s2, i3)), e2.state32.set(this.state32), e2.pos = this.pos, e2.posOut = this.posOut, e2.finished = this.finished, e2.rounds = i3, e2.suffix = r2, e2.outputLen = o2, e2.enableXOF = s2, e2.destroyed = this.destroyed, e2;
  }
};
var Yi = (t2, e2, n2) => Qn(() => new En(e2, t2, n2));
var Gi = Yi(1, 136, 256 / 8);
function Fe(t2) {
  if (!Number.isSafeInteger(t2) || t2 < 0) throw new Error("positive integer expected, got " + t2);
}
function Sr(t2) {
  return t2 instanceof Uint8Array || ArrayBuffer.isView(t2) && t2.constructor.name === "Uint8Array";
}
function tt(t2, ...e2) {
  if (!Sr(t2)) throw new Error("Uint8Array expected");
  if (e2.length > 0 && !e2.includes(t2.length)) throw new Error("Uint8Array expected of length " + e2 + ", got length=" + t2.length);
}
function Or(t2, e2 = true) {
  if (t2.destroyed) throw new Error("Hash instance has been destroyed");
  if (e2 && t2.finished) throw new Error("Hash#digest() has already been called");
}
function ps(t2, e2) {
  tt(t2);
  const n2 = e2.outputLen;
  if (t2.length < n2) throw new Error("digestInto() expects output buffer of length at least " + n2);
}
function Ar(t2) {
  if (typeof t2 != "boolean") throw new Error(`boolean expected, not ${t2}`);
}
var mt = (t2) => new Uint32Array(t2.buffer, t2.byteOffset, Math.floor(t2.byteLength / 4));
var gs = (t2) => new DataView(t2.buffer, t2.byteOffset, t2.byteLength);
var ys = new Uint8Array(new Uint32Array([287454020]).buffer)[0] === 68;
if (!ys) throw new Error("Non little-endian hardware is not supported");
function ms(t2) {
  if (typeof t2 != "string") throw new Error("string expected");
  return new Uint8Array(new TextEncoder().encode(t2));
}
function ze(t2) {
  if (typeof t2 == "string") t2 = ms(t2);
  else if (Sr(t2)) t2 = Ze(t2);
  else throw new Error("Uint8Array expected, got " + typeof t2);
  return t2;
}
function ws(t2, e2) {
  if (e2 == null || typeof e2 != "object") throw new Error("options must be defined");
  return Object.assign(t2, e2);
}
function bs(t2, e2) {
  if (t2.length !== e2.length) return false;
  let n2 = 0;
  for (let r2 = 0; r2 < t2.length; r2++) n2 |= t2[r2] ^ e2[r2];
  return n2 === 0;
}
var Es = (t2, e2) => {
  function n2(r2, ...o2) {
    if (tt(r2), t2.nonceLength !== void 0) {
      const l3 = o2[0];
      if (!l3) throw new Error("nonce / iv required");
      t2.varSizeNonce ? tt(l3) : tt(l3, t2.nonceLength);
    }
    const i3 = t2.tagLength;
    i3 && o2[1] !== void 0 && tt(o2[1]);
    const s2 = e2(r2, ...o2), c2 = (l3, f6) => {
      if (f6 !== void 0) {
        if (l3 !== 2) throw new Error("cipher output not supported");
        tt(f6);
      }
    };
    let a = false;
    return { encrypt(l3, f6) {
      if (a) throw new Error("cannot encrypt() twice with same key + nonce");
      return a = true, tt(l3), c2(s2.encrypt.length, f6), s2.encrypt(l3, f6);
    }, decrypt(l3, f6) {
      if (tt(l3), i3 && l3.length < i3) throw new Error("invalid ciphertext length: smaller than tagLength=" + i3);
      return c2(s2.decrypt.length, f6), s2.decrypt(l3, f6);
    } };
  }
  return Object.assign(n2, t2), n2;
};
function Br(t2, e2, n2 = true) {
  if (e2 === void 0) return new Uint8Array(t2);
  if (e2.length !== t2) throw new Error("invalid output length, expected " + t2 + ", got: " + e2.length);
  if (n2 && !vs(e2)) throw new Error("invalid output, must be aligned");
  return e2;
}
function Ir(t2, e2, n2, r2) {
  if (typeof t2.setBigUint64 == "function") return t2.setBigUint64(e2, n2, r2);
  const o2 = BigInt(32), i3 = BigInt(4294967295), s2 = Number(n2 >> o2 & i3), c2 = Number(n2 & i3), a = r2 ? 4 : 0, u2 = r2 ? 0 : 4;
  t2.setUint32(e2 + a, s2, r2), t2.setUint32(e2 + u2, c2, r2);
}
function vs(t2) {
  return t2.byteOffset % 4 === 0;
}
function Ze(t2) {
  return Uint8Array.from(t2);
}
function jt(...t2) {
  for (let e2 = 0; e2 < t2.length; e2++) t2[e2].fill(0);
}
var Nr = (t2) => Uint8Array.from(t2.split("").map((e2) => e2.charCodeAt(0)));
var xs = Nr("expand 16-byte k");
var Ss = Nr("expand 32-byte k");
var Os = mt(xs);
var As = mt(Ss);
function V(t2, e2) {
  return t2 << e2 | t2 >>> 32 - e2;
}
function Ye(t2) {
  return t2.byteOffset % 4 === 0;
}
var ge = 64;
var Bs = 16;
var Ur = 2 ** 32 - 1;
var Tr = new Uint32Array();
function Is(t2, e2, n2, r2, o2, i3, s2, c2) {
  const a = o2.length, u2 = new Uint8Array(ge), l3 = mt(u2), f6 = Ye(o2) && Ye(i3), h4 = f6 ? mt(o2) : Tr, y4 = f6 ? mt(i3) : Tr;
  for (let E2 = 0; E2 < a; s2++) {
    if (t2(e2, n2, r2, l3, s2, c2), s2 >= Ur) throw new Error("arx: counter overflow");
    const p = Math.min(ge, a - E2);
    if (f6 && p === ge) {
      const d2 = E2 / 4;
      if (E2 % 4 !== 0) throw new Error("arx: invalid block position");
      for (let v = 0, m2; v < Bs; v++) m2 = d2 + v, y4[m2] = h4[m2] ^ l3[v];
      E2 += ge;
      continue;
    }
    for (let d2 = 0, v; d2 < p; d2++) v = E2 + d2, i3[v] = o2[v] ^ u2[d2];
    E2 += p;
  }
}
function Ns(t2, e2) {
  const { allowShortKeys: n2, extendNonceFn: r2, counterLength: o2, counterRight: i3, rounds: s2 } = ws({ allowShortKeys: false, counterLength: 8, counterRight: false, rounds: 20 }, e2);
  if (typeof t2 != "function") throw new Error("core must be a function");
  return Fe(o2), Fe(s2), Ar(i3), Ar(n2), (c2, a, u2, l3, f6 = 0) => {
    tt(c2), tt(a), tt(u2);
    const h4 = u2.length;
    if (l3 === void 0 && (l3 = new Uint8Array(h4)), tt(l3), Fe(f6), f6 < 0 || f6 >= Ur) throw new Error("arx: counter overflow");
    if (l3.length < h4) throw new Error(`arx: output (${l3.length}) is shorter than data (${h4})`);
    const y4 = [];
    let E2 = c2.length, p, d2;
    if (E2 === 32) y4.push(p = Ze(c2)), d2 = As;
    else if (E2 === 16 && n2) p = new Uint8Array(32), p.set(c2), p.set(c2, 16), d2 = Os, y4.push(p);
    else throw new Error(`arx: invalid 32-byte key, got length=${E2}`);
    Ye(a) || y4.push(a = Ze(a));
    const v = mt(p);
    if (r2) {
      if (a.length !== 24) throw new Error("arx: extended nonce must be 24 bytes");
      r2(d2, v, mt(a.subarray(0, 16)), v), a = a.subarray(16);
    }
    const m2 = 16 - o2;
    if (m2 !== a.length) throw new Error(`arx: nonce must be ${m2} or 16 bytes`);
    if (m2 !== 12) {
      const N = new Uint8Array(12);
      N.set(a, i3 ? 0 : 12 - a.length), a = N, y4.push(a);
    }
    const O2 = mt(a);
    return Is(t2, d2, v, O2, u2, l3, f6, s2), jt(...y4), l3;
  };
}
var F = (t2, e2) => t2[e2++] & 255 | (t2[e2++] & 255) << 8;
var Us = class {
  constructor(e2) {
    this.blockLen = 16, this.outputLen = 16, this.buffer = new Uint8Array(16), this.r = new Uint16Array(10), this.h = new Uint16Array(10), this.pad = new Uint16Array(8), this.pos = 0, this.finished = false, e2 = ze(e2), tt(e2, 32);
    const n2 = F(e2, 0), r2 = F(e2, 2), o2 = F(e2, 4), i3 = F(e2, 6), s2 = F(e2, 8), c2 = F(e2, 10), a = F(e2, 12), u2 = F(e2, 14);
    this.r[0] = n2 & 8191, this.r[1] = (n2 >>> 13 | r2 << 3) & 8191, this.r[2] = (r2 >>> 10 | o2 << 6) & 7939, this.r[3] = (o2 >>> 7 | i3 << 9) & 8191, this.r[4] = (i3 >>> 4 | s2 << 12) & 255, this.r[5] = s2 >>> 1 & 8190, this.r[6] = (s2 >>> 14 | c2 << 2) & 8191, this.r[7] = (c2 >>> 11 | a << 5) & 8065, this.r[8] = (a >>> 8 | u2 << 8) & 8191, this.r[9] = u2 >>> 5 & 127;
    for (let l3 = 0; l3 < 8; l3++) this.pad[l3] = F(e2, 16 + 2 * l3);
  }
  process(e2, n2, r2 = false) {
    const o2 = r2 ? 0 : 2048, { h: i3, r: s2 } = this, c2 = s2[0], a = s2[1], u2 = s2[2], l3 = s2[3], f6 = s2[4], h4 = s2[5], y4 = s2[6], E2 = s2[7], p = s2[8], d2 = s2[9], v = F(e2, n2 + 0), m2 = F(e2, n2 + 2), O2 = F(e2, n2 + 4), N = F(e2, n2 + 6), $ = F(e2, n2 + 8), B3 = F(e2, n2 + 10), A2 = F(e2, n2 + 12), T = F(e2, n2 + 14);
    let S2 = i3[0] + (v & 8191), L = i3[1] + ((v >>> 13 | m2 << 3) & 8191), U4 = i3[2] + ((m2 >>> 10 | O2 << 6) & 8191), _ = i3[3] + ((O2 >>> 7 | N << 9) & 8191), j = i3[4] + ((N >>> 4 | $ << 12) & 8191), g = i3[5] + ($ >>> 1 & 8191), w = i3[6] + (($ >>> 14 | B3 << 2) & 8191), b = i3[7] + ((B3 >>> 11 | A2 << 5) & 8191), I2 = i3[8] + ((A2 >>> 8 | T << 8) & 8191), R3 = i3[9] + (T >>> 5 | o2), x = 0, C4 = x + S2 * c2 + L * (5 * d2) + U4 * (5 * p) + _ * (5 * E2) + j * (5 * y4);
    x = C4 >>> 13, C4 &= 8191, C4 += g * (5 * h4) + w * (5 * f6) + b * (5 * l3) + I2 * (5 * u2) + R3 * (5 * a), x += C4 >>> 13, C4 &= 8191;
    let P4 = x + S2 * a + L * c2 + U4 * (5 * d2) + _ * (5 * p) + j * (5 * E2);
    x = P4 >>> 13, P4 &= 8191, P4 += g * (5 * y4) + w * (5 * h4) + b * (5 * f6) + I2 * (5 * l3) + R3 * (5 * u2), x += P4 >>> 13, P4 &= 8191;
    let k2 = x + S2 * u2 + L * a + U4 * c2 + _ * (5 * d2) + j * (5 * p);
    x = k2 >>> 13, k2 &= 8191, k2 += g * (5 * E2) + w * (5 * y4) + b * (5 * h4) + I2 * (5 * f6) + R3 * (5 * l3), x += k2 >>> 13, k2 &= 8191;
    let M3 = x + S2 * l3 + L * u2 + U4 * a + _ * c2 + j * (5 * d2);
    x = M3 >>> 13, M3 &= 8191, M3 += g * (5 * p) + w * (5 * E2) + b * (5 * y4) + I2 * (5 * h4) + R3 * (5 * f6), x += M3 >>> 13, M3 &= 8191;
    let D = x + S2 * f6 + L * l3 + U4 * u2 + _ * a + j * c2;
    x = D >>> 13, D &= 8191, D += g * (5 * d2) + w * (5 * p) + b * (5 * E2) + I2 * (5 * y4) + R3 * (5 * h4), x += D >>> 13, D &= 8191;
    let z = x + S2 * h4 + L * f6 + U4 * l3 + _ * u2 + j * a;
    x = z >>> 13, z &= 8191, z += g * c2 + w * (5 * d2) + b * (5 * p) + I2 * (5 * E2) + R3 * (5 * y4), x += z >>> 13, z &= 8191;
    let Z2 = x + S2 * y4 + L * h4 + U4 * f6 + _ * l3 + j * u2;
    x = Z2 >>> 13, Z2 &= 8191, Z2 += g * a + w * c2 + b * (5 * d2) + I2 * (5 * p) + R3 * (5 * E2), x += Z2 >>> 13, Z2 &= 8191;
    let st2 = x + S2 * E2 + L * y4 + U4 * h4 + _ * f6 + j * l3;
    x = st2 >>> 13, st2 &= 8191, st2 += g * u2 + w * a + b * c2 + I2 * (5 * d2) + R3 * (5 * p), x += st2 >>> 13, st2 &= 8191;
    let W = x + S2 * p + L * E2 + U4 * y4 + _ * h4 + j * f6;
    x = W >>> 13, W &= 8191, W += g * l3 + w * u2 + b * a + I2 * c2 + R3 * (5 * d2), x += W >>> 13, W &= 8191;
    let J2 = x + S2 * d2 + L * p + U4 * E2 + _ * y4 + j * h4;
    x = J2 >>> 13, J2 &= 8191, J2 += g * f6 + w * l3 + b * u2 + I2 * a + R3 * c2, x += J2 >>> 13, J2 &= 8191, x = (x << 2) + x | 0, x = x + C4 | 0, C4 = x & 8191, x = x >>> 13, P4 += x, i3[0] = C4, i3[1] = P4, i3[2] = k2, i3[3] = M3, i3[4] = D, i3[5] = z, i3[6] = Z2, i3[7] = st2, i3[8] = W, i3[9] = J2;
  }
  finalize() {
    const { h: e2, pad: n2 } = this, r2 = new Uint16Array(10);
    let o2 = e2[1] >>> 13;
    e2[1] &= 8191;
    for (let c2 = 2; c2 < 10; c2++) e2[c2] += o2, o2 = e2[c2] >>> 13, e2[c2] &= 8191;
    e2[0] += o2 * 5, o2 = e2[0] >>> 13, e2[0] &= 8191, e2[1] += o2, o2 = e2[1] >>> 13, e2[1] &= 8191, e2[2] += o2, r2[0] = e2[0] + 5, o2 = r2[0] >>> 13, r2[0] &= 8191;
    for (let c2 = 1; c2 < 10; c2++) r2[c2] = e2[c2] + o2, o2 = r2[c2] >>> 13, r2[c2] &= 8191;
    r2[9] -= 8192;
    let i3 = (o2 ^ 1) - 1;
    for (let c2 = 0; c2 < 10; c2++) r2[c2] &= i3;
    i3 = ~i3;
    for (let c2 = 0; c2 < 10; c2++) e2[c2] = e2[c2] & i3 | r2[c2];
    e2[0] = (e2[0] | e2[1] << 13) & 65535, e2[1] = (e2[1] >>> 3 | e2[2] << 10) & 65535, e2[2] = (e2[2] >>> 6 | e2[3] << 7) & 65535, e2[3] = (e2[3] >>> 9 | e2[4] << 4) & 65535, e2[4] = (e2[4] >>> 12 | e2[5] << 1 | e2[6] << 14) & 65535, e2[5] = (e2[6] >>> 2 | e2[7] << 11) & 65535, e2[6] = (e2[7] >>> 5 | e2[8] << 8) & 65535, e2[7] = (e2[8] >>> 8 | e2[9] << 5) & 65535;
    let s2 = e2[0] + n2[0];
    e2[0] = s2 & 65535;
    for (let c2 = 1; c2 < 8; c2++) s2 = (e2[c2] + n2[c2] | 0) + (s2 >>> 16) | 0, e2[c2] = s2 & 65535;
    jt(r2);
  }
  update(e2) {
    Or(this);
    const { buffer: n2, blockLen: r2 } = this;
    e2 = ze(e2);
    const o2 = e2.length;
    for (let i3 = 0; i3 < o2; ) {
      const s2 = Math.min(r2 - this.pos, o2 - i3);
      if (s2 === r2) {
        for (; r2 <= o2 - i3; i3 += r2) this.process(e2, i3);
        continue;
      }
      n2.set(e2.subarray(i3, i3 + s2), this.pos), this.pos += s2, i3 += s2, this.pos === r2 && (this.process(n2, 0, false), this.pos = 0);
    }
    return this;
  }
  destroy() {
    jt(this.h, this.r, this.buffer, this.pad);
  }
  digestInto(e2) {
    Or(this), ps(e2, this), this.finished = true;
    const { buffer: n2, h: r2 } = this;
    let { pos: o2 } = this;
    if (o2) {
      for (n2[o2++] = 1; o2 < 16; o2++) n2[o2] = 0;
      this.process(n2, 0, true);
    }
    this.finalize();
    let i3 = 0;
    for (let s2 = 0; s2 < 8; s2++) e2[i3++] = r2[s2] >>> 0, e2[i3++] = r2[s2] >>> 8;
    return e2;
  }
  digest() {
    const { buffer: e2, outputLen: n2 } = this;
    this.digestInto(e2);
    const r2 = e2.slice(0, n2);
    return this.destroy(), r2;
  }
};
function Ts(t2) {
  const e2 = (r2, o2) => t2(o2).update(ze(r2)).digest(), n2 = t2(new Uint8Array(32));
  return e2.outputLen = n2.outputLen, e2.blockLen = n2.blockLen, e2.create = (r2) => t2(r2), e2;
}
var Rs = Ts((t2) => new Us(t2));
function _s(t2, e2, n2, r2, o2, i3 = 20) {
  let s2 = t2[0], c2 = t2[1], a = t2[2], u2 = t2[3], l3 = e2[0], f6 = e2[1], h4 = e2[2], y4 = e2[3], E2 = e2[4], p = e2[5], d2 = e2[6], v = e2[7], m2 = o2, O2 = n2[0], N = n2[1], $ = n2[2], B3 = s2, A2 = c2, T = a, S2 = u2, L = l3, U4 = f6, _ = h4, j = y4, g = E2, w = p, b = d2, I2 = v, R3 = m2, x = O2, C4 = N, P4 = $;
  for (let M3 = 0; M3 < i3; M3 += 2) B3 = B3 + L | 0, R3 = V(R3 ^ B3, 16), g = g + R3 | 0, L = V(L ^ g, 12), B3 = B3 + L | 0, R3 = V(R3 ^ B3, 8), g = g + R3 | 0, L = V(L ^ g, 7), A2 = A2 + U4 | 0, x = V(x ^ A2, 16), w = w + x | 0, U4 = V(U4 ^ w, 12), A2 = A2 + U4 | 0, x = V(x ^ A2, 8), w = w + x | 0, U4 = V(U4 ^ w, 7), T = T + _ | 0, C4 = V(C4 ^ T, 16), b = b + C4 | 0, _ = V(_ ^ b, 12), T = T + _ | 0, C4 = V(C4 ^ T, 8), b = b + C4 | 0, _ = V(_ ^ b, 7), S2 = S2 + j | 0, P4 = V(P4 ^ S2, 16), I2 = I2 + P4 | 0, j = V(j ^ I2, 12), S2 = S2 + j | 0, P4 = V(P4 ^ S2, 8), I2 = I2 + P4 | 0, j = V(j ^ I2, 7), B3 = B3 + U4 | 0, P4 = V(P4 ^ B3, 16), b = b + P4 | 0, U4 = V(U4 ^ b, 12), B3 = B3 + U4 | 0, P4 = V(P4 ^ B3, 8), b = b + P4 | 0, U4 = V(U4 ^ b, 7), A2 = A2 + _ | 0, R3 = V(R3 ^ A2, 16), I2 = I2 + R3 | 0, _ = V(_ ^ I2, 12), A2 = A2 + _ | 0, R3 = V(R3 ^ A2, 8), I2 = I2 + R3 | 0, _ = V(_ ^ I2, 7), T = T + j | 0, x = V(x ^ T, 16), g = g + x | 0, j = V(j ^ g, 12), T = T + j | 0, x = V(x ^ T, 8), g = g + x | 0, j = V(j ^ g, 7), S2 = S2 + L | 0, C4 = V(C4 ^ S2, 16), w = w + C4 | 0, L = V(L ^ w, 12), S2 = S2 + L | 0, C4 = V(C4 ^ S2, 8), w = w + C4 | 0, L = V(L ^ w, 7);
  let k2 = 0;
  r2[k2++] = s2 + B3 | 0, r2[k2++] = c2 + A2 | 0, r2[k2++] = a + T | 0, r2[k2++] = u2 + S2 | 0, r2[k2++] = l3 + L | 0, r2[k2++] = f6 + U4 | 0, r2[k2++] = h4 + _ | 0, r2[k2++] = y4 + j | 0, r2[k2++] = E2 + g | 0, r2[k2++] = p + w | 0, r2[k2++] = d2 + b | 0, r2[k2++] = v + I2 | 0, r2[k2++] = m2 + R3 | 0, r2[k2++] = O2 + x | 0, r2[k2++] = N + C4 | 0, r2[k2++] = $ + P4 | 0;
}
var $s = Ns(_s, { counterRight: false, counterLength: 4, allowShortKeys: false });
var Ls = new Uint8Array(16);
var Rr = (t2, e2) => {
  t2.update(e2);
  const n2 = e2.length % 16;
  n2 && t2.update(Ls.subarray(n2));
};
var js = new Uint8Array(32);
function _r(t2, e2, n2, r2, o2) {
  const i3 = t2(e2, n2, js), s2 = Rs.create(i3);
  o2 && Rr(s2, o2), Rr(s2, r2);
  const c2 = new Uint8Array(16), a = gs(c2);
  Ir(a, 0, BigInt(o2 ? o2.length : 0), true), Ir(a, 8, BigInt(r2.length), true), s2.update(c2);
  const u2 = s2.digest();
  return jt(i3, c2), u2;
}
var Cs = (t2) => (e2, n2, r2) => ({ encrypt(i3, s2) {
  const c2 = i3.length;
  s2 = Br(c2 + 16, s2, false), s2.set(i3);
  const a = s2.subarray(0, -16);
  t2(e2, n2, a, a, 1);
  const u2 = _r(t2, e2, n2, a, r2);
  return s2.set(u2, c2), jt(u2), s2;
}, decrypt(i3, s2) {
  s2 = Br(i3.length - 16, s2, false);
  const c2 = i3.subarray(0, -16), a = i3.subarray(-16), u2 = _r(t2, e2, n2, c2, r2);
  if (!bs(a, u2)) throw new Error("invalid tag");
  return s2.set(i3.subarray(0, -16)), t2(e2, n2, s2, s2, 1), jt(u2), s2;
} });
var $r = Es({ blockSize: 64, nonceLength: 12, tagLength: 16 }, Cs($s));
var Lr = class extends ke {
  constructor(e2, n2) {
    super(), this.finished = false, this.destroyed = false, Ce(e2);
    const r2 = $t(n2);
    if (this.iHash = e2.create(), typeof this.iHash.update != "function") throw new Error("Expected instance of class which extends utils.Hash");
    this.blockLen = this.iHash.blockLen, this.outputLen = this.iHash.outputLen;
    const o2 = this.blockLen, i3 = new Uint8Array(o2);
    i3.set(r2.length > o2 ? e2.create().update(r2).digest() : r2);
    for (let s2 = 0; s2 < i3.length; s2++) i3[s2] ^= 54;
    this.iHash.update(i3), this.oHash = e2.create();
    for (let s2 = 0; s2 < i3.length; s2++) i3[s2] ^= 106;
    this.oHash.update(i3), i3.fill(0);
  }
  update(e2) {
    return Rt(this), this.iHash.update(e2), this;
  }
  digestInto(e2) {
    Rt(this), Xt(e2, this.outputLen), this.finished = true, this.iHash.digestInto(e2), this.oHash.update(e2), this.oHash.digestInto(e2), this.destroy();
  }
  digest() {
    const e2 = new Uint8Array(this.oHash.outputLen);
    return this.digestInto(e2), e2;
  }
  _cloneInto(e2) {
    e2 || (e2 = Object.create(Object.getPrototypeOf(this), {}));
    const { oHash: n2, iHash: r2, finished: o2, destroyed: i3, blockLen: s2, outputLen: c2 } = this;
    return e2 = e2, e2.finished = o2, e2.destroyed = i3, e2.blockLen = s2, e2.outputLen = c2, e2.oHash = n2._cloneInto(e2.oHash), e2.iHash = r2._cloneInto(e2.iHash), e2;
  }
  destroy() {
    this.destroyed = true, this.oHash.destroy(), this.iHash.destroy();
  }
};
var ye = (t2, e2, n2) => new Lr(t2, e2).update(n2).digest();
ye.create = (t2, e2) => new Lr(t2, e2);
var Ge = new Uint8Array([0]);
var jr = new Uint8Array();
function Ms(t2, e2, n2, r2) {
  if (typeof t2.setBigUint64 == "function") return t2.setBigUint64(e2, n2, r2);
  const o2 = BigInt(32), i3 = BigInt(4294967295), s2 = Number(n2 >> o2 & i3), c2 = Number(n2 & i3), a = r2 ? 4 : 0, u2 = r2 ? 0 : 4;
  t2.setUint32(e2 + a, s2, r2), t2.setUint32(e2 + u2, c2, r2);
}
function Ds(t2, e2, n2) {
  return t2 & e2 ^ ~t2 & n2;
}
function Hs(t2, e2, n2) {
  return t2 & e2 ^ t2 & n2 ^ e2 & n2;
}
var qs = class extends ke {
  constructor(e2, n2, r2, o2) {
    super(), this.blockLen = e2, this.outputLen = n2, this.padOffset = r2, this.isLE = o2, this.finished = false, this.length = 0, this.pos = 0, this.destroyed = false, this.buffer = new Uint8Array(e2), this.view = Pe(this.buffer);
  }
  update(e2) {
    Rt(this);
    const { view: n2, buffer: r2, blockLen: o2 } = this;
    e2 = $t(e2);
    const i3 = e2.length;
    for (let s2 = 0; s2 < i3; ) {
      const c2 = Math.min(o2 - this.pos, i3 - s2);
      if (c2 === o2) {
        const a = Pe(e2);
        for (; o2 <= i3 - s2; s2 += o2) this.process(a, s2);
        continue;
      }
      r2.set(e2.subarray(s2, s2 + c2), this.pos), this.pos += c2, s2 += c2, this.pos === o2 && (this.process(n2, 0), this.pos = 0);
    }
    return this.length += e2.length, this.roundClean(), this;
  }
  digestInto(e2) {
    Rt(this), Gn(e2, this), this.finished = true;
    const { buffer: n2, view: r2, blockLen: o2, isLE: i3 } = this;
    let { pos: s2 } = this;
    n2[s2++] = 128, this.buffer.subarray(s2).fill(0), this.padOffset > o2 - s2 && (this.process(r2, 0), s2 = 0);
    for (let f6 = s2; f6 < o2; f6++) n2[f6] = 0;
    Ms(r2, o2 - 8, BigInt(this.length * 8), i3), this.process(r2, 0);
    const c2 = Pe(e2), a = this.outputLen;
    if (a % 4) throw new Error("_sha2: outputLen should be aligned to 32bit");
    const u2 = a / 4, l3 = this.get();
    if (u2 > l3.length) throw new Error("_sha2: outputLen bigger than state");
    for (let f6 = 0; f6 < u2; f6++) c2.setUint32(4 * f6, l3[f6], i3);
  }
  digest() {
    const { buffer: e2, outputLen: n2 } = this;
    this.digestInto(e2);
    const r2 = e2.slice(0, n2);
    return this.destroy(), r2;
  }
  _cloneInto(e2) {
    e2 || (e2 = new this.constructor()), e2.set(...this.get());
    const { blockLen: n2, buffer: r2, length: o2, finished: i3, destroyed: s2, pos: c2 } = this;
    return e2.length = o2, e2.pos = c2, e2.finished = i3, e2.destroyed = s2, o2 % n2 && e2.buffer.set(r2), e2;
  }
};
var Ks = new Uint32Array([1116352408, 1899447441, 3049323471, 3921009573, 961987163, 1508970993, 2453635748, 2870763221, 3624381080, 310598401, 607225278, 1426881987, 1925078388, 2162078206, 2614888103, 3248222580, 3835390401, 4022224774, 264347078, 604807628, 770255983, 1249150122, 1555081692, 1996064986, 2554220882, 2821834349, 2952996808, 3210313671, 3336571891, 3584528711, 113926993, 338241895, 666307205, 773529912, 1294757372, 1396182291, 1695183700, 1986661051, 2177026350, 2456956037, 2730485921, 2820302411, 3259730800, 3345764771, 3516065817, 3600352804, 4094571909, 275423344, 430227734, 506948616, 659060556, 883997877, 958139571, 1322822218, 1537002063, 1747873779, 1955562222, 2024104815, 2227730452, 2361852424, 2428436474, 2756734187, 3204031479, 3329325298]);
var wt = new Uint32Array([1779033703, 3144134277, 1013904242, 2773480762, 1359893119, 2600822924, 528734635, 1541459225]);
var bt = new Uint32Array(64);
var Fs = class extends qs {
  constructor() {
    super(64, 32, 8, false), this.A = wt[0] | 0, this.B = wt[1] | 0, this.C = wt[2] | 0, this.D = wt[3] | 0, this.E = wt[4] | 0, this.F = wt[5] | 0, this.G = wt[6] | 0, this.H = wt[7] | 0;
  }
  get() {
    const { A: e2, B: n2, C: r2, D: o2, E: i3, F: s2, G: c2, H: a } = this;
    return [e2, n2, r2, o2, i3, s2, c2, a];
  }
  set(e2, n2, r2, o2, i3, s2, c2, a) {
    this.A = e2 | 0, this.B = n2 | 0, this.C = r2 | 0, this.D = o2 | 0, this.E = i3 | 0, this.F = s2 | 0, this.G = c2 | 0, this.H = a | 0;
  }
  process(e2, n2) {
    for (let f6 = 0; f6 < 16; f6++, n2 += 4) bt[f6] = e2.getUint32(n2, false);
    for (let f6 = 16; f6 < 64; f6++) {
      const h4 = bt[f6 - 15], y4 = bt[f6 - 2], E2 = ct(h4, 7) ^ ct(h4, 18) ^ h4 >>> 3, p = ct(y4, 17) ^ ct(y4, 19) ^ y4 >>> 10;
      bt[f6] = p + bt[f6 - 7] + E2 + bt[f6 - 16] | 0;
    }
    let { A: r2, B: o2, C: i3, D: s2, E: c2, F: a, G: u2, H: l3 } = this;
    for (let f6 = 0; f6 < 64; f6++) {
      const h4 = ct(c2, 6) ^ ct(c2, 11) ^ ct(c2, 25), y4 = l3 + h4 + Ds(c2, a, u2) + Ks[f6] + bt[f6] | 0, p = (ct(r2, 2) ^ ct(r2, 13) ^ ct(r2, 22)) + Hs(r2, o2, i3) | 0;
      l3 = u2, u2 = a, a = c2, c2 = s2 + y4 | 0, s2 = i3, i3 = o2, o2 = r2, r2 = y4 + p | 0;
    }
    r2 = r2 + this.A | 0, o2 = o2 + this.B | 0, i3 = i3 + this.C | 0, s2 = s2 + this.D | 0, c2 = c2 + this.E | 0, a = a + this.F | 0, u2 = u2 + this.G | 0, l3 = l3 + this.H | 0, this.set(r2, o2, i3, s2, c2, a, u2, l3);
  }
  roundClean() {
    bt.fill(0);
  }
  destroy() {
    this.set(0, 0, 0, 0, 0, 0, 0, 0), this.buffer.fill(0);
  }
};
var Qt = Qn(() => new Fs());
var me = BigInt(0);
var we = BigInt(1);
var zs = BigInt(2);
function St(t2) {
  return t2 instanceof Uint8Array || ArrayBuffer.isView(t2) && t2.constructor.name === "Uint8Array";
}
function te(t2) {
  if (!St(t2)) throw new Error("Uint8Array expected");
}
function Ct(t2, e2) {
  if (typeof e2 != "boolean") throw new Error(t2 + " boolean expected, got " + e2);
}
var Zs = Array.from({ length: 256 }, (t2, e2) => e2.toString(16).padStart(2, "0"));
function Pt(t2) {
  te(t2);
  let e2 = "";
  for (let n2 = 0; n2 < t2.length; n2++) e2 += Zs[t2[n2]];
  return e2;
}
function kt(t2) {
  const e2 = t2.toString(16);
  return e2.length & 1 ? "0" + e2 : e2;
}
function We(t2) {
  if (typeof t2 != "string") throw new Error("hex string expected, got " + typeof t2);
  return t2 === "" ? me : BigInt("0x" + t2);
}
var ut = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };
function Cr(t2) {
  if (t2 >= ut._0 && t2 <= ut._9) return t2 - ut._0;
  if (t2 >= ut.A && t2 <= ut.F) return t2 - (ut.A - 10);
  if (t2 >= ut.a && t2 <= ut.f) return t2 - (ut.a - 10);
}
function Vt(t2) {
  if (typeof t2 != "string") throw new Error("hex string expected, got " + typeof t2);
  const e2 = t2.length, n2 = e2 / 2;
  if (e2 % 2) throw new Error("hex string expected, got unpadded hex of length " + e2);
  const r2 = new Uint8Array(n2);
  for (let o2 = 0, i3 = 0; o2 < n2; o2++, i3 += 2) {
    const s2 = Cr(t2.charCodeAt(i3)), c2 = Cr(t2.charCodeAt(i3 + 1));
    if (s2 === void 0 || c2 === void 0) {
      const a = t2[i3] + t2[i3 + 1];
      throw new Error('hex string expected, got non-hex character "' + a + '" at index ' + i3);
    }
    r2[o2] = s2 * 16 + c2;
  }
  return r2;
}
function Ot(t2) {
  return We(Pt(t2));
}
function ee(t2) {
  return te(t2), We(Pt(Uint8Array.from(t2).reverse()));
}
function Mt(t2, e2) {
  return Vt(t2.toString(16).padStart(e2 * 2, "0"));
}
function be(t2, e2) {
  return Mt(t2, e2).reverse();
}
function Ys(t2) {
  return Vt(kt(t2));
}
function et(t2, e2, n2) {
  let r2;
  if (typeof e2 == "string") try {
    r2 = Vt(e2);
  } catch (i3) {
    throw new Error(t2 + " must be hex string or Uint8Array, cause: " + i3);
  }
  else if (St(e2)) r2 = Uint8Array.from(e2);
  else throw new Error(t2 + " must be hex string or Uint8Array");
  const o2 = r2.length;
  if (typeof n2 == "number" && o2 !== n2) throw new Error(t2 + " of length " + n2 + " expected, got " + o2);
  return r2;
}
function ne(...t2) {
  let e2 = 0;
  for (let r2 = 0; r2 < t2.length; r2++) {
    const o2 = t2[r2];
    te(o2), e2 += o2.length;
  }
  const n2 = new Uint8Array(e2);
  for (let r2 = 0, o2 = 0; r2 < t2.length; r2++) {
    const i3 = t2[r2];
    n2.set(i3, o2), o2 += i3.length;
  }
  return n2;
}
function Gs(t2, e2) {
  if (t2.length !== e2.length) return false;
  let n2 = 0;
  for (let r2 = 0; r2 < t2.length; r2++) n2 |= t2[r2] ^ e2[r2];
  return n2 === 0;
}
function Ws(t2) {
  if (typeof t2 != "string") throw new Error("string expected");
  return new Uint8Array(new TextEncoder().encode(t2));
}
var Xe = (t2) => typeof t2 == "bigint" && me <= t2;
function Ee(t2, e2, n2) {
  return Xe(t2) && Xe(e2) && Xe(n2) && e2 <= t2 && t2 < n2;
}
function ft(t2, e2, n2, r2) {
  if (!Ee(e2, n2, r2)) throw new Error("expected valid " + t2 + ": " + n2 + " <= n < " + r2 + ", got " + e2);
}
function Pr(t2) {
  let e2;
  for (e2 = 0; t2 > me; t2 >>= we, e2 += 1) ;
  return e2;
}
function Xs(t2, e2) {
  return t2 >> BigInt(e2) & we;
}
function Js(t2, e2, n2) {
  return t2 | (n2 ? we : me) << BigInt(e2);
}
var Je = (t2) => (zs << BigInt(t2 - 1)) - we;
var Qe = (t2) => new Uint8Array(t2);
var kr = (t2) => Uint8Array.from(t2);
function Vr(t2, e2, n2) {
  if (typeof t2 != "number" || t2 < 2) throw new Error("hashLen must be a number");
  if (typeof e2 != "number" || e2 < 2) throw new Error("qByteLen must be a number");
  if (typeof n2 != "function") throw new Error("hmacFn must be a function");
  let r2 = Qe(t2), o2 = Qe(t2), i3 = 0;
  const s2 = () => {
    r2.fill(1), o2.fill(0), i3 = 0;
  }, c2 = (...f6) => n2(o2, r2, ...f6), a = (f6 = Qe()) => {
    o2 = c2(kr([0]), f6), r2 = c2(), f6.length !== 0 && (o2 = c2(kr([1]), f6), r2 = c2());
  }, u2 = () => {
    if (i3++ >= 1e3) throw new Error("drbg: tried 1000 values");
    let f6 = 0;
    const h4 = [];
    for (; f6 < e2; ) {
      r2 = c2();
      const y4 = r2.slice();
      h4.push(y4), f6 += r2.length;
    }
    return ne(...h4);
  };
  return (f6, h4) => {
    s2(), a(f6);
    let y4;
    for (; !(y4 = h4(u2())); ) a();
    return s2(), y4;
  };
}
var Qs = { bigint: (t2) => typeof t2 == "bigint", function: (t2) => typeof t2 == "function", boolean: (t2) => typeof t2 == "boolean", string: (t2) => typeof t2 == "string", stringOrUint8Array: (t2) => typeof t2 == "string" || St(t2), isSafeInteger: (t2) => Number.isSafeInteger(t2), array: (t2) => Array.isArray(t2), field: (t2, e2) => e2.Fp.isValid(t2), hash: (t2) => typeof t2 == "function" && Number.isSafeInteger(t2.outputLen) };
function Dt(t2, e2, n2 = {}) {
  const r2 = (o2, i3, s2) => {
    const c2 = Qs[i3];
    if (typeof c2 != "function") throw new Error("invalid validator function");
    const a = t2[o2];
    if (!(s2 && a === void 0) && !c2(a, t2)) throw new Error("param " + String(o2) + " is invalid. Expected " + i3 + ", got " + a);
  };
  for (const [o2, i3] of Object.entries(e2)) r2(o2, i3, false);
  for (const [o2, i3] of Object.entries(n2)) r2(o2, i3, true);
  return t2;
}
var tc = () => {
  throw new Error("not implemented");
};
function tn(t2) {
  const e2 = /* @__PURE__ */ new WeakMap();
  return (n2, ...r2) => {
    const o2 = e2.get(n2);
    if (o2 !== void 0) return o2;
    const i3 = t2(n2, ...r2);
    return e2.set(n2, i3), i3;
  };
}
var ec = Object.freeze({ __proto__: null, isBytes: St, abytes: te, abool: Ct, bytesToHex: Pt, numberToHexUnpadded: kt, hexToNumber: We, hexToBytes: Vt, bytesToNumberBE: Ot, bytesToNumberLE: ee, numberToBytesBE: Mt, numberToBytesLE: be, numberToVarBytesBE: Ys, ensureBytes: et, concatBytes: ne, equalBytes: Gs, utf8ToBytes: Ws, inRange: Ee, aInRange: ft, bitLen: Pr, bitGet: Xs, bitSet: Js, bitMask: Je, createHmacDrbg: Vr, validateObject: Dt, notImplemented: tc, memoized: tn });
var q = BigInt(0);
var H = BigInt(1);
var At = BigInt(2);
var nc = BigInt(3);
var en = BigInt(4);
var Mr = BigInt(5);
var Dr = BigInt(8);
function X(t2, e2) {
  const n2 = t2 % e2;
  return n2 >= q ? n2 : e2 + n2;
}
function Hr(t2, e2, n2) {
  if (e2 < q) throw new Error("invalid exponent, negatives unsupported");
  if (n2 <= q) throw new Error("invalid modulus");
  if (n2 === H) return q;
  let r2 = H;
  for (; e2 > q; ) e2 & H && (r2 = r2 * t2 % n2), t2 = t2 * t2 % n2, e2 >>= H;
  return r2;
}
function it(t2, e2, n2) {
  let r2 = t2;
  for (; e2-- > q; ) r2 *= r2, r2 %= n2;
  return r2;
}
function nn(t2, e2) {
  if (t2 === q) throw new Error("invert: expected non-zero number");
  if (e2 <= q) throw new Error("invert: expected positive modulus, got " + e2);
  let n2 = X(t2, e2), r2 = e2, o2 = q, i3 = H;
  for (; n2 !== q; ) {
    const c2 = r2 / n2, a = r2 % n2, u2 = o2 - i3 * c2;
    r2 = n2, n2 = a, o2 = i3, i3 = u2;
  }
  if (r2 !== H) throw new Error("invert: does not exist");
  return X(o2, e2);
}
function rc(t2) {
  const e2 = (t2 - H) / At;
  let n2, r2, o2;
  for (n2 = t2 - H, r2 = 0; n2 % At === q; n2 /= At, r2++) ;
  for (o2 = At; o2 < t2 && Hr(o2, e2, t2) !== t2 - H; o2++) if (o2 > 1e3) throw new Error("Cannot find square root: likely non-prime P");
  if (r2 === 1) {
    const s2 = (t2 + H) / en;
    return function(a, u2) {
      const l3 = a.pow(u2, s2);
      if (!a.eql(a.sqr(l3), u2)) throw new Error("Cannot find square root");
      return l3;
    };
  }
  const i3 = (n2 + H) / At;
  return function(c2, a) {
    if (c2.pow(a, e2) === c2.neg(c2.ONE)) throw new Error("Cannot find square root");
    let u2 = r2, l3 = c2.pow(c2.mul(c2.ONE, o2), n2), f6 = c2.pow(a, i3), h4 = c2.pow(a, n2);
    for (; !c2.eql(h4, c2.ONE); ) {
      if (c2.eql(h4, c2.ZERO)) return c2.ZERO;
      let y4 = 1;
      for (let p = c2.sqr(h4); y4 < u2 && !c2.eql(p, c2.ONE); y4++) p = c2.sqr(p);
      const E2 = c2.pow(l3, H << BigInt(u2 - y4 - 1));
      l3 = c2.sqr(E2), f6 = c2.mul(f6, E2), h4 = c2.mul(h4, l3), u2 = y4;
    }
    return f6;
  };
}
function oc(t2) {
  if (t2 % en === nc) {
    const e2 = (t2 + H) / en;
    return function(r2, o2) {
      const i3 = r2.pow(o2, e2);
      if (!r2.eql(r2.sqr(i3), o2)) throw new Error("Cannot find square root");
      return i3;
    };
  }
  if (t2 % Dr === Mr) {
    const e2 = (t2 - Mr) / Dr;
    return function(r2, o2) {
      const i3 = r2.mul(o2, At), s2 = r2.pow(i3, e2), c2 = r2.mul(o2, s2), a = r2.mul(r2.mul(c2, At), s2), u2 = r2.mul(c2, r2.sub(a, r2.ONE));
      if (!r2.eql(r2.sqr(u2), o2)) throw new Error("Cannot find square root");
      return u2;
    };
  }
  return rc(t2);
}
var ic = ["create", "isValid", "is0", "neg", "inv", "sqrt", "sqr", "eql", "add", "sub", "mul", "pow", "div", "addN", "subN", "mulN", "sqrN"];
function sc(t2) {
  const e2 = { ORDER: "bigint", MASK: "bigint", BYTES: "isSafeInteger", BITS: "isSafeInteger" }, n2 = ic.reduce((r2, o2) => (r2[o2] = "function", r2), e2);
  return Dt(t2, n2);
}
function cc(t2, e2, n2) {
  if (n2 < q) throw new Error("invalid exponent, negatives unsupported");
  if (n2 === q) return t2.ONE;
  if (n2 === H) return e2;
  let r2 = t2.ONE, o2 = e2;
  for (; n2 > q; ) n2 & H && (r2 = t2.mul(r2, o2)), o2 = t2.sqr(o2), n2 >>= H;
  return r2;
}
function ac(t2, e2) {
  const n2 = new Array(e2.length), r2 = e2.reduce((i3, s2, c2) => t2.is0(s2) ? i3 : (n2[c2] = i3, t2.mul(i3, s2)), t2.ONE), o2 = t2.inv(r2);
  return e2.reduceRight((i3, s2, c2) => t2.is0(s2) ? i3 : (n2[c2] = t2.mul(i3, n2[c2]), t2.mul(i3, s2)), o2), n2;
}
function qr(t2, e2) {
  const n2 = e2 !== void 0 ? e2 : t2.toString(2).length, r2 = Math.ceil(n2 / 8);
  return { nBitLength: n2, nByteLength: r2 };
}
function Kr2(t2, e2, n2 = false, r2 = {}) {
  if (t2 <= q) throw new Error("invalid field: expected ORDER > 0, got " + t2);
  const { nBitLength: o2, nByteLength: i3 } = qr(t2, e2);
  if (i3 > 2048) throw new Error("invalid field: expected ORDER of <= 2048 bytes");
  let s2;
  const c2 = Object.freeze({ ORDER: t2, isLE: n2, BITS: o2, BYTES: i3, MASK: Je(o2), ZERO: q, ONE: H, create: (a) => X(a, t2), isValid: (a) => {
    if (typeof a != "bigint") throw new Error("invalid field element: expected bigint, got " + typeof a);
    return q <= a && a < t2;
  }, is0: (a) => a === q, isOdd: (a) => (a & H) === H, neg: (a) => X(-a, t2), eql: (a, u2) => a === u2, sqr: (a) => X(a * a, t2), add: (a, u2) => X(a + u2, t2), sub: (a, u2) => X(a - u2, t2), mul: (a, u2) => X(a * u2, t2), pow: (a, u2) => cc(c2, a, u2), div: (a, u2) => X(a * nn(u2, t2), t2), sqrN: (a) => a * a, addN: (a, u2) => a + u2, subN: (a, u2) => a - u2, mulN: (a, u2) => a * u2, inv: (a) => nn(a, t2), sqrt: r2.sqrt || ((a) => (s2 || (s2 = oc(t2)), s2(c2, a))), invertBatch: (a) => ac(c2, a), cmov: (a, u2, l3) => l3 ? u2 : a, toBytes: (a) => n2 ? be(a, i3) : Mt(a, i3), fromBytes: (a) => {
    if (a.length !== i3) throw new Error("Field.fromBytes: expected " + i3 + " bytes, got " + a.length);
    return n2 ? ee(a) : Ot(a);
  } });
  return Object.freeze(c2);
}
function Fr(t2) {
  if (typeof t2 != "bigint") throw new Error("field order must be bigint");
  const e2 = t2.toString(2).length;
  return Math.ceil(e2 / 8);
}
function zr(t2) {
  const e2 = Fr(t2);
  return e2 + Math.ceil(e2 / 2);
}
function uc(t2, e2, n2 = false) {
  const r2 = t2.length, o2 = Fr(e2), i3 = zr(e2);
  if (r2 < 16 || r2 < i3 || r2 > 1024) throw new Error("expected " + i3 + "-1024 bytes of input, got " + r2);
  const s2 = n2 ? ee(t2) : Ot(t2), c2 = X(s2, e2 - H) + H;
  return n2 ? be(c2, o2) : Mt(c2, o2);
}
var Zr = BigInt(0);
var ve = BigInt(1);
function rn(t2, e2) {
  const n2 = e2.negate();
  return t2 ? n2 : e2;
}
function Yr(t2, e2) {
  if (!Number.isSafeInteger(t2) || t2 <= 0 || t2 > e2) throw new Error("invalid window size, expected [1.." + e2 + "], got W=" + t2);
}
function on(t2, e2) {
  Yr(t2, e2);
  const n2 = Math.ceil(e2 / t2) + 1, r2 = 2 ** (t2 - 1);
  return { windows: n2, windowSize: r2 };
}
function fc(t2, e2) {
  if (!Array.isArray(t2)) throw new Error("array expected");
  t2.forEach((n2, r2) => {
    if (!(n2 instanceof e2)) throw new Error("invalid point at index " + r2);
  });
}
function lc(t2, e2) {
  if (!Array.isArray(t2)) throw new Error("array of scalars expected");
  t2.forEach((n2, r2) => {
    if (!e2.isValid(n2)) throw new Error("invalid scalar at index " + r2);
  });
}
var sn2 = /* @__PURE__ */ new WeakMap();
var Gr = /* @__PURE__ */ new WeakMap();
function cn(t2) {
  return Gr.get(t2) || 1;
}
function dc(t2, e2) {
  return { constTimeNegate: rn, hasPrecomputes(n2) {
    return cn(n2) !== 1;
  }, unsafeLadder(n2, r2, o2 = t2.ZERO) {
    let i3 = n2;
    for (; r2 > Zr; ) r2 & ve && (o2 = o2.add(i3)), i3 = i3.double(), r2 >>= ve;
    return o2;
  }, precomputeWindow(n2, r2) {
    const { windows: o2, windowSize: i3 } = on(r2, e2), s2 = [];
    let c2 = n2, a = c2;
    for (let u2 = 0; u2 < o2; u2++) {
      a = c2, s2.push(a);
      for (let l3 = 1; l3 < i3; l3++) a = a.add(c2), s2.push(a);
      c2 = a.double();
    }
    return s2;
  }, wNAF(n2, r2, o2) {
    const { windows: i3, windowSize: s2 } = on(n2, e2);
    let c2 = t2.ZERO, a = t2.BASE;
    const u2 = BigInt(2 ** n2 - 1), l3 = 2 ** n2, f6 = BigInt(n2);
    for (let h4 = 0; h4 < i3; h4++) {
      const y4 = h4 * s2;
      let E2 = Number(o2 & u2);
      o2 >>= f6, E2 > s2 && (E2 -= l3, o2 += ve);
      const p = y4, d2 = y4 + Math.abs(E2) - 1, v = h4 % 2 !== 0, m2 = E2 < 0;
      E2 === 0 ? a = a.add(rn(v, r2[p])) : c2 = c2.add(rn(m2, r2[d2]));
    }
    return { p: c2, f: a };
  }, wNAFUnsafe(n2, r2, o2, i3 = t2.ZERO) {
    const { windows: s2, windowSize: c2 } = on(n2, e2), a = BigInt(2 ** n2 - 1), u2 = 2 ** n2, l3 = BigInt(n2);
    for (let f6 = 0; f6 < s2; f6++) {
      const h4 = f6 * c2;
      if (o2 === Zr) break;
      let y4 = Number(o2 & a);
      if (o2 >>= l3, y4 > c2 && (y4 -= u2, o2 += ve), y4 === 0) continue;
      let E2 = r2[h4 + Math.abs(y4) - 1];
      y4 < 0 && (E2 = E2.negate()), i3 = i3.add(E2);
    }
    return i3;
  }, getPrecomputes(n2, r2, o2) {
    let i3 = sn2.get(r2);
    return i3 || (i3 = this.precomputeWindow(r2, n2), n2 !== 1 && sn2.set(r2, o2(i3))), i3;
  }, wNAFCached(n2, r2, o2) {
    const i3 = cn(n2);
    return this.wNAF(i3, this.getPrecomputes(i3, n2, o2), r2);
  }, wNAFCachedUnsafe(n2, r2, o2, i3) {
    const s2 = cn(n2);
    return s2 === 1 ? this.unsafeLadder(n2, r2, i3) : this.wNAFUnsafe(s2, this.getPrecomputes(s2, n2, o2), r2, i3);
  }, setWindowSize(n2, r2) {
    Yr(r2, e2), Gr.set(n2, r2), sn2.delete(n2);
  } };
}
function hc(t2, e2, n2, r2) {
  if (fc(n2, t2), lc(r2, e2), n2.length !== r2.length) throw new Error("arrays of points and scalars must have equal length");
  const o2 = t2.ZERO, i3 = Pr(BigInt(n2.length)), s2 = i3 > 12 ? i3 - 3 : i3 > 4 ? i3 - 2 : i3 ? 2 : 1, c2 = (1 << s2) - 1, a = new Array(c2 + 1).fill(o2), u2 = Math.floor((e2.BITS - 1) / s2) * s2;
  let l3 = o2;
  for (let f6 = u2; f6 >= 0; f6 -= s2) {
    a.fill(o2);
    for (let y4 = 0; y4 < r2.length; y4++) {
      const E2 = r2[y4], p = Number(E2 >> BigInt(f6) & BigInt(c2));
      a[p] = a[p].add(n2[y4]);
    }
    let h4 = o2;
    for (let y4 = a.length - 1, E2 = o2; y4 > 0; y4--) E2 = E2.add(a[y4]), h4 = h4.add(E2);
    if (l3 = l3.add(h4), f6 !== 0) for (let y4 = 0; y4 < s2; y4++) l3 = l3.double();
  }
  return l3;
}
function Wr(t2) {
  return sc(t2.Fp), Dt(t2, { n: "bigint", h: "bigint", Gx: "field", Gy: "field" }, { nBitLength: "isSafeInteger", nByteLength: "isSafeInteger" }), Object.freeze({ ...qr(t2.n, t2.nBitLength), ...t2, p: t2.Fp.ORDER });
}
BigInt(0), BigInt(1), BigInt(2), BigInt(8);
var Ht = BigInt(0);
var an = BigInt(1);
function pc(t2) {
  return Dt(t2, { a: "bigint" }, { montgomeryBits: "isSafeInteger", nByteLength: "isSafeInteger", adjustScalarBytes: "function", domain: "function", powPminus2: "function", Gu: "bigint" }), Object.freeze({ ...t2 });
}
function gc(t2) {
  const e2 = pc(t2), { P: n2 } = e2, r2 = (m2) => X(m2, n2), o2 = e2.montgomeryBits, i3 = Math.ceil(o2 / 8), s2 = e2.nByteLength, c2 = e2.adjustScalarBytes || ((m2) => m2), a = e2.powPminus2 || ((m2) => Hr(m2, n2 - BigInt(2), n2));
  function u2(m2, O2, N) {
    const $ = r2(m2 * (O2 - N));
    return O2 = r2(O2 - $), N = r2(N + $), [O2, N];
  }
  const l3 = (e2.a - BigInt(2)) / BigInt(4);
  function f6(m2, O2) {
    ft("u", m2, Ht, n2), ft("scalar", O2, Ht, n2);
    const N = O2, $ = m2;
    let B3 = an, A2 = Ht, T = m2, S2 = an, L = Ht, U4;
    for (let j = BigInt(o2 - 1); j >= Ht; j--) {
      const g = N >> j & an;
      L ^= g, U4 = u2(L, B3, T), B3 = U4[0], T = U4[1], U4 = u2(L, A2, S2), A2 = U4[0], S2 = U4[1], L = g;
      const w = B3 + A2, b = r2(w * w), I2 = B3 - A2, R3 = r2(I2 * I2), x = b - R3, C4 = T + S2, P4 = T - S2, k2 = r2(P4 * w), M3 = r2(C4 * I2), D = k2 + M3, z = k2 - M3;
      T = r2(D * D), S2 = r2($ * r2(z * z)), B3 = r2(b * R3), A2 = r2(x * (b + r2(l3 * x)));
    }
    U4 = u2(L, B3, T), B3 = U4[0], T = U4[1], U4 = u2(L, A2, S2), A2 = U4[0], S2 = U4[1];
    const _ = a(A2);
    return r2(B3 * _);
  }
  function h4(m2) {
    return be(r2(m2), i3);
  }
  function y4(m2) {
    const O2 = et("u coordinate", m2, i3);
    return s2 === 32 && (O2[31] &= 127), ee(O2);
  }
  function E2(m2) {
    const O2 = et("scalar", m2), N = O2.length;
    if (N !== i3 && N !== s2) {
      let $ = "" + i3 + " or " + s2;
      throw new Error("invalid scalar, expected " + $ + " bytes, got " + N);
    }
    return ee(c2(O2));
  }
  function p(m2, O2) {
    const N = y4(O2), $ = E2(m2), B3 = f6(N, $);
    if (B3 === Ht) throw new Error("invalid private or public key received");
    return h4(B3);
  }
  const d2 = h4(e2.Gu);
  function v(m2) {
    return p(m2, d2);
  }
  return { scalarMult: p, scalarMultBase: v, getSharedSecret: (m2, O2) => p(m2, O2), getPublicKey: (m2) => v(m2), utils: { randomPrivateKey: () => e2.randomBytes(e2.nByteLength) }, GuBytes: d2 };
}
var un = BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949");
BigInt(0);
var yc = BigInt(1);
var Xr = BigInt(2);
var mc = BigInt(3);
var wc = BigInt(5);
BigInt(8);
function bc(t2) {
  const e2 = BigInt(10), n2 = BigInt(20), r2 = BigInt(40), o2 = BigInt(80), i3 = un, c2 = t2 * t2 % i3 * t2 % i3, a = it(c2, Xr, i3) * c2 % i3, u2 = it(a, yc, i3) * t2 % i3, l3 = it(u2, wc, i3) * u2 % i3, f6 = it(l3, e2, i3) * l3 % i3, h4 = it(f6, n2, i3) * f6 % i3, y4 = it(h4, r2, i3) * h4 % i3, E2 = it(y4, o2, i3) * y4 % i3, p = it(E2, o2, i3) * y4 % i3, d2 = it(p, e2, i3) * l3 % i3;
  return { pow_p_5_8: it(d2, Xr, i3) * t2 % i3, b2: c2 };
}
function Ec(t2) {
  return t2[0] &= 248, t2[31] &= 127, t2[31] |= 64, t2;
}
var fn = gc({ P: un, a: BigInt(486662), montgomeryBits: 255, nByteLength: 32, Gu: BigInt(9), powPminus2: (t2) => {
  const e2 = un, { pow_p_5_8: n2, b2: r2 } = bc(t2);
  return X(it(n2, mc, e2) * r2, e2);
}, adjustScalarBytes: Ec, randomBytes: Lt });
function Jr(t2) {
  t2.lowS !== void 0 && Ct("lowS", t2.lowS), t2.prehash !== void 0 && Ct("prehash", t2.prehash);
}
function vc(t2) {
  const e2 = Wr(t2);
  Dt(e2, { a: "field", b: "field" }, { allowedPrivateKeyLengths: "array", wrapPrivateKey: "boolean", isTorsionFree: "function", clearCofactor: "function", allowInfinityPoint: "boolean", fromBytes: "function", toBytes: "function" });
  const { endo: n2, Fp: r2, a: o2 } = e2;
  if (n2) {
    if (!r2.eql(o2, r2.ZERO)) throw new Error("invalid endomorphism, can only be defined for Koblitz curves that have a=0");
    if (typeof n2 != "object" || typeof n2.beta != "bigint" || typeof n2.splitScalar != "function") throw new Error("invalid endomorphism, expected beta: bigint and splitScalar: function");
  }
  return Object.freeze({ ...e2 });
}
var { bytesToNumberBE: xc, hexToBytes: Sc } = ec;
var Oc = class extends Error {
  constructor(e2 = "") {
    super(e2);
  }
};
var lt = { Err: Oc, _tlv: { encode: (t2, e2) => {
  const { Err: n2 } = lt;
  if (t2 < 0 || t2 > 256) throw new n2("tlv.encode: wrong tag");
  if (e2.length & 1) throw new n2("tlv.encode: unpadded data");
  const r2 = e2.length / 2, o2 = kt(r2);
  if (o2.length / 2 & 128) throw new n2("tlv.encode: long form length too big");
  const i3 = r2 > 127 ? kt(o2.length / 2 | 128) : "";
  return kt(t2) + i3 + o2 + e2;
}, decode(t2, e2) {
  const { Err: n2 } = lt;
  let r2 = 0;
  if (t2 < 0 || t2 > 256) throw new n2("tlv.encode: wrong tag");
  if (e2.length < 2 || e2[r2++] !== t2) throw new n2("tlv.decode: wrong tlv");
  const o2 = e2[r2++], i3 = !!(o2 & 128);
  let s2 = 0;
  if (!i3) s2 = o2;
  else {
    const a = o2 & 127;
    if (!a) throw new n2("tlv.decode(long): indefinite length not supported");
    if (a > 4) throw new n2("tlv.decode(long): byte length is too big");
    const u2 = e2.subarray(r2, r2 + a);
    if (u2.length !== a) throw new n2("tlv.decode: length bytes not complete");
    if (u2[0] === 0) throw new n2("tlv.decode(long): zero leftmost byte");
    for (const l3 of u2) s2 = s2 << 8 | l3;
    if (r2 += a, s2 < 128) throw new n2("tlv.decode(long): not minimal encoding");
  }
  const c2 = e2.subarray(r2, r2 + s2);
  if (c2.length !== s2) throw new n2("tlv.decode: wrong value length");
  return { v: c2, l: e2.subarray(r2 + s2) };
} }, _int: { encode(t2) {
  const { Err: e2 } = lt;
  if (t2 < dt) throw new e2("integer: negative integers are not allowed");
  let n2 = kt(t2);
  if (Number.parseInt(n2[0], 16) & 8 && (n2 = "00" + n2), n2.length & 1) throw new e2("unexpected DER parsing assertion: unpadded hex");
  return n2;
}, decode(t2) {
  const { Err: e2 } = lt;
  if (t2[0] & 128) throw new e2("invalid signature integer: negative");
  if (t2[0] === 0 && !(t2[1] & 128)) throw new e2("invalid signature integer: unnecessary leading zero");
  return xc(t2);
} }, toSig(t2) {
  const { Err: e2, _int: n2, _tlv: r2 } = lt, o2 = typeof t2 == "string" ? Sc(t2) : t2;
  te(o2);
  const { v: i3, l: s2 } = r2.decode(48, o2);
  if (s2.length) throw new e2("invalid signature: left bytes after parsing");
  const { v: c2, l: a } = r2.decode(2, i3), { v: u2, l: l3 } = r2.decode(2, a);
  if (l3.length) throw new e2("invalid signature: left bytes after parsing");
  return { r: n2.decode(c2), s: n2.decode(u2) };
}, hexFromSig(t2) {
  const { _tlv: e2, _int: n2 } = lt, r2 = e2.encode(2, n2.encode(t2.r)), o2 = e2.encode(2, n2.encode(t2.s)), i3 = r2 + o2;
  return e2.encode(48, i3);
} };
var dt = BigInt(0);
var K = BigInt(1);
BigInt(2);
var Qr = BigInt(3);
BigInt(4);
function Ac(t2) {
  const e2 = vc(t2), { Fp: n2 } = e2, r2 = Kr2(e2.n, e2.nBitLength), o2 = e2.toBytes || ((p, d2, v) => {
    const m2 = d2.toAffine();
    return ne(Uint8Array.from([4]), n2.toBytes(m2.x), n2.toBytes(m2.y));
  }), i3 = e2.fromBytes || ((p) => {
    const d2 = p.subarray(1), v = n2.fromBytes(d2.subarray(0, n2.BYTES)), m2 = n2.fromBytes(d2.subarray(n2.BYTES, 2 * n2.BYTES));
    return { x: v, y: m2 };
  });
  function s2(p) {
    const { a: d2, b: v } = e2, m2 = n2.sqr(p), O2 = n2.mul(m2, p);
    return n2.add(n2.add(O2, n2.mul(p, d2)), v);
  }
  if (!n2.eql(n2.sqr(e2.Gy), s2(e2.Gx))) throw new Error("bad generator point: equation left != right");
  function c2(p) {
    return Ee(p, K, e2.n);
  }
  function a(p) {
    const { allowedPrivateKeyLengths: d2, nByteLength: v, wrapPrivateKey: m2, n: O2 } = e2;
    if (d2 && typeof p != "bigint") {
      if (St(p) && (p = Pt(p)), typeof p != "string" || !d2.includes(p.length)) throw new Error("invalid private key");
      p = p.padStart(v * 2, "0");
    }
    let N;
    try {
      N = typeof p == "bigint" ? p : Ot(et("private key", p, v));
    } catch {
      throw new Error("invalid private key, expected hex or " + v + " bytes, got " + typeof p);
    }
    return m2 && (N = X(N, O2)), ft("private key", N, K, O2), N;
  }
  function u2(p) {
    if (!(p instanceof h4)) throw new Error("ProjectivePoint expected");
  }
  const l3 = tn((p, d2) => {
    const { px: v, py: m2, pz: O2 } = p;
    if (n2.eql(O2, n2.ONE)) return { x: v, y: m2 };
    const N = p.is0();
    d2 == null && (d2 = N ? n2.ONE : n2.inv(O2));
    const $ = n2.mul(v, d2), B3 = n2.mul(m2, d2), A2 = n2.mul(O2, d2);
    if (N) return { x: n2.ZERO, y: n2.ZERO };
    if (!n2.eql(A2, n2.ONE)) throw new Error("invZ was invalid");
    return { x: $, y: B3 };
  }), f6 = tn((p) => {
    if (p.is0()) {
      if (e2.allowInfinityPoint && !n2.is0(p.py)) return;
      throw new Error("bad point: ZERO");
    }
    const { x: d2, y: v } = p.toAffine();
    if (!n2.isValid(d2) || !n2.isValid(v)) throw new Error("bad point: x or y not FE");
    const m2 = n2.sqr(v), O2 = s2(d2);
    if (!n2.eql(m2, O2)) throw new Error("bad point: equation left != right");
    if (!p.isTorsionFree()) throw new Error("bad point: not in prime-order subgroup");
    return true;
  });
  class h4 {
    constructor(d2, v, m2) {
      if (this.px = d2, this.py = v, this.pz = m2, d2 == null || !n2.isValid(d2)) throw new Error("x required");
      if (v == null || !n2.isValid(v)) throw new Error("y required");
      if (m2 == null || !n2.isValid(m2)) throw new Error("z required");
      Object.freeze(this);
    }
    static fromAffine(d2) {
      const { x: v, y: m2 } = d2 || {};
      if (!d2 || !n2.isValid(v) || !n2.isValid(m2)) throw new Error("invalid affine point");
      if (d2 instanceof h4) throw new Error("projective point not allowed");
      const O2 = (N) => n2.eql(N, n2.ZERO);
      return O2(v) && O2(m2) ? h4.ZERO : new h4(v, m2, n2.ONE);
    }
    get x() {
      return this.toAffine().x;
    }
    get y() {
      return this.toAffine().y;
    }
    static normalizeZ(d2) {
      const v = n2.invertBatch(d2.map((m2) => m2.pz));
      return d2.map((m2, O2) => m2.toAffine(v[O2])).map(h4.fromAffine);
    }
    static fromHex(d2) {
      const v = h4.fromAffine(i3(et("pointHex", d2)));
      return v.assertValidity(), v;
    }
    static fromPrivateKey(d2) {
      return h4.BASE.multiply(a(d2));
    }
    static msm(d2, v) {
      return hc(h4, r2, d2, v);
    }
    _setWindowSize(d2) {
      E2.setWindowSize(this, d2);
    }
    assertValidity() {
      f6(this);
    }
    hasEvenY() {
      const { y: d2 } = this.toAffine();
      if (n2.isOdd) return !n2.isOdd(d2);
      throw new Error("Field doesn't support isOdd");
    }
    equals(d2) {
      u2(d2);
      const { px: v, py: m2, pz: O2 } = this, { px: N, py: $, pz: B3 } = d2, A2 = n2.eql(n2.mul(v, B3), n2.mul(N, O2)), T = n2.eql(n2.mul(m2, B3), n2.mul($, O2));
      return A2 && T;
    }
    negate() {
      return new h4(this.px, n2.neg(this.py), this.pz);
    }
    double() {
      const { a: d2, b: v } = e2, m2 = n2.mul(v, Qr), { px: O2, py: N, pz: $ } = this;
      let B3 = n2.ZERO, A2 = n2.ZERO, T = n2.ZERO, S2 = n2.mul(O2, O2), L = n2.mul(N, N), U4 = n2.mul($, $), _ = n2.mul(O2, N);
      return _ = n2.add(_, _), T = n2.mul(O2, $), T = n2.add(T, T), B3 = n2.mul(d2, T), A2 = n2.mul(m2, U4), A2 = n2.add(B3, A2), B3 = n2.sub(L, A2), A2 = n2.add(L, A2), A2 = n2.mul(B3, A2), B3 = n2.mul(_, B3), T = n2.mul(m2, T), U4 = n2.mul(d2, U4), _ = n2.sub(S2, U4), _ = n2.mul(d2, _), _ = n2.add(_, T), T = n2.add(S2, S2), S2 = n2.add(T, S2), S2 = n2.add(S2, U4), S2 = n2.mul(S2, _), A2 = n2.add(A2, S2), U4 = n2.mul(N, $), U4 = n2.add(U4, U4), S2 = n2.mul(U4, _), B3 = n2.sub(B3, S2), T = n2.mul(U4, L), T = n2.add(T, T), T = n2.add(T, T), new h4(B3, A2, T);
    }
    add(d2) {
      u2(d2);
      const { px: v, py: m2, pz: O2 } = this, { px: N, py: $, pz: B3 } = d2;
      let A2 = n2.ZERO, T = n2.ZERO, S2 = n2.ZERO;
      const L = e2.a, U4 = n2.mul(e2.b, Qr);
      let _ = n2.mul(v, N), j = n2.mul(m2, $), g = n2.mul(O2, B3), w = n2.add(v, m2), b = n2.add(N, $);
      w = n2.mul(w, b), b = n2.add(_, j), w = n2.sub(w, b), b = n2.add(v, O2);
      let I2 = n2.add(N, B3);
      return b = n2.mul(b, I2), I2 = n2.add(_, g), b = n2.sub(b, I2), I2 = n2.add(m2, O2), A2 = n2.add($, B3), I2 = n2.mul(I2, A2), A2 = n2.add(j, g), I2 = n2.sub(I2, A2), S2 = n2.mul(L, b), A2 = n2.mul(U4, g), S2 = n2.add(A2, S2), A2 = n2.sub(j, S2), S2 = n2.add(j, S2), T = n2.mul(A2, S2), j = n2.add(_, _), j = n2.add(j, _), g = n2.mul(L, g), b = n2.mul(U4, b), j = n2.add(j, g), g = n2.sub(_, g), g = n2.mul(L, g), b = n2.add(b, g), _ = n2.mul(j, b), T = n2.add(T, _), _ = n2.mul(I2, b), A2 = n2.mul(w, A2), A2 = n2.sub(A2, _), _ = n2.mul(w, j), S2 = n2.mul(I2, S2), S2 = n2.add(S2, _), new h4(A2, T, S2);
    }
    subtract(d2) {
      return this.add(d2.negate());
    }
    is0() {
      return this.equals(h4.ZERO);
    }
    wNAF(d2) {
      return E2.wNAFCached(this, d2, h4.normalizeZ);
    }
    multiplyUnsafe(d2) {
      const { endo: v, n: m2 } = e2;
      ft("scalar", d2, dt, m2);
      const O2 = h4.ZERO;
      if (d2 === dt) return O2;
      if (this.is0() || d2 === K) return this;
      if (!v || E2.hasPrecomputes(this)) return E2.wNAFCachedUnsafe(this, d2, h4.normalizeZ);
      let { k1neg: N, k1: $, k2neg: B3, k2: A2 } = v.splitScalar(d2), T = O2, S2 = O2, L = this;
      for (; $ > dt || A2 > dt; ) $ & K && (T = T.add(L)), A2 & K && (S2 = S2.add(L)), L = L.double(), $ >>= K, A2 >>= K;
      return N && (T = T.negate()), B3 && (S2 = S2.negate()), S2 = new h4(n2.mul(S2.px, v.beta), S2.py, S2.pz), T.add(S2);
    }
    multiply(d2) {
      const { endo: v, n: m2 } = e2;
      ft("scalar", d2, K, m2);
      let O2, N;
      if (v) {
        const { k1neg: $, k1: B3, k2neg: A2, k2: T } = v.splitScalar(d2);
        let { p: S2, f: L } = this.wNAF(B3), { p: U4, f: _ } = this.wNAF(T);
        S2 = E2.constTimeNegate($, S2), U4 = E2.constTimeNegate(A2, U4), U4 = new h4(n2.mul(U4.px, v.beta), U4.py, U4.pz), O2 = S2.add(U4), N = L.add(_);
      } else {
        const { p: $, f: B3 } = this.wNAF(d2);
        O2 = $, N = B3;
      }
      return h4.normalizeZ([O2, N])[0];
    }
    multiplyAndAddUnsafe(d2, v, m2) {
      const O2 = h4.BASE, N = (B3, A2) => A2 === dt || A2 === K || !B3.equals(O2) ? B3.multiplyUnsafe(A2) : B3.multiply(A2), $ = N(this, v).add(N(d2, m2));
      return $.is0() ? void 0 : $;
    }
    toAffine(d2) {
      return l3(this, d2);
    }
    isTorsionFree() {
      const { h: d2, isTorsionFree: v } = e2;
      if (d2 === K) return true;
      if (v) return v(h4, this);
      throw new Error("isTorsionFree() has not been declared for the elliptic curve");
    }
    clearCofactor() {
      const { h: d2, clearCofactor: v } = e2;
      return d2 === K ? this : v ? v(h4, this) : this.multiplyUnsafe(e2.h);
    }
    toRawBytes(d2 = true) {
      return Ct("isCompressed", d2), this.assertValidity(), o2(h4, this, d2);
    }
    toHex(d2 = true) {
      return Ct("isCompressed", d2), Pt(this.toRawBytes(d2));
    }
  }
  h4.BASE = new h4(e2.Gx, e2.Gy, n2.ONE), h4.ZERO = new h4(n2.ZERO, n2.ONE, n2.ZERO);
  const y4 = e2.nBitLength, E2 = dc(h4, e2.endo ? Math.ceil(y4 / 2) : y4);
  return { CURVE: e2, ProjectivePoint: h4, normPrivateKeyToScalar: a, weierstrassEquation: s2, isWithinCurveOrder: c2 };
}
function Bc(t2) {
  const e2 = Wr(t2);
  return Dt(e2, { hash: "hash", hmac: "function", randomBytes: "function" }, { bits2int: "function", bits2int_modN: "function", lowS: "boolean" }), Object.freeze({ lowS: true, ...e2 });
}
function Ic(t2) {
  const e2 = Bc(t2), { Fp: n2, n: r2 } = e2, o2 = n2.BYTES + 1, i3 = 2 * n2.BYTES + 1;
  function s2(g) {
    return X(g, r2);
  }
  function c2(g) {
    return nn(g, r2);
  }
  const { ProjectivePoint: a, normPrivateKeyToScalar: u2, weierstrassEquation: l3, isWithinCurveOrder: f6 } = Ac({ ...e2, toBytes(g, w, b) {
    const I2 = w.toAffine(), R3 = n2.toBytes(I2.x), x = ne;
    return Ct("isCompressed", b), b ? x(Uint8Array.from([w.hasEvenY() ? 2 : 3]), R3) : x(Uint8Array.from([4]), R3, n2.toBytes(I2.y));
  }, fromBytes(g) {
    const w = g.length, b = g[0], I2 = g.subarray(1);
    if (w === o2 && (b === 2 || b === 3)) {
      const R3 = Ot(I2);
      if (!Ee(R3, K, n2.ORDER)) throw new Error("Point is not on curve");
      const x = l3(R3);
      let C4;
      try {
        C4 = n2.sqrt(x);
      } catch (M3) {
        const D = M3 instanceof Error ? ": " + M3.message : "";
        throw new Error("Point is not on curve" + D);
      }
      const P4 = (C4 & K) === K;
      return (b & 1) === 1 !== P4 && (C4 = n2.neg(C4)), { x: R3, y: C4 };
    } else if (w === i3 && b === 4) {
      const R3 = n2.fromBytes(I2.subarray(0, n2.BYTES)), x = n2.fromBytes(I2.subarray(n2.BYTES, 2 * n2.BYTES));
      return { x: R3, y: x };
    } else {
      const R3 = o2, x = i3;
      throw new Error("invalid Point, expected length of " + R3 + ", or uncompressed " + x + ", got " + w);
    }
  } }), h4 = (g) => Pt(Mt(g, e2.nByteLength));
  function y4(g) {
    const w = r2 >> K;
    return g > w;
  }
  function E2(g) {
    return y4(g) ? s2(-g) : g;
  }
  const p = (g, w, b) => Ot(g.slice(w, b));
  class d2 {
    constructor(w, b, I2) {
      this.r = w, this.s = b, this.recovery = I2, this.assertValidity();
    }
    static fromCompact(w) {
      const b = e2.nByteLength;
      return w = et("compactSignature", w, b * 2), new d2(p(w, 0, b), p(w, b, 2 * b));
    }
    static fromDER(w) {
      const { r: b, s: I2 } = lt.toSig(et("DER", w));
      return new d2(b, I2);
    }
    assertValidity() {
      ft("r", this.r, K, r2), ft("s", this.s, K, r2);
    }
    addRecoveryBit(w) {
      return new d2(this.r, this.s, w);
    }
    recoverPublicKey(w) {
      const { r: b, s: I2, recovery: R3 } = this, x = B3(et("msgHash", w));
      if (R3 == null || ![0, 1, 2, 3].includes(R3)) throw new Error("recovery id invalid");
      const C4 = R3 === 2 || R3 === 3 ? b + e2.n : b;
      if (C4 >= n2.ORDER) throw new Error("recovery id 2 or 3 invalid");
      const P4 = (R3 & 1) === 0 ? "02" : "03", k2 = a.fromHex(P4 + h4(C4)), M3 = c2(C4), D = s2(-x * M3), z = s2(I2 * M3), Z2 = a.BASE.multiplyAndAddUnsafe(k2, D, z);
      if (!Z2) throw new Error("point at infinify");
      return Z2.assertValidity(), Z2;
    }
    hasHighS() {
      return y4(this.s);
    }
    normalizeS() {
      return this.hasHighS() ? new d2(this.r, s2(-this.s), this.recovery) : this;
    }
    toDERRawBytes() {
      return Vt(this.toDERHex());
    }
    toDERHex() {
      return lt.hexFromSig({ r: this.r, s: this.s });
    }
    toCompactRawBytes() {
      return Vt(this.toCompactHex());
    }
    toCompactHex() {
      return h4(this.r) + h4(this.s);
    }
  }
  const v = { isValidPrivateKey(g) {
    try {
      return u2(g), true;
    } catch {
      return false;
    }
  }, normPrivateKeyToScalar: u2, randomPrivateKey: () => {
    const g = zr(e2.n);
    return uc(e2.randomBytes(g), e2.n);
  }, precompute(g = 8, w = a.BASE) {
    return w._setWindowSize(g), w.multiply(BigInt(3)), w;
  } };
  function m2(g, w = true) {
    return a.fromPrivateKey(g).toRawBytes(w);
  }
  function O2(g) {
    const w = St(g), b = typeof g == "string", I2 = (w || b) && g.length;
    return w ? I2 === o2 || I2 === i3 : b ? I2 === 2 * o2 || I2 === 2 * i3 : g instanceof a;
  }
  function N(g, w, b = true) {
    if (O2(g)) throw new Error("first arg must be private key");
    if (!O2(w)) throw new Error("second arg must be public key");
    return a.fromHex(w).multiply(u2(g)).toRawBytes(b);
  }
  const $ = e2.bits2int || function(g) {
    if (g.length > 8192) throw new Error("input is too large");
    const w = Ot(g), b = g.length * 8 - e2.nBitLength;
    return b > 0 ? w >> BigInt(b) : w;
  }, B3 = e2.bits2int_modN || function(g) {
    return s2($(g));
  }, A2 = Je(e2.nBitLength);
  function T(g) {
    return ft("num < 2^" + e2.nBitLength, g, dt, A2), Mt(g, e2.nByteLength);
  }
  function S2(g, w, b = L) {
    if (["recovered", "canonical"].some((W) => W in b)) throw new Error("sign() legacy options not supported");
    const { hash: I2, randomBytes: R3 } = e2;
    let { lowS: x, prehash: C4, extraEntropy: P4 } = b;
    x == null && (x = true), g = et("msgHash", g), Jr(b), C4 && (g = et("prehashed msgHash", I2(g)));
    const k2 = B3(g), M3 = u2(w), D = [T(M3), T(k2)];
    if (P4 != null && P4 !== false) {
      const W = P4 === true ? R3(n2.BYTES) : P4;
      D.push(et("extraEntropy", W));
    }
    const z = ne(...D), Z2 = k2;
    function st2(W) {
      const J2 = $(W);
      if (!f6(J2)) return;
      const Be3 = c2(J2), zt = a.BASE.multiply(J2).toAffine(), vt = s2(zt.x);
      if (vt === dt) return;
      const Zt = s2(Be3 * s2(Z2 + vt * M3));
      if (Zt === dt) return;
      let Ut = (zt.x === vt ? 0 : 2) | Number(zt.y & K), vn2 = Zt;
      return x && y4(Zt) && (vn2 = E2(Zt), Ut ^= 1), new d2(vt, vn2, Ut);
    }
    return { seed: z, k2sig: st2 };
  }
  const L = { lowS: e2.lowS, prehash: false }, U4 = { lowS: e2.lowS, prehash: false };
  function _(g, w, b = L) {
    const { seed: I2, k2sig: R3 } = S2(g, w, b), x = e2;
    return Vr(x.hash.outputLen, x.nByteLength, x.hmac)(I2, R3);
  }
  a.BASE._setWindowSize(8);
  function j(g, w, b, I2 = U4) {
    var _a;
    const R3 = g;
    w = et("msgHash", w), b = et("publicKey", b);
    const { lowS: x, prehash: C4, format: P4 } = I2;
    if (Jr(I2), "strict" in I2) throw new Error("options.strict was renamed to lowS");
    if (P4 !== void 0 && P4 !== "compact" && P4 !== "der") throw new Error("format must be compact or der");
    const k2 = typeof R3 == "string" || St(R3), M3 = !k2 && !P4 && typeof R3 == "object" && R3 !== null && typeof R3.r == "bigint" && typeof R3.s == "bigint";
    if (!k2 && !M3) throw new Error("invalid signature, expected Uint8Array, hex string or Signature instance");
    let D, z;
    try {
      if (M3 && (D = new d2(R3.r, R3.s)), k2) {
        try {
          P4 !== "compact" && (D = d2.fromDER(R3));
        } catch (Ut) {
          if (!(Ut instanceof lt.Err)) throw Ut;
        }
        !D && P4 !== "der" && (D = d2.fromCompact(R3));
      }
      z = a.fromHex(b);
    } catch {
      return false;
    }
    if (!D || x && D.hasHighS()) return false;
    C4 && (w = e2.hash(w));
    const { r: Z2, s: st2 } = D, W = B3(w), J2 = c2(st2), Be3 = s2(W * J2), zt = s2(Z2 * J2), vt = (_a = a.BASE.multiplyAndAddUnsafe(z, Be3, zt)) == null ? void 0 : _a.toAffine();
    return vt ? s2(vt.x) === Z2 : false;
  }
  return { CURVE: e2, getPublicKey: m2, getSharedSecret: N, sign: _, verify: j, ProjectivePoint: a, Signature: d2, utils: v };
}
function Nc(t2) {
  return { hash: t2, hmac: (e2, ...n2) => ye(t2, e2, Vi(...n2)), randomBytes: Lt };
}
function Uc(t2, e2) {
  const n2 = (r2) => Ic({ ...t2, ...Nc(r2) });
  return { ...n2(e2), create: n2 };
}
var to = Kr2(BigInt("0xffffffff00000001000000000000000000000000ffffffffffffffffffffffff"));
var Tc = to.create(BigInt("-3"));
var Rc = BigInt("0x5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b");
var _c = Uc({ a: Tc, b: Rc, Fp: to, n: BigInt("0xffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551"), Gx: BigInt("0x6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296"), Gy: BigInt("0x4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5"), h: BigInt(1), lowS: false }, Qt);

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/core/dist/index.es.js
var import_window_getters2 = __toESM(require_cjs2());
var ze2 = "wc";
var he = "core";
var B = `${ze2}@2:${he}:`;
var Ys2 = import_time2.FIVE_SECONDS * 1e3;
var Ht2 = "https://verify.walletconnect.org";
var ue = Ht2;
var Yt2 = `${ue}/v3`;
function rr2(r2, e2) {
  if (r2.length >= 255) throw new TypeError("Alphabet too long");
  for (var t2 = new Uint8Array(256), i3 = 0; i3 < t2.length; i3++) t2[i3] = 255;
  for (var s2 = 0; s2 < r2.length; s2++) {
    var n2 = r2.charAt(s2), o2 = n2.charCodeAt(0);
    if (t2[o2] !== 255) throw new TypeError(n2 + " is ambiguous");
    t2[o2] = s2;
  }
  var a = r2.length, c2 = r2.charAt(0), h4 = Math.log(a) / Math.log(256), l3 = Math.log(256) / Math.log(a);
  function d2(u2) {
    if (u2 instanceof Uint8Array || (ArrayBuffer.isView(u2) ? u2 = new Uint8Array(u2.buffer, u2.byteOffset, u2.byteLength) : Array.isArray(u2) && (u2 = Uint8Array.from(u2))), !(u2 instanceof Uint8Array)) throw new TypeError("Expected Uint8Array");
    if (u2.length === 0) return "";
    for (var b = 0, x = 0, I2 = 0, D = u2.length; I2 !== D && u2[I2] === 0; ) I2++, b++;
    for (var j = (D - I2) * l3 + 1 >>> 0, T = new Uint8Array(j); I2 !== D; ) {
      for (var q2 = u2[I2], J2 = 0, K2 = j - 1; (q2 !== 0 || J2 < x) && K2 !== -1; K2--, J2++) q2 += 256 * T[K2] >>> 0, T[K2] = q2 % a >>> 0, q2 = q2 / a >>> 0;
      if (q2 !== 0) throw new Error("Non-zero carry");
      x = J2, I2++;
    }
    for (var H2 = j - x; H2 !== j && T[H2] === 0; ) H2++;
    for (var me2 = c2.repeat(b); H2 < j; ++H2) me2 += r2.charAt(T[H2]);
    return me2;
  }
  function g(u2) {
    if (typeof u2 != "string") throw new TypeError("Expected String");
    if (u2.length === 0) return new Uint8Array();
    var b = 0;
    if (u2[b] !== " ") {
      for (var x = 0, I2 = 0; u2[b] === c2; ) x++, b++;
      for (var D = (u2.length - b) * h4 + 1 >>> 0, j = new Uint8Array(D); u2[b]; ) {
        var T = t2[u2.charCodeAt(b)];
        if (T === 255) return;
        for (var q2 = 0, J2 = D - 1; (T !== 0 || q2 < I2) && J2 !== -1; J2--, q2++) T += a * j[J2] >>> 0, j[J2] = T % 256 >>> 0, T = T / 256 >>> 0;
        if (T !== 0) throw new Error("Non-zero carry");
        I2 = q2, b++;
      }
      if (u2[b] !== " ") {
        for (var K2 = D - I2; K2 !== D && j[K2] === 0; ) K2++;
        for (var H2 = new Uint8Array(x + (D - K2)), me2 = x; K2 !== D; ) H2[me2++] = j[K2++];
        return H2;
      }
    }
  }
  function _(u2) {
    var b = g(u2);
    if (b) return b;
    throw new Error(`Non-${e2} character`);
  }
  return { encode: d2, decodeUnsafe: g, decode: _ };
}
var nr2 = rr2;
var or2 = nr2;
var si2 = (r2) => {
  if (r2 instanceof Uint8Array && r2.constructor.name === "Uint8Array") return r2;
  if (r2 instanceof ArrayBuffer) return new Uint8Array(r2);
  if (ArrayBuffer.isView(r2)) return new Uint8Array(r2.buffer, r2.byteOffset, r2.byteLength);
  throw new Error("Unknown type, must be binary type");
};
var ar = (r2) => new TextEncoder().encode(r2);
var cr = (r2) => new TextDecoder().decode(r2);
var hr = class {
  constructor(e2, t2, i3) {
    this.name = e2, this.prefix = t2, this.baseEncode = i3;
  }
  encode(e2) {
    if (e2 instanceof Uint8Array) return `${this.prefix}${this.baseEncode(e2)}`;
    throw Error("Unknown type, must be binary type");
  }
};
var lr = class {
  constructor(e2, t2, i3) {
    if (this.name = e2, this.prefix = t2, t2.codePointAt(0) === void 0) throw new Error("Invalid prefix character");
    this.prefixCodePoint = t2.codePointAt(0), this.baseDecode = i3;
  }
  decode(e2) {
    if (typeof e2 == "string") {
      if (e2.codePointAt(0) !== this.prefixCodePoint) throw Error(`Unable to decode multibase string ${JSON.stringify(e2)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);
      return this.baseDecode(e2.slice(this.prefix.length));
    } else throw Error("Can only multibase decode strings");
  }
  or(e2) {
    return ri2(this, e2);
  }
};
var ur = class {
  constructor(e2) {
    this.decoders = e2;
  }
  or(e2) {
    return ri2(this, e2);
  }
  decode(e2) {
    const t2 = e2[0], i3 = this.decoders[t2];
    if (i3) return i3.decode(e2);
    throw RangeError(`Unable to decode multibase string ${JSON.stringify(e2)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`);
  }
};
var ri2 = (r2, e2) => new ur({ ...r2.decoders || { [r2.prefix]: r2 }, ...e2.decoders || { [e2.prefix]: e2 } });
var dr = class {
  constructor(e2, t2, i3, s2) {
    this.name = e2, this.prefix = t2, this.baseEncode = i3, this.baseDecode = s2, this.encoder = new hr(e2, t2, i3), this.decoder = new lr(e2, t2, s2);
  }
  encode(e2) {
    return this.encoder.encode(e2);
  }
  decode(e2) {
    return this.decoder.decode(e2);
  }
};
var Ee2 = ({ name: r2, prefix: e2, encode: t2, decode: i3 }) => new dr(r2, e2, t2, i3);
var de = ({ prefix: r2, name: e2, alphabet: t2 }) => {
  const { encode: i3, decode: s2 } = or2(t2, e2);
  return Ee2({ prefix: r2, name: e2, encode: i3, decode: (n2) => si2(s2(n2)) });
};
var gr = (r2, e2, t2, i3) => {
  const s2 = {};
  for (let l3 = 0; l3 < e2.length; ++l3) s2[e2[l3]] = l3;
  let n2 = r2.length;
  for (; r2[n2 - 1] === "="; ) --n2;
  const o2 = new Uint8Array(n2 * t2 / 8 | 0);
  let a = 0, c2 = 0, h4 = 0;
  for (let l3 = 0; l3 < n2; ++l3) {
    const d2 = s2[r2[l3]];
    if (d2 === void 0) throw new SyntaxError(`Non-${i3} character`);
    c2 = c2 << t2 | d2, a += t2, a >= 8 && (a -= 8, o2[h4++] = 255 & c2 >> a);
  }
  if (a >= t2 || 255 & c2 << 8 - a) throw new SyntaxError("Unexpected end of data");
  return o2;
};
var pr = (r2, e2, t2) => {
  const i3 = e2[e2.length - 1] === "=", s2 = (1 << t2) - 1;
  let n2 = "", o2 = 0, a = 0;
  for (let c2 = 0; c2 < r2.length; ++c2) for (a = a << 8 | r2[c2], o2 += 8; o2 > t2; ) o2 -= t2, n2 += e2[s2 & a >> o2];
  if (o2 && (n2 += e2[s2 & a << t2 - o2]), i3) for (; n2.length * t2 & 7; ) n2 += "=";
  return n2;
};
var P3 = ({ name: r2, prefix: e2, bitsPerChar: t2, alphabet: i3 }) => Ee2({ prefix: e2, name: r2, encode(s2) {
  return pr(s2, i3, t2);
}, decode(s2) {
  return gr(s2, i3, t2, r2);
} });
var yr = Ee2({ prefix: "\0", name: "identity", encode: (r2) => cr(r2), decode: (r2) => ar(r2) });
var br = Object.freeze({ __proto__: null, identity: yr });
var mr = P3({ prefix: "0", name: "base2", alphabet: "01", bitsPerChar: 1 });
var fr = Object.freeze({ __proto__: null, base2: mr });
var Dr2 = P3({ prefix: "7", name: "base8", alphabet: "01234567", bitsPerChar: 3 });
var vr = Object.freeze({ __proto__: null, base8: Dr2 });
var wr = de({ prefix: "9", name: "base10", alphabet: "0123456789" });
var _r2 = Object.freeze({ __proto__: null, base10: wr });
var Er = P3({ prefix: "f", name: "base16", alphabet: "0123456789abcdef", bitsPerChar: 4 });
var Ir2 = P3({ prefix: "F", name: "base16upper", alphabet: "0123456789ABCDEF", bitsPerChar: 4 });
var Tr2 = Object.freeze({ __proto__: null, base16: Er, base16upper: Ir2 });
var Cr2 = P3({ prefix: "b", name: "base32", alphabet: "abcdefghijklmnopqrstuvwxyz234567", bitsPerChar: 5 });
var Pr2 = P3({ prefix: "B", name: "base32upper", alphabet: "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567", bitsPerChar: 5 });
var Sr2 = P3({ prefix: "c", name: "base32pad", alphabet: "abcdefghijklmnopqrstuvwxyz234567=", bitsPerChar: 5 });
var Or2 = P3({ prefix: "C", name: "base32padupper", alphabet: "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=", bitsPerChar: 5 });
var Rr2 = P3({ prefix: "v", name: "base32hex", alphabet: "0123456789abcdefghijklmnopqrstuv", bitsPerChar: 5 });
var Ar2 = P3({ prefix: "V", name: "base32hexupper", alphabet: "0123456789ABCDEFGHIJKLMNOPQRSTUV", bitsPerChar: 5 });
var xr = P3({ prefix: "t", name: "base32hexpad", alphabet: "0123456789abcdefghijklmnopqrstuv=", bitsPerChar: 5 });
var Nr2 = P3({ prefix: "T", name: "base32hexpadupper", alphabet: "0123456789ABCDEFGHIJKLMNOPQRSTUV=", bitsPerChar: 5 });
var $r2 = P3({ prefix: "h", name: "base32z", alphabet: "ybndrfg8ejkmcpqxot1uwisza345h769", bitsPerChar: 5 });
var zr2 = Object.freeze({ __proto__: null, base32: Cr2, base32upper: Pr2, base32pad: Sr2, base32padupper: Or2, base32hex: Rr2, base32hexupper: Ar2, base32hexpad: xr, base32hexpadupper: Nr2, base32z: $r2 });
var Lr2 = de({ prefix: "k", name: "base36", alphabet: "0123456789abcdefghijklmnopqrstuvwxyz" });
var kr2 = de({ prefix: "K", name: "base36upper", alphabet: "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ" });
var jr2 = Object.freeze({ __proto__: null, base36: Lr2, base36upper: kr2 });
var Ur2 = de({ name: "base58btc", prefix: "z", alphabet: "**********************************************************" });
var Fr2 = de({ name: "base58flickr", prefix: "Z", alphabet: "**********************************************************" });
var Mr2 = Object.freeze({ __proto__: null, base58btc: Ur2, base58flickr: Fr2 });
var Kr3 = P3({ prefix: "m", name: "base64", alphabet: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", bitsPerChar: 6 });
var Br2 = P3({ prefix: "M", name: "base64pad", alphabet: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=", bitsPerChar: 6 });
var Vr2 = P3({ prefix: "u", name: "base64url", alphabet: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_", bitsPerChar: 6 });
var qr2 = P3({ prefix: "U", name: "base64urlpad", alphabet: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=", bitsPerChar: 6 });
var Gr2 = Object.freeze({ __proto__: null, base64: Kr3, base64pad: Br2, base64url: Vr2, base64urlpad: qr2 });
var ni2 = Array.from("🚀🪐☄🛰🌌🌑🌒🌓🌔🌕🌖🌗🌘🌍🌏🌎🐉☀💻🖥💾💿😂❤😍🤣😊🙏💕😭😘👍😅👏😁🔥🥰💔💖💙😢🤔😆🙄💪😉☺👌🤗💜😔😎😇🌹🤦🎉💞✌✨🤷😱😌🌸🙌😋💗💚😏💛🙂💓🤩😄😀🖤😃💯🙈👇🎶😒🤭❣😜💋👀😪😑💥🙋😞😩😡🤪👊🥳😥🤤👉💃😳✋😚😝😴🌟😬🙃🍀🌷😻😓⭐✅🥺🌈😈🤘💦✔😣🏃💐☹🎊💘😠☝😕🌺🎂🌻😐🖕💝🙊😹🗣💫💀👑🎵🤞😛🔴😤🌼😫⚽🤙☕🏆🤫👈😮🙆🍻🍃🐶💁😲🌿🧡🎁⚡🌞🎈❌✊👋😰🤨😶🤝🚶💰🍓💢🤟🙁🚨💨🤬✈🎀🍺🤓😙💟🌱😖👶🥴▶➡❓💎💸⬇😨🌚🦋😷🕺⚠🙅😟😵👎🤲🤠🤧📌🔵💅🧐🐾🍒😗🤑🌊🤯🐷☎💧😯💆👆🎤🙇🍑❄🌴💣🐸💌📍🥀🤢👅💡💩👐📸👻🤐🤮🎼🥵🚩🍎🍊👼💍📣🥂");
var Wr2 = ni2.reduce((r2, e2, t2) => (r2[t2] = e2, r2), []);
var Hr2 = ni2.reduce((r2, e2, t2) => (r2[e2.codePointAt(0)] = t2, r2), []);
function Yr2(r2) {
  return r2.reduce((e2, t2) => (e2 += Wr2[t2], e2), "");
}
function Jr2(r2) {
  const e2 = [];
  for (const t2 of r2) {
    const i3 = Hr2[t2.codePointAt(0)];
    if (i3 === void 0) throw new Error(`Non-base256emoji character: ${t2}`);
    e2.push(i3);
  }
  return new Uint8Array(e2);
}
var Xr2 = Ee2({ prefix: "🚀", name: "base256emoji", encode: Yr2, decode: Jr2 });
var Zr2 = Object.freeze({ __proto__: null, base256emoji: Xr2 });
var Qr2 = ai;
var oi = 128;
var en2 = 127;
var tn2 = ~en2;
var sn3 = Math.pow(2, 31);
function ai(r2, e2, t2) {
  e2 = e2 || [], t2 = t2 || 0;
  for (var i3 = t2; r2 >= sn3; ) e2[t2++] = r2 & 255 | oi, r2 /= 128;
  for (; r2 & tn2; ) e2[t2++] = r2 & 255 | oi, r2 >>>= 7;
  return e2[t2] = r2 | 0, ai.bytes = t2 - i3 + 1, e2;
}
var rn2 = Me;
var nn2 = 128;
var ci = 127;
function Me(r2, i3) {
  var t2 = 0, i3 = i3 || 0, s2 = 0, n2 = i3, o2, a = r2.length;
  do {
    if (n2 >= a) throw Me.bytes = 0, new RangeError("Could not decode varint");
    o2 = r2[n2++], t2 += s2 < 28 ? (o2 & ci) << s2 : (o2 & ci) * Math.pow(2, s2), s2 += 7;
  } while (o2 >= nn2);
  return Me.bytes = n2 - i3, t2;
}
var on2 = Math.pow(2, 7);
var an2 = Math.pow(2, 14);
var cn2 = Math.pow(2, 21);
var hn = Math.pow(2, 28);
var ln = Math.pow(2, 35);
var un2 = Math.pow(2, 42);
var dn = Math.pow(2, 49);
var gn = Math.pow(2, 56);
var pn = Math.pow(2, 63);
var yn = function(r2) {
  return r2 < on2 ? 1 : r2 < an2 ? 2 : r2 < cn2 ? 3 : r2 < hn ? 4 : r2 < ln ? 5 : r2 < un2 ? 6 : r2 < dn ? 7 : r2 < gn ? 8 : r2 < pn ? 9 : 10;
};
var bn = { encode: Qr2, decode: rn2, encodingLength: yn };
var hi = bn;
var li2 = (r2, e2, t2 = 0) => (hi.encode(r2, e2, t2), e2);
var ui = (r2) => hi.encodingLength(r2);
var Ke = (r2, e2) => {
  const t2 = e2.byteLength, i3 = ui(r2), s2 = i3 + ui(t2), n2 = new Uint8Array(s2 + t2);
  return li2(r2, n2, 0), li2(t2, n2, i3), n2.set(e2, s2), new mn(r2, t2, e2, n2);
};
var mn = class {
  constructor(e2, t2, i3, s2) {
    this.code = e2, this.size = t2, this.digest = i3, this.bytes = s2;
  }
};
var di = ({ name: r2, code: e2, encode: t2 }) => new fn2(r2, e2, t2);
var fn2 = class {
  constructor(e2, t2, i3) {
    this.name = e2, this.code = t2, this.encode = i3;
  }
  digest(e2) {
    if (e2 instanceof Uint8Array) {
      const t2 = this.encode(e2);
      return t2 instanceof Uint8Array ? Ke(this.code, t2) : t2.then((i3) => Ke(this.code, i3));
    } else throw Error("Unknown type, must be binary type");
  }
};
var gi2 = (r2) => async (e2) => new Uint8Array(await crypto.subtle.digest(r2, e2));
var Dn = di({ name: "sha2-256", code: 18, encode: gi2("SHA-256") });
var vn = di({ name: "sha2-512", code: 19, encode: gi2("SHA-512") });
var wn = Object.freeze({ __proto__: null, sha256: Dn, sha512: vn });
var pi = 0;
var _n = "identity";
var yi2 = si2;
var En2 = (r2) => Ke(pi, yi2(r2));
var In = { code: pi, name: _n, encode: yi2, digest: En2 };
var Tn = Object.freeze({ __proto__: null, identity: In });
new TextEncoder(), new TextDecoder();
var bi2 = { ...br, ...fr, ...vr, ..._r2, ...Tr2, ...zr2, ...jr2, ...Mr2, ...Gr2, ...Zr2 };
({ ...wn, ...Tn });
function Cn(r2 = 0) {
  return globalThis.Buffer != null && globalThis.Buffer.allocUnsafe != null ? globalThis.Buffer.allocUnsafe(r2) : new Uint8Array(r2);
}
function mi2(r2, e2, t2, i3) {
  return { name: r2, prefix: e2, encoder: { name: r2, prefix: e2, encode: t2 }, decoder: { decode: i3 } };
}
var fi2 = mi2("utf8", "u", (r2) => "u" + new TextDecoder("utf8").decode(r2), (r2) => new TextEncoder().encode(r2.substring(1)));
var Be = mi2("ascii", "a", (r2) => {
  let e2 = "a";
  for (let t2 = 0; t2 < r2.length; t2++) e2 += String.fromCharCode(r2[t2]);
  return e2;
}, (r2) => {
  r2 = r2.substring(1);
  const e2 = Cn(r2.length);
  for (let t2 = 0; t2 < r2.length; t2++) e2[t2] = r2.charCodeAt(t2);
  return e2;
});
var Pn2 = { utf8: fi2, "utf-8": fi2, hex: bi2.base16, latin1: Be, ascii: Be, binary: Be, ...bi2 };

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/sign-client/dist/index.es.js
var import_time3 = __toESM(require_cjs());
var import_events4 = __toESM(require_events());
var De2 = "wc";
var Le = 2;
var ke2 = "client";
var we2 = `${De2}@${Le}:${ke2}:`;
var yt = "wc";
var wt2 = "auth";
var ae = `${yt}@${1.5}:${wt2}:`;
var ce = `${ae}:PUB_KEY`;

// node_modules/@reown/appkit-controllers/node_modules/@walletconnect/universal-provider/dist/index.es.js
var import_events5 = __toESM(require_events());
var Dt2 = "wc";
var qt2 = "universal_provider";
var U3 = `${Dt2}@2:${qt2}:`;
var st = "https://rpc.walletconnect.org/v1/";
var jt2 = `${st}bundler`;

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/SIWXUtil.js
var SIWXUtil = {
  getSIWX() {
    return OptionsController.state.siwx;
  },
  async initializeIfEnabled() {
    var _a;
    const siwx = OptionsController.state.siwx;
    const caipAddress = ChainController.getActiveCaipAddress();
    if (!(siwx && caipAddress)) {
      return;
    }
    const [namespace, chainId, address] = caipAddress.split(":");
    if (!ChainController.checkIfSupportedNetwork(namespace)) {
      return;
    }
    try {
      const sessions = await siwx.getSessions(`${namespace}:${chainId}`, address);
      if (sessions.length) {
        return;
      }
      await ModalController.open({
        view: "SIWXSignMessage"
      });
    } catch (error) {
      console.error("SIWXUtil:initializeIfEnabled", error);
      EventsController.sendEvent({
        type: "track",
        event: "SIWX_AUTH_ERROR",
        properties: this.getSIWXEventProperties()
      });
      await ((_a = ConnectionController._getClient()) == null ? void 0 : _a.disconnect().catch(console.error));
      RouterController.reset("Connect");
      SnackController.showError("A problem occurred while trying initialize authentication");
    }
  },
  async requestSignMessage() {
    const siwx = OptionsController.state.siwx;
    const address = CoreHelperUtil.getPlainAddress(ChainController.getActiveCaipAddress());
    const network = ChainController.getActiveCaipNetwork();
    const client = ConnectionController._getClient();
    if (!siwx) {
      throw new Error("SIWX is not enabled");
    }
    if (!address) {
      throw new Error("No ActiveCaipAddress found");
    }
    if (!network) {
      throw new Error("No ActiveCaipNetwork or client found");
    }
    if (!client) {
      throw new Error("No ConnectionController client found");
    }
    try {
      const siwxMessage = await siwx.createMessage({
        chainId: network.caipNetworkId,
        accountAddress: address
      });
      const message = siwxMessage.toString();
      const connectorId = ConnectorController.getConnectorId(network.chainNamespace);
      if (connectorId === ConstantsUtil.CONNECTOR_ID.AUTH) {
        RouterController.pushTransactionStack({});
      }
      const signature = await client.signMessage(message);
      await siwx.addSession({
        data: siwxMessage,
        message,
        signature
      });
      ModalController.close();
      EventsController.sendEvent({
        type: "track",
        event: "SIWX_AUTH_SUCCESS",
        properties: this.getSIWXEventProperties()
      });
    } catch (error) {
      const properties = this.getSIWXEventProperties();
      if (!ModalController.state.open || RouterController.state.view === "ApproveTransaction") {
        await ModalController.open({
          view: "SIWXSignMessage"
        });
      }
      if (properties.isSmartAccount) {
        SnackController.showError("This application might not support Smart Accounts");
      } else {
        SnackController.showError("Signature declined");
      }
      EventsController.sendEvent({
        type: "track",
        event: "SIWX_AUTH_ERROR",
        properties
      });
      console.error("SWIXUtil:requestSignMessage", error);
    }
  },
  async cancelSignMessage() {
    var _a;
    try {
      const siwx = this.getSIWX();
      const isRequired = (_a = siwx == null ? void 0 : siwx.getRequired) == null ? void 0 : _a.call(siwx);
      if (isRequired) {
        await ConnectionController.disconnect();
      } else {
        ModalController.close();
      }
      RouterController.reset("Connect");
      EventsController.sendEvent({
        event: "CLICK_CANCEL_SIWX",
        type: "track",
        properties: this.getSIWXEventProperties()
      });
    } catch (error) {
      console.error("SIWXUtil:cancelSignMessage", error);
    }
  },
  async getSessions() {
    const siwx = OptionsController.state.siwx;
    const address = CoreHelperUtil.getPlainAddress(ChainController.getActiveCaipAddress());
    const network = ChainController.getActiveCaipNetwork();
    if (!(siwx && address && network)) {
      return [];
    }
    return siwx.getSessions(network.caipNetworkId, address);
  },
  async isSIWXCloseDisabled() {
    var _a;
    const siwx = this.getSIWX();
    if (siwx) {
      const isApproveSignScreen = RouterController.state.view === "ApproveTransaction";
      const isSiwxSignMessage = RouterController.state.view === "SIWXSignMessage";
      if (isApproveSignScreen || isSiwxSignMessage) {
        return ((_a = siwx.getRequired) == null ? void 0 : _a.call(siwx)) && (await this.getSessions()).length === 0;
      }
    }
    return false;
  },
  async universalProviderAuthenticate({ universalProvider, chains, methods }) {
    var _a, _b, _c2;
    const siwx = SIWXUtil.getSIWX();
    const namespaces = new Set(chains.map((chain) => chain.split(":")[0]));
    if (!siwx || namespaces.size !== 1 || !namespaces.has("eip155")) {
      return false;
    }
    const siwxMessage = await siwx.createMessage({
      chainId: ((_a = ChainController.getActiveCaipNetwork()) == null ? void 0 : _a.caipNetworkId) || "",
      accountAddress: ""
    });
    const result = await universalProvider.authenticate({
      nonce: siwxMessage.nonce,
      domain: siwxMessage.domain,
      uri: siwxMessage.uri,
      exp: siwxMessage.expirationTime,
      iat: siwxMessage.issuedAt,
      nbf: siwxMessage.notBefore,
      requestId: siwxMessage.requestId,
      version: siwxMessage.version,
      resources: siwxMessage.resources,
      statement: siwxMessage.statement,
      chainId: siwxMessage.chainId,
      methods,
      // The first chainId is what is used for universal provider to build the message
      chains: [siwxMessage.chainId, ...chains.filter((chain) => chain !== siwxMessage.chainId)]
    });
    SnackController.showLoading("Authenticating...", { autoClose: false });
    AccountController.setConnectedWalletInfo({
      ...result.session.peer.metadata,
      name: result.session.peer.metadata.name,
      icon: (_b = result.session.peer.metadata.icons) == null ? void 0 : _b[0],
      type: "WALLET_CONNECT"
    }, Array.from(namespaces)[0]);
    if ((_c2 = result == null ? void 0 : result.auths) == null ? void 0 : _c2.length) {
      const sessions = result.auths.map((cacao) => {
        const message = universalProvider.client.formatAuthMessage({
          request: cacao.p,
          iss: cacao.p.iss
        });
        return {
          data: {
            ...cacao.p,
            accountAddress: cacao.p.iss.split(":").slice(-1).join(""),
            chainId: cacao.p.iss.split(":").slice(2, 4).join(":"),
            uri: cacao.p.aud,
            version: cacao.p.version || siwxMessage.version,
            expirationTime: cacao.p.exp,
            issuedAt: cacao.p.iat,
            notBefore: cacao.p.nbf
          },
          message,
          signature: cacao.s.s,
          cacao
        };
      });
      try {
        await siwx.setSessions(sessions);
        EventsController.sendEvent({
          type: "track",
          event: "SIWX_AUTH_SUCCESS",
          properties: SIWXUtil.getSIWXEventProperties()
        });
      } catch (error) {
        console.error("SIWX:universalProviderAuth - failed to set sessions", error);
        EventsController.sendEvent({
          type: "track",
          event: "SIWX_AUTH_ERROR",
          properties: SIWXUtil.getSIWXEventProperties()
        });
        await universalProvider.disconnect().catch(console.error);
        throw error;
      } finally {
        SnackController.hide();
      }
    }
    return true;
  },
  getSIWXEventProperties() {
    var _a, _b;
    const activeChainNamespace = ChainController.state.activeChain;
    return {
      network: ((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId) || "",
      isSmartAccount: ((_b = AccountController.state.preferredAccountTypes) == null ? void 0 : _b[activeChainNamespace]) === W3mFrameRpcConstants.ACCOUNT_TYPES.SMART_ACCOUNT
    };
  },
  async clearSessions() {
    const siwx = this.getSIWX();
    if (siwx) {
      await siwx.setSessions([]);
    }
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/ModalUtil.js
var ModalUtil = {
  isUnsupportedChainView() {
    return RouterController.state.view === "UnsupportedChain" || RouterController.state.view === "SwitchNetwork" && RouterController.state.history.includes("UnsupportedChain");
  },
  async safeClose() {
    if (this.isUnsupportedChainView()) {
      ModalController.shake();
      return;
    }
    const isSIWXCloseDisabled = await SIWXUtil.isSIWXCloseDisabled();
    if (isSIWXCloseDisabled) {
      ModalController.shake();
      return;
    }
    ModalController.close();
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/OnRampController.js
var USDC_CURRENCY_DEFAULT = {
  id: "2b92315d-eab7-5bef-84fa-089a131333f5",
  name: "USD Coin",
  symbol: "USDC",
  networks: [
    {
      name: "ethereum-mainnet",
      display_name: "Ethereum",
      chain_id: "1",
      contract_address: "******************************************"
    },
    {
      name: "polygon-mainnet",
      display_name: "Polygon",
      chain_id: "137",
      contract_address: "******************************************"
    }
  ]
};
var USD_CURRENCY_DEFAULT = {
  id: "USD",
  payment_method_limits: [
    {
      id: "card",
      min: "10.00",
      max: "7500.00"
    },
    {
      id: "ach_bank_account",
      min: "10.00",
      max: "25000.00"
    }
  ]
};
var defaultState = {
  providers: ONRAMP_PROVIDERS,
  selectedProvider: null,
  error: null,
  purchaseCurrency: USDC_CURRENCY_DEFAULT,
  paymentCurrency: USD_CURRENCY_DEFAULT,
  purchaseCurrencies: [USDC_CURRENCY_DEFAULT],
  paymentCurrencies: [],
  quotesLoading: false
};
var state20 = proxy(defaultState);
var controller13 = {
  state: state20,
  subscribe(callback) {
    return subscribe(state20, () => callback(state20));
  },
  subscribeKey(key, callback) {
    return subscribeKey(state20, key, callback);
  },
  setSelectedProvider(provider) {
    if (provider && provider.name === "meld") {
      const currency = ChainController.state.activeChain === ConstantsUtil.CHAIN.SOLANA ? "SOL" : "USDC";
      const address = AccountController.state.address ?? "";
      const url = new URL(provider.url);
      url.searchParams.append("publicKey", MELD_PUBLIC_KEY);
      url.searchParams.append("destinationCurrencyCode", currency);
      url.searchParams.append("walletAddress", address);
      url.searchParams.append("externalCustomerId", OptionsController.state.projectId);
      state20.selectedProvider = { ...provider, url: url.toString() };
    } else {
      state20.selectedProvider = provider;
    }
  },
  setOnrampProviders(providers) {
    if (Array.isArray(providers) && providers.every((item) => typeof item === "string")) {
      const validOnramp = providers;
      const newProviders = ONRAMP_PROVIDERS.filter((provider) => validOnramp.includes(provider.name));
      state20.providers = newProviders;
    } else {
      state20.providers = [];
    }
  },
  setPurchaseCurrency(currency) {
    state20.purchaseCurrency = currency;
  },
  setPaymentCurrency(currency) {
    state20.paymentCurrency = currency;
  },
  setPurchaseAmount(amount) {
    OnRampController.state.purchaseAmount = amount;
  },
  setPaymentAmount(amount) {
    OnRampController.state.paymentAmount = amount;
  },
  async getAvailableCurrencies() {
    const options = await BlockchainApiController.getOnrampOptions();
    state20.purchaseCurrencies = options.purchaseCurrencies;
    state20.paymentCurrencies = options.paymentCurrencies;
    state20.paymentCurrency = options.paymentCurrencies[0] || USD_CURRENCY_DEFAULT;
    state20.purchaseCurrency = options.purchaseCurrencies[0] || USDC_CURRENCY_DEFAULT;
    await ApiController.fetchCurrencyImages(options.paymentCurrencies.map((currency) => currency.id));
    await ApiController.fetchTokenImages(options.purchaseCurrencies.map((currency) => currency.symbol));
  },
  async getQuote() {
    var _a, _b;
    state20.quotesLoading = true;
    try {
      const quote = await BlockchainApiController.getOnrampQuote({
        purchaseCurrency: state20.purchaseCurrency,
        paymentCurrency: state20.paymentCurrency,
        amount: ((_a = state20.paymentAmount) == null ? void 0 : _a.toString()) || "0",
        network: (_b = state20.purchaseCurrency) == null ? void 0 : _b.symbol
      });
      state20.quotesLoading = false;
      state20.purchaseAmount = Number(quote == null ? void 0 : quote.purchaseAmount.amount);
      return quote;
    } catch (error) {
      state20.error = error.message;
      state20.quotesLoading = false;
      return null;
    } finally {
      state20.quotesLoading = false;
    }
  },
  resetState() {
    state20.selectedProvider = null;
    state20.error = null;
    state20.purchaseCurrency = USDC_CURRENCY_DEFAULT;
    state20.paymentCurrency = USD_CURRENCY_DEFAULT;
    state20.purchaseCurrencies = [USDC_CURRENCY_DEFAULT];
    state20.paymentCurrencies = [];
    state20.paymentAmount = void 0;
    state20.purchaseAmount = void 0;
    state20.quotesLoading = false;
  }
};
var OnRampController = withErrorBoundary(controller13);

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/SwapApiUtil.js
var SwapApiUtil = {
  async getTokenList() {
    var _a;
    const caipNetwork = ChainController.state.activeCaipNetwork;
    const response = await BlockchainApiController.fetchSwapTokens({
      chainId: caipNetwork == null ? void 0 : caipNetwork.caipNetworkId
    });
    const tokens = ((_a = response == null ? void 0 : response.tokens) == null ? void 0 : _a.map((token) => ({
      ...token,
      eip2612: false,
      quantity: {
        decimals: "0",
        numeric: "0"
      },
      price: 0,
      value: 0
    }))) || [];
    return tokens;
  },
  async fetchGasPrice() {
    var _a, _b;
    const caipNetwork = ChainController.state.activeCaipNetwork;
    if (!caipNetwork) {
      return null;
    }
    try {
      switch (caipNetwork.chainNamespace) {
        case "solana":
          const lamportsPerSignature = (_b = await ((_a = ConnectionController) == null ? void 0 : _a.estimateGas({ chainNamespace: "solana" }))) == null ? void 0 : _b.toString();
          return {
            standard: lamportsPerSignature,
            fast: lamportsPerSignature,
            instant: lamportsPerSignature
          };
        case "eip155":
        default:
          return await BlockchainApiController.fetchGasPrice({
            chainId: caipNetwork.caipNetworkId
          });
      }
    } catch {
      return null;
    }
  },
  async fetchSwapAllowance({ tokenAddress, userAddress, sourceTokenAmount, sourceTokenDecimals }) {
    const response = await BlockchainApiController.fetchSwapAllowance({
      tokenAddress,
      userAddress
    });
    if ((response == null ? void 0 : response.allowance) && sourceTokenAmount && sourceTokenDecimals) {
      const parsedValue = ConnectionController.parseUnits(sourceTokenAmount, sourceTokenDecimals) || 0;
      const hasAllowance = BigInt(response.allowance) >= parsedValue;
      return hasAllowance;
    }
    return false;
  },
  async getMyTokensWithBalance(forceUpdate) {
    const address = AccountController.state.address;
    const caipNetwork = ChainController.state.activeCaipNetwork;
    if (!address || !caipNetwork) {
      return [];
    }
    const response = await BlockchainApiController.getBalance(address, caipNetwork.caipNetworkId, forceUpdate);
    const balances = response.balances.filter((balance) => balance.quantity.decimals !== "0");
    AccountController.setTokenBalance(balances, ChainController.state.activeChain);
    return this.mapBalancesToSwapTokens(balances);
  },
  mapBalancesToSwapTokens(balances) {
    return (balances == null ? void 0 : balances.map((token) => ({
      ...token,
      address: (token == null ? void 0 : token.address) ? token.address : ChainController.getActiveNetworkTokenAddress(),
      decimals: parseInt(token.quantity.decimals, 10),
      logoUri: token.iconUrl,
      eip2612: false
    }))) || [];
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/SwapCalculationUtil.js
var SwapCalculationUtil = {
  getGasPriceInEther(gas, gasPrice) {
    const totalGasCostInWei = gasPrice * gas;
    const totalGasCostInEther = Number(totalGasCostInWei) / 1e18;
    return totalGasCostInEther;
  },
  getGasPriceInUSD(networkPrice, gas, gasPrice) {
    const totalGasCostInEther = SwapCalculationUtil.getGasPriceInEther(gas, gasPrice);
    const networkPriceInUSD = NumberUtil.bigNumber(networkPrice);
    const gasCostInUSD = networkPriceInUSD.times(totalGasCostInEther);
    return gasCostInUSD.toNumber();
  },
  getPriceImpact({ sourceTokenAmount, sourceTokenPriceInUSD, toTokenPriceInUSD, toTokenAmount }) {
    const inputValue = NumberUtil.bigNumber(sourceTokenAmount).times(sourceTokenPriceInUSD);
    const outputValue = NumberUtil.bigNumber(toTokenAmount).times(toTokenPriceInUSD);
    const priceImpact = inputValue.minus(outputValue).div(inputValue).times(100);
    return priceImpact.toNumber();
  },
  getMaxSlippage(slippage, toTokenAmount) {
    const slippageToleranceDecimal = NumberUtil.bigNumber(slippage).div(100);
    const maxSlippageAmount = NumberUtil.multiply(toTokenAmount, slippageToleranceDecimal);
    return maxSlippageAmount.toNumber();
  },
  getProviderFee(sourceTokenAmount, feePercentage = 85e-4) {
    const providerFee = NumberUtil.bigNumber(sourceTokenAmount).times(feePercentage);
    return providerFee.toString();
  },
  isInsufficientNetworkTokenForGas(networkBalanceInUSD, gasPriceInUSD) {
    const gasPrice = gasPriceInUSD || "0";
    if (NumberUtil.bigNumber(networkBalanceInUSD).eq(0)) {
      return true;
    }
    return NumberUtil.bigNumber(NumberUtil.bigNumber(gasPrice)).gt(networkBalanceInUSD);
  },
  isInsufficientSourceTokenForSwap(sourceTokenAmount, sourceTokenAddress, balance) {
    var _a, _b;
    const sourceTokenBalance = (_b = (_a = balance == null ? void 0 : balance.find((token) => token.address === sourceTokenAddress)) == null ? void 0 : _a.quantity) == null ? void 0 : _b.numeric;
    const isInSufficientBalance = NumberUtil.bigNumber(sourceTokenBalance || "0").lt(sourceTokenAmount);
    return isInSufficientBalance;
  },
  getToTokenAmount({ sourceToken, toToken, sourceTokenPrice, toTokenPrice, sourceTokenAmount }) {
    if (sourceTokenAmount === "0") {
      return "0";
    }
    if (!sourceToken || !toToken) {
      return "0";
    }
    const sourceTokenDecimals = sourceToken.decimals;
    const sourceTokenPriceInUSD = sourceTokenPrice;
    const toTokenDecimals = toToken.decimals;
    const toTokenPriceInUSD = toTokenPrice;
    if (toTokenPriceInUSD <= 0) {
      return "0";
    }
    const providerFee = NumberUtil.bigNumber(sourceTokenAmount).times(85e-4);
    const adjustedSourceTokenAmount = NumberUtil.bigNumber(sourceTokenAmount).minus(providerFee);
    const sourceAmountInSmallestUnit = adjustedSourceTokenAmount.times(NumberUtil.bigNumber(10).pow(sourceTokenDecimals));
    const priceRatio = NumberUtil.bigNumber(sourceTokenPriceInUSD).div(toTokenPriceInUSD);
    const decimalDifference = sourceTokenDecimals - toTokenDecimals;
    const toTokenAmountInSmallestUnit = sourceAmountInSmallestUnit.times(priceRatio).div(NumberUtil.bigNumber(10).pow(decimalDifference));
    const toTokenAmount = toTokenAmountInSmallestUnit.div(NumberUtil.bigNumber(10).pow(toTokenDecimals));
    const amount = toTokenAmount.toFixed(toTokenDecimals).toString();
    return amount;
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/SwapController.js
var INITIAL_GAS_LIMIT = 15e4;
var TO_AMOUNT_DECIMALS = 6;
var initialState = {
  // Loading states
  initializing: false,
  initialized: false,
  loadingPrices: false,
  loadingQuote: false,
  loadingApprovalTransaction: false,
  loadingBuildTransaction: false,
  loadingTransaction: false,
  // Error states
  fetchError: false,
  // Approval & Swap transaction states
  approvalTransaction: void 0,
  swapTransaction: void 0,
  transactionError: void 0,
  // Input values
  sourceToken: void 0,
  sourceTokenAmount: "",
  sourceTokenPriceInUSD: 0,
  toToken: void 0,
  toTokenAmount: "",
  toTokenPriceInUSD: 0,
  networkPrice: "0",
  networkBalanceInUSD: "0",
  networkTokenSymbol: "",
  inputError: void 0,
  // Request values
  slippage: ConstantsUtil2.CONVERT_SLIPPAGE_TOLERANCE,
  // Tokens
  tokens: void 0,
  popularTokens: void 0,
  suggestedTokens: void 0,
  foundTokens: void 0,
  myTokensWithBalance: void 0,
  tokensPriceMap: {},
  // Calculations
  gasFee: "0",
  gasPriceInUSD: 0,
  priceImpact: void 0,
  maxSlippage: void 0,
  providerFee: void 0
};
var state21 = proxy(initialState);
var controller14 = {
  state: state21,
  subscribe(callback) {
    return subscribe(state21, () => callback(state21));
  },
  subscribeKey(key, callback) {
    return subscribeKey(state21, key, callback);
  },
  getParams() {
    var _a, _b, _c2, _d, _e2, _f, _g, _h;
    const caipAddress = ChainController.state.activeCaipAddress;
    const namespace = ChainController.state.activeChain;
    const address = CoreHelperUtil.getPlainAddress(caipAddress);
    const networkAddress = ChainController.getActiveNetworkTokenAddress();
    const connectorId = ConnectorController.getConnectorId(namespace);
    if (!address) {
      throw new Error("No address found to swap the tokens from.");
    }
    const invalidToToken = !((_a = state21.toToken) == null ? void 0 : _a.address) || !((_b = state21.toToken) == null ? void 0 : _b.decimals);
    const invalidSourceToken = !((_c2 = state21.sourceToken) == null ? void 0 : _c2.address) || !((_d = state21.sourceToken) == null ? void 0 : _d.decimals) || !NumberUtil.bigNumber(state21.sourceTokenAmount).gt(0);
    const invalidSourceTokenAmount = !state21.sourceTokenAmount;
    return {
      networkAddress,
      fromAddress: address,
      fromCaipAddress: caipAddress,
      sourceTokenAddress: (_e2 = state21.sourceToken) == null ? void 0 : _e2.address,
      toTokenAddress: (_f = state21.toToken) == null ? void 0 : _f.address,
      toTokenAmount: state21.toTokenAmount,
      toTokenDecimals: (_g = state21.toToken) == null ? void 0 : _g.decimals,
      sourceTokenAmount: state21.sourceTokenAmount,
      sourceTokenDecimals: (_h = state21.sourceToken) == null ? void 0 : _h.decimals,
      invalidToToken,
      invalidSourceToken,
      invalidSourceTokenAmount,
      availableToSwap: caipAddress && !invalidToToken && !invalidSourceToken && !invalidSourceTokenAmount,
      isAuthConnector: connectorId === ConstantsUtil.CONNECTOR_ID.AUTH
    };
  },
  setSourceToken(sourceToken) {
    if (!sourceToken) {
      state21.sourceToken = sourceToken;
      state21.sourceTokenAmount = "";
      state21.sourceTokenPriceInUSD = 0;
      return;
    }
    state21.sourceToken = sourceToken;
    SwapController.setTokenPrice(sourceToken.address, "sourceToken");
  },
  setSourceTokenAmount(amount) {
    state21.sourceTokenAmount = amount;
  },
  setToToken(toToken) {
    if (!toToken) {
      state21.toToken = toToken;
      state21.toTokenAmount = "";
      state21.toTokenPriceInUSD = 0;
      return;
    }
    state21.toToken = toToken;
    SwapController.setTokenPrice(toToken.address, "toToken");
  },
  setToTokenAmount(amount) {
    state21.toTokenAmount = amount ? NumberUtil.formatNumberToLocalString(amount, TO_AMOUNT_DECIMALS) : "";
  },
  async setTokenPrice(address, target) {
    let price = state21.tokensPriceMap[address] || 0;
    if (!price) {
      state21.loadingPrices = true;
      price = await SwapController.getAddressPrice(address);
    }
    if (target === "sourceToken") {
      state21.sourceTokenPriceInUSD = price;
    } else if (target === "toToken") {
      state21.toTokenPriceInUSD = price;
    }
    if (state21.loadingPrices) {
      state21.loadingPrices = false;
    }
    if (SwapController.getParams().availableToSwap) {
      SwapController.swapTokens();
    }
  },
  switchTokens() {
    if (state21.initializing || !state21.initialized) {
      return;
    }
    const newSourceToken = state21.toToken ? { ...state21.toToken } : void 0;
    const newToToken = state21.sourceToken ? { ...state21.sourceToken } : void 0;
    const newSourceTokenAmount = newSourceToken && state21.toTokenAmount === "" ? "1" : state21.toTokenAmount;
    SwapController.setSourceToken(newSourceToken);
    SwapController.setToToken(newToToken);
    SwapController.setSourceTokenAmount(newSourceTokenAmount);
    SwapController.setToTokenAmount("");
    SwapController.swapTokens();
  },
  resetState() {
    state21.myTokensWithBalance = initialState.myTokensWithBalance;
    state21.tokensPriceMap = initialState.tokensPriceMap;
    state21.initialized = initialState.initialized;
    state21.sourceToken = initialState.sourceToken;
    state21.sourceTokenAmount = initialState.sourceTokenAmount;
    state21.sourceTokenPriceInUSD = initialState.sourceTokenPriceInUSD;
    state21.toToken = initialState.toToken;
    state21.toTokenAmount = initialState.toTokenAmount;
    state21.toTokenPriceInUSD = initialState.toTokenPriceInUSD;
    state21.networkPrice = initialState.networkPrice;
    state21.networkTokenSymbol = initialState.networkTokenSymbol;
    state21.networkBalanceInUSD = initialState.networkBalanceInUSD;
    state21.inputError = initialState.inputError;
    state21.myTokensWithBalance = initialState.myTokensWithBalance;
  },
  resetValues() {
    var _a;
    const { networkAddress } = SwapController.getParams();
    const networkToken = (_a = state21.tokens) == null ? void 0 : _a.find((token) => token.address === networkAddress);
    SwapController.setSourceToken(networkToken);
    SwapController.setToToken(void 0);
  },
  getApprovalLoadingState() {
    return state21.loadingApprovalTransaction;
  },
  clearError() {
    state21.transactionError = void 0;
  },
  async initializeState() {
    if (state21.initializing) {
      return;
    }
    state21.initializing = true;
    if (!state21.initialized) {
      try {
        await SwapController.fetchTokens();
        state21.initialized = true;
      } catch (error) {
        state21.initialized = false;
        SnackController.showError("Failed to initialize swap");
        RouterController.goBack();
      }
    }
    state21.initializing = false;
  },
  async fetchTokens() {
    var _a;
    const { networkAddress } = SwapController.getParams();
    await SwapController.getTokenList();
    await SwapController.getNetworkTokenPrice();
    await SwapController.getMyTokensWithBalance();
    const networkToken = (_a = state21.tokens) == null ? void 0 : _a.find((token) => token.address === networkAddress);
    if (networkToken) {
      state21.networkTokenSymbol = networkToken.symbol;
      SwapController.setSourceToken(networkToken);
      SwapController.setSourceTokenAmount("1");
    }
  },
  async getTokenList() {
    const tokens = await SwapApiUtil.getTokenList();
    state21.tokens = tokens;
    state21.popularTokens = tokens.sort((aTokenInfo, bTokenInfo) => {
      if (aTokenInfo.symbol < bTokenInfo.symbol) {
        return -1;
      }
      if (aTokenInfo.symbol > bTokenInfo.symbol) {
        return 1;
      }
      return 0;
    });
    state21.suggestedTokens = tokens.filter((token) => {
      if (ConstantsUtil2.SWAP_SUGGESTED_TOKENS.includes(token.symbol)) {
        return true;
      }
      return false;
    }, {});
  },
  async getAddressPrice(address) {
    var _a, _b;
    const existPrice = state21.tokensPriceMap[address];
    if (existPrice) {
      return existPrice;
    }
    const response = await BlockchainApiController.fetchTokenPrice({
      addresses: [address]
    });
    const fungibles = (response == null ? void 0 : response.fungibles) || [];
    const allTokens = [...state21.tokens || [], ...state21.myTokensWithBalance || []];
    const symbol = (_a = allTokens == null ? void 0 : allTokens.find((token) => token.address === address)) == null ? void 0 : _a.symbol;
    const price = ((_b = fungibles.find((p) => p.symbol.toLowerCase() === (symbol == null ? void 0 : symbol.toLowerCase()))) == null ? void 0 : _b.price) || 0;
    const priceAsFloat = parseFloat(price.toString());
    state21.tokensPriceMap[address] = priceAsFloat;
    return priceAsFloat;
  },
  async getNetworkTokenPrice() {
    var _a;
    const { networkAddress } = SwapController.getParams();
    const response = await BlockchainApiController.fetchTokenPrice({
      addresses: [networkAddress]
    }).catch(() => {
      SnackController.showError("Failed to fetch network token price");
      return { fungibles: [] };
    });
    const token = (_a = response.fungibles) == null ? void 0 : _a[0];
    const price = (token == null ? void 0 : token.price.toString()) || "0";
    state21.tokensPriceMap[networkAddress] = parseFloat(price);
    state21.networkTokenSymbol = (token == null ? void 0 : token.symbol) || "";
    state21.networkPrice = price;
  },
  async getMyTokensWithBalance(forceUpdate) {
    const balances = await SendApiUtil.getMyTokensWithBalance(forceUpdate);
    const swapBalances = SendApiUtil.mapBalancesToSwapTokens(balances);
    if (!swapBalances) {
      return;
    }
    await SwapController.getInitialGasPrice();
    SwapController.setBalances(swapBalances);
  },
  setBalances(balances) {
    const { networkAddress } = SwapController.getParams();
    const caipNetwork = ChainController.state.activeCaipNetwork;
    if (!caipNetwork) {
      return;
    }
    const networkToken = balances.find((token) => token.address === networkAddress);
    balances.forEach((token) => {
      state21.tokensPriceMap[token.address] = token.price || 0;
    });
    state21.myTokensWithBalance = balances.filter((token) => token.address.startsWith(caipNetwork.caipNetworkId));
    state21.networkBalanceInUSD = networkToken ? NumberUtil.multiply(networkToken.quantity.numeric, networkToken.price).toString() : "0";
  },
  async getInitialGasPrice() {
    var _a, _b;
    const res = await SwapApiUtil.fetchGasPrice();
    if (!res) {
      return { gasPrice: null, gasPriceInUSD: null };
    }
    switch ((_b = (_a = ChainController.state) == null ? void 0 : _a.activeCaipNetwork) == null ? void 0 : _b.chainNamespace) {
      case "solana":
        state21.gasFee = res.standard ?? "0";
        state21.gasPriceInUSD = NumberUtil.multiply(res.standard, state21.networkPrice).div(1e9).toNumber();
        return {
          gasPrice: BigInt(state21.gasFee),
          gasPriceInUSD: Number(state21.gasPriceInUSD)
        };
      case "eip155":
      default:
        const value = res.standard ?? "0";
        const gasFee = BigInt(value);
        const gasLimit = BigInt(INITIAL_GAS_LIMIT);
        const gasPrice = SwapCalculationUtil.getGasPriceInUSD(state21.networkPrice, gasLimit, gasFee);
        state21.gasFee = value;
        state21.gasPriceInUSD = gasPrice;
        return { gasPrice: gasFee, gasPriceInUSD: gasPrice };
    }
  },
  // -- Swap -------------------------------------- //
  async swapTokens() {
    var _a, _b;
    const address = AccountController.state.address;
    const sourceToken = state21.sourceToken;
    const toToken = state21.toToken;
    const haveSourceTokenAmount = NumberUtil.bigNumber(state21.sourceTokenAmount).gt(0);
    if (!haveSourceTokenAmount) {
      SwapController.setToTokenAmount("");
    }
    if (!toToken || !sourceToken || state21.loadingPrices || !haveSourceTokenAmount) {
      return;
    }
    state21.loadingQuote = true;
    const amountDecimal = NumberUtil.bigNumber(state21.sourceTokenAmount).times(10 ** sourceToken.decimals).round(0);
    try {
      const quoteResponse = await BlockchainApiController.fetchSwapQuote({
        userAddress: address,
        from: sourceToken.address,
        to: toToken.address,
        gasPrice: state21.gasFee,
        amount: amountDecimal.toString()
      });
      state21.loadingQuote = false;
      const quoteToAmount = (_b = (_a = quoteResponse == null ? void 0 : quoteResponse.quotes) == null ? void 0 : _a[0]) == null ? void 0 : _b.toAmount;
      if (!quoteToAmount) {
        AlertController.open({
          shortMessage: "Incorrect amount",
          longMessage: "Please enter a valid amount"
        }, "error");
        return;
      }
      const toTokenAmount = NumberUtil.bigNumber(quoteToAmount).div(10 ** toToken.decimals).toString();
      SwapController.setToTokenAmount(toTokenAmount);
      const isInsufficientToken = SwapController.hasInsufficientToken(state21.sourceTokenAmount, sourceToken.address);
      if (isInsufficientToken) {
        state21.inputError = "Insufficient balance";
      } else {
        state21.inputError = void 0;
        SwapController.setTransactionDetails();
      }
    } catch (error) {
      state21.loadingQuote = false;
      state21.inputError = "Insufficient balance";
    }
  },
  // -- Create Transactions -------------------------------------- //
  async getTransaction() {
    const { fromCaipAddress, availableToSwap } = SwapController.getParams();
    const sourceToken = state21.sourceToken;
    const toToken = state21.toToken;
    if (!fromCaipAddress || !availableToSwap || !sourceToken || !toToken || state21.loadingQuote) {
      return void 0;
    }
    try {
      state21.loadingBuildTransaction = true;
      const hasAllowance = await SwapApiUtil.fetchSwapAllowance({
        userAddress: fromCaipAddress,
        tokenAddress: sourceToken.address,
        sourceTokenAmount: state21.sourceTokenAmount,
        sourceTokenDecimals: sourceToken.decimals
      });
      let transaction = void 0;
      if (hasAllowance) {
        transaction = await SwapController.createSwapTransaction();
      } else {
        transaction = await SwapController.createAllowanceTransaction();
      }
      state21.loadingBuildTransaction = false;
      state21.fetchError = false;
      return transaction;
    } catch (error) {
      RouterController.goBack();
      SnackController.showError("Failed to check allowance");
      state21.loadingBuildTransaction = false;
      state21.approvalTransaction = void 0;
      state21.swapTransaction = void 0;
      state21.fetchError = true;
      return void 0;
    }
  },
  async createAllowanceTransaction() {
    const { fromCaipAddress, sourceTokenAddress, toTokenAddress } = SwapController.getParams();
    if (!fromCaipAddress || !toTokenAddress) {
      return void 0;
    }
    if (!sourceTokenAddress) {
      throw new Error("createAllowanceTransaction - No source token address found.");
    }
    try {
      const response = await BlockchainApiController.generateApproveCalldata({
        from: sourceTokenAddress,
        to: toTokenAddress,
        userAddress: fromCaipAddress
      });
      const transaction = {
        data: response.tx.data,
        to: CoreHelperUtil.getPlainAddress(response.tx.from),
        gasPrice: BigInt(response.tx.eip155.gasPrice),
        value: BigInt(response.tx.value),
        toAmount: state21.toTokenAmount
      };
      state21.swapTransaction = void 0;
      state21.approvalTransaction = {
        data: transaction.data,
        to: transaction.to,
        gasPrice: transaction.gasPrice,
        value: transaction.value,
        toAmount: transaction.toAmount
      };
      return {
        data: transaction.data,
        to: transaction.to,
        gasPrice: transaction.gasPrice,
        value: transaction.value,
        toAmount: transaction.toAmount
      };
    } catch (error) {
      RouterController.goBack();
      SnackController.showError("Failed to create approval transaction");
      state21.approvalTransaction = void 0;
      state21.swapTransaction = void 0;
      state21.fetchError = true;
      return void 0;
    }
  },
  async createSwapTransaction() {
    var _a;
    const { networkAddress, fromCaipAddress, sourceTokenAmount } = SwapController.getParams();
    const sourceToken = state21.sourceToken;
    const toToken = state21.toToken;
    if (!fromCaipAddress || !sourceTokenAmount || !sourceToken || !toToken) {
      return void 0;
    }
    const amount = (_a = ConnectionController.parseUnits(sourceTokenAmount, sourceToken.decimals)) == null ? void 0 : _a.toString();
    try {
      const response = await BlockchainApiController.generateSwapCalldata({
        userAddress: fromCaipAddress,
        from: sourceToken.address,
        to: toToken.address,
        amount,
        disableEstimate: true
      });
      const isSourceTokenIsNetworkToken = sourceToken.address === networkAddress;
      const gas = BigInt(response.tx.eip155.gas);
      const gasPrice = BigInt(response.tx.eip155.gasPrice);
      const transaction = {
        data: response.tx.data,
        to: CoreHelperUtil.getPlainAddress(response.tx.to),
        gas,
        gasPrice,
        value: isSourceTokenIsNetworkToken ? BigInt(amount ?? "0") : BigInt("0"),
        toAmount: state21.toTokenAmount
      };
      state21.gasPriceInUSD = SwapCalculationUtil.getGasPriceInUSD(state21.networkPrice, gas, gasPrice);
      state21.approvalTransaction = void 0;
      state21.swapTransaction = transaction;
      return transaction;
    } catch (error) {
      RouterController.goBack();
      SnackController.showError("Failed to create transaction");
      state21.approvalTransaction = void 0;
      state21.swapTransaction = void 0;
      state21.fetchError = true;
      return void 0;
    }
  },
  // -- Send Transactions --------------------------------- //
  async sendTransactionForApproval(data) {
    var _a, _b, _c2, _d;
    const { fromAddress, isAuthConnector } = SwapController.getParams();
    state21.loadingApprovalTransaction = true;
    const approveLimitMessage = `Approve limit increase in your wallet`;
    if (isAuthConnector) {
      RouterController.pushTransactionStack({
        onSuccess() {
          SnackController.showLoading(approveLimitMessage);
        }
      });
    } else {
      SnackController.showLoading(approveLimitMessage);
    }
    try {
      await ConnectionController.sendTransaction({
        address: fromAddress,
        to: data.to,
        data: data.data,
        value: data.value,
        chainNamespace: "eip155"
      });
      await SwapController.swapTokens();
      await SwapController.getTransaction();
      state21.approvalTransaction = void 0;
      state21.loadingApprovalTransaction = false;
    } catch (err) {
      const error = err;
      state21.transactionError = error == null ? void 0 : error.shortMessage;
      state21.loadingApprovalTransaction = false;
      SnackController.showError((error == null ? void 0 : error.shortMessage) || "Transaction error");
      EventsController.sendEvent({
        type: "track",
        event: "SWAP_APPROVAL_ERROR",
        properties: {
          message: (error == null ? void 0 : error.shortMessage) || (error == null ? void 0 : error.message) || "Unknown",
          network: ((_a = ChainController.state.activeCaipNetwork) == null ? void 0 : _a.caipNetworkId) || "",
          swapFromToken: ((_b = SwapController.state.sourceToken) == null ? void 0 : _b.symbol) || "",
          swapToToken: ((_c2 = SwapController.state.toToken) == null ? void 0 : _c2.symbol) || "",
          swapFromAmount: SwapController.state.sourceTokenAmount || "",
          swapToAmount: SwapController.state.toTokenAmount || "",
          isSmartAccount: ((_d = AccountController.state.preferredAccountTypes) == null ? void 0 : _d.eip155) === W3mFrameRpcConstants.ACCOUNT_TYPES.SMART_ACCOUNT
        }
      });
    }
  },
  async sendTransactionForSwap(data) {
    var _a, _b, _c2, _d, _e2, _f, _g, _h, _i2, _j, _k, _l, _m, _n2;
    if (!data) {
      return void 0;
    }
    const { fromAddress, toTokenAmount, isAuthConnector } = SwapController.getParams();
    state21.loadingTransaction = true;
    const snackbarPendingMessage = `Swapping ${(_a = state21.sourceToken) == null ? void 0 : _a.symbol} to ${NumberUtil.formatNumberToLocalString(toTokenAmount, 3)} ${(_b = state21.toToken) == null ? void 0 : _b.symbol}`;
    const snackbarSuccessMessage = `Swapped ${(_c2 = state21.sourceToken) == null ? void 0 : _c2.symbol} to ${NumberUtil.formatNumberToLocalString(toTokenAmount, 3)} ${(_d = state21.toToken) == null ? void 0 : _d.symbol}`;
    if (isAuthConnector) {
      RouterController.pushTransactionStack({
        onSuccess() {
          RouterController.replace("Account");
          SnackController.showLoading(snackbarPendingMessage);
          controller14.resetState();
        }
      });
    } else {
      SnackController.showLoading("Confirm transaction in your wallet");
    }
    try {
      const forceUpdateAddresses = [(_e2 = state21.sourceToken) == null ? void 0 : _e2.address, (_f = state21.toToken) == null ? void 0 : _f.address].join(",");
      const transactionHash = await ConnectionController.sendTransaction({
        address: fromAddress,
        to: data.to,
        data: data.data,
        value: data.value,
        chainNamespace: "eip155"
      });
      state21.loadingTransaction = false;
      SnackController.showSuccess(snackbarSuccessMessage);
      EventsController.sendEvent({
        type: "track",
        event: "SWAP_SUCCESS",
        properties: {
          network: ((_g = ChainController.state.activeCaipNetwork) == null ? void 0 : _g.caipNetworkId) || "",
          swapFromToken: ((_h = SwapController.state.sourceToken) == null ? void 0 : _h.symbol) || "",
          swapToToken: ((_i2 = SwapController.state.toToken) == null ? void 0 : _i2.symbol) || "",
          swapFromAmount: SwapController.state.sourceTokenAmount || "",
          swapToAmount: SwapController.state.toTokenAmount || "",
          isSmartAccount: ((_j = AccountController.state.preferredAccountTypes) == null ? void 0 : _j.eip155) === W3mFrameRpcConstants.ACCOUNT_TYPES.SMART_ACCOUNT
        }
      });
      controller14.resetState();
      if (!isAuthConnector) {
        RouterController.replace("Account");
      }
      controller14.getMyTokensWithBalance(forceUpdateAddresses);
      return transactionHash;
    } catch (err) {
      const error = err;
      state21.transactionError = error == null ? void 0 : error.shortMessage;
      state21.loadingTransaction = false;
      SnackController.showError((error == null ? void 0 : error.shortMessage) || "Transaction error");
      EventsController.sendEvent({
        type: "track",
        event: "SWAP_ERROR",
        properties: {
          message: (error == null ? void 0 : error.shortMessage) || (error == null ? void 0 : error.message) || "Unknown",
          network: ((_k = ChainController.state.activeCaipNetwork) == null ? void 0 : _k.caipNetworkId) || "",
          swapFromToken: ((_l = SwapController.state.sourceToken) == null ? void 0 : _l.symbol) || "",
          swapToToken: ((_m = SwapController.state.toToken) == null ? void 0 : _m.symbol) || "",
          swapFromAmount: SwapController.state.sourceTokenAmount || "",
          swapToAmount: SwapController.state.toTokenAmount || "",
          isSmartAccount: ((_n2 = AccountController.state.preferredAccountTypes) == null ? void 0 : _n2.eip155) === W3mFrameRpcConstants.ACCOUNT_TYPES.SMART_ACCOUNT
        }
      });
      return void 0;
    }
  },
  // -- Checks -------------------------------------------- //
  hasInsufficientToken(sourceTokenAmount, sourceTokenAddress) {
    const isInsufficientSourceTokenForSwap = SwapCalculationUtil.isInsufficientSourceTokenForSwap(sourceTokenAmount, sourceTokenAddress, state21.myTokensWithBalance);
    return isInsufficientSourceTokenForSwap;
  },
  // -- Calculations -------------------------------------- //
  setTransactionDetails() {
    const { toTokenAddress, toTokenDecimals } = SwapController.getParams();
    if (!toTokenAddress || !toTokenDecimals) {
      return;
    }
    state21.gasPriceInUSD = SwapCalculationUtil.getGasPriceInUSD(state21.networkPrice, BigInt(state21.gasFee), BigInt(INITIAL_GAS_LIMIT));
    state21.priceImpact = SwapCalculationUtil.getPriceImpact({
      sourceTokenAmount: state21.sourceTokenAmount,
      sourceTokenPriceInUSD: state21.sourceTokenPriceInUSD,
      toTokenPriceInUSD: state21.toTokenPriceInUSD,
      toTokenAmount: state21.toTokenAmount
    });
    state21.maxSlippage = SwapCalculationUtil.getMaxSlippage(state21.slippage, state21.toTokenAmount);
    state21.providerFee = SwapCalculationUtil.getProviderFee(state21.sourceTokenAmount);
  }
};
var SwapController = withErrorBoundary(controller14);

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/TooltipController.js
var state22 = proxy({
  message: "",
  open: false,
  triggerRect: {
    width: 0,
    height: 0,
    top: 0,
    left: 0
  },
  variant: "shade"
});
var controller15 = {
  state: state22,
  subscribe(callback) {
    return subscribe(state22, () => callback(state22));
  },
  subscribeKey(key, callback) {
    return subscribeKey(state22, key, callback);
  },
  showTooltip({ message, triggerRect, variant }) {
    state22.open = true;
    state22.message = message;
    state22.triggerRect = triggerRect;
    state22.variant = variant;
  },
  hide() {
    state22.open = false;
    state22.message = "";
    state22.triggerRect = {
      width: 0,
      height: 0,
      top: 0,
      left: 0
    };
  }
};
var TooltipController = withErrorBoundary(controller15);

// node_modules/@reown/appkit-controllers/dist/esm/src/utils/EnsUtil.js
var SLIP44_MSB = 2147483648;
var EnsUtil = {
  convertEVMChainIdToCoinType(chainId) {
    if (chainId >= SLIP44_MSB) {
      throw new Error("Invalid chainId");
    }
    return (SLIP44_MSB | chainId) >>> 0;
  }
};

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/EnsController.js
var state23 = proxy({
  suggestions: [],
  loading: false
});
var controller16 = {
  state: state23,
  subscribe(callback) {
    return subscribe(state23, () => callback(state23));
  },
  subscribeKey(key, callback) {
    return subscribeKey(state23, key, callback);
  },
  async resolveName(name) {
    var _a, _b;
    try {
      return await BlockchainApiController.lookupEnsName(name);
    } catch (e2) {
      const error = e2;
      throw new Error(((_b = (_a = error == null ? void 0 : error.reasons) == null ? void 0 : _a[0]) == null ? void 0 : _b.description) || "Error resolving name");
    }
  },
  async isNameRegistered(name) {
    try {
      await BlockchainApiController.lookupEnsName(name);
      return true;
    } catch {
      return false;
    }
  },
  async getSuggestions(value) {
    try {
      state23.loading = true;
      state23.suggestions = [];
      const response = await BlockchainApiController.getEnsNameSuggestions(value);
      state23.suggestions = response.suggestions.map((suggestion) => ({
        ...suggestion,
        name: suggestion.name
      })) || [];
      return state23.suggestions;
    } catch (e2) {
      const errorMessage = EnsController.parseEnsApiError(e2, "Error fetching name suggestions");
      throw new Error(errorMessage);
    } finally {
      state23.loading = false;
    }
  },
  async getNamesForAddress(address) {
    try {
      const network = ChainController.state.activeCaipNetwork;
      if (!network) {
        return [];
      }
      const cachedEns = StorageUtil.getEnsFromCacheForAddress(address);
      if (cachedEns) {
        return cachedEns;
      }
      const response = await BlockchainApiController.reverseLookupEnsName({ address });
      StorageUtil.updateEnsCache({
        address,
        ens: response,
        timestamp: Date.now()
      });
      return response;
    } catch (e2) {
      const errorMessage = EnsController.parseEnsApiError(e2, "Error fetching names for address");
      throw new Error(errorMessage);
    }
  },
  async registerName(name) {
    const network = ChainController.state.activeCaipNetwork;
    if (!network) {
      throw new Error("Network not found");
    }
    const address = AccountController.state.address;
    const emailConnector = ConnectorController.getAuthConnector();
    if (!address || !emailConnector) {
      throw new Error("Address or auth connector not found");
    }
    state23.loading = true;
    try {
      const message = JSON.stringify({
        name,
        attributes: {},
        // Unix timestamp
        timestamp: Math.floor(Date.now() / 1e3)
      });
      RouterController.pushTransactionStack({
        onCancel() {
          RouterController.replace("RegisterAccountName");
        }
      });
      const signature = await ConnectionController.signMessage(message);
      state23.loading = false;
      const networkId = network.id;
      if (!networkId) {
        throw new Error("Network not found");
      }
      const coinType = EnsUtil.convertEVMChainIdToCoinType(Number(networkId));
      await BlockchainApiController.registerEnsName({
        coinType,
        address,
        signature,
        message
      });
      AccountController.setProfileName(name, network.chainNamespace);
      RouterController.replace("RegisterAccountNameSuccess");
    } catch (e2) {
      const errorMessage = EnsController.parseEnsApiError(e2, `Error registering name ${name}`);
      RouterController.replace("RegisterAccountName");
      throw new Error(errorMessage);
    } finally {
      state23.loading = false;
    }
  },
  validateName(name) {
    return /^[a-zA-Z0-9-]{4,}$/u.test(name);
  },
  parseEnsApiError(error, defaultError) {
    var _a, _b;
    const ensError = error;
    return ((_b = (_a = ensError == null ? void 0 : ensError.reasons) == null ? void 0 : _a[0]) == null ? void 0 : _b.description) || defaultError;
  }
};
var EnsController = withErrorBoundary(controller16);

// node_modules/@reown/appkit-controllers/dist/esm/src/controllers/OptionsStateController.js
var state24 = proxy({
  isLegalCheckboxChecked: false
});

// node_modules/@reown/appkit-ui/dist/esm/src/utils/ThemeUtil.js
var themeTag = void 0;
var darkModeTag = void 0;
var lightModeTag = void 0;
function initializeTheming(themeVariables, themeMode) {
  themeTag = document.createElement("style");
  darkModeTag = document.createElement("style");
  lightModeTag = document.createElement("style");
  themeTag.textContent = createRootStyles(themeVariables).core.cssText;
  darkModeTag.textContent = createRootStyles(themeVariables).dark.cssText;
  lightModeTag.textContent = createRootStyles(themeVariables).light.cssText;
  document.head.appendChild(themeTag);
  document.head.appendChild(darkModeTag);
  document.head.appendChild(lightModeTag);
  setColorTheme(themeMode);
}
function setColorTheme(themeMode) {
  if (darkModeTag && lightModeTag) {
    if (themeMode === "light") {
      darkModeTag.removeAttribute("media");
      lightModeTag.media = "enabled";
    } else {
      lightModeTag.removeAttribute("media");
      darkModeTag.media = "enabled";
    }
  }
}
function setThemeVariables(themeVariables) {
  if (themeTag && darkModeTag && lightModeTag) {
    themeTag.textContent = createRootStyles(themeVariables).core.cssText;
    darkModeTag.textContent = createRootStyles(themeVariables).dark.cssText;
    lightModeTag.textContent = createRootStyles(themeVariables).light.cssText;
  }
}
function createRootStyles(themeVariables) {
  return {
    core: css`
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
      @keyframes w3m-shake {
        0% {
          transform: scale(1) rotate(0deg);
        }
        20% {
          transform: scale(1) rotate(-1deg);
        }
        40% {
          transform: scale(1) rotate(1.5deg);
        }
        60% {
          transform: scale(1) rotate(-1.5deg);
        }
        80% {
          transform: scale(1) rotate(1deg);
        }
        100% {
          transform: scale(1) rotate(0deg);
        }
      }
      @keyframes w3m-iframe-fade-out {
        0% {
          opacity: 1;
        }
        100% {
          opacity: 0;
        }
      }
      @keyframes w3m-iframe-zoom-in {
        0% {
          transform: translateY(50px);
          opacity: 0;
        }
        100% {
          transform: translateY(0px);
          opacity: 1;
        }
      }
      @keyframes w3m-iframe-zoom-in-mobile {
        0% {
          transform: scale(0.95);
          opacity: 0;
        }
        100% {
          transform: scale(1);
          opacity: 1;
        }
      }
      :root {
        --w3m-modal-width: 360px;
        --w3m-color-mix-strength: ${unsafeCSS((themeVariables == null ? void 0 : themeVariables["--w3m-color-mix-strength"]) ? `${themeVariables["--w3m-color-mix-strength"]}%` : "0%")};
        --w3m-font-family: ${unsafeCSS((themeVariables == null ? void 0 : themeVariables["--w3m-font-family"]) || "Inter, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;")};
        --w3m-font-size-master: ${unsafeCSS((themeVariables == null ? void 0 : themeVariables["--w3m-font-size-master"]) || "10px")};
        --w3m-border-radius-master: ${unsafeCSS((themeVariables == null ? void 0 : themeVariables["--w3m-border-radius-master"]) || "4px")};
        --w3m-z-index: ${unsafeCSS((themeVariables == null ? void 0 : themeVariables["--w3m-z-index"]) || 999)};

        --wui-font-family: var(--w3m-font-family);

        --wui-font-size-mini: calc(var(--w3m-font-size-master) * 0.8);
        --wui-font-size-micro: var(--w3m-font-size-master);
        --wui-font-size-tiny: calc(var(--w3m-font-size-master) * 1.2);
        --wui-font-size-small: calc(var(--w3m-font-size-master) * 1.4);
        --wui-font-size-paragraph: calc(var(--w3m-font-size-master) * 1.6);
        --wui-font-size-medium: calc(var(--w3m-font-size-master) * 1.8);
        --wui-font-size-large: calc(var(--w3m-font-size-master) * 2);
        --wui-font-size-title-6: calc(var(--w3m-font-size-master) * 2.2);
        --wui-font-size-medium-title: calc(var(--w3m-font-size-master) * 2.4);
        --wui-font-size-2xl: calc(var(--w3m-font-size-master) * 4);

        --wui-border-radius-5xs: var(--w3m-border-radius-master);
        --wui-border-radius-4xs: calc(var(--w3m-border-radius-master) * 1.5);
        --wui-border-radius-3xs: calc(var(--w3m-border-radius-master) * 2);
        --wui-border-radius-xxs: calc(var(--w3m-border-radius-master) * 3);
        --wui-border-radius-xs: calc(var(--w3m-border-radius-master) * 4);
        --wui-border-radius-s: calc(var(--w3m-border-radius-master) * 5);
        --wui-border-radius-m: calc(var(--w3m-border-radius-master) * 7);
        --wui-border-radius-l: calc(var(--w3m-border-radius-master) * 9);
        --wui-border-radius-3xl: calc(var(--w3m-border-radius-master) * 20);

        --wui-font-weight-light: 400;
        --wui-font-weight-regular: 500;
        --wui-font-weight-medium: 600;
        --wui-font-weight-bold: 700;

        --wui-letter-spacing-2xl: -1.6px;
        --wui-letter-spacing-medium-title: -0.96px;
        --wui-letter-spacing-title-6: -0.88px;
        --wui-letter-spacing-large: -0.8px;
        --wui-letter-spacing-medium: -0.72px;
        --wui-letter-spacing-paragraph: -0.64px;
        --wui-letter-spacing-small: -0.56px;
        --wui-letter-spacing-tiny: -0.48px;
        --wui-letter-spacing-micro: -0.2px;
        --wui-letter-spacing-mini: -0.16px;

        --wui-spacing-0: 0px;
        --wui-spacing-4xs: 2px;
        --wui-spacing-3xs: 4px;
        --wui-spacing-xxs: 6px;
        --wui-spacing-2xs: 7px;
        --wui-spacing-xs: 8px;
        --wui-spacing-1xs: 10px;
        --wui-spacing-s: 12px;
        --wui-spacing-m: 14px;
        --wui-spacing-l: 16px;
        --wui-spacing-2l: 18px;
        --wui-spacing-xl: 20px;
        --wui-spacing-xxl: 24px;
        --wui-spacing-2xl: 32px;
        --wui-spacing-3xl: 40px;
        --wui-spacing-4xl: 90px;
        --wui-spacing-5xl: 95px;

        --wui-icon-box-size-xxs: 14px;
        --wui-icon-box-size-xs: 20px;
        --wui-icon-box-size-sm: 24px;
        --wui-icon-box-size-md: 32px;
        --wui-icon-box-size-mdl: 36px;
        --wui-icon-box-size-lg: 40px;
        --wui-icon-box-size-2lg: 48px;
        --wui-icon-box-size-xl: 64px;

        --wui-icon-size-inherit: inherit;
        --wui-icon-size-xxs: 10px;
        --wui-icon-size-xs: 12px;
        --wui-icon-size-sm: 14px;
        --wui-icon-size-md: 16px;
        --wui-icon-size-mdl: 18px;
        --wui-icon-size-lg: 20px;
        --wui-icon-size-xl: 24px;
        --wui-icon-size-xxl: 28px;

        --wui-wallet-image-size-inherit: inherit;
        --wui-wallet-image-size-sm: 40px;
        --wui-wallet-image-size-md: 56px;
        --wui-wallet-image-size-lg: 80px;

        --wui-visual-size-size-inherit: inherit;
        --wui-visual-size-sm: 40px;
        --wui-visual-size-md: 55px;
        --wui-visual-size-lg: 80px;

        --wui-box-size-md: 100px;
        --wui-box-size-lg: 120px;

        --wui-ease-out-power-2: cubic-bezier(0, 0, 0.22, 1);
        --wui-ease-out-power-1: cubic-bezier(0, 0, 0.55, 1);

        --wui-ease-in-power-3: cubic-bezier(0.66, 0, 1, 1);
        --wui-ease-in-power-2: cubic-bezier(0.45, 0, 1, 1);
        --wui-ease-in-power-1: cubic-bezier(0.3, 0, 1, 1);

        --wui-ease-inout-power-1: cubic-bezier(0.45, 0, 0.55, 1);

        --wui-duration-lg: 200ms;
        --wui-duration-md: 125ms;
        --wui-duration-sm: 75ms;

        --wui-path-network-sm: path(
          'M15.4 2.1a5.21 5.21 0 0 1 5.2 0l11.61 6.7a5.21 5.21 0 0 1 2.61 4.52v13.4c0 1.87-1 3.59-2.6 4.52l-11.61 6.7c-1.62.93-3.6.93-5.22 0l-11.6-6.7a5.21 5.21 0 0 1-2.61-4.51v-13.4c0-1.87 1-3.6 2.6-4.52L15.4 2.1Z'
        );

        --wui-path-network-md: path(
          'M43.4605 10.7248L28.0485 1.61089C25.5438 0.129705 22.4562 0.129705 19.9515 1.61088L4.53951 10.7248C2.03626 12.2051 0.5 14.9365 0.5 17.886V36.1139C0.5 39.0635 2.03626 41.7949 4.53951 43.2752L19.9515 52.3891C22.4562 53.8703 25.5438 53.8703 28.0485 52.3891L43.4605 43.2752C45.9637 41.7949 47.5 39.0635 47.5 36.114V17.8861C47.5 14.9365 45.9637 12.2051 43.4605 10.7248Z'
        );

        --wui-path-network-lg: path(
          'M78.3244 18.926L50.1808 2.45078C45.7376 -0.150261 40.2624 -0.150262 35.8192 2.45078L7.6756 18.926C3.23322 21.5266 0.5 26.3301 0.5 31.5248V64.4752C0.5 69.6699 3.23322 74.4734 7.6756 77.074L35.8192 93.5492C40.2624 96.1503 45.7376 96.1503 50.1808 93.5492L78.3244 77.074C82.7668 74.4734 85.5 69.6699 85.5 64.4752V31.5248C85.5 26.3301 82.7668 21.5266 78.3244 18.926Z'
        );

        --wui-width-network-sm: 36px;
        --wui-width-network-md: 48px;
        --wui-width-network-lg: 86px;

        --wui-height-network-sm: 40px;
        --wui-height-network-md: 54px;
        --wui-height-network-lg: 96px;

        --wui-icon-size-network-xs: 12px;
        --wui-icon-size-network-sm: 16px;
        --wui-icon-size-network-md: 24px;
        --wui-icon-size-network-lg: 42px;

        --wui-color-inherit: inherit;

        --wui-color-inverse-100: #fff;
        --wui-color-inverse-000: #000;

        --wui-cover: rgba(20, 20, 20, 0.8);

        --wui-color-modal-bg: var(--wui-color-modal-bg-base);

        --wui-color-accent-100: var(--wui-color-accent-base-100);
        --wui-color-accent-090: var(--wui-color-accent-base-090);
        --wui-color-accent-080: var(--wui-color-accent-base-080);

        --wui-color-success-100: var(--wui-color-success-base-100);
        --wui-color-success-125: var(--wui-color-success-base-125);

        --wui-color-warning-100: var(--wui-color-warning-base-100);

        --wui-color-error-100: var(--wui-color-error-base-100);
        --wui-color-error-125: var(--wui-color-error-base-125);

        --wui-color-blue-100: var(--wui-color-blue-base-100);
        --wui-color-blue-90: var(--wui-color-blue-base-90);

        --wui-icon-box-bg-error-100: var(--wui-icon-box-bg-error-base-100);
        --wui-icon-box-bg-blue-100: var(--wui-icon-box-bg-blue-base-100);
        --wui-icon-box-bg-success-100: var(--wui-icon-box-bg-success-base-100);
        --wui-icon-box-bg-inverse-100: var(--wui-icon-box-bg-inverse-base-100);

        --wui-all-wallets-bg-100: var(--wui-all-wallets-bg-100);

        --wui-avatar-border: var(--wui-avatar-border-base);

        --wui-thumbnail-border: var(--wui-thumbnail-border-base);

        --wui-wallet-button-bg: var(--wui-wallet-button-bg-base);

        --wui-box-shadow-blue: var(--wui-color-accent-glass-020);
      }

      @supports (background: color-mix(in srgb, white 50%, black)) {
        :root {
          --wui-color-modal-bg: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-modal-bg-base)
          );

          --wui-box-shadow-blue: color-mix(in srgb, var(--wui-color-accent-100) 20%, transparent);

          --wui-color-accent-100: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 100%,
            transparent
          );
          --wui-color-accent-090: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 90%,
            transparent
          );
          --wui-color-accent-080: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 80%,
            transparent
          );
          --wui-color-accent-glass-090: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 90%,
            transparent
          );
          --wui-color-accent-glass-080: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 80%,
            transparent
          );
          --wui-color-accent-glass-020: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 20%,
            transparent
          );
          --wui-color-accent-glass-015: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 15%,
            transparent
          );
          --wui-color-accent-glass-010: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 10%,
            transparent
          );
          --wui-color-accent-glass-005: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 5%,
            transparent
          );
          --wui-color-accent-002: color-mix(
            in srgb,
            var(--wui-color-accent-base-100) 2%,
            transparent
          );

          --wui-color-fg-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-100)
          );
          --wui-color-fg-125: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-125)
          );
          --wui-color-fg-150: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-150)
          );
          --wui-color-fg-175: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-175)
          );
          --wui-color-fg-200: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-200)
          );
          --wui-color-fg-225: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-225)
          );
          --wui-color-fg-250: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-250)
          );
          --wui-color-fg-275: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-275)
          );
          --wui-color-fg-300: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-300)
          );
          --wui-color-fg-325: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-325)
          );
          --wui-color-fg-350: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-fg-350)
          );

          --wui-color-bg-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-100)
          );
          --wui-color-bg-125: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-125)
          );
          --wui-color-bg-150: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-150)
          );
          --wui-color-bg-175: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-175)
          );
          --wui-color-bg-200: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-200)
          );
          --wui-color-bg-225: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-225)
          );
          --wui-color-bg-250: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-250)
          );
          --wui-color-bg-275: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-275)
          );
          --wui-color-bg-300: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-300)
          );
          --wui-color-bg-325: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-325)
          );
          --wui-color-bg-350: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-bg-350)
          );

          --wui-color-success-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-success-base-100)
          );
          --wui-color-success-125: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-success-base-125)
          );

          --wui-color-warning-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-warning-base-100)
          );

          --wui-color-error-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-error-base-100)
          );
          --wui-color-blue-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-blue-base-100)
          );
          --wui-color-blue-90: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-blue-base-90)
          );
          --wui-color-error-125: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-color-error-base-125)
          );

          --wui-icon-box-bg-error-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-icon-box-bg-error-base-100)
          );
          --wui-icon-box-bg-accent-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-icon-box-bg-blue-base-100)
          );
          --wui-icon-box-bg-success-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-icon-box-bg-success-base-100)
          );
          --wui-icon-box-bg-inverse-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-icon-box-bg-inverse-base-100)
          );

          --wui-all-wallets-bg-100: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-all-wallets-bg-100)
          );

          --wui-avatar-border: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-avatar-border-base)
          );

          --wui-thumbnail-border: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-thumbnail-border-base)
          );

          --wui-wallet-button-bg: color-mix(
            in srgb,
            var(--w3m-color-mix) var(--w3m-color-mix-strength),
            var(--wui-wallet-button-bg-base)
          );
        }
      }
    `,
    light: css`
      :root {
        --w3m-color-mix: ${unsafeCSS((themeVariables == null ? void 0 : themeVariables["--w3m-color-mix"]) || "#fff")};
        --w3m-accent: ${unsafeCSS(getW3mThemeVariables(themeVariables, "dark")["--w3m-accent"])};
        --w3m-default: #fff;

        --wui-color-modal-bg-base: ${unsafeCSS(getW3mThemeVariables(themeVariables, "dark")["--w3m-background"])};
        --wui-color-accent-base-100: var(--w3m-accent);

        --wui-color-blueberry-100: hsla(230, 100%, 67%, 1);
        --wui-color-blueberry-090: hsla(231, 76%, 61%, 1);
        --wui-color-blueberry-080: hsla(230, 59%, 55%, 1);
        --wui-color-blueberry-050: hsla(231, 100%, 70%, 0.1);

        --wui-color-fg-100: #e4e7e7;
        --wui-color-fg-125: #d0d5d5;
        --wui-color-fg-150: #a8b1b1;
        --wui-color-fg-175: #a8b0b0;
        --wui-color-fg-200: #949e9e;
        --wui-color-fg-225: #868f8f;
        --wui-color-fg-250: #788080;
        --wui-color-fg-275: #788181;
        --wui-color-fg-300: #6e7777;
        --wui-color-fg-325: #9a9a9a;
        --wui-color-fg-350: #363636;

        --wui-color-bg-100: #141414;
        --wui-color-bg-125: #191a1a;
        --wui-color-bg-150: #1e1f1f;
        --wui-color-bg-175: #222525;
        --wui-color-bg-200: #272a2a;
        --wui-color-bg-225: #2c3030;
        --wui-color-bg-250: #313535;
        --wui-color-bg-275: #363b3b;
        --wui-color-bg-300: #3b4040;
        --wui-color-bg-325: #252525;
        --wui-color-bg-350: #ffffff;

        --wui-color-success-base-100: #26d962;
        --wui-color-success-base-125: #30a46b;

        --wui-color-warning-base-100: #f3a13f;

        --wui-color-error-base-100: #f25a67;
        --wui-color-error-base-125: #df4a34;

        --wui-color-blue-base-100: rgba(102, 125, 255, 1);
        --wui-color-blue-base-90: rgba(102, 125, 255, 0.9);

        --wui-color-success-glass-001: rgba(38, 217, 98, 0.01);
        --wui-color-success-glass-002: rgba(38, 217, 98, 0.02);
        --wui-color-success-glass-005: rgba(38, 217, 98, 0.05);
        --wui-color-success-glass-010: rgba(38, 217, 98, 0.1);
        --wui-color-success-glass-015: rgba(38, 217, 98, 0.15);
        --wui-color-success-glass-020: rgba(38, 217, 98, 0.2);
        --wui-color-success-glass-025: rgba(38, 217, 98, 0.25);
        --wui-color-success-glass-030: rgba(38, 217, 98, 0.3);
        --wui-color-success-glass-060: rgba(38, 217, 98, 0.6);
        --wui-color-success-glass-080: rgba(38, 217, 98, 0.8);

        --wui-color-success-glass-reown-020: rgba(48, 164, 107, 0.2);

        --wui-color-warning-glass-reown-020: rgba(243, 161, 63, 0.2);

        --wui-color-error-glass-001: rgba(242, 90, 103, 0.01);
        --wui-color-error-glass-002: rgba(242, 90, 103, 0.02);
        --wui-color-error-glass-005: rgba(242, 90, 103, 0.05);
        --wui-color-error-glass-010: rgba(242, 90, 103, 0.1);
        --wui-color-error-glass-015: rgba(242, 90, 103, 0.15);
        --wui-color-error-glass-020: rgba(242, 90, 103, 0.2);
        --wui-color-error-glass-025: rgba(242, 90, 103, 0.25);
        --wui-color-error-glass-030: rgba(242, 90, 103, 0.3);
        --wui-color-error-glass-060: rgba(242, 90, 103, 0.6);
        --wui-color-error-glass-080: rgba(242, 90, 103, 0.8);

        --wui-color-error-glass-reown-020: rgba(223, 74, 52, 0.2);

        --wui-color-gray-glass-001: rgba(255, 255, 255, 0.01);
        --wui-color-gray-glass-002: rgba(255, 255, 255, 0.02);
        --wui-color-gray-glass-005: rgba(255, 255, 255, 0.05);
        --wui-color-gray-glass-010: rgba(255, 255, 255, 0.1);
        --wui-color-gray-glass-015: rgba(255, 255, 255, 0.15);
        --wui-color-gray-glass-020: rgba(255, 255, 255, 0.2);
        --wui-color-gray-glass-025: rgba(255, 255, 255, 0.25);
        --wui-color-gray-glass-030: rgba(255, 255, 255, 0.3);
        --wui-color-gray-glass-060: rgba(255, 255, 255, 0.6);
        --wui-color-gray-glass-080: rgba(255, 255, 255, 0.8);
        --wui-color-gray-glass-090: rgba(255, 255, 255, 0.9);

        --wui-color-dark-glass-100: rgba(42, 42, 42, 1);

        --wui-icon-box-bg-error-base-100: #3c2426;
        --wui-icon-box-bg-blue-base-100: #20303f;
        --wui-icon-box-bg-success-base-100: #1f3a28;
        --wui-icon-box-bg-inverse-base-100: #243240;

        --wui-all-wallets-bg-100: #222b35;

        --wui-avatar-border-base: #252525;

        --wui-thumbnail-border-base: #252525;

        --wui-wallet-button-bg-base: var(--wui-color-bg-125);

        --w3m-card-embedded-shadow-color: rgb(17 17 18 / 25%);
      }
    `,
    dark: css`
      :root {
        --w3m-color-mix: ${unsafeCSS((themeVariables == null ? void 0 : themeVariables["--w3m-color-mix"]) || "#000")};
        --w3m-accent: ${unsafeCSS(getW3mThemeVariables(themeVariables, "light")["--w3m-accent"])};
        --w3m-default: #000;

        --wui-color-modal-bg-base: ${unsafeCSS(getW3mThemeVariables(themeVariables, "light")["--w3m-background"])};
        --wui-color-accent-base-100: var(--w3m-accent);

        --wui-color-blueberry-100: hsla(231, 100%, 70%, 1);
        --wui-color-blueberry-090: hsla(231, 97%, 72%, 1);
        --wui-color-blueberry-080: hsla(231, 92%, 74%, 1);

        --wui-color-fg-100: #141414;
        --wui-color-fg-125: #2d3131;
        --wui-color-fg-150: #474d4d;
        --wui-color-fg-175: #636d6d;
        --wui-color-fg-200: #798686;
        --wui-color-fg-225: #828f8f;
        --wui-color-fg-250: #8b9797;
        --wui-color-fg-275: #95a0a0;
        --wui-color-fg-300: #9ea9a9;
        --wui-color-fg-325: #9a9a9a;
        --wui-color-fg-350: #d0d0d0;

        --wui-color-bg-100: #ffffff;
        --wui-color-bg-125: #f5fafa;
        --wui-color-bg-150: #f3f8f8;
        --wui-color-bg-175: #eef4f4;
        --wui-color-bg-200: #eaf1f1;
        --wui-color-bg-225: #e5eded;
        --wui-color-bg-250: #e1e9e9;
        --wui-color-bg-275: #dce7e7;
        --wui-color-bg-300: #d8e3e3;
        --wui-color-bg-325: #f3f3f3;
        --wui-color-bg-350: #202020;

        --wui-color-success-base-100: #26b562;
        --wui-color-success-base-125: #30a46b;

        --wui-color-warning-base-100: #f3a13f;

        --wui-color-error-base-100: #f05142;
        --wui-color-error-base-125: #df4a34;

        --wui-color-blue-base-100: rgba(102, 125, 255, 1);
        --wui-color-blue-base-90: rgba(102, 125, 255, 0.9);

        --wui-color-success-glass-001: rgba(38, 181, 98, 0.01);
        --wui-color-success-glass-002: rgba(38, 181, 98, 0.02);
        --wui-color-success-glass-005: rgba(38, 181, 98, 0.05);
        --wui-color-success-glass-010: rgba(38, 181, 98, 0.1);
        --wui-color-success-glass-015: rgba(38, 181, 98, 0.15);
        --wui-color-success-glass-020: rgba(38, 181, 98, 0.2);
        --wui-color-success-glass-025: rgba(38, 181, 98, 0.25);
        --wui-color-success-glass-030: rgba(38, 181, 98, 0.3);
        --wui-color-success-glass-060: rgba(38, 181, 98, 0.6);
        --wui-color-success-glass-080: rgba(38, 181, 98, 0.8);

        --wui-color-success-glass-reown-020: rgba(48, 164, 107, 0.2);

        --wui-color-warning-glass-reown-020: rgba(243, 161, 63, 0.2);

        --wui-color-error-glass-001: rgba(240, 81, 66, 0.01);
        --wui-color-error-glass-002: rgba(240, 81, 66, 0.02);
        --wui-color-error-glass-005: rgba(240, 81, 66, 0.05);
        --wui-color-error-glass-010: rgba(240, 81, 66, 0.1);
        --wui-color-error-glass-015: rgba(240, 81, 66, 0.15);
        --wui-color-error-glass-020: rgba(240, 81, 66, 0.2);
        --wui-color-error-glass-025: rgba(240, 81, 66, 0.25);
        --wui-color-error-glass-030: rgba(240, 81, 66, 0.3);
        --wui-color-error-glass-060: rgba(240, 81, 66, 0.6);
        --wui-color-error-glass-080: rgba(240, 81, 66, 0.8);

        --wui-color-error-glass-reown-020: rgba(223, 74, 52, 0.2);

        --wui-icon-box-bg-error-base-100: #f4dfdd;
        --wui-icon-box-bg-blue-base-100: #d9ecfb;
        --wui-icon-box-bg-success-base-100: #daf0e4;
        --wui-icon-box-bg-inverse-base-100: #dcecfc;

        --wui-all-wallets-bg-100: #e8f1fa;

        --wui-avatar-border-base: #f3f4f4;

        --wui-thumbnail-border-base: #eaefef;

        --wui-wallet-button-bg-base: var(--wui-color-bg-125);

        --wui-color-gray-glass-001: rgba(0, 0, 0, 0.01);
        --wui-color-gray-glass-002: rgba(0, 0, 0, 0.02);
        --wui-color-gray-glass-005: rgba(0, 0, 0, 0.05);
        --wui-color-gray-glass-010: rgba(0, 0, 0, 0.1);
        --wui-color-gray-glass-015: rgba(0, 0, 0, 0.15);
        --wui-color-gray-glass-020: rgba(0, 0, 0, 0.2);
        --wui-color-gray-glass-025: rgba(0, 0, 0, 0.25);
        --wui-color-gray-glass-030: rgba(0, 0, 0, 0.3);
        --wui-color-gray-glass-060: rgba(0, 0, 0, 0.6);
        --wui-color-gray-glass-080: rgba(0, 0, 0, 0.8);
        --wui-color-gray-glass-090: rgba(0, 0, 0, 0.9);

        --wui-color-dark-glass-100: rgba(233, 233, 233, 1);

        --w3m-card-embedded-shadow-color: rgb(224 225 233 / 25%);
      }
    `
  };
}
var resetStyles = css`
  *,
  *::after,
  *::before,
  :host {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-style: normal;
    text-rendering: optimizeSpeed;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-tap-highlight-color: transparent;
    font-family: var(--wui-font-family);
    backface-visibility: hidden;
  }
`;
var elementStyles = css`
  button,
  a {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    transition:
      color var(--wui-duration-lg) var(--wui-ease-out-power-1),
      background-color var(--wui-duration-lg) var(--wui-ease-out-power-1),
      border var(--wui-duration-lg) var(--wui-ease-out-power-1),
      border-radius var(--wui-duration-lg) var(--wui-ease-out-power-1),
      box-shadow var(--wui-duration-lg) var(--wui-ease-out-power-1);
    will-change: background-color, color, border, box-shadow, border-radius;
    outline: none;
    border: none;
    column-gap: var(--wui-spacing-3xs);
    background-color: transparent;
    text-decoration: none;
  }

  wui-flex {
    transition: border-radius var(--wui-duration-lg) var(--wui-ease-out-power-1);
    will-change: border-radius;
  }

  button:disabled > wui-wallet-image,
  button:disabled > wui-all-wallets-image,
  button:disabled > wui-network-image,
  button:disabled > wui-image,
  button:disabled > wui-transaction-visual,
  button:disabled > wui-logo {
    filter: grayscale(1);
  }

  @media (hover: hover) and (pointer: fine) {
    button:hover:enabled {
      background-color: var(--wui-color-gray-glass-005);
    }

    button:active:enabled {
      background-color: var(--wui-color-gray-glass-010);
    }
  }

  button:disabled > wui-icon-box {
    opacity: 0.5;
  }

  input {
    border: none;
    outline: none;
    appearance: none;
  }
`;
var colorStyles = css`
  .wui-color-inherit {
    color: var(--wui-color-inherit);
  }

  .wui-color-accent-100 {
    color: var(--wui-color-accent-100);
  }

  .wui-color-error-100 {
    color: var(--wui-color-error-100);
  }

  .wui-color-blue-100 {
    color: var(--wui-color-blue-100);
  }

  .wui-color-blue-90 {
    color: var(--wui-color-blue-90);
  }

  .wui-color-error-125 {
    color: var(--wui-color-error-125);
  }

  .wui-color-success-100 {
    color: var(--wui-color-success-100);
  }

  .wui-color-success-125 {
    color: var(--wui-color-success-125);
  }

  .wui-color-inverse-100 {
    color: var(--wui-color-inverse-100);
  }

  .wui-color-inverse-000 {
    color: var(--wui-color-inverse-000);
  }

  .wui-color-fg-100 {
    color: var(--wui-color-fg-100);
  }

  .wui-color-fg-200 {
    color: var(--wui-color-fg-200);
  }

  .wui-color-fg-300 {
    color: var(--wui-color-fg-300);
  }

  .wui-color-fg-325 {
    color: var(--wui-color-fg-325);
  }

  .wui-color-fg-350 {
    color: var(--wui-color-fg-350);
  }

  .wui-bg-color-inherit {
    background-color: var(--wui-color-inherit);
  }

  .wui-bg-color-blue-100 {
    background-color: var(--wui-color-accent-100);
  }

  .wui-bg-color-error-100 {
    background-color: var(--wui-color-error-100);
  }

  .wui-bg-color-error-125 {
    background-color: var(--wui-color-error-125);
  }

  .wui-bg-color-success-100 {
    background-color: var(--wui-color-success-100);
  }

  .wui-bg-color-success-125 {
    background-color: var(--wui-color-success-100);
  }

  .wui-bg-color-inverse-100 {
    background-color: var(--wui-color-inverse-100);
  }

  .wui-bg-color-inverse-000 {
    background-color: var(--wui-color-inverse-000);
  }

  .wui-bg-color-fg-100 {
    background-color: var(--wui-color-fg-100);
  }

  .wui-bg-color-fg-200 {
    background-color: var(--wui-color-fg-200);
  }

  .wui-bg-color-fg-300 {
    background-color: var(--wui-color-fg-300);
  }

  .wui-color-fg-325 {
    background-color: var(--wui-color-fg-325);
  }

  .wui-color-fg-350 {
    background-color: var(--wui-color-fg-350);
  }
`;

// node_modules/@reown/appkit-ui/dist/esm/src/utils/UiHelperUtil.js
var UiHelperUtil = {
  getSpacingStyles(spacing, index) {
    if (Array.isArray(spacing)) {
      return spacing[index] ? `var(--wui-spacing-${spacing[index]})` : void 0;
    } else if (typeof spacing === "string") {
      return `var(--wui-spacing-${spacing})`;
    }
    return void 0;
  },
  getFormattedDate(date) {
    return new Intl.DateTimeFormat("en-US", { month: "short", day: "numeric" }).format(date);
  },
  getHostName(url) {
    try {
      const newUrl = new URL(url);
      return newUrl.hostname;
    } catch (error) {
      return "";
    }
  },
  getTruncateString({ string, charsStart, charsEnd, truncate }) {
    if (string.length <= charsStart + charsEnd) {
      return string;
    }
    if (truncate === "end") {
      return `${string.substring(0, charsStart)}...`;
    } else if (truncate === "start") {
      return `...${string.substring(string.length - charsEnd)}`;
    }
    return `${string.substring(0, Math.floor(charsStart))}...${string.substring(string.length - Math.floor(charsEnd))}`;
  },
  generateAvatarColors(address) {
    const hash = address.toLowerCase().replace(/^0x/iu, "").replace(/[^a-f0-9]/gu, "");
    const baseColor = hash.substring(0, 6).padEnd(6, "0");
    const rgbColor = this.hexToRgb(baseColor);
    const masterBorderRadius = getComputedStyle(document.documentElement).getPropertyValue("--w3m-border-radius-master");
    const radius = Number(masterBorderRadius == null ? void 0 : masterBorderRadius.replace("px", ""));
    const edge = 100 - 3 * radius;
    const gradientCircle = `${edge}% ${edge}% at 65% 40%`;
    const colors = [];
    for (let i3 = 0; i3 < 5; i3 += 1) {
      const tintedColor = this.tintColor(rgbColor, 0.15 * i3);
      colors.push(`rgb(${tintedColor[0]}, ${tintedColor[1]}, ${tintedColor[2]})`);
    }
    return `
    --local-color-1: ${colors[0]};
    --local-color-2: ${colors[1]};
    --local-color-3: ${colors[2]};
    --local-color-4: ${colors[3]};
    --local-color-5: ${colors[4]};
    --local-radial-circle: ${gradientCircle}
   `;
  },
  hexToRgb(hex) {
    const bigint = parseInt(hex, 16);
    const r2 = bigint >> 16 & 255;
    const g = bigint >> 8 & 255;
    const b = bigint & 255;
    return [r2, g, b];
  },
  tintColor(rgb, tint) {
    const [r2, g, b] = rgb;
    const tintedR = Math.round(r2 + (255 - r2) * tint);
    const tintedG = Math.round(g + (255 - g) * tint);
    const tintedB = Math.round(b + (255 - b) * tint);
    return [tintedR, tintedG, tintedB];
  },
  isNumber(character) {
    const regex = {
      number: /^[0-9]+$/u
    };
    return regex.number.test(character);
  },
  getColorTheme(theme) {
    var _a;
    if (theme) {
      return theme;
    } else if (typeof window !== "undefined" && window.matchMedia) {
      if ((_a = window.matchMedia("(prefers-color-scheme: dark)")) == null ? void 0 : _a.matches) {
        return "dark";
      }
      return "light";
    }
    return "dark";
  },
  splitBalance(input) {
    const parts = input.split(".");
    if (parts.length === 2) {
      return [parts[0], parts[1]];
    }
    return ["0", "00"];
  },
  roundNumber(number, threshold, fixed) {
    const roundedNumber = number.toString().length >= threshold ? Number(number).toFixed(fixed) : number;
    return roundedNumber;
  },
  formatNumberToLocalString(value, decimals = 2) {
    if (value === void 0) {
      return "0.00";
    }
    if (typeof value === "number") {
      return value.toLocaleString("en-US", {
        maximumFractionDigits: decimals,
        minimumFractionDigits: decimals
      });
    }
    return parseFloat(value).toLocaleString("en-US", {
      maximumFractionDigits: decimals,
      minimumFractionDigits: decimals
    });
  }
};

// node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js
function standardCustomElement(tagName, descriptor) {
  const { kind, elements } = descriptor;
  return {
    kind,
    elements,
    finisher(clazz) {
      if (!customElements.get(tagName)) {
        customElements.define(tagName, clazz);
      }
    }
  };
}
function legacyCustomElement(tagName, clazz) {
  if (!customElements.get(tagName)) {
    customElements.define(tagName, clazz);
  }
  return clazz;
}
function customElement(tagName) {
  return function create2(classOrDescriptor) {
    return typeof classOrDescriptor === "function" ? legacyCustomElement(tagName, classOrDescriptor) : standardCustomElement(tagName, classOrDescriptor);
  };
}

// node_modules/@reown/appkit-scaffold-ui/dist/esm/src/utils/ConstantsUtil.js
var ConstantsUtil3 = {
  ACCOUNT_TABS: [{ label: "Tokens" }, { label: "NFTs" }, { label: "Activity" }],
  SECURE_SITE_ORIGIN: (typeof process !== "undefined" && typeof process.env !== "undefined" ? process.env["NEXT_PUBLIC_SECURE_SITE_ORIGIN"] : void 0) || "https://secure.walletconnect.org",
  VIEW_DIRECTION: {
    Next: "next",
    Prev: "prev"
  },
  DEFAULT_CONNECT_METHOD_ORDER: ["email", "social", "wallet"],
  ANIMATION_DURATIONS: {
    HeaderText: 120,
    ModalHeight: 150,
    ViewTransition: 150
  }
};

export {
  ConstantsUtil,
  NetworkUtil,
  ParseUtil,
  proxy,
  subscribe,
  ref,
  subscribeKey,
  ConstantsUtil2,
  StorageUtil,
  CoreHelperUtil,
  OptionsController,
  AssetController,
  AssetUtil,
  AlertController,
  EventsController,
  ApiController,
  RouterController,
  ThemeController,
  ConnectorController,
  SnackController,
  ConnectionController,
  PublicStateController,
  SendController,
  ChainController,
  BlockchainApiController,
  AccountController,
  ModalController,
  OnRampController,
  TooltipController,
  EnsController,
  SIWXUtil,
  ModalUtil,
  ConstantsUtil3,
  initializeTheming,
  setColorTheme,
  setThemeVariables,
  resetStyles,
  elementStyles,
  colorStyles,
  UiHelperUtil,
  customElement
};
/*! Bundled license information:

@walletconnect/utils/dist/index.es.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)
*/
//# sourceMappingURL=chunk-WB3F7RHB.js.map
