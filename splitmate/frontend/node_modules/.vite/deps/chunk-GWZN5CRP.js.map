{"version": 3, "sources": ["../../@lit/reactive-element/src/decorators/property.ts", "../../@lit/reactive-element/src/decorators/state.ts", "../../@lit/reactive-element/src/decorators/query.ts", "../../@reown/appkit-ui/src/layout/wui-flex/styles.ts", "../../@reown/appkit-ui/src/layout/wui-flex/index.ts", "../../lit-html/src/directives/if-defined.ts", "../../lit-html/src/directive.ts", "../../lit-html/src/directives/class-map.ts", "../../@reown/appkit-ui/src/components/wui-text/styles.ts", "../../@reown/appkit-ui/src/components/wui-text/index.ts", "../../lit-html/src/directive-helpers.ts", "../../lit-html/src/async-directive.ts", "../../lit-html/src/directives/private-async-helpers.ts", "../../lit-html/src/directives/until.ts", "../../@reown/appkit-ui/src/utils/CacheUtil.ts", "../../@reown/appkit-ui/src/components/wui-icon/styles.ts", "../../@reown/appkit-ui/src/components/wui-icon/index.ts", "../../@reown/appkit-ui/src/composites/wui-icon-box/styles.ts", "../../@reown/appkit-ui/src/composites/wui-icon-box/index.ts", "../../@reown/appkit-ui/src/components/wui-image/styles.ts", "../../@reown/appkit-ui/src/components/wui-image/index.ts", "../../@reown/appkit-ui/src/composites/wui-tag/styles.ts", "../../@reown/appkit-ui/src/composites/wui-tag/index.ts", "../../@reown/appkit-ui/src/components/wui-loading-spinner/styles.ts", "../../@reown/appkit-ui/src/components/wui-loading-spinner/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {\n  type PropertyDeclaration,\n  type ReactiveElement,\n  defaultConverter,\n  notEqual,\n} from '../reactive-element.js';\nimport type {Interface} from './base.js';\n\nconst DEV_MODE = true;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  globalThis.litIssuedWarnings ??= new Set();\n\n  /**\n   * Issue a warning if we haven't already, based either on `code` or `warning`.\n   * Warnings are disabled automatically only by `warning`; disabling via `code`\n   * can be done by users.\n   */\n  issueWarning = (code: string, warning: string) => {\n    warning += ` See https://lit.dev/msg/${code} for more information.`;\n    if (\n      !globalThis.litIssuedWarnings!.has(warning) &&\n      !globalThis.litIssuedWarnings!.has(code)\n    ) {\n      console.warn(warning);\n      globalThis.litIssuedWarnings!.add(warning);\n    }\n  };\n}\n\n// Overloads for property decorator so that TypeScript can infer the correct\n// return type when a decorator is used as an accessor decorator or a setter\n// decorator.\nexport type PropertyDecorator = {\n  // accessor decorator signature\n  <C extends Interface<ReactiveElement>, V>(\n    target: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): ClassAccessorDecoratorResult<C, V>;\n\n  // setter decorator signature\n  <C extends Interface<ReactiveElement>, V>(\n    target: (value: V) => void,\n    context: ClassSetterDecoratorContext<C, V>\n  ): (this: C, value: V) => void;\n\n  // legacy decorator signature\n  (\n    protoOrDescriptor: Object,\n    name: PropertyKey,\n    descriptor?: PropertyDescriptor\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): any;\n};\n\nconst legacyProperty = (\n  options: PropertyDeclaration | undefined,\n  proto: Object,\n  name: PropertyKey\n) => {\n  const hasOwnProperty = proto.hasOwnProperty(name);\n  (proto.constructor as typeof ReactiveElement).createProperty(name, options);\n  // For accessors (which have a descriptor on the prototype) we need to\n  // return a descriptor, otherwise TypeScript overwrites the descriptor we\n  // define in createProperty() with the original descriptor. We don't do this\n  // for fields, which don't have a descriptor, because this could overwrite\n  // descriptor defined by other decorators.\n  return hasOwnProperty\n    ? Object.getOwnPropertyDescriptor(proto, name)\n    : undefined;\n};\n\n// This is duplicated from a similar variable in reactive-element.ts, but\n// actually makes sense to have this default defined with the decorator, so\n// that different decorators could have different defaults.\nconst defaultPropertyDeclaration: PropertyDeclaration = {\n  attribute: true,\n  type: String,\n  converter: defaultConverter,\n  reflect: false,\n  hasChanged: notEqual,\n};\n\n// Temporary type, until google3 is on TypeScript 5.2\ntype StandardPropertyContext<C, V> = (\n  | ClassAccessorDecoratorContext<C, V>\n  | ClassSetterDecoratorContext<C, V>\n) & {metadata: object};\n\n/**\n * Wraps a class accessor or setter so that `requestUpdate()` is called with the\n * property name and old value when the accessor is set.\n */\nexport const standardProperty = <C extends Interface<ReactiveElement>, V>(\n  options: PropertyDeclaration = defaultPropertyDeclaration,\n  target: ClassAccessorDecoratorTarget<C, V> | ((value: V) => void),\n  context: StandardPropertyContext<C, V>\n): ClassAccessorDecoratorResult<C, V> | ((this: C, value: V) => void) => {\n  const {kind, metadata} = context;\n\n  if (DEV_MODE && metadata == null) {\n    issueWarning(\n      'missing-class-metadata',\n      `The class ${target} is missing decorator metadata. This ` +\n        `could mean that you're using a compiler that supports decorators ` +\n        `but doesn't support decorator metadata, such as TypeScript 5.1. ` +\n        `Please update your compiler.`\n    );\n  }\n\n  // Store the property options\n  let properties = globalThis.litPropertyMetadata.get(metadata);\n  if (properties === undefined) {\n    globalThis.litPropertyMetadata.set(metadata, (properties = new Map()));\n  }\n  if (kind === 'setter') {\n    options = Object.create(options);\n    options.wrapped = true;\n  }\n  properties.set(context.name, options);\n\n  if (kind === 'accessor') {\n    // Standard decorators cannot dynamically modify the class, so we can't\n    // replace a field with accessors. The user must use the new `accessor`\n    // keyword instead.\n    const {name} = context;\n    return {\n      set(this: ReactiveElement, v: V) {\n        const oldValue = (\n          target as ClassAccessorDecoratorTarget<C, V>\n        ).get.call(this as unknown as C);\n        (target as ClassAccessorDecoratorTarget<C, V>).set.call(\n          this as unknown as C,\n          v\n        );\n        this.requestUpdate(name, oldValue, options);\n      },\n      init(this: ReactiveElement, v: V): V {\n        if (v !== undefined) {\n          this._$changeProperty(name, undefined, options, v);\n        }\n        return v;\n      },\n    } as unknown as ClassAccessorDecoratorResult<C, V>;\n  } else if (kind === 'setter') {\n    const {name} = context;\n    return function (this: ReactiveElement, value: V) {\n      const oldValue = this[name as keyof ReactiveElement];\n      (target as (value: V) => void).call(this, value);\n      this.requestUpdate(name, oldValue, options);\n    } as unknown as (this: C, value: V) => void;\n  }\n  throw new Error(`Unsupported decorator location: ${kind}`);\n};\n\n/**\n * A class field or accessor decorator which creates a reactive property that\n * reflects a corresponding attribute value. When a decorated property is set\n * the element will update and render. A {@linkcode PropertyDeclaration} may\n * optionally be supplied to configure property features.\n *\n * This decorator should only be used for public fields. As public fields,\n * properties should be considered as primarily settable by element users,\n * either via attribute or the property itself.\n *\n * Generally, properties that are changed by the element should be private or\n * protected fields and should use the {@linkcode state} decorator.\n *\n * However, sometimes element code does need to set a public property. This\n * should typically only be done in response to user interaction, and an event\n * should be fired informing the user; for example, a checkbox sets its\n * `checked` property when clicked and fires a `changed` event. Mutating public\n * properties should typically not be done for non-primitive (object or array)\n * properties. In other cases when an element needs to manage state, a private\n * property decorated via the {@linkcode state} decorator should be used. When\n * needed, state properties can be initialized via public properties to\n * facilitate complex interactions.\n *\n * ```ts\n * class MyElement {\n *   @property({ type: Boolean })\n *   clicked = false;\n * }\n * ```\n * @category Decorator\n * @ExportDecoratedItems\n */\nexport function property(options?: PropertyDeclaration): PropertyDecorator {\n  return <C extends Interface<ReactiveElement>, V>(\n    protoOrTarget:\n      | object\n      | ClassAccessorDecoratorTarget<C, V>\n      | ((value: V) => void),\n    nameOrContext:\n      | PropertyKey\n      | ClassAccessorDecoratorContext<C, V>\n      | ClassSetterDecoratorContext<C, V>\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): any => {\n    return (\n      typeof nameOrContext === 'object'\n        ? standardProperty<C, V>(\n            options,\n            protoOrTarget as\n              | ClassAccessorDecoratorTarget<C, V>\n              | ((value: V) => void),\n            nameOrContext as StandardPropertyContext<C, V>\n          )\n        : legacyProperty(\n            options,\n            protoOrTarget as Object,\n            nameOrContext as PropertyKey\n          )\n    ) as PropertyDecorator;\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {property} from './property.js';\n\nexport interface StateDeclaration<Type = unknown> {\n  /**\n   * A function that indicates if a property should be considered changed when\n   * it is set. The function should take the `newValue` and `oldValue` and\n   * return `true` if an update should be requested.\n   */\n  hasChanged?(value: Type, oldValue: Type): boolean;\n}\n\n/**\n * @deprecated use StateDeclaration\n */\nexport type InternalPropertyDeclaration<Type = unknown> =\n  StateDeclaration<Type>;\n\n/**\n * Declares a private or protected reactive property that still triggers\n * updates to the element when it changes. It does not reflect from the\n * corresponding attribute.\n *\n * Properties declared this way must not be used from HTML or HTML templating\n * systems, they're solely for properties internal to the element. These\n * properties may be renamed by optimization tools like closure compiler.\n * @category Decorator\n */\nexport function state(options?: StateDeclaration) {\n  return property({\n    ...options,\n    // Add both `state` and `attribute` because we found a third party\n    // controller that is keying off of PropertyOptions.state to determine\n    // whether a field is a private internal property or not.\n    state: true,\n    attribute: false,\n  });\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nimport type {ReactiveElement} from '../reactive-element.js';\nimport {desc, type Interface} from './base.js';\n\nconst DEV_MODE = true;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  globalThis.litIssuedWarnings ??= new Set();\n\n  /**\n   * Issue a warning if we haven't already, based either on `code` or `warning`.\n   * Warnings are disabled automatically only by `warning`; disabling via `code`\n   * can be done by users.\n   */\n  issueWarning = (code: string, warning: string) => {\n    warning += code\n      ? ` See https://lit.dev/msg/${code} for more information.`\n      : '';\n    if (\n      !globalThis.litIssuedWarnings!.has(warning) &&\n      !globalThis.litIssuedWarnings!.has(code)\n    ) {\n      console.warn(warning);\n      globalThis.litIssuedWarnings!.add(warning);\n    }\n  };\n}\n\nexport type QueryDecorator = {\n  // legacy\n  (\n    proto: Interface<ReactiveElement>,\n    name: PropertyKey,\n    descriptor?: PropertyDescriptor\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any;\n\n  // standard\n  <C extends Interface<ReactiveElement>, V extends Element | null>(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): ClassAccessorDecoratorResult<C, V>;\n};\n\n/**\n * A property decorator that converts a class property into a getter that\n * executes a querySelector on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n * @param cache An optional boolean which when true performs the DOM query only\n *     once and caches the result.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @query('#first')\n *   first: HTMLDivElement;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function query(selector: string, cache?: boolean): QueryDecorator {\n  return (<C extends Interface<ReactiveElement>, V extends Element | null>(\n    protoOrTarget: ClassAccessorDecoratorTarget<C, V>,\n    nameOrContext: PropertyKey | ClassAccessorDecoratorContext<C, V>,\n    descriptor?: PropertyDescriptor\n  ) => {\n    const doQuery = (el: Interface<ReactiveElement>): V => {\n      const result = (el.renderRoot?.querySelector(selector) ?? null) as V;\n      if (DEV_MODE && result === null && cache && !el.hasUpdated) {\n        const name =\n          typeof nameOrContext === 'object'\n            ? nameOrContext.name\n            : nameOrContext;\n        issueWarning(\n          '',\n          `@query'd field ${JSON.stringify(String(name))} with the 'cache' ` +\n            `flag set for selector '${selector}' has been accessed before ` +\n            `the first update and returned null. This is expected if the ` +\n            `renderRoot tree has not been provided beforehand (e.g. via ` +\n            `Declarative Shadow DOM). Therefore the value hasn't been cached.`\n        );\n      }\n      // TODO: if we want to allow users to assert that the query will never\n      // return null, we need a new option and to throw here if the result\n      // is null.\n      return result;\n    };\n    if (cache) {\n      // Accessors to wrap from either:\n      //   1. The decorator target, in the case of standard decorators\n      //   2. The property descriptor, in the case of experimental decorators\n      //      on auto-accessors.\n      //   3. Functions that access our own cache-key property on the instance,\n      //      in the case of experimental decorators on fields.\n      const {get, set} =\n        typeof nameOrContext === 'object'\n          ? protoOrTarget\n          : descriptor ??\n            (() => {\n              const key = DEV_MODE\n                ? Symbol(`${String(nameOrContext)} (@query() cache)`)\n                : Symbol();\n              type WithCache = ReactiveElement & {\n                [key: symbol]: Element | null;\n              };\n              return {\n                get() {\n                  return (this as WithCache)[key];\n                },\n                set(v) {\n                  (this as WithCache)[key] = v;\n                },\n              };\n            })();\n      return desc(protoOrTarget, nameOrContext, {\n        get(this: ReactiveElement): V {\n          let result: V = get!.call(this);\n          if (result === undefined) {\n            result = doQuery(this);\n            if (result !== null || this.hasUpdated) {\n              set!.call(this, result);\n            }\n          }\n          return result;\n        },\n      });\n    } else {\n      // This object works as the return type for both standard and\n      // experimental decorators.\n      return desc(protoOrTarget, nameOrContext, {\n        get(this: ReactiveElement) {\n          return doQuery(this);\n        },\n      });\n    }\n  }) as QueryDecorator;\n}\n", null, null, "/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {nothing} from '../lit-html.js';\n\n/**\n * For AttributeParts, sets the attribute if the value is defined and removes\n * the attribute if the value is undefined.\n *\n * For other part types, this directive is a no-op.\n */\nexport const ifDefined = <T>(value: T) => value ?? nothing;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {Disconnectable, Part} from './lit-html.js';\n\nexport {\n  AttributePart,\n  BooleanAttributePart,\n  ChildPart,\n  ElementPart,\n  EventPart,\n  Part,\n  PropertyPart,\n} from './lit-html.js';\n\nexport interface DirectiveClass {\n  new (part: PartInfo): Directive;\n}\n\n/**\n * This utility type extracts the signature of a directive class's render()\n * method so we can use it for the type of the generated directive function.\n */\nexport type DirectiveParameters<C extends Directive> = Parameters<C['render']>;\n\n/**\n * A generated directive function doesn't evaluate the directive, but just\n * returns a DirectiveResult object that captures the arguments.\n */\nexport interface DirectiveResult<C extends DirectiveClass = DirectiveClass> {\n  /**\n   * This property needs to remain unminified.\n   * @internal\n   */\n  ['_$litDirective$']: C;\n  /** @internal */\n  values: DirectiveParameters<InstanceType<C>>;\n}\n\nexport const PartType = {\n  ATTRIBUTE: 1,\n  CHILD: 2,\n  PROPERTY: 3,\n  BOOLEAN_ATTRIBUTE: 4,\n  EVENT: 5,\n  ELEMENT: 6,\n} as const;\n\nexport type PartType = (typeof PartType)[keyof typeof PartType];\n\nexport interface ChildPartInfo {\n  readonly type: typeof PartType.CHILD;\n}\n\nexport interface AttributePartInfo {\n  readonly type:\n    | typeof PartType.ATTRIBUTE\n    | typeof PartType.PROPERTY\n    | typeof PartType.BOOLEAN_ATTRIBUTE\n    | typeof PartType.EVENT;\n  readonly strings?: ReadonlyArray<string>;\n  readonly name: string;\n  readonly tagName: string;\n}\n\nexport interface ElementPartInfo {\n  readonly type: typeof PartType.ELEMENT;\n}\n\n/**\n * Information about the part a directive is bound to.\n *\n * This is useful for checking that a directive is attached to a valid part,\n * such as with directive that can only be used on attribute bindings.\n */\nexport type PartInfo = ChildPartInfo | AttributePartInfo | ElementPartInfo;\n\n/**\n * Creates a user-facing directive function from a Directive class. This\n * function has the same parameters as the directive's render() method.\n */\nexport const directive =\n  <C extends DirectiveClass>(c: C) =>\n  (...values: DirectiveParameters<InstanceType<C>>): DirectiveResult<C> => ({\n    // This property needs to remain unminified.\n    ['_$litDirective$']: c,\n    values,\n  });\n\n/**\n * Base class for creating custom directives. Users should extend this class,\n * implement `render` and/or `update`, and then pass their subclass to\n * `directive`.\n */\nexport abstract class Directive implements Disconnectable {\n  //@internal\n  __part!: Part;\n  //@internal\n  __attributeIndex: number | undefined;\n  //@internal\n  __directive?: Directive;\n\n  //@internal\n  _$parent!: Disconnectable;\n\n  // These will only exist on the AsyncDirective subclass\n  //@internal\n  _$disconnectableChildren?: Set<Disconnectable>;\n  // This property needs to remain unminified.\n  //@internal\n  ['_$notifyDirectiveConnectionChanged']?(isConnected: boolean): void;\n\n  constructor(_partInfo: PartInfo) {}\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  /** @internal */\n  _$initialize(\n    part: Part,\n    parent: Disconnectable,\n    attributeIndex: number | undefined\n  ) {\n    this.__part = part;\n    this._$parent = parent;\n    this.__attributeIndex = attributeIndex;\n  }\n  /** @internal */\n  _$resolve(part: Part, props: Array<unknown>): unknown {\n    return this.update(part, props);\n  }\n\n  abstract render(...props: Array<unknown>): unknown;\n\n  update(_part: Part, props: Array<unknown>): unknown {\n    return this.render(...props);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {AttributePart, noChange} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\n\n/**\n * A key-value set of class names to truthy values.\n */\nexport interface ClassInfo {\n  readonly [name: string]: string | boolean | number;\n}\n\nclass ClassMapDirective extends Directive {\n  /**\n   * Stores the ClassInfo object applied to a given AttributePart.\n   * Used to unset existing values when a new ClassInfo object is applied.\n   */\n  private _previousClasses?: Set<string>;\n  private _staticClasses?: Set<string>;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (\n      partInfo.type !== PartType.ATTRIBUTE ||\n      partInfo.name !== 'class' ||\n      (partInfo.strings?.length as number) > 2\n    ) {\n      throw new Error(\n        '`classMap()` can only be used in the `class` attribute ' +\n          'and must be the only part in the attribute.'\n      );\n    }\n  }\n\n  render(classInfo: ClassInfo) {\n    // Add spaces to ensure separation from static classes\n    return (\n      ' ' +\n      Object.keys(classInfo)\n        .filter((key) => classInfo[key])\n        .join(' ') +\n      ' '\n    );\n  }\n\n  override update(part: AttributePart, [classInfo]: DirectiveParameters<this>) {\n    // Remember dynamic classes on the first render\n    if (this._previousClasses === undefined) {\n      this._previousClasses = new Set();\n      if (part.strings !== undefined) {\n        this._staticClasses = new Set(\n          part.strings\n            .join(' ')\n            .split(/\\s/)\n            .filter((s) => s !== '')\n        );\n      }\n      for (const name in classInfo) {\n        if (classInfo[name] && !this._staticClasses?.has(name)) {\n          this._previousClasses.add(name);\n        }\n      }\n      return this.render(classInfo);\n    }\n\n    const classList = part.element.classList;\n\n    // Remove old classes that no longer apply\n    for (const name of this._previousClasses) {\n      if (!(name in classInfo)) {\n        classList.remove(name);\n        this._previousClasses!.delete(name);\n      }\n    }\n\n    // Add or remove classes based on their classMap value\n    for (const name in classInfo) {\n      // We explicitly want a loose truthy check of `value` because it seems\n      // more convenient that '' and 0 are skipped.\n      const value = !!classInfo[name];\n      if (\n        value !== this._previousClasses.has(name) &&\n        !this._staticClasses?.has(name)\n      ) {\n        if (value) {\n          classList.add(name);\n          this._previousClasses.add(name);\n        } else {\n          classList.remove(name);\n          this._previousClasses.delete(name);\n        }\n      }\n    }\n    return noChange;\n  }\n}\n\n/**\n * A directive that applies dynamic CSS classes.\n *\n * This must be used in the `class` attribute and must be the only part used in\n * the attribute. It takes each property in the `classInfo` argument and adds\n * the property name to the element's `classList` if the property value is\n * truthy; if the property value is falsy, the property name is removed from\n * the element's `class`.\n *\n * For example `{foo: bar}` applies the class `foo` if the value of `bar` is\n * truthy.\n *\n * @param classInfo\n */\nexport const classMap = directive(ClassMapDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {ClassMapDirective};\n", null, null, "/**\n * @license\n * Copyright 2020 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {\n  _$LH,\n  Part,\n  DirectiveParent,\n  CompiledTemplateResult,\n  MaybeCompiledTemplateResult,\n  UncompiledTemplateResult,\n} from './lit-html.js';\nimport {\n  DirectiveResult,\n  DirectiveClass,\n  PartInfo,\n  AttributePartInfo,\n} from './directive.js';\ntype Primitive = null | undefined | boolean | number | string | symbol | bigint;\n\nconst {_ChildPart: ChildPart} = _$LH;\n\ntype ChildPart = InstanceType<typeof ChildPart>;\n\nconst ENABLE_SHADYDOM_NOPATCH = true;\n\nconst wrap =\n  ENABLE_SHADYDOM_NOPATCH &&\n  window.ShadyDOM?.inUse &&\n  window.ShadyDOM?.noPatch === true\n    ? window.ShadyDOM!.wrap\n    : (node: Node) => node;\n\n/**\n * Tests if a value is a primitive value.\n *\n * See https://tc39.github.io/ecma262/#sec-typeof-operator\n */\nexport const isPrimitive = (value: unknown): value is Primitive =>\n  value === null || (typeof value != 'object' && typeof value != 'function');\n\nexport const TemplateResultType = {\n  HTML: 1,\n  SVG: 2,\n  MATHML: 3,\n} as const;\n\nexport type TemplateResultType =\n  (typeof TemplateResultType)[keyof typeof TemplateResultType];\n\ntype IsTemplateResult = {\n  (val: unknown): val is MaybeCompiledTemplateResult;\n  <T extends TemplateResultType>(\n    val: unknown,\n    type: T\n  ): val is UncompiledTemplateResult<T>;\n};\n\n/**\n * Tests if a value is a TemplateResult or a CompiledTemplateResult.\n */\nexport const isTemplateResult: IsTemplateResult = (\n  value: unknown,\n  type?: TemplateResultType\n): value is UncompiledTemplateResult =>\n  type === undefined\n    ? // This property needs to remain unminified.\n      (value as UncompiledTemplateResult)?.['_$litType$'] !== undefined\n    : (value as UncompiledTemplateResult)?.['_$litType$'] === type;\n\n/**\n * Tests if a value is a CompiledTemplateResult.\n */\nexport const isCompiledTemplateResult = (\n  value: unknown\n): value is CompiledTemplateResult => {\n  return (value as CompiledTemplateResult)?.['_$litType$']?.h != null;\n};\n\n/**\n * Tests if a value is a DirectiveResult.\n */\nexport const isDirectiveResult = (value: unknown): value is DirectiveResult =>\n  // This property needs to remain unminified.\n  (value as DirectiveResult)?.['_$litDirective$'] !== undefined;\n\n/**\n * Retrieves the Directive class for a DirectiveResult\n */\nexport const getDirectiveClass = (value: unknown): DirectiveClass | undefined =>\n  // This property needs to remain unminified.\n  (value as DirectiveResult)?.['_$litDirective$'];\n\n/**\n * Tests whether a part has only a single-expression with no strings to\n * interpolate between.\n *\n * Only AttributePart and PropertyPart can have multiple expressions.\n * Multi-expression parts have a `strings` property and single-expression\n * parts do not.\n */\nexport const isSingleExpression = (part: PartInfo) =>\n  (part as AttributePartInfo).strings === undefined;\n\nconst createMarker = () => document.createComment('');\n\n/**\n * Inserts a ChildPart into the given container ChildPart's DOM, either at the\n * end of the container ChildPart, or before the optional `refPart`.\n *\n * This does not add the part to the containerPart's committed value. That must\n * be done by callers.\n *\n * @param containerPart Part within which to add the new ChildPart\n * @param refPart Part before which to add the new ChildPart; when omitted the\n *     part added to the end of the `containerPart`\n * @param part Part to insert, or undefined to create a new part\n */\nexport const insertPart = (\n  containerPart: ChildPart,\n  refPart?: ChildPart,\n  part?: ChildPart\n): ChildPart => {\n  const container = wrap(containerPart._$startNode).parentNode!;\n\n  const refNode =\n    refPart === undefined ? containerPart._$endNode : refPart._$startNode;\n\n  if (part === undefined) {\n    const startNode = wrap(container).insertBefore(createMarker(), refNode);\n    const endNode = wrap(container).insertBefore(createMarker(), refNode);\n    part = new ChildPart(\n      startNode,\n      endNode,\n      containerPart,\n      containerPart.options\n    );\n  } else {\n    const endNode = wrap(part._$endNode!).nextSibling;\n    const oldParent = part._$parent;\n    const parentChanged = oldParent !== containerPart;\n    if (parentChanged) {\n      part._$reparentDisconnectables?.(containerPart);\n      // Note that although `_$reparentDisconnectables` updates the part's\n      // `_$parent` reference after unlinking from its current parent, that\n      // method only exists if Disconnectables are present, so we need to\n      // unconditionally set it here\n      part._$parent = containerPart;\n      // Since the _$isConnected getter is somewhat costly, only\n      // read it once we know the subtree has directives that need\n      // to be notified\n      let newConnectionState;\n      if (\n        part._$notifyConnectionChanged !== undefined &&\n        (newConnectionState = containerPart._$isConnected) !==\n          oldParent!._$isConnected\n      ) {\n        part._$notifyConnectionChanged(newConnectionState);\n      }\n    }\n    if (endNode !== refNode || parentChanged) {\n      let start: Node | null = part._$startNode;\n      while (start !== endNode) {\n        const n: Node | null = wrap(start!).nextSibling;\n        wrap(container).insertBefore(start!, refNode);\n        start = n;\n      }\n    }\n  }\n\n  return part;\n};\n\n/**\n * Sets the value of a Part.\n *\n * Note that this should only be used to set/update the value of user-created\n * parts (i.e. those created using `insertPart`); it should not be used\n * by directives to set the value of the directive's container part. Directives\n * should return a value from `update`/`render` to update their part state.\n *\n * For directives that require setting their part value asynchronously, they\n * should extend `AsyncDirective` and call `this.setValue()`.\n *\n * @param part Part to set\n * @param value Value to set\n * @param index For `AttributePart`s, the index to set\n * @param directiveParent Used internally; should not be set by user\n */\nexport const setChildPartValue = <T extends ChildPart>(\n  part: T,\n  value: unknown,\n  directiveParent: DirectiveParent = part\n): T => {\n  part._$setValue(value, directiveParent);\n  return part;\n};\n\n// A sentinel value that can never appear as a part value except when set by\n// live(). Used to force a dirty-check to fail and cause a re-render.\nconst RESET_VALUE = {};\n\n/**\n * Sets the committed value of a ChildPart directly without triggering the\n * commit stage of the part.\n *\n * This is useful in cases where a directive needs to update the part such\n * that the next update detects a value change or not. When value is omitted,\n * the next update will be guaranteed to be detected as a change.\n *\n * @param part\n * @param value\n */\nexport const setCommittedValue = (part: Part, value: unknown = RESET_VALUE) =>\n  (part._$committedValue = value);\n\n/**\n * Returns the committed value of a ChildPart.\n *\n * The committed value is used for change detection and efficient updates of\n * the part. It can differ from the value set by the template or directive in\n * cases where the template value is transformed before being committed.\n *\n * - `TemplateResult`s are committed as a `TemplateInstance`\n * - Iterables are committed as `Array<ChildPart>`\n * - All other types are committed as the template value or value returned or\n *   set by a directive.\n *\n * @param part\n */\nexport const getCommittedValue = (part: ChildPart) => part._$committedValue;\n\n/**\n * Removes a ChildPart from the DOM, including any of its content.\n *\n * @param part The Part to remove\n */\nexport const removePart = (part: ChildPart) => {\n  part._$notifyConnectionChanged?.(false, true);\n  let start: ChildNode | null = part._$startNode;\n  const end: ChildNode | null = wrap(part._$endNode!).nextSibling;\n  while (start !== end) {\n    const n: ChildNode | null = wrap(start!).nextSibling;\n    (wrap(start!) as ChildNode).remove();\n    start = n;\n  }\n};\n\nexport const clearPart = (part: ChildPart) => {\n  part._$clear();\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Overview:\n *\n * This module is designed to add support for an async `setValue` API and\n * `disconnected` callback to directives with the least impact on the core\n * runtime or payload when that feature is not used.\n *\n * The strategy is to introduce a `AsyncDirective` subclass of\n * `Directive` that climbs the \"parent\" tree in its constructor to note which\n * branches of lit-html's \"logical tree\" of data structures contain such\n * directives and thus need to be crawled when a subtree is being cleared (or\n * manually disconnected) in order to run the `disconnected` callback.\n *\n * The \"nodes\" of the logical tree include Parts, TemplateInstances (for when a\n * TemplateResult is committed to a value of a ChildPart), and Directives; these\n * all implement a common interface called `DisconnectableChild`. Each has a\n * `_$parent` reference which is set during construction in the core code, and a\n * `_$disconnectableChildren` field which is initially undefined.\n *\n * The sparse tree created by means of the `AsyncDirective` constructor\n * crawling up the `_$parent` tree and placing a `_$disconnectableChildren` Set\n * on each parent that includes each child that contains a\n * `AsyncDirective` directly or transitively via its children. In order to\n * notify connection state changes and disconnect (or reconnect) a tree, the\n * `_$notifyConnectionChanged` API is patched onto ChildParts as a directive\n * climbs the parent tree, which is called by the core when clearing a part if\n * it exists. When called, that method iterates over the sparse tree of\n * Set<DisconnectableChildren> built up by AsyncDirectives, and calls\n * `_$notifyDirectiveConnectionChanged` on any directives that are encountered\n * in that tree, running the required callbacks.\n *\n * A given \"logical tree\" of lit-html data-structures might look like this:\n *\n *  ChildPart(N1) _$dC=[D2,T3]\n *   ._directive\n *     AsyncDirective(D2)\n *   ._value // user value was TemplateResult\n *     TemplateInstance(T3) _$dC=[A4,A6,N10,N12]\n *      ._$parts[]\n *        AttributePart(A4) _$dC=[D5]\n *         ._directives[]\n *           AsyncDirective(D5)\n *        AttributePart(A6) _$dC=[D7,D8]\n *         ._directives[]\n *           AsyncDirective(D7)\n *           Directive(D8) _$dC=[D9]\n *            ._directive\n *              AsyncDirective(D9)\n *        ChildPart(N10) _$dC=[D11]\n *         ._directive\n *           AsyncDirective(D11)\n *         ._value\n *           string\n *        ChildPart(N12) _$dC=[D13,N14,N16]\n *         ._directive\n *           AsyncDirective(D13)\n *         ._value // user value was iterable\n *           Array<ChildPart>\n *             ChildPart(N14) _$dC=[D15]\n *              ._value\n *                string\n *             ChildPart(N16) _$dC=[D17,T18]\n *              ._directive\n *                AsyncDirective(D17)\n *              ._value // user value was TemplateResult\n *                TemplateInstance(T18) _$dC=[A19,A21,N25]\n *                 ._$parts[]\n *                   AttributePart(A19) _$dC=[D20]\n *                    ._directives[]\n *                      AsyncDirective(D20)\n *                   AttributePart(A21) _$dC=[22,23]\n *                    ._directives[]\n *                      AsyncDirective(D22)\n *                      Directive(D23) _$dC=[D24]\n *                       ._directive\n *                         AsyncDirective(D24)\n *                   ChildPart(N25) _$dC=[D26]\n *                    ._directive\n *                      AsyncDirective(D26)\n *                    ._value\n *                      string\n *\n * Example 1: The directive in ChildPart(N12) updates and returns `nothing`. The\n * ChildPart will _clear() itself, and so we need to disconnect the \"value\" of\n * the ChildPart (but not its directive). In this case, when `_clear()` calls\n * `_$notifyConnectionChanged()`, we don't iterate all of the\n * _$disconnectableChildren, rather we do a value-specific disconnection: i.e.\n * since the _value was an Array<ChildPart> (because an iterable had been\n * committed), we iterate the array of ChildParts (N14, N16) and run\n * `setConnected` on them (which does recurse down the full tree of\n * `_$disconnectableChildren` below it, and also removes N14 and N16 from N12's\n * `_$disconnectableChildren`). Once the values have been disconnected, we then\n * check whether the ChildPart(N12)'s list of `_$disconnectableChildren` is empty\n * (and would remove it from its parent TemplateInstance(T3) if so), but since\n * it would still contain its directive D13, it stays in the disconnectable\n * tree.\n *\n * Example 2: In the course of Example 1, `setConnected` will reach\n * ChildPart(N16); in this case the entire part is being disconnected, so we\n * simply iterate all of N16's `_$disconnectableChildren` (D17,T18) and\n * recursively run `setConnected` on them. Note that we only remove children\n * from `_$disconnectableChildren` for the top-level values being disconnected\n * on a clear; doing this bookkeeping lower in the tree is wasteful since it's\n * all being thrown away.\n *\n * Example 3: If the LitElement containing the entire tree above becomes\n * disconnected, it will run `childPart.setConnected()` (which calls\n * `childPart._$notifyConnectionChanged()` if it exists); in this case, we\n * recursively run `setConnected()` over the entire tree, without removing any\n * children from `_$disconnectableChildren`, since this tree is required to\n * re-connect the tree, which does the same operation, simply passing\n * `isConnected: true` down the tree, signaling which callback to run.\n */\n\nimport {AttributePart, ChildPart, Disconnectable, Part} from './lit-html.js';\nimport {isSingleExpression} from './directive-helpers.js';\nimport {Directive, PartInfo, PartType} from './directive.js';\nexport * from './directive.js';\n\nconst DEV_MODE = true;\n\n/**\n * Recursively walks down the tree of Parts/TemplateInstances/Directives to set\n * the connected state of directives and run `disconnected`/ `reconnected`\n * callbacks.\n *\n * @return True if there were children to disconnect; false otherwise\n */\nconst notifyChildrenConnectedChanged = (\n  parent: Disconnectable,\n  isConnected: boolean\n): boolean => {\n  const children = parent._$disconnectableChildren;\n  if (children === undefined) {\n    return false;\n  }\n  for (const obj of children) {\n    // The existence of `_$notifyDirectiveConnectionChanged` is used as a \"brand\" to\n    // disambiguate AsyncDirectives from other DisconnectableChildren\n    // (as opposed to using an instanceof check to know when to call it); the\n    // redundancy of \"Directive\" in the API name is to avoid conflicting with\n    // `_$notifyConnectionChanged`, which exists `ChildParts` which are also in\n    // this list\n    // Disconnect Directive (and any nested directives contained within)\n    // This property needs to remain unminified.\n    (obj as AsyncDirective)['_$notifyDirectiveConnectionChanged']?.(\n      isConnected,\n      false\n    );\n    // Disconnect Part/TemplateInstance\n    notifyChildrenConnectedChanged(obj, isConnected);\n  }\n  return true;\n};\n\n/**\n * Removes the given child from its parent list of disconnectable children, and\n * if the parent list becomes empty as a result, removes the parent from its\n * parent, and so forth up the tree when that causes subsequent parent lists to\n * become empty.\n */\nconst removeDisconnectableFromParent = (obj: Disconnectable) => {\n  let parent, children;\n  do {\n    if ((parent = obj._$parent) === undefined) {\n      break;\n    }\n    children = parent._$disconnectableChildren!;\n    children.delete(obj);\n    obj = parent;\n  } while (children?.size === 0);\n};\n\nconst addDisconnectableToParent = (obj: Disconnectable) => {\n  // Climb the parent tree, creating a sparse tree of children needing\n  // disconnection\n  for (let parent; (parent = obj._$parent); obj = parent) {\n    let children = parent._$disconnectableChildren;\n    if (children === undefined) {\n      parent._$disconnectableChildren = children = new Set();\n    } else if (children.has(obj)) {\n      // Once we've reached a parent that already contains this child, we\n      // can short-circuit\n      break;\n    }\n    children.add(obj);\n    installDisconnectAPI(parent);\n  }\n};\n\n/**\n * Changes the parent reference of the ChildPart, and updates the sparse tree of\n * Disconnectable children accordingly.\n *\n * Note, this method will be patched onto ChildPart instances and called from\n * the core code when parts are moved between different parents.\n */\nfunction reparentDisconnectables(this: ChildPart, newParent: Disconnectable) {\n  if (this._$disconnectableChildren !== undefined) {\n    removeDisconnectableFromParent(this);\n    this._$parent = newParent;\n    addDisconnectableToParent(this);\n  } else {\n    this._$parent = newParent;\n  }\n}\n\n/**\n * Sets the connected state on any directives contained within the committed\n * value of this part (i.e. within a TemplateInstance or iterable of\n * ChildParts) and runs their `disconnected`/`reconnected`s, as well as within\n * any directives stored on the ChildPart (when `valueOnly` is false).\n *\n * `isClearingValue` should be passed as `true` on a top-level part that is\n * clearing itself, and not as a result of recursively disconnecting directives\n * as part of a `clear` operation higher up the tree. This both ensures that any\n * directive on this ChildPart that produced a value that caused the clear\n * operation is not disconnected, and also serves as a performance optimization\n * to avoid needless bookkeeping when a subtree is going away; when clearing a\n * subtree, only the top-most part need to remove itself from the parent.\n *\n * `fromPartIndex` is passed only in the case of a partial `_clear` running as a\n * result of truncating an iterable.\n *\n * Note, this method will be patched onto ChildPart instances and called from the\n * core code when parts are cleared or the connection state is changed by the\n * user.\n */\nfunction notifyChildPartConnectedChanged(\n  this: ChildPart,\n  isConnected: boolean,\n  isClearingValue = false,\n  fromPartIndex = 0\n) {\n  const value = this._$committedValue;\n  const children = this._$disconnectableChildren;\n  if (children === undefined || children.size === 0) {\n    return;\n  }\n  if (isClearingValue) {\n    if (Array.isArray(value)) {\n      // Iterable case: Any ChildParts created by the iterable should be\n      // disconnected and removed from this ChildPart's disconnectable\n      // children (starting at `fromPartIndex` in the case of truncation)\n      for (let i = fromPartIndex; i < value.length; i++) {\n        notifyChildrenConnectedChanged(value[i], false);\n        removeDisconnectableFromParent(value[i]);\n      }\n    } else if (value != null) {\n      // TemplateInstance case: If the value has disconnectable children (will\n      // only be in the case that it is a TemplateInstance), we disconnect it\n      // and remove it from this ChildPart's disconnectable children\n      notifyChildrenConnectedChanged(value as Disconnectable, false);\n      removeDisconnectableFromParent(value as Disconnectable);\n    }\n  } else {\n    notifyChildrenConnectedChanged(this, isConnected);\n  }\n}\n\n/**\n * Patches disconnection API onto ChildParts.\n */\nconst installDisconnectAPI = (obj: Disconnectable) => {\n  if ((obj as ChildPart).type == PartType.CHILD) {\n    (obj as ChildPart)._$notifyConnectionChanged ??=\n      notifyChildPartConnectedChanged;\n    (obj as ChildPart)._$reparentDisconnectables ??= reparentDisconnectables;\n  }\n};\n\n/**\n * An abstract `Directive` base class whose `disconnected` method will be\n * called when the part containing the directive is cleared as a result of\n * re-rendering, or when the user calls `part.setConnected(false)` on\n * a part that was previously rendered containing the directive (as happens\n * when e.g. a LitElement disconnects from the DOM).\n *\n * If `part.setConnected(true)` is subsequently called on a\n * containing part, the directive's `reconnected` method will be called prior\n * to its next `update`/`render` callbacks. When implementing `disconnected`,\n * `reconnected` should also be implemented to be compatible with reconnection.\n *\n * Note that updates may occur while the directive is disconnected. As such,\n * directives should generally check the `this.isConnected` flag during\n * render/update to determine whether it is safe to subscribe to resources\n * that may prevent garbage collection.\n */\nexport abstract class AsyncDirective extends Directive {\n  // As opposed to other Disconnectables, AsyncDirectives always get notified\n  // when the RootPart connection changes, so the public `isConnected`\n  // is a locally stored variable initialized via its part's getter and synced\n  // via `_$notifyDirectiveConnectionChanged`. This is cheaper than using\n  // the _$isConnected getter, which has to look back up the tree each time.\n  /**\n   * The connection state for this Directive.\n   */\n  isConnected!: boolean;\n\n  // @internal\n  override _$disconnectableChildren?: Set<Disconnectable> = undefined;\n  /**\n   * Initialize the part with internal fields\n   * @param part\n   * @param parent\n   * @param attributeIndex\n   */\n  override _$initialize(\n    part: Part,\n    parent: Disconnectable,\n    attributeIndex: number | undefined\n  ) {\n    super._$initialize(part, parent, attributeIndex);\n    addDisconnectableToParent(this);\n    this.isConnected = part._$isConnected;\n  }\n  // This property needs to remain unminified.\n  /**\n   * Called from the core code when a directive is going away from a part (in\n   * which case `shouldRemoveFromParent` should be true), and from the\n   * `setChildrenConnected` helper function when recursively changing the\n   * connection state of a tree (in which case `shouldRemoveFromParent` should\n   * be false).\n   *\n   * @param isConnected\n   * @param isClearingDirective - True when the directive itself is being\n   *     removed; false when the tree is being disconnected\n   * @internal\n   */\n  override ['_$notifyDirectiveConnectionChanged'](\n    isConnected: boolean,\n    isClearingDirective = true\n  ) {\n    if (isConnected !== this.isConnected) {\n      this.isConnected = isConnected;\n      if (isConnected) {\n        this.reconnected?.();\n      } else {\n        this.disconnected?.();\n      }\n    }\n    if (isClearingDirective) {\n      notifyChildrenConnectedChanged(this, isConnected);\n      removeDisconnectableFromParent(this);\n    }\n  }\n\n  /**\n   * Sets the value of the directive's Part outside the normal `update`/`render`\n   * lifecycle of a directive.\n   *\n   * This method should not be called synchronously from a directive's `update`\n   * or `render`.\n   *\n   * @param directive The directive to update\n   * @param value The value to set\n   */\n  setValue(value: unknown) {\n    if (isSingleExpression(this.__part as unknown as PartInfo)) {\n      this.__part._$setValue(value, this);\n    } else {\n      // this.__attributeIndex will be defined in this case, but\n      // assert it in dev mode\n      if (DEV_MODE && this.__attributeIndex === undefined) {\n        throw new Error(`Expected this.__attributeIndex to be a number`);\n      }\n      const newValues = [...(this.__part._$committedValue as Array<unknown>)];\n      newValues[this.__attributeIndex!] = value;\n      (this.__part as AttributePart)._$setValue(newValues, this, 0);\n    }\n  }\n\n  /**\n   * User callbacks for implementing logic to release any resources/subscriptions\n   * that may have been retained by this directive. Since directives may also be\n   * re-connected, `reconnected` should also be implemented to restore the\n   * working state of the directive prior to the next render.\n   */\n  protected disconnected() {}\n  protected reconnected() {}\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n// Note, this module is not included in package exports so that it's private to\n// our first-party directives. If it ends up being useful, we can open it up and\n// export it.\n\n/**\n * Helper to iterate an AsyncIterable in its own closure.\n * @param iterable The iterable to iterate\n * @param callback The callback to call for each value. If the callback returns\n * `false`, the loop will be broken.\n */\nexport const forAwaitOf = async <T>(\n  iterable: AsyncIterable<T>,\n  callback: (value: T) => Promise<boolean>\n) => {\n  for await (const v of iterable) {\n    if ((await callback(v)) === false) {\n      return;\n    }\n  }\n};\n\n/**\n * Holds a reference to an instance that can be disconnected and reconnected,\n * so that a closure over the ref (e.g. in a then function to a promise) does\n * not strongly hold a ref to the instance. Approximates a WeakRef but must\n * be manually connected & disconnected to the backing instance.\n */\nexport class PseudoWeakRef<T> {\n  private _ref?: T;\n  constructor(ref: T) {\n    this._ref = ref;\n  }\n  /**\n   * Disassociates the ref with the backing instance.\n   */\n  disconnect() {\n    this._ref = undefined;\n  }\n  /**\n   * Reassociates the ref with the backing instance.\n   */\n  reconnect(ref: T) {\n    this._ref = ref;\n  }\n  /**\n   * Retrieves the backing instance (will be undefined when disconnected)\n   */\n  deref() {\n    return this._ref;\n  }\n}\n\n/**\n * A helper to pause and resume waiting on a condition in an async function\n */\nexport class Pauser {\n  private _promise?: Promise<void> = undefined;\n  private _resolve?: () => void = undefined;\n  /**\n   * When paused, returns a promise to be awaited; when unpaused, returns\n   * undefined. Note that in the microtask between the pauser being resumed\n   * an await of this promise resolving, the pauser could be paused again,\n   * hence callers should check the promise in a loop when awaiting.\n   * @returns A promise to be awaited when paused or undefined\n   */\n  get() {\n    return this._promise;\n  }\n  /**\n   * Creates a promise to be awaited\n   */\n  pause() {\n    this._promise ??= new Promise((resolve) => (this._resolve = resolve));\n  }\n  /**\n   * Resolves the promise which may be awaited\n   */\n  resume() {\n    this._resolve?.();\n    this._promise = this._resolve = undefined;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {Part, noChange} from '../lit-html.js';\nimport {isPrimitive} from '../directive-helpers.js';\nimport {directive, AsyncDirective} from '../async-directive.js';\nimport {Pauser, PseudoWeakRef} from './private-async-helpers.js';\n\nconst isPromise = (x: unknown) => {\n  return !isPrimitive(x) && typeof (x as {then?: unknown}).then === 'function';\n};\n// Effectively infinity, but a SMI.\nconst _infinity = 0x3fffffff;\n\nexport class UntilDirective extends AsyncDirective {\n  private __lastRenderedIndex: number = _infinity;\n  private __values: unknown[] = [];\n  private __weakThis = new PseudoWeakRef(this);\n  private __pauser = new Pauser();\n\n  render(...args: Array<unknown>): unknown {\n    return args.find((x) => !isPromise(x)) ?? noChange;\n  }\n\n  override update(_part: Part, args: Array<unknown>) {\n    const previousValues = this.__values;\n    let previousLength = previousValues.length;\n    this.__values = args;\n\n    const weakThis = this.__weakThis;\n    const pauser = this.__pauser;\n\n    // If our initial render occurs while disconnected, ensure that the pauser\n    // and weakThis are in the disconnected state\n    if (!this.isConnected) {\n      this.disconnected();\n    }\n\n    for (let i = 0; i < args.length; i++) {\n      // If we've rendered a higher-priority value already, stop.\n      if (i > this.__lastRenderedIndex) {\n        break;\n      }\n\n      const value = args[i];\n\n      // Render non-Promise values immediately\n      if (!isPromise(value)) {\n        this.__lastRenderedIndex = i;\n        // Since a lower-priority value will never overwrite a higher-priority\n        // synchronous value, we can stop processing now.\n        return value;\n      }\n\n      // If this is a Promise we've already handled, skip it.\n      if (i < previousLength && value === previousValues[i]) {\n        continue;\n      }\n\n      // We have a Promise that we haven't seen before, so priorities may have\n      // changed. Forget what we rendered before.\n      this.__lastRenderedIndex = _infinity;\n      previousLength = 0;\n\n      // Note, the callback avoids closing over `this` so that the directive\n      // can be gc'ed before the promise resolves; instead `this` is retrieved\n      // from `weakThis`, which can break the hard reference in the closure when\n      // the directive disconnects\n      Promise.resolve(value).then(async (result: unknown) => {\n        // If we're disconnected, wait until we're (maybe) reconnected\n        // The while loop here handles the case that the connection state\n        // thrashes, causing the pauser to resume and then get re-paused\n        while (pauser.get()) {\n          await pauser.get();\n        }\n        // If the callback gets here and there is no `this`, it means that the\n        // directive has been disconnected and garbage collected and we don't\n        // need to do anything else\n        const _this = weakThis.deref();\n        if (_this !== undefined) {\n          const index = _this.__values.indexOf(value);\n          // If state.values doesn't contain the value, we've re-rendered without\n          // the value, so don't render it. Then, only render if the value is\n          // higher-priority than what's already been rendered.\n          if (index > -1 && index < _this.__lastRenderedIndex) {\n            _this.__lastRenderedIndex = index;\n            _this.setValue(result);\n          }\n        }\n      });\n    }\n\n    return noChange;\n  }\n\n  override disconnected() {\n    this.__weakThis.disconnect();\n    this.__pauser.pause();\n  }\n\n  override reconnected() {\n    this.__weakThis.reconnect(this);\n    this.__pauser.resume();\n  }\n}\n\n/**\n * Renders one of a series of values, including Promises, to a Part.\n *\n * Values are rendered in priority order, with the first argument having the\n * highest priority and the last argument having the lowest priority. If a\n * value is a Promise, low-priority values will be rendered until it resolves.\n *\n * The priority of values can be used to create placeholder content for async\n * data. For example, a Promise with pending content can be the first,\n * highest-priority, argument, and a non_promise loading indicator template can\n * be used as the second, lower-priority, argument. The loading indicator will\n * render immediately, and the primary content will render when the Promise\n * resolves.\n *\n * Example:\n *\n * ```js\n * const content = fetch('./content.txt').then(r => r.text());\n * html`${until(content, html`<span>Loading...</span>`)}`\n * ```\n */\nexport const until = directive(UntilDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\n// export type {UntilDirective};\n", null, null, null, null, null, null, null, null, null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;AAqBA,IAAM,WAAW;AAEjB,IAAI;AAEJ,IAAI,UAAU;AAGZ,aAAW,sBAAX,WAAW,oBAAsB,oBAAI,IAAG;AAOxC,iBAAe,CAAC,MAAc,YAAmB;AAC/C,eAAW,4BAA4B,IAAI;AAC3C,QACE,CAAC,WAAW,kBAAmB,IAAI,OAAO,KAC1C,CAAC,WAAW,kBAAmB,IAAI,IAAI,GACvC;AACA,cAAQ,KAAK,OAAO;AACpB,iBAAW,kBAAmB,IAAI,OAAO;IAC3C;EACF;AACF;AA2BA,IAAM,iBAAiB,CACrB,SACA,OACA,SACE;AACF,QAAM,iBAAiB,MAAM,eAAe,IAAI;AAC/C,QAAM,YAAuC,eAAe,MAAM,OAAO;AAM1E,SAAO,iBACH,OAAO,yBAAyB,OAAO,IAAI,IAC3C;AACN;AAKA,IAAM,6BAAkD;EACtD,WAAW;EACX,MAAM;EACN,WAAW;EACX,SAAS;EACT,YAAY;;AAaP,IAAM,mBAAmB,CAC9B,UAA+B,4BAC/B,QACA,YACsE;AACtE,QAAM,EAAC,MAAM,SAAQ,IAAI;AAEzB,MAAI,YAAY,YAAY,MAAM;AAChC,iBACE,0BACA,aAAa,MAAM,oMAGa;EAEpC;AAGA,MAAI,aAAa,WAAW,oBAAoB,IAAI,QAAQ;AAC5D,MAAI,eAAe,QAAW;AAC5B,eAAW,oBAAoB,IAAI,UAAW,aAAa,oBAAI,IAAG,CAAG;EACvE;AACA,MAAI,SAAS,UAAU;AACrB,cAAU,OAAO,OAAO,OAAO;AAC/B,YAAQ,UAAU;EACpB;AACA,aAAW,IAAI,QAAQ,MAAM,OAAO;AAEpC,MAAI,SAAS,YAAY;AAIvB,UAAM,EAAC,KAAI,IAAI;AACf,WAAO;MACL,IAA2B,GAAI;AAC7B,cAAM,WACJ,OACA,IAAI,KAAK,IAAoB;AAC9B,eAA8C,IAAI,KACjD,MACA,CAAC;AAEH,aAAK,cAAc,MAAM,UAAU,OAAO;MAC5C;MACA,KAA4B,GAAI;AAC9B,YAAI,MAAM,QAAW;AACnB,eAAK,iBAAiB,MAAM,QAAW,SAAS,CAAC;QACnD;AACA,eAAO;MACT;;EAEJ,WAAW,SAAS,UAAU;AAC5B,UAAM,EAAC,KAAI,IAAI;AACf,WAAO,SAAiC,OAAQ;AAC9C,YAAM,WAAW,KAAK,IAA6B;AAClD,aAA8B,KAAK,MAAM,KAAK;AAC/C,WAAK,cAAc,MAAM,UAAU,OAAO;IAC5C;EACF;AACA,QAAM,IAAI,MAAM,mCAAmC,IAAI,EAAE;AAC3D;AAkCM,SAAU,SAAS,SAA6B;AACpD,SAAO,CACL,eAIA,kBAKO;AACP,WACE,OAAO,kBAAkB,WACrB,iBACE,SACA,eAGA,aAA8C,IAEhD,eACE,SACA,eACA,aAA4B;EAGtC;AACF;;;AChMM,SAAU,MAAM,SAA0B;AAC9C,SAAO,SAAS;IACd,GAAG;;;;IAIH,OAAO;IACP,WAAW;GACZ;AACH;;;AClCA,IAAMA,YAAW;AAEjB,IAAIC;AAEJ,IAAID,WAAU;AAGZ,aAAW,sBAAX,WAAW,oBAAsB,oBAAI,IAAG;AAOxC,EAAAC,gBAAe,CAAC,MAAc,YAAmB;AAC/C,eAAW,OACP,4BAA4B,IAAI,2BAChC;AACJ,QACE,CAAC,WAAW,kBAAmB,IAAI,OAAO,KAC1C,CAAC,WAAW,kBAAmB,IAAI,IAAI,GACvC;AACA,cAAQ,KAAK,OAAO;AACpB,iBAAW,kBAAmB,IAAI,OAAO;IAC3C;EACF;AACF;;;ACvCA,IAAA,iBAAe;;;;;;;;;;;;;;;ACiBR,IAAM,UAAN,MAAMC,iBAAgB,WAAU;EA6BrB,SAAM;AACpB,SAAK,MAAM,UAAU;wBACD,KAAK,aAAa;mBACvB,KAAK,QAAQ;oBACZ,KAAK,SAAS;mBACf,KAAK,QAAQ;qBACX,KAAK,UAAU;qBACf,KAAK,UAAU;yBACX,KAAK,cAAc;oBACxB,KAAK,aAAa,qBAAqB,KAAK,SAAS,GAAG;iBAC3D,KAAK,UAAU,qBAAqB,KAAK,MAAM,GAAG;aACtD,KAAK,OAAO,qBAAqB,KAAK,GAAG,GAAG;qBACpC,KAAK,WAAW,aAAa,iBAAiB,KAAK,SAAS,CAAC,CAAC;uBAC5D,KAAK,WAAW,aAAa,iBAAiB,KAAK,SAAS,CAAC,CAAC;wBAC7D,KAAK,WAAW,aAAa,iBAAiB,KAAK,SAAS,CAAC,CAAC;sBAChE,KAAK,WAAW,aAAa,iBAAiB,KAAK,SAAS,CAAC,CAAC;oBAChE,KAAK,UAAU,aAAa,iBAAiB,KAAK,QAAQ,CAAC,CAAC;sBAC1D,KAAK,UAAU,aAAa,iBAAiB,KAAK,QAAQ,CAAC,CAAC;uBAC3D,KAAK,UAAU,aAAa,iBAAiB,KAAK,QAAQ,CAAC,CAAC;qBAC9D,KAAK,UAAU,aAAa,iBAAiB,KAAK,QAAQ,CAAC,CAAC;;AAG7E,WAAO;EACT;;AAnDuB,QAAA,SAAS,CAAC,aAAa,cAAM;AAGjC,WAAA;EAAlB,SAAQ;;AAEU,WAAA;EAAlB,SAAQ;;AAEU,WAAA;EAAlB,SAAQ;;AAEU,WAAA;EAAlB,SAAQ;;AAEU,WAAA;EAAlB,SAAQ;;AAEU,WAAA;EAAlB,SAAQ;;AAEU,WAAA;EAAlB,SAAQ;;AAEU,WAAA;EAAlB,SAAQ;;AAEU,WAAA;EAAlB,SAAQ;;AAEU,WAAA;EAAlB,SAAQ;;AAEU,WAAA;EAAlB,SAAQ;;AAEU,WAAA;EAAlB,SAAQ;;AA1BE,UAAO,WAAA;EADnB,cAAc,UAAU;GACZ,OAAO;;;ACLb,IAAM,YAAY,CAAI,UAAa,SAAS;;;AC4B5C,IAAM,WAAW;EACtB,WAAW;EACX,OAAO;EACP,UAAU;EACV,mBAAmB;EACnB,OAAO;EACP,SAAS;;AAoCJ,IAAM,YACX,CAA2B,MAC3B,IAAI,YAAsE;;EAExE,CAAC,iBAAiB,GAAG;EACrB;;AAQE,IAAgB,YAAhB,MAAyB;EAkB7B,YAAY,WAAmB;EAAG;;EAGlC,IAAI,gBAAa;AACf,WAAO,KAAK,SAAS;EACvB;;EAGA,aACE,MACA,QACA,gBAAkC;AAElC,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,mBAAmB;EAC1B;;EAEA,UAAU,MAAY,OAAqB;AACzC,WAAO,KAAK,OAAO,MAAM,KAAK;EAChC;EAIA,OAAO,OAAa,OAAqB;AACvC,WAAO,KAAK,OAAO,GAAG,KAAK;EAC7B;;;;ACvHF,IAAM,oBAAN,cAAgC,UAAS;EAQvC,YAAY,UAAkB;AA9BhC,QAAAC;AA+BI,UAAM,QAAQ;AACd,QACE,SAAS,SAAS,SAAS,aAC3B,SAAS,SAAS,aACjBA,MAAA,SAAS,YAAT,gBAAAA,IAAkB,UAAoB,GACvC;AACA,YAAM,IAAI,MACR,oGAC+C;IAEnD;EACF;EAEA,OAAO,WAAoB;AAEzB,WACE,MACA,OAAO,KAAK,SAAS,EAClB,OAAO,CAAC,QAAQ,UAAU,GAAG,CAAC,EAC9B,KAAK,GAAG,IACX;EAEJ;EAES,OAAO,MAAqB,CAAC,SAAS,GAA4B;AAvD7E,QAAAA,KAAAC;AAyDI,QAAI,KAAK,qBAAqB,QAAW;AACvC,WAAK,mBAAmB,oBAAI,IAAG;AAC/B,UAAI,KAAK,YAAY,QAAW;AAC9B,aAAK,iBAAiB,IAAI,IACxB,KAAK,QACF,KAAK,GAAG,EACR,MAAM,IAAI,EACV,OAAO,CAAC,MAAM,MAAM,EAAE,CAAC;MAE9B;AACA,iBAAW,QAAQ,WAAW;AAC5B,YAAI,UAAU,IAAI,KAAK,GAACD,MAAA,KAAK,mBAAL,gBAAAA,IAAqB,IAAI,QAAO;AACtD,eAAK,iBAAiB,IAAI,IAAI;QAChC;MACF;AACA,aAAO,KAAK,OAAO,SAAS;IAC9B;AAEA,UAAM,YAAY,KAAK,QAAQ;AAG/B,eAAW,QAAQ,KAAK,kBAAkB;AACxC,UAAI,EAAE,QAAQ,YAAY;AACxB,kBAAU,OAAO,IAAI;AACrB,aAAK,iBAAkB,OAAO,IAAI;MACpC;IACF;AAGA,eAAW,QAAQ,WAAW;AAG5B,YAAM,QAAQ,CAAC,CAAC,UAAU,IAAI;AAC9B,UACE,UAAU,KAAK,iBAAiB,IAAI,IAAI,KACxC,GAACC,MAAA,KAAK,mBAAL,gBAAAA,IAAqB,IAAI,QAC1B;AACA,YAAI,OAAO;AACT,oBAAU,IAAI,IAAI;AAClB,eAAK,iBAAiB,IAAI,IAAI;QAChC,OAAO;AACL,oBAAU,OAAO,IAAI;AACrB,eAAK,iBAAiB,OAAO,IAAI;QACnC;MACF;IACF;AACA,WAAO;EACT;;AAiBK,IAAM,WAAW,UAAU,iBAAiB;;;ACvHnD,IAAAC,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACQR,IAAM,UAAN,MAAMC,iBAAgB,WAAU;EAAhC,cAAA;;AAIc,SAAA,UAAoB;AAEpB,SAAA,QAAmB;AAEnB,SAAA,QAAoB;AAEpB,SAAA,YAAwB;EAkB7C;EAfkB,SAAM;AACpB,UAAM,UAAU;MACd,CAAC,YAAY,KAAK,OAAO,EAAE,GAAG;MAC9B,CAAC,aAAa,KAAK,KAAK,EAAE,GAAG;MAE7B,CAAC,kBAAkB,KAAK,SAAS,EAAE,GAAG,KAAK,YAAY,OAAO;;AAGhE,SAAK,MAAM,UAAU;uBACF,KAAK,KAAK;uCACM,KAAK,KAAK;;AAG7C,WAAO,mBAAmB,SAAS,OAAO,CAAC;EAC7C;;AA1BuB,QAAA,SAAS,CAAC,aAAaC,eAAM;AAGjCC,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAVE,UAAOA,YAAA;EADnB,cAAc,UAAU;GACZ,OAAO;;;ACYpB,IAAM,EAAC,YAAY,UAAS,IAAI;AAIhC,IAAM,0BAA0B;AA1BhC;AA4BA,IAAM,OACJ,6BACA,YAAO,aAAP,mBAAiB,YACjB,YAAO,aAAP,mBAAiB,aAAY,OACzB,OAAO,SAAU,OACjB,CAAC,SAAe;AAOf,IAAM,cAAc,CAAC,UAC1B,UAAU,QAAS,OAAO,SAAS,YAAY,OAAO,SAAS;AA8D1D,IAAM,qBAAqB,CAAC,SAChC,KAA2B,YAAY;;;ACqB1C,IAAMC,YAAW;AASjB,IAAM,iCAAiC,CACrC,QACA,gBACW;AAzIb,MAAAC;AA0IE,QAAM,WAAW,OAAO;AACxB,MAAI,aAAa,QAAW;AAC1B,WAAO;EACT;AACA,aAAW,OAAO,UAAU;AASzB,KAAAA,MAAA,IAAuB,0CAAvB,gBAAAA,IAAA,UACC,aACA;AAGF,mCAA+B,KAAK,WAAW;EACjD;AACA,SAAO;AACT;AAQA,IAAM,iCAAiC,CAAC,QAAuB;AAC7D,MAAI,QAAQ;AACZ,KAAG;AACD,SAAK,SAAS,IAAI,cAAc,QAAW;AACzC;IACF;AACA,eAAW,OAAO;AAClB,aAAS,OAAO,GAAG;AACnB,UAAM;EACR,UAAS,qCAAU,UAAS;AAC9B;AAEA,IAAM,4BAA4B,CAAC,QAAuB;AAGxD,WAAS,QAAS,SAAS,IAAI,UAAW,MAAM,QAAQ;AACtD,QAAI,WAAW,OAAO;AACtB,QAAI,aAAa,QAAW;AAC1B,aAAO,2BAA2B,WAAW,oBAAI,IAAG;IACtD,WAAW,SAAS,IAAI,GAAG,GAAG;AAG5B;IACF;AACA,aAAS,IAAI,GAAG;AAChB,yBAAqB,MAAM;EAC7B;AACF;AASA,SAAS,wBAAyC,WAAyB;AACzE,MAAI,KAAK,6BAA6B,QAAW;AAC/C,mCAA+B,IAAI;AACnC,SAAK,WAAW;AAChB,8BAA0B,IAAI;EAChC,OAAO;AACL,SAAK,WAAW;EAClB;AACF;AAuBA,SAAS,gCAEP,aACA,kBAAkB,OAClB,gBAAgB,GAAC;AAEjB,QAAM,QAAQ,KAAK;AACnB,QAAM,WAAW,KAAK;AACtB,MAAI,aAAa,UAAa,SAAS,SAAS,GAAG;AACjD;EACF;AACA,MAAI,iBAAiB;AACnB,QAAI,MAAM,QAAQ,KAAK,GAAG;AAIxB,eAAS,IAAI,eAAe,IAAI,MAAM,QAAQ,KAAK;AACjD,uCAA+B,MAAM,CAAC,GAAG,KAAK;AAC9C,uCAA+B,MAAM,CAAC,CAAC;MACzC;IACF,WAAW,SAAS,MAAM;AAIxB,qCAA+B,OAAyB,KAAK;AAC7D,qCAA+B,KAAuB;IACxD;EACF,OAAO;AACL,mCAA+B,MAAM,WAAW;EAClD;AACF;AAKA,IAAM,uBAAuB,CAAC,QAAuB;AACnD,MAAK,IAAkB,QAAQ,SAAS,OAAO;AAC5C,QAAkB,8BAAlB,IAAkB,4BACjB;AACD,QAAkB,8BAAlB,IAAkB,4BAA8B;EACnD;AACF;AAmBM,IAAgB,iBAAhB,cAAuC,UAAS;EAAtD,cAAA;;AAYW,SAAA,2BAAiD;EAgF5D;;;;;;;EAzEW,aACP,MACA,QACA,gBAAkC;AAElC,UAAM,aAAa,MAAM,QAAQ,cAAc;AAC/C,8BAA0B,IAAI;AAC9B,SAAK,cAAc,KAAK;EAC1B;;;;;;;;;;;;;;EAcS,CAAC,oCAAoC,EAC5C,aACA,sBAAsB,MAAI;AAjV9B,QAAAA,KAAAC;AAmVI,QAAI,gBAAgB,KAAK,aAAa;AACpC,WAAK,cAAc;AACnB,UAAI,aAAa;AACf,SAAAD,MAAA,KAAK,gBAAL,gBAAAA,IAAA;MACF,OAAO;AACL,SAAAC,MAAA,KAAK,iBAAL,gBAAAA,IAAA;MACF;IACF;AACA,QAAI,qBAAqB;AACvB,qCAA+B,MAAM,WAAW;AAChD,qCAA+B,IAAI;IACrC;EACF;;;;;;;;;;;EAYA,SAAS,OAAc;AACrB,QAAI,mBAAmB,KAAK,MAA6B,GAAG;AAC1D,WAAK,OAAO,WAAW,OAAO,IAAI;IACpC,OAAO;AAGL,UAAIF,aAAY,KAAK,qBAAqB,QAAW;AACnD,cAAM,IAAI,MAAM,+CAA+C;MACjE;AACA,YAAM,YAAY,CAAC,GAAI,KAAK,OAAO,gBAAmC;AACtE,gBAAU,KAAK,gBAAiB,IAAI;AACnC,WAAK,OAAyB,WAAW,WAAW,MAAM,CAAC;IAC9D;EACF;;;;;;;EAQU,eAAY;EAAI;EAChB,cAAW;EAAI;;;;AChWrB,IAAO,gBAAP,MAAoB;EAExB,YAAY,KAAM;AAChB,SAAK,OAAO;EACd;;;;EAIA,aAAU;AACR,SAAK,OAAO;EACd;;;;EAIA,UAAU,KAAM;AACd,SAAK,OAAO;EACd;;;;EAIA,QAAK;AACH,WAAO,KAAK;EACd;;AAMI,IAAO,SAAP,MAAa;EAAnB,cAAA;AACU,SAAA,WAA2B;AAC3B,SAAA,WAAwB;EAwBlC;;;;;;;;EAhBE,MAAG;AACD,WAAO,KAAK;EACd;;;;EAIA,QAAK;AACH,SAAK,aAAL,KAAK,WAAa,IAAI,QAAQ,CAAC,YAAa,KAAK,WAAW,OAAQ;EACtE;;;;EAIA,SAAM;AAnFR,QAAAG;AAoFI,KAAAA,MAAA,KAAK,aAAL,gBAAAA,IAAA;AACA,SAAK,WAAW,KAAK,WAAW;EAClC;;;;AC3EF,IAAM,YAAY,CAAC,MAAc;AAC/B,SAAO,CAAC,YAAY,CAAC,KAAK,OAAQ,EAAuB,SAAS;AACpE;AAEA,IAAM,YAAY;AAEZ,IAAO,iBAAP,cAA8B,eAAc;EAAlD,cAAA;;AACU,SAAA,sBAA8B;AAC9B,SAAA,WAAsB,CAAA;AACtB,SAAA,aAAa,IAAI,cAAc,IAAI;AACnC,SAAA,WAAW,IAAI,OAAM;EAsF/B;EApFE,UAAU,MAAoB;AAC5B,WAAO,KAAK,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,KAAK;EAC5C;EAES,OAAO,OAAa,MAAoB;AAC/C,UAAM,iBAAiB,KAAK;AAC5B,QAAI,iBAAiB,eAAe;AACpC,SAAK,WAAW;AAEhB,UAAM,WAAW,KAAK;AACtB,UAAM,SAAS,KAAK;AAIpB,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,aAAY;IACnB;AAEA,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAEpC,UAAI,IAAI,KAAK,qBAAqB;AAChC;MACF;AAEA,YAAM,QAAQ,KAAK,CAAC;AAGpB,UAAI,CAAC,UAAU,KAAK,GAAG;AACrB,aAAK,sBAAsB;AAG3B,eAAO;MACT;AAGA,UAAI,IAAI,kBAAkB,UAAU,eAAe,CAAC,GAAG;AACrD;MACF;AAIA,WAAK,sBAAsB;AAC3B,uBAAiB;AAMjB,cAAQ,QAAQ,KAAK,EAAE,KAAK,OAAO,WAAmB;AAIpD,eAAO,OAAO,IAAG,GAAI;AACnB,gBAAM,OAAO,IAAG;QAClB;AAIA,cAAM,QAAQ,SAAS,MAAK;AAC5B,YAAI,UAAU,QAAW;AACvB,gBAAM,QAAQ,MAAM,SAAS,QAAQ,KAAK;AAI1C,cAAI,QAAQ,MAAM,QAAQ,MAAM,qBAAqB;AACnD,kBAAM,sBAAsB;AAC5B,kBAAM,SAAS,MAAM;UACvB;QACF;MACF,CAAC;IACH;AAEA,WAAO;EACT;EAES,eAAY;AACnB,SAAK,WAAW,WAAU;AAC1B,SAAK,SAAS,MAAK;EACrB;EAES,cAAW;AAClB,SAAK,WAAW,UAAU,IAAI;AAC9B,SAAK,SAAS,OAAM;EACtB;;AAwBK,IAAM,QAAQ,UAAU,cAAc;;;AChIvC,IAAO,YAAP,MAAgB;EAAtB,cAAA;AACU,SAAA,QAAQ,oBAAI,IAAG;EAqBzB;EAnBE,IAAI,KAAQ,OAAQ;AAClB,SAAK,MAAM,IAAI,KAAK,KAAK;EAC3B;EAEA,IAAI,KAAM;AACR,WAAO,KAAK,MAAM,IAAI,GAAG;EAC3B;EAEA,IAAI,KAAM;AACR,WAAO,KAAK,MAAM,IAAI,GAAG;EAC3B;EAEA,OAAO,KAAM;AACX,SAAK,MAAM,OAAO,GAAG;EACvB;EAEA,QAAK;AACH,SAAK,MAAM,MAAK;EAClB;;AAGK,IAAM,iBAAiB,IAAI,UAAS;;;ACxB3C,IAAAC,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACSf,IAAM,QAAQ;EACZ,KAAK,aAAa,MAAM,OAAO,mBAAyB,GAAG;EAC3D,YAAY,aAAa,MAAM,OAAO,2BAAiC,GAAG;EAC1E,mBAAmB,aAChB,MAAM,OAAO,mCAAyC,GAAG;EAC5D,UAAU,aAAa,MAAM,OAAO,yBAA+B,GAAG;EACtE,OAAO,aAAa,MAAM,OAAO,qBAA2B,GAAG;EAC/D,aAAa,aAAa,MAAM,OAAO,4BAAkC,GAAG;EAC5E,WAAW,aAAa,MAAM,OAAO,0BAAgC,GAAG;EACxE,YAAY,aAAa,MAAM,OAAO,2BAAiC,GAAG;EAC1E,UAAU,aAAa,MAAM,OAAO,yBAA+B,GAAG;EACtE,MAAM,aAAa,MAAM,OAAO,oBAA0B,GAAG;EAC7D,SAAS,aAAa,MAAM,OAAO,uBAA6B,GAAG;EACnE,MAAM,aAAa,MAAM,OAAO,oBAA0B,GAAG;EAC7D,WAAW,aAAa,MAAM,OAAO,yBAA+B,GAAG;EACvE,eAAe,aAAa,MAAM,OAAO,8BAAoC,GAAG;EAChF,eAAe,aAAa,MAAM,OAAO,8BAAoC,GAAG;EAChF,aAAa,aAAa,MAAM,OAAO,4BAAkC,GAAG;EAC5E,cAAc,aAAa,MAAM,OAAO,6BAAmC,GAAG;EAC9E,YAAY,aAAa,MAAM,OAAO,2BAAiC,GAAG;EAC1E,aAAa,aAAa,MAAM,OAAO,4BAAkC,GAAG;EAC5E,OAAO,aAAa,MAAM,OAAO,qBAA2B,GAAG;EAC/D,OAAO,aAAa,MAAM,OAAO,qBAA2B,GAAG;EAC/D,SAAS,aAAa,MAAM,OAAO,uBAA6B,GAAG;EACnE,iBAAiB,aACd,MAAM,OAAO,+BAAqC,GAAG;EACxD,MAAM,aAAa,MAAM,OAAO,oBAA0B,GAAG;EAC7D,QAAQ,aAAa,MAAM,OAAO,sBAA4B,GAAG;EACjE,mBAAmB,aAChB,MAAM,OAAO,kCAAwC,GAAG;EAC3D,SAAS,aAAa,MAAM,OAAO,uBAA6B,GAAG;EACnE,YAAY,aAAa,MAAM,OAAO,0BAAgC,GAAG;EACzE,SAAS,aAAa,MAAM,OAAO,uBAA6B,GAAG;EACnE,WAAW,aAAa,MAAM,OAAO,yBAA+B,GAAG;EACvE,WAAW,aAAa,MAAM,OAAO,yBAA+B,GAAG;EACvE,cAAc,aAAa,MAAM,OAAO,6BAAmC,GAAG;EAC9E,UAAU,aAAa,MAAM,OAAO,wBAA8B,GAAG;EACrE,WAAW,aAAa,MAAM,OAAO,yBAA+B,GAAG;EACvE,SAAS,aAAa,MAAM,OAAO,uBAA6B,GAAG;EACnE,QAAQ,aAAa,MAAM,OAAO,sBAA4B,GAAG;EACjE,QAAQ,aAAa,MAAM,OAAO,sBAA4B,GAAG;EACjE,YAAY,aAAa,MAAM,OAAO,2BAAiC,GAAG;EAC1E,OAAO,aAAa,MAAM,OAAO,qBAA2B,GAAG;EAC/D,IAAI,aAAa,MAAM,OAAO,kBAAwB,GAAG;EACzD,YAAY,aAAa,MAAM,OAAO,2BAAiC,GAAG;EAC1E,WAAW,aAAa,MAAM,OAAO,yBAA+B,GAAG;EACvE,MAAM,aAAa,MAAM,OAAO,oBAA0B,GAAG;EAC7D,QAAQ,aAAa,MAAM,OAAO,sBAA4B,GAAG;EACjE,MAAM,aAAa,MAAM,OAAO,oBAA0B,GAAG;EAC7D,oBAAoB,aACjB,MAAM,OAAO,mCAAyC,GAAG;EAC5D,gBAAgB,aACb,MAAM,OAAO,8BAAoC,GAAG;EACvD,KAAK,aAAa,MAAM,OAAO,mBAAyB,GAAG;EAC3D,WAAW,aAAa,MAAM,OAAO,0BAAgC,GAAG;EACxE,MAAM,aAAa,MAAM,OAAO,oBAA0B,GAAG;EAC7D,QAAQ,aAAa,MAAM,OAAO,uBAA6B,GAAG;EAClE,mBAAmB,aAChB,MAAM,OAAO,kCAAwC,GAAG;EAC3D,SAAS,aAAa,MAAM,OAAO,uBAA6B,GAAG;EACnE,QAAQ,aAAa,MAAM,OAAO,sBAA4B,GAAG;EACjE,MAAM,aAAa,MAAM,OAAO,oBAA0B,GAAG;EAC7D,gBAAgB,aACb,MAAM,OAAO,8BAAoC,GAAG;EACvD,sBAAsB,aACnB,MAAM,OAAO,oCAA0C,GAAG;EAC7D,oBAAoB,aACjB,MAAM,OAAO,kCAAwC,GAAG;EAC3D,2BAA2B,aACxB,MAAM,OAAO,yCAA+C,GAAG;EAClE,cAAc,aAAa,MAAM,OAAO,4BAAkC,GAAG;EAC7E,UAAU,aAAa,MAAM,OAAO,wBAA8B,GAAG;EACrE,WAAW,aAAa,MAAM,OAAO,0BAAgC,GAAG;EACxE,QAAQ,aAAa,MAAM,OAAO,sBAA4B,GAAG;EACjE,SAAS,aAAa,MAAM,OAAO,iBAAuB,GAAG;EAC7D,aAAa,aAAa,MAAM,OAAO,2BAAiC,GAAG;EAC3E,QAAQ,aAAa,MAAM,OAAO,sBAA4B,GAAG;EACjE,cAAc,aAAa,MAAM,OAAO,6BAAmC,GAAG;EAC9E,QAAQ,aAAa,MAAM,OAAO,sBAA4B,GAAG;EACjE,eAAe,aAAa,MAAM,OAAO,6BAAmC,GAAG;EAC/E,yBAAyB,aACtB,MAAM,OAAO,6BAAmC,GAAG;EACtD,oBAAoB,aACjB,MAAM,OAAO,6BAAmC,GAAG;EACtD,mBAAmB,aAChB,MAAM,OAAO,kCAAwC,GAAG;EAC3D,eAAe,aAAa,MAAM,OAAO,8BAAoC,GAAG;EAChF,GAAG,aAAa,MAAM,OAAO,iBAAuB,GAAG;EACvD,MAAM,aAAa,MAAM,OAAO,oBAA0B,GAAG;EAC7D,qBAAqB,aAClB,MAAM,OAAO,oCAA0C,GAAG;EAC7D,OAAO,aAAa,MAAM,OAAO,0BAAgC,GAAG;;AAGtE,eAAe,OAAO,MAAc;AAClC,MAAI,eAAe,IAAI,IAAI,GAAG;AAC5B,WAAO,eAAe,IAAI,IAAI;EAChC;AAEA,QAAM,WAAW,MAAM,IAA0B,KAAK,MAAM;AAC5D,QAAM,aAAa,SAAQ;AAE3B,iBAAe,IAAI,MAAM,UAAU;AAEnC,SAAO;AACT;AAGO,IAAM,UAAN,MAAMC,iBAAgB,WAAU;EAAhC,cAAA;;AAIc,SAAA,OAAiB;AAEjB,SAAA,OAAiB;AAEjB,SAAA,QAAmB;AAEnB,SAAA,cAAc;EAYnC;EATkB,SAAM;AACpB,SAAK,MAAM,UAAU;uBACF,mBAAmB,KAAK,KAAK,IAAI;uBACjC,uBAAuB,KAAK,IAAI,IAAI;8BAC7B,KAAK,WAAW;;AAG1C,WAAO,OAAO,MAAM,OAAO,KAAK,IAAI,GAAG,kCAAkC,CAAC;EAC5E;;AApBuB,QAAA,SAAS,CAAC,aAAa,aAAaC,eAAM;AAG9CC,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAVE,UAAOA,YAAA;EADnB,cAAc,UAAU;GACZ,OAAO;;;ACrHpB,IAAAC,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACeR,IAAM,aAAN,MAAMC,oBAAmB,WAAU;EAAnC,cAAA;;AAIc,SAAA,OAAiB;AAEjB,SAAA,kBAA6B;AAE7B,SAAA,YAAuB;AAIvB,SAAA,aAA6B;AAEZ,SAAA,SAAU;AAE3B,SAAA,cAAkC;AAElC,SAAA,OAAiB;EAsCtC;EAnCkB,SAAM;AACpB,UAAM,WAAW,KAAK,YAAY,KAAK;AACvC,UAAM,OAAO,KAAK,SAAS;AAC3B,UAAM,OAAO,KAAK,SAAS;AAE3B,UAAM,QAAQ,OAAO,QAAQ;AAC7B,UAAM,eAAe,OAAO,QAAQ,OAAO,MAAM;AACjD,UAAM,SAAS,KAAK,eAAe;AACnC,UAAM,WAAW,KAAK,eAAe;AACrC,UAAM,gBACH,KAAK,oBAAoB,gBAAgB,YACzC,KAAK,oBAAoB,iBAAiB,YAC1C,KAAK,oBAAoB,eAAe,YACxC,KAAK,oBAAoB,iBAAiB;AAE7C,QAAI,kBAAkB,mBAAmB,KAAK,eAAe;AAE7D,QAAI,eAAe;AACjB,wBAAkB,yBAAyB,KAAK,eAAe;IACjE,WAAW,QAAQ;AACjB,wBAAkB,wBAAwB,KAAK,eAAe;IAChE;AAEA,SAAK,MAAM,UAAU;2BACE,eAAe;yBACjB,iBAAiB,SAAS,SAAS,KAAK;wDACT,YAAY;+CACrB,KAAK,IAAI;yBAC/B,KAAK,gBAAgB,qBAAqB,QAAQ,KAAK,UACvE,KAAK,SAAS,SAAS,KAAK,WAAW,MAAM,aAC/C;;AAGH,WAAO,wBAAwB,KAAK,SAAS,SAAS,QAAQ,SAAS,KAAK,IAAI;EAClF;;AAtDuB,WAAA,SAAS,CAAC,aAAa,eAAeC,eAAM;AAGhDC,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAE2BA,YAAA;EAAnC,SAAS,EAAE,MAAM,QAAO,CAAE;;AAERA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAlBE,aAAUA,YAAA;EADtB,cAAc,cAAc;GAChB,UAAU;;;ACfvB,IAAAC,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;ACOR,IAAM,WAAN,MAAMC,kBAAiB,WAAU;EAAjC,cAAA;;AAIc,SAAA,MAAM;AAEN,SAAA,MAAM;AAEN,SAAA,OAAkB;EAevC;EAZkB,SAAM;AACpB,SAAK,MAAM,UAAU;uBACF,KAAK,OAAO,uBAAuB,KAAK,IAAI,OAAO,MAAM;wBACxD,KAAK,OAAO,uBAAuB,KAAK,IAAI,OAAO,MAAM;;AAG7E,WAAO,gBAAgB,KAAK,GAAG,QAAQ,KAAK,GAAG,WAAW,KAAK,gBAAgB;EACjF;EAEQ,mBAAgB;AACtB,SAAK,cAAc,IAAI,YAAY,eAAe,EAAE,SAAS,MAAM,UAAU,KAAI,CAAE,CAAC;EACtF;;AArBuB,SAAA,SAAS,CAAC,aAAa,aAAaC,eAAM;AAG9CC,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AARE,WAAQA,YAAA;EADpB,cAAc,WAAW;GACb,QAAQ;;;ACPrB,IAAAC,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACQR,IAAM,SAAN,MAAMC,gBAAe,WAAU;EAA/B,cAAA;;AAIc,SAAA,UAAmB;AAEnB,SAAA,OAAoB;EAczC;EAXkB,SAAM;AACpB,SAAK,QAAQ,SAAS,IAAI,KAAK;AAC/B,SAAK,QAAQ,MAAM,IAAI,KAAK;AAC5B,UAAM,cAAc,KAAK,SAAS,OAAO,aAAa;AAEtD,WAAO;+BACoB,KAAK,OAAO,YAAY,WAAW;;;;EAIhE;;AAlBuB,OAAA,SAAS,CAAC,aAAaC,eAAM;AAGjCC,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AANE,SAAMA,YAAA;EADlB,cAAc,SAAS;GACX,MAAM;;;ACRnB,IAAAC,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACOR,IAAM,oBAAN,MAAMC,2BAA0B,WAAU;EAA1C,cAAA;;AAGc,SAAA,QAAmB;AAEnB,SAAA,OAA4D;EAcjF;EAXkB,SAAM;AACpB,SAAK,MAAM,UAAU,kBACnB,KAAK,UAAU,YAAY,YAAY,mBAAmB,KAAK,KAAK,GACtE;AAEA,SAAK,QAAQ,MAAM,IAAI,KAAK;AAE5B,WAAO;;;EAGT;;AAjBuB,kBAAA,SAAS,CAAC,aAAaC,eAAM;AAEjCC,YAAA;EAAlB,SAAQ;;AAEUA,YAAA;EAAlB,SAAQ;;AALE,oBAAiBA,YAAA;EAD7B,cAAc,qBAAqB;GACvB,iBAAiB;", "names": ["DEV_MODE", "issueWarning", "WuiFlex", "_a", "_b", "styles_default", "WuiText", "styles_default", "__decorate", "DEV_MODE", "_a", "_b", "_a", "styles_default", "WuiIcon", "styles_default", "__decorate", "styles_default", "WuiIconBox", "styles_default", "__decorate", "styles_default", "WuiImage", "styles_default", "__decorate", "styles_default", "WuiTag", "styles_default", "__decorate", "styles_default", "WuiLoadingSpinner", "styles_default", "__decorate"]}