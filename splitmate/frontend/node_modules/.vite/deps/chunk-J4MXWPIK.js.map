{"version": 3, "sources": ["../../@noble/curves/src/utils.ts"], "sourcesContent": ["/**\n * Hex, bytes and number utilities.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport {\n  abytes as abytes_,\n  bytesToHex as bytesToHex_,\n  concatBytes as concatBytes_,\n  hexToBytes as hexToBytes_,\n  isBytes as isBytes_,\n} from '@noble/hashes/utils.js';\nexport {\n  abytes,\n  anumber,\n  bytesToHex,\n  bytesToUtf8,\n  concatBytes,\n  hexToBytes,\n  isBytes,\n  randomBytes,\n  utf8ToBytes,\n} from '@noble/hashes/utils.js';\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nexport type Hex = Uint8Array | string; // hex strings are accepted for simplicity\nexport type PrivKey = Hex | bigint; // bigints are accepted to ease learning curve\nexport type CHash = {\n  (message: Uint8Array | string): Uint8Array;\n  blockLen: number;\n  outputLen: number;\n  create(opts?: { dkLen?: number }): any; // For shake\n};\nexport type FHash = (message: Uint8Array | string) => Uint8Array;\n\nexport function abool(title: string, value: boolean): void {\n  if (typeof value !== 'boolean') throw new Error(title + ' boolean expected, got ' + value);\n}\n\n// Used in weierstrass, der\nexport function numberToHexUnpadded(num: number | bigint): string {\n  const hex = num.toString(16);\n  return hex.length & 1 ? '0' + hex : hex;\n}\n\nexport function hexToNumber(hex: string): bigint {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  return hex === '' ? _0n : BigInt('0x' + hex); // Big Endian\n}\n\n// BE: Big Endian, LE: Little Endian\nexport function bytesToNumberBE(bytes: Uint8Array): bigint {\n  return hexToNumber(bytesToHex_(bytes));\n}\nexport function bytesToNumberLE(bytes: Uint8Array): bigint {\n  abytes_(bytes);\n  return hexToNumber(bytesToHex_(Uint8Array.from(bytes).reverse()));\n}\n\nexport function numberToBytesBE(n: number | bigint, len: number): Uint8Array {\n  return hexToBytes_(n.toString(16).padStart(len * 2, '0'));\n}\nexport function numberToBytesLE(n: number | bigint, len: number): Uint8Array {\n  return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nexport function numberToVarBytesBE(n: number | bigint): Uint8Array {\n  return hexToBytes_(numberToHexUnpadded(n));\n}\n\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nexport function ensureBytes(title: string, hex: Hex, expectedLength?: number): Uint8Array {\n  let res: Uint8Array;\n  if (typeof hex === 'string') {\n    try {\n      res = hexToBytes_(hex);\n    } catch (e) {\n      throw new Error(title + ' must be hex string or Uint8Array, cause: ' + e);\n    }\n  } else if (isBytes_(hex)) {\n    // Uint8Array.from() instead of hash.slice() because node.js Buffer\n    // is instance of Uint8Array, and its slice() creates **mutable** copy\n    res = Uint8Array.from(hex);\n  } else {\n    throw new Error(title + ' must be hex string or Uint8Array');\n  }\n  const len = res.length;\n  if (typeof expectedLength === 'number' && len !== expectedLength)\n    throw new Error(title + ' of length ' + expectedLength + ' expected, got ' + len);\n  return res;\n}\n\n// Compares 2 u8a-s in kinda constant time\nexport function equalBytes(a: Uint8Array, b: Uint8Array): boolean {\n  if (a.length !== b.length) return false;\n  let diff = 0;\n  for (let i = 0; i < a.length; i++) diff |= a[i] ^ b[i];\n  return diff === 0;\n}\n\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\n// export const utf8ToBytes: typeof utf8ToBytes_ = utf8ToBytes_;\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\n// export const bytesToUtf8: typeof bytesToUtf8_ = bytesToUtf8_;\n\n// Is positive bigint\nconst isPosBig = (n: bigint) => typeof n === 'bigint' && _0n <= n;\n\nexport function inRange(n: bigint, min: bigint, max: bigint): boolean {\n  return isPosBig(n) && isPosBig(min) && isPosBig(max) && min <= n && n < max;\n}\n\n/**\n * Asserts min <= n < max. NOTE: It's < max and not <= max.\n * @example\n * aInRange('x', x, 1n, 256n); // would assume x is in (1n..255n)\n */\nexport function aInRange(title: string, n: bigint, min: bigint, max: bigint): void {\n  // Why min <= n < max and not a (min < n < max) OR b (min <= n <= max)?\n  // consider P=256n, min=0n, max=P\n  // - a for min=0 would require -1:          `inRange('x', x, -1n, P)`\n  // - b would commonly require subtraction:  `inRange('x', x, 0n, P - 1n)`\n  // - our way is the cleanest:               `inRange('x', x, 0n, P)\n  if (!inRange(n, min, max))\n    throw new Error('expected valid ' + title + ': ' + min + ' <= n < ' + max + ', got ' + n);\n}\n\n// Bit operations\n\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n * TODO: merge with nLength in modular\n */\nexport function bitLen(n: bigint): number {\n  let len;\n  for (len = 0; n > _0n; n >>= _1n, len += 1);\n  return len;\n}\n\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nexport function bitGet(n: bigint, pos: number): bigint {\n  return (n >> BigInt(pos)) & _1n;\n}\n\n/**\n * Sets single bit at position.\n */\nexport function bitSet(n: bigint, pos: number, value: boolean): bigint {\n  return n | ((value ? _1n : _0n) << BigInt(pos));\n}\n\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nexport const bitMask = (n: number): bigint => (_1n << BigInt(n)) - _1n;\n\n// DRBG\n\ntype Pred<T> = (v: Uint8Array) => T | undefined;\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nexport function createHmacDrbg<T>(\n  hashLen: number,\n  qByteLen: number,\n  hmacFn: (key: Uint8Array, ...messages: Uint8Array[]) => Uint8Array\n): (seed: Uint8Array, predicate: Pred<T>) => T {\n  if (typeof hashLen !== 'number' || hashLen < 2) throw new Error('hashLen must be a number');\n  if (typeof qByteLen !== 'number' || qByteLen < 2) throw new Error('qByteLen must be a number');\n  if (typeof hmacFn !== 'function') throw new Error('hmacFn must be a function');\n  // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n  const u8n = (len: number) => new Uint8Array(len); // creates Uint8Array\n  const u8of = (byte: number) => Uint8Array.of(byte); // another shortcut\n  let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n  let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n  let i = 0; // Iterations counter, will throw when over 1000\n  const reset = () => {\n    v.fill(1);\n    k.fill(0);\n    i = 0;\n  };\n  const h = (...b: Uint8Array[]) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n  const reseed = (seed = u8n(0)) => {\n    // HMAC-DRBG reseed() function. Steps D-G\n    k = h(u8of(0x00), seed); // k = hmac(k || v || 0x00 || seed)\n    v = h(); // v = hmac(k || v)\n    if (seed.length === 0) return;\n    k = h(u8of(0x01), seed); // k = hmac(k || v || 0x01 || seed)\n    v = h(); // v = hmac(k || v)\n  };\n  const gen = () => {\n    // HMAC-DRBG generate() function\n    if (i++ >= 1000) throw new Error('drbg: tried 1000 values');\n    let len = 0;\n    const out: Uint8Array[] = [];\n    while (len < qByteLen) {\n      v = h();\n      const sl = v.slice();\n      out.push(sl);\n      len += v.length;\n    }\n    return concatBytes_(...out);\n  };\n  const genUntil = (seed: Uint8Array, pred: Pred<T>): T => {\n    reset();\n    reseed(seed); // Steps D-G\n    let res: T | undefined = undefined; // Step H: grind until k is in [1..n-1]\n    while (!(res = pred(gen()))) reseed();\n    reset();\n    return res;\n  };\n  return genUntil;\n}\n\n// Validating curves and fields\n\nconst validatorFns = {\n  bigint: (val: any): boolean => typeof val === 'bigint',\n  function: (val: any): boolean => typeof val === 'function',\n  boolean: (val: any): boolean => typeof val === 'boolean',\n  string: (val: any): boolean => typeof val === 'string',\n  stringOrUint8Array: (val: any): boolean => typeof val === 'string' || isBytes_(val),\n  isSafeInteger: (val: any): boolean => Number.isSafeInteger(val),\n  array: (val: any): boolean => Array.isArray(val),\n  field: (val: any, object: any): any => (object as any).Fp.isValid(val),\n  hash: (val: any): boolean => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n} as const;\ntype Validator = keyof typeof validatorFns;\ntype ValMap<T extends Record<string, any>> = { [K in keyof T]?: Validator };\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\n\nexport function validateObject<T extends Record<string, any>>(\n  object: T,\n  validators: ValMap<T>,\n  optValidators: ValMap<T> = {}\n): T {\n  const checkField = (fieldName: keyof T, type: Validator, isOptional: boolean) => {\n    const checkVal = validatorFns[type];\n    if (typeof checkVal !== 'function') throw new Error('invalid validator function');\n\n    const val = object[fieldName as keyof typeof object];\n    if (isOptional && val === undefined) return;\n    if (!checkVal(val, object)) {\n      throw new Error(\n        'param ' + String(fieldName) + ' is invalid. Expected ' + type + ', got ' + val\n      );\n    }\n  };\n  for (const [fieldName, type] of Object.entries(validators)) checkField(fieldName, type!, false);\n  for (const [fieldName, type] of Object.entries(optValidators)) checkField(fieldName, type!, true);\n  return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n\nexport function isHash(val: CHash): boolean {\n  return typeof val === 'function' && Number.isSafeInteger(val.outputLen);\n}\nexport function _validateObject(\n  object: Record<string, any>,\n  fields: Record<string, string>,\n  optFields: Record<string, string> = {}\n): void {\n  if (!object || typeof object !== 'object') throw new Error('expected valid options object');\n  type Item = keyof typeof object;\n  function checkField(fieldName: Item, expectedType: string, isOpt: boolean) {\n    const val = object[fieldName];\n    if (isOpt && val === undefined) return;\n    const current = typeof val;\n    if (current !== expectedType || val === null)\n      throw new Error(`param \"${fieldName}\" is invalid: expected ${expectedType}, got ${current}`);\n  }\n  Object.entries(fields).forEach(([k, v]) => checkField(k, v, false));\n  Object.entries(optFields).forEach(([k, v]) => checkField(k, v, true));\n}\n\n/**\n * throws not implemented error\n */\nexport const notImplemented = (): never => {\n  throw new Error('not implemented');\n};\n\n/**\n * Memoizes (caches) computation result.\n * Uses WeakMap: the value is going auto-cleaned by GC after last reference is removed.\n */\nexport function memoized<T extends object, R, O extends any[]>(\n  fn: (arg: T, ...args: O) => R\n): (arg: T, ...args: O) => R {\n  const map = new WeakMap<T, R>();\n  return (arg: T, ...args: O): R => {\n    const val = map.get(arg);\n    if (val !== undefined) return val;\n    const computed = fn(arg, ...args);\n    map.set(arg, computed);\n    return computed;\n  };\n}\n"], "mappings": ";;;;;;;;;AAuBA,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,MAAsB,OAAO,CAAC;AAW9B,SAAU,MAAM,OAAe,OAAc;AACjD,MAAI,OAAO,UAAU;AAAW,UAAM,IAAI,MAAM,QAAQ,4BAA4B,KAAK;AAC3F;AAGM,SAAU,oBAAoB,KAAoB;AACtD,QAAM,MAAM,IAAI,SAAS,EAAE;AAC3B,SAAO,IAAI,SAAS,IAAI,MAAM,MAAM;AACtC;AAEM,SAAU,YAAY,KAAW;AACrC,MAAI,OAAO,QAAQ;AAAU,UAAM,IAAI,MAAM,8BAA8B,OAAO,GAAG;AACrF,SAAO,QAAQ,KAAK,MAAM,OAAO,OAAO,GAAG;AAC7C;AAGM,SAAU,gBAAgB,OAAiB;AAC/C,SAAO,YAAY,WAAY,KAAK,CAAC;AACvC;AACM,SAAU,gBAAgB,OAAiB;AAC/C,SAAQ,KAAK;AACb,SAAO,YAAY,WAAY,WAAW,KAAK,KAAK,EAAE,QAAO,CAAE,CAAC;AAClE;AAEM,SAAU,gBAAgB,GAAoB,KAAW;AAC7D,SAAO,WAAY,EAAE,SAAS,EAAE,EAAE,SAAS,MAAM,GAAG,GAAG,CAAC;AAC1D;AACM,SAAU,gBAAgB,GAAoB,KAAW;AAC7D,SAAO,gBAAgB,GAAG,GAAG,EAAE,QAAO;AACxC;AAeM,SAAU,YAAY,OAAe,KAAU,gBAAuB;AAC1E,MAAI;AACJ,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI;AACF,YAAM,WAAY,GAAG;IACvB,SAAS,GAAG;AACV,YAAM,IAAI,MAAM,QAAQ,+CAA+C,CAAC;IAC1E;EACF,WAAW,QAAS,GAAG,GAAG;AAGxB,UAAM,WAAW,KAAK,GAAG;EAC3B,OAAO;AACL,UAAM,IAAI,MAAM,QAAQ,mCAAmC;EAC7D;AACA,QAAM,MAAM,IAAI;AAChB,MAAI,OAAO,mBAAmB,YAAY,QAAQ;AAChD,UAAM,IAAI,MAAM,QAAQ,gBAAgB,iBAAiB,oBAAoB,GAAG;AAClF,SAAO;AACT;AAqBA,IAAM,WAAW,CAAC,MAAc,OAAO,MAAM,YAAY,OAAO;AAE1D,SAAU,QAAQ,GAAW,KAAa,KAAW;AACzD,SAAO,SAAS,CAAC,KAAK,SAAS,GAAG,KAAK,SAAS,GAAG,KAAK,OAAO,KAAK,IAAI;AAC1E;AAOM,SAAU,SAAS,OAAe,GAAW,KAAa,KAAW;AAMzE,MAAI,CAAC,QAAQ,GAAG,KAAK,GAAG;AACtB,UAAM,IAAI,MAAM,oBAAoB,QAAQ,OAAO,MAAM,aAAa,MAAM,WAAW,CAAC;AAC5F;AASM,SAAU,OAAO,GAAS;AAC9B,MAAI;AACJ,OAAK,MAAM,GAAG,IAAI,KAAK,MAAM,KAAK,OAAO;AAAE;AAC3C,SAAO;AACT;AAsBO,IAAM,UAAU,CAAC,OAAuB,OAAO,OAAO,CAAC,KAAK;AAY7D,SAAU,eACd,SACA,UACA,QAAkE;AAElE,MAAI,OAAO,YAAY,YAAY,UAAU;AAAG,UAAM,IAAI,MAAM,0BAA0B;AAC1F,MAAI,OAAO,aAAa,YAAY,WAAW;AAAG,UAAM,IAAI,MAAM,2BAA2B;AAC7F,MAAI,OAAO,WAAW;AAAY,UAAM,IAAI,MAAM,2BAA2B;AAE7E,QAAM,MAAM,CAAC,QAAgB,IAAI,WAAW,GAAG;AAC/C,QAAM,OAAO,CAAC,SAAiB,WAAW,GAAG,IAAI;AACjD,MAAI,IAAI,IAAI,OAAO;AACnB,MAAI,IAAI,IAAI,OAAO;AACnB,MAAI,IAAI;AACR,QAAM,QAAQ,MAAK;AACjB,MAAE,KAAK,CAAC;AACR,MAAE,KAAK,CAAC;AACR,QAAI;EACN;AACA,QAAM,IAAI,IAAI,MAAoB,OAAO,GAAG,GAAG,GAAG,CAAC;AACnD,QAAM,SAAS,CAAC,OAAO,IAAI,CAAC,MAAK;AAE/B,QAAI,EAAE,KAAK,CAAI,GAAG,IAAI;AACtB,QAAI,EAAC;AACL,QAAI,KAAK,WAAW;AAAG;AACvB,QAAI,EAAE,KAAK,CAAI,GAAG,IAAI;AACtB,QAAI,EAAC;EACP;AACA,QAAM,MAAM,MAAK;AAEf,QAAI,OAAO;AAAM,YAAM,IAAI,MAAM,yBAAyB;AAC1D,QAAI,MAAM;AACV,UAAM,MAAoB,CAAA;AAC1B,WAAO,MAAM,UAAU;AACrB,UAAI,EAAC;AACL,YAAM,KAAK,EAAE,MAAK;AAClB,UAAI,KAAK,EAAE;AACX,aAAO,EAAE;IACX;AACA,WAAO,YAAa,GAAG,GAAG;EAC5B;AACA,QAAM,WAAW,CAAC,MAAkB,SAAoB;AACtD,UAAK;AACL,WAAO,IAAI;AACX,QAAI,MAAqB;AACzB,WAAO,EAAE,MAAM,KAAK,IAAG,CAAE;AAAI,aAAM;AACnC,UAAK;AACL,WAAO;EACT;AACA,SAAO;AACT;AAiDM,SAAU,OAAO,KAAU;AAC/B,SAAO,OAAO,QAAQ,cAAc,OAAO,cAAc,IAAI,SAAS;AACxE;AACM,SAAU,gBACd,QACA,QACA,YAAoC,CAAA,GAAE;AAEtC,MAAI,CAAC,UAAU,OAAO,WAAW;AAAU,UAAM,IAAI,MAAM,+BAA+B;AAE1F,WAAS,WAAW,WAAiB,cAAsB,OAAc;AACvE,UAAM,MAAM,OAAO,SAAS;AAC5B,QAAI,SAAS,QAAQ;AAAW;AAChC,UAAM,UAAU,OAAO;AACvB,QAAI,YAAY,gBAAgB,QAAQ;AACtC,YAAM,IAAI,MAAM,UAAU,SAAS,0BAA0B,YAAY,SAAS,OAAO,EAAE;EAC/F;AACA,SAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,WAAW,GAAG,GAAG,KAAK,CAAC;AAClE,SAAO,QAAQ,SAAS,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,WAAW,GAAG,GAAG,IAAI,CAAC;AACtE;AAaM,SAAU,SACd,IAA6B;AAE7B,QAAM,MAAM,oBAAI,QAAO;AACvB,SAAO,CAAC,QAAW,SAAc;AAC/B,UAAM,MAAM,IAAI,IAAI,GAAG;AACvB,QAAI,QAAQ;AAAW,aAAO;AAC9B,UAAM,WAAW,GAAG,KAAK,GAAG,IAAI;AAChC,QAAI,IAAI,KAAK,QAAQ;AACrB,WAAO;EACT;AACF;", "names": []}