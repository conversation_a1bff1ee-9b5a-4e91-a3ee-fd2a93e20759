import { useState } from 'react'
import { Check, Clock, Users, DollarSign, MoreHorizontal } from 'lucide-react'

interface Expense {
  id: string
  name: string
  amount: number
  currency: string
  participants: number
  paidCount: number
  status: 'active' | 'completed' | 'overdue'
  createdAt: string
  category: string
}

const mockExpenses: Expense[] = [
  {
    id: '1',
    name: 'Pizza Night',
    amount: 90,
    currency: 'USDC',
    participants: 3,
    paidCount: 2,
    status: 'active',
    createdAt: '2024-01-15',
    category: 'Food'
  },
  {
    id: '2',
    name: 'Uber to Airport',
    amount: 45,
    currency: 'USDC',
    participants: 2,
    paidCount: 2,
    status: 'completed',
    createdAt: '2024-01-14',
    category: 'Transportation'
  },
  {
    id: '3',
    name: 'Movie Tickets',
    amount: 60,
    currency: 'USDC',
    participants: 4,
    paidCount: 1,
    status: 'overdue',
    createdAt: '2024-01-12',
    category: 'Entertainment'
  },
  {
    id: '4',
    name: 'Grocery Shopping',
    amount: 120,
    currency: 'USDC',
    participants: 3,
    paidCount: 3,
    status: 'completed',
    createdAt: '2024-01-10',
    category: 'Food'
  }
]

export function ExpenseList() {
  const [filter, setFilter] = useState<'all' | 'active' | 'completed' | 'overdue'>('all')

  const filteredExpenses = mockExpenses.filter(expense => 
    filter === 'all' || expense.status === filter
  )

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <Check className="w-4 h-4 text-green-600" />
      case 'overdue':
        return <Clock className="w-4 h-4 text-red-600" />
      default:
        return <Clock className="w-4 h-4 text-yellow-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
    
    switch (status) {
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800`
      case 'overdue':
        return `${baseClasses} bg-red-100 text-red-800`
      default:
        return `${baseClasses} bg-yellow-100 text-yellow-800`
    }
  }

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Expenses</h3>
            <p className="text-sm text-gray-600">Manage your group expenses</p>
          </div>
          
          {/* Filter Buttons */}
          <div className="flex space-x-2">
            {(['all', 'active', 'completed', 'overdue'] as const).map((filterOption) => (
              <button
                key={filterOption}
                onClick={() => setFilter(filterOption)}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  filter === filterOption
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                }`}
              >
                {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      <div className="card-content">
        <div className="space-y-4">
          {filteredExpenses.map((expense) => (
            <div
              key={expense.id}
              className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-4">
                <div className="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-lg">
                  {getStatusIcon(expense.status)}
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900">{expense.name}</h4>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span className="flex items-center">
                      <DollarSign className="w-3 h-3 mr-1" />
                      {expense.amount} {expense.currency}
                    </span>
                    <span className="flex items-center">
                      <Users className="w-3 h-3 mr-1" />
                      {expense.paidCount}/{expense.participants} paid
                    </span>
                    <span>{expense.category}</span>
                    <span>{new Date(expense.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <span className={getStatusBadge(expense.status)}>
                  {expense.status}
                </span>
                
                <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                  <MoreHorizontal className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
          
          {filteredExpenses.length === 0 && (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <DollarSign className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No expenses found</h3>
              <p className="text-gray-600">
                {filter === 'all' 
                  ? "You haven't created any expenses yet."
                  : `No ${filter} expenses found.`
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
