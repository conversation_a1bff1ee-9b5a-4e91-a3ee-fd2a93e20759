import { Check, Plus, Send, Clock } from 'lucide-react'

interface Activity {
  id: string
  type: 'expense_created' | 'payment_received' | 'reminder_sent' | 'expense_completed'
  title: string
  description: string
  timestamp: string
  amount?: number
  currency?: string
  user?: string
}

const mockActivities: Activity[] = [
  {
    id: '1',
    type: 'payment_received',
    title: 'Payment Received',
    description: '<PERSON> paid $30 for Pizza Night',
    timestamp: '2024-01-15T10:30:00Z',
    amount: 30,
    currency: 'USDC',
    user: 'alice.base'
  },
  {
    id: '2',
    type: 'expense_created',
    title: 'New Expense Created',
    description: 'Pizza Night expense created for $90',
    timestamp: '2024-01-15T09:15:00Z',
    amount: 90,
    currency: 'USDC'
  },
  {
    id: '3',
    type: 'expense_completed',
    title: 'Expense Completed',
    description: 'Uber to Airport - all payments received',
    timestamp: '2024-01-14T16:45:00Z',
    amount: 45,
    currency: 'USDC'
  },
  {
    id: '4',
    type: 'reminder_sent',
    title: '<PERSON>mind<PERSON>',
    description: 'Payment reminder sent to <PERSON> for Movie Tickets',
    timestamp: '2024-01-13T14:20:00Z',
    user: 'bob.base'
  },
  {
    id: '5',
    type: 'payment_received',
    title: 'Payment Received',
    description: 'Carol paid $40 for Grocery Shopping',
    timestamp: '2024-01-12T11:10:00Z',
    amount: 40,
    currency: 'USDC',
    user: 'carol.base'
  }
]

interface RecentActivityProps {
  showAll?: boolean
}

export function RecentActivity({ showAll = false }: RecentActivityProps) {
  const activities = showAll ? mockActivities : mockActivities.slice(0, 5)

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'expense_created':
        return <Plus className="w-4 h-4 text-blue-600" />
      case 'payment_received':
        return <Check className="w-4 h-4 text-green-600" />
      case 'reminder_sent':
        return <Send className="w-4 h-4 text-yellow-600" />
      case 'expense_completed':
        return <Check className="w-4 h-4 text-green-600" />
      default:
        return <Clock className="w-4 h-4 text-gray-600" />
    }
  }

  const getActivityBgColor = (type: string) => {
    switch (type) {
      case 'expense_created':
        return 'bg-blue-100'
      case 'payment_received':
        return 'bg-green-100'
      case 'reminder_sent':
        return 'bg-yellow-100'
      case 'expense_completed':
        return 'bg-green-100'
      default:
        return 'bg-gray-100'
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-gray-900">
          {showAll ? 'All Activity' : 'Recent Activity'}
        </h3>
        <p className="text-sm text-gray-600">
          {showAll ? 'Complete activity history' : 'Latest updates and transactions'}
        </p>
      </div>
      
      <div className="card-content">
        <div className="space-y-4">
          {activities.map((activity, index) => (
            <div key={activity.id} className="flex items-start space-x-3">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${getActivityBgColor(activity.type)}`}>
                {getActivityIcon(activity.type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatTimestamp(activity.timestamp)}
                  </p>
                </div>
                
                <p className="text-sm text-gray-600 mt-1">
                  {activity.description}
                </p>
                
                {activity.amount && (
                  <p className="text-sm font-medium text-gray-900 mt-1">
                    ${activity.amount} {activity.currency}
                  </p>
                )}
                
                {activity.user && (
                  <p className="text-xs text-gray-500 mt-1">
                    @{activity.user}
                  </p>
                )}
              </div>
            </div>
          ))}
          
          {activities.length === 0 && (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No activity yet</h3>
              <p className="text-gray-600">
                Activity will appear here as you create expenses and receive payments.
              </p>
            </div>
          )}
        </div>
        
        {!showAll && activities.length > 0 && (
          <div className="mt-6 text-center">
            <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
              View all activity
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
