import { useState } from 'react'
import { X, Plus, Trash2, Users } from 'lucide-react'

interface CreateExpenseFormProps {
  onClose: () => void
}

export function CreateExpenseForm({ onClose }: CreateExpenseFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    amount: '',
    currency: 'USDC',
    category: 'Food',
    participants: ['']
  })

  const categories = [
    'Food',
    'Transportation',
    'Entertainment',
    'Shopping',
    'Utilities',
    'Travel',
    'Other'
  ]

  const addParticipant = () => {
    setFormData(prev => ({
      ...prev,
      participants: [...prev.participants, '']
    }))
  }

  const removeParticipant = (index: number) => {
    setFormData(prev => ({
      ...prev,
      participants: prev.participants.filter((_, i) => i !== index)
    }))
  }

  const updateParticipant = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      participants: prev.participants.map((p, i) => i === index ? value : p)
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    if (!formData.name || !formData.amount) {
      alert('Please fill in all required fields')
      return
    }

    const validParticipants = formData.participants.filter(p => p.trim() !== '')
    if (validParticipants.length === 0) {
      alert('Please add at least one participant')
      return
    }

    // Here you would typically send the data to your backend
    console.log('Creating expense:', {
      ...formData,
      participants: validParticipants,
      amount: parseFloat(formData.amount)
    })

    // Show success message and close form
    alert('Expense created successfully!')
    onClose()
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Create New Expense</h2>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X className="w-6 h-6" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Expense Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Expense Name *
          </label>
          <input
            type="text"
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="e.g., Pizza Night, Uber ride, Movie tickets"
            className="input"
            required
          />
        </div>

        {/* Amount and Currency */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
              Amount *
            </label>
            <input
              type="number"
              id="amount"
              value={formData.amount}
              onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
              placeholder="0.00"
              step="0.01"
              min="0"
              className="input"
              required
            />
          </div>
          <div>
            <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-2">
              Currency
            </label>
            <select
              id="currency"
              value={formData.currency}
              onChange={(e) => setFormData(prev => ({ ...prev, currency: e.target.value }))}
              className="input"
            >
              <option value="USDC">USDC</option>
              <option value="ETH">ETH</option>
            </select>
          </div>
        </div>

        {/* Category */}
        <div>
          <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
            Category
          </label>
          <select
            id="category"
            value={formData.category}
            onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
            className="input"
          >
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>

        {/* Participants */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Participants *
          </label>
          <div className="space-y-3">
            {formData.participants.map((participant, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div className="flex-1">
                  <input
                    type="text"
                    value={participant}
                    onChange={(e) => updateParticipant(index, e.target.value)}
                    placeholder="@username.base or 0x..."
                    className="input"
                  />
                </div>
                {formData.participants.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeParticipant(index)}
                    className="p-2 text-red-500 hover:text-red-700 transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                )}
              </div>
            ))}
            
            <button
              type="button"
              onClick={addParticipant}
              className="flex items-center space-x-2 text-primary-600 hover:text-primary-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span className="text-sm font-medium">Add Participant</span>
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Use @username.base for Base usernames or wallet addresses (0x...)
          </p>
        </div>

        {/* Summary */}
        {formData.amount && formData.participants.filter(p => p.trim()).length > 0 && (
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Users className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-900">Split Summary</span>
            </div>
            <p className="text-sm text-gray-600">
              Each person will pay{' '}
              <span className="font-medium">
                {(parseFloat(formData.amount || '0') / formData.participants.filter(p => p.trim()).length).toFixed(2)} {formData.currency}
              </span>
            </p>
          </div>
        )}

        {/* Actions */}
        <div className="flex space-x-3 pt-4">
          <button
            type="button"
            onClick={onClose}
            className="btn btn-secondary flex-1 py-2"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary flex-1 py-2"
          >
            Create Expense
          </button>
        </div>
      </form>
    </div>
  )
}
