import { useAccount, useBalance } from 'wagmi'
import { base } from 'wagmi/chains'
import { Wallet, TrendingUp, TrendingDown, DollarSign } from 'lucide-react'

// USDC contract address on Base
const USDC_ADDRESS = '******************************************'

export function WalletBalance() {
  const { address } = useAccount()
  
  // Get ETH balance
  const { data: ethBalance, isLoading: ethLoading } = useBalance({
    address,
    chainId: base.id,
  })

  // Get USDC balance
  const { data: usdcBalance, isLoading: usdcLoading } = useBalance({
    address,
    token: USDC_ADDRESS as `0x${string}`,
    chainId: base.id,
  })

  const formatBalance = (balance: string | undefined, decimals: number = 4) => {
    if (!balance) return '0.00'
    const num = parseFloat(balance)
    return num.toFixed(decimals)
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {/* ETH Balance */}
      <div className="card">
        <div className="card-content">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">ETH Balance</p>
              <p className="text-2xl font-bold text-gray-900">
                {ethLoading ? (
                  <span className="animate-pulse">Loading...</span>
                ) : (
                  `${formatBalance(ethBalance?.formatted)} ETH`
                )}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Wallet className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>
      </div>

      {/* USDC Balance */}
      <div className="card">
        <div className="card-content">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">USDC Balance</p>
              <p className="text-2xl font-bold text-gray-900">
                {usdcLoading ? (
                  <span className="animate-pulse">Loading...</span>
                ) : (
                  `$${formatBalance(usdcBalance?.formatted, 2)}`
                )}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Expense Summary */}
      <div className="card">
        <div className="card-content">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">This Month</p>
              <div className="flex items-center space-x-2">
                <p className="text-2xl font-bold text-gray-900">$247.50</p>
                <div className="flex items-center text-green-600">
                  <TrendingDown className="w-4 h-4" />
                  <span className="text-sm font-medium">12%</span>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">vs last month</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
