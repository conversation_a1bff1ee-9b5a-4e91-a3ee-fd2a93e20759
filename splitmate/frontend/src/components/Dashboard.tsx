import { useState } from 'react'
import { useAccount } from 'wagmi'
import { Expense<PERSON>hart } from './ExpenseChart'
import { ExpenseList } from './ExpenseList'
import { CreateExpenseForm } from './CreateExpenseForm'
import { WalletBalance } from './WalletBalance'
import { RecentActivity } from './RecentActivity'
import { Plus, PieChart, List, Activity, Wallet } from 'lucide-react'

export function Dashboard() {
  const { isConnected } = useAccount()
  const [activeTab, setActiveTab] = useState<'overview' | 'expenses' | 'activity'>('overview')
  const [showCreateForm, setShowCreateForm] = useState(false)

  if (!isConnected) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <Wallet className="w-8 h-8 text-gray-400" />
        </div>
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">Connect Your Wallet</h2>
        <p className="text-gray-600 max-w-md">
          Connect your wallet to start splitting expenses with your friends and track payments on Base network.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Manage your group expenses and payments</p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="btn btn-primary px-4 py-2 mt-4 sm:mt-0"
        >
          <Plus className="w-4 h-4 mr-2" />
          New Expense
        </button>
      </div>

      {/* Wallet Balance Card */}
      <WalletBalance />

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'overview'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <PieChart className="w-4 h-4 inline mr-2" />
            Overview
          </button>
          <button
            onClick={() => setActiveTab('expenses')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'expenses'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <List className="w-4 h-4 inline mr-2" />
            Expenses
          </button>
          <button
            onClick={() => setActiveTab('activity')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'activity'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Activity className="w-4 h-4 inline mr-2" />
            Activity
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ExpenseChart />
            <RecentActivity />
          </div>
        )}
        
        {activeTab === 'expenses' && <ExpenseList />}
        
        {activeTab === 'activity' && <RecentActivity showAll />}
      </div>

      {/* Create Expense Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <CreateExpenseForm onClose={() => setShowCreateForm(false)} />
          </div>
        </div>
      )}
    </div>
  )
}
