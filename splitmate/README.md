# SplitMate 🧾💰

A secure group chat agent that helps users split expenses, send reminders, reward contributors, and manage a shared wallet—all inside XMTP-enabled chats.

## 🎯 Buildathon Submission

**Focus Area:** Utility Agents (with extensions into Social Agents and DeFi Agents)

**Tech Stack:**
- XMTP: Messaging backend + group DMs
- AgentKit + Basenames: Agent scaffolding and user-friendly handles
- Coinbase Wallet: For onchain transactions
- OnchainKit: Payment links and token actions
- Base Mainnet: For USDC transactions and wallet management

## 🛠️ Core Features

### 1. Group Expense Splitting
- Users tag the agent to create a new "expense" (e.g., `/split pizza 90 @alice @bob @carol`)
- Agent calculates shares, sends DMs with payment links (via Coinbase Wallet / OnchainKit)

### 2. Reminders & Settlements
- Tracks who paid and who hasn't
- Sends reminders like "💸 Carol still owes 30 USDC for Pizza 🍕"

### 3. Shared Wallet Tracker
- View total contributions and balances in a shared group wallet
- Initiate onchain actions: transfer, swap, or contribute

### 4. Reward Contributions
- Admins can reward users with POAP-style NFTs or Base tokens (e.g., for paying on time or organizing events)

### 5. Mini-App Integration (Optional)
- Visual expense charts and wallet stats via embedded mini-apps inside the chat

## 📁 Project Structure

```
/splitmate
├── /agent        → XMTP Agent code (backend)
├── /frontend     → Mini app (React, optional)
├── /contracts    → NFT reward logic (optional)
└── README.md     → Buildathon requirements
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Base wallet for testing
- XMTP-compatible wallet

### Installation

1. **Clone and Setup:**
```bash
git clone <repository-url>
cd splitmate
```

2. **Backend Agent Setup:**
```bash
cd agent
npm install
cp .env.example .env
# Edit .env with your XMTP private key and configuration
npm run dev
```

3. **Frontend Setup:**
```bash
cd frontend
npm install
cp .env.example .env
# Edit .env with your WalletConnect project ID
npm run dev
```

4. **Smart Contracts Setup:**
```bash
cd contracts
npm install
cp .env.example .env
# Edit .env with your private key for deployment
npx hardhat compile
npx hardhat run scripts/deploy.js --network baseSepolia
```

### 🧪 Testing

**Test Core Agent Functionality:**
```bash
cd agent
node test.js
```

**Test Frontend:**
```bash
cd frontend
npm run dev
# Open http://localhost:5173
```

**Test Smart Contracts:**
```bash
cd contracts
npx hardhat test
npx hardhat run scripts/deploy.js --network hardhat
```

## 🧪 Usage Examples

### Basic Expense Split
```
/split dinner 120 @alice.base @bob.base @carol.base
```
Agent responds: "💰 Dinner split created! Each person owes 40 USDC. Payment links sent via DM."

### Check Status
```
/status dinner
```
Agent responds: "🍽️ Dinner Status: Alice ✅ paid, Bob ⏳ pending, Carol ⏳ pending"

### Reminders
Agent automatically sends: "💸 Hey Bob! You still owe 40 USDC for Dinner 🍽️. [Pay Now](payment-link)"

## 🏗️ Development Phases

- ✅ **Phase 1:** Project Setup
- ✅ **Phase 2:** Backend Agent (Core Logic)
- ✅ **Phase 3:** Onchain Actions
- ✅ **Phase 4:** Notifications & Reminders
- ✅ **Phase 5:** Rewards (Optional)
- ✅ **Phase 6:** Frontend Mini App (Optional)

## 🎯 Implementation Status

### ✅ Completed Features

**Backend Agent (XMTP Integration)**
- Command parsing for `/split`, `/status`, `/pay`, `/help`
- Expense management with in-memory storage
- Basenames resolution for user-friendly handles
- Payment tracking on Base network
- Automated reminder system with scheduling
- Direct messaging for payment links and reminders

**Onchain Integration**
- Base network integration with ethers.js
- USDC payment tracking and monitoring
- Coinbase Wallet payment link generation
- Transaction confirmation monitoring
- Gas estimation and balance checking

**Frontend Mini App**
- React + TypeScript + Tailwind CSS
- Wagmi integration for wallet connection
- Dashboard with expense visualization
- Pie chart for expense breakdown
- Real-time balance display (ETH + USDC)
- Expense list with filtering
- Activity feed with transaction history
- Create expense form with participant management

**Smart Contracts**
- ERC-721 NFT reward system
- Achievement-based minting (POAP-style)
- Batch minting capabilities
- Soulbound token option
- Base network deployment ready

### 🔧 Core Components

**Agent Services:**
- `SplitMateAgent.js` - Main XMTP agent orchestrator
- `CommandParser.js` - Message command parsing
- `ExpenseManager.js` - Expense state management
- `PaymentTracker.js` - Blockchain payment monitoring
- `ReminderService.js` - Automated notification system
- `BasenameResolver.js` - Username to address resolution
- `OnchainService.js` - Base network operations

**Frontend Components:**
- `Dashboard.tsx` - Main dashboard interface
- `Header.tsx` - Wallet connection and navigation
- `WalletBalance.tsx` - Real-time balance display
- `ExpenseChart.tsx` - Visual expense breakdown
- `ExpenseList.tsx` - Expense management interface
- `RecentActivity.tsx` - Transaction activity feed
- `CreateExpenseForm.tsx` - New expense creation

**Smart Contracts:**
- `SplitMateRewards.sol` - NFT achievement system

## 🤝 Contributing

This project is built for the XMTP Buildathon. Feel free to contribute improvements and features!

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   XMTP Agent    │    │  Frontend App   │    │ Smart Contracts │
│                 │    │                 │    │                 │
│ • Command Parse │    │ • React + Vite  │    │ • NFT Rewards   │
│ • Expense Mgmt  │    │ • Wagmi + Viem  │    │ • Achievement   │
│ • Payment Track │    │ • Tailwind CSS  │    │   System        │
│ • Reminders     │    │ • Charts        │    │ • Base Network  │
│ • Basenames     │    │ • Wallet Conn   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Base Network   │
                    │                 │
                    │ • USDC Payments │
                    │ • Transaction   │
                    │   Monitoring    │
                    │ • Gas Tracking  │
                    └─────────────────┘
```

## 🎮 Advanced Usage

### Custom Achievement Types

Add new achievement types to the NFT contract:

```javascript
// In your agent code
await rewardsContract.updateAchievementMetadata(
  "super_splitter",
  "ipfs://QmSuperSplitterMetadata"
);

// Mint the achievement
await rewardsContract.mintReward(userAddress, "super_splitter");
```

### Webhook Integration

Set up webhooks for real-time payment notifications:

```javascript
// In PaymentTracker.js
onPaymentConfirmed(payment) {
  // Send webhook to external services
  fetch('https://your-webhook-url.com/payment-confirmed', {
    method: 'POST',
    body: JSON.stringify(payment)
  });
}
```

### Database Integration

Replace in-memory storage with persistent database:

```javascript
// Example with Supabase
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// In ExpenseManager.js
async createExpense(expenseData) {
  const { data, error } = await supabase
    .from('expenses')
    .insert([expenseData]);

  return data;
}
```

## 🔮 Future Enhancements

- **Multi-chain Support**: Extend to other networks (Ethereum, Polygon, Arbitrum)
- **Advanced Splitting**: Support for unequal splits and percentage-based divisions
- **Recurring Expenses**: Automatic monthly/weekly expense creation
- **Group Management**: Create and manage expense groups with roles
- **Mobile App**: React Native app for mobile users
- **Analytics Dashboard**: Advanced spending analytics and insights
- **Integration APIs**: Connect with popular expense tracking apps
- **DAO Governance**: Community governance for feature development

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

MIT License
