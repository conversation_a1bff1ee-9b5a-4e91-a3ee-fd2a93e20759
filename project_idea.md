SplitMate is a secure group chat agent that helps users split expenses, send reminders, reward contributors, and manage a shared wallet—all inside XMTP-enabled chats.

It targets the "Utility Agents" focus area, with light extensions into Social Agents (rewards, fun interactions) and DeFi Agents (onchain wallet integration, simple swaps, or micro-payments).
🛠️ Core Features

    Group Expense Splitting

        Users tag the agent to create a new "expense" (e.g., /split pizza 90 @alice @bob @carol)

        Agent calculates shares, sends DMs with payment links (via Coinbase Wallet / OnchainKit)

    Reminders & Settlements

        Tracks who paid and who hasn’t.

        Sends reminders like "💸 <PERSON> still owes 30 USDC for Pizza 🍕"

    Shared Wallet Tracker

        View total contributions and balances in a shared group wallet.

        Initiate onchain actions: transfer, swap, or contribute.

    Reward Contributions

        Admins can reward users with POAP-style NFTs or Base tokens (e.g., for paying on time or organizing events)

    Mini-App Integration (Optional)

        Visual expense charts and wallet stats via embedded mini-apps inside the chat.

💻 Tech Stack

    XMTP: Messaging backend + group DMs

    AgentKit + Basenames: Agent scaffolding and user-friendly handles

    Coinbase Wallet: For onchain transactions

    OnchainKit: Payment links and token actions

    Base Mainnet: For USDC transactions and wallet management

    Mini-app (optional): Simple frontend for analytics or polls